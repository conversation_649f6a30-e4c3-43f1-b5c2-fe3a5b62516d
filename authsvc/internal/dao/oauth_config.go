package authdao

import (
	"context"
	"database/sql"
	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"html"
	"time"
)

type OAuthConfig struct {
	IdOAuthConfig algouid.UUID
	IdAuthApp     algouid.UUID
	ClientId      string
	ClientSecret  string
	pdb.AtUnix
}

type OAuthConfigRepository interface {
	Count(ctx context.Context, params *ParamsOAuthConfigSelect) (int64, error)
	Select(ctx context.Context, params *ParamsOAuthConfigSelect) ([]*OAuthConfig, error)

	Create(ctx context.Context, oauthconfig ...*OAuthConfig) error
	Update(ctx context.Context, params ...*ParamsOAuthConfigUpdate) error
}

//type ParamsOAuthConfigSelect struct

type ParamsOAuthConfigSelect struct {
	IdOAuthConfig        sql.Null[algouid.UUID]
	IdAuthApp            sql.Null[algouid.UUID]
	ClientId             sql.Null[string]
	ClientSecret         sql.Null[string]
	Offset               int64
	Limit                int64
	OrderByCol           string
	IsOrderAsc           bool
	IsActiveOrDeactivate sql.Null[bool]
	IsNoKeyUpdate        bool
}

func (*OAuthConfig) Columns() []string {
	return []string{
		"id_o_auth_config",
		"id_auth_app",
		"client_id",
		"client_secret",
		"created_at", "updated_at", "deleted_at",
	}
}

func (u *OAuthConfig) Values() []any {
	return []any{

		u.IdOAuthConfig, u.IdAuthApp, u.ClientId, u.ClientSecret,
		u.CreatedAt, u.UpdatedAt, u.DeletedAt,
	}
}

func (u *OAuthConfig) FullScan(scan pdb.Scanner) (err error) {
	return scan.Scan(
		&u.IdOAuthConfig, &u.IdAuthApp, &u.ClientId, &u.ClientSecret,
		&u.CreatedAt, &u.UpdatedAt, &u.DeletedAt,
	)
}

// Select

func OAuthConfigSelectWithIdOAuthConfig(param algouid.UUID) func(*ParamsOAuthConfigSelect) {
	return func(p *ParamsOAuthConfigSelect) {

		p.IdOAuthConfig = sql.Null[algouid.UUID]{
			V:     param,
			Valid: !param.IsNil(),
		}
	}
}

func OAuthConfigSelectWithIdAuthApp(param algouid.UUID) func(*ParamsOAuthConfigSelect) {
	return func(p *ParamsOAuthConfigSelect) {

		p.IdAuthApp = sql.Null[algouid.UUID]{
			V:     param,
			Valid: !param.IsNil(),
		}
	}
}

func OAuthConfigSelectWithClientId(param string) func(*ParamsOAuthConfigSelect) {
	return func(p *ParamsOAuthConfigSelect) {
		param = html.EscapeString(param)
		p.ClientId = sql.Null[string]{
			V:     param,
			Valid: len(param) > 0,
		}
	}
}

func OAuthConfigSelectWithClientSecret(param string) func(*ParamsOAuthConfigSelect) {
	return func(p *ParamsOAuthConfigSelect) {
		param = html.EscapeString(param)
		p.ClientSecret = sql.Null[string]{
			V:     param,
			Valid: len(param) > 0,
		}
	}
}

func OAuthConfigSelectWithActiveOrDeactivate(isActive bool, isDeactivate bool) func(*ParamsOAuthConfigSelect) {
	return func(p *ParamsOAuthConfigSelect) {
		p.IsActiveOrDeactivate = sql.Null[bool]{
			V:     isActive,
			Valid: !(isDeactivate == isActive),
		}
	}
}

// paging
func OAuthConfigSelectWithPagination(offset, limit int64) func(*ParamsOAuthConfigSelect) {
	return func(p *ParamsOAuthConfigSelect) {
		p.Offset = offset
		p.Limit = limit
	}
}
func OAuthConfigSelectOrderByActiveState() func(p *ParamsOAuthConfigSelect) {
	return func(p *ParamsOAuthConfigSelect) {
		p.OrderByCol = "deleted_at"
		p.IsOrderAsc = true
	}
}

// paging

// Update

type ParamsOAuthConfigUpdate struct {
	IdOAuthConfig algouid.UUID
	IdAuthApp     sql.Null[algouid.UUID]
	ClientId      sql.Null[string]
	ClientSecret  sql.Null[string]
	DeletedAt     sql.Null[int64]
	IsChange      bool
}

func OAuthConfigUpdateIdAuthApp(current, newData algouid.UUID) func(*ParamsOAuthConfigUpdate) {
	return func(p *ParamsOAuthConfigUpdate) {
		p.IdAuthApp = sql.Null[algouid.UUID]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.IdAuthApp.Valid
	}
}

func OAuthConfigUpdateClientId(current, newData string) func(*ParamsOAuthConfigUpdate) {
	return func(p *ParamsOAuthConfigUpdate) {
		p.ClientId = sql.Null[string]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.ClientId.Valid
	}
}

func OAuthConfigUpdateClientSecret(current, newData string) func(*ParamsOAuthConfigUpdate) {
	return func(p *ParamsOAuthConfigUpdate) {
		p.ClientSecret = sql.Null[string]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.ClientSecret.Valid
	}
}

func OAuthConfigUpdateActiveState(currentActiveState, wantActiveState bool) func(*ParamsOAuthConfigUpdate) {
	return func(p *ParamsOAuthConfigUpdate) {
		tnow := time.Now().Unix()
		if wantActiveState {
			tnow = 0
		}
		p.DeletedAt = sql.Null[int64]{
			V:     tnow,
			Valid: currentActiveState != wantActiveState,
		}
		p.IsChange = p.IsChange || p.DeletedAt.Valid
	}
}
