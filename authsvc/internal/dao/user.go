package authdao

import (
	"context"
	"database/sql"
	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"html"
	"time"
)

type User struct {
	IdUser       algouid.UUID
	IdApp        algouid.UUID
	IdRole       algouid.UUID
	Email        string
	HashPassword string
	Salt         string

	pdb.AtUnix
}

type UserRepository interface {
	Count(ctx context.Context, params *ParamsUserSelect) (int64, error)
	Select(ctx context.Context, params *ParamsUserSelect) ([]*User, error)

	Create(ctx context.Context, user ...*User) error
	Update(ctx context.Context, params ...*ParamsUserUpdate) error
}

type ParamsUserSelect struct {
	IdUser     sql.Null[algouid.UUID]
	ListIdUser []algouid.UUID
	IdApp      sql.Null[algouid.UUID]
	ListIdApp  []algouid.UUID
	IdRole     sql.Null[algouid.UUID]
	ListIdRole []algouid.UUID

	Email                sql.Null[string]
	EmailSearch          sql.Null[string]
	HashPassword         sql.Null[string]
	Salt                 sql.Null[string]
	CreatedAtGte         int64
	CreatedAtLte         int64
	Offset               int64
	Limit                int64
	OrderByCol           string
	IsOrderAsc           bool
	IsActiveOrDeactivate sql.Null[bool]
	IsNoKeyUpdate        bool
}

func (*User) Columns() []string {
	return []string{
		"id_auth_user", "id_auth_app", "id_auth_role", "email", "hash_password", "salt", "created_at", "updated_at", "deleted_at",
	}
}

func (u *User) Values() []any {
	return []any{
		u.IdUser, u.IdApp, u.IdRole, u.Email, u.HashPassword, u.Salt, u.CreatedAt, u.UpdatedAt, u.DeletedAt,
	}
}

func (u *User) FullScan(scan pdb.Scanner) (err error) {
	return scan.Scan(
		&u.IdUser, &u.IdApp, &u.IdRole, &u.Email, &u.HashPassword, &u.Salt, &u.CreatedAt, &u.UpdatedAt, &u.DeletedAt,
	)
}

func UserSelectWithIdUser(idUser algouid.UUID) func(*ParamsUserSelect) {
	return func(p *ParamsUserSelect) {
		p.IdUser = sql.Null[algouid.UUID]{
			V:     idUser,
			Valid: !idUser.IsNil(),
		}
	}
}

func UserSelectWithListIdUser(listIdUser []algouid.UUID) func(*ParamsUserSelect) {
	return func(p *ParamsUserSelect) {
		p.ListIdUser = listIdUser
	}
}

func UserSelectWithIdRole(idRole algouid.UUID) func(*ParamsUserSelect) {
	return func(p *ParamsUserSelect) {
		p.IdRole = sql.Null[algouid.UUID]{
			V:     idRole,
			Valid: !idRole.IsNil(),
		}
	}
}

func UserSelectWithListIdRole(listIdRole []algouid.UUID) func(*ParamsUserSelect) {
	return func(p *ParamsUserSelect) {
		p.ListIdRole = listIdRole
	}
}

func UserSelectWithListIdApp(listIdApp []algouid.UUID) func(*ParamsUserSelect) {
	return func(p *ParamsUserSelect) {
		p.ListIdApp = listIdApp
	}
}

func UserSelectWithIdApp(idApp algouid.UUID) func(*ParamsUserSelect) {
	return func(p *ParamsUserSelect) {
		p.IdApp = sql.Null[algouid.UUID]{
			V:     idApp,
			Valid: !idApp.IsNil(),
		}
	}
}

func UserSelectWithEmail(email string) func(*ParamsUserSelect) {
	return func(p *ParamsUserSelect) {
		email = html.EscapeString(email)
		p.Email = sql.Null[string]{
			V:     email,
			Valid: len(email) > 0,
		}
	}
}

func UserSelectWithEmailSearch(emailSearch string) func(*ParamsUserSelect) {
	return func(p *ParamsUserSelect) {
		p.EmailSearch = sql.Null[string]{
			V:     emailSearch,
			Valid: len(emailSearch) > 0,
		}
	}
}

func UserSelectWithHashPassword(hashPassword string) func(*ParamsUserSelect) {
	return func(p *ParamsUserSelect) {
		p.HashPassword = sql.Null[string]{
			V:     hashPassword,
			Valid: len(hashPassword) > 0,
		}
	}
}

func UserSelectWithSalt(salt string) func(*ParamsUserSelect) {
	return func(p *ParamsUserSelect) {
		p.Salt = sql.Null[string]{
			V:     salt,
			Valid: len(salt) > 0,
		}
	}
}

func UserSelectWithCreatedAtGte(gte int64) func(*ParamsUserSelect) {
	return func(p *ParamsUserSelect) {
		p.CreatedAtGte = gte
	}
}

func UserSelectWithCreatedAtLte(lte int64) func(*ParamsUserSelect) {
	return func(p *ParamsUserSelect) {
		p.CreatedAtLte = lte
	}
}

func UserSelectWithActiveOrDeactivate(isActive bool, isDeactivate bool) func(*ParamsUserSelect) {
	return func(p *ParamsUserSelect) {
		p.IsActiveOrDeactivate = sql.Null[bool]{
			V:     isActive,
			Valid: isActive != isDeactivate,
		}
	}
}

func UserSelectWithPagination(offset, limit int64) func(*ParamsUserSelect) {
	return func(p *ParamsUserSelect) {
		p.Offset = offset
		p.Limit = limit
	}
}

func UserSelectOrderByActiveState() func(p *ParamsUserSelect) {
	return func(p *ParamsUserSelect) {
		p.OrderByCol = "deleted_at"
		p.IsOrderAsc = true
	}
}

type ParamsUserUpdate struct {
	IdUser       algouid.UUID
	IdRole       sql.Null[algouid.UUID]
	HashPassword sql.Null[string]
	Salt         sql.Null[string]

	DeletedAt sql.Null[int64]

	IsChange bool
}

func UserUpdateWithIdUser(idUser algouid.UUID) func(*ParamsUserUpdate) {
	return func(p *ParamsUserUpdate) {
		p.IdUser = idUser
	}
}

func UserUpdateIdRole(oldIdRole, newIdRole algouid.UUID) func(*ParamsUserUpdate) {
	return func(p *ParamsUserUpdate) {
		p.IdRole = sql.Null[algouid.UUID]{
			V:     newIdRole,
			Valid: !oldIdRole.Compare(newIdRole),
		}
		p.IsChange = p.IsChange || p.IdRole.Valid
	}
}

func UserUpdateHashPassword(currentHashPassword, newHashPassword string) func(*ParamsUserUpdate) {
	return func(p *ParamsUserUpdate) {
		p.HashPassword = sql.Null[string]{
			V:     newHashPassword,
			Valid: currentHashPassword != newHashPassword,
		}
		p.IsChange = p.IsChange || p.HashPassword.Valid
	}
}

func UserUpdateSalt(currentSalt, newSalt string) func(*ParamsUserUpdate) {
	return func(p *ParamsUserUpdate) {
		p.Salt = sql.Null[string]{
			V:     newSalt,
			Valid: currentSalt != newSalt,
		}
		p.IsChange = p.IsChange || p.Salt.Valid
	}
}

func UserUpdateActiveState(currentActiveState, wantActiveState bool) func(*ParamsUserUpdate) {
	return func(p *ParamsUserUpdate) {
		tnow := time.Now().Unix()
		if wantActiveState {
			tnow = 0
		}
		p.DeletedAt = sql.Null[int64]{
			V:     tnow,
			Valid: currentActiveState != wantActiveState,
		}
		p.IsChange = p.IsChange || p.DeletedAt.Valid
	}
}
