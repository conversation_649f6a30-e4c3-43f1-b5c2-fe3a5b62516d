package authdao

import (
	"context"
	"database/sql"
	"html"
	"time"

	"git.tmproxy-infra.com/algo/authsvc/internal/authenum"
	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
)

type ConfigTemplateEmail struct {
	IdConfigTemplateEmail algouid.UUID
	IdAuthApp             algouid.UUID
	Name                  authenum.TemplateEmailType
	Content               string
	pdb.AtUnix
}

type ConfigTemplateEmailRepository interface {
	Count(ctx context.Context, params *ParamsConfigTemplateEmailSelect) (int64, error)
	Select(ctx context.Context, params *ParamsConfigTemplateEmailSelect) ([]*ConfigTemplateEmail, error)

	Create(ctx context.Context, configTemplateEmail ...*ConfigTemplateEmail) error
	Update(ctx context.Context, params ...*ParamsConfigTemplateEmailUpdate) error
}

//type ParamsConfigTemplateEmailSelect struct

type ParamsConfigTemplateEmailSelect struct {
	IdConfigTemplateEmail sql.Null[algouid.UUID]
	IdAuthApp             sql.Null[algouid.UUID]
	Name                  sql.Null[authenum.TemplateEmailType]
	Content               sql.Null[string]
	Offset                int64
	Limit                 int64
	OrderByCol            string
	IsOrderAsc            bool
	IsActiveOrDeactivate  sql.Null[bool]
	IsNoKeyUpdate         bool
}

func (*ConfigTemplateEmail) Columns() []string {
	return []string{
		"id_config_template_email",
		"id_auth_app",
		"name",
		"content",
		"created_at", "updated_at", "deleted_at",
	}
}

func (u *ConfigTemplateEmail) Values() []any {
	return []any{

		u.IdConfigTemplateEmail, u.IdAuthApp, u.Name, u.Content,
		u.CreatedAt, u.UpdatedAt, u.DeletedAt,
	}
}

func (u *ConfigTemplateEmail) FullScan(scan pdb.Scanner) (err error) {
	return scan.Scan(
		&u.IdConfigTemplateEmail, &u.IdAuthApp, &u.Name, &u.Content,
		&u.CreatedAt, &u.UpdatedAt, &u.DeletedAt,
	)
}

// Select

func ConfigTemplateEmailSelectWithIdConfigTemplateEmail(param algouid.UUID) func(*ParamsConfigTemplateEmailSelect) {
	return func(p *ParamsConfigTemplateEmailSelect) {

		p.IdConfigTemplateEmail = sql.Null[algouid.UUID]{
			V:     param,
			Valid: !param.IsNil(),
		}
	}
}

func ConfigTemplateEmailSelectWithIdAuthApp(param algouid.UUID) func(*ParamsConfigTemplateEmailSelect) {
	return func(p *ParamsConfigTemplateEmailSelect) {

		p.IdAuthApp = sql.Null[algouid.UUID]{
			V:     param,
			Valid: !param.IsNil(),
		}
	}
}

func ConfigTemplateEmailSelectWithName(param authenum.TemplateEmailType) func(*ParamsConfigTemplateEmailSelect) {
	return func(p *ParamsConfigTemplateEmailSelect) {
		p.Name = sql.Null[authenum.TemplateEmailType]{
			V:     param,
			Valid: param.IsValid(),
		}
	}
}

func ConfigTemplateEmailSelectWithContent(param string) func(*ParamsConfigTemplateEmailSelect) {
	return func(p *ParamsConfigTemplateEmailSelect) {
		param = html.EscapeString(param)
		p.Content = sql.Null[string]{
			V:     param,
			Valid: len(param) > 0,
		}
	}
}

func ConfigTemplateEmailSelectWithActiveOrDeactivate(isActive bool, isDeactivate bool) func(*ParamsConfigTemplateEmailSelect) {
	return func(p *ParamsConfigTemplateEmailSelect) {
		p.IsActiveOrDeactivate = sql.Null[bool]{
			V:     isActive,
			Valid: !(isDeactivate == isActive),
		}
	}
}

// paging
func ConfigTemplateEmailSelectWithPagination(offset, limit int64) func(*ParamsConfigTemplateEmailSelect) {
	return func(p *ParamsConfigTemplateEmailSelect) {
		p.Offset = offset
		p.Limit = limit
	}
}
func ConfigTemplateEmailSelectOrderByActiveState() func(p *ParamsConfigTemplateEmailSelect) {
	return func(p *ParamsConfigTemplateEmailSelect) {
		p.OrderByCol = "deleted_at"
		p.IsOrderAsc = true
	}
}

// paging

// Update

type ParamsConfigTemplateEmailUpdate struct {
	IdConfigTemplateEmail algouid.UUID
	IdAuthApp             sql.Null[algouid.UUID]
	Name                  sql.Null[authenum.TemplateEmailType]
	Content               sql.Null[string]
	DeletedAt             sql.Null[int64]
	IsChange              bool
}

func ConfigTemplateEmailUpdateIdAuthApp(current, newData algouid.UUID) func(*ParamsConfigTemplateEmailUpdate) {
	return func(p *ParamsConfigTemplateEmailUpdate) {
		p.IdAuthApp = sql.Null[algouid.UUID]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.IdAuthApp.Valid
	}
}

func ConfigTemplateEmailUpdateName(current, newData authenum.TemplateEmailType) func(*ParamsConfigTemplateEmailUpdate) {
	return func(p *ParamsConfigTemplateEmailUpdate) {
		p.Name = sql.Null[authenum.TemplateEmailType]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.Name.Valid
	}
}

func ConfigTemplateEmailUpdateContent(current, newData string) func(*ParamsConfigTemplateEmailUpdate) {
	return func(p *ParamsConfigTemplateEmailUpdate) {
		p.Content = sql.Null[string]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.Content.Valid
	}
}

func ConfigTemplateEmailUpdateActiveState(currentActiveState, wantActiveState bool) func(*ParamsConfigTemplateEmailUpdate) {
	return func(p *ParamsConfigTemplateEmailUpdate) {
		tnow := time.Now().Unix()
		if wantActiveState {
			tnow = 0
		}
		p.DeletedAt = sql.Null[int64]{
			V:     tnow,
			Valid: currentActiveState != wantActiveState,
		}
		p.IsChange = p.IsChange || p.DeletedAt.Valid
	}
}
