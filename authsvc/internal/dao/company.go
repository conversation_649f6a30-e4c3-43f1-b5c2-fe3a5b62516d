package authdao

import (
	"context"
	"database/sql"
	"html"
	"time"

	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
)

type Company struct {
	IdCompany algouid.UUID
	IdAuthApp sql.Null[algouid.UUID]
	Name      string
	Street    string
	IdState   sql.Null[int64]
	IdCity    sql.Null[int64]
	IdCountry sql.Null[int64]
	Mst       string
	Logo      string
	Verify    bool
	pdb.AtUnix
}

type CompanyRepository interface {
	Count(ctx context.Context, params *ParamsCompanySelect) (int64, error)
	Select(ctx context.Context, params *ParamsCompanySelect) ([]*Company, error)

	Create(ctx context.Context, company ...*Company) error
	Update(ctx context.Context, params ...*ParamsCompanyUpdate) error
}

type ParamsCompanySelect struct {
	IdCompany     sql.Null[algouid.UUID]
	ListIdCompany []algouid.UUID
	IdAuthApp     sql.Null[algouid.UUID]
	Name          sql.Null[string]
	Street        sql.Null[string]
	IdState       sql.Null[int64]
	IdCity        sql.Null[int64]
	IdCountry     sql.Null[int64]
	Mst           sql.Null[string]
	Logo          sql.Null[string]
	Verify        sql.Null[bool]

	Offset               int64
	Limit                int64
	OrderByCol           string
	IsOrderAsc           bool
	IsActiveOrDeactivate sql.Null[bool]
	IsNoKeyUpdate        bool
}

func (*Company) Columns() []string {
	return []string{
		"id_company",
		"id_auth_app",
		"name",
		"street",
		"id_state",
		"id_city",
		"id_country",
		"mst",
		"logo",
		"verify",
		"created_at", "updated_at", "deleted_at",
	}
}

func (u *Company) Values() []any {
	return []any{

		u.IdCompany, u.IdAuthApp, u.Name, u.Street, u.IdState, u.IdCity, u.IdCountry, u.Mst, u.Logo, u.Verify,
		u.CreatedAt, u.UpdatedAt, u.DeletedAt,
	}
}

func (u *Company) FullScan(scan pdb.Scanner) (err error) {
	return scan.Scan(

		&u.IdCompany, &u.IdAuthApp, &u.Name, &u.Street, &u.IdState, &u.IdCity, &u.IdCountry, &u.Mst, &u.Logo, &u.Verify,
		&u.CreatedAt, &u.UpdatedAt, &u.DeletedAt,
	)
}

func CompanySelectWithIdCompany(idcompany algouid.UUID) func(*ParamsCompanySelect) {
	return func(p *ParamsCompanySelect) {
		p.IdCompany = sql.Null[algouid.UUID]{
			V:     idcompany,
			Valid: !idcompany.IsNil(),
		}
	}
}

func CompanySelectWithIdAuthApp(idApp algouid.UUID) func(*ParamsCompanySelect) {
	return func(p *ParamsCompanySelect) {
		p.IdAuthApp = sql.Null[algouid.UUID]{
			V:     idApp,
			Valid: !idApp.IsNil(),
		}
	}
}
func CompanySelectWithListIdCompany(listIdCompany []algouid.UUID) func(*ParamsCompanySelect) {
	return func(p *ParamsCompanySelect) {
		p.ListIdCompany = listIdCompany
	}
}

func CompanySelectWithName(name string) func(*ParamsCompanySelect) {
	return func(p *ParamsCompanySelect) {
		name = html.EscapeString(name)
		p.Name = sql.Null[string]{
			V:     name,
			Valid: len(name) > 0,
		}
	}
}

func CompanySelectWithStreet(street string) func(*ParamsCompanySelect) {
	return func(p *ParamsCompanySelect) {
		street = html.EscapeString(street)
		p.Street = sql.Null[string]{
			V:     street,
			Valid: len(street) > 0,
		}
	}
}
func CompanySelectWithState(state int64) func(*ParamsCompanySelect) {
	return func(p *ParamsCompanySelect) {
		p.IdState = sql.Null[int64]{
			V:     state,
			Valid: state > 0,
		}
	}
}
func CompanySelectWithCity(city int64) func(*ParamsCompanySelect) {
	return func(p *ParamsCompanySelect) {
		p.IdCity = sql.Null[int64]{
			V:     city,
			Valid: city > 0,
		}
	}
}
func CompanySelectWithPagination(offset, limit int64) func(*ParamsCompanySelect) {
	return func(p *ParamsCompanySelect) {
		p.Offset = offset
		p.Limit = limit
	}
}
func CompanySelectOrderByActiveState() func(p *ParamsCompanySelect) {
	return func(p *ParamsCompanySelect) {
		p.OrderByCol = "deleted_at"
		p.IsOrderAsc = true
	}
}

func CompanySelectWithCountry(country int64) func(*ParamsCompanySelect) {
	return func(p *ParamsCompanySelect) {
		p.IdCountry = sql.Null[int64]{
			V:     country,
			Valid: country > 0,
		}
	}
}

func CompanySelectWithActiveOrDeactivate(isActive bool, isDeactivate bool) func(*ParamsCompanySelect) {
	return func(p *ParamsCompanySelect) {
		p.IsActiveOrDeactivate = sql.Null[bool]{
			V:     isActive,
			Valid: !(isDeactivate == isActive),
		}
	}
}
func CompanySelectWithMst(mst string) func(*ParamsCompanySelect) {
	return func(p *ParamsCompanySelect) {
		mst = html.EscapeString(mst)
		p.Mst = sql.Null[string]{
			V:     mst,
			Valid: len(mst) > 0,
		}
	}
}

func CompanySelectWithVerifyOrNotVerify(verify bool, notVerify bool) func(*ParamsCompanySelect) {
	return func(p *ParamsCompanySelect) {
		p.Verify = sql.Null[bool]{
			V:     verify,
			Valid: !(verify == notVerify),
		}
	}
}

func CompanySelectWithLogo(logo string) func(*ParamsCompanySelect) {
	return func(p *ParamsCompanySelect) {
		logo = html.EscapeString(logo)
		p.Logo = sql.Null[string]{
			V:     logo,
			Valid: len(logo) > 0,
		}
	}
}

type ParamsCompanyUpdate struct {
	IdCompany algouid.UUID
	Name      sql.Null[string]
	Street    sql.Null[string]
	IdState   sql.Null[int64]
	IdCity    sql.Null[int64]
	IdCountry sql.Null[int64]
	Mst       sql.Null[string]
	Logo      sql.Null[string]
	Verify    sql.Null[bool]
	DeletedAt sql.Null[int64]
	IsChange  bool
}

func CompanyUpdateName(currentName, newName string) func(*ParamsCompanyUpdate) {
	return func(p *ParamsCompanyUpdate) {
		p.Name = sql.Null[string]{
			V:     newName,
			Valid: currentName != newName,
		}
		p.IsChange = p.IsChange || p.Name.Valid
	}
}
func CompanyUpdateVerify(newItem bool) func(*ParamsCompanyUpdate) {
	return func(p *ParamsCompanyUpdate) {
		p.Verify = sql.Null[bool]{
			V:     newItem,
			Valid: newItem,
		}
		p.IsChange = p.IsChange || p.Verify.Valid
	}
}
func CompanyUpdateStreet(currentStreet, newStreet string) func(*ParamsCompanyUpdate) {
	return func(p *ParamsCompanyUpdate) {
		p.Street = sql.Null[string]{
			V:     newStreet,
			Valid: currentStreet != newStreet,
		}
		p.IsChange = p.IsChange || p.Street.Valid
	}
}

func CompanyUpdateState(current, newData int64) func(*ParamsCompanyUpdate) {
	return func(p *ParamsCompanyUpdate) {
		p.IdState = sql.Null[int64]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.IdState.Valid
	}
}

func CompanyUpdateCity(currentCity, newCity int64) func(*ParamsCompanyUpdate) {
	return func(p *ParamsCompanyUpdate) {
		p.IdCity = sql.Null[int64]{
			V:     newCity,
			Valid: currentCity != newCity,
		}
		p.IsChange = p.IsChange || p.IdCity.Valid
	}
}

func CompanyUpdateCountry(currentCountry, newCountry int64) func(*ParamsCompanyUpdate) {
	return func(p *ParamsCompanyUpdate) {
		p.IdCountry = sql.Null[int64]{
			V:     newCountry,
			Valid: currentCountry != newCountry,
		}
		p.IsChange = p.IsChange || p.IdCountry.Valid
	}
}

func CompanyUpdateMst(currentMst, newMst string) func(*ParamsCompanyUpdate) {
	return func(p *ParamsCompanyUpdate) {
		p.Mst = sql.Null[string]{
			V:     newMst,
			Valid: currentMst != newMst,
		}
		p.IsChange = p.IsChange || p.Mst.Valid
	}
}

func CompanyUpdateLogo(currentLogo, newLogo string) func(*ParamsCompanyUpdate) {
	return func(p *ParamsCompanyUpdate) {
		p.Logo = sql.Null[string]{
			V:     newLogo,
			Valid: currentLogo != newLogo,
		}
		p.IsChange = p.IsChange || p.Logo.Valid
	}
}

func CompanyUpdateActiveState(currentActiveState, wantActiveState bool) func(*ParamsCompanyUpdate) {
	return func(p *ParamsCompanyUpdate) {
		tnow := time.Now().Unix()
		if wantActiveState {
			tnow = 0
		}
		p.DeletedAt = sql.Null[int64]{
			V:     tnow,
			Valid: currentActiveState != wantActiveState,
		}
		p.IsChange = p.IsChange || p.DeletedAt.Valid
	}
}
