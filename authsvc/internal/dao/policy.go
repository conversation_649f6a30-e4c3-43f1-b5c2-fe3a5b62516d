package authdao

import (
	"context"
	"database/sql"
	"time"

	"git.tmproxy-infra.com/algo/common/pkg/algouid"

	"git.tmproxy-infra.com/algo/common/pkg/pdb"
)

type PolicyRepository interface {
	WithOption(...pdb.ModelOption) PolicyRepository

	Count(ctx context.Context, params *ParamsPolicySelect) (total int64, err error)
	Select(ctx context.Context, params *ParamsPolicySelect) (listAuthPolicy []*Policy, err error)
	Create(ctx context.Context, listAuthPolicy ...*Policy) (err error)
	Update(ctx context.Context, listAuthPolicy ...*ParamsPolicyUpdate) (err error)
}

type Policy struct {
	IdPolicy algouid.UUID
	IdPath   algouid.UUID
	IdRole   algouid.UUID
	pdb.AtUnix
}

func (*Policy) TableName() string {
	return "auth_policy"
}

func (*Policy) Columns() []string {
	return []string{"id_auth_policy", "id_auth_path", "id_auth_role", "created_at", "updated_at", "deleted_at"}
}

func (u *Policy) Values() []any {
	return []any{u.IdPolicy, u.IdPath, u.IdRole, u.CreatedAt, u.UpdatedAt, u.DeletedAt}
}
func (u *Policy) FullScan(scan pdb.Scanner) (err error) {
	return scan.Scan(&u.IdPolicy, &u.IdPath, &u.IdRole, &u.CreatedAt, &u.UpdatedAt, &u.DeletedAt)
}

type ParamsPolicySelect struct {
	IdPolicy sql.Null[algouid.UUID]
	IdPath   sql.Null[algouid.UUID]
	IdRole   sql.Null[algouid.UUID]

	IsActiveOrDeactivate sql.Null[bool]

	Offset     int64
	Limit      int64
	OrderByCol string
	IsOrderAsc bool

	IsNoKeyUpdate bool
}

func PolicySelectWithIdPolicy(idPolicy algouid.UUID) func(p *ParamsPolicySelect) {
	return func(p *ParamsPolicySelect) {
		p.IdPolicy = sql.Null[algouid.UUID]{
			V:     idPolicy,
			Valid: !idPolicy.IsNil(),
		}
	}
}

func PolicySelectWithIdPath(idPath algouid.UUID) func(p *ParamsPolicySelect) {
	return func(p *ParamsPolicySelect) {
		p.IdPath = sql.Null[algouid.UUID]{
			V:     idPath,
			Valid: !idPath.IsNil(),
		}
	}
}

func PolicySelectWithIdRole(idAuthRole algouid.UUID) func(p *ParamsPolicySelect) {
	return func(p *ParamsPolicySelect) {
		p.IdRole = sql.Null[algouid.UUID]{
			V:     idAuthRole,
			Valid: !idAuthRole.IsNil(),
		}
	}
}

func PolicySelectWithActiveOrDeactivate(isActive bool, isDeactivate bool) func(p *ParamsPolicySelect) {
	return func(p *ParamsPolicySelect) {
		p.IsActiveOrDeactivate = sql.Null[bool]{
			V:     isActive,
			Valid: isActive != isDeactivate,
		}
	}
}

func PolicySelectWithPagination(offset, limit int64) func(p *ParamsPolicySelect) {
	return func(p *ParamsPolicySelect) {
		p.Offset = offset
		p.Limit = limit
	}
}

func PolicySelectWithNoKeyUpdate() func(p *ParamsPolicySelect) {
	return func(p *ParamsPolicySelect) {
		p.IsNoKeyUpdate = true
	}
}

func PolicySelectWithOrderByCreatedAt(isAsc bool) func(p *ParamsPolicySelect) {
	return func(p *ParamsPolicySelect) {
		p.OrderByCol = "created_at"
		p.IsOrderAsc = isAsc
	}
}

type ParamsPolicyUpdate struct {
	IdPolicy  algouid.UUID
	Role      sql.Null[string]
	DeletedAt sql.Null[int64]

	IsChange bool
}

func PolicyUpdateWithIdPolicy(idPolicy algouid.UUID) func(p *ParamsPolicyUpdate) {
	return func(p *ParamsPolicyUpdate) {
		p.IdPolicy = idPolicy
	}
}

func PolicyUpdateRole(oldRole, newRole string) func(p *ParamsPolicyUpdate) {
	return func(p *ParamsPolicyUpdate) {
		p.Role = sql.Null[string]{
			V:     newRole,
			Valid: oldRole != newRole,
		}
		p.IsChange = p.IsChange || p.Role.Valid
	}
}

func PolicyUpdateActiveState(currentStateActive, wantStateActive bool) func(d *ParamsPolicyUpdate) {
	return func(d *ParamsPolicyUpdate) {
		t := time.Now().Unix()
		if wantStateActive {
			t = int64(0)
		}
		d.DeletedAt = sql.Null[int64]{
			V:     t,
			Valid: currentStateActive != wantStateActive,
		}
		d.IsChange = d.IsChange || d.DeletedAt.Valid
	}
}
