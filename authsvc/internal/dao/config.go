package authdao

import (
	"context"
	"database/sql"
	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"time"
)

type Config struct {
	IdConfig   algouid.UUID
	IdAuthApp  algouid.UUID
	ConfigType string
	Value      string
	pdb.AtUnix
}

type ConfigRepository interface {
	Count(ctx context.Context, params *ParamsConfigSelect) (int64, error)
	Select(ctx context.Context, params *ParamsConfigSelect) ([]*Config, error)
	Create(ctx context.Context, Config ...*Config) error
	Update(ctx context.Context, params ...*ParamsConfigUpdate) error
}

type ParamsConfigSelect struct {
	IdConfig     sql.Null[algouid.UUID]
	IdAuthApp    sql.Null[algouid.UUID]
	ConfigType   sql.Null[string]
	Value        sql.Null[string]
	ListType     []string
	CreatedAtGte int64
	CreatedAtLte int64

	Offset int64
	Limit  int64

	OrderByCol string
	IsOrderAsc bool

	IsActiveOrDeactivate sql.Null[bool]
	IsNoKeyUpdate        bool
}

func (*Config) Columns() []string {
	return []string{
		"id_config", "id_auth_app", "config_type", "value",
		"created_at", "updated_at", "deleted_at",
	}
}

func (u *Config) Values() []any {
	return []any{
		u.IdConfig, u.IdAuthApp, u.ConfigType, u.Value,
		u.CreatedAt, u.UpdatedAt, u.DeletedAt,
	}
}

func (u *Config) FullScan(scan pdb.Scanner) (err error) {
	return scan.Scan(
		&u.IdConfig,
		&u.IdAuthApp, &u.ConfigType, &u.Value,
		&u.CreatedAt, &u.UpdatedAt, &u.DeletedAt,
	)
}

func ConfigSelectWithIdAuthApp(param algouid.UUID) func(*ParamsConfigSelect) {
	return func(p *ParamsConfigSelect) {
		p.IdAuthApp = sql.Null[algouid.UUID]{
			V:     param,
			Valid: !param.IsNil(),
		}
	}
}

func ConfigSelectWithIdConfig(param algouid.UUID) func(*ParamsConfigSelect) {
	return func(p *ParamsConfigSelect) {
		p.IdConfig = sql.Null[algouid.UUID]{
			V:     param,
			Valid: !param.IsNil(),
		}
	}
}
func ConfigSelectWithType(param string) func(*ParamsConfigSelect) {
	return func(p *ParamsConfigSelect) {
		p.ConfigType = sql.Null[string]{
			V:     param,
			Valid: len(param) > 0,
		}
	}
}
func ConfigSelectWithAnyType(params []string) func(*ParamsConfigSelect) {
	return func(p *ParamsConfigSelect) {
		p.ListType = params
	}
}

func ConfigSelectWithCreatedAtGte(gte int64) func(*ParamsConfigSelect) {
	return func(p *ParamsConfigSelect) {
		p.CreatedAtGte = gte
	}
}

func ConfigSelectWithCreatedAtLte(lte int64) func(*ParamsConfigSelect) {
	return func(p *ParamsConfigSelect) {
		p.CreatedAtLte = lte
	}
}

func ConfigSelectWithActiveOrDeactivate(isActive bool, isDeactivate bool) func(*ParamsConfigSelect) {
	return func(p *ParamsConfigSelect) {
		p.IsActiveOrDeactivate = sql.Null[bool]{
			V:     isActive,
			Valid: !(isDeactivate == isActive),
		}
	}
}

func ConfigSelectWithPagination(offset, limit int64) func(*ParamsConfigSelect) {
	return func(p *ParamsConfigSelect) {
		p.Offset = offset
		p.Limit = limit
	}
}

func ConfigSelectOrderByActiveState() func(p *ParamsConfigSelect) {
	return func(p *ParamsConfigSelect) {
		p.OrderByCol = "deleted_at"
		p.IsOrderAsc = true
	}
}

type ParamsConfigUpdate struct {
	IdConfig   algouid.UUID
	IdAuthApp  algouid.UUID
	ConfigType sql.Null[string]
	Value      sql.Null[string]
	DeletedAt  sql.Null[int64]
	IsChange   bool
}

func ConfigUpdateWithIdAuthApp(param algouid.UUID) func(*ParamsConfigUpdate) {
	return func(p *ParamsConfigUpdate) {
		p.IdAuthApp = param
	}
}

func ConfigUpdateWithValue(current string, param string) func(*ParamsConfigUpdate) {
	return func(p *ParamsConfigUpdate) {
		p.IsChange = current != param
		p.Value = sql.Null[string]{
			V:     param,
			Valid: current != param,
		}
	}
}

func ConfigUpdateWithType(current string, param string) func(*ParamsConfigUpdate) {

	return func(p *ParamsConfigUpdate) {
		p.IsChange = current != param

		p.ConfigType = sql.Null[string]{
			V:     param,
			Valid: current != param,
		}
	}
}

func ConfigUpdateActiveState(currentActiveState, wantActiveState bool) func(*ParamsConfigUpdate) {
	return func(p *ParamsConfigUpdate) {
		if currentActiveState == wantActiveState {
			return
		}
		p.IsChange = true
		tnow := time.Now().Unix()
		if wantActiveState {
			tnow = 0
		}
		p.DeletedAt = sql.Null[int64]{
			V:     tnow,
			Valid: true,
		}
	}
}

// model của config stmp được lưu dạng json trong db config
type SmtpAwsConfig struct {
	SmtpServerAddr string `json:"smtpServerAddr"`
	SmtpServerPort string `json:"smtpServerPort"`
	AuthUserName   string `json:"authUserName"`
	AuthPassword   string `json:"authPassword"`
	SenderEmail    string `json:"senderEmail"`
}
