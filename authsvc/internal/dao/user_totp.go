package authdao

import (
	"context"
	"database/sql"
	"git.tmproxy-infra.com/algo/authsvc/internal/authenum"
	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"time"
)

type UserTotp struct {
	IdUserTotp algouid.UUID
	IdUser     algouid.UUID
	TotpType   authenum.TotpType
	Secret     string

	pdb.AtUnix
}

type UserTotpRepository interface {
	Count(ctx context.Context, params *ParamsUserTotpSelect) (int64, error)
	Select(ctx context.Context, params *ParamsUserTotpSelect) ([]*UserTotp, error)

	Create(ctx context.Context, UserTotp ...*UserTotp) error
	Update(ctx context.Context, params ...*ParamsUserTotpUpdate) error
}

type ParamsUserTotpSelect struct {
	IdUserTotp     sql.Null[algouid.UUID]
	ListIdUserTotp []algouid.UUID
	IdUser         sql.Null[algouid.UUID]
	ListIdUser     []algouid.UUID
	TotpType       sql.Null[authenum.TotpType]
	Secret         sql.Null[string]

	CreatedAtGte int64
	CreatedAtLte int64

	Offset int64
	Limit  int64

	OrderByCol string
	IsOrderAsc bool

	IsActiveOrDeactivate sql.Null[bool]
	IsNoKeyUpdate        bool
}

func (*UserTotp) Columns() []string {
	return []string{
		"id_auth_user_totp", "id_auth_user", "totp_type", "secret",
		"created_at", "updated_at", "deleted_at",
	}
}

func (u *UserTotp) Values() []any {
	return []any{
		u.IdUserTotp, u.IdUser, u.TotpType, u.Secret,
		u.CreatedAt, u.UpdatedAt, u.DeletedAt,
	}
}

func (u *UserTotp) FullScan(scan pdb.Scanner) (err error) {
	return scan.Scan(
		&u.IdUserTotp, &u.IdUser, &u.TotpType, &u.Secret,
		&u.CreatedAt, &u.UpdatedAt, &u.DeletedAt,
	)
}

func UserTotpSelectWithIdUserTotp(idUserTotp algouid.UUID) func(*ParamsUserTotpSelect) {
	return func(p *ParamsUserTotpSelect) {
		p.IdUserTotp = sql.Null[algouid.UUID]{
			V:     idUserTotp,
			Valid: !idUserTotp.IsNil(),
		}
	}
}

func UserTotpSelectWithListIdUserTotp(listidUserTotp []algouid.UUID) func(*ParamsUserTotpSelect) {
	return func(p *ParamsUserTotpSelect) {
		p.ListIdUserTotp = listidUserTotp
	}
}

func UserTotpSelectWithListIdUser(listidUser []algouid.UUID) func(*ParamsUserTotpSelect) {
	return func(p *ParamsUserTotpSelect) {
		p.ListIdUser = listidUser
	}
}

func UserTotpSelectWithIdUser(idUser algouid.UUID) func(*ParamsUserTotpSelect) {
	return func(p *ParamsUserTotpSelect) {
		p.IdUser = sql.Null[algouid.UUID]{
			V:     idUser,
			Valid: idUser.IsNil() == false,
		}
	}
}

func UserTotpSelectWithTotpType(totpType authenum.TotpType) func(*ParamsUserTotpSelect) {
	return func(p *ParamsUserTotpSelect) {
		p.TotpType = sql.Null[authenum.TotpType]{
			V:     totpType,
			Valid: totpType.IsValid(),
		}
	}
}

func UserTotpSelectWithSecret(secret string) func(*ParamsUserTotpSelect) {
	return func(p *ParamsUserTotpSelect) {
		p.Secret = sql.Null[string]{
			V:     secret,
			Valid: len(secret) > 0,
		}
	}
}

func UserTotpSelectWithCreatedAtGte(gte int64) func(*ParamsUserTotpSelect) {
	return func(p *ParamsUserTotpSelect) {
		p.CreatedAtGte = gte
	}
}

func UserTotpSelectWithCreatedAtLte(lte int64) func(*ParamsUserTotpSelect) {
	return func(p *ParamsUserTotpSelect) {
		p.CreatedAtLte = lte
	}
}

func UserTotpSelectWithActiveOrDeactivate(isActive bool, isDeactivate bool) func(*ParamsUserTotpSelect) {
	return func(p *ParamsUserTotpSelect) {
		p.IsActiveOrDeactivate = sql.Null[bool]{
			V:     isActive,
			Valid: !(isDeactivate == isActive),
		}
	}
}

func UserTotpSelectWithPagination(offset, limit int64) func(*ParamsUserTotpSelect) {
	return func(p *ParamsUserTotpSelect) {
		p.Offset = offset
		p.Limit = limit
	}
}

func UserTotpSelectOrderByActiveState() func(p *ParamsUserTotpSelect) {
	return func(p *ParamsUserTotpSelect) {
		p.OrderByCol = "deleted_at"
		p.IsOrderAsc = true
	}
}

type ParamsUserTotpUpdate struct {
	IdUserTotp algouid.UUID
	TotpType   sql.Null[authenum.TotpType]
	Secret     sql.Null[string]
	IdUser     algouid.UUID
	DeletedAt  sql.Null[int64]

	IsChange bool
}

func UserTotpUpdateWithidUserTotp(idUserTotp algouid.UUID) func(*ParamsUserTotpUpdate) {
	return func(p *ParamsUserTotpUpdate) {
		p.IdUserTotp = idUserTotp
	}
}
func UserTotpUpdateWithIdUser(idUser algouid.UUID) func(*ParamsUserTotpUpdate) {
	return func(p *ParamsUserTotpUpdate) {
		p.IdUser = idUser
	}
}

func UserTotpUpdateTotpType(currentTotpType, newTotpType authenum.TotpType) func(*ParamsUserTotpUpdate) {
	return func(p *ParamsUserTotpUpdate) {
		if currentTotpType.Compare(newTotpType) {
			return
		}
		p.IsChange = true
		p.TotpType = sql.Null[authenum.TotpType]{
			V:     newTotpType,
			Valid: true,
		}
	}
}

func UserTotpUpdateSecret(currentSecret, newSecret string) func(*ParamsUserTotpUpdate) {
	return func(p *ParamsUserTotpUpdate) {
		if currentSecret == newSecret {
			return
		}
		p.IsChange = true
		p.Secret = sql.Null[string]{
			V:     newSecret,
			Valid: true,
		}
	}
}

func UserTotpUpdateActiveState(currentActiveState, wantActiveState bool) func(*ParamsUserTotpUpdate) {
	return func(p *ParamsUserTotpUpdate) {
		if currentActiveState == wantActiveState {
			return
		}
		p.IsChange = true
		tnow := time.Now().Unix()
		if wantActiveState {
			tnow = 0
		}
		p.DeletedAt = sql.Null[int64]{
			V:     tnow,
			Valid: true,
		}
	}
}
