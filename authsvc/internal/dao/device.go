package authdao

import (
	"context"
	"database/sql"
	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"html"
	"time"
)

type Device struct {
	IdDevice     algouid.UUID
	IdAuthApp    algouid.UUID
	IdAuthUser   algouid.UUID
	DeviceId     string
	DeviceName   string
	RefreshToken string
	TokenExpiry  int64
	LastUsedAt   int64
	pdb.AtUnix
}

type DeviceRepository interface {
	Count(ctx context.Context, params *ParamsDeviceSelect) (int64, error)
	Select(ctx context.Context, params *ParamsDeviceSelect) ([]*Device, error)

	Create(ctx context.Context, device ...*Device) error
	Update(ctx context.Context, params ...*ParamsDeviceUpdate) error
}

//type ParamsDeviceSelect struct

type ParamsDeviceSelect struct {
	IdDevice             sql.Null[algouid.UUID]
	IdAuthApp            sql.Null[algouid.UUID]
	IdAuthUser           sql.Null[algouid.UUID]
	DeviceId             sql.Null[string]
	DeviceName           sql.Null[string]
	RefreshToken         sql.Null[string]
	TokenExpiry          sql.Null[uint64]
	LastUsedAt           sql.Null[uint64]
	Offset               int64
	Limit                int64
	OrderByCol           string
	IsOrderAsc           bool
	IsActiveOrDeactivate sql.Null[bool]
	IsNoKeyUpdate        bool
}

func (*Device) Columns() []string {
	return []string{
		"id_device",
		"id_auth_app",
		"id_auth_user",
		"device_id",
		"device_name",
		"refresh_token",
		"token_expiry",
		"last_used_at",
		"created_at", "updated_at", "deleted_at",
	}
}

func (u *Device) Values() []any {
	return []any{

		u.IdDevice, u.IdAuthApp, u.IdAuthUser, u.DeviceId, u.DeviceName, u.RefreshToken, u.TokenExpiry, u.LastUsedAt,
		u.CreatedAt, u.UpdatedAt, u.DeletedAt,
	}
}

func (u *Device) FullScan(scan pdb.Scanner) (err error) {
	return scan.Scan(
		&u.IdDevice, &u.IdAuthApp, &u.IdAuthUser, &u.DeviceId, &u.DeviceName, &u.RefreshToken, &u.TokenExpiry, &u.LastUsedAt,
		&u.CreatedAt, &u.UpdatedAt, &u.DeletedAt,
	)
}

// Select

func DeviceSelectWithIdDevice(param algouid.UUID) func(*ParamsDeviceSelect) {
	return func(p *ParamsDeviceSelect) {

		p.IdDevice = sql.Null[algouid.UUID]{
			V:     param,
			Valid: !param.IsNil(),
		}
	}
}

func DeviceSelectWithIdAuthApp(param algouid.UUID) func(*ParamsDeviceSelect) {
	return func(p *ParamsDeviceSelect) {

		p.IdAuthApp = sql.Null[algouid.UUID]{
			V:     param,
			Valid: !param.IsNil(),
		}
	}
}

func DeviceSelectWithIdAuthUser(param algouid.UUID) func(*ParamsDeviceSelect) {
	return func(p *ParamsDeviceSelect) {

		p.IdAuthUser = sql.Null[algouid.UUID]{
			V:     param,
			Valid: !param.IsNil(),
		}
	}
}

func DeviceSelectWithDeviceId(param string) func(*ParamsDeviceSelect) {
	return func(p *ParamsDeviceSelect) {
		param = html.EscapeString(param)
		p.DeviceId = sql.Null[string]{
			V:     param,
			Valid: len(param) > 0,
		}
	}
}

func DeviceSelectWithDeviceName(param string) func(*ParamsDeviceSelect) {
	return func(p *ParamsDeviceSelect) {
		param = html.EscapeString(param)
		p.DeviceName = sql.Null[string]{
			V:     param,
			Valid: len(param) > 0,
		}
	}
}

func DeviceSelectWithRefreshToken(param string) func(*ParamsDeviceSelect) {
	return func(p *ParamsDeviceSelect) {
		param = html.EscapeString(param)
		p.RefreshToken = sql.Null[string]{
			V:     param,
			Valid: len(param) > 0,
		}
	}
}

func DeviceSelectWithTokenExpiry(param uint64) func(*ParamsDeviceSelect) {
	return func(p *ParamsDeviceSelect) {

		p.TokenExpiry = sql.Null[uint64]{
			V:     param,
			Valid: true,
		}
	}
}

func DeviceSelectWithLastUsedAt(param uint64) func(*ParamsDeviceSelect) {
	return func(p *ParamsDeviceSelect) {

		p.LastUsedAt = sql.Null[uint64]{
			V:     param,
			Valid: true,
		}
	}
}

func DeviceSelectWithActiveOrDeactivate(isActive bool, isDeactivate bool) func(*ParamsDeviceSelect) {
	return func(p *ParamsDeviceSelect) {
		p.IsActiveOrDeactivate = sql.Null[bool]{
			V:     isActive,
			Valid: !(isDeactivate == isActive),
		}
	}
}

// paging
func DeviceSelectWithPagination(offset, limit int64) func(*ParamsDeviceSelect) {
	return func(p *ParamsDeviceSelect) {
		p.Offset = offset
		p.Limit = limit
	}
}
func DeviceSelectOrderByActiveState() func(p *ParamsDeviceSelect) {
	return func(p *ParamsDeviceSelect) {
		p.OrderByCol = "deleted_at"
		p.IsOrderAsc = true
	}
}

// paging

// Update

type ParamsDeviceUpdate struct {
	IdDevice     algouid.UUID
	IdAuthApp    sql.Null[algouid.UUID]
	IdAuthUser   sql.Null[algouid.UUID]
	DeviceId     sql.Null[string]
	DeviceName   sql.Null[string]
	RefreshToken sql.Null[string]
	TokenExpiry  sql.Null[int64]
	LastUsedAt   sql.Null[int64]
	DeletedAt    sql.Null[int64]
	IsChange     bool
}

func DeviceUpdateWithIdDevice(idDevice algouid.UUID) func(*ParamsDeviceUpdate) {
	return func(p *ParamsDeviceUpdate) {
		p.IdDevice = idDevice
	}
}

func DeviceUpdateIdAuthApp(current, newData algouid.UUID) func(*ParamsDeviceUpdate) {
	return func(p *ParamsDeviceUpdate) {
		p.IdAuthApp = sql.Null[algouid.UUID]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.IdAuthApp.Valid
	}
}

func DeviceUpdateIdAuthUser(current, newData algouid.UUID) func(*ParamsDeviceUpdate) {
	return func(p *ParamsDeviceUpdate) {
		p.IdAuthUser = sql.Null[algouid.UUID]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.IdAuthUser.Valid
	}
}

func DeviceUpdateDeviceId(current, newData string) func(*ParamsDeviceUpdate) {
	return func(p *ParamsDeviceUpdate) {
		p.DeviceId = sql.Null[string]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.DeviceId.Valid
	}
}

func DeviceUpdateDeviceName(current, newData string) func(*ParamsDeviceUpdate) {
	return func(p *ParamsDeviceUpdate) {
		p.DeviceName = sql.Null[string]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.DeviceName.Valid
	}
}

func DeviceUpdateRefreshToken(current, newData string) func(*ParamsDeviceUpdate) {
	return func(p *ParamsDeviceUpdate) {
		p.RefreshToken = sql.Null[string]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.RefreshToken.Valid
	}
}

func DeviceUpdateTokenExpiry(current, newData int64) func(*ParamsDeviceUpdate) {
	return func(p *ParamsDeviceUpdate) {
		p.TokenExpiry = sql.Null[int64]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.TokenExpiry.Valid
	}
}

func DeviceUpdateLastUsedAt(current, newData int64) func(*ParamsDeviceUpdate) {
	return func(p *ParamsDeviceUpdate) {
		p.LastUsedAt = sql.Null[int64]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.LastUsedAt.Valid
	}
}

func DeviceUpdateActiveState(currentActiveState, wantActiveState bool) func(*ParamsDeviceUpdate) {
	return func(p *ParamsDeviceUpdate) {
		tnow := time.Now().Unix()
		if wantActiveState {
			tnow = 0
		}
		p.DeletedAt = sql.Null[int64]{
			V:     tnow,
			Valid: currentActiveState != wantActiveState,
		}
		p.IsChange = p.IsChange || p.DeletedAt.Valid
	}
}
