package authdao

import (
	"context"
	"database/sql"
	"html"
	"time"

	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
)

type ServiceRepository interface {
	WithOption(...pdb.ModelOption) ServiceRepository

	Count(ctx context.Context, params *ParamsServiceSelect) (total int64, err error)
	Select(ctx context.Context, params *ParamsServiceSelect) (listAuthService []*Service, err error)
	Create(ctx context.Context, listAuthService ...*Service) (err error)
	Update(ctx context.Context, listAuthService ...*ParamsServiceUpdate) (err error)
}

type Service struct {
	IdService algouid.UUID
	Name      string

	pdb.AtUnix
}

func (*Service) Columns() []string {
	return []string{"id_auth_service", "name", "created_at", "updated_at", "deleted_at"}
}

func (u *Service) Values() []any {
	return []any{u.IdService, u.Name, u.CreatedAt, u.UpdatedAt, u.DeletedAt}
}
func (u *Service) FullScan(scan pdb.Scanner) (err error) {
	return scan.Scan(&u.IdService, &u.Name, &u.CreatedAt, &u.UpdatedAt, &u.DeletedAt)
}

type ParamsServiceSelect struct {
	IdService  sql.Null[algouid.UUID]
	Name       sql.Null[string]
	NameSearch sql.Null[string]

	NameExist sql.Null[string]

	ListIdService []algouid.UUID

	IsActiveOrDeactivate sql.Null[bool]

	Offset     int64
	Limit      int64
	OrderByCol string
	IsOrderAsc bool

	IsNoKeyUpdate bool
}

func ServiceSelectWithIdService(idService algouid.UUID) func(p *ParamsServiceSelect) {
	return func(p *ParamsServiceSelect) {
		p.IdService = sql.Null[algouid.UUID]{
			V:     idService,
			Valid: !idService.IsNil(),
		}
	}
}

func ServiceSelectWithListIdService(listIdService []algouid.UUID) func(p *ParamsServiceSelect) {
	return func(p *ParamsServiceSelect) {
		p.ListIdService = listIdService
	}
}

func ServiceSelectWithName(name string) func(p *ParamsServiceSelect) {
	return func(p *ParamsServiceSelect) {
		p.Name = sql.Null[string]{
			V:     name,
			Valid: len(name) > 0,
		}
	}
}
func ServiceSelectWithNameSearch(nameSearch string) func(p *ParamsServiceSelect) {
	return func(p *ParamsServiceSelect) {
		p.NameSearch = sql.Null[string]{
			V:     html.EscapeString(nameSearch),
			Valid: len(nameSearch) > 0,
		}
	}
}

func ServiceSelectWithExist(nameExist string) func(p *ParamsServiceSelect) {
	return func(p *ParamsServiceSelect) {
		p.NameExist = sql.Null[string]{
			V:     nameExist,
			Valid: len(nameExist) > 0,
		}

	}
}

func ServiceSelectWithActiveOrDeactivate(isActive bool, isDeactivate bool) func(p *ParamsServiceSelect) {
	return func(p *ParamsServiceSelect) {

		p.IsActiveOrDeactivate = sql.Null[bool]{
			V:     isActive,
			Valid: isActive != isDeactivate,
		}
	}
}

func ServiceSelectWithPagination(offset, limit int64) func(p *ParamsServiceSelect) {
	return func(p *ParamsServiceSelect) {
		p.Offset = offset
		p.Limit = limit
	}
}

func ServiceSelectWithNoKeyUpdate() func(p *ParamsServiceSelect) {
	return func(p *ParamsServiceSelect) {
		p.IsNoKeyUpdate = true
	}
}

func ServiceSelectWithOrderByCreatedAt(isAsc bool) func(p *ParamsServiceSelect) {
	return func(p *ParamsServiceSelect) {
		p.OrderByCol = "created_at"
		p.IsOrderAsc = isAsc
	}
}

type ParamsServiceUpdate struct {
	IdService algouid.UUID
	IdParent  sql.Null[algouid.UUID]
	Name      sql.Null[string]
	Path      sql.Null[string]

	DeletedAt sql.Null[int64]

	IsChange bool
}

func ServiceUpdateName(oldName, newName string) func(p *ParamsServiceUpdate) {
	return func(p *ParamsServiceUpdate) {
		p.IsChange = true
		p.Name = sql.Null[string]{
			V:     newName,
			Valid: oldName != newName,
		}
		p.IsChange = p.IsChange || p.Name.Valid
	}
}
func ServiceUpdatePath(oldPath, newPath string) func(p *ParamsServiceUpdate) {
	return func(p *ParamsServiceUpdate) {
		p.Path = sql.Null[string]{
			V:     newPath,
			Valid: oldPath != newPath,
		}
		p.IsChange = p.IsChange || p.Path.Valid
	}
}
func ServiceUpdateWithIdService(idService algouid.UUID) func(p *ParamsServiceUpdate) {
	return func(p *ParamsServiceUpdate) {
		p.IdService = idService
	}
}

func ServiceUpdateActiveState(currentActiveState, wantActiveState bool) func(d *ParamsServiceUpdate) {
	return func(p *ParamsServiceUpdate) {
		tnow := time.Now().Unix()
		if wantActiveState {
			tnow = 0
		}
		p.DeletedAt = sql.Null[int64]{
			V:     tnow,
			Valid: currentActiveState != wantActiveState,
		}
		p.IsChange = p.IsChange || p.DeletedAt.Valid
	}
}
