package authdao

import (
	"context"
	"database/sql"
	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"html"
	"time"
)

type ConfigMail struct {
	IdConfigMail      algouid.UUID
	IdAuthApp         algouid.UUID
	SmtpServerAddress string
	SmtpServerPort    uint64
	AuthUsername      string
	AuthPassword      string
	SenderEmail       string
	pdb.AtUnix
}

type ConfigMailRepository interface {
	Count(ctx context.Context, params *ParamsConfigMailSelect) (int64, error)
	Select(ctx context.Context, params *ParamsConfigMailSelect) ([]*ConfigMail, error)

	Create(ctx context.Context, configMail ...*ConfigMail) error
	Update(ctx context.Context, params ...*ParamsConfigMailUpdate) error
}

//type ParamsConfigMailSelect struct

type ParamsConfigMailSelect struct {
	IdConfigMail         sql.Null[algouid.UUID]
	IdAuthApp            sql.Null[algouid.UUID]
	SmtpServerAddress    sql.Null[string]
	SmtpServerPort       sql.Null[uint64]
	AuthUsername         sql.Null[string]
	AuthPassword         sql.Null[string]
	SenderEmail          sql.Null[string]
	Offset               int64
	Limit                int64
	OrderByCol           string
	IsOrderAsc           bool
	IsActiveOrDeactivate sql.Null[bool]
	IsNoKeyUpdate        bool
}

func (*ConfigMail) Columns() []string {
	return []string{
		"id_config_mail",
		"id_auth_app",
		"smtp_server_address",
		"smtp_server_port",
		"auth_username",
		"auth_password",
		"sender_email",
		"created_at", "updated_at", "deleted_at",
	}
}

func (u *ConfigMail) Values() []any {
	return []any{

		u.IdConfigMail, u.IdAuthApp, u.SmtpServerAddress, u.SmtpServerPort, u.AuthUsername, u.AuthPassword, u.SenderEmail,
		u.CreatedAt, u.UpdatedAt, u.DeletedAt,
	}
}

func (u *ConfigMail) FullScan(scan pdb.Scanner) (err error) {
	return scan.Scan(
		&u.IdConfigMail, &u.IdAuthApp, &u.SmtpServerAddress, &u.SmtpServerPort, &u.AuthUsername, &u.AuthPassword, &u.SenderEmail,
		&u.CreatedAt, &u.UpdatedAt, &u.DeletedAt,
	)
}

// Select

func ConfigMailSelectWithIdConfigMail(param algouid.UUID) func(*ParamsConfigMailSelect) {
	return func(p *ParamsConfigMailSelect) {

		p.IdConfigMail = sql.Null[algouid.UUID]{
			V:     param,
			Valid: !param.IsNil(),
		}
	}
}

func ConfigMailSelectWithIdAuthApp(param algouid.UUID) func(*ParamsConfigMailSelect) {
	return func(p *ParamsConfigMailSelect) {

		p.IdAuthApp = sql.Null[algouid.UUID]{
			V:     param,
			Valid: !param.IsNil(),
		}
	}
}

func ConfigMailSelectWithSmtpServerAddress(param string) func(*ParamsConfigMailSelect) {
	return func(p *ParamsConfigMailSelect) {
		param = html.EscapeString(param)
		p.SmtpServerAddress = sql.Null[string]{
			V:     param,
			Valid: len(param) > 0,
		}
	}
}

func ConfigMailSelectWithSmtpServerPort(param uint64) func(*ParamsConfigMailSelect) {
	return func(p *ParamsConfigMailSelect) {

		p.SmtpServerPort = sql.Null[uint64]{
			V:     param,
			Valid: true,
		}
	}
}

func ConfigMailSelectWithAuthUsername(param string) func(*ParamsConfigMailSelect) {
	return func(p *ParamsConfigMailSelect) {
		param = html.EscapeString(param)
		p.AuthUsername = sql.Null[string]{
			V:     param,
			Valid: len(param) > 0,
		}
	}
}

func ConfigMailSelectWithAuthPassword(param string) func(*ParamsConfigMailSelect) {
	return func(p *ParamsConfigMailSelect) {
		param = html.EscapeString(param)
		p.AuthPassword = sql.Null[string]{
			V:     param,
			Valid: len(param) > 0,
		}
	}
}

func ConfigMailSelectWithSenderEmail(param string) func(*ParamsConfigMailSelect) {
	return func(p *ParamsConfigMailSelect) {
		param = html.EscapeString(param)
		p.SenderEmail = sql.Null[string]{
			V:     param,
			Valid: len(param) > 0,
		}
	}
}

func ConfigMailSelectWithActiveOrDeactivate(isActive bool, isDeactivate bool) func(*ParamsConfigMailSelect) {
	return func(p *ParamsConfigMailSelect) {
		p.IsActiveOrDeactivate = sql.Null[bool]{
			V:     isActive,
			Valid: !(isDeactivate == isActive),
		}
	}
}

// paging
func ConfigMailSelectWithPagination(offset, limit int64) func(*ParamsConfigMailSelect) {
	return func(p *ParamsConfigMailSelect) {
		p.Offset = offset
		p.Limit = limit
	}
}
func ConfigMailSelectOrderByActiveState() func(p *ParamsConfigMailSelect) {
	return func(p *ParamsConfigMailSelect) {
		p.OrderByCol = "deleted_at"
		p.IsOrderAsc = true
	}
}

// paging

// Update

type ParamsConfigMailUpdate struct {
	IdConfigMail      algouid.UUID
	IdAuthApp         sql.Null[algouid.UUID]
	SmtpServerAddress sql.Null[string]
	SmtpServerPort    sql.Null[uint64]
	AuthUsername      sql.Null[string]
	AuthPassword      sql.Null[string]
	SenderEmail       sql.Null[string]
	DeletedAt         sql.Null[int64]
	IsChange          bool
}

func ConfigMailUpdateIdAuthApp(current, newData algouid.UUID) func(*ParamsConfigMailUpdate) {
	return func(p *ParamsConfigMailUpdate) {
		p.IdAuthApp = sql.Null[algouid.UUID]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.IdAuthApp.Valid
	}
}

func ConfigMailUpdateSmtpServerAddress(current, newData string) func(*ParamsConfigMailUpdate) {
	return func(p *ParamsConfigMailUpdate) {
		p.SmtpServerAddress = sql.Null[string]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.SmtpServerAddress.Valid
	}
}

func ConfigMailUpdateSmtpServerPort(current, newData uint64) func(*ParamsConfigMailUpdate) {
	return func(p *ParamsConfigMailUpdate) {
		p.SmtpServerPort = sql.Null[uint64]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.SmtpServerPort.Valid
	}
}

func ConfigMailUpdateAuthUsername(current, newData string) func(*ParamsConfigMailUpdate) {
	return func(p *ParamsConfigMailUpdate) {
		p.AuthUsername = sql.Null[string]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.AuthUsername.Valid
	}
}

func ConfigMailUpdateAuthPassword(current, newData string) func(*ParamsConfigMailUpdate) {
	return func(p *ParamsConfigMailUpdate) {
		p.AuthPassword = sql.Null[string]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.AuthPassword.Valid
	}
}

func ConfigMailUpdateSenderEmail(current, newData string) func(*ParamsConfigMailUpdate) {
	return func(p *ParamsConfigMailUpdate) {
		p.SenderEmail = sql.Null[string]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.SenderEmail.Valid
	}
}

func ConfigMailUpdateActiveState(currentActiveState, wantActiveState bool) func(*ParamsConfigMailUpdate) {
	return func(p *ParamsConfigMailUpdate) {
		tnow := time.Now().Unix()
		if wantActiveState {
			tnow = 0
		}
		p.DeletedAt = sql.Null[int64]{
			V:     tnow,
			Valid: currentActiveState != wantActiveState,
		}
		p.IsChange = p.IsChange || p.DeletedAt.Valid
	}
}
