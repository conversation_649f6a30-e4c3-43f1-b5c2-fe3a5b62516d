package authdao

import (
	"context"
	"database/sql"
	"time"

	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"golang.org/x/net/html"
)

type UserDetail struct {
	IdUserDetail algouid.UUID
	IdUser       algouid.UUID
	FirstName    string
	LastName     string
	PhoneNumber  string
	Street       string
	IdState      sql.Null[int64]
	IdCity       sql.Null[int64]
	IdCountry    sql.Null[int64]
	IdCompany    algouid.UUID
	RefCode      string
	UserRefId    algouid.UUID
	pdb.AtUnix
}

type UserDetailRepository interface {
	Count(ctx context.Context, params *ParamsUserDetailSelect) (int64, error)
	Select(ctx context.Context, params *ParamsUserDetailSelect) ([]*UserDetail, error)

	Create(ctx context.Context, authUserDetails ...*UserDetail) error
	Update(ctx context.Context, params ...*ParamsUserDetailUpdate) error
}

type ParamsUserDetailSelect struct {
	IdUserDetail     sql.Null[algouid.UUID]
	ListIdUserDetail []algouid.UUID
	IdUser           sql.Null[algouid.UUID]
	ListIdUser       []algouid.UUID
	FirstName        sql.Null[string]
	LastName         sql.Null[string]
	PhoneNumber      sql.Null[string]
	RefCode          sql.Null[string]
	UserRefId        sql.Null[algouid.UUID]
	IdCompany        sql.Null[algouid.UUID]
	CreatedAtGte     int64
	CreatedAtLte     int64

	Offset int64
	Limit  int64

	OrderByCol string
	IsOrderAsc bool

	IsActiveOrDeactivate sql.Null[bool]
	IsNoKeyUpdate        bool
}

func (*UserDetail) Columns() []string {
	return []string{
		"id_auth_user_detail", "id_auth_user", "first_name", "last_name", "phone_number", "street", "id_state", "id_city", "id_country", "id_company", "ref_code", "user_ref_id",
		"created_at", "updated_at", "deleted_at",
	}
}

func (u *UserDetail) Values() []any {
	return []any{
		u.IdUserDetail, u.IdUser, u.FirstName, u.LastName, u.PhoneNumber, u.Street, u.IdState, u.IdCity, u.IdCountry, u.IdCompany, u.RefCode, u.UserRefId,
		u.CreatedAt, u.UpdatedAt, u.DeletedAt,
	}
}

func (u *UserDetail) FullScan(scan pdb.Scanner) (err error) {
	return scan.Scan(
		&u.IdUserDetail, &u.IdUser, &u.FirstName, &u.LastName, &u.PhoneNumber, &u.Street, &u.IdState, &u.IdCity, &u.IdCountry, &u.IdCompany, &u.RefCode, &u.UserRefId,
		&u.CreatedAt, &u.UpdatedAt, &u.DeletedAt,
	)
}

func UserDetailSelectWithIdUserDetail(idUserDetail algouid.UUID) func(*ParamsUserDetailSelect) {
	return func(p *ParamsUserDetailSelect) {
		p.IdUserDetail = sql.Null[algouid.UUID]{
			V:     idUserDetail,
			Valid: !idUserDetail.IsNil(),
		}
	}
}

func UserDetailSelectWithListIdUserDetail(listIdUserDetail []algouid.UUID) func(*ParamsUserDetailSelect) {
	return func(p *ParamsUserDetailSelect) {
		p.ListIdUserDetail = listIdUserDetail
	}
}

func UserDetailSelectWithListIdUser(listIdUser []algouid.UUID) func(*ParamsUserDetailSelect) {
	return func(p *ParamsUserDetailSelect) {
		p.ListIdUser = listIdUser
	}
}

func UserDetailSelectWithIdUser(idUser algouid.UUID) func(*ParamsUserDetailSelect) {
	return func(p *ParamsUserDetailSelect) {
		p.IdUser = sql.Null[algouid.UUID]{
			V:     idUser,
			Valid: idUser.IsNil() == false,
		}
	}
}
func UserDetailSelectWithRefCode(refCode string) func(*ParamsUserDetailSelect) {
	return func(p *ParamsUserDetailSelect) {
		p.RefCode = sql.Null[string]{
			V:     refCode,
			Valid: len(refCode) > 0,
		}
	}
}
func UserDetailSelectWithFirstName(firstName string) func(*ParamsUserDetailSelect) {
	return func(p *ParamsUserDetailSelect) {
		firstName = html.EscapeString(firstName)
		p.FirstName = sql.Null[string]{
			V:     firstName,
			Valid: len(firstName) > 0,
		}
	}
}

func UserDetailSelectWithLastName(lastName string) func(*ParamsUserDetailSelect) {
	return func(p *ParamsUserDetailSelect) {
		lastName = html.EscapeString(lastName)
		p.LastName = sql.Null[string]{
			V:     lastName,
			Valid: len(lastName) > 0,
		}
	}
}

func UserDetailSelectWithPhoneNumber(phoneNumber string) func(*ParamsUserDetailSelect) {
	return func(p *ParamsUserDetailSelect) {
		phoneNumber = html.EscapeString(phoneNumber)
		p.PhoneNumber = sql.Null[string]{
			V:     phoneNumber,
			Valid: len(phoneNumber) > 0,
		}
	}
}

func UserDetailSelectWithCreatedAtGte(gte int64) func(*ParamsUserDetailSelect) {
	return func(p *ParamsUserDetailSelect) {
		p.CreatedAtGte = gte
	}
}

func UserDetailSelectWithCreatedAtLte(lte int64) func(*ParamsUserDetailSelect) {
	return func(p *ParamsUserDetailSelect) {
		p.CreatedAtLte = lte
	}
}

func UserDetailSelectWithActiveOrDeactivate(isActive bool, isDeactivate bool) func(*ParamsUserDetailSelect) {
	return func(p *ParamsUserDetailSelect) {
		p.IsActiveOrDeactivate = sql.Null[bool]{
			V:     isActive,
			Valid: isDeactivate != isActive,
		}
	}
}

func UserDetailSelectWithPagination(offset, limit int64) func(*ParamsUserDetailSelect) {
	return func(p *ParamsUserDetailSelect) {
		p.Offset = offset
		p.Limit = limit
	}
}

func UserDetailSelectOrderByActiveState() func(p *ParamsUserDetailSelect) {
	return func(p *ParamsUserDetailSelect) {
		p.OrderByCol = "deleted_at"
		p.IsOrderAsc = true
	}
}

type ParamsUserDetailUpdate struct {
	IdUserDetail algouid.UUID
	FirstName    sql.Null[string]
	LastName     sql.Null[string]
	PhoneNumber  sql.Null[string]
	Street       sql.Null[string]
	IdState      sql.Null[int64]
	IdCity       sql.Null[int64]
	IdCountry    sql.Null[int64]
	IdCompany    sql.Null[algouid.UUID]
	RefCode      sql.Null[string]
	UserRefId    sql.Null[algouid.UUID]
	DeletedAt    sql.Null[int64]

	IsChange bool
}

func UserDetailUpdateWithIdUserDetail(idAuthUserDetail algouid.UUID) func(*ParamsUserDetailUpdate) {
	return func(p *ParamsUserDetailUpdate) {
		p.IdUserDetail = idAuthUserDetail
	}
}

func UserDetailUpdateFirstName(currentFirstName, newFirstName string) func(*ParamsUserDetailUpdate) {
	return func(p *ParamsUserDetailUpdate) {
		p.FirstName = sql.Null[string]{
			V:     newFirstName,
			Valid: currentFirstName != newFirstName,
		}
		p.IsChange = p.IsChange || p.FirstName.Valid
	}
}

func UserDetailUpdateLastName(currentLastName, newLastName string) func(*ParamsUserDetailUpdate) {
	return func(p *ParamsUserDetailUpdate) {
		p.LastName = sql.Null[string]{
			V:     newLastName,
			Valid: currentLastName != newLastName,
		}
		p.IsChange = p.IsChange || p.LastName.Valid
	}
}

func UserDetailUpdatePhoneNumber(currentPhoneNumber, newPhoneNumber string) func(*ParamsUserDetailUpdate) {
	return func(p *ParamsUserDetailUpdate) {
		p.PhoneNumber = sql.Null[string]{
			V:     newPhoneNumber,
			Valid: currentPhoneNumber != newPhoneNumber,
		}
		p.IsChange = p.IsChange || p.PhoneNumber.Valid
	}
}
func UserDetailUpdateState(current, newData int64) func(*ParamsUserDetailUpdate) {
	return func(p *ParamsUserDetailUpdate) {
		p.IdState = sql.Null[int64]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.IdState.Valid
	}
}
func UserDetailUpdateCity(current, newData int64) func(*ParamsUserDetailUpdate) {
	return func(p *ParamsUserDetailUpdate) {
		p.IdCity = sql.Null[int64]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.IdCity.Valid
	}
}

func UserDetailUpdateCountry(current, newData int64) func(*ParamsUserDetailUpdate) {
	return func(p *ParamsUserDetailUpdate) {
		p.IdCountry = sql.Null[int64]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.IdCountry.Valid
	}
}

func UserDetailUpdateStreet(current, newData string) func(*ParamsUserDetailUpdate) {
	return func(p *ParamsUserDetailUpdate) {
		p.Street = sql.Null[string]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.Street.Valid
	}
}

func UserDetailUpdateIdCompany(current, newData algouid.UUID) func(*ParamsUserDetailUpdate) {
	return func(p *ParamsUserDetailUpdate) {
		p.IdCompany = sql.Null[algouid.UUID]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.IdCompany.Valid
	}
}

func UserDetailUpdateRefCode(current, newData string) func(*ParamsUserDetailUpdate) {
	return func(p *ParamsUserDetailUpdate) {
		p.RefCode = sql.Null[string]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.RefCode.Valid
	}
}

func UserDetailUpdateUserRefId(current, newData algouid.UUID) func(*ParamsUserDetailUpdate) {
	return func(p *ParamsUserDetailUpdate) {
		p.UserRefId = sql.Null[algouid.UUID]{
			V:     newData,
			Valid: current != newData,
		}
		p.IsChange = p.IsChange || p.IdCompany.Valid
	}
}

func AuthUserDetailUpdateActiveState(currentActiveState bool, wantActiveState bool) func(*ParamsUserDetailUpdate) {
	return func(d *ParamsUserDetailUpdate) {
		tnow := time.Now().Unix()
		if wantActiveState {
			tnow = 0
		}
		d.DeletedAt = sql.Null[int64]{
			V:     tnow,
			Valid: currentActiveState != wantActiveState,
		}
		d.IsChange = d.IsChange || d.DeletedAt.Valid
	}
}
