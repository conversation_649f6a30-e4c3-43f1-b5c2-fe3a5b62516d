package authdao

import (
	"context"
	"database/sql"
	"git.tmproxy-infra.com/algo/common/enumtype"
	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"golang.org/x/net/html"
	"strings"
	"time"
)

type App struct {
	IdApp      algouid.UUID
	Name       string
	AppType    enumtype.AppType
	AppCountry enumtype.AppCountry
	Domain     string

	TokenTTL       int64
	PrivateEd25519 string
	PublicEd25519  string

	pdb.AtUnix
}

type AuthAppRepository interface {
	Count(ctx context.Context, params *ParamsAppSelect) (int64, error)
	Select(ctx context.Context, params *ParamsAppSelect) ([]*App, error)

	Create(ctx context.Context, app ...*App) error
	Update(ctx context.Context, params ...*ParamsAppUpdate) error
}

type ParamsAppSelect struct {
	IdApp      sql.Null[algouid.UUID]
	ListIdApp  []algouid.UUID
	AppType    sql.Null[enumtype.AppType]
	AppCountry sql.Null[enumtype.AppCountry]
	Domain     sql.Null[string]

	DomainSearch sql.Null[string]
	Name         sql.Null[string]
	NameSearch   sql.Null[string]

	Offset int64
	Limit  int64

	OrderByCol string
	IsOrderAsc bool

	IsActiveOrDeactivate sql.Null[bool]
	IsNoKeyUpdate        bool
}

func (*App) Columns() []string {
	return []string{"id_auth_app", "name", "app_type", "domain", "token_ttl", "private_ed25519", "public_ed25519", "app_country", "created_at", "updated_at", "deleted_at"}
}

func (u *App) Values() []any {
	return []any{u.IdApp, u.Name, u.AppType, u.Domain, u.TokenTTL, u.PrivateEd25519, u.PublicEd25519, u.AppCountry, u.CreatedAt, u.UpdatedAt, u.DeletedAt}
}
func (u *App) FullScan(scan pdb.Scanner) (err error) {
	return scan.Scan(&u.IdApp, &u.Name, &u.AppType, &u.Domain, &u.TokenTTL, &u.PrivateEd25519, &u.PublicEd25519, &u.AppCountry, &u.CreatedAt, &u.UpdatedAt, &u.DeletedAt)
}
func AppSelectWithIdApp(IdApp algouid.UUID) func(*ParamsAppSelect) {
	return func(p *ParamsAppSelect) {
		p.IdApp = sql.Null[algouid.UUID]{
			V:     IdApp,
			Valid: !IdApp.IsNil(),
		}
	}
}
func AppSelectWithDomain(domain string) func(*ParamsAppSelect) {
	return func(p *ParamsAppSelect) {
		domain = html.EscapeString(domain)
		p.Domain = sql.Null[string]{V: domain, Valid: len(domain) > 0}
	}
}
func AppSelectWithDomainSearch(domainSearch string) func(*ParamsAppSelect) {
	return func(p *ParamsAppSelect) {
		p.DomainSearch = sql.Null[string]{V: domainSearch, Valid: len(domainSearch) > 0}
	}
}

func AppSelectWithListIdApp(listIdApp []algouid.UUID) func(*ParamsAppSelect) {
	return func(p *ParamsAppSelect) {
		p.ListIdApp = listIdApp
	}
}
func AppSelectWithAppType(appType enumtype.AppType) func(*ParamsAppSelect) {
	return func(p *ParamsAppSelect) {
		p.AppType = sql.Null[enumtype.AppType]{
			V:     appType,
			Valid: appType.IsValid(),
		}
	}
}
func AppSelectWithAppCountry(appCountry enumtype.AppCountry) func(*ParamsAppSelect) {
	return func(p *ParamsAppSelect) {
		p.AppCountry = sql.Null[enumtype.AppCountry]{
			V:     appCountry,
			Valid: appCountry.IsValid(),
		}
	}
}
func AppSelectWithNameSearch(nameSearch string) func(*ParamsAppSelect) {
	return func(p *ParamsAppSelect) {
		nameSearch = html.EscapeString(nameSearch)
		p.NameSearch = sql.Null[string]{
			V:     nameSearch,
			Valid: len(nameSearch) > 0,
		}
	}
}

func AppSelectWithName(name string) func(*ParamsAppSelect) {
	return func(p *ParamsAppSelect) {
		name = html.EscapeString(name)
		p.Name = sql.Null[string]{
			V:     name,
			Valid: len(name) > 0,
		}
	}
}
func AppSelectWithActiveOrDeactivate(isActive bool, isDeactivate bool) func(p *ParamsAppSelect) {
	return func(p *ParamsAppSelect) {
		p.IsActiveOrDeactivate = sql.Null[bool]{
			V:     isActive,
			Valid: isActive != isDeactivate,
		}
	}
}
func AppSelectWithPagination(offset, limit int64) func(p *ParamsAppSelect) {
	return func(p *ParamsAppSelect) {
		p.Offset = offset
		p.Limit = limit
	}
}
func AppSelectOrderByActiveState() func(p *ParamsAppSelect) {
	return func(p *ParamsAppSelect) {
		p.OrderByCol = "deleted_at"
		p.IsOrderAsc = true
	}
}

type ParamsAppUpdate struct {
	IdApp          algouid.UUID
	Name           sql.Null[string]
	AppType        sql.Null[enumtype.AppType]
	AppCountry     sql.Null[enumtype.AppCountry]
	PrivateEd25519 sql.Null[string]
	PubEd25519     sql.Null[string]
	TokenTTL       sql.Null[int64]
	DeletedAt      sql.Null[int64]

	IsChange bool
}

func AppUpdateWithIdApp(IdApp algouid.UUID) func(*ParamsAppUpdate) {
	return func(p *ParamsAppUpdate) {
		p.IdApp = IdApp
	}
}

func AppUpdateAppType(currentAppType, newAppType enumtype.AppType) func(*ParamsAppUpdate) {
	return func(p *ParamsAppUpdate) {
		p.AppType = sql.Null[enumtype.AppType]{
			V:     newAppType,
			Valid: !currentAppType.Compare(newAppType),
		}
		p.IsChange = p.IsChange || p.AppType.Valid
	}
}
func AppUpdateAppCountry(currentAppCountry, newAppCountry enumtype.AppCountry) func(*ParamsAppUpdate) {
	return func(p *ParamsAppUpdate) {
		p.AppCountry = sql.Null[enumtype.AppCountry]{
			V:     newAppCountry,
			Valid: !currentAppCountry.Compare(newAppCountry),
		}
		p.IsChange = p.IsChange || p.AppType.Valid
	}
}
func AppUpdateName(currentName, newName string) func(*ParamsAppUpdate) {
	return func(p *ParamsAppUpdate) {
		p.Name = sql.Null[string]{
			V:     newName,
			Valid: strings.Compare(currentName, newName) != 0,
		}
		p.IsChange = p.IsChange || p.Name.Valid
	}
}

func AppUpdateTokenTtl(oldTtl, newTtl int64) func(*ParamsAppUpdate) {
	return func(p *ParamsAppUpdate) {
		p.TokenTTL = sql.Null[int64]{
			V:     newTtl,
			Valid: oldTtl != newTtl,
		}
		p.IsChange = p.IsChange || p.TokenTTL.Valid
	}
}

func AppUpdateKeyPair(oldPrivate, oldPub, newPrivate, newPub string) func(*ParamsAppUpdate) {
	return func(p *ParamsAppUpdate) {
		p.PrivateEd25519 = sql.Null[string]{
			V:     newPrivate,
			Valid: oldPrivate != newPrivate,
		}
		p.PubEd25519 = sql.Null[string]{
			V:     newPub,
			Valid: oldPub != newPub,
		}
		p.IsChange = p.IsChange || p.PrivateEd25519.Valid || p.PubEd25519.Valid
	}
}

func AppUpdateActiveState(currentActiveState, wantActiveState bool) func(*ParamsAppUpdate) {
	return func(p *ParamsAppUpdate) {
		tnow := time.Now().Unix()
		if wantActiveState {
			tnow = 0
		}
		p.DeletedAt = sql.Null[int64]{
			V:     tnow,
			Valid: currentActiveState != wantActiveState,
		}
		p.IsChange = p.IsChange || p.DeletedAt.Valid
	}
}
