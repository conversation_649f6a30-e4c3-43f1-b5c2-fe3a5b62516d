package authdao

import (
	"context"
	"database/sql"
	"golang.org/x/net/html"
	"time"

	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
)

type PathRepository interface {
	WithOption(...pdb.ModelOption) PathRepository

	Count(ctx context.Context, params *ParamsPathSelect) (total int64, err error)
	Select(ctx context.Context, params *ParamsPathSelect) (listAuthPath []*Path, err error)

	Create(ctx context.Context, listAuthPath ...*Path) (err error)
	Update(ctx context.Context, listAuthPath ...*ParamsPathUpdate) (err error)
}

type Path struct {
	IdPath       algouid.UUID
	IdService    algouid.UUID
	AbsolutePath string

	pdb.AtUnix
}

func (*Path) Columns() []string {
	return []string{"id_auth_path", "id_auth_service", "absolute_path", "created_at", "updated_at", "deleted_at"}
}

func (u *Path) Values() []any {
	return []any{u.IdPath, u.IdService, u.AbsolutePath, u.CreatedAt, u.UpdatedAt, u.DeletedAt}
}

func (u *Path) FullScan(scan pdb.Scanner) (err error) {
	return scan.Scan(&u.IdPath, &u.IdService, &u.AbsolutePath, &u.CreatedAt, &u.UpdatedAt, &u.DeletedAt)
}

type ParamsPathSelect struct {
	IdPath    sql.Null[algouid.UUID]
	IdService sql.Null[algouid.UUID]

	ListIdPath         []algouid.UUID
	AbsolutePath       sql.Null[string]
	AbsolutePathSearch sql.Null[string]

	IdServiceExist    sql.Null[algouid.UUID]
	AbsolutePathExist sql.Null[string]

	IsActiveOrDeactivate sql.Null[bool]

	Offset     int64
	Limit      int64
	OrderByCol string
	IsOrderAsc bool

	IsNoKeyUpdate bool
}

func PathSelectWithIdPath(idPath algouid.UUID) func(p *ParamsPathSelect) {
	return func(p *ParamsPathSelect) {
		p.IdPath = sql.Null[algouid.UUID]{
			V:     idPath,
			Valid: !idPath.IsNil(),
		}
	}
}

func PathSelectWithListIdPath(listIdPath []algouid.UUID) func(p *ParamsPathSelect) {
	return func(p *ParamsPathSelect) {
		p.ListIdPath = listIdPath
	}
}

func PathSelectWithIdService(idService algouid.UUID) func(p *ParamsPathSelect) {
	return func(p *ParamsPathSelect) {
		p.IdService = sql.Null[algouid.UUID]{
			V:     idService,
			Valid: !idService.IsNil(),
		}
	}
}

func PathSelectWithAbsolutePath(absolutePath string) func(p *ParamsPathSelect) {
	return func(p *ParamsPathSelect) {
		absolutePath = html.EscapeString(absolutePath)
		p.AbsolutePath = sql.Null[string]{
			V:     absolutePath,
			Valid: len(absolutePath) > 0,
		}
	}
}
func PathSelectWithAbsolutePathSearch(absolutePathSearch string) func(p *ParamsPathSelect) {
	return func(p *ParamsPathSelect) {
		absolutePathSearch = html.EscapeString(absolutePathSearch)
		p.AbsolutePathSearch = sql.Null[string]{
			V:     absolutePathSearch,
			Valid: len(absolutePathSearch) > 0,
		}
	}
}

func PathSelectWithIdServiceAndAbsolutePathExist(idCpAuthService algouid.UUID, absolutePath string) func(d *ParamsPathSelect) {
	return func(d *ParamsPathSelect) {
		d.IdServiceExist = sql.Null[algouid.UUID]{
			V:     idCpAuthService,
			Valid: !idCpAuthService.IsNil(),
		}
		d.AbsolutePathExist = sql.Null[string]{
			V:     absolutePath,
			Valid: len(absolutePath) > 0,
		}
	}
}

func PathSelectWithActiveOrDeactivate(isActive bool, isDeactivate bool) func(p *ParamsPathSelect) {
	return func(p *ParamsPathSelect) {
		p.IsActiveOrDeactivate = sql.Null[bool]{
			V:     isActive,
			Valid: isActive != isDeactivate,
		}
	}
}
func PathSelectWithPagination(offset, limit int64) func(p *ParamsPathSelect) {
	return func(p *ParamsPathSelect) {
		p.Offset = offset
		p.Limit = limit
	}
}

func PathSelectWithNoKeyUpdate() func(p *ParamsPathSelect) {
	return func(p *ParamsPathSelect) {
		p.IsNoKeyUpdate = true
	}
}

func PathSelectWithOrderByCreatedAt(isAsc bool) func(p *ParamsPathSelect) {
	return func(p *ParamsPathSelect) {
		p.OrderByCol = "created_at"
		p.IsOrderAsc = isAsc
	}
}

type ParamsPathUpdate struct {
	IdPath       algouid.UUID
	AbsolutePath sql.Null[string]
	DeletedAt    sql.Null[int64]

	IsChange bool
}

func PathUpdateWithIdPath(idPath algouid.UUID) func(p *ParamsPathUpdate) {
	return func(p *ParamsPathUpdate) {
		p.IdPath = idPath
	}
}

func PathUpdateAbsolutePath(oldUrlPath, newUrlPath string) func(p *ParamsPathUpdate) {
	return func(p *ParamsPathUpdate) {
		p.AbsolutePath = sql.Null[string]{
			V:     newUrlPath,
			Valid: oldUrlPath != newUrlPath,
		}
		p.IsChange = p.IsChange || p.AbsolutePath.Valid
	}
}

func PathUpdateActiveState(currentActiveState bool, wantActiveState bool) func(d *ParamsPathUpdate) {
	return func(d *ParamsPathUpdate) {
		tnow := time.Now().Unix()
		if wantActiveState {
			tnow = 0
		}
		d.DeletedAt = sql.Null[int64]{
			V:     tnow,
			Valid: currentActiveState != wantActiveState,
		}
		d.IsChange = d.IsChange || d.DeletedAt.Valid
	}
}
