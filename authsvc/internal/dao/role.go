package authdao

import (
	"context"
	"database/sql"
	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"golang.org/x/net/html"
	"strings"
	"time"
)

type Role struct {
	IdRole   algouid.UUID
	RoleName string
	Priority int64

	pdb.AtUnix
}

type RoleRepository interface {
	Count(ctx context.Context, params *ParamsRoleSelect) (int64, error)
	Select(ctx context.Context, params *ParamsRoleSelect) ([]*Role, error)

	Create(ctx context.Context, authRole ...*Role) error
	Update(ctx context.Context, params ...*ParamsRoleUpdate) error
}

type ParamsRoleSelect struct {
	IdRole         sql.Null[algouid.UUID]
	ListIdRole     []algouid.UUID
	RoleName       sql.Null[string]
	RoleNameSearch sql.Null[string]

	PriorityGte int64
	PriorityLte int64

	PriorityGt int64
	PriorityLt int64

	Offset int64
	Limit  int64

	OrderByCol string
	IsOrderAsc bool

	IsActiveOrDeactivate sql.Null[bool]
	IsNoKeyUpdate        bool
}

func (*Role) Columns() []string {
	return []string{
		"id_auth_role", "role_name", "priority", "created_at", "updated_at", "deleted_at",
	}
}

func (u *Role) Values() []any {
	return []any{
		u.IdRole, u.RoleName, u.Priority, u.CreatedAt, u.UpdatedAt, u.DeletedAt,
	}
}

func (u *Role) FullScan(scan pdb.Scanner) (err error) {
	return scan.Scan(
		&u.IdRole, &u.RoleName, &u.Priority, &u.CreatedAt, &u.UpdatedAt, &u.DeletedAt,
	)
}

func RoleSelectWithIdRole(idRole algouid.UUID) func(*ParamsRoleSelect) {
	return func(p *ParamsRoleSelect) {
		p.IdRole = sql.Null[algouid.UUID]{
			V:     idRole,
			Valid: idRole.IsNil() == false,
		}
	}
}

func RoleSelectWithListIdRole(listIdRole []algouid.UUID) func(*ParamsRoleSelect) {
	return func(p *ParamsRoleSelect) {
		p.ListIdRole = listIdRole
	}
}

func RoleSelectWithRoleName(roleName string) func(*ParamsRoleSelect) {
	return func(p *ParamsRoleSelect) {
		roleName = html.EscapeString(roleName)
		p.RoleName = sql.Null[string]{
			V:     roleName,
			Valid: len(roleName) > 0,
		}
	}
}

func RoleSelectWithRoleNameSearch(roleNameSearch string) func(*ParamsRoleSelect) {
	return func(p *ParamsRoleSelect) {
		roleNameSearch = html.EscapeString(roleNameSearch)
		p.RoleNameSearch = sql.Null[string]{
			V:     roleNameSearch,
			Valid: len(roleNameSearch) > 0,
		}
	}
}
func RoleSelectWithPriorityGt(priorityGt int64) func(*ParamsRoleSelect) {
	return func(p *ParamsRoleSelect) {
		p.PriorityGt = priorityGt
	}
}
func RoleSelectWithPriorityLt(priorityLt int64) func(*ParamsRoleSelect) {
	return func(p *ParamsRoleSelect) {
		p.PriorityLt = priorityLt
	}
}
func RoleSelectWithPriorityGte(priorityGte int64) func(*ParamsRoleSelect) {
	return func(p *ParamsRoleSelect) {
		p.PriorityGte = priorityGte
	}
}

func RoleSelectWithPriorityLte(priorityLte int64) func(*ParamsRoleSelect) {
	return func(p *ParamsRoleSelect) {
		p.PriorityLte = priorityLte
	}
}

func RoleSelectWithActiveOrDeactivate(isActive bool, isDeactivate bool) func(*ParamsRoleSelect) {
	return func(p *ParamsRoleSelect) {
		p.IsActiveOrDeactivate = sql.Null[bool]{
			V:     isActive,
			Valid: !(isDeactivate == isActive),
		}
	}
}

func RoleSelectWithPagination(offset, limit int64) func(*ParamsRoleSelect) {
	return func(p *ParamsRoleSelect) {
		p.Offset = offset
		p.Limit = limit
	}
}
func RoleSelectOrderByPriority() func(p *ParamsRoleSelect) {
	return func(p *ParamsRoleSelect) {
		p.OrderByCol = "priority"
		p.IsOrderAsc = true
	}
}

func RoleSelectOrderByActiveState() func(p *ParamsRoleSelect) {
	return func(p *ParamsRoleSelect) {
		p.OrderByCol = "deleted_at"
		p.IsOrderAsc = true
	}
}

type ParamsRoleUpdate struct {
	IdRole   algouid.UUID
	RoleName sql.Null[string]
	Priority sql.Null[int64]

	DeletedAt sql.Null[int64]

	IsChange bool
}

func RoleUpdateWithIdRole(idRole algouid.UUID) func(*ParamsRoleUpdate) {
	return func(p *ParamsRoleUpdate) {
		p.IdRole = idRole
	}
}

func RoleUpdateRoleName(currentRoleName, newRoleName string) func(*ParamsRoleUpdate) {
	return func(p *ParamsRoleUpdate) {
		p.RoleName = sql.Null[string]{
			V:     newRoleName,
			Valid: strings.Compare(currentRoleName, newRoleName) != 0,
		}
		p.IsChange = p.IsChange || p.RoleName.Valid
	}
}

func RoleUpdatePriority(currentPriority, newPriority int64) func(*ParamsRoleUpdate) {
	return func(p *ParamsRoleUpdate) {
		p.Priority = sql.Null[int64]{
			V:     newPriority,
			Valid: currentPriority != newPriority,
		}
		p.IsChange = p.IsChange || p.Priority.Valid
	}
}

func RoleUpdateActiveState(currentActiveState, wantActiveState bool) func(*ParamsRoleUpdate) {
	return func(p *ParamsRoleUpdate) {
		tnow := time.Now().Unix()
		if wantActiveState {
			tnow = 0
		}
		
		p.DeletedAt = sql.Null[int64]{
			V:     tnow,
			Valid: currentActiveState != wantActiveState,
		}
		p.IsChange = p.IsChange || p.DeletedAt.Valid
	}
}
