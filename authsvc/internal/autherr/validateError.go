package autherr

import "errors"

var (
	IdAppRequiredError     = errors.New("IdApp required")
	IdRoleRequiredError    = errors.New("IdRole required")
	IdUserRequiredError    = errors.New("IdUser required")
	IdServiceRequiredError = errors.New("IdService required")
	IdPathRequiredError    = errors.New("IdPath required")
	IdPolicyRequiredError  = errors.New("IdPolicy required")

	EmailRequiredError                                  = errors.New("email required")
	DomainRequiredError                                 = errors.New("domain required")
	IpAddressRequiredError                              = errors.New("ipAddress required")
	PasswordRequiredError                               = errors.New("password required")
	PasswordLengthMustBeGte8RequiredError               = errors.New("password.length must be greater than 8")
	UserAgentRequiredError                              = errors.New("userAgent required")
	AppTypeRequiredError                                = errors.New("appType required")
	AppTypeNotSupportError                              = errors.New("appType not supported")
	NameRequiredError                                   = errors.New("name required")
	TokenTTLRequiredError                               = errors.New("tokenTTL required")
	AbsolutePathRequiredError                           = errors.New("absolutePath required")
	StateIsActiveAndIsDeactivateNotBeTheSameRequiredErr = errors.New("state.isActive and state.isDeactivate can not be the same")
	FirstNameRequiredError                              = errors.New("firstName required")
	LastNameRequiredError                               = errors.New("lastName required")
	PhoneNumberRequiredError                            = errors.New("phoneNumber required")
	ConfigTypeRequiredError                             = errors.New("config type required")
	ConfigIdRequiredError                               = errors.New("config id required")
	MstRequiredError                                    = errors.New("mst required")
	IdCompanyRequiredError                              = errors.New("company id required")
	RefreshTokenRequiredError                           = errors.New("refresh token required")
	RefreshTokenInvalidError                            = errors.New("refresh token invalid")
	RefreshInternalServiceError                         = errors.New("refresh internal service error")
	RefCodeRequiredError                                = errors.New("ref code required")

	OldPasswordRequiredError = errors.New("password required")
	NewPasswordRequiredError = errors.New("password required")

	ClientIdRequiredError     = errors.New("client id required")
	ClientSecretRequiredError = errors.New("client secret required")
	OAuthTokenRequiredError   = errors.New("oauth token required")
)
