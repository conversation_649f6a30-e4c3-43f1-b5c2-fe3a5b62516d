package autherr

import "github.com/pkg/errors"

var (
	ErrAppExist            = errors.New("app_exist")
	ErrAppNotExist         = errors.New("app_not_exist")
	ErrUserNotExist        = errors.New("user_not_exist")
	ErrUserExist           = errors.New("user_exist")
	ErrRoleExist           = errors.New("role_exist")
	ErrRoleNotExist        = errors.New("role_not_exist")
	ErrServiceExist        = errors.New("service_exist")
	ErrServiceNotExist     = errors.New("service_not_exist")
	ErrPathExist           = errors.New("path_exist")
	ErrPathNotExist        = errors.New("path_not_exist")
	ErrPolicyExist         = errors.New("policy_exist")
	ErrPolicyNotExist      = errors.New("policy_not_exist")
	ErrConfigNotExist      = errors.New("config_not_exist")
	ErrConfigInvalidFormat = errors.New("config_invalid_format")
	ErrCompanyNotFound     = errors.New("company_not_exist")
	ErrMstExist            = errors.New("mst_exist")
	ErrConfigMailNotFound  = errors.New("config_mail_not_exist")
	ErrNotExist            = errors.New("not_exist")
	ErrTokenExpired        = errors.New("token_expired")
	ErrOtpInvalid          = errors.New("otp_invalid")
	ErrRefCodeNotExist     = errors.New("ref_code_not_exist")
	ErrLockSpam            = errors.New("lock_spam")
	ErrOtpRequired         = errors.New("otp_required")
	ErrPasswordNewRequired = errors.New("password_new_required")
)
