package authrepo

import (
	"context"
	"fmt"
	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"github.com/pkg/errors"
	"strings"
	"time"
)

var _ authdao.RoleRepository = (*authRoleImpl)(nil)

func NewAuthRoleRepository(db pdb.DB, tableName string) *authRoleImpl {
	return _initAuthRoleRepository(db, pdb.NewModel(tableName))
}

type authRoleImpl struct {
	db    pdb.DB
	model pdb.Model

	countAuthRoleQuery  string
	selectAuthRoleQuery string
	updateAuthRoleQuery string
}

const (
	authRoleSchema = `CREATE TABLE IF NOT EXISTS "%s" (
 "id_auth_role" uuid PRIMARY KEY,
 "role_name" varchar(64) UNIQUE NOT NULL,
 "priority" int NOT NULL,
 "created_at" bigint NOT NULL,
 "updated_at" bigint NOT NULL,
 "deleted_at" bigint NOT NULL
);`

	selectAuthRoleQueryFmt = "select %s from %s" +
		" where (not $1 or id_auth_role = $2)" +
		" and (not $3 or id_auth_role = any($4))" +
		" and (not $5 or lower(role_name) = lower($6))" +
		" and (not $7 or lower(role_name) like '%%' || lower($8) || '%%')" +
		" and (not $9 or priority >= $10)" +
		" and (not $11 or priority <= $12)" +
		" and (not $13 or priority > $14)" +
		" and (not $15 or priority < $16)" +
		" and (not $17 or (updated_at > deleted_at) = $18)"

	updateAuthRoleQueryFmt = "update %s set" +
		" role_name = coalesce($1,role_name)" +
		", priority = coalesce($2,priority)" +
		", deleted_at = coalesce($3,deleted_at)" +
		", updated_at = $4" +
		" where id_auth_role = $5"
)

func toSliceAuthRoleSelect(params *authdao.ParamsRoleSelect) []any {
	return []any{
		params.IdRole.Valid, params.IdRole,
		len(params.ListIdRole) > 0, params.ListIdRole,
		params.RoleName.Valid, params.RoleName,
		params.RoleNameSearch.Valid, params.RoleNameSearch,
		params.PriorityGte > 0, params.PriorityGte,
		params.PriorityLte > 0, params.PriorityLte,
		params.PriorityGt > 0, params.PriorityGt,
		params.PriorityLt > 0, params.PriorityLt,
		params.IsActiveOrDeactivate.Valid, params.IsActiveOrDeactivate,
	}
}

func toSliceAuthRoleUpdate(params *authdao.ParamsRoleUpdate) []any {
	return []any{
		params.RoleName,
		params.Priority,
		params.DeletedAt,
		time.Now().Unix(),
		params.IdRole,
	}
}

func (a *authRoleImpl) Count(ctx context.Context, params *authdao.ParamsRoleSelect) (total int64, err error) {
	dbCtx := a.db.WithContext(ctx)

	total, err = pdb.Count(dbCtx, a.countAuthRoleQuery, toSliceAuthRoleSelect(params))
	if err != nil {
		return 0, errors.WithStack(err)
	}
	if total == 0 {
		return 0, nil
	}
	return total, nil
}

func (a *authRoleImpl) Select(ctx context.Context, params *authdao.ParamsRoleSelect) (listAuthRole []*authdao.Role, err error) {
	dbCtx := a.db.WithContext(ctx)
	var (
		selectQuery = []string{a.selectAuthRoleQuery}
	)

	if len(params.OrderByCol) > 0 {
		if params.IsOrderAsc {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s asc", params.OrderByCol))
		} else {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s desc", params.OrderByCol))
		}
	}
	if params.Limit > 0 {
		selectQuery = append(selectQuery, fmt.Sprintf("limit %d offset %d", params.Limit, params.Offset))
	}
	if params.IsNoKeyUpdate {
		selectQuery = append(selectQuery, "for no key update")
	}

	listAuthRole, err = pdb.Select[authdao.Role](dbCtx, strings.Join(selectQuery, " "), toSliceAuthRoleSelect(params), func(scanner pdb.Scanner) (t *authdao.Role, err error) {
		t = &authdao.Role{}
		if err = t.FullScan(scanner); err != nil {
			return nil, errors.WithStack(err)
		}
		return t, nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return listAuthRole, nil
}

func (a *authRoleImpl) Create(ctx context.Context, authRole ...*authdao.Role) (err error) {
	if err = pdb.CreateGeneric[*authdao.Role](a.db.WithContext(ctx), a.model, authRole); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (a *authRoleImpl) Update(ctx context.Context, listParams ...*authdao.ParamsRoleUpdate) (err error) {
	var (
		vals [][]any
	)
	for _, param := range listParams {
		if param.IdRole.IsNil() {
			return errors.WithStack(errors.New("id_auth_role must be not nil"))
		}
		if param.IsChange == false {
			return errors.WithStack(errors.New("no col update"))
		}
		vals = append(vals, toSliceAuthRoleUpdate(param))
	}

	if len(vals) == 0 {
		return errors.WithStack(errors.New("no row update"))
	}

	if err = pdb.UpdateWithBatch(a.db.WithContext(ctx), a.updateAuthRoleQuery, vals); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func _initAuthRoleRepository(db pdb.DB, model pdb.Model, listOpts ...pdb.ModelOption) *authRoleImpl {
	authRole := authdao.Role{}
	newModel := model.WithOption(listOpts...)
	return &authRoleImpl{
		db:                  db,
		model:               newModel,
		countAuthRoleQuery:  fmt.Sprintf(selectAuthRoleQueryFmt, "count(1)", newModel.TableName()),
		selectAuthRoleQuery: fmt.Sprintf(selectAuthRoleQueryFmt, strings.Join(authRole.Columns(), ","), newModel.TableName()),
		updateAuthRoleQuery: fmt.Sprintf(updateAuthRoleQueryFmt, newModel.TableName()),
	}
}
