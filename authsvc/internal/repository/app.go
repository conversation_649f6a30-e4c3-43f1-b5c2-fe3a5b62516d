package authrepo

import (
	"context"
	"fmt"
	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"github.com/pkg/errors"
	"strings"
	"time"
)

var _ authdao.AuthAppRepository = (*appImpl)(nil)

func NewAppRepository(db pdb.DB, tableName string) *appImpl {
	return _initAppRepository(db, pdb.NewModel(tableName))
}

type appImpl struct {
	db    pdb.DB
	model pdb.Model

	countAppQuery  string
	selectAppQuery string
	updateAppQuery string
}

const (
	appPathSchema = `CREATE TABLE IF NOT EXISTS "%s" (
 "id_auth_app" uuid PRIMARY KEY,
  "name" varchar(64) UNIQUE NOT NULL,
  "token_ttl" bigint NOT NULL,
  "private_ed25519" varchar(256) NOT NULL,
  "public_ed25519" varchar(256) NOT NULL,
  "created_at" bigint NOT NULL,
  "updated_at" bigint NOT NULL,
  "deleted_at" bigint NOT NULL
);`

	selectAppPathQueryFmt = "select %s from %s" +
		" where (not $1 or id_auth_app = $2)" +
		" and (not $3 or id_auth_app = any($4))" +
		" and (not $5 or app_type = $6)" +
		" and (not $7 or lower(name) = lower($8))" +
		" and (not $9 or lower(name) like '%%' || lower($10) || '%%')" +
		" and (not $11 or (updated_at > deleted_at) = $12)" +
		" and (not $13 or lower(domain) = lower($14))" +
		" and (not $15 or lower(domain) like '%%' || lower($16) || '%%')" +
		" and (not $17 or app_type = $18)"

	updateAppPathQueryFmt = "update %s set" +
		" name = coalesce($1,name)" +
		", token_ttl = coalesce($2,token_ttl)" +
		", private_ed25519 = coalesce($3,private_ed25519)" +
		", public_ed25519 = coalesce($4,public_ed25519)" +
		", app_type = coalesce($5,app_type)" +
		", deleted_at = coalesce($6,deleted_at)" +
		", updated_at = $7" +
		", app_country = coalesce($8,app_country)" +
		" where id_auth_app = $9"
)

func toSliceAppSelect(params *authdao.ParamsAppSelect) []any {
	return []any{
		params.IdApp.Valid, params.IdApp,
		len(params.ListIdApp) > 0, params.ListIdApp,
		params.AppType.Valid, params.AppType,
		params.Name.Valid, params.Name,
		params.NameSearch.Valid, params.NameSearch,
		params.IsActiveOrDeactivate.Valid, params.IsActiveOrDeactivate,
		params.Domain.Valid, params.Domain,
		params.DomainSearch.Valid, params.DomainSearch,
		params.AppCountry.Valid, params.AppCountry,
	}
}
func toSliceAppUpdate(params *authdao.ParamsAppUpdate) []any {
	return []any{
		params.Name,
		params.TokenTTL,
		params.PrivateEd25519,
		params.PubEd25519,
		params.AppType,
		params.DeletedAt,
		time.Now().Unix(),
		params.AppCountry,
		params.IdApp,
	}
}

func (a *appImpl) Count(ctx context.Context, params *authdao.ParamsAppSelect) (total int64, err error) {
	dbCtx := a.db.WithContext(ctx)

	total, err = pdb.Count(dbCtx, a.countAppQuery, toSliceAppSelect(params))
	if err != nil {
		return 0, errors.WithStack(err)
	}
	if total == 0 {
		return 0, nil
	}
	return total, nil
}

func (a *appImpl) Select(ctx context.Context, params *authdao.ParamsAppSelect) (listAuthApp []*authdao.App, err error) {
	dbCtx := a.db.WithContext(ctx)
	var (
		selectQuery = []string{a.selectAppQuery}
	)

	if len(params.OrderByCol) > 0 {
		if params.IsOrderAsc {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s asc", params.OrderByCol))
		} else {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s desc", params.OrderByCol))
		}
	}
	if params.Limit > 0 {
		selectQuery = append(selectQuery, fmt.Sprintf("limit %d offset %d", params.Limit, params.Offset))
	}
	if params.IsNoKeyUpdate {
		selectQuery = append(selectQuery, "for no key update")
	}

	listAuthApp, err = pdb.Select[authdao.App](dbCtx, strings.Join(selectQuery, " "), toSliceAppSelect(params), func(scanner pdb.Scanner) (t *authdao.App, err error) {
		t = &authdao.App{}
		if err = t.FullScan(scanner); err != nil {
			return nil, errors.WithStack(err)
		}
		return t, nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return listAuthApp, nil
}

func (a *appImpl) Create(ctx context.Context, listApp ...*authdao.App) (err error) {
	if err = pdb.CreateGeneric[*authdao.App](a.db.WithContext(ctx), a.model, listApp); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (a *appImpl) Update(ctx context.Context, listParams ...*authdao.ParamsAppUpdate) (err error) {
	var (
		vals [][]any
	)
	for _, param := range listParams {
		if param.IdApp.IsNil() {
			return errors.WithStack(errors.New("id_app must be not nil"))
		}
		if !param.IsChange {
			return errors.WithStack(errors.New("no col update"))
		}
		vals = append(vals, toSliceAppUpdate(param))
	}

	if len(vals) == 0 {
		return errors.WithStack(errors.New("no row update"))
	}

	if err = pdb.UpdateWithBatch(a.db.WithContext(ctx), a.updateAppQuery, vals); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func _initAppRepository(db pdb.DB, model pdb.Model, listOpts ...pdb.ModelOption) *appImpl {
	app := authdao.App{}
	newModel := model.WithOption(listOpts...)
	return &appImpl{
		db:             db,
		model:          newModel,
		countAppQuery:  fmt.Sprintf(selectAppPathQueryFmt, "count(1)", newModel.TableName()),
		selectAppQuery: fmt.Sprintf(selectAppPathQueryFmt, strings.Join(app.Columns(), ","), newModel.TableName()),
		updateAppQuery: fmt.Sprintf(updateAppPathQueryFmt, newModel.TableName()),
	}
}
