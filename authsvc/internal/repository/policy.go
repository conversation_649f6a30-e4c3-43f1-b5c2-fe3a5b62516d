package authrepo

import (
	"context"
	"fmt"
	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	"strings"
	"time"

	"github.com/pkg/errors"

	"git.tmproxy-infra.com/algo/common/pkg/pdb"
)

const (
	authPolicySchema = `CREATE TABLE if not exists "%s" (
   "id_auth_policy" uuid PRIMARY KEY,
  "id_auth_path" uuid NOT NULL,
  "id_auth_role" uuid NOT NULL,
  "created_at" bigint NOT NULL,
  "updated_at" bigint NOT NULL,
  "deleted_at" bigint NOT NULL
);`

	selectAuthPolicyQueryFmt = "select %s from %s" +
		" where (not $1 or id_auth_policy = $2)" +
		" and (not $3 or id_auth_path = $4)" +
		" and (not $5 or id_auth_role = $6)" +
		" and (not $7 or (updated_at > deleted_at) = $8)"
	updateAuthPolicyQueryFmt = "update %s set" +
		" deleted_at = coalesce($1,deleted_at)" +
		", updated_at = $2" +
		" where id_auth_policy = $3"
)

func toSliceAuthPolicySelect(params *authdao.ParamsPolicySelect) []any {
	return []any{
		params.IdPolicy.Valid, params.IdPolicy,
		params.IdPath.Valid, params.IdPath,
		params.IdRole.Valid, params.IdRole,
		params.IsActiveOrDeactivate.Valid, params.IsActiveOrDeactivate,
	}
}

func toSliceAuthPolicyUpdate(param *authdao.ParamsPolicyUpdate) []any {
	return []any{
		param.DeletedAt,
		time.Now().Unix(),
		param.IdPolicy,
	}
}

type authPolicyImpl struct {
	db    pdb.DB
	model pdb.Model

	countAuthPolicyQuery  string
	selectAuthPolicyQuery string
	updateAuthPolicyQuery string
}

func (repo *authPolicyImpl) _initSchema(ctx context.Context) (err error) {
	_, err = repo.db.WithContext(ctx).Exec(fmt.Sprintf(authPolicySchema, repo.model.TableName()))
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (repo *authPolicyImpl) WithOption(listOpts ...pdb.ModelOption) authdao.PolicyRepository {
	return _initAuthPolicyRepository(repo.db, repo.model, listOpts...)
}

func _initAuthPolicyRepository(db pdb.DB, model pdb.Model, listOpts ...pdb.ModelOption) *authPolicyImpl {
	authPolicySchema := authdao.Policy{}
	return &authPolicyImpl{
		db:                    db,
		model:                 model.WithOption(listOpts...),
		countAuthPolicyQuery:  fmt.Sprintf(selectAuthPolicyQueryFmt, "count(1)", model.TableName()),
		selectAuthPolicyQuery: fmt.Sprintf(selectAuthPolicyQueryFmt, strings.Join(authPolicySchema.Columns(), ","), model.TableName()),
		updateAuthPolicyQuery: fmt.Sprintf(updateAuthPolicyQueryFmt, model.TableName()),
	}
}

func (repo *authPolicyImpl) Count(ctx context.Context, params *authdao.ParamsPolicySelect) (total int64, err error) {
	dbCtx := repo.db.WithContext(ctx)

	total, err = pdb.Count(dbCtx, repo.countAuthPolicyQuery, toSliceAuthPolicySelect(params))
	if err != nil {
		return 0, errors.WithStack(err)
	}
	if total == 0 {
		return 0, nil
	}
	return total, nil
}

func (repo *authPolicyImpl) Select(ctx context.Context, params *authdao.ParamsPolicySelect) (listAuthPolicy []*authdao.Policy, err error) {
	dbCtx := repo.db.WithContext(ctx)
	var (
		selectQuery = []string{repo.selectAuthPolicyQuery}
	)

	if len(params.OrderByCol) > 0 {
		if params.IsOrderAsc {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s asc", params.OrderByCol))
		} else {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s desc", params.OrderByCol))
		}
	}
	if params.Limit > 0 {
		selectQuery = append(selectQuery, fmt.Sprintf("limit %d offset %d", params.Limit, params.Offset))
	}
	if params.IsNoKeyUpdate {
		selectQuery = append(selectQuery, "for no key update")
	}

	listAuthPolicy, err = pdb.Select[authdao.Policy](dbCtx, strings.Join(selectQuery, " "), toSliceAuthPolicySelect(params), func(scanner pdb.Scanner) (t *authdao.Policy, err error) {
		t = &authdao.Policy{}
		if err = t.FullScan(scanner); err != nil {
			return nil, errors.WithStack(err)
		}
		return t, nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return listAuthPolicy, nil
}

func (repo *authPolicyImpl) Create(ctx context.Context, listAuthPolicy ...*authdao.Policy) (err error) {
	if err = pdb.CreateGeneric[*authdao.Policy](repo.db.WithContext(ctx), repo.model, listAuthPolicy); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (repo *authPolicyImpl) Update(ctx context.Context, listParams ...*authdao.ParamsPolicyUpdate) (err error) {
	var (
		vals [][]any
	)
	for _, param := range listParams {
		if param.IdPolicy.IsNil() {
			return errors.WithStack(errors.New("id_auth_policy must be not nil"))
		}
		if !param.IsChange {
			return errors.WithStack(errors.New("no col update"))
		}

		vals = append(vals, toSliceAuthPolicyUpdate(param))
	}
	if len(vals) == 0 {
		return errors.WithStack(errors.New("no row update"))
	}
	if err = pdb.UpdateWithBatch(repo.db.WithContext(ctx), repo.updateAuthPolicyQuery, vals); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func NewAuthPolicyRepository(db pdb.DB, tableName string) *authPolicyImpl {
	return _initAuthPolicyRepository(db, pdb.NewModel(tableName))
}
