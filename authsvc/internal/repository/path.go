package authrepo

import (
	"context"
	"fmt"
	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	"strings"
	"time"

	"github.com/pkg/errors"

	"git.tmproxy-infra.com/algo/common/pkg/pdb"
)

const (
	authPathSchema = `CREATE TABLE IF NOT EXISTS "%s" (
  "id_auth_path" uuid PRIMARY KEY,
  "id_auth_service" uuid NOT NULL,
  "absolute_path" varchar(256) UNIQUE NOT NULL,
  "created_at" bigint NOT NULL,
  "updated_at" bigint NOT NULL,
  "deleted_at" bigint NOT NULL
);`

	selectAuthPathQueryFmt = "select %s from %s" +
		" where (not $1 or id_auth_path = $2)" +
		" and (not $3 or id_auth_service = $4)" +
		" and (not $5 or lower(absolute_path) = lower($6))" +
		" and (not $7 or lower(absolute_path) = lower($8))" +
		" and (not $9 or (updated_at > deleted_at) = $10)" +
		" and (not $11 or id_auth_path = any($12))" +
		" and (not $13 or lower(absolute_path) like '%%' || lower($14) || '%%')"
	updateAuthPathQueryFmt = "update %s set" +
		" absolute_path = coalesce($1,absolute_path)" +
		", deleted_at = coalesce($2,deleted_at)" +
		", updated_at = $3" +
		" where id_auth_path = $4"
)

func toSliceAuthPathSelect(params *authdao.ParamsPathSelect) []any {
	return []any{
		params.IdPath.Valid, params.IdPath,
		params.IdService.Valid, params.IdService,
		params.AbsolutePath.Valid, params.AbsolutePath,
		params.AbsolutePathExist.Valid, params.AbsolutePathExist,
		params.IsActiveOrDeactivate.Valid, params.IsActiveOrDeactivate,
		len(params.ListIdPath) > 0, params.ListIdPath,
		params.AbsolutePathSearch.Valid, params.AbsolutePathSearch,
	}
}
func toSliceAuthPathUpdate(param *authdao.ParamsPathUpdate) []any {
	return []any{
		param.AbsolutePath,
		param.DeletedAt,
		time.Now().Unix(),
		param.IdPath,
	}
}

type authPathImpl struct {
	db    pdb.DB
	model pdb.Model

	countAuthPathQuery  string
	selectAuthPathQuery string
	updateAuthPathQuery string
}

func (repo *authPathImpl) _initSchema(ctx context.Context) (err error) {
	_, err = repo.db.WithContext(ctx).Exec(fmt.Sprintf(authPathSchema, repo.model.TableName()))
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (repo *authPathImpl) WithOption(listOpts ...pdb.ModelOption) authdao.PathRepository {
	return _initAuthPathRepository(repo.db, repo.model, listOpts...)
}

func _initAuthPathRepository(db pdb.DB, model pdb.Model, listOpts ...pdb.ModelOption) *authPathImpl {
	authPathSchema := authdao.Path{}
	newModel := model.WithOption(listOpts...)
	return &authPathImpl{
		db:                  db,
		model:               newModel,
		countAuthPathQuery:  fmt.Sprintf(selectAuthPathQueryFmt, "count(1)", newModel.TableName()),
		selectAuthPathQuery: fmt.Sprintf(selectAuthPathQueryFmt, strings.Join(authPathSchema.Columns(), ","), newModel.TableName()),
		updateAuthPathQuery: fmt.Sprintf(updateAuthPathQueryFmt, newModel.TableName()),
	}
}

func (repo *authPathImpl) Count(ctx context.Context, params *authdao.ParamsPathSelect) (total int64, err error) {
	dbCtx := repo.db.WithContext(ctx)

	total, err = pdb.Count(dbCtx, repo.countAuthPathQuery, toSliceAuthPathSelect(params))
	if err != nil {
		return 0, errors.WithStack(err)
	}
	if total == 0 {
		return 0, nil
	}
	return total, nil
}

func (repo *authPathImpl) Select(ctx context.Context, params *authdao.ParamsPathSelect) (listAuthPath []*authdao.Path, err error) {
	dbCtx := repo.db.WithContext(ctx)
	var (
		selectQuery = []string{repo.selectAuthPathQuery}
	)

	if len(params.OrderByCol) > 0 {
		if params.IsOrderAsc {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s asc", params.OrderByCol))
		} else {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s desc", params.OrderByCol))
		}
	}
	if params.Limit > 0 {
		selectQuery = append(selectQuery, fmt.Sprintf("limit %d offset %d", params.Limit, params.Offset))
	}
	if params.IsNoKeyUpdate {
		selectQuery = append(selectQuery, "for no key update")
	}

	listAuthPath, err = pdb.Select[authdao.Path](dbCtx, strings.Join(selectQuery, " "), toSliceAuthPathSelect(params), func(scanner pdb.Scanner) (t *authdao.Path, err error) {
		t = &authdao.Path{}
		if err = t.FullScan(scanner); err != nil {
			return nil, errors.WithStack(err)
		}
		return t, nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return listAuthPath, nil
}

func (repo *authPathImpl) Create(ctx context.Context, listAuthPath ...*authdao.Path) (err error) {
	if err = pdb.CreateGeneric[*authdao.Path](repo.db.WithContext(ctx), repo.model, listAuthPath); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (repo *authPathImpl) Update(ctx context.Context, listParams ...*authdao.ParamsPathUpdate) (err error) {
	var (
		vals [][]any
	)
	for _, param := range listParams {
		if param.IdPath.IsNil() {
			return errors.WithStack(errors.New("id_path must be not nil"))
		}
		if !param.IsChange {
			return errors.WithStack(errors.New("no col update"))
		}
		vals = append(vals, toSliceAuthPathUpdate(param))
	}

	if len(vals) == 0 {
		return errors.WithStack(errors.New("no row update"))
	}

	if err = pdb.UpdateWithBatch(repo.db.WithContext(ctx), repo.updateAuthPathQuery, vals); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func NewAuthPathRepository(db pdb.DB, tableName string) *authPathImpl {
	return _initAuthPathRepository(db, pdb.NewModel(tableName))
}
