package authrepo

import (
	"context"
	"fmt"
	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"github.com/pkg/errors"
	"strings"
	"time"
)

var _ authdao.ConfigTemplateEmailRepository = (*configTemplateEmailImpl)(nil)

func NewConfigTemplateEmailRepository(db pdb.DB, tableName string) *configTemplateEmailImpl {
	return _initConfigTemplateEmailRepository(db, pdb.NewModel(tableName))
}

type configTemplateEmailImpl struct {
	db    pdb.DB
	model pdb.Model

	countConfigTemplateEmailQuery  string
	selectConfigTemplateEmailQuery string
	updateConfigTemplateEmailQuery string
}

const authconfig_template_emailSchema = `CREATE TABLE IF NOT EXISTS "%s" (
     "id_config_template_email" uuid NOT NULL,
     "id_auth_app" uuid,
 "name" varchar,
 "content" string,

     "created_at" bigint NOT NULL,
     "updated_at" bigint NOT NULL,
     "deleted_at" bigint NOT NULL,
    );`

const selectConfigTemplateEmailQueryFmt = `
      SELECT %s 
      FROM %s
      WHERE
    	(NOT $1 OR id_config_template_email = $2)
    	AND (NOT $3 OR id_auth_app = $4)
	AND (NOT $5 OR LOWER(name) LIKE LOWER('%%' || $6 || '%%'))
	AND (NOT $7 OR LOWER(content) LIKE LOWER('%%' || $8 || '%%'))    
    	AND (NOT $9 OR (updated_at > deleted_at) = $10)
    `

const updateConfigTemplateEmailQueryFmt = "update %s set" +
	" id_auth_app = coalesce($1,id_auth_app)," +
	" name = coalesce($2,name)," +
	" content = coalesce($3,content)," +
	" updated_at = $4," +
	" deleted_at = coalesce($5,deleted_at)" +
	" where id_config_template_email = $6"

func toSliceConfigTemplateEmailSelect(params *authdao.ParamsConfigTemplateEmailSelect) []any {
	return []any{
		params.IdConfigTemplateEmail.Valid, params.IdConfigTemplateEmail,
		params.IdAuthApp.Valid, params.IdAuthApp,
		params.Name.Valid, params.Name,
		params.Content.Valid, params.Content,
		params.IsActiveOrDeactivate.Valid, params.IsActiveOrDeactivate,
	}
}

func toSliceConfigTemplateEmailUpdate(params *authdao.ParamsConfigTemplateEmailUpdate) []any {
	return []any{
		params.IdAuthApp,
		params.Name,
		params.Content,
		time.Now().Unix(),
		params.DeletedAt,
		params.IdConfigTemplateEmail,
	}
}

func (u *configTemplateEmailImpl) Count(ctx context.Context, params *authdao.ParamsConfigTemplateEmailSelect) (total int64, err error) {
	dbCtx := u.db.WithContext(ctx)
	total, err = pdb.Count(dbCtx, u.countConfigTemplateEmailQuery, toSliceConfigTemplateEmailSelect(params))
	if err != nil {
		return 0, errors.WithStack(err)
	}
	if total == 0 {
		return 0, nil
	}
	return total, nil
}

func (u *configTemplateEmailImpl) Select(ctx context.Context, params *authdao.ParamsConfigTemplateEmailSelect) (listConfigTemplateEmail []*authdao.ConfigTemplateEmail, err error) {
	dbCtx := u.db.WithContext(ctx)
	var (
		selectQuery = []string{u.selectConfigTemplateEmailQuery}
	)

	if len(params.OrderByCol) > 0 {
		if params.IsOrderAsc {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s asc", params.OrderByCol))
		} else {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s desc", params.OrderByCol))
		}
	}
	if params.Limit > 0 {
		selectQuery = append(selectQuery, fmt.Sprintf("limit %d offset %d", params.Limit, params.Offset))
	}
	if params.IsNoKeyUpdate {
		selectQuery = append(selectQuery, "for no key update")
	}

	listConfigTemplateEmail, err = pdb.Select[authdao.ConfigTemplateEmail](dbCtx, strings.Join(selectQuery, " "), toSliceConfigTemplateEmailSelect(params), func(scanner pdb.Scanner) (t *authdao.ConfigTemplateEmail, err error) {
		t = &authdao.ConfigTemplateEmail{}
		if err = t.FullScan(scanner); err != nil {
			return nil, errors.WithStack(err)
		}
		return t, nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return listConfigTemplateEmail, nil
}

func (u *configTemplateEmailImpl) Create(ctx context.Context, configTemplateEmail ...*authdao.ConfigTemplateEmail) (err error) {
	if err = pdb.CreateGeneric[*authdao.ConfigTemplateEmail](u.db.WithContext(ctx), u.model, configTemplateEmail); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (u *configTemplateEmailImpl) Update(ctx context.Context, listParams ...*authdao.ParamsConfigTemplateEmailUpdate) (err error) {
	var (
		vals [][]any
	)
	for _, param := range listParams {
		if param.IdConfigTemplateEmail.IsNil() == true {
			return errors.WithStack(errors.New("id_configTemplateEmail must be not nil"))
		}
		vals = append(vals, toSliceConfigTemplateEmailUpdate(param))
	}

	if err = pdb.UpdateWithBatch(u.db.WithContext(ctx), u.updateConfigTemplateEmailQuery, vals); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func _initConfigTemplateEmailRepository(db pdb.DB, model pdb.Model, listOpts ...pdb.ModelOption) *configTemplateEmailImpl {
	configTemplateEmail := authdao.ConfigTemplateEmail{}
	newModel := model.WithOption(listOpts...)
	return &configTemplateEmailImpl{
		db:                             db,
		model:                          newModel,
		countConfigTemplateEmailQuery:  fmt.Sprintf(selectConfigTemplateEmailQueryFmt, "count(1)", newModel.TableName()),
		selectConfigTemplateEmailQuery: fmt.Sprintf(selectConfigTemplateEmailQueryFmt, strings.Join(configTemplateEmail.Columns(), ","), newModel.TableName()),
		updateConfigTemplateEmailQuery: fmt.Sprintf(updateConfigTemplateEmailQueryFmt, newModel.TableName()),
	}
}
