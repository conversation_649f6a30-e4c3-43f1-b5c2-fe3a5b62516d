package authrepo

import (
	"context"
	"fmt"
	"strings"
	"time"

	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"github.com/pkg/errors"
)

var _ authdao.CompanyRepository = (*companyImpl)(nil)

func NewCompanyRepository(db pdb.DB, tableName string) *companyImpl {
	return _initCompanyRepository(db, pdb.NewModel(tableName))
}

type companyImpl struct {
	db    pdb.DB
	model pdb.Model

	countCompanyQuery  string
	selectCompanyQuery string
	updateCompanyQuery string
}

const authCompanySchema = `CREATE TABLE IF NOT EXISTS "%s" (
 "id_company" uuid NOT NULL,
 "name" varchar NOT NULL,
 "street" varchar,
 "city" varchar,
 "country" varchar,
 "logo" varchar,
 "mst" varchar,
 "verify" bit,
 "created_at" bigint NOT NULL,
 "updated_at" bigint NOT NULL,
 "deleted_at" bigint NOT NULL,
);`

const selectAuthCompanyQueryFmt = `
  SELECT %s 
  FROM %s
  WHERE
    (NOT $1 OR id_company = $2)
    AND (NOT $3 OR LOWER(name) LIKE LOWER('%%' || $4 || '%%'))
    AND (NOT $5 OR LOWER(street) LIKE LOWER('%%' || $6 || '%%'))
    AND (NOT $7 OR id_state = $8)
    AND (NOT $9 OR id_city = $10)
    AND (NOT $11 OR id_country = $12)
    AND (NOT $13 OR mst = $14)
    AND (NOT $15 OR verify = $16)
    AND (NOT $17 OR (updated_at > deleted_at) = $18)
    AND (NOT $19 OR id_auth_app = $20)

`

const updateAuthCompanyQueryFmt = "update %s set" +
	" name = coalesce($1,name)" +
	", street = coalesce($2,street)" +
	", id_state = coalesce($3,id_state)" +
	", id_city = coalesce($4,id_city)" +
	", id_country = coalesce($5,id_country)" +
	", logo = coalesce($6,logo)" +
	", mst = coalesce($7,mst)" +
	", verify = coalesce($8,verify)" +
	", updated_at = $9" +
	", deleted_at = coalesce($10,deleted_at)" +
	" where id_company = $11"

func toSliceCompanySelect(params *authdao.ParamsCompanySelect) []any {
	return []any{
		params.IdCompany.Valid, params.IdCompany,
		params.Name.Valid, params.Name,
		params.Street.Valid, params.Street,
		params.IdState.Valid, params.IdState,
		params.IdCity.Valid, params.IdCity,
		params.IdCountry.Valid, params.IdCountry,
		params.Mst.Valid, params.Mst,
		params.Verify.Valid, params.Verify,
		params.IsActiveOrDeactivate.Valid, params.IsActiveOrDeactivate,
		params.IdAuthApp.Valid, params.IdAuthApp,
	}
}

func toSliceCompanyUpdate(params *authdao.ParamsCompanyUpdate) []any {
	return []any{
		params.Name,
		params.Street,
		params.IdState,
		params.IdCity,
		params.IdCountry,
		params.Logo,
		params.Mst,
		params.Verify,
		time.Now().Unix(),
		params.DeletedAt,
		params.IdCompany,
	}
}

func (u *companyImpl) Count(ctx context.Context, params *authdao.ParamsCompanySelect) (total int64, err error) {
	dbCtx := u.db.WithContext(ctx)
	total, err = pdb.Count(dbCtx, u.countCompanyQuery, toSliceCompanySelect(params))
	if err != nil {
		return 0, errors.WithStack(err)
	}
	if total == 0 {
		return 0, nil
	}
	return total, nil
}

func (u *companyImpl) Select(ctx context.Context, params *authdao.ParamsCompanySelect) (listCompany []*authdao.Company, err error) {
	dbCtx := u.db.WithContext(ctx)
	var (
		selectQuery = []string{u.selectCompanyQuery}
	)

	if len(params.OrderByCol) > 0 {
		if params.IsOrderAsc {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s asc", params.OrderByCol))
		} else {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s desc", params.OrderByCol))
		}
	}
	if params.Limit > 0 {
		selectQuery = append(selectQuery, fmt.Sprintf("limit %d offset %d", params.Limit, params.Offset))
	}
	if params.IsNoKeyUpdate {
		selectQuery = append(selectQuery, "for no key update")
	}

	listCompany, err = pdb.Select[authdao.Company](dbCtx, strings.Join(selectQuery, " "), toSliceCompanySelect(params), func(scanner pdb.Scanner) (t *authdao.Company, err error) {
		t = &authdao.Company{}
		if err = t.FullScan(scanner); err != nil {
			return nil, errors.WithStack(err)
		}
		return t, nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return listCompany, nil
}

func (u *companyImpl) Create(ctx context.Context, company ...*authdao.Company) (err error) {
	if err = pdb.CreateGeneric[*authdao.Company](u.db.WithContext(ctx), u.model, company); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (u *companyImpl) Delete(ctx context.Context, company ...*authdao.Company) (err error) {
	if err = pdb.CreateGeneric[*authdao.Company](u.db.WithContext(ctx), u.model, company); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (u *companyImpl) Update(ctx context.Context, listParams ...*authdao.ParamsCompanyUpdate) (err error) {
	var (
		vals [][]any
	)
	for _, param := range listParams {
		if param.IdCompany.IsNil() == true {
			return errors.WithStack(errors.New("id_company must be not nil"))
		}
		vals = append(vals, toSliceCompanyUpdate(param))
	}

	if len(vals) == 0 {
		return errors.WithStack(errors.New("no row update"))
	}

	if err = pdb.UpdateWithBatch(u.db.WithContext(ctx), u.updateCompanyQuery, vals); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func _initCompanyRepository(db pdb.DB, model pdb.Model, listOpts ...pdb.ModelOption) *companyImpl {
	company := authdao.Company{}
	newModel := model.WithOption(listOpts...)
	return &companyImpl{
		db:                 db,
		model:              newModel,
		countCompanyQuery:  fmt.Sprintf(selectAuthCompanyQueryFmt, "count(1)", newModel.TableName()),
		selectCompanyQuery: fmt.Sprintf(selectAuthCompanyQueryFmt, strings.Join(company.Columns(), ","), newModel.TableName()),
		updateCompanyQuery: fmt.Sprintf(updateAuthCompanyQueryFmt, newModel.TableName()),
	}
}
