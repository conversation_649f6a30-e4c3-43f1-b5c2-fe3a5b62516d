package authrepo

import (
	"context"
	"fmt"
	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"github.com/pkg/errors"
	"strings"
	"time"
)

var _ authdao.ConfigMailRepository = (*configMailImpl)(nil)

func NewConfigMailRepository(db pdb.DB, tableName string) *configMailImpl {
	return _initConfigMailRepository(db, pdb.NewModel(tableName))
}

type configMailImpl struct {
	db    pdb.DB
	model pdb.Model

	countConfigMailQuery  string
	selectConfigMailQuery string
	updateConfigMailQuery string
}

const authconfig_mail_Schema = `CREATE TABLE IF NOT EXISTS "%s" (
     "id_config_mail" uuid NOT NULL,
     "id_auth_app" uuid,
 "smtp_server_address" varchar,
 "smtp_server_port" int,
 "auth_username" varchar,
 "auth_password" varchar,
 "sender_email" varchar,

     "created_at" bigint NOT NULL,
     "updated_at" bigint NOT NULL,
     "deleted_at" bigint NOT NULL,
    );`

const selectConfigMailQueryFmt = `
      SELECT %s 
      FROM %s
      WHERE
    	(NOT $1 OR id_config_mail = $2)
    	AND (NOT $3 OR id_auth_app = $4)
	AND (NOT $5 OR LOWER(smtp_server_address) LIKE LOWER('%%' || $6 || '%%'))
	AND (NOT $7 OR smtp_server_port = $8)
	AND (NOT $9 OR LOWER(auth_username) LIKE LOWER('%%' || $10 || '%%'))
	AND (NOT $11 OR LOWER(auth_password) LIKE LOWER('%%' || $12 || '%%'))
	AND (NOT $13 OR LOWER(sender_email) LIKE LOWER('%%' || $14 || '%%'))    
    	AND (NOT $15 OR (updated_at > deleted_at) = $16)
    `

const updateConfigMailQueryFmt = "update %s set" +
	" id_auth_app = coalesce($1,id_auth_app)," +
	" smtp_server_address = coalesce($2,smtp_server_address)," +
	" smtp_server_port = coalesce($3,smtp_server_port)," +
	" auth_username = coalesce($4,auth_username)," +
	" auth_password = coalesce($5,auth_password)," +
	" sender_email = coalesce($6,sender_email)," +
	" updated_at = $7," +
	" deleted_at = coalesce($8,deleted_at)" +
	" where id_config_mail = $9"

func toSliceConfigMailSelect(params *authdao.ParamsConfigMailSelect) []any {
	return []any{
		params.IdConfigMail.Valid, params.IdConfigMail,
		params.IdAuthApp.Valid, params.IdAuthApp,
		params.SmtpServerAddress.Valid, params.SmtpServerAddress,
		params.SmtpServerPort.Valid, params.SmtpServerPort,
		params.AuthUsername.Valid, params.AuthUsername,
		params.AuthPassword.Valid, params.AuthPassword,
		params.SenderEmail.Valid, params.SenderEmail,
		params.IsActiveOrDeactivate.Valid, params.IsActiveOrDeactivate,
	}
}

func toSliceConfigMailUpdate(params *authdao.ParamsConfigMailUpdate) []any {
	return []any{
		params.IdAuthApp,
		params.SmtpServerAddress,
		params.SmtpServerPort,
		params.AuthUsername,
		params.AuthPassword,
		params.SenderEmail,
		time.Now().Unix(),
		params.DeletedAt,
		params.IdConfigMail,
	}
}

func (u *configMailImpl) Count(ctx context.Context, params *authdao.ParamsConfigMailSelect) (total int64, err error) {
	dbCtx := u.db.WithContext(ctx)
	total, err = pdb.Count(dbCtx, u.countConfigMailQuery, toSliceConfigMailSelect(params))
	if err != nil {
		return 0, errors.WithStack(err)
	}
	if total == 0 {
		return 0, nil
	}
	return total, nil
}

func (u *configMailImpl) Select(ctx context.Context, params *authdao.ParamsConfigMailSelect) (listConfigMail []*authdao.ConfigMail, err error) {
	dbCtx := u.db.WithContext(ctx)
	var (
		selectQuery = []string{u.selectConfigMailQuery}
	)

	if len(params.OrderByCol) > 0 {
		if params.IsOrderAsc {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s asc", params.OrderByCol))
		} else {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s desc", params.OrderByCol))
		}
	}
	if params.Limit > 0 {
		selectQuery = append(selectQuery, fmt.Sprintf("limit %d offset %d", params.Limit, params.Offset))
	}
	if params.IsNoKeyUpdate {
		selectQuery = append(selectQuery, "for no key update")
	}

	listConfigMail, err = pdb.Select[authdao.ConfigMail](dbCtx, strings.Join(selectQuery, " "), toSliceConfigMailSelect(params), func(scanner pdb.Scanner) (t *authdao.ConfigMail, err error) {
		t = &authdao.ConfigMail{}
		if err = t.FullScan(scanner); err != nil {
			return nil, errors.WithStack(err)
		}
		return t, nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return listConfigMail, nil
}

func (u *configMailImpl) Create(ctx context.Context, configMail ...*authdao.ConfigMail) (err error) {
	if err = pdb.CreateGeneric[*authdao.ConfigMail](u.db.WithContext(ctx), u.model, configMail); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (u *configMailImpl) Update(ctx context.Context, listParams ...*authdao.ParamsConfigMailUpdate) (err error) {
	var (
		vals [][]any
	)
	for _, param := range listParams {
		if param.IdConfigMail.IsNil() == true {
			return errors.WithStack(errors.New("id_configMail must be not nil"))
		}
		vals = append(vals, toSliceConfigMailUpdate(param))
	}

	if err = pdb.UpdateWithBatch(u.db.WithContext(ctx), u.updateConfigMailQuery, vals); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func _initConfigMailRepository(db pdb.DB, model pdb.Model, listOpts ...pdb.ModelOption) *configMailImpl {
	configMail := authdao.ConfigMail{}
	newModel := model.WithOption(listOpts...)
	return &configMailImpl{
		db:                    db,
		model:                 newModel,
		countConfigMailQuery:  fmt.Sprintf(selectConfigMailQueryFmt, "count(1)", newModel.TableName()),
		selectConfigMailQuery: fmt.Sprintf(selectConfigMailQueryFmt, strings.Join(configMail.Columns(), ","), newModel.TableName()),
		updateConfigMailQuery: fmt.Sprintf(updateConfigMailQueryFmt, newModel.TableName()),
	}
}
