package authrepo

import (
	"context"
	"fmt"
	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"github.com/pkg/errors"
	"strings"
	"time"
)

var _ authdao.DeviceRepository = (*deviceImpl)(nil)

func NewDeviceRepository(db pdb.DB, tableName string) *deviceImpl {
	return _initDeviceRepository(db, pdb.NewModel(tableName))
}

type deviceImpl struct {
	db    pdb.DB
	model pdb.Model

	countDeviceQuery  string
	selectDeviceQuery string
	updateDeviceQuery string
}

const authdeviceSchema = `CREATE TABLE IF NOT EXISTS "%s" (
     "id_device" uuid NOT NULL,
     "id_auth_app" uuid,
 "id_auth_user" uuid,
 "device_id" varchar,
 "device_name" varchar,
 "refresh_token" string,
 "token_expiry" bigint,
 "last_used_at" bigint,

     "created_at" bigint NOT NULL,
     "updated_at" bigint NOT NULL,
     "deleted_at" bigint NOT NULL,
    );`

const selectDeviceQueryFmt = `
      SELECT %s 
      FROM %s
      WHERE
    	(NOT $1 OR id_device = $2)
    	AND (NOT $3 OR id_auth_app = $4)
	AND (NOT $5 OR id_auth_user = $6)
	AND (NOT $7 OR LOWER(device_id) LIKE LOWER('%%' || $8 || '%%'))
	AND (NOT $9 OR LOWER(device_name) LIKE LOWER('%%' || $10 || '%%'))
	AND (NOT $11 OR LOWER(refresh_token) LIKE LOWER('%%' || $12 || '%%'))
	AND (NOT $13 OR token_expiry = $14)
	AND (NOT $15 OR last_used_at = $16)    
    	AND (NOT $17 OR (updated_at > deleted_at) = $18)
    `

const updateDeviceQueryFmt = "update %s set" +
	" id_auth_app = coalesce($1,id_auth_app)," +
	" id_auth_user = coalesce($2,id_auth_user)," +
	" device_id = coalesce($3,device_id)," +
	" device_name = coalesce($4,device_name)," +
	" refresh_token = coalesce($5,refresh_token)," +
	" token_expiry = coalesce($6,token_expiry)," +
	" last_used_at = coalesce($7,last_used_at)," +
	" updated_at = $8," +
	" deleted_at = coalesce($9,deleted_at)" +
	" where id_device = $10"

func toSliceDeviceSelect(params *authdao.ParamsDeviceSelect) []any {
	return []any{
		params.IdDevice.Valid, params.IdDevice,
		params.IdAuthApp.Valid, params.IdAuthApp,
		params.IdAuthUser.Valid, params.IdAuthUser,
		params.DeviceId.Valid, params.DeviceId,
		params.DeviceName.Valid, params.DeviceName,
		params.RefreshToken.Valid, params.RefreshToken,
		params.TokenExpiry.Valid, params.TokenExpiry,
		params.LastUsedAt.Valid, params.LastUsedAt,
		params.IsActiveOrDeactivate.Valid, params.IsActiveOrDeactivate,
	}
}

func toSliceDeviceUpdate(params *authdao.ParamsDeviceUpdate) []any {
	return []any{
		params.IdAuthApp,
		params.IdAuthUser,
		params.DeviceId,
		params.DeviceName,
		params.RefreshToken,
		params.TokenExpiry,
		params.LastUsedAt,
		time.Now().Unix(),
		params.DeletedAt,
		params.IdDevice,
	}
}

func (u *deviceImpl) Count(ctx context.Context, params *authdao.ParamsDeviceSelect) (total int64, err error) {
	dbCtx := u.db.WithContext(ctx)
	total, err = pdb.Count(dbCtx, u.countDeviceQuery, toSliceDeviceSelect(params))
	if err != nil {
		return 0, errors.WithStack(err)
	}
	if total == 0 {
		return 0, nil
	}
	return total, nil
}

func (u *deviceImpl) Select(ctx context.Context, params *authdao.ParamsDeviceSelect) (listDevice []*authdao.Device, err error) {
	dbCtx := u.db.WithContext(ctx)
	var (
		selectQuery = []string{u.selectDeviceQuery}
	)

	if len(params.OrderByCol) > 0 {
		if params.IsOrderAsc {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s asc", params.OrderByCol))
		} else {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s desc", params.OrderByCol))
		}
	}
	if params.Limit > 0 {
		selectQuery = append(selectQuery, fmt.Sprintf("limit %d offset %d", params.Limit, params.Offset))
	}
	if params.IsNoKeyUpdate {
		selectQuery = append(selectQuery, "for no key update")
	}

	listDevice, err = pdb.Select[authdao.Device](dbCtx, strings.Join(selectQuery, " "), toSliceDeviceSelect(params), func(scanner pdb.Scanner) (t *authdao.Device, err error) {
		t = &authdao.Device{}
		if err = t.FullScan(scanner); err != nil {
			return nil, errors.WithStack(err)
		}
		return t, nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return listDevice, nil
}

func (u *deviceImpl) Create(ctx context.Context, device ...*authdao.Device) (err error) {
	if err = pdb.CreateGeneric[*authdao.Device](u.db.WithContext(ctx), u.model, device); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (u *deviceImpl) Update(ctx context.Context, listParams ...*authdao.ParamsDeviceUpdate) (err error) {
	var (
		vals [][]any
	)
	for _, param := range listParams {
		if param.IdDevice.IsNil() == true {
			return errors.WithStack(errors.New("id_device must be not nil"))
		}
		vals = append(vals, toSliceDeviceUpdate(param))
	}

	if err = pdb.UpdateWithBatch(u.db.WithContext(ctx), u.updateDeviceQuery, vals); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func _initDeviceRepository(db pdb.DB, model pdb.Model, listOpts ...pdb.ModelOption) *deviceImpl {
	device := authdao.Device{}
	newModel := model.WithOption(listOpts...)
	return &deviceImpl{
		db:                db,
		model:             newModel,
		countDeviceQuery:  fmt.Sprintf(selectDeviceQueryFmt, "count(1)", newModel.TableName()),
		selectDeviceQuery: fmt.Sprintf(selectDeviceQueryFmt, strings.Join(device.Columns(), ","), newModel.TableName()),
		updateDeviceQuery: fmt.Sprintf(updateDeviceQueryFmt, newModel.TableName()),
	}
}
