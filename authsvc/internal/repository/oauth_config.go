package authrepo

import (
	"context"
	"fmt"
	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"github.com/pkg/errors"
	"strings"
	"time"
)

var _ authdao.OAuthConfigRepository = (*oauthconfigImpl)(nil)

func NewOAuthConfigRepository(db pdb.DB, tableName string) *oauthconfigImpl {
	return _initOAuthConfigRepository(db, pdb.NewModel(tableName))
}

type oauthconfigImpl struct {
	db    pdb.DB
	model pdb.Model

	countOAuthConfigQuery  string
	selectOAuthConfigQuery string
	updateOAuthConfigQuery string
}

const autho_auth_configSchema = `CREATE TABLE IF NOT EXISTS "%s" (
     "id_o_auth_config" uuid NOT NULL,
     "id_auth_app" uuid,
 "client_id" varchar,
 "client_secret" varchar,

     "created_at" bigint NOT NULL,
     "updated_at" bigint NOT NULL,
     "deleted_at" bigint NOT NULL,
    );`

const selectOAuthConfigQueryFmt = `
      SELECT %s 
      FROM %s
      WHERE
    	(NOT $1 OR id_o_auth_config = $2)
    	AND (NOT $3 OR id_auth_app = $4)
	AND (NOT $5 OR LOWER(client_id) LIKE LOWER('%%' || $6 || '%%'))
	AND (NOT $7 OR LOWER(client_secret) LIKE LOWER('%%' || $8 || '%%'))    
    	AND (NOT $9 OR (updated_at > deleted_at) = $10)
    `

const updateOAuthConfigQueryFmt = "update %s set" +
	" id_auth_app = coalesce($1,id_auth_app)," +
	" client_id = coalesce($2,client_id)," +
	" client_secret = coalesce($3,client_secret)," +
	" updated_at = $4," +
	" deleted_at = coalesce($5,deleted_at)" +
	" where id_o_auth_config = $6"

func toSliceOAuthConfigSelect(params *authdao.ParamsOAuthConfigSelect) []any {
	return []any{
		params.IdOAuthConfig.Valid, params.IdOAuthConfig,
		params.IdAuthApp.Valid, params.IdAuthApp,
		params.ClientId.Valid, params.ClientId,
		params.ClientSecret.Valid, params.ClientSecret,
		params.IsActiveOrDeactivate.Valid, params.IsActiveOrDeactivate,
	}
}

func toSliceOAuthConfigUpdate(params *authdao.ParamsOAuthConfigUpdate) []any {
	return []any{
		params.IdAuthApp,
		params.ClientId,
		params.ClientSecret,
		time.Now().Unix(),
		params.DeletedAt,
		params.IdOAuthConfig,
	}
}

func (u *oauthconfigImpl) Count(ctx context.Context, params *authdao.ParamsOAuthConfigSelect) (total int64, err error) {
	dbCtx := u.db.WithContext(ctx)
	total, err = pdb.Count(dbCtx, u.countOAuthConfigQuery, toSliceOAuthConfigSelect(params))
	if err != nil {
		return 0, errors.WithStack(err)
	}
	if total == 0 {
		return 0, nil
	}
	return total, nil
}

func (u *oauthconfigImpl) Select(ctx context.Context, params *authdao.ParamsOAuthConfigSelect) (listOAuthConfig []*authdao.OAuthConfig, err error) {
	dbCtx := u.db.WithContext(ctx)
	var (
		selectQuery = []string{u.selectOAuthConfigQuery}
	)

	if len(params.OrderByCol) > 0 {
		if params.IsOrderAsc {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s asc", params.OrderByCol))
		} else {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s desc", params.OrderByCol))
		}
	}
	if params.Limit > 0 {
		selectQuery = append(selectQuery, fmt.Sprintf("limit %d offset %d", params.Limit, params.Offset))
	}
	if params.IsNoKeyUpdate {
		selectQuery = append(selectQuery, "for no key update")
	}

	listOAuthConfig, err = pdb.Select[authdao.OAuthConfig](dbCtx, strings.Join(selectQuery, " "), toSliceOAuthConfigSelect(params), func(scanner pdb.Scanner) (t *authdao.OAuthConfig, err error) {
		t = &authdao.OAuthConfig{}
		if err = t.FullScan(scanner); err != nil {
			return nil, errors.WithStack(err)
		}
		return t, nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return listOAuthConfig, nil
}

func (u *oauthconfigImpl) Create(ctx context.Context, oauthconfig ...*authdao.OAuthConfig) (err error) {
	if err = pdb.CreateGeneric[*authdao.OAuthConfig](u.db.WithContext(ctx), u.model, oauthconfig); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (u *oauthconfigImpl) Update(ctx context.Context, listParams ...*authdao.ParamsOAuthConfigUpdate) (err error) {
	var (
		vals [][]any
	)
	for _, param := range listParams {
		if param.IdOAuthConfig.IsNil() == true {
			return errors.WithStack(errors.New("id_oauthconfig must be not nil"))
		}
		vals = append(vals, toSliceOAuthConfigUpdate(param))
	}

	if err = pdb.UpdateWithBatch(u.db.WithContext(ctx), u.updateOAuthConfigQuery, vals); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func _initOAuthConfigRepository(db pdb.DB, model pdb.Model, listOpts ...pdb.ModelOption) *oauthconfigImpl {
	oauthconfig := authdao.OAuthConfig{}
	newModel := model.WithOption(listOpts...)
	return &oauthconfigImpl{
		db:                     db,
		model:                  newModel,
		countOAuthConfigQuery:  fmt.Sprintf(selectOAuthConfigQueryFmt, "count(1)", newModel.TableName()),
		selectOAuthConfigQuery: fmt.Sprintf(selectOAuthConfigQueryFmt, strings.Join(oauthconfig.Columns(), ","), newModel.TableName()),
		updateOAuthConfigQuery: fmt.Sprintf(updateOAuthConfigQueryFmt, newModel.TableName()),
	}
}
