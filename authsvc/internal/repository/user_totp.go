package authrepo

import (
	"context"
	"fmt"
	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"github.com/pkg/errors"
	"strings"
	"time"
)

var _ authdao.UserTotpRepository = (*authUserTotpImpl)(nil)

func NewAuthUserTotpRepository(db pdb.DB, tableName string) *authUserTotpImpl {
	return _initAuthUserTotpRepository(db, pdb.NewModel(tableName))
}

type authUserTotpImpl struct {
	db    pdb.DB
	model pdb.Model

	countAuthUserTotpQuery  string
	selectAuthUserTotpQuery string
	updateAuthUserTotpQuery string
}

const (
	authUserTotpSchema = `CREATE TABLE IF NOT EXISTS "%s" (
 "id_auth_user_totp" uuid PRIMARY KEY,
 "id_auth_user" uuid NOT NULL,
 "totp_type" int NOT NULL,
 "secret" varchar(256) NOT NULL,
 "created_at" bigint NOT NULL,
 "updated_at" bigint NOT NULL,
 "deleted_at" bigint NOT NULL
);`

	selectAuthUserTotpQueryFmt = "select %s from %s" +
		" where (not $1 or id_auth_user_totp = $2)" +
		" and (not $3 or id_auth_user_totp = any($4))" +
		" and (not $5 or id_auth_user = $6)" +
		" and (not $7 or id_auth_user = any($8))" +
		" and (not $9 or totp_type = $10)" +
		" and (not $11 or secret = $12)" +
		" and (not $13 or created_at >= $14)" +
		" and (not $15 or created_at <= $16)" +
		" and (not $17 or (updated_at > deleted_at) = $18)" +
		" and (not $19 or id_auth_user = any($20))"

	updateAuthUserTotpQueryFmt = "update %s set" +
		" totp_type = coalesce($1,totp_type)" +
		", secret = coalesce($2,secret)" +
		", deleted_at = coalesce($3,deleted_at)" +
		", updated_at = $4" +
		" where id_auth_user_totp = $5"
)

func toSliceAuthUserTotpSelect(params *authdao.ParamsUserTotpSelect) []any {
	return []any{
		params.IdUserTotp.Valid, params.IdUserTotp,
		len(params.ListIdUserTotp) > 0, params.ListIdUserTotp,
		params.IdUser.Valid, params.IdUser,
		len(params.ListIdUser) > 0, params.ListIdUser,
		params.TotpType.Valid, params.TotpType,
		params.Secret.Valid, params.Secret,
		params.CreatedAtGte > 0, params.CreatedAtGte,
		params.CreatedAtLte > 0, params.CreatedAtLte,
		params.IsActiveOrDeactivate.Valid, params.IsActiveOrDeactivate,
		len(params.ListIdUser) > 0, params.ListIdUser,
	}
}

func toSliceAuthUserTotpUpdate(params *authdao.ParamsUserTotpUpdate) []any {
	return []any{
		params.TotpType,
		params.Secret,
		params.DeletedAt,
		time.Now().Unix(),
		params.IdUserTotp,
	}
}

func (p *authUserTotpImpl) Count(ctx context.Context, params *authdao.ParamsUserTotpSelect) (total int64, err error) {
	dbCtx := p.db.WithContext(ctx)

	total, err = pdb.Count(dbCtx, p.countAuthUserTotpQuery, toSliceAuthUserTotpSelect(params))
	if err != nil {
		return 0, errors.WithStack(err)
	}
	if total == 0 {
		return 0, nil
	}
	return total, nil
}

func (p *authUserTotpImpl) Select(ctx context.Context, params *authdao.ParamsUserTotpSelect) (listAuthUserTotp []*authdao.UserTotp, err error) {
	dbCtx := p.db.WithContext(ctx)
	var (
		selectQuery = []string{p.selectAuthUserTotpQuery}
	)

	if len(params.OrderByCol) > 0 {
		if params.IsOrderAsc {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s asc", params.OrderByCol))
		} else {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s desc", params.OrderByCol))
		}
	}
	if params.Limit > 0 {
		selectQuery = append(selectQuery, fmt.Sprintf("limit %d offset %d", params.Limit, params.Offset))
	}
	if params.IsNoKeyUpdate {
		selectQuery = append(selectQuery, "for no key update")
	}

	listAuthUserTotp, err = pdb.Select[authdao.UserTotp](dbCtx, strings.Join(selectQuery, " "), toSliceAuthUserTotpSelect(params), func(scanner pdb.Scanner) (t *authdao.UserTotp, err error) {
		t = &authdao.UserTotp{}
		if err = t.FullScan(scanner); err != nil {
			return nil, errors.WithStack(err)
		}
		return t, nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return listAuthUserTotp, nil
}

func (p *authUserTotpImpl) Create(ctx context.Context, authUserTotp ...*authdao.UserTotp) (err error) {
	if err = pdb.CreateGeneric[*authdao.UserTotp](p.db.WithContext(ctx), p.model, authUserTotp); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (p *authUserTotpImpl) Update(ctx context.Context, listParams ...*authdao.ParamsUserTotpUpdate) (err error) {
	var (
		vals [][]any
	)
	for _, param := range listParams {
		if param.IdUserTotp.IsNil() {
			return errors.WithStack(errors.New("id_user_totp must be not nil"))
		}
		if param.IsChange == false {
			return errors.WithStack(errors.New("no col update"))
		}
		vals = append(vals, toSliceAuthUserTotpUpdate(param))
	}

	if len(vals) == 0 {
		return errors.WithStack(errors.New("no row update"))
	}

	if err = pdb.UpdateWithBatch(p.db.WithContext(ctx), p.updateAuthUserTotpQuery, vals); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func _initAuthUserTotpRepository(db pdb.DB, model pdb.Model, listOpts ...pdb.ModelOption) *authUserTotpImpl {
	authUserTotp := authdao.UserTotp{}
	newModel := model.WithOption(listOpts...)
	return &authUserTotpImpl{
		db:                      db,
		model:                   newModel,
		countAuthUserTotpQuery:  fmt.Sprintf(selectAuthUserTotpQueryFmt, "count(1)", newModel.TableName()),
		selectAuthUserTotpQuery: fmt.Sprintf(selectAuthUserTotpQueryFmt, strings.Join(authUserTotp.Columns(), ","), newModel.TableName()),
		updateAuthUserTotpQuery: fmt.Sprintf(updateAuthUserTotpQueryFmt, newModel.TableName()),
	}
}
