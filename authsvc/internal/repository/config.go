package authrepo

import (
	"context"
	"fmt"
	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"github.com/pkg/errors"
	"strings"
	"time"
)

var _ authdao.ConfigRepository = (*authConfigImpl)(nil)

func NewAuthConfigRepository(db pdb.DB, tableName string) *authConfigImpl {
	return _initAuthConfigRepository(db, pdb.NewModel(tableName))
}

type authConfigImpl struct {
	db    pdb.DB
	model pdb.Model

	countAuthConfigQuery  string
	selectAuthConfigQuery string
	updateAuthConfigQuery string
}

const (
	authConfigSchema = `CREATE TABLE IF NOT EXISTS "%s" (
 "id_config" uuid NOT NULL,
 "id_auth_app" uuid NOT NULL,
 "config_type" varchar NOT NULL,
 "value" TEXT  NOT NULL,
 "created_at" bigint NOT NULL,
 "updated_at" bigint NOT NULL,
 "deleted_at" bigint NOT NULL
);`

	selectAuthConfigQueryFmt = "select %s from %s" +
		" where (not $1 or id_config = $2)" +
		" and (not $3 or id_auth_app = $4)" +
		" and (not $5 or lower(config_type) like lower('%%' || $6 || '%%'))" +
		" and (not $7 or created_at >= $8)" +
		" and (not $9 or created_at <= $10)" +
		" and (not $11 or (updated_at > deleted_at) = $12)" +
		" and (not $13 or config_type = any($14))"

	updateAuthConfigQueryFmt = "update %s set" +
		" id_auth_app = coalesce($1,id_auth_app)" +
		", config_type = coalesce($2,config_type)" +
		", value = coalesce($3,value)" +
		", deleted_at = coalesce($4,deleted_at)" +
		", updated_at = $5" +
		" where id_config = $6"
)

func toSliceAuthConfigSelect(params *authdao.ParamsConfigSelect) []any {
	return []any{
		params.IdConfig.Valid, params.IdConfig,
		params.IdAuthApp.Valid, params.IdAuthApp,
		params.ConfigType.Valid, params.ConfigType,
		params.CreatedAtGte > 0, params.CreatedAtGte,
		params.CreatedAtLte > 0, params.CreatedAtLte,
		params.IsActiveOrDeactivate.Valid, params.IsActiveOrDeactivate,
		len(params.ListType) > 0, params.ListType,
	}
}

func toSliceAuthConfigUpdate(params *authdao.ParamsConfigUpdate) []any {
	return []any{
		params.IdAuthApp,
		params.ConfigType,
		params.Value,
		params.DeletedAt,
		time.Now().Unix(),
		params.IdConfig,
	}
}

func (p *authConfigImpl) Count(ctx context.Context, params *authdao.ParamsConfigSelect) (total int64, err error) {
	dbCtx := p.db.WithContext(ctx)

	total, err = pdb.Count(dbCtx, p.countAuthConfigQuery, toSliceAuthConfigSelect(params))
	if err != nil {
		return 0, errors.WithStack(err)
	}
	if total == 0 {
		return 0, nil
	}
	return total, nil
}

func (p *authConfigImpl) Select(ctx context.Context, params *authdao.ParamsConfigSelect) (data []*authdao.Config, err error) {
	dbCtx := p.db.WithContext(ctx)
	var (
		selectQuery = []string{p.selectAuthConfigQuery}
	)

	if len(params.OrderByCol) > 0 {
		if params.IsOrderAsc {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s asc", params.OrderByCol))
		} else {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s desc", params.OrderByCol))
		}
	}
	if params.Limit > 0 {
		selectQuery = append(selectQuery, fmt.Sprintf("limit %d offset %d", params.Limit, params.Offset))
	}
	if params.IsNoKeyUpdate {
		selectQuery = append(selectQuery, "for no key update")
	}

	data, err = pdb.Select[authdao.Config](dbCtx, strings.Join(selectQuery, " "), toSliceAuthConfigSelect(params), func(scanner pdb.Scanner) (t *authdao.Config, err error) {
		t = &authdao.Config{}
		if err = t.FullScan(scanner); err != nil {
			return nil, errors.WithStack(err)
		}
		return t, nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return data, nil
}

func (p *authConfigImpl) Create(ctx context.Context, authConfig ...*authdao.Config) (err error) {
	if err = pdb.CreateGeneric[*authdao.Config](p.db.WithContext(ctx), p.model, authConfig); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (p *authConfigImpl) Update(ctx context.Context, listParams ...*authdao.ParamsConfigUpdate) (err error) {
	var (
		vals [][]any
	)
	for _, param := range listParams {
		if param.IdConfig.IsNil() {
			return errors.WithStack(errors.New("id must be not nil"))
		}
		if param.IsChange == false {
			return errors.WithStack(errors.New("no col update"))
		}
		vals = append(vals, toSliceAuthConfigUpdate(param))
	}

	if len(vals) == 0 {
		return errors.WithStack(errors.New("no row update"))
	}

	if err = pdb.UpdateWithBatch(p.db.WithContext(ctx), p.updateAuthConfigQuery, vals); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func _initAuthConfigRepository(db pdb.DB, model pdb.Model, listOpts ...pdb.ModelOption) *authConfigImpl {
	authConfig := authdao.Config{}
	newModel := model.WithOption(listOpts...)
	return &authConfigImpl{
		db:                    db,
		model:                 newModel,
		countAuthConfigQuery:  fmt.Sprintf(selectAuthConfigQueryFmt, "count(1)", newModel.TableName()),
		selectAuthConfigQuery: fmt.Sprintf(selectAuthConfigQueryFmt, strings.Join(authConfig.Columns(), ","), newModel.TableName()),
		updateAuthConfigQuery: fmt.Sprintf(updateAuthConfigQueryFmt, newModel.TableName()),
	}
}
