package authrepo

import (
	"context"
	"fmt"
	"strings"
	"time"

	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"github.com/pkg/errors"
)

var _ authdao.UserDetailRepository = (*authUserDetailImpl)(nil)

func NewAuthUserDetailRepository(db pdb.DB, tableName string) *authUserDetailImpl {
	return _initAuthUserDetailRepository(db, pdb.NewModel(tableName))
}

type authUserDetailImpl struct {
	db    pdb.DB
	model pdb.Model

	countAuthUserDetailQuery  string
	selectAuthUserDetailQuery string
	updateAuthUserDetailQuery string
}

const (
	authUserDetailSchema = `CREATE TABLE IF NOT EXISTS "%s" (
 	"id_auth_user_detail" uuid PRIMARY KEY,
 	"id_auth_user" uuid NOT NULL,
 	"first_name" varchar(64) NOT NULL,
 	"last_name" varchar(64) NOT NULL,
 	"phone_number" varchar(15) NOT NULL,
	"street" varchar(256),
   	"city" varchar(256),
    "country" varchar(256),
    "id_company" uuid,
    "ref_code" varchar(256),
    "user_ref_id" uuid,
 	"created_at" bigint NOT NULL,
 	"updated_at" bigint NOT NULL,
 	"deleted_at" bigint NOT NULL
);`

	selectAuthUserDetailQueryFmt = "SELECT %s FROM %s" +
		" WHERE (NOT $1 OR id_auth_user_detail = $2)" +
		" AND (NOT $3 OR id_auth_user_detail = ANY($4))" +
		" AND (NOT $5 OR id_auth_user = $6)" +
		" AND (NOT $7 OR id_auth_user = ANY($8))" +
		" AND (NOT $9 OR LOWER(first_name) LIKE LOWER('%%' || $10 || '%%'))" +
		" AND (NOT $11 OR LOWER(last_name) LIKE LOWER('%%' || $12 || '%%'))" +
		" AND (NOT $13 OR LOWER(phone_number) LIKE LOWER('%%' || $14 || '%%'))" +
		" AND (NOT $15 OR created_at >= $16)" +
		" AND (NOT $17 OR created_at <= $18)" +
		" AND (NOT $19 OR (deleted_at = 0) = $20)" +
		" AND (NOT $21 OR id_company = $22)" +
		" AND (NOT $23 OR ref_code = $24)" +
		" AND (NOT $25 OR user_ref_id = $26)"

	updateAuthUserDetailQueryFmt = "update %s set" +
		" first_name = coalesce($1,first_name)" +
		", last_name = coalesce($2,last_name)" +
		", phone_number = coalesce($3,phone_number)" +
		", ref_code = coalesce($4,ref_code)" +
		", user_ref_id = coalesce($5,user_ref_id)" +
		", street = coalesce($6,street)" +
		", id_state = coalesce($7,id_state)" +
		", id_city = coalesce($8,id_city)" +
		", id_country = coalesce($9,id_country)" +
		", id_company = coalesce($10,id_company)" +
		", deleted_at = coalesce($11,deleted_at)" +
		", updated_at = $12" +
		" where id_auth_user_detail = $13"
)

func toSliceAuthUserDetailSelect(params *authdao.ParamsUserDetailSelect) []any {
	return []any{
		params.IdUserDetail.Valid, params.IdUserDetail,
		len(params.ListIdUserDetail) > 0, params.ListIdUserDetail,
		params.IdUser.Valid, params.IdUser,
		len(params.ListIdUser) > 0, params.ListIdUser,
		params.FirstName.Valid, params.FirstName,
		params.LastName.Valid, params.LastName,
		params.PhoneNumber.Valid, params.PhoneNumber,
		params.CreatedAtGte > 0, params.CreatedAtGte,
		params.CreatedAtLte > 0, params.CreatedAtLte,
		params.IsActiveOrDeactivate.Valid, params.IsActiveOrDeactivate,
		params.IdCompany.Valid, params.IdCompany,
		params.RefCode.Valid, params.RefCode,
		params.UserRefId.Valid, params.UserRefId,
	}
}

func toSliceAuthUserDetailUpdate(params *authdao.ParamsUserDetailUpdate) []any {
	return []any{
		params.FirstName,
		params.LastName,
		params.PhoneNumber,
		params.RefCode,
		params.UserRefId,
		params.Street,
		params.IdState,
		params.IdCity,
		params.IdCountry,
		params.IdCompany,
		params.DeletedAt,
		time.Now().Unix(),
		params.IdUserDetail,
	}
}

func (p *authUserDetailImpl) Count(ctx context.Context, params *authdao.ParamsUserDetailSelect) (total int64, err error) {
	dbCtx := p.db.WithContext(ctx)

	total, err = pdb.Count(dbCtx, p.countAuthUserDetailQuery, toSliceAuthUserDetailSelect(params))
	if err != nil {
		return 0, errors.WithStack(err)
	}
	if total == 0 {
		return 0, nil
	}
	return total, nil
}

func (p *authUserDetailImpl) Select(ctx context.Context, params *authdao.ParamsUserDetailSelect) (listAuthUserDetail []*authdao.UserDetail, err error) {
	dbCtx := p.db.WithContext(ctx)
	var (
		selectQuery = []string{p.selectAuthUserDetailQuery}
	)

	if len(params.OrderByCol) > 0 {
		if params.IsOrderAsc {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s asc", params.OrderByCol))
		} else {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s desc", params.OrderByCol))
		}
	}
	if params.Limit > 0 {
		selectQuery = append(selectQuery, fmt.Sprintf("limit %d offset %d", params.Limit, params.Offset))
	}
	if params.IsNoKeyUpdate {
		selectQuery = append(selectQuery, "for no key update")
	}

	listAuthUserDetail, err = pdb.Select[authdao.UserDetail](dbCtx, strings.Join(selectQuery, " "), toSliceAuthUserDetailSelect(params), func(scanner pdb.Scanner) (t *authdao.UserDetail, err error) {
		t = &authdao.UserDetail{}
		if err = t.FullScan(scanner); err != nil {
			return nil, errors.WithStack(err)
		}
		return t, nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return listAuthUserDetail, nil
}

func (p *authUserDetailImpl) Create(ctx context.Context, authUserDetails ...*authdao.UserDetail) (err error) {
	if err = pdb.CreateGeneric[*authdao.UserDetail](p.db.WithContext(ctx), p.model, authUserDetails); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (p *authUserDetailImpl) Update(ctx context.Context, listParams ...*authdao.ParamsUserDetailUpdate) (err error) {
	var (
		vals [][]any
	)
	for _, param := range listParams {
		if param.IdUserDetail.IsNil() {
			return errors.WithStack(errors.New("id_auth_user_detail must be not nil"))
		}
		if param.IsChange == false {
			return errors.WithStack(errors.New("no col update"))
		}
		vals = append(vals, toSliceAuthUserDetailUpdate(param))
	}

	if len(vals) == 0 {
		return nil
	}

	if err = pdb.UpdateWithBatch(p.db.WithContext(ctx), p.updateAuthUserDetailQuery, vals); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func _initAuthUserDetailRepository(db pdb.DB, model pdb.Model, listOpts ...pdb.ModelOption) *authUserDetailImpl {
	authUserDetail := authdao.UserDetail{}
	newModel := model.WithOption(listOpts...)
	return &authUserDetailImpl{
		db:    db,
		model: newModel,

		countAuthUserDetailQuery:  fmt.Sprintf(selectAuthUserDetailQueryFmt, "count(1)", newModel.TableName()),
		selectAuthUserDetailQuery: fmt.Sprintf(selectAuthUserDetailQueryFmt, strings.Join(authUserDetail.Columns(), ","), newModel.TableName()),
		updateAuthUserDetailQuery: fmt.Sprintf(updateAuthUserDetailQueryFmt, newModel.TableName()),
	}
}
