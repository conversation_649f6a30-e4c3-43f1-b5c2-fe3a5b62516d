package authrepo

import (
	"context"
	"fmt"
	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	"strings"
	"time"

	"github.com/pkg/errors"

	"git.tmproxy-infra.com/algo/common/pkg/pdb"
)

const (
	authServiceSchema = `CREATE TABLE IF NOT EXISTS "%s" (
  "id_auth_service" uuid PRIMARY KEY,
  "name" varchar(255) UNIQUE NOT NULL,
  "created_at" bigint NOT NULL,
  "updated_at" bigint NOT NULL,
  "deleted_at" bigint NOT NULL
	);
`
	selectAuthServiceQueryFmt = "select %s from %s" +
		" where (not $1 or id_auth_service = $2)" +
		" and (not $3 or id_auth_service = any($4))" +
		" and (not $5 or lower(name) = lower($6))" +
		" and (not $7 or (updated_at > deleted_at) = $8)" +
		" and (not $9 or lower(name) = lower($10))" +
		" and (not $11 or lower(name) like '%%' || lower($12) || '%%')"

	updateAuthServiceQueryFmt = "update %s set" +
		" name = coalesce($1,name)" +
		", deleted_at = coalesce($2,deleted_at)" +
		", updated_at = $3" +
		" where id_auth_service = $4"
)

func toSliceAuthServiceSelect(params *authdao.ParamsServiceSelect) []any {

	return []any{
		params.IdService.Valid, params.IdService,
		len(params.ListIdService) > 0, params.ListIdService,
		params.Name.Valid, params.Name,
		params.IsActiveOrDeactivate.Valid, params.IsActiveOrDeactivate,
		params.NameExist.Valid, params.NameExist,
		params.NameSearch.Valid, params.NameSearch,
	}
}
func toSliceAuthServiceUpdate(param *authdao.ParamsServiceUpdate) []any {
	return []any{
		param.Name,
		param.DeletedAt,
		time.Now().Unix(),
		param.IdService,
	}
}

type authServiceImpl struct {
	db    pdb.DB
	model pdb.Model

	countAuthServiceQuery  string
	selectAuthServiceQuery string
	updateAuthServiceQuery string
}

func (repo *authServiceImpl) _initSchema(ctx context.Context) (err error) {
	_, err = repo.db.WithContext(ctx).Exec(fmt.Sprintf(authServiceSchema, repo.model.TableName()))
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (repo *authServiceImpl) WithOption(listOpts ...pdb.ModelOption) authdao.ServiceRepository {
	return _initAuthServiceRepository(repo.db, repo.model, listOpts...)
}

func _initAuthServiceRepository(db pdb.DB, model pdb.Model, listOpts ...pdb.ModelOption) *authServiceImpl {
	authServiceSchema := authdao.Service{}
	return &authServiceImpl{
		db:                     db,
		model:                  model.WithOption(listOpts...),
		countAuthServiceQuery:  fmt.Sprintf(selectAuthServiceQueryFmt, "count(1)", model.TableName()),
		selectAuthServiceQuery: fmt.Sprintf(selectAuthServiceQueryFmt, strings.Join(authServiceSchema.Columns(), ","), model.TableName()),
		updateAuthServiceQuery: fmt.Sprintf(updateAuthServiceQueryFmt, model.TableName()),
	}
}

func (repo *authServiceImpl) Count(ctx context.Context, params *authdao.ParamsServiceSelect) (total int64, err error) {
	dbCtx := repo.db.WithContext(ctx)

	total, err = pdb.Count(dbCtx, repo.countAuthServiceQuery, toSliceAuthServiceSelect(params))
	if err != nil {
		return 0, errors.WithStack(err)
	}
	if total == 0 {
		return 0, nil
	}
	return total, nil
}

func (repo *authServiceImpl) Select(ctx context.Context, params *authdao.ParamsServiceSelect) (listAuthService []*authdao.Service, err error) {
	dbCtx := repo.db.WithContext(ctx)
	var (
		selectQuery = []string{repo.selectAuthServiceQuery}
	)

	if len(params.OrderByCol) > 0 {
		if params.IsOrderAsc {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s asc", params.OrderByCol))
		} else {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s desc", params.OrderByCol))
		}
	}
	if params.Limit > 0 {
		selectQuery = append(selectQuery, fmt.Sprintf("limit %d offset %d", params.Limit, params.Offset))
	}
	if params.IsNoKeyUpdate {
		selectQuery = append(selectQuery, "for no key update")
	}

	listAuthService, err = pdb.Select[authdao.Service](dbCtx, strings.Join(selectQuery, " "), toSliceAuthServiceSelect(params), func(scanner pdb.Scanner) (t *authdao.Service, err error) {
		t = &authdao.Service{}
		if err = t.FullScan(scanner); err != nil {
			return nil, errors.WithStack(err)
		}
		return t, nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return listAuthService, nil
}

func (repo *authServiceImpl) Create(ctx context.Context, listAuthService ...*authdao.Service) (err error) {
	if err = pdb.CreateGeneric[*authdao.Service](repo.db.WithContext(ctx), repo.model, listAuthService); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (repo *authServiceImpl) Update(ctx context.Context, listParams ...*authdao.ParamsServiceUpdate) (err error) {
	var (
		vals [][]any
	)
	for _, param := range listParams {
		if param.IdService.IsNil() {
			return errors.WithStack(errors.New("id_auth_service must be not nil"))
		}
		if !param.IsChange {
			return errors.WithStack(errors.New("no col update"))
		}

		vals = append(vals, toSliceAuthServiceUpdate(param))
	}

	if err = pdb.UpdateWithBatch(repo.db.WithContext(ctx), repo.updateAuthServiceQuery, vals); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func NewAuthServiceRepository(db pdb.DB, tableName string) *authServiceImpl {
	return _initAuthServiceRepository(db, pdb.NewModel(tableName))
}
