package authrepo

import (
	"context"
	"fmt"
	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"github.com/pkg/errors"
	"strings"
	"time"
)

var _ authdao.UserRepository = (*userImpl)(nil)

func NewUserRepository(db pdb.DB, tableName string) *userImpl {
	return _initUserRepository(db, pdb.NewModel(tableName))
}

type userImpl struct {
	db    pdb.DB
	model pdb.Model

	countUserQuery  string
	selectUserQuery string
	updateUserQuery string
}

const (
	userSchema = `CREATE TABLE IF NOT EXISTS "%s" (
 "id_auth_user" uuid PRIMARY KEY,
 "id_auth_app" uuid NOT NULL,
 "id_auth_role" uuid NOT NULL,
 "email" varchar(255) UNIQUE NOT NULL,
 "hash_password" text NOT NULL,
 "salt" text NOT NULL,
 "created_at" bigint NOT NULL,
 "updated_at" bigint NOT NULL,
 "deleted_at" bigint NOT NULL
);`

	selectUserQueryFmt = "select %s from %s" +
		" where (not $1 or id_auth_user = $2)" +
		" and (not $3 or id_auth_user = any($4))" +
		" and (not $5 or id_auth_app = $6)" +
		" and (not $7 or id_auth_app = any($8))" +
		" and (not $9 or id_auth_role = $10)" +
		" and (not $11 or id_auth_role = any($12))" +
		" and (not $13 or lower(email) = lower($14))" +
		" and (not $15 or lower(email) like '%%' || lower($16) || '%%')" +
		" and (not $17 or hash_password = $18)" +
		" and (not $19 or salt = $20)" +
		" and (not $21 or created_at >= $22)" +
		" and (not $23 or created_at <= $24)" +
		" and (not $25 or (updated_at > deleted_at) = $26)"

	updateUserQueryFmt = "update %s set" +
		" id_auth_role = coalesce($1,id_auth_role)" +
		", hash_password = coalesce($2,hash_password)" +
		", salt = coalesce($3,salt)" +
		", deleted_at = coalesce($4,deleted_at)" +
		", updated_at = $5" +
		" where id_auth_user = $6"
)

func toSliceUserSelect(params *authdao.ParamsUserSelect) []any {
	return []any{
		params.IdUser.Valid, params.IdUser,
		len(params.ListIdUser) > 0, params.ListIdUser,
		params.IdApp.Valid, params.IdApp,
		len(params.ListIdApp) > 0, params.ListIdApp,
		params.IdRole.Valid, params.IdRole,
		len(params.ListIdRole) > 0, params.ListIdRole,
		params.Email.Valid, params.Email,
		params.EmailSearch.Valid, params.EmailSearch,
		params.HashPassword.Valid, params.HashPassword,
		params.Salt.Valid, params.Salt,
		params.CreatedAtGte > 0, params.CreatedAtGte,
		params.CreatedAtLte > 0, params.CreatedAtLte,
		params.IsActiveOrDeactivate.Valid, params.IsActiveOrDeactivate,
	}
}

func toSliceUserUpdate(params *authdao.ParamsUserUpdate) []any {
	return []any{
		params.IdRole,
		params.HashPassword,
		params.Salt,
		params.DeletedAt,
		time.Now().Unix(),
		params.IdUser,
	}
}

func (u *userImpl) Count(ctx context.Context, params *authdao.ParamsUserSelect) (total int64, err error) {
	dbCtx := u.db.WithContext(ctx)

	total, err = pdb.Count(dbCtx, u.countUserQuery, toSliceUserSelect(params))
	if err != nil {
		return 0, errors.WithStack(err)
	}
	if total == 0 {
		return 0, nil
	}
	return total, nil
}

func (u *userImpl) Select(ctx context.Context, params *authdao.ParamsUserSelect) (listUser []*authdao.User, err error) {
	dbCtx := u.db.WithContext(ctx)
	var (
		selectQuery = []string{u.selectUserQuery}
	)

	if len(params.OrderByCol) > 0 {
		if params.IsOrderAsc {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s asc", params.OrderByCol))
		} else {
			selectQuery = append(selectQuery, fmt.Sprintf("order by %s desc", params.OrderByCol))
		}
	}
	if params.Limit > 0 {
		selectQuery = append(selectQuery, fmt.Sprintf("limit %d offset %d", params.Limit, params.Offset))
	}
	if params.IsNoKeyUpdate {
		selectQuery = append(selectQuery, "for no key update")
	}

	listUser, err = pdb.Select[authdao.User](dbCtx, strings.Join(selectQuery, " "), toSliceUserSelect(params), func(scanner pdb.Scanner) (t *authdao.User, err error) {
		t = &authdao.User{}
		if err = t.FullScan(scanner); err != nil {
			return nil, errors.WithStack(err)
		}
		return t, nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return listUser, nil
}

func (u *userImpl) Create(ctx context.Context, user ...*authdao.User) (err error) {
	if err = pdb.CreateGeneric[*authdao.User](u.db.WithContext(ctx), u.model, user); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (u *userImpl) Update(ctx context.Context, listParams ...*authdao.ParamsUserUpdate) (err error) {
	var (
		vals [][]any
	)
	for _, param := range listParams {
		if param.IdUser.IsNil() {
			return errors.WithStack(errors.New("id_auth_user must be not nil"))
		}
		if param.IsChange == false {
			return errors.WithStack(errors.New("no col update"))
		}
		vals = append(vals, toSliceUserUpdate(param))
	}

	if len(vals) == 0 {
		return errors.WithStack(errors.New("no row update"))
	}

	if err = pdb.UpdateWithBatch(u.db.WithContext(ctx), u.updateUserQuery, vals); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func _initUserRepository(db pdb.DB, model pdb.Model, listOpts ...pdb.ModelOption) *userImpl {
	user := authdao.User{}
	newModel := model.WithOption(listOpts...)
	return &userImpl{
		db:              db,
		model:           newModel,
		countUserQuery:  fmt.Sprintf(selectUserQueryFmt, "count(1)", newModel.TableName()),
		selectUserQuery: fmt.Sprintf(selectUserQueryFmt, strings.Join(user.Columns(), ","), newModel.TableName()),
		updateUserQuery: fmt.Sprintf(updateUserQueryFmt, newModel.TableName()),
	}
}
