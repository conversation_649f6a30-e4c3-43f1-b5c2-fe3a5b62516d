package sendemailotphandler

import (
	"bytes"
	"connectrpc.com/connect"
	"context"
	"fmt"
	authv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1"
	errmsgv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	"git.tmproxy-infra.com/algo/authsvc/internal/authenum"
	authinfra "git.tmproxy-infra.com/algo/authsvc/internal/infra"
	"git.tmproxy-infra.com/algo/common/asynqtask/sendemailotp"
	"github.com/goccy/go-json"
	"github.com/hibiken/asynq"
	"text/template"
)

type handler struct {
	infra authinfra.Infra
}

func NewSendEmailOtpHandler(infra authinfra.Infra) *handler {
	return &handler{infra}
}

func (h *handler) HandleSendEmailOTP(ctx context.Context, t *asynq.Task) (err error) {
	var payload = &sendemailotp.Payload{}
	if err = json.Unmarshal(t.Payload(), payload); err != nil {
		return fmt.Errorf("json.Unmarshal failed: %v: %w", err, asynq.SkipRetry)
	}
	h.infra.Logger().Info("trigger send email otp")
	temp, err := h.infra.CommonFunction().FetchConfigTemplateMail(ctx, payload.IdAuthApp, authenum.MailOTP)
	if err != nil {
		return fmt.Errorf("Error get template OTP  %v: %w", err, asynq.SkipRetry)
	}
	content := temp.Content
	tmpl, err := template.New("mail_otp").Parse(content)
	if err != nil {
		return fmt.Errorf("Error process template OTP  %v: %w", err, asynq.SkipRetry)
	}
	var buf bytes.Buffer

	err = tmpl.Execute(&buf, payload)
	if err != nil {
		return fmt.Errorf("Error executing template %v: %w", err, asynq.SkipRetry)
	}

	reqSendMail := connect.NewRequest[authv1.InternalAuthServiceSendMailHtmlRequest](&authv1.InternalAuthServiceSendMailHtmlRequest{
		IdApp:   payload.IdAuthApp.Hex(),
		To:      []string{payload.To},
		Subject: payload.Subject,
		Body:    buf.String(),
	})
	resSend, err := h.infra.GetAuthInternalHandler().SendMailHtml(ctx, reqSendMail)
	if err != nil || resSend.Msg.Error.Code != errmsgv1.ErrorCode_ERROR_CODE_SUCCESS {
		return fmt.Errorf("Error send mail otp %v: %w", err, asynq.SkipRetry)
	}
	return nil
}
