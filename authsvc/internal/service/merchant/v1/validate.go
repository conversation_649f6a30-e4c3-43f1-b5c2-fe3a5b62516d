package merchantv1svc

import (
	"html"
	"strings"

	authv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1"
	"git.tmproxy-infra.com/algo/authsvc/internal/autherr"
)

func _loginValidate(request *authv1.MerchantAuthServiceLoginRequest) error {
	switch {
	case len(request.GetEmail()) == 0:
		return autherr.EmailRequiredError
	case len(request.GetPassword()) == 0:
		return autherr.PasswordRequiredError
	case len(request.GetDomain()) == 0:
		return autherr.DomainRequiredError
	}
	return nil
}

func _changePasswordValidate(request *authv1.MerchantAuthServiceChangePasswordRequest) error {
	request.NewPassword = strings.TrimSpace(html.EscapeString(request.GetNewPassword()))
	request.OldPassword = strings.TrimSpace(html.EscapeString(request.GetOldPassword()))
	switch {
	case len(request.GetOldPassword()) == 0:
		return autherr.OldPasswordRequiredError
	case len(request.GetNewPassword()) == 0:
		return autherr.NewPasswordRequiredError
	case len(request.GetNewPassword()) < 8:
		return autherr.PasswordLengthMustBeGte8RequiredError
	}
	return nil
}
