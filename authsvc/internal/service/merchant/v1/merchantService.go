package merchantv1svc

import (
	"context"
	"html"

	"connectrpc.com/connect"
	authv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1"
	"git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1/authv1connect"
	errmsgv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	utilsv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	"git.tmproxy-infra.com/algo/authsvc/internal/autherr"
	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	authinfra "git.tmproxy-infra.com/algo/authsvc/internal/infra"
	"git.tmproxy-infra.com/algo/common/enumtype"
	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"git.tmproxy-infra.com/algo/common/pkg/rpcutils"
	"git.tmproxy-infra.com/algo/common/pkg/usercontext"
	"github.com/pkg/errors"
	"golang.org/x/sync/errgroup"
)

type merchantServiceImpl struct {
	infra authinfra.Infra
}

func (a *merchantServiceImpl) ForgotPassword(ctx context.Context, c *connect.Request[authv1.MerchantAuthServiceForgotPasswordRequest]) (res *connect.Response[authv1.MerchantAuthServiceForgotPasswordResponse], err error) {
	//TODO implement me
	res = connect.NewResponse[authv1.MerchantAuthServiceForgotPasswordResponse](
		&authv1.MerchantAuthServiceForgotPasswordResponse{
			Error: &errmsgv1.ErrorMessage{},
		})
	var (
		ipUser = c.Header().Get("X-Real-Ip")
	)

	requestInternal := connect.NewRequest[authv1.InternalAuthServiceForgotPasswordRequest](&authv1.InternalAuthServiceForgotPasswordRequest{
		Email:       c.Msg.Email,
		NewPassword: c.Msg.NewPassword,
		IpAddress:   ipUser,
		Otp:         c.Msg.GetOtp(),
	})

	listApp, err := a.infra.AppRepository().Select(ctx, pdb.OptionParams[authdao.ParamsAppSelect](
		authdao.AppSelectWithAppType(enumtype.MerchantAppType),
		authdao.AppSelectWithDomain(c.Msg.GetDomain()),
		authdao.AppSelectWithActiveOrDeactivate(true, false),
	))
	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}
	if len(listApp) == 0 {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_APP_EXIST
		return res, errors.Errorf("app %s, not found", c.Msg.GetDomain())
	}
	currentApp := listApp[0]
	requestInternal.Msg.Domain = currentApp.Domain

	responseInternal, err := a.infra.GetAuthInternalHandler().ForgotPassword(ctx, requestInternal)
	if err != nil {
		if responseInternal != nil {
			if responseInternal.Msg.Error.Code != errmsgv1.ErrorCode_ERROR_CODE_SUCCESS {
				res.Msg.Error.Code = responseInternal.Msg.Error.Code
				return res, errors.WithStack(err)
			}
		}
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}
	res.Msg.Error.Code = responseInternal.Msg.GetError().Code
	return res, nil
}

func (m *merchantServiceImpl) ChangePassword(ctx context.Context, request *connect.Request[authv1.MerchantAuthServiceChangePasswordRequest]) (response *connect.Response[authv1.MerchantAuthServiceChangePasswordResponse], err error) {
	response = connect.NewResponse[authv1.MerchantAuthServiceChangePasswordResponse](&authv1.MerchantAuthServiceChangePasswordResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	userCtx := usercontext.FromContext(ctx)
	if userCtx == nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.New("user context is nil, interceptor has problem")
	}
	idUser, err := algouid.FromHex(userCtx.IdAuthUser)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return response, errors.WithStack(err)
	}

	if err = m.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		listUserDao, err := m.infra.User().UserRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsUserSelect](
			authdao.UserSelectWithIdUser(idUser),
		))
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listUserDao) == 0 {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_USER_NOT_EXIST
			return autherr.ErrUserNotExist
		}
		currentUser := listUserDao[0]
		ok, err := m.infra.CommonFunction().VerifyPasswordAndSalt(request.Msg.GetOldPassword(), currentUser.HashPassword, currentUser.Salt)
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if !ok {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_OLD_PASSWORD_INCORRECT
			return errors.New("old password incorrect")
		}
		hashPassword, salt := m.infra.CommonFunction().GeneratePasswordAndSalt(request.Msg.GetNewPassword())
		paramUserUpdate := pdb.OptionParams[authdao.ParamsUserUpdate](
			authdao.UserUpdateWithIdUser(currentUser.IdUser),
			authdao.UserUpdateHashPassword(currentUser.HashPassword, hashPassword),
			authdao.UserUpdateSalt(currentUser.Salt, salt),
		)
		if err = m.infra.User().UserRepository().Update(tranCtx, paramUserUpdate); err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return response, errors.WithStack(err)
	}
	response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return response, nil
}

func (m *merchantServiceImpl) FetchRole(ctx context.Context, request *connect.Request[authv1.MerchantAuthServiceFetchRoleRequest]) (response *connect.Response[authv1.MerchantAuthServiceFetchRoleResponse], err error) {
	response = connect.NewResponse[authv1.MerchantAuthServiceFetchRoleResponse](&authv1.MerchantAuthServiceFetchRoleResponse{
		Error:      &errmsgv1.ErrorMessage{},
		Pagination: &utilsv1.PaginationResponse{},
	})
	userCtx := usercontext.FromContext(ctx)
	if userCtx == nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.New("user_context is nil")
	}
	currentRole, err := m.infra.PolicyManager().GetRole(ctx, userCtx.IdAuthRole)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}

	var (
		requestFetchRole                    = request.Msg
		pageSize, pageNumber, offset, limit = rpcutils.ToOffsetLimit(requestFetchRole.GetPagination().GetPageSize(), requestFetchRole.GetPagination().GetPageNumber())
		total                               int64
	)
	defer func() {
		response.Msg.Pagination.PageSize = pageSize
		response.Msg.Pagination.CurrentPage = pageNumber
		response.Msg.Pagination.Total = total
		response.Msg.Pagination.TotalPages = rpcutils.ToTotalPage(pageSize, total)
	}()

	nameSearchEscape := html.EscapeString(requestFetchRole.GetNameSearch())

	params := pdb.OptionParams[authdao.ParamsRoleSelect](
		authdao.RoleSelectWithIdRole(algouid.FromHexOrNil(requestFetchRole.GetIdRole())),
		authdao.RoleSelectWithRoleNameSearch(nameSearchEscape),
		authdao.RoleSelectWithPriorityGt(currentRole.Priority),
		authdao.RoleSelectWithActiveOrDeactivate(true, false),
		authdao.RoleSelectOrderByPriority(),
		authdao.RoleSelectWithPagination(offset, limit),
	)
	total, err = m.infra.RoleRepository().Count(ctx, params)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	if total == 0 {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
		return response, nil
	}
	listRole, err := m.infra.RoleRepository().Select(ctx, params)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	var (
		listRoleModels = make([]*authv1.MerchantRoleModel, 0, len(listRole))
	)
	for _, v := range listRole {
		listRoleModels = append(listRoleModels, &authv1.MerchantRoleModel{
			IdRole: v.IdRole.Hex(),
			Name:   v.RoleName,
		})
	}
	response.Msg.Items = listRoleModels
	response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return response, nil
}

func (m *merchantServiceImpl) FetchUser(ctx context.Context, request *connect.Request[authv1.MerchantAuthServiceFetchUserRequest]) (response *connect.Response[authv1.MerchantAuthServiceFetchUserResponse], err error) {
	response = connect.NewResponse[authv1.MerchantAuthServiceFetchUserResponse](&authv1.MerchantAuthServiceFetchUserResponse{
		Error:      &errmsgv1.ErrorMessage{},
		Pagination: &utilsv1.PaginationResponse{},
	})

	userCtx := usercontext.FromContext(ctx)
	if userCtx == nil {
		// if userCtx == nil, interceptor has problem
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.New("user context is nil")
	}
	idAuthMerchant, err := algouid.FromHex(userCtx.IdAuthApp)
	if err != nil {
		// if userCtx == nil, interceptor has problem
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	currentRoleRedis, err := m.infra.PolicyManager().GetRole(ctx, userCtx.IdAuthRole)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	listRoleCanFetch, err := m.infra.RoleRepository().Select(ctx, pdb.OptionParams[authdao.ParamsRoleSelect](
		authdao.RoleSelectWithPriorityGt(currentRoleRedis.Priority),
	))
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}

	var (
		requestFetchApp                     = request.Msg
		pageSize, pageNumber, offset, limit = rpcutils.ToOffsetLimit(requestFetchApp.GetPagination().GetPageSize(), requestFetchApp.GetPagination().GetPageNumber())
		total                               int64
	)
	listIdRoleCanFetch := make([]algouid.UUID, 0, len(listRoleCanFetch))

	for _, v := range listRoleCanFetch {
		listIdRoleCanFetch = append(listIdRoleCanFetch, v.IdRole)
	}
	defer func() {
		response.Msg.Pagination.PageSize = pageSize
		response.Msg.Pagination.CurrentPage = pageNumber
		response.Msg.Pagination.Total = total
		response.Msg.Pagination.TotalPages = rpcutils.ToTotalPage(pageSize, total)
	}()

	filter := pdb.OptionParams[authdao.ParamsUserSelect](
		authdao.UserSelectWithIdApp(idAuthMerchant),
		authdao.UserSelectWithEmailSearch(request.Msg.EmailSearch),
		authdao.UserSelectWithIdUser(algouid.FromHexOrNil(request.Msg.GetIdUser())),
		authdao.UserSelectWithListIdRole(listIdRoleCanFetch),
		authdao.UserSelectWithPagination(offset, limit),
		authdao.UserSelectWithActiveOrDeactivate(request.Msg.GetState().GetIsActive(), request.Msg.GetState().GetIsDeactivate()),
	)

	total, err = m.infra.User().UserRepository().Count(ctx, filter)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	if total == 0 {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
		return response, nil
	}

	listUserDao, err := m.infra.User().UserRepository().Select(ctx, filter)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	var (
		mapRole = make(map[algouid.UUID]struct{})
	)
	for _, v := range listUserDao {
		_, ok := mapRole[v.IdRole]
		if !ok {
			mapRole[v.IdRole] = struct{}{}
		}
	}
	roleBundle, err := m.infra.BundleLoader().LoadBundleRole(ctx, m.infra.RoleRepository(), mapRole)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	listUserResp := make([]*authv1.MerchantServiceUserModel, 0, len(listUserDao))
	for _, v := range listUserDao {
		roleOfUser, ok := roleBundle[v.IdRole]
		if !ok {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return response, errors.New("data mismatch, role not found")
		}
		um := &authv1.MerchantServiceUserModel{
			IdUser: v.IdUser.Hex(),
			Role: &authv1.MerchantUserRoleModel{
				IdRole:   roleOfUser.IdRole.Hex(),
				Name:     roleOfUser.RoleName,
				IsActive: roleOfUser.IsActive(),
			},
			Email:        v.Email,
			IsEnableTotp: false,
			IsActive:     v.IsActive(),
		}
		listUserResp = append(listUserResp, um)
	}
	response.Msg.Users = listUserResp
	response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return response, nil
}

func (m *merchantServiceImpl) UpdateUser(ctx context.Context, request *connect.Request[authv1.MerchantAuthServiceUpdateUserRequest]) (response *connect.Response[authv1.MerchantAuthServiceUpdateUserResponse], err error) {
	response = connect.NewResponse[authv1.MerchantAuthServiceUpdateUserResponse](&authv1.MerchantAuthServiceUpdateUserResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	userCtx := usercontext.FromContext(ctx)
	if userCtx == nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.New("user context is nil, interceptor has problem")
	}
	currentAdminRole, err := m.infra.PolicyManager().GetRole(ctx, userCtx.IdAuthRole)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}

	idMerchant, err := algouid.FromHex(userCtx.IdAuthApp)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	idUser, err := algouid.FromHex(request.Msg.GetIdUser())
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}

	if err = m.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		listUserDao, err := m.infra.User().UserRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsUserSelect](
			authdao.UserSelectWithIdApp(idMerchant),
			authdao.UserSelectWithIdUser(idUser)))
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listUserDao) == 0 {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_USER_NOT_EXIST
			return autherr.ErrUserNotExist
		}
		currentTargetUser := listUserDao[0]
		roleOfTargetUser, err := m.infra.PolicyManager().GetRole(tranCtx, currentTargetUser.IdRole.Hex())
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if currentAdminRole.Priority < roleOfTargetUser.Priority {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_PERMISSION_DENY
			return errors.New("permission deny")
		}

		paramUpdate := pdb.OptionParams[authdao.ParamsUserUpdate](
			authdao.UserUpdateWithIdUser(currentTargetUser.IdUser),
			authdao.UserUpdateActiveState(request.Msg.GetState().GetIsActive(), request.Msg.GetState().GetIsDeactivate()),
		)
		if !paramUpdate.IsChange {
			return nil
		}
		if err = m.infra.User().UserRepository().Update(tranCtx, paramUpdate); err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return response, errors.WithStack(err)
	}
	response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return response, nil
}

func (m *merchantServiceImpl) Login(ctx context.Context, request *connect.Request[authv1.MerchantAuthServiceLoginRequest]) (response *connect.Response[authv1.MerchantAuthServiceLoginResponse], err error) {
	response = connect.NewResponse[authv1.MerchantAuthServiceLoginResponse](&authv1.MerchantAuthServiceLoginResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	if err = _loginValidate(request.Msg); err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		response.Msg.Error.Message = err.Error()
		return response, errors.WithStack(err)
	}
	var (
		userAgent = request.Header().Get("User-Agent")
		ipAddress = request.Header().Get("X-Real-Ip")
		//xRealHost = request.Header().Get("X-Real-Host")
	)

	loginInternalResponse, err := m.infra.GetAuthInternalHandler().Login(ctx, connect.NewRequest[authv1.InternalAuthServiceLoginRequest](&authv1.InternalAuthServiceLoginRequest{
		Domain:    request.Msg.GetDomain(),
		Email:     request.Msg.GetEmail(),
		Password:  request.Msg.GetPassword(),
		IpAddress: ipAddress,
		UserAgent: userAgent,
		Otp:       request.Msg.GetOtp(),
		DeviceId:  request.Msg.GetDeviceId(),
	}))
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	if loginInternalResponse.Msg.Error.Code != errmsgv1.ErrorCode_ERROR_CODE_SUCCESS {
		response.Msg.Error.Code = loginInternalResponse.Msg.Error.Code
		return response, errors.New(loginInternalResponse.Msg.Error.Code.String())
	}
	response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	response.Msg.Token = loginInternalResponse.Msg.GetToken()
	response.Msg.RefreshToken = loginInternalResponse.Msg.GetRefreshToken()
	return response, nil

}
func (a *merchantServiceImpl) RefreshToken(ctx context.Context, c *connect.Request[authv1.MerchantAuthServiceRefreshTokenRequest]) (res *connect.Response[authv1.MerchantAuthServiceRefreshTokenResponse], err error) {
	//TODO implement me
	res = connect.NewResponse[authv1.MerchantAuthServiceRefreshTokenResponse](
		&authv1.MerchantAuthServiceRefreshTokenResponse{
			Error: &errmsgv1.ErrorMessage{},
		})
	var (
		deviceId = c.Header().Get("User-Device-ID")
	)
	requestInternal := connect.NewRequest[authv1.InternalAuthServiceRefreshTokenRequest](&authv1.InternalAuthServiceRefreshTokenRequest{
		DeviceId:     deviceId,
		RefreshToken: c.Msg.GetRefreshToken(),
	})
	refreshToken, err := a.infra.GetAuthInternalHandler().RefreshToken(ctx, requestInternal)
	if err != nil {
		res.Msg.Error.Code = refreshToken.Msg.Error.Code
		return res, errors.WithStack(err)
	}
	res.Msg.RefreshToken = refreshToken.Msg.RefreshToken
	res.Msg.Token = refreshToken.Msg.Token
	return res, nil
}
func (m *merchantServiceImpl) Me(ctx context.Context, request *connect.Request[authv1.MerchantAuthServiceMeRequest]) (response *connect.Response[authv1.MerchantAuthServiceMeResponse], err error) {
	response = connect.NewResponse[authv1.MerchantAuthServiceMeResponse](&authv1.MerchantAuthServiceMeResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	userCtx := usercontext.FromContext(ctx)
	if userCtx == nil {
		// interceptor work incorrect
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.New("user context is nil")
	}

	idUser, err := algouid.FromHex(userCtx.IdAuthUser)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	idMerchant, err := algouid.FromHex(userCtx.IdAuthApp)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}

	var (
		currentUserDetail *authdao.UserDetail
		currentUser       *authdao.User
		company           *authdao.Company
	)
	errGr, _ := errgroup.WithContext(ctx)
	errGr.Go(func() error {
		listUser, err := m.infra.User().UserRepository().Select(ctx, pdb.OptionParams[authdao.ParamsUserSelect](
			authdao.UserSelectWithIdUser(idUser),
			authdao.UserSelectWithIdApp(idMerchant),
			authdao.UserSelectWithActiveOrDeactivate(true, false),
			authdao.UserSelectWithPagination(0, 1),
		))
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listUser) == 0 {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return autherr.ErrUserNotExist
		}
		currentUser = listUser[0]
		return nil
	})
	errGr.Go(func() error {
		listUserDetailDao, err := m.infra.User().UserDetailRepository().Select(ctx, pdb.OptionParams[authdao.ParamsUserDetailSelect](
			authdao.UserDetailSelectWithIdUser(idUser),
			authdao.UserDetailSelectWithActiveOrDeactivate(true, false),
		))
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listUserDetailDao) == 0 {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return autherr.ErrUserNotExist
		}
		currentUserDetail = listUserDetailDao[0]
		if currentUserDetail.IdCompany.IsNil() == false {
			listCompany, err := m.infra.CompanyRepository().Select(ctx, pdb.OptionParams[authdao.ParamsCompanySelect](
				authdao.CompanySelectWithIdCompany(currentUserDetail.IdCompany),
				authdao.CompanySelectWithActiveOrDeactivate(true, false),
			))
			if err != nil {
				response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
				return errors.WithStack(err)
			}
			if len(listCompany) > 0 {
				company = listCompany[0]
			}
		}
		return nil
	})

	if err = errGr.Wait(); err != nil {
		return response, errors.WithStack(err)
	}
	if currentUserDetail == nil || currentUser == nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.New("user detail or user is nil")
	}
	listMerchant, err := m.infra.AppRepository().Select(ctx, pdb.OptionParams[authdao.ParamsAppSelect](
		authdao.AppSelectWithIdApp(idMerchant),
		authdao.AppSelectWithActiveOrDeactivate(true, false),
		authdao.AppSelectWithPagination(0, 1),
	))
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	if len(listMerchant) == 0 {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.New("merchant not found")
	}
	currentMerchant := listMerchant[0]
	listPathOfRole, err := m.infra.PolicyManager().GetAllPathOfRole(ctx, userCtx.IdAuthRole)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	response.Msg.UserDetail = &authv1.MerchantMeUserDetails{
		FirstName:   currentUserDetail.FirstName,
		LastName:    currentUserDetail.LastName,
		Email:       currentUser.Email,
		PhoneNumber: currentUserDetail.PhoneNumber,
		Street:      currentUserDetail.Street,
		IdCompany:   currentUserDetail.IdCompany.Hex(),
		IdCity:      currentUserDetail.IdCity.V,
		IdState:     currentUserDetail.IdState.V,
		IdCountry:   currentUserDetail.IdCountry.V,
	}
	response.Msg.MerchantDetail = &authv1.MerchantAuthServiceDetail{
		Domain: currentMerchant.Domain,
		Name:   currentMerchant.Name,
	}
	if company != nil {
		response.Msg.Company = &authv1.CompanyModel{
			IdCompany: company.IdCompany.Hex(),
			Name:      company.Name,
			Street:    company.Street,
			Mst:       company.Mst,
			Logo:      company.Logo,
			IdCity:    company.IdCity.V,
			IdState:   company.IdState.V,
			IdCountry: company.IdCountry.V,
		}
	}
	response.Msg.Paths = listPathOfRole
	response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return response, nil
}

func NewMerchantAuthService(infra authinfra.Infra) authv1connect.MerchantAuthServiceHandler {
	return &merchantServiceImpl{infra: infra}
}
