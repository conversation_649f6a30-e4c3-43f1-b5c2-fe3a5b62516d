package customerv1svc

import (
	authv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1"
	"git.tmproxy-infra.com/algo/authsvc/internal/autherr"
	"golang.org/x/net/html"
	"strings"
)

func _loginValidate(request *authv1.CustomerAuthServiceLoginRequest) error {
	switch {
	case len(request.GetEmail()) == 0:
		return autherr.EmailRequiredError
	case len(request.GetPassword()) == 0:
		return autherr.PasswordRequiredError
	}
	return nil
}
func _loginOAuthValidate(request *authv1.CustomerAuthServiceLoginOAuthRequest) error {
	switch {
	case len(request.GetOauthToken()) == 0:
		return autherr.OAuthTokenRequiredError
	}
	return nil
}
func _signupValidateRequest(request *authv1.CustomerAuthServiceSignUpRequest) (err error) {
	request.Email = html.EscapeString(strings.TrimSpace(request.GetEmail()))
	request.FirstName = html.EscapeString(strings.TrimSpace(request.GetFirstName()))
	request.LastName = html.EscapeString(strings.TrimSpace(request.GetLastName()))
	switch {
	case len(request.GetEmail()) == 0:
		return autherr.EmailRequiredError
	case len(request.GetPassword()) == 0:
		return autherr.PasswordRequiredError
	case len(request.GetFirstName()) == 0:
		return autherr.FirstNameRequiredError
	case len(request.GetLastName()) == 0:
		return autherr.LastNameRequiredError
	case len(request.GetPhoneNumber()) == 0:
		return autherr.PhoneNumberRequiredError
	}
	return nil
}
