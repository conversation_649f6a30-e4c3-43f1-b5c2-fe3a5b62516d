package customerv1svc

import (
	"context"
	"fmt"
	"time"

	"connectrpc.com/connect"
	authv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1"
	"git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1/authv1connect"
	errmsgv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	dbipv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/misc/dbip/v1"
	"git.tmproxy-infra.com/algo/authsvc/internal/autherr"
	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	authinfra "git.tmproxy-infra.com/algo/authsvc/internal/infra"
	"git.tmproxy-infra.com/algo/common/enumtype"
	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"git.tmproxy-infra.com/algo/common/pkg/usercontext"
	"github.com/pkg/errors"
	"golang.org/x/sync/errgroup"
	"google.golang.org/api/idtoken"
)

type customerAuthServiceImpl struct {
	infra authinfra.Infra
}

func (a *customerAuthServiceImpl) ForgotPassword(ctx context.Context, c *connect.Request[authv1.CustomerAuthServiceForgotPasswordRequest]) (res *connect.Response[authv1.CustomerAuthServiceForgotPasswordResponse], err error) {
	//TODO implement me
	res = connect.NewResponse[authv1.CustomerAuthServiceForgotPasswordResponse](
		&authv1.CustomerAuthServiceForgotPasswordResponse{
			Error: &errmsgv1.ErrorMessage{},
		})
	var (
		ipUser = c.Header().Get("X-Real-Ip")
	)

	requestInternal := connect.NewRequest[authv1.InternalAuthServiceForgotPasswordRequest](&authv1.InternalAuthServiceForgotPasswordRequest{
		Email:       c.Msg.Email,
		NewPassword: c.Msg.NewPassword,
		IpAddress:   ipUser,
		Otp:         c.Msg.GetOtp(),
	})

	listApp, err := a.infra.AppRepository().Select(ctx, pdb.OptionParams[authdao.ParamsAppSelect](
		authdao.AppSelectWithAppType(enumtype.MerchantAppType),
		authdao.AppSelectWithDomain(c.Msg.GetDomain()),
		authdao.AppSelectWithActiveOrDeactivate(true, false),
	))
	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}
	if len(listApp) == 0 {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_APP_EXIST
		return res, errors.Errorf("app %s, not found", c.Msg.GetDomain())
	}
	currentApp := listApp[0]
	requestInternal.Msg.Domain = currentApp.Domain

	responseInternal, err := a.infra.GetAuthInternalHandler().ForgotPassword(ctx, requestInternal)
	if err != nil {
		if responseInternal != nil {
			if responseInternal.Msg.Error.Code != errmsgv1.ErrorCode_ERROR_CODE_SUCCESS {
				res.Msg.Error.Code = responseInternal.Msg.Error.Code
				return res, errors.WithStack(err)
			}
		}
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}
	res.Msg.Error.Code = responseInternal.Msg.GetError().Code
	return res, nil
}

func (c *customerAuthServiceImpl) ChangePassword(ctx context.Context, request *connect.Request[authv1.CustomerAuthServiceChangePasswordRequest]) (response *connect.Response[authv1.CustomerAuthServiceChangePasswordResponse], err error) {
	response = connect.NewResponse[authv1.CustomerAuthServiceChangePasswordResponse](&authv1.CustomerAuthServiceChangePasswordResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	userCtx := usercontext.FromContext(ctx)
	if userCtx == nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.New("user context is nil, interceptor has problem")
	}
	idUser, err := algouid.FromHex(userCtx.IdAuthUser)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return response, errors.WithStack(err)
	}
	if err = c.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		listUserDao, err := c.infra.User().UserRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsUserSelect](
			authdao.UserSelectWithIdUser(idUser),
		))
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listUserDao) == 0 {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_USER_NOT_EXIST
			return autherr.ErrUserNotExist
		}
		currentUser := listUserDao[0]
		ok, err := c.infra.CommonFunction().VerifyPasswordAndSalt(request.Msg.GetOldPassword(), currentUser.HashPassword, currentUser.Salt)
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if !ok {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_OLD_PASSWORD_INCORRECT
			return errors.New("old password incorrect")
		}
		hashPassword, salt := c.infra.CommonFunction().GeneratePasswordAndSalt(request.Msg.GetNewPassword())
		paramUserUpdate := pdb.OptionParams[authdao.ParamsUserUpdate](
			authdao.UserUpdateWithIdUser(currentUser.IdUser),
			authdao.UserUpdateHashPassword(currentUser.HashPassword, hashPassword),
			authdao.UserUpdateSalt(currentUser.Salt, salt),
		)
		if err = c.infra.User().UserRepository().Update(tranCtx, paramUserUpdate); err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return response, errors.WithStack(err)
	}
	response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return response, nil
}

func NewCustomerAuthService(infra authinfra.Infra) authv1connect.CustomerAuthServiceHandler {
	return &customerAuthServiceImpl{infra: infra}
}

func (c *customerAuthServiceImpl) SignUp(ctx context.Context, request *connect.Request[authv1.CustomerAuthServiceSignUpRequest]) (response *connect.Response[authv1.CustomerAuthServiceSignUpResponse], err error) {
	response = connect.NewResponse[authv1.CustomerAuthServiceSignUpResponse](&authv1.CustomerAuthServiceSignUpResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	var (
		xCountry = request.Header().Get("X-App-Country")
	)

	loggedInUser := usercontext.FromContext(ctx)
	if loggedInUser != nil && len(loggedInUser.IdAuthUser) > 0 {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
		return response, errors.New("user already logged in")
	}
	if err = _signupValidateRequest(request.Msg); err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		response.Msg.Error.Message = err.Error()
		return response, errors.WithStack(err)
	}
	idRoleMerchantUser, err := algouid.FromHex(authinfra.IdRoleMerchantUser)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.Wrap(err, "id role merchant user is invalid")
	}
	// because path in role_public, we need check xRealHost for app
	//request.Header().Set("Origin", "https://proxyv4.vn")
	xRealHost := request.Header().Get("X-Real-Host")
	if xRealHost == "" {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return response, errors.New("xRealHost is empty")
	}
	currentApp, err := c.infra.CommonFunction().FetchAppByCountry(ctx, pdb.OptionParams[authdao.ParamsAppSelect](
		authdao.AppSelectWithDomain(xRealHost),
		authdao.AppSelectOrderByActiveState(),
	), xCountry)

	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}

	if err = c.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		total, err := c.infra.User().UserRepository().Count(tranCtx, pdb.OptionParams[authdao.ParamsUserSelect](
			authdao.UserSelectWithIdApp(algouid.FromHexOrNil(currentApp.IdApp)),
			authdao.UserSelectWithEmail(request.Msg.GetEmail()),
			authdao.UserSelectWithPagination(0, 1),
		))
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if total > 0 {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_USER_EXIST
			return errors.New("email exist, please try again with another email")
		}
		userRefId := algouid.UUID{}
		if request.Msg.GetRefCode() != "" {
			userRef, err := c.infra.User().UserDetailRepository().Select(ctx, pdb.OptionParams[authdao.ParamsUserDetailSelect](
				authdao.UserDetailSelectWithRefCode(request.Msg.GetRefCode()),
			))
			if err != nil {
				response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
				return errors.WithStack(err)
			}
			if len(userRef) == 0 {
				response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_REF_CODE_INCORRECT
				return autherr.ErrRefCodeNotExist
			}
			userRefId = userRef[0].IdUser
		}

		tnowUnix := time.Now().Unix()
		newUser := &authdao.User{
			Email:  request.Msg.GetEmail(),
			IdApp:  algouid.FromHexOrNil(currentApp.IdApp),
			IdRole: idRoleMerchantUser,
			AtUnix: pdb.AtUnix{
				CreatedAt: tnowUnix,
				UpdatedAt: tnowUnix,
				DeletedAt: 0,
			},
		}
		newUser.IdUser, err = algouid.NewUUID()
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		newUser.HashPassword, newUser.Salt = c.infra.CommonFunction().GeneratePasswordAndSalt(request.Msg.GetPassword())
		userDetail := &authdao.UserDetail{
			IdUser:      newUser.IdUser,
			FirstName:   request.Msg.GetFirstName(),
			LastName:    request.Msg.GetLastName(),
			PhoneNumber: request.Msg.GetPhoneNumber(),
			RefCode:     c.infra.CommonFunction().GenerateRefCode(request.Msg.GetEmail()),
			UserRefId:   userRefId,
			AtUnix: pdb.AtUnix{
				CreatedAt: tnowUnix,
				UpdatedAt: tnowUnix,
				DeletedAt: 0,
			},
		}
		userDetail.IdUserDetail, err = algouid.NewUUID()
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if err = c.infra.User().UserRepository().Create(tranCtx, newUser); err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if err = c.infra.User().UserDetailRepository().Create(tranCtx, userDetail); err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		//resp, err := c.infra.ExternalService().BillingUserTransactionInternalClient().CreateUserBalance(ctx, connect.NewRequest[transactionv1.InternalTransactionServiceCreateUserBalanceRequest](&transactionv1.InternalTransactionServiceCreateUserBalanceRequest{
		//	IdUser: newUser.IdUser.Hex(),
		//	IdApp:  newUser.IdApp.Hex(),
		//}))
		//if err != nil {
		//	response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		//	return errors.WithStack(err)
		//}
		//if resp.Msg.Error.Code != errmsgv1.ErrorCode_ERROR_CODE_SUCCESS {
		//	return errors.Wrapf(errors.New("customer.Signup failed"), "create user balance error: %s", resp.Msg.Error.Message)
		//}
		//if errResp := resp.Msg.Error; errResp == nil || resp.Msg.Error.Code != errmsgv1.ErrorCode_ERROR_CODE_SUCCESS {
		//	response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		//	return errors.New("create user balance fail")
		//}
		return nil
	}); err != nil {
		return response, errors.WithStack(err)
	}
	response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return response, nil
}

func (c *customerAuthServiceImpl) Login(ctx context.Context, request *connect.Request[authv1.CustomerAuthServiceLoginRequest]) (response *connect.Response[authv1.CustomerAuthServiceLoginResponse], err error) {
	response = connect.NewResponse[authv1.CustomerAuthServiceLoginResponse](&authv1.CustomerAuthServiceLoginResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	if err = _loginValidate(request.Msg); err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		response.Msg.Error.Message = err.Error()
		return response, errors.WithStack(err)
	}
	var (
		xRealHost = request.Header().Get("X-Real-Host")
		userAgent = request.Header().Get("User-Agent")
		ipAddress = request.Header().Get("X-Real-Ip")
	)
	requestInternal := connect.NewRequest[authv1.InternalAuthServiceLoginRequest](&authv1.InternalAuthServiceLoginRequest{
		Domain:    xRealHost,
		Email:     request.Msg.GetEmail(),
		Password:  request.Msg.GetPassword(),
		IpAddress: ipAddress,
		UserAgent: userAgent,
		Otp:       request.Msg.GetOtp(),
		DeviceId:  request.Msg.GetDeviceId(),
	})
	requestInternal.Header().Set("X-App-Country", request.Header().Get("X-App-Country"))
	loginInternalResponse, err := c.infra.GetAuthInternalHandler().Login(ctx, requestInternal)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	if loginInternalResponse.Msg.Error.Code != errmsgv1.ErrorCode_ERROR_CODE_SUCCESS {
		response.Msg.Error.Code = loginInternalResponse.Msg.Error.Code
		return response, errors.New("login internal failed: " + loginInternalResponse.Msg.Error.Code.String())
	}
	response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	response.Msg.Token = loginInternalResponse.Msg.GetToken()
	response.Msg.RefreshToken = loginInternalResponse.Msg.GetRefreshToken()
	return response, nil
}
func (a *customerAuthServiceImpl) RefreshToken(ctx context.Context, c *connect.Request[authv1.CustomerAuthServiceRefreshTokenRequest]) (res *connect.Response[authv1.CustomerAuthServiceRefreshTokenResponse], err error) {
	//TODO implement me
	res = connect.NewResponse[authv1.CustomerAuthServiceRefreshTokenResponse](
		&authv1.CustomerAuthServiceRefreshTokenResponse{
			Error: &errmsgv1.ErrorMessage{},
		})
	requestInternal := connect.NewRequest[authv1.InternalAuthServiceRefreshTokenRequest](&authv1.InternalAuthServiceRefreshTokenRequest{
		DeviceId:     c.Msg.GetDeviceId(),
		RefreshToken: c.Msg.GetRefreshToken(),
	})
	refreshToken, err := a.infra.GetAuthInternalHandler().RefreshToken(ctx, requestInternal)
	if err != nil {
		res.Msg.Error.Code = refreshToken.Msg.Error.Code
		return res, errors.WithStack(err)
	}
	res.Msg.RefreshToken = refreshToken.Msg.RefreshToken
	res.Msg.Token = refreshToken.Msg.Token
	return res, nil
}
func (c *customerAuthServiceImpl) Me(ctx context.Context, request *connect.Request[authv1.CustomerAuthServiceMeRequest]) (response *connect.Response[authv1.CustomerAuthServiceMeResponse], err error) {
	response = connect.NewResponse[authv1.CustomerAuthServiceMeResponse](&authv1.CustomerAuthServiceMeResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	userCtx := usercontext.FromContext(ctx)
	if userCtx == nil {
		// interceptor work incorrect
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.New("user context is nil")
	}

	idUser, err := algouid.FromHex(userCtx.IdAuthUser)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	idMerchant, err := algouid.FromHex(userCtx.IdAuthApp)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}

	var (
		currentUserDetail *authdao.UserDetail
		currentUser       *authdao.User
	)
	errGr, _ := errgroup.WithContext(ctx)
	errGr.Go(func() error {
		listUser, err := c.infra.User().UserRepository().Select(ctx, pdb.OptionParams[authdao.ParamsUserSelect](
			authdao.UserSelectWithIdUser(idUser),
			authdao.UserSelectWithIdApp(idMerchant),
			authdao.UserSelectWithActiveOrDeactivate(true, false),
			authdao.UserSelectWithPagination(0, 1),
		))
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listUser) == 0 {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return autherr.ErrUserNotExist
		}
		currentUser = listUser[0]
		return nil
	})
	errGr.Go(func() error {
		listUserDetailDao, err := c.infra.User().UserDetailRepository().Select(ctx, pdb.OptionParams[authdao.ParamsUserDetailSelect](
			authdao.UserDetailSelectWithIdUser(idUser),
			authdao.UserDetailSelectWithActiveOrDeactivate(true, false),
			authdao.UserDetailSelectWithPagination(0, 1),
		))
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listUserDetailDao) == 0 {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return autherr.ErrUserNotExist
		}
		currentUserDetail = listUserDetailDao[0]
		return nil
	})
	if err = errGr.Wait(); err != nil {
		return response, errors.WithStack(err)
	}
	if currentUserDetail == nil || currentUser == nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.New("user detail or user is nil")
	}
	response.Msg.UserDetail = &authv1.CustomerMeUserDetails{
		FirstName:   currentUserDetail.FirstName,
		LastName:    currentUserDetail.LastName,
		Email:       currentUser.Email,
		PhoneNumber: currentUserDetail.PhoneNumber,
		RefCode:     currentUserDetail.RefCode,
		UserRefId:   currentUserDetail.UserRefId.Hex(),
	}
	listPathOfRole, err := c.infra.PolicyManager().GetAllPathOfRole(ctx, userCtx.IdAuthRole)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	response.Msg.Paths = listPathOfRole
	response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return response, nil
}

func (a *customerAuthServiceImpl) LoginOAuth(ctx context.Context, request *connect.Request[authv1.CustomerAuthServiceLoginOAuthRequest]) (response *connect.Response[authv1.CustomerAuthServiceLoginOAuthResponse], err error) {
	response = connect.NewResponse[authv1.CustomerAuthServiceLoginOAuthResponse](&authv1.CustomerAuthServiceLoginOAuthResponse{
		Error: &errmsgv1.ErrorMessage{},
	})

	var (
		xCountry  = request.Header().Get("X-App-Country")
		xRealHost = request.Header().Get("X-Real-Host")
		userAgent = request.Header().Get("User-Agent")
		ipAddress = request.Header().Get("X-Real-Ip")
	)
	if err = _loginOAuthValidate(request.Msg); err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		response.Msg.Error.Message = err.Error()
		return response, errors.WithStack(err)
	}

	currentApp, err := a.infra.CommonFunction().FetchAppByCountry(ctx, pdb.OptionParams[authdao.ParamsAppSelect](
		authdao.AppSelectWithDomain(xRealHost),
		authdao.AppSelectOrderByActiveState(),
	), xCountry)

	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}

	listOAuth, err := a.infra.OAuthConfigRepository().Select(ctx, pdb.OptionParams[authdao.ParamsOAuthConfigSelect](
		authdao.OAuthConfigSelectWithIdAuthApp(algouid.FromHexOrNil(currentApp.IdApp)),
		authdao.OAuthConfigSelectWithActiveOrDeactivate(true, false),
		authdao.OAuthConfigSelectWithPagination(0, 1),
	))
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	if len(listOAuth) == 0 {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_CONFIG_NOT_EXIST
		return response, errors.Errorf("xRealHost: %s cannot setup oauth", xRealHost)
	}
	oAuth := listOAuth[0]
	payload, err := idtoken.Validate(ctx, request.Msg.GetOauthToken(), oAuth.ClientId)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return response, err
	}
	isRevoked, err := a.infra.SessionManager().IsTokenRevoked(ctx, request.Msg.GetOauthToken(), currentApp.IdApp)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	if isRevoked == true {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return response, errors.Errorf("token: %s is revoked", request.Msg.GetOauthToken())
	}
	email := payload.Claims["email"].(string)
	firstname := payload.Claims["given_name"].(string)
	lastname := payload.Claims["family_name"].(string)

	listUserDao, err := a.infra.User().UserRepository().Select(ctx, pdb.OptionParams[authdao.ParamsUserSelect](
		authdao.UserSelectWithIdApp(algouid.FromHexOrNil(currentApp.IdApp)),
		authdao.UserSelectWithEmail(email),
		authdao.UserSelectWithActiveOrDeactivate(true, false),
		authdao.UserSelectWithPagination(0, 1),
	))
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	userContext := &usercontext.UserContext{}
	currentUser := &authdao.User{}
	if len(listUserDao) > 0 {
		currentUser = listUserDao[0]
		dbIpResponse, err := a.infra.ExternalService().MiscDBIPClient().IPInfo(ctx, connect.NewRequest(&dbipv1.PublicDBIPServiceIPInfoRequest{
			IpAddr: ipAddress,
		}))
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return response, errors.WithStack(err)
		}
		userContext = &usercontext.UserContext{
			IdAuthApp:  currentUser.IdApp.Hex(),
			IdAuthUser: currentUser.IdUser.Hex(),
			IdAuthRole: currentUser.IdRole.Hex(),
			UserAgent:  userAgent,
			UserIP:     ipAddress,
			Domain:     currentApp.Domain,
			Latitude:   dbIpResponse.Msg.GetIpInfo().GetGeoLocation().GetLat(),
			Longitude:  dbIpResponse.Msg.GetIpInfo().GetGeoLocation().GetLong(),
		}
		fmt.Println("userContext login", userContext)
	} else {
		signUp := connect.NewRequest[authv1.CustomerAuthServiceSignUpRequest](&authv1.CustomerAuthServiceSignUpRequest{
			Email:       email,
			Password:    fmt.Sprintf(request.Msg.GetOauthToken(), "_", oAuth.ClientSecret),
			RefCode:     "",
			FirstName:   firstname,
			LastName:    lastname,
			PhoneNumber: "000000000",
		})
		signUp.Header().Set("X-Real-Host", request.Header().Get("X-Real-Host"))
		resSignUp, errSignUp := a.SignUp(ctx, signUp)
		if errSignUp != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return response, errors.WithStack(errSignUp)
		}
		if resSignUp.Msg.Error.Code != errmsgv1.ErrorCode_ERROR_CODE_SUCCESS {
			response.Msg.Error.Code = resSignUp.Msg.Error.Code
			return response, errors.WithStack(errSignUp)
		}
		listUserDao, err = a.infra.User().UserRepository().Select(ctx, pdb.OptionParams[authdao.ParamsUserSelect](
			authdao.UserSelectWithIdApp(algouid.FromHexOrNil(currentApp.IdApp)),
			authdao.UserSelectWithEmail(email),
			authdao.UserSelectWithActiveOrDeactivate(true, false),
			authdao.UserSelectWithPagination(0, 1),
		))
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return response, errors.WithStack(err)
		}
		if len(listUserDao) > 0 {
			currentUser = listUserDao[0]
			dbIpResponse, err := a.infra.ExternalService().MiscDBIPClient().IPInfo(ctx, connect.NewRequest(&dbipv1.PublicDBIPServiceIPInfoRequest{
				IpAddr: ipAddress,
			}))
			if err != nil {
				response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
				return response, errors.WithStack(err)
			}
			userContext = &usercontext.UserContext{
				IdAuthApp:  currentUser.IdApp.Hex(),
				IdAuthUser: currentUser.IdUser.Hex(),
				IdAuthRole: currentUser.IdRole.Hex(),
				UserAgent:  userAgent,
				UserIP:     ipAddress,
				Domain:     currentApp.Domain,
				Latitude:   dbIpResponse.Msg.GetIpInfo().GetGeoLocation().GetLat(),
				Longitude:  dbIpResponse.Msg.GetIpInfo().GetGeoLocation().GetLong(),
			}
		} else {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return response, errors.WithStack(err)
		}
	}
	token, err := a.infra.SessionManager().GenerateToken(ctx, currentUser.IdApp.Hex(), userContext)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}

	refreshToken, err := a.infra.CommonFunction().GenerateRefreshToken(ctx, currentUser.IdApp.Hex(), request.Msg.GetDeviceId(), userContext)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	err = a.infra.SessionManager().RevokeToken(ctx, request.Msg.GetOauthToken(), currentApp.IdApp)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	response.Msg.Token = token
	response.Msg.RefreshToken = refreshToken
	response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return response, err
}

func (a *customerAuthServiceImpl) FetchOAuthApp(ctx context.Context, c *connect.Request[authv1.CustomerAuthServiceFetchOAuthAppRequest]) (response *connect.Response[authv1.CustomerAuthServiceFetchOAuthAppResponse], err error) {
	response = connect.NewResponse[authv1.CustomerAuthServiceFetchOAuthAppResponse](&authv1.CustomerAuthServiceFetchOAuthAppResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	var (
		xRealHost = c.Header().Get("X-Real-Host")
		xCountry  = c.Header().Get("X-App-Country")
	)
	currentApp, err := a.infra.CommonFunction().FetchAppByCountry(ctx, pdb.OptionParams[authdao.ParamsAppSelect](
		authdao.AppSelectWithDomain(xRealHost),
		authdao.AppSelectOrderByActiveState(),
	), xCountry)

	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}

	listOAuth, err := a.infra.OAuthConfigRepository().Select(ctx, pdb.OptionParams[authdao.ParamsOAuthConfigSelect](
		authdao.OAuthConfigSelectWithIdAuthApp(algouid.FromHexOrNil(currentApp.IdApp)),
		authdao.OAuthConfigSelectWithActiveOrDeactivate(true, false),
		authdao.OAuthConfigSelectWithPagination(0, 1),
	))
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	if len(listOAuth) == 0 {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_CONFIG_NOT_EXIST
		return response, errors.Errorf("xRealHost: %s cannot setup oauth", xRealHost)
	}
	currentOAuth := listOAuth[0]

	response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	response.Msg.ClientId = currentOAuth.ClientId
	return response, nil
}
