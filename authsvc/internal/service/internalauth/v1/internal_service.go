package internalv1svc

import (
	"context"
	"fmt"
	"net/smtp"
	"strconv"
	"strings"
	"time"

	"aidanwoods.dev/go-paseto"
	"connectrpc.com/connect"
	authv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1"
	"git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1/authv1connect"
	errmsgv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	dbipv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/misc/dbip/v1"
	utilsv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	"git.tmproxy-infra.com/algo/authsvc/internal/autherr"
	cpauthdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	authinfra "git.tmproxy-infra.com/algo/authsvc/internal/infra"
	sessionredis "git.tmproxy-infra.com/algo/authsvc/internal/inmem/session"
	totpredis "git.tmproxy-infra.com/algo/authsvc/internal/inmem/totp"
	"git.tmproxy-infra.com/algo/common/asynqtask/sendemailotp"
	"git.tmproxy-infra.com/algo/common/enumtype"
	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"git.tmproxy-infra.com/algo/common/pkg/rpcutils"
	"git.tmproxy-infra.com/algo/common/pkg/usercontext"
	"github.com/pkg/errors"
	"github.com/pquerna/otp/totp"
)

var _ authv1connect.InternalAuthServiceHandler = (*authInternalService)(nil)

type authInternalService struct {
	infra authinfra.Infra
}

func (a *authInternalService) ForgotPassword(ctx context.Context, c *connect.Request[authv1.InternalAuthServiceForgotPasswordRequest]) (res *connect.Response[authv1.InternalAuthServiceForgotPasswordResponse], err error) {
	res = &connect.Response[authv1.InternalAuthServiceForgotPasswordResponse]{
		Msg: &authv1.InternalAuthServiceForgotPasswordResponse{
			Error: &errmsgv1.ErrorMessage{},
		},
	}

	if err = forgotPasswordValidateRequest(c.Msg); err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return res, errors.WithStack(err)
	}

	//const (
	//	NUMBER_START_LOCK = 10
	//)
	//if err = a.infra.CommonFunction().GetLockByIp(ctx, c.Msg.GetIpAddress(), NUMBER_START_LOCK); err != nil {
	//	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
	//	return res, errors.WithStack(err)
	//}

	listApp, err := a.infra.AppRepository().Select(ctx, pdb.OptionParams[cpauthdao.ParamsAppSelect](
		cpauthdao.AppSelectWithDomain(c.Msg.GetDomain()),
	))
	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}
	if len(listApp) == 0 {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_APP_NOT_EXIST
		return res, errors.WithStack(err)
	}
	currentApp := listApp[0]
	keyOtp := []string{
		sessionredis.PATH_OTP_FORGOT_PASSWORD,
		currentApp.IdApp.Hex(),
		c.Msg.GetEmail(),
	}
	if len(c.Msg.Otp) == 0 {
		listUserDao, err := a.infra.User().UserRepository().Select(ctx, pdb.OptionParams[cpauthdao.ParamsUserSelect](
			cpauthdao.UserSelectWithIdApp(currentApp.IdApp),
			cpauthdao.UserSelectWithEmail(c.Msg.GetEmail()),
			cpauthdao.UserSelectWithActiveOrDeactivate(true, false),
			cpauthdao.UserSelectWithPagination(0, 1),
		))
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return res, errors.WithStack(err)
		}
		if len(listUserDao) == 0 {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_USER_NOT_EXIST
			return res, errors.WithStack(err)
		}

		_, duration, err := a.infra.SessionManager().GetOtp(ctx, keyOtp...)
		if err == nil && duration.Minutes() > 7 {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_OTP_REQUIRED
			return res, errors.WithStack(err)
		}
		otpInital, err := a.infra.SessionManager().InitOtp(ctx, keyOtp...)
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return res, errors.WithStack(err)
		}
		fmt.Println(otpInital)
		//payloadSendMailOtp := &sendemailotpforgotpassword.Payload{
		//	To:        c.Msg.GetEmail(),
		//	OTP:       otpInital,
		//	IdAuthApp: currentApp.IdApp,
		//	Subject:   "Mã OTP quên mật khẩu",
		//	AppName:   currentApp.Name,
		//}
		//task, err := sendemailotpforgotpassword.NewSendEmailTask(payloadSendMailOtp)
		//if err != nil {
		//	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		//	return res, errors.WithStack(err)
		//}
		//_, err = a.infra.AsynqClient().Enqueue(task)
		//if err != nil {
		//	a.infra.Logger().Error("could not enqueue task: " + err.Error())
		//	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		//	return res, errors.WithStack(err)
		//}
		a.infra.SessionManager().ResetCountByIp(ctx, c.Msg.GetIpAddress())
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_OTP_REQUIRED
		return res, errors.WithStack(err)
	}

	if err = forgotPasswordValidateStepChangePassRequest(c.Msg); err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return res, errors.WithStack(err)
	}

	otpVerify, _, err := a.infra.SessionManager().GetOtp(ctx, keyOtp...)
	if err != nil || otpVerify != c.Msg.GetOtp() {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_OTP_VERIFY_INCORRECT
		return res, errors.WithStack(err)
	}
	err = a.infra.SessionManager().RemoveOtp(ctx, keyOtp...)
	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}

	if err = a.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		users, err := a.infra.User().UserRepository().Select(tranCtx, pdb.OptionParams[cpauthdao.ParamsUserSelect](
			cpauthdao.UserSelectWithIdApp(currentApp.IdApp),
			cpauthdao.UserSelectWithEmail(c.Msg.GetEmail()),
			cpauthdao.UserSelectWithPagination(0, 1),
		))
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(users) == 0 {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_USER_EXIST
			return errors.New("email exist, please try again with another email")
		}
		currentUser := users[0]
		hashPassword, salt := a.infra.CommonFunction().GeneratePasswordAndSalt(c.Msg.GetNewPassword())
		paramUserUpdate := pdb.OptionParams[cpauthdao.ParamsUserUpdate](
			cpauthdao.UserUpdateWithIdUser(currentUser.IdUser),
			cpauthdao.UserUpdateHashPassword(currentUser.HashPassword, hashPassword),
			cpauthdao.UserUpdateSalt(currentUser.Salt, salt),
		)
		paramUserUpdate.IdUser = currentUser.IdUser

		if err = a.infra.User().UserRepository().Update(tranCtx, paramUserUpdate); err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return res, errors.WithStack(err)
	}
	a.infra.SessionManager().ResetCountByIp(ctx, c.Msg.GetIpAddress())
	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return res, nil
}

func (a *authInternalService) RefreshToken(ctx context.Context, c *connect.Request[authv1.InternalAuthServiceRefreshTokenRequest]) (response *connect.Response[authv1.InternalAuthServiceRefreshTokenResponse], err error) {
	response = connect.NewResponse[authv1.InternalAuthServiceRefreshTokenResponse](&authv1.InternalAuthServiceRefreshTokenResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	if err = refreshTokenValidateRequest(c.Msg); err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		response.Msg.Error.Message = err.Error()
		return response, errors.WithStack(err)
	}
	var (
		token        string
		refreshToken string
	)
	userContext, uuidToken, err := a.infra.SessionManager().VerifyRefreshToken(ctx, c.Msg.GetRefreshToken())
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_REFRESH_TOKEN_EXPRIRY
		return response, errors.WithStack(err)
	}
	token, err = a.infra.SessionManager().GenerateToken(ctx, userContext.IdAuthApp, userContext)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	refreshToken, err = a.infra.CommonFunction().GenerateRefreshToken(ctx, userContext.IdAuthApp, c.Msg.GetDeviceId(), userContext)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	if err = a.infra.SessionManager().RemoveRefreshToken(ctx, uuidToken, c.Msg.GetRefreshToken()); err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	response.Msg.Token = token
	response.Msg.RefreshToken = refreshToken
	return response, nil
}

func (a *authInternalService) FetchUser(ctx context.Context, c *connect.Request[authv1.InternalAuthServiceFetchUserRequest]) (*connect.Response[authv1.InternalAuthServiceFetchUserResponse], error) {
	//TODO implement me
	panic("implement me")
}

func (a *authInternalService) FetchUserInfo(ctx context.Context, request *connect.Request[authv1.InternalAuthServiceFetchUserInfoRequest]) (response *connect.Response[authv1.InternalAuthServiceFetchUserInfoResponse], err error) {
	response = connect.NewResponse[authv1.InternalAuthServiceFetchUserInfoResponse](&authv1.InternalAuthServiceFetchUserInfoResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	listIdUser := make([]algouid.UUID, 0, len(request.Msg.ListIdUser))
	for _, idAuthUserHex := range request.Msg.GetListIdUser() {
		idAuthUser, err := algouid.FromHex(idAuthUserHex)
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return response, errors.WithStack(err)
		}
		listIdUser = append(listIdUser, idAuthUser)
	}
	listUser, err := a.infra.User().UserRepository().Select(ctx, pdb.OptionParams[cpauthdao.ParamsUserSelect](
		cpauthdao.UserSelectWithListIdUser(listIdUser),
	))
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	if len(listUser) == 0 {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.New("user not found")
	}
	mapUser := make(map[algouid.UUID]*cpauthdao.User, len(listUser))
	for i, v := range listUser {
		mapUser[v.IdUser] = listUser[i]
	}
	listUserDetail, err := a.infra.User().UserDetailRepository().Select(ctx, pdb.OptionParams[cpauthdao.ParamsUserDetailSelect](
		cpauthdao.UserDetailSelectWithListIdUser(listIdUser),
		cpauthdao.UserDetailSelectWithActiveOrDeactivate(true, false),
	))
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	listUserModel := make([]*authv1.InternalUserInfoModel, 0, len(listUserDetail))
	for _, v := range listUserDetail {
		userOf, ok := mapUser[v.IdUser]
		if !ok {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return response, errors.New("user not found")
		}
		listUserModel = append(listUserModel, &authv1.InternalUserInfoModel{
			IdUser:    v.IdUser.Hex(),
			Email:     userOf.Email,
			FirstName: v.FirstName,
			LastName:  v.LastName,
			IsActive:  userOf.IsActive(),
		})
	}
	response.Msg.Users = listUserModel
	return response, nil
}

func NewAuthInternalService(infra authinfra.Infra) authv1connect.InternalAuthServiceHandler {
	return &authInternalService{infra: infra}
}

func (a *authInternalService) CreateAppMerchant(ctx context.Context, createAppMerchantRequest *connect.Request[authv1.InternalAuthServiceCreateAppMerchantRequest]) (createAppMerchantResponse *connect.Response[authv1.InternalAuthServiceCreateAppMerchantResponse], err error) {
	createAppMerchantResponse = connect.NewResponse[authv1.InternalAuthServiceCreateAppMerchantResponse](&authv1.InternalAuthServiceCreateAppMerchantResponse{
		Error: &errmsgv1.ErrorMessage{},
	})

	if err = createAppMerchantValidateRequest(createAppMerchantRequest.Msg); err != nil {
		createAppMerchantResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		createAppMerchantResponse.Msg.Error.Message = err.Error()
		return createAppMerchantResponse, errors.WithStack(err)
	}
	if err = a.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		idApp, err := algouid.FromHex(createAppMerchantRequest.Msg.GetIdApp())
		if err != nil {
			createAppMerchantResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		countAppDomain, err := a.infra.AppRepository().Count(tranCtx, pdb.OptionParams[cpauthdao.ParamsAppSelect](
			cpauthdao.AppSelectWithDomain(createAppMerchantRequest.Msg.GetDomain()),
		))
		if err != nil {
			createAppMerchantResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if countAppDomain > 0 {
			createAppMerchantResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_APP_EXIST
			return autherr.ErrAppExist
		}
		countAppName, err := a.infra.AppRepository().Count(tranCtx, pdb.OptionParams[cpauthdao.ParamsAppSelect](
			cpauthdao.AppSelectWithName(createAppMerchantRequest.Msg.GetName()),
		))
		if err != nil {
			createAppMerchantResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if countAppName > 0 {
			createAppMerchantResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_APP_EXIST
			return autherr.ErrAppExist
		}
		tnow := time.Now().Unix()
		privateKey := paseto.NewV4AsymmetricSecretKey()
		if err != nil {
			createAppMerchantResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		newApp := &cpauthdao.App{
			IdApp:          idApp,
			Name:           createAppMerchantRequest.Msg.GetName(),
			AppType:        enumtype.MerchantAppType,
			AppCountry:     authinfra.ConvertAppCountryDaoToAppCountryEnum(createAppMerchantRequest.Msg.GetAppCountry()),
			Domain:         createAppMerchantRequest.Msg.GetDomain(),
			TokenTTL:       86400,
			PrivateEd25519: privateKey.ExportHex(),
			PublicEd25519:  privateKey.Public().ExportHex(),
			AtUnix: pdb.AtUnix{
				CreatedAt: tnow,
				UpdatedAt: tnow,
				DeletedAt: 0,
			},
		}
		if err = a.infra.AppRepository().Create(tranCtx, newApp); err != nil {
			return errors.WithStack(err)
		}
		if err = a.infra.SessionManager().SetPasetoConfig(tranCtx, newApp.IdApp.Hex(), newApp.PublicEd25519, newApp.PrivateEd25519, newApp.Name, newApp.TokenTTL); err != nil {
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return createAppMerchantResponse, errors.WithStack(err)
	}
	createAppMerchantResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return createAppMerchantResponse, nil
}

func (a *authInternalService) FetchApp(ctx context.Context, fetchAppRequest *connect.Request[authv1.InternalAuthServiceFetchAppRequest]) (fetchAppResponse *connect.Response[authv1.InternalAuthServiceFetchAppResponse], err error) {
	fetchAppResponse = &connect.Response[authv1.InternalAuthServiceFetchAppResponse]{
		Msg: &authv1.InternalAuthServiceFetchAppResponse{
			Items: make([]*authv1.InternalAppModel, 0),
			Error: &errmsgv1.ErrorMessage{},
			Pagination: &utilsv1.PaginationResponse{
				CurrentPage: 0,
				PageSize:    0,
				Total:       0,
				TotalPages:  0,
			},
		},
	}
	var (
		requestFetchApp                     = fetchAppRequest.Msg
		pageSize, pageNumber, offset, limit = rpcutils.ToOffsetLimit(requestFetchApp.GetPagination().GetPageSize(), requestFetchApp.GetPagination().GetPageNumber())
		total                               int64
	)
	defer func() {
		fetchAppResponse.Msg.Pagination.PageSize = pageSize
		fetchAppResponse.Msg.Pagination.CurrentPage = pageNumber
		fetchAppResponse.Msg.Pagination.Total = total
		fetchAppResponse.Msg.Pagination.TotalPages = rpcutils.ToTotalPage(pageSize, total)
	}()

	listIdApp := make([]algouid.UUID, 0, len(requestFetchApp.ListIdApp))
	for _, idAuthApp := range requestFetchApp.GetListIdApp() {
		idApp, err := algouid.FromHex(idAuthApp)
		if err != nil {
			fetchAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return fetchAppResponse, errors.WithStack(err)
		}
		listIdApp = append(listIdApp, idApp)
	}

	params := pdb.OptionParams[cpauthdao.ParamsAppSelect](
		cpauthdao.AppSelectWithListIdApp(listIdApp),
		cpauthdao.AppSelectWithDomainSearch(requestFetchApp.GetDomainSearch()),
		cpauthdao.AppSelectWithAppType(authinfra.ConvertAppTypeEnumToAppTypeDao(requestFetchApp.GetAppType())),
		cpauthdao.AppSelectWithAppCountry(authinfra.ConvertAppCountryDaoToAppCountryEnum(requestFetchApp.GetAppCountry())),
		cpauthdao.AppSelectWithNameSearch(requestFetchApp.GetNameSearch()),
		cpauthdao.AppSelectWithPagination(offset, limit),
		cpauthdao.AppSelectWithActiveOrDeactivate(requestFetchApp.GetState().GetIsActive(), requestFetchApp.GetState().GetIsDeactivate()),
		cpauthdao.AppSelectOrderByActiveState(),
	)

	var (
		listInternalAppModels   []*authv1.InternalAppModel
		listBackofficeAppModels []*authv1.BackofficeAppModel
	)
	listBackofficeAppModels, total, err = a.infra.CommonFunction().FetchApp(ctx, params)
	if err != nil {
		fetchAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return fetchAppResponse, errors.WithStack(err)
	}
	for _, v := range listBackofficeAppModels {
		listInternalAppModels = append(listInternalAppModels, &authv1.InternalAppModel{
			IdApp:          v.IdApp,
			Name:           v.Name,
			Domain:         v.Domain,
			AppType:        v.AppType,
			ShortPublicKey: v.ShortPublicKey,
			TokenTtl:       v.TokenTtl,
			IsActive:       v.IsActive,
			AppCountry:     v.AppCountry,
		})
	}
	fetchAppResponse.Msg.Items = listInternalAppModels
	fetchAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return fetchAppResponse, nil
}

func (a *authInternalService) Login(ctx context.Context, loginRequest *connect.Request[authv1.InternalAuthServiceLoginRequest]) (loginResponse *connect.Response[authv1.InternalAuthServiceLoginResponse], err error) {
	loginResponse = &connect.Response[authv1.InternalAuthServiceLoginResponse]{
		Msg: &authv1.InternalAuthServiceLoginResponse{
			Error: &errmsgv1.ErrorMessage{},
		},
	}

	if err = loginValidateRequest(loginRequest.Msg); err != nil {
		loginResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return loginResponse, errors.WithStack(err)
	}
	const (
		NUMBER_START_LOCK = 10
	)
	// var (
	// 	deviceId      = loginRequest.Msg.GetDeviceId()
	// 	isRequiredOtp = true
	// )

	//if err = a.infra.CommonFunction().GetLockByIp(ctx, loginRequest.Msg.GetIpAddress(), NUMBER_START_LOCK); err != nil {
	//	loginResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
	//	return loginResponse, errors.WithStack(err)
	//}

	listAppDao, err := a.infra.AppRepository().Select(ctx, pdb.OptionParams[cpauthdao.ParamsAppSelect](
		cpauthdao.AppSelectWithDomain(loginRequest.Msg.GetDomain()),
	))
	if err != nil {
		loginResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return loginResponse, errors.WithStack(err)
	}
	var (
		currentApp     *cpauthdao.App
		xCountry       = loginRequest.Header().Get("X-App-Country")
		appCountryType = enumtype.AppCountryVN
	)
	if xCountry == "global" {
		appCountryType = enumtype.AppCountryWW
	}
	for i, v := range listAppDao {
		if v.AppCountry == appCountryType {
			currentApp = listAppDao[i]
			break
		}
	}
	if currentApp == nil || currentApp.AppCountry != appCountryType {
		loginResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_USERNAME_OR_PASSWORD_DOES_NOT_MATCH
		return loginResponse, errors.New("app not found")
	}

	listUserDao, err := a.infra.User().UserRepository().Select(ctx, pdb.OptionParams[cpauthdao.ParamsUserSelect](
		cpauthdao.UserSelectWithIdApp(currentApp.IdApp),
		cpauthdao.UserSelectWithEmail(loginRequest.Msg.GetEmail()),
		cpauthdao.UserSelectWithActiveOrDeactivate(true, false),
		cpauthdao.UserSelectWithPagination(0, 1),
	))
	if err != nil {
		loginResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_USERNAME_OR_PASSWORD_DOES_NOT_MATCH
		return loginResponse, errors.WithStack(err)
	}
	if len(listUserDao) == 0 {
		loginResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_USERNAME_OR_PASSWORD_DOES_NOT_MATCH
		return loginResponse, errors.WithStack(err)
	}

	currentUser := listUserDao[0]
	ok, err := a.infra.CommonFunction().VerifyPasswordAndSalt(loginRequest.Msg.GetPassword(), currentUser.HashPassword, currentUser.Salt)
	if err != nil {
		loginResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_USERNAME_OR_PASSWORD_DOES_NOT_MATCH
		return loginResponse, errors.WithStack(err)
	}
	if !ok {
		loginResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_USERNAME_OR_PASSWORD_DOES_NOT_MATCH
		return loginResponse, errors.New("username or password not match")
	}

	listUserDetailDao, err := a.infra.User().UserDetailRepository().Select(ctx, pdb.OptionParams[cpauthdao.ParamsUserDetailSelect](
		cpauthdao.UserDetailSelectWithIdUser(currentUser.IdUser),
		cpauthdao.UserDetailSelectWithActiveOrDeactivate(true, false),
		cpauthdao.UserDetailSelectWithPagination(0, 1),
	))
	if err != nil {
		loginResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_USERNAME_OR_PASSWORD_DOES_NOT_MATCH
		return loginResponse, errors.WithStack(err)
	}
	if len(listUserDetailDao) == 0 {
		loginResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_USERNAME_OR_PASSWORD_DOES_NOT_MATCH
		return loginResponse, errors.WithStack(err)
	}
	// listRefreshTokenDao, err := a.infra.DeviceRepository().Select(ctx, pdb.OptionParams[cpauthdao.ParamsDeviceSelect](
	// 	cpauthdao.DeviceSelectWithDeviceId(deviceId),
	// 	cpauthdao.DeviceSelectWithIdAuthApp(currentApp.IdApp),
	// 	cpauthdao.DeviceSelectWithIdAuthUser(currentUser.IdUser),
	// 	cpauthdao.DeviceSelectWithActiveOrDeactivate(true, false),
	// 	cpauthdao.DeviceSelectWithPagination(0, 1),
	// ))
	// if err == nil {
	// 	if len(listRefreshTokenDao) > 0 && time.Now().Unix()-listRefreshTokenDao[0].LastUsedAt <= 30*24*60*60 {
	// 		isRequiredOtp = false
	// 	}
	// }
	// if isRequiredOtp {
	// 	status, err := a.handlerLoginOtp(ctx, currentApp, listUserDetailDao[0], loginRequest)
	// 	if err != nil || status != errmsgv1.ErrorCode_ERROR_CODE_SUCCESS {
	// 		loginResponse.Msg.Error.Code = status
	// 		return loginResponse, err
	// 	}
	// }

	dbIpResponse, err := a.infra.ExternalService().MiscDBIPClient().IPInfo(ctx, connect.NewRequest(&dbipv1.PublicDBIPServiceIPInfoRequest{
		IpAddr: loginRequest.Msg.GetIpAddress(),
	}))
	if err != nil {
		loginResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return loginResponse, errors.WithStack(err)
	}
	userContext := &usercontext.UserContext{
		IdAuthApp:  currentUser.IdApp.Hex(),
		IdAuthUser: currentUser.IdUser.Hex(),
		IdAuthRole: currentUser.IdRole.Hex(),
		UserAgent:  loginRequest.Msg.GetUserAgent(),
		UserIP:     loginRequest.Msg.GetIpAddress(),
		Domain:     currentApp.Domain,
		Latitude:   dbIpResponse.Msg.GetIpInfo().GetGeoLocation().GetLat(),
		Longitude:  dbIpResponse.Msg.GetIpInfo().GetGeoLocation().GetLong(),
	}

	fmt.Println("userContext login", userContext)
	token, err := a.infra.SessionManager().GenerateToken(ctx, currentUser.IdApp.Hex(), userContext)
	if err != nil {
		loginResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return loginResponse, errors.WithStack(err)
	}
	refreshToken, err := a.infra.CommonFunction().GenerateRefreshToken(ctx, currentUser.IdApp.Hex(), loginRequest.Msg.GetDeviceId(), userContext)
	if err != nil {
		loginResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return loginResponse, errors.WithStack(err)
	}

	err = a.infra.SessionManager().ResetCountByIp(ctx, loginRequest.Msg.GetIpAddress())
	loginResponse.Msg.RefreshToken = refreshToken
	loginResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	loginResponse.Msg.Token = token
	return loginResponse, nil
}
func (a *authInternalService) getSecretTotpByUser(ctx context.Context, userId algouid.UUID) (res *totpredis.TotpInitialRedisValue, err error) {
	listTotpDao, err := a.infra.User().UserTotpRepository().Select(ctx, pdb.OptionParams[cpauthdao.ParamsUserTotpSelect](
		cpauthdao.UserTotpSelectWithIdUserTotp(userId),
		cpauthdao.UserTotpSelectWithActiveOrDeactivate(true, false),
		cpauthdao.UserTotpSelectWithPagination(0, 1),
	))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(listTotpDao) > 0 {
		res = &totpredis.TotpInitialRedisValue{
			SecretKey: listTotpDao[0].Secret,
		}
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}
	return res, nil
}
func (a *authInternalService) Logout(ctx context.Context, logoutRequest *connect.Request[authv1.InternalAuthServiceLogoutRequest]) (logoutResponse *connect.Response[authv1.InternalAuthServiceLogoutResponse], err error) {
	logoutResponse = &connect.Response[authv1.InternalAuthServiceLogoutResponse]{
		Msg: &authv1.InternalAuthServiceLogoutResponse{
			Error: &errmsgv1.ErrorMessage{},
		},
	}
	requestLogout := logoutRequest.Msg
	userCtx, err := a.infra.SessionManager().Verify(ctx, requestLogout.GetToken())
	if err != nil {
		logoutResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return logoutResponse, errors.WithStack(err)
	}
	if err := a.infra.SessionManager().RemoveToken(ctx, userCtx.IdAuthUser); err != nil {
		logoutResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return logoutResponse, errors.WithStack(err)
	}
	logoutResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return logoutResponse, nil
}

func (a *authInternalService) Verify(ctx context.Context, verifyRequest *connect.Request[authv1.InternalAuthServiceVerifyRequest]) (verifyResponse *connect.Response[authv1.InternalAuthServiceVerifyResponse], err error) {
	verifyResponse = &connect.Response[authv1.InternalAuthServiceVerifyResponse]{
		Msg: &authv1.InternalAuthServiceVerifyResponse{
			User:  &authv1.InternalUserModel{},
			Error: &errmsgv1.ErrorMessage{},
		},
	}
	// TODO : remove fmt.Println
	xRealHost := verifyRequest.Header().Get("X-Real-Host")
	fmt.Println("X-Real-Host", xRealHost)
	fmt.Println("X-Real-IP", verifyRequest.Header().Get("X-Real-IP"))
	fmt.Println("Path", verifyRequest.Msg.GetAbsolutePath())
	// cheat code role_public
	granted, _ := a.infra.PolicyManager().IsAllow(ctx, authinfra.IdRoleBackOfficePublic, verifyRequest.Msg.GetAbsolutePath())
	if granted {
		verifyResponse.Msg.User = &authv1.InternalUserModel{
			IdApp:  "",
			IdUser: "",
			IdRole: authinfra.IdRoleBackOfficePublic,
			UserIp: "",
		}
		verifyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
		return verifyResponse, nil
	}
	userCtx, err := a.infra.SessionManager().Verify(ctx, verifyRequest.Msg.GetToken())
	if err != nil {
		verifyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_UNAUTHORIZED
		return verifyResponse, errors.WithStack(err)
	}
	if userCtx == nil {
		verifyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_UNAUTHORIZED
		return verifyResponse, errors.New("user context is nil")
	}
	granted, err = a.infra.PolicyManager().IsAllow(ctx, userCtx.IdAuthRole, verifyRequest.Msg.GetAbsolutePath())
	if err != nil {
		verifyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_UNAUTHORIZED
		return verifyResponse, errors.WithStack(err)
	}
	if !granted {
		verifyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_ABORTED
		return verifyResponse, errors.New("user not granted")
	}

	if userCtx.IdAuthRole != authinfra.IdRoleMerchantAdmin {
		if userCtx.Domain != xRealHost {
			verifyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_UNAUTHORIZED
			return verifyResponse, errors.New("block user cross domain")
		}
	}
	verifyResponse.Msg.User = &authv1.InternalUserModel{
		IdApp:  userCtx.IdAuthApp,
		IdUser: userCtx.IdAuthUser,
		IdRole: userCtx.IdAuthRole,
		UserIp: userCtx.UserIP,
		Lat:    userCtx.Latitude,
		Lng:    userCtx.Longitude,
	}
	verifyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return verifyResponse, nil
}

func (a *authInternalService) SendMailHtml(ctx context.Context, c *connect.Request[authv1.InternalAuthServiceSendMailHtmlRequest]) (res *connect.Response[authv1.InternalAuthServiceSendMailHtmlResponse], err error) {
	res = connect.NewResponse[authv1.InternalAuthServiceSendMailHtmlResponse](&authv1.InternalAuthServiceSendMailHtmlResponse{
		Error: &errmsgv1.ErrorMessage{},
	})

	listConfigEmailFetch, err := a.infra.ConfigMailRepository().Select(ctx, pdb.OptionParams[cpauthdao.ParamsConfigMailSelect](
		cpauthdao.ConfigMailSelectWithActiveOrDeactivate(true, false),
		cpauthdao.ConfigMailSelectWithIdAuthApp(algouid.FromHexOrNil(c.Msg.IdApp)),
	))
	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}
	if len(listConfigEmailFetch) == 0 {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.New("config send email not exist")
	}

	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}
	configSmtp := listConfigEmailFetch[0]

	message := fmt.Sprintf(
		"From: %s\n"+
			"To: %s\n"+
			"Subject: %s\n"+
			"Content-Type: multipart/alternative; boundary=\"boundary\"\n\n--boundary\n"+
			"Content-Type: text/html; charset=UTF-8\n\n%s\n\n--boundary--",
		configSmtp.SenderEmail, strings.Join(c.Msg.To, ","), c.Msg.GetSubject(), c.Msg.GetBody(),
	)
	auth := smtp.PlainAuth("", configSmtp.AuthUsername, configSmtp.AuthPassword, configSmtp.SmtpServerAddress)
	addr := configSmtp.SmtpServerAddress + ":" + strconv.FormatUint(configSmtp.SmtpServerPort, 10)
	err = smtp.SendMail(addr, auth, configSmtp.SenderEmail, c.Msg.To, []byte(message))
	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}
	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return res, nil
}

func (a *authInternalService) handlerLoginOtp(ctx context.Context, currentApp *cpauthdao.App, currentUser *cpauthdao.UserDetail, loginRequest *connect.Request[authv1.InternalAuthServiceLoginRequest]) (res errmsgv1.ErrorCode, err error) {
	res = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	secretTotp, err := a.getSecretTotpByUser(ctx, currentUser.IdUser)
	if err != nil {
		res = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}
	if secretTotp != nil && secretTotp.SecretKey != "" {
		if loginRequest.Msg.GetOtp() == "" {
			res = errmsgv1.ErrorCode_ERROR_CODE_AUTH_TOTP_REQUIRED
			return res, errors.WithStack(err)
		}
		if !totp.Validate(loginRequest.Msg.GetOtp(), secretTotp.SecretKey) {
			res = errmsgv1.ErrorCode_ERROR_CODE_AUTH_TOTP_VERIFY_INCORRECT
			return res, errors.WithStack(err)
		}
	} else {
		if loginRequest.Msg.GetOtp() == "" {
			_, duration, err := a.infra.SessionManager().GetOtp(ctx, sessionredis.PATH_OTP, currentUser.IdUser.Hex(), loginRequest.Msg.GetDeviceId())
			if err == nil && duration.Minutes() > 8 {
				res = errmsgv1.ErrorCode_ERROR_CODE_AUTH_OTP_REQUIRED
				return res, errors.WithStack(err)
			}
			otpInital, err := a.infra.SessionManager().InitOtp(ctx, sessionredis.PATH_OTP, currentUser.IdUser.Hex(), loginRequest.Msg.GetDeviceId())
			if err != nil {
				res = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
				return res, errors.WithStack(err)
			}
			fmt.Println(otpInital)
			res = errmsgv1.ErrorCode_ERROR_CODE_AUTH_OTP_REQUIRED
			fmt.Println("Hard code")
			payloadSendMailOtp := &sendemailotp.Payload{
				To:        loginRequest.Msg.GetEmail(),
				OTP:       otpInital,
				IdAuthApp: currentApp.IdApp,
				Subject:   "Mã xác nhận đăng nhập",
				AppName:   currentApp.Name,
				UserName:  currentUser.FirstName + " " + currentUser.LastName,
			}
			task, err := sendemailotp.NewSendEmailTask(payloadSendMailOtp)
			if err != nil {
				res = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
				return res, errors.WithStack(err)
			}
			_, err = a.infra.AsynqClient().Enqueue(task)
			if err != nil {
				a.infra.Logger().Error("could not enqueue task: " + err.Error())
				res = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
				return res, errors.WithStack(err)
			}
			res = errmsgv1.ErrorCode_ERROR_CODE_AUTH_OTP_REQUIRED
			return res, nil
		}
		otpVerify, _, err := a.infra.SessionManager().GetOtp(ctx, sessionredis.PATH_OTP, currentUser.IdUser.Hex(), loginRequest.Msg.GetDeviceId())
		if err != nil || otpVerify != loginRequest.Msg.GetOtp() {
			res = errmsgv1.ErrorCode_ERROR_CODE_AUTH_OTP_VERIFY_INCORRECT
			return res, errors.WithStack(err)
		}
		err = a.infra.SessionManager().RemoveOtp(ctx, sessionredis.PATH_OTP, currentUser.IdUser.Hex(), loginRequest.Msg.GetDeviceId())
		if err != nil {
			res = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return res, errors.WithStack(err)
		}
	}
	return res, err

}
