package internalv1svc

import (
	authv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1"
	"git.tmproxy-infra.com/algo/authsvc/internal/autherr"
	"html"
	"strings"
)

func createAppMerchantValidateRequest(request *authv1.InternalAuthServiceCreateAppMerchantRequest) (err error) {
	switch {
	case len(request.GetIdApp()) == 0:
		return autherr.IdAppRequiredError
	case len(request.GetName()) == 0:
		return autherr.NameRequiredError
	case len(request.GetDomain()) == 0:
		return autherr.DomainRequiredError
	}
	return nil
}
func loginValidateRequest(request *authv1.InternalAuthServiceLoginRequest) (err error) {

	request.Email = strings.TrimSpace(html.EscapeString(request.GetEmail()))
	request.Domain = strings.TrimSpace(html.EscapeString(request.GetDomain()))

	request.IpAddress = strings.TrimSpace(html.EscapeString(request.GetIpAddress()))
	request.UserAgent = strings.TrimSpace(html.EscapeString(request.GetUserAgent()))
	switch {
	case len(request.GetDomain()) == 0:
		return autherr.DomainRequiredError
	case len(request.GetEmail()) == 0:
		return autherr.EmailRequiredError
	case len(request.GetPassword()) == 0:
		return autherr.PasswordRequiredError
	case len(request.GetIpAddress()) == 0:
		return autherr.IpAddressRequiredError
	case len(request.GetUserAgent()) == 0:
		return autherr.UserAgentRequiredError
	}
	return nil
}

func refreshTokenValidateRequest(request *authv1.InternalAuthServiceRefreshTokenRequest) (err error) {

	switch {
	case len(request.GetRefreshToken()) == 0:
		return autherr.RefreshTokenRequiredError
	}
	return nil
}

func forgotPasswordValidateRequest(request *authv1.InternalAuthServiceForgotPasswordRequest) (err error) {
	switch {
	case len(request.GetEmail()) == 0:
		return autherr.EmailRequiredError
	}
	return nil
}

func forgotPasswordValidateStepChangePassRequest(request *authv1.InternalAuthServiceForgotPasswordRequest) (err error) {
	switch {
	case len(request.GetOtp()) == 0:
		return autherr.ErrOtpRequired
	case len(request.GetNewPassword()) == 0:
		return autherr.ErrPasswordNewRequired

	}
	return nil
}
