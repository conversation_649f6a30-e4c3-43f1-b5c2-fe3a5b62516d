package backofficev1svc

import (
	"context"
	"database/sql"
	"html"
	"strings"
	"time"

	"aidanwoods.dev/go-paseto"
	"connectrpc.com/connect"
	authv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1"
	"git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1/authv1connect"
	transactionv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/transaction/v1"
	errmsgv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	paymentaddressv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/misc/paymentaddress/v1"
	utilsv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	"git.tmproxy-infra.com/algo/authsvc/internal/authenum"
	"git.tmproxy-infra.com/algo/authsvc/internal/autherr"
	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	authinfra "git.tmproxy-infra.com/algo/authsvc/internal/infra"
	totpredis "git.tmproxy-infra.com/algo/authsvc/internal/inmem/totp"
	"git.tmproxy-infra.com/algo/common/enumtype"
	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"git.tmproxy-infra.com/algo/common/pkg/rpcutils"
	"git.tmproxy-infra.com/algo/common/pkg/usercontext"
	"github.com/pkg/errors"
	"github.com/pquerna/otp/totp"
	"golang.org/x/sync/errgroup"
)

var _ authv1connect.BackofficeAuthServiceHandler = (*authBackofficeServiceImpl)(nil)

type authBackofficeServiceImpl struct {
	infra authinfra.Infra
}

func (a *authBackofficeServiceImpl) ForgotPassword(ctx context.Context, c *connect.Request[authv1.BackofficeAuthServiceForgotPasswordRequest]) (res *connect.Response[authv1.BackofficeAuthServiceForgotPasswordResponse], err error) {
	//TODO implement me
	res = connect.NewResponse[authv1.BackofficeAuthServiceForgotPasswordResponse](
		&authv1.BackofficeAuthServiceForgotPasswordResponse{
			Error: &errmsgv1.ErrorMessage{},
		})
	var (
		ipUser    = c.Header().Get("X-Real-Ip")
		xRealHost = c.Header().Get("X-Real-Host")
	)

	requestInternal := connect.NewRequest[authv1.InternalAuthServiceForgotPasswordRequest](&authv1.InternalAuthServiceForgotPasswordRequest{
		Email:       c.Msg.Email,
		NewPassword: c.Msg.NewPassword,
		IpAddress:   ipUser,
		Otp:         c.Msg.GetOtp(),
		Domain:      xRealHost,
	})
	responseInternal, err := a.infra.GetAuthInternalHandler().ForgotPassword(ctx, requestInternal)
	if err != nil {
		if responseInternal != nil {
			if responseInternal.Msg.Error.Code != errmsgv1.ErrorCode_ERROR_CODE_SUCCESS {
				res.Msg.Error.Code = responseInternal.Msg.Error.Code
				return res, errors.WithStack(err)
			}
		}
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}
	res.Msg.Error.Code = responseInternal.Msg.GetError().Code
	return res, nil
}

func (a *authBackofficeServiceImpl) UpdateRefCode(ctx context.Context, c *connect.Request[authv1.BackofficeAuthServiceUpdateRefCodeRequest]) (response *connect.Response[authv1.BackofficeAuthServiceUpdateRefCodeResponse], err error) {
	response = connect.NewResponse[authv1.BackofficeAuthServiceUpdateRefCodeResponse](
		&authv1.BackofficeAuthServiceUpdateRefCodeResponse{
			Error: &errmsgv1.ErrorMessage{},
		})

	if err = updateRefCodeValidateRequest(c.Msg); err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return response, errors.WithStack(err)
	}

	userCtx := usercontext.FromContext(ctx)
	if userCtx == nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.New("user context is nil")
	}
	idUser, err := algouid.FromHex(c.Msg.GetUserId())
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return response, errors.WithStack(err)
	}

	err = a.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {

		listUser, err := a.infra.User().UserDetailRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsUserDetailSelect](
			authdao.UserDetailSelectWithIdUser(idUser),
			authdao.UserDetailSelectWithActiveOrDeactivate(true, false),
			authdao.UserDetailSelectWithPagination(0, 1),
		))
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listUser) == 0 {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return autherr.ErrUserNotExist
		}
		currentUser := listUser[0]

		listRefExistDao, err := a.infra.User().UserDetailRepository().Count(tranCtx, pdb.OptionParams[authdao.ParamsUserDetailSelect](
			authdao.UserDetailSelectWithRefCode(c.Msg.GetRefCode()),
		))
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if listRefExistDao > 0 {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_REF_EXIST
			return autherr.ErrRefCodeNotExist
		}
		params := pdb.OptionParams[authdao.ParamsUserDetailUpdate](
			authdao.UserDetailUpdateRefCode(currentUser.RefCode, c.Msg.GetRefCode()),
		)
		params.IdUserDetail = currentUser.IdUserDetail
		if err = a.infra.User().UserDetailRepository().Update(tranCtx, params); err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		return nil
	})
	if err != nil {
		return response, errors.WithStack(err)
	}
	response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return response, nil
}

func (a *authBackofficeServiceImpl) RefreshToken(ctx context.Context, c *connect.Request[authv1.BackofficeAuthServiceRefreshTokenRequest]) (res *connect.Response[authv1.BackofficeAuthServiceRefreshTokenResponse], err error) {
	//TODO implement me
	res = connect.NewResponse[authv1.BackofficeAuthServiceRefreshTokenResponse](
		&authv1.BackofficeAuthServiceRefreshTokenResponse{
			Error: &errmsgv1.ErrorMessage{},
		})

	requestInternal := connect.NewRequest[authv1.InternalAuthServiceRefreshTokenRequest](&authv1.InternalAuthServiceRefreshTokenRequest{
		DeviceId:     c.Msg.GetDeviceId(),
		RefreshToken: c.Msg.GetRefreshToken(),
	})
	refreshToken, err := a.infra.GetAuthInternalHandler().RefreshToken(ctx, requestInternal)
	if err != nil {
		res.Msg.Error.Code = refreshToken.Msg.Error.Code
		return res, errors.WithStack(err)
	}
	res.Msg.RefreshToken = refreshToken.Msg.RefreshToken
	res.Msg.Token = refreshToken.Msg.Token
	return res, nil
}

func (a *authBackofficeServiceImpl) ChangePassword(ctx context.Context, request *connect.Request[authv1.BackofficeAuthServiceChangePasswordRequest]) (response *connect.Response[authv1.BackofficeAuthServiceChangePasswordResponse], err error) {
	response = connect.NewResponse[authv1.BackofficeAuthServiceChangePasswordResponse](&authv1.BackofficeAuthServiceChangePasswordResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	userCtx := usercontext.FromContext(ctx)
	if userCtx == nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.New("user context is nil, interceptor has problem")
	}
	idUser, err := algouid.FromHex(userCtx.IdAuthUser)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return response, errors.WithStack(err)
	}

	if err = a.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		listUserDao, err := a.infra.User().UserRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsUserSelect](
			authdao.UserSelectWithIdUser(idUser),
		))
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listUserDao) == 0 {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_USER_NOT_EXIST
			return autherr.ErrUserNotExist
		}
		currentUser := listUserDao[0]
		ok, err := a.infra.CommonFunction().VerifyPasswordAndSalt(request.Msg.GetOldPassword(), currentUser.HashPassword, currentUser.Salt)
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if !ok {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_OLD_PASSWORD_INCORRECT
			return errors.New("old password incorrect")
		}
		hashPassword, salt := a.infra.CommonFunction().GeneratePasswordAndSalt(request.Msg.GetNewPassword())
		paramUserUpdate := pdb.OptionParams[authdao.ParamsUserUpdate](
			authdao.UserUpdateWithIdUser(currentUser.IdUser),
			authdao.UserUpdateHashPassword(currentUser.HashPassword, hashPassword),
			authdao.UserUpdateSalt(currentUser.Salt, salt),
		)
		if err = a.infra.User().UserRepository().Update(tranCtx, paramUserUpdate); err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return response, errors.WithStack(err)
	}
	response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return response, nil
}

func NewBackofficeAuthService(infra authinfra.Infra) authv1connect.BackofficeAuthServiceHandler {
	return &authBackofficeServiceImpl{infra: infra}
}

func (a *authBackofficeServiceImpl) Me(ctx context.Context, _ *connect.Request[authv1.BackofficeAuthServiceMeRequest]) (meResponse *connect.Response[authv1.BackofficeAuthServiceMeResponse], err error) {
	meResponse = connect.NewResponse[authv1.BackofficeAuthServiceMeResponse](&authv1.BackofficeAuthServiceMeResponse{
		Error:      &errmsgv1.ErrorMessage{},
		UserDetail: nil,
	})
	userCtx := usercontext.FromContext(ctx)
	if userCtx == nil {
		meResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return meResponse, errors.New("user context is nil")
	}
	idUser, err := algouid.FromHex(userCtx.IdAuthUser)
	if err != nil {
		meResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return meResponse, errors.WithStack(err)
	}

	var (
		currentUserDetail *authdao.UserDetail
		currentUser       *authdao.User
	)
	errGr, _ := errgroup.WithContext(ctx)
	errGr.Go(func() error {
		listUser, err := a.infra.User().UserRepository().Select(ctx, pdb.OptionParams[authdao.ParamsUserSelect](
			authdao.UserSelectWithIdUser(idUser),
			authdao.UserSelectWithActiveOrDeactivate(true, false),
			authdao.UserSelectWithPagination(0, 1),
		))
		if err != nil {
			meResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listUser) == 0 {
			meResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return autherr.ErrUserNotExist
		}
		currentUser = listUser[0]
		return nil
	})
	errGr.Go(func() error {
		listUserDetailDao, err := a.infra.User().UserDetailRepository().Select(ctx, pdb.OptionParams[authdao.ParamsUserDetailSelect](
			authdao.UserDetailSelectWithIdUser(idUser),
			authdao.UserDetailSelectWithActiveOrDeactivate(true, false),
			authdao.UserDetailSelectWithPagination(0, 1),
		))
		if err != nil {
			meResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listUserDetailDao) == 0 {
			meResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return autherr.ErrUserNotExist
		}
		currentUserDetail = listUserDetailDao[0]
		return nil
	})
	if err = errGr.Wait(); err != nil {
		return meResponse, errors.WithStack(err)
	}
	if currentUserDetail == nil || currentUser == nil {
		meResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return meResponse, errors.New("user detail or user is nil")
	}
	meResponse.Msg.UserDetail = &authv1.BackofficeUserDetail{
		FirstName:   currentUserDetail.FirstName,
		LastName:    currentUserDetail.LastName,
		Email:       currentUser.Email,
		PhoneNumber: currentUserDetail.PhoneNumber,
		IdCity:      currentUserDetail.IdCity.V,
		IdState:     currentUserDetail.IdState.V,
		IdCountry:   currentUserDetail.IdCountry.V,
		IdCompany:   currentUserDetail.IdCompany.String(),
		Street:      currentUserDetail.Street,
		RefCode:     currentUserDetail.RefCode,
		UserRefId:   currentUserDetail.UserRefId.String(),
	}
	listPathOfRole, err := a.infra.PolicyManager().GetAllPathOfRole(ctx, userCtx.IdAuthRole)
	if err != nil {
		meResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return meResponse, errors.WithStack(err)
	}

	meResponse.Msg.Paths = listPathOfRole
	meResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return meResponse, nil
}

func (a *authBackofficeServiceImpl) ReloadEnforcer(ctx context.Context, _ *connect.Request[authv1.BackofficeAuthServiceReloadEnforcerRequest]) (reloadEnforcerResponse *connect.Response[authv1.BackofficeAuthServiceReloadEnforcerResponse], err error) {
	reloadEnforcerResponse = connect.NewResponse[authv1.BackofficeAuthServiceReloadEnforcerResponse](
		&authv1.BackofficeAuthServiceReloadEnforcerResponse{
			Error: &errmsgv1.ErrorMessage{},
		})
	if err = a.infra.ReloadCachePolicy(ctx); err != nil {
		reloadEnforcerResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return reloadEnforcerResponse, errors.WithStack(err)
	}

	reloadEnforcerResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return reloadEnforcerResponse, nil
}

func (a *authBackofficeServiceImpl) Login(ctx context.Context, loginRequest *connect.Request[authv1.BackofficeAuthServiceLoginRequest]) (loginResponse *connect.Response[authv1.BackofficeAuthServiceLoginResponse], err error) {
	loginResponse = connect.NewResponse[authv1.BackofficeAuthServiceLoginResponse](
		&authv1.BackofficeAuthServiceLoginResponse{
			Error: &errmsgv1.ErrorMessage{},
			Token: "",
		})
	//origin    = request.Header().Get("app_name")
	// TODO: remove this on production
	//loginRequest.Header().Set("Origin", "https://backoffice.algoproxy.app")
	var (
		xRealHost = loginRequest.Header().Get("X-Real-Host")
		ua        = loginRequest.Header().Get("User-Agent")
		ipUser    = loginRequest.Header().Get("X-Real-Ip")
	)

	loginRequestInternal := connect.NewRequest[authv1.InternalAuthServiceLoginRequest](&authv1.InternalAuthServiceLoginRequest{
		Domain:    xRealHost,
		Email:     loginRequest.Msg.Email,
		Password:  loginRequest.Msg.Password,
		UserAgent: ua,
		IpAddress: ipUser,
		Otp:       loginRequest.Msg.GetOtp(),
		DeviceId:  loginRequest.Msg.GetDeviceId(),
	})
	loginRequestInternal.Header().Set("X-App-Country", loginRequest.Header().Get("X-App-Country"))
	// call truc tiep nen co error tra ve
	loginResponseInternal, err := a.infra.GetAuthInternalHandler().Login(ctx, loginRequestInternal)
	if err != nil {
		if loginResponseInternal != nil {
			if loginResponseInternal.Msg.Error.Code != errmsgv1.ErrorCode_ERROR_CODE_SUCCESS {
				loginResponse.Msg.Error.Code = loginResponseInternal.Msg.Error.Code
				return loginResponse, errors.WithStack(err)
			}
		}
		loginResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return loginResponse, errors.WithStack(err)
	}
	loginResponse.Msg.Token = loginResponseInternal.Msg.GetToken()
	loginResponse.Msg.RefreshToken = loginResponseInternal.Msg.GetRefreshToken()
	loginResponse.Msg.Error.Code = loginResponseInternal.Msg.GetError().Code
	return loginResponse, nil
}
func (a *authBackofficeServiceImpl) CreateApp(ctx context.Context, createAppRequest *connect.Request[authv1.BackofficeAuthServiceCreateAppRequest]) (createAppResponse *connect.Response[authv1.BackofficeAuthServiceCreateAppResponse], err error) {
	createAppResponse = connect.NewResponse[authv1.BackofficeAuthServiceCreateAppResponse](&authv1.BackofficeAuthServiceCreateAppResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	appNameEscaped := html.EscapeString(createAppRequest.Msg.GetName())
	if err = createAppValidateRequest(createAppRequest.Msg); err != nil {
		createAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return createAppResponse, errors.WithStack(err)
	}
	if err = a.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		listAppExistDao, err := a.infra.AppRepository().Count(tranCtx, pdb.OptionParams[authdao.ParamsAppSelect](
			authdao.AppSelectWithName(appNameEscaped),
		))
		if err != nil {
			createAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if listAppExistDao > 0 {
			createAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_APP_EXIST
			return errors.New("app exist")
		}
		tnow := time.Now().Unix()
		privateKey := paseto.NewV4AsymmetricSecretKey()
		newApp := &authdao.App{
			Name:           appNameEscaped,
			TokenTTL:       int64(createAppRequest.Msg.GetTokenTtl()),
			AppType:        authinfra.ConvertAppTypeEnumToAppTypeDao(createAppRequest.Msg.GetAppType()),
			AppCountry:     authinfra.ConvertAppCountryDaoToAppCountryEnum(createAppRequest.Msg.GetAppCountry()),
			PrivateEd25519: privateKey.ExportHex(),
			PublicEd25519:  privateKey.Public().ExportHex(),
			AtUnix: pdb.AtUnix{
				CreatedAt: tnow,
				UpdatedAt: tnow,
				DeletedAt: 0,
			},
		}
		newApp.IdApp, err = algouid.NewUUID()
		if err != nil {
			createAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}

		if err = a.infra.AppRepository().Create(tranCtx, newApp); err != nil {
			return errors.WithStack(err)
		}
		if err = a.infra.SessionManager().SetPasetoConfig(tranCtx, newApp.IdApp.Hex(), newApp.PublicEd25519, newApp.PrivateEd25519, newApp.Name, newApp.TokenTTL); err != nil {
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return createAppResponse, errors.WithStack(err)
	}
	createAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return createAppResponse, nil
}

func (a *authBackofficeServiceImpl) FetchApp(ctx context.Context, fetchAppRequest *connect.Request[authv1.BackofficeAuthServiceFetchAppRequest]) (fetchAppResponse *connect.Response[authv1.BackofficeAuthServiceFetchAppResponse], err error) {
	fetchAppResponse = connect.NewResponse[authv1.BackofficeAuthServiceFetchAppResponse](&authv1.BackofficeAuthServiceFetchAppResponse{
		Items: make([]*authv1.BackofficeAppModel, 0),
		Error: &errmsgv1.ErrorMessage{},
		Pagination: &utilsv1.PaginationResponse{
			CurrentPage: 0,
			PageSize:    0,
			Total:       0,
			TotalPages:  0,
		},
	})
	var (
		requestFetchApp                     = fetchAppRequest.Msg
		pageSize, pageNumber, offset, limit = rpcutils.ToOffsetLimit(requestFetchApp.GetPagination().GetPageSize(), requestFetchApp.GetPagination().GetPageNumber())
		total                               int64
	)
	defer func() {
		fetchAppResponse.Msg.Pagination.PageSize = pageSize
		fetchAppResponse.Msg.Pagination.CurrentPage = pageNumber
		fetchAppResponse.Msg.Pagination.Total = total
		fetchAppResponse.Msg.Pagination.TotalPages = rpcutils.ToTotalPage(pageSize, total)
	}()
	nameSearchEscape := html.EscapeString(requestFetchApp.GetNameSearch())

	params := pdb.OptionParams[authdao.ParamsAppSelect](
		authdao.AppSelectWithIdApp(algouid.FromHexOrNil(requestFetchApp.GetIdApp())),
		authdao.AppSelectWithAppType(authinfra.ConvertAppTypeEnumToAppTypeDao(requestFetchApp.GetAppType())),
		authdao.AppSelectWithNameSearch(nameSearchEscape),
		authdao.AppSelectWithAppCountry(authinfra.ConvertAppCountryDaoToAppCountryEnum(requestFetchApp.GetAppCountry())),
		authdao.AppSelectWithPagination(offset, limit),
		authdao.AppSelectWithActiveOrDeactivate(requestFetchApp.GetState().GetIsActive(), requestFetchApp.GetState().GetIsDeactivate()),
		authdao.AppSelectOrderByActiveState(),
	)

	var (
		listAppModels []*authv1.BackofficeAppModel
	)
	listAppModels, total, err = a.infra.CommonFunction().FetchApp(ctx, params)
	if err != nil {
		fetchAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return fetchAppResponse, errors.WithStack(err)
	}
	fetchAppResponse.Msg.Items = listAppModels
	fetchAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return fetchAppResponse, nil
}

func (a *authBackofficeServiceImpl) UpdateApp(ctx context.Context, updateAppRequest *connect.Request[authv1.BackofficeAuthServiceUpdateAppRequest]) (updateAppResponse *connect.Response[authv1.BackofficeAuthServiceUpdateAppResponse], err error) {
	updateAppResponse = connect.NewResponse[authv1.BackofficeAuthServiceUpdateAppResponse](&authv1.BackofficeAuthServiceUpdateAppResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	if err = updateAppValidateRequest(updateAppRequest.Msg); err != nil {
		updateAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		updateAppResponse.Msg.Error.Message = err.Error()
		return updateAppResponse, errors.WithStack(err)
	}

	idAuthApp, err := algouid.FromHex(updateAppRequest.Msg.GetIdApp())

	if err != nil {
		updateAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return updateAppResponse, errors.WithStack(err)
	}
	nameEscape := html.EscapeString(updateAppRequest.Msg.GetName())

	if err = a.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		listAppDao, err := a.infra.AppRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsAppSelect](
			authdao.AppSelectWithIdApp(idAuthApp),
		))
		if err != nil {
			updateAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listAppDao) == 0 {
			updateAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_APP_NOT_EXIST
			return autherr.ErrAppNotExist
		}
		currentApp := listAppDao[0]

		updateParams := pdb.OptionParams[authdao.ParamsAppUpdate](
			authdao.AppUpdateWithIdApp(currentApp.IdApp),
			authdao.AppUpdateName(currentApp.Name, nameEscape),
			authdao.AppUpdateAppType(currentApp.AppType, authinfra.ConvertAppTypeEnumToAppTypeDao(updateAppRequest.Msg.GetAppType())),
			authdao.AppUpdateAppCountry(currentApp.AppCountry, authinfra.ConvertAppCountryDaoToAppCountryEnum(updateAppRequest.Msg.GetAppCountry())),
			authdao.AppUpdateTokenTtl(currentApp.TokenTTL, int64(updateAppRequest.Msg.GetTokenTtl())),
			authdao.AppUpdateActiveState(currentApp.IsActive(), updateAppRequest.Msg.State.GetIsActive()),
		)
		if updateParams.AppType.Valid {
			updateAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
			updateAppResponse.Msg.Error.Message = autherr.AppTypeNotSupportError.Error()
			return autherr.AppTypeNotSupportError
		}

		if updateAppRequest.Msg.GetIsChangePasetoKey() {
			privateKey := paseto.NewV4AsymmetricSecretKey()
			authdao.AppUpdateKeyPair(currentApp.PrivateEd25519, currentApp.PublicEd25519, privateKey.ExportHex(), privateKey.Public().ExportHex())(updateParams)
		}
		if !updateParams.IsChange {
			updateAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
			return nil
		}
		// check exist
		if updateParams.Name.Valid {
			listAppNameExist, err := a.infra.AppRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsAppSelect](
				authdao.AppSelectWithName(nameEscape),
			))
			if err != nil {
				updateAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
				return errors.WithStack(err)
			}
			if len(listAppNameExist) > 0 {
				updateAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_APP_EXIST
				return autherr.ErrAppExist
			}
		}

		if a.infra.AppRepository().Update(tranCtx, updateParams) != nil {
			return errors.WithStack(err)
		}
		appAfterUpdate, err := a.infra.AppRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsAppSelect](
			authdao.AppSelectWithIdApp(currentApp.IdApp)))
		if err != nil {
			updateAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(appAfterUpdate) == 0 {
			updateAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.New("app update fail")
		}
		currentAppAfterUpdate := appAfterUpdate[0]
		if err = a.infra.SessionManager().SetPasetoConfig(tranCtx, currentAppAfterUpdate.IdApp.Hex(), currentAppAfterUpdate.PublicEd25519, currentAppAfterUpdate.PrivateEd25519, currentAppAfterUpdate.Name, currentAppAfterUpdate.TokenTTL); err != nil {
			updateAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return updateAppResponse, errors.WithStack(err)
	}
	updateAppResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return updateAppResponse, nil
}

func (a *authBackofficeServiceImpl) FetchUser(ctx context.Context, fetchUserRequest *connect.Request[authv1.BackofficeAuthServiceFetchUserRequest]) (fetchUserResponse *connect.Response[authv1.BackofficeAuthServiceFetchUserResponse], err error) {
	fetchUserResponse = connect.NewResponse[authv1.BackofficeAuthServiceFetchUserResponse](&authv1.BackofficeAuthServiceFetchUserResponse{
		Error:      &errmsgv1.ErrorMessage{},
		Pagination: &utilsv1.PaginationResponse{},
		Items:      make([]*authv1.BackofficeUserModel, 0),
	})
	var (
		requestFetchUser                    = fetchUserRequest.Msg
		pageSize, pageNumber, offset, limit = rpcutils.ToOffsetLimit(requestFetchUser.GetPagination().GetPageSize(), requestFetchUser.GetPagination().GetPageNumber())
		total                               int64
	)
	defer func() {
		fetchUserResponse.Msg.Pagination.PageSize = pageSize
		fetchUserResponse.Msg.Pagination.CurrentPage = pageNumber
		fetchUserResponse.Msg.Pagination.Total = total
		fetchUserResponse.Msg.Pagination.TotalPages = total / pageSize
		if fetchUserResponse.Msg.Pagination.TotalPages == 0 && total > 0 {
			fetchUserResponse.Msg.Pagination.TotalPages = 1
		}
	}()

	var (
		searchEmailEscape = html.EscapeString(requestFetchUser.GetEmailSearch())
	)

	params := pdb.OptionParams[authdao.ParamsUserSelect](
		authdao.UserSelectWithIdApp(algouid.FromHexOrNil(requestFetchUser.GetIdApp())),
		authdao.UserSelectWithIdUser(algouid.FromHexOrNil(requestFetchUser.GetIdUser())),
		authdao.UserSelectWithIdRole(algouid.FromHexOrNil(requestFetchUser.GetIdRole())),
		authdao.UserSelectWithEmailSearch(searchEmailEscape),
		authdao.UserSelectWithActiveOrDeactivate(requestFetchUser.GetState().GetIsActive(), requestFetchUser.GetState().GetIsDeactivate()),
		authdao.UserSelectWithPagination(offset, limit),
	)
	total, err = a.infra.User().UserRepository().Count(ctx, params)
	if err != nil {
		fetchUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return fetchUserResponse, errors.WithStack(err)
	}
	if total == 0 {
		fetchUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
		return fetchUserResponse, nil
	}
	listUserDao, err := a.infra.User().UserRepository().Select(ctx, params)
	if err != nil {
		fetchUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return fetchUserResponse, errors.WithStack(err)
	}
	var (
		listUserModels        = make([]*authv1.BackofficeUserModel, 0, len(listUserDao))
		mapIdRoleOfUser       = make(map[algouid.UUID]struct{})
		mapIdAppOfUser        = make(map[algouid.UUID]struct{})
		mapIdUserDetailOfUser = make(map[algouid.UUID]struct{})
	)
	for _, v := range listUserDao {
		mapIdRoleOfUser[v.IdRole] = struct{}{}
		mapIdAppOfUser[v.IdApp] = struct{}{}
		mapIdUserDetailOfUser[v.IdUser] = struct{}{}
	}

	roleBundle, err := a.infra.BundleLoader().LoadBundleRole(ctx, a.infra.RoleRepository(), mapIdRoleOfUser)
	if err != nil {
		fetchUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return fetchUserResponse, errors.WithStack(err)
	}
	appBundle, err := a.infra.BundleLoader().LoadBundleApp(ctx, a.infra.AppRepository(), mapIdAppOfUser)
	if err != nil {
		fetchUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return fetchUserResponse, errors.WithStack(err)
	}

	userDetailBundle, err := a.infra.BundleLoader().LoadBundleUserDetail(ctx, a.infra.User().UserDetailRepository(), mapIdUserDetailOfUser)
	if err != nil {
		fetchUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return fetchUserResponse, errors.WithStack(err)
	}

	for _, v := range listUserDao {
		roleOfUser, ok := roleBundle[v.IdRole]
		if !ok {
			fetchUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return fetchUserResponse, errors.New("role_bundle error")
		}
		appOfUser, ok := appBundle[v.IdApp]
		if !ok {
			fetchUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return fetchUserResponse, errors.New("app_bundle error")
		}

		detailOfUser, ok := userDetailBundle[v.IdUser]
		if !ok {
			fetchUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return fetchUserResponse, errors.New("detail_bundle error")
		}

		listUserModels = append(listUserModels, &authv1.BackofficeUserModel{
			IdUser: v.IdUser.Hex(),
			Email:  v.Email,
			App: &authv1.AuthUserApp{
				IdApp:    appOfUser.IdApp.Hex(),
				Name:     appOfUser.Name,
				IsActive: appOfUser.IsActive(),
			},
			Role: &authv1.BackofficeUserRoleModel{
				IdRole:   roleOfUser.IdRole.Hex(),
				Name:     roleOfUser.RoleName,
				IsActive: roleOfUser.IsActive(),
			},
			IsEnableTotp: false,
			IsActive:     appOfUser.IsActive() && roleOfUser.IsActive() && v.IsActive(),
			UserDetails: &authv1.BackofficeUserDetail{
				FirstName:   detailOfUser.FirstName,
				LastName:    detailOfUser.LastName,
				PhoneNumber: detailOfUser.PhoneNumber,
				RefCode:     detailOfUser.RefCode,
				UserRefId:   detailOfUser.UserRefId.Hex(),
				Street:      detailOfUser.Street,
				IdCity:      detailOfUser.IdCity.V,
				IdCountry:   detailOfUser.IdCountry.V,
				IdCompany:   detailOfUser.IdCompany.Hex(),
				IdState:     detailOfUser.IdState.V,
			},
		})
	}
	fetchUserResponse.Msg.Items = listUserModels
	fetchUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return fetchUserResponse, nil
}

func (a *authBackofficeServiceImpl) CreateUser(ctx context.Context, createUserRequest *connect.Request[authv1.BackofficeAuthServiceCreateUserRequest]) (createUserResponse *connect.Response[authv1.BackofficeAuthServiceCreateUserResponse], err error) {
	createUserResponse = connect.NewResponse[authv1.BackofficeAuthServiceCreateUserResponse](&authv1.BackofficeAuthServiceCreateUserResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	if err = createUserValidateRequest(createUserRequest.Msg); err != nil {
		createUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		createUserResponse.Msg.Error.Message = err.Error()
		return createUserResponse, errors.WithStack(err)
	}

	var (
		requestCreateUser = createUserRequest.Msg
		tnowUnix          = time.Now().Unix()
	)

	appUUID, err := algouid.FromHex(requestCreateUser.GetIdApp())
	if err != nil {
		createUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return createUserResponse, errors.WithStack(err)
	}
	roleUUID, err := algouid.FromHex(requestCreateUser.GetIdRole())
	if err != nil {
		createUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return createUserResponse, errors.WithStack(err)
	}
	if appUUID.IsNil() || roleUUID.IsNil() {
		createUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return createUserResponse, errors.WithStack(err)
	}

	if err = a.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		// find app exist
		listAppDao, err := a.infra.AppRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsAppSelect](
			authdao.AppSelectWithIdApp(appUUID),
		))
		if err != nil {
			createUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listAppDao) == 0 {
			createUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_APP_NOT_EXIST
			return autherr.ErrAppNotExist
		}
		currentApp := listAppDao[0]
		// find user exist
		countUser, err := a.infra.User().UserRepository().Count(tranCtx, pdb.OptionParams[authdao.ParamsUserSelect](
			authdao.UserSelectWithIdApp(currentApp.IdApp),
			authdao.UserSelectWithEmail(requestCreateUser.GetEmail()),
		))
		if err != nil {
			return errors.WithStack(err)
		}
		if countUser > 0 {
			createUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_USER_EXIST
			return autherr.ErrUserExist
		}
		listRoleDao, err := a.infra.RoleRepository().Select(ctx, pdb.OptionParams[authdao.ParamsRoleSelect](
			authdao.RoleSelectWithIdRole(roleUUID),
		))
		if err != nil {
			createUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listRoleDao) == 0 {
			createUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_ROLE_NOT_EXIST
			return errors.WithStack(err)
		}
		currentRole := listRoleDao[0]
		newUser := &authdao.User{
			Email:  requestCreateUser.GetEmail(),
			IdApp:  currentApp.IdApp,
			IdRole: currentRole.IdRole,
			AtUnix: pdb.AtUnix{
				CreatedAt: tnowUnix,
				UpdatedAt: tnowUnix,
				DeletedAt: 0,
			},
		}

		newUser.IdUser, err = algouid.NewUUID()
		if err != nil {
			createUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		newUser.HashPassword, newUser.Salt = a.infra.CommonFunction().GeneratePasswordAndSalt(createUserRequest.Msg.GetPassword())

		newUserDetail := &authdao.UserDetail{
			IdUser:      newUser.IdUser,
			FirstName:   requestCreateUser.GetFirstName(),
			LastName:    requestCreateUser.GetLastName(),
			PhoneNumber: requestCreateUser.GetPhoneNumber(),
			RefCode:     a.infra.CommonFunction().GenerateRefCode(requestCreateUser.GetEmail()),
			AtUnix: pdb.AtUnix{
				CreatedAt: tnowUnix,
				UpdatedAt: tnowUnix,
				DeletedAt: 0,
			},
		}
		newUserDetail.IdUserDetail, err = algouid.NewUUID()
		if err != nil {
			createUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if err = a.infra.User().UserRepository().Create(tranCtx, newUser); err != nil {
			createUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if err = a.infra.User().UserDetailRepository().Create(tranCtx, newUserDetail); err != nil {
			createUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if currentApp.AppType == enumtype.MerchantAppType {
			resp, err := a.infra.ExternalService().BillingUserTransactionInternalClient().CreateUserBalance(tranCtx, connect.NewRequest[transactionv1.InternalTransactionServiceCreateUserBalanceRequest](&transactionv1.InternalTransactionServiceCreateUserBalanceRequest{
				IdUser: newUser.IdUser.Hex(),
				IdApp:  newUser.IdApp.Hex(),
			}))
			if err != nil {
				createUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
				return errors.WithStack(err)
			}
			if errResp := resp.Msg.Error; errResp == nil || resp.Msg.Error.Code != errmsgv1.ErrorCode_ERROR_CODE_SUCCESS {
				createUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
				return errors.New("create user balance fail")
			}
		}
		return nil
	}); err != nil {
		return createUserResponse, errors.WithStack(err)
	}
	createUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return createUserResponse, nil
}

func (a *authBackofficeServiceImpl) UpdateUser(ctx context.Context, updateUserRequest *connect.Request[authv1.BackofficeAuthServiceUpdateUserRequest]) (updateUserResponse *connect.Response[authv1.BackofficeAuthServiceUpdateUserResponse], err error) {
	updateUserResponse = connect.NewResponse[authv1.BackofficeAuthServiceUpdateUserResponse](
		&authv1.BackofficeAuthServiceUpdateUserResponse{
			Error: &errmsgv1.ErrorMessage{},
		})
	userCtx := usercontext.FromContext(ctx)

	if userCtx == nil {
		updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return updateUserResponse, errors.New("user_context is nil")
	}
	if err = updateUserValidateRequest(updateUserRequest.Msg); err != nil {
		updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		updateUserResponse.Msg.Error.Message = err.Error()
		return updateUserResponse, err
	}

	curAdminRole, err := a.infra.PolicyManager().GetRole(ctx, userCtx.IdAuthRole)
	if err != nil {
		updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return updateUserResponse, errors.WithStack(err)
	}
	newRoleOfTargetUser, err := a.infra.PolicyManager().GetRole(ctx, updateUserRequest.Msg.GetIdRole())
	if err != nil {
		updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return updateUserResponse, errors.WithStack(err)
	}
	// user k co quyen update 1 user khac len quyen cao hon hoac bang quyen cua user hien tai
	if curAdminRole.Priority > 0 && curAdminRole.Priority >= newRoleOfTargetUser.Priority {
		updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_PERMISSION_DENY
		return updateUserResponse, errors.New("user_role_priority_higher_than_target_role")
	}
	idRoleWillUpdate, err := algouid.FromHex(updateUserRequest.Msg.GetIdRole())
	if err != nil {
		updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return updateUserResponse, errors.WithStack(err)
	}

	if err = updateUserValidateRequest(updateUserRequest.Msg); err != nil {
		updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		updateUserResponse.Msg.Error.Message = err.Error()
		return updateUserResponse, err
	}
	var (
		requestUpdateUser = updateUserRequest.Msg
	)
	userUUID, err := algouid.FromHex(updateUserRequest.Msg.GetIdUser())
	if err != nil {
		updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return updateUserResponse, errors.WithStack(err)
	}

	if err = a.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		listUserDao, err := a.infra.User().UserRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsUserSelect](
			authdao.UserSelectWithIdUser(userUUID),
		))
		if err != nil {
			updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listUserDao) == 0 {
			updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_USER_NOT_EXIST
			return autherr.ErrUserNotExist
		}
		currentUser := listUserDao[0]
		listCurrentRoleOfUser, err := a.infra.RoleRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsRoleSelect](
			authdao.RoleSelectWithIdRole(currentUser.IdRole),
		))
		if err != nil {
			updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listCurrentRoleOfUser) == 0 {
			updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_ROLE_NOT_EXIST
			return autherr.ErrRoleNotExist
		}
		// user k co quyen update user co priority thap hon minh ( tuc k co quyen voi user co role cao hon )
		currentRoleOfUser := listCurrentRoleOfUser[0]
		if curAdminRole.Priority > currentRoleOfUser.Priority {
			updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_PERMISSION_DENY
			return errors.New("admin user must be higher priority than target user")
		}

		listAppDao, err := a.infra.AppRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsAppSelect](
			authdao.AppSelectWithIdApp(currentUser.IdApp),
		))
		if err != nil {
			updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listAppDao) == 0 {
			updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_APP_NOT_EXIST
			return autherr.ErrAppNotExist
		}

		params := pdb.OptionParams[authdao.ParamsUserUpdate](
			authdao.UserUpdateWithIdUser(currentUser.IdUser),
			authdao.UserUpdateIdRole(currentUser.IdRole, idRoleWillUpdate),
			authdao.UserUpdateActiveState(currentUser.IsActive(), requestUpdateUser.GetState().GetIsActive()),
		)
		if len(requestUpdateUser.GetPassword()) > 0 {
			hashPassword, salt := a.infra.CommonFunction().GeneratePasswordAndSalt(requestUpdateUser.GetPassword())
			authdao.UserUpdateSalt(currentUser.Salt, salt)(params)
			authdao.UserUpdateHashPassword(currentUser.HashPassword, hashPassword)(params)
		}
		if !params.IsChange {
			updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
			return nil
		}
		if err = a.infra.User().UserRepository().Update(tranCtx, params); err != nil {
			updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		// kick user session
		if err = a.infra.SessionManager().RemoveToken(ctx, currentUser.IdUser.Hex()); err != nil {
			updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return updateUserResponse, errors.WithStack(err)
	}
	updateUserResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return updateUserResponse, nil
}
func (m *authBackofficeServiceImpl) UpdateProfile(ctx context.Context, request *connect.Request[authv1.BackofficeAuthServiceUpdateProfileRequest]) (response *connect.Response[authv1.BackofficeAuthServiceUpdateProfileResponse], err error) {
	response = connect.NewResponse[authv1.BackofficeAuthServiceUpdateProfileResponse](&authv1.BackofficeAuthServiceUpdateProfileResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	var tnowUnix = time.Now().Unix()

	idUser, err := algouid.FromHex(request.Msg.GetIdUser())
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return response, errors.WithStack(err)
	}

	if err = m.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		listUserDao, err := m.infra.User().UserRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsUserSelect](
			authdao.UserSelectWithIdUser(idUser)))
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listUserDao) == 0 {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_USER_NOT_EXIST
			return autherr.ErrUserNotExist
		}

		validResponse, err := m.infra.ExternalService().MiscInternalClient().ValidPaymentAddress(tranCtx, connect.NewRequest(&paymentaddressv1.InternalPaymentAddressServiceValidPaymentAddressRequest{
			IdCountry: request.Msg.GetIdCountry(),
			IdState:   request.Msg.GetIdState(),
			IdCity:    request.Msg.GetIdCity(),
		}))

		if err != nil || validResponse == nil {

			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
			return errors.WithStack(err)
		}
		if validResponse.Msg.Error.Code != errmsgv1.ErrorCode_ERROR_CODE_SUCCESS {
			response.Msg.Error.Code = validResponse.Msg.Error.Code
			return errors.WithStack(err)
		}

		listUserDetailDao, err := m.infra.User().UserDetailRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsUserDetailSelect](
			authdao.UserDetailSelectWithIdUser(idUser),
			authdao.UserDetailSelectWithActiveOrDeactivate(true, false),
		))
		if err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listUserDetailDao) > 0 {
			var listUpdate []*authdao.ParamsUserDetailUpdate
			for _, item := range listUserDetailDao {
				paramUpdate := pdb.OptionParams[authdao.ParamsUserDetailUpdate](
					authdao.AuthUserDetailUpdateActiveState(item.IsActive(), false),
					authdao.UserDetailUpdateWithIdUserDetail(item.IdUserDetail),
				)
				if paramUpdate.IsChange {
					listUpdate = append(listUpdate, paramUpdate)
				}
			}
			if err = m.infra.User().UserDetailRepository().Update(tranCtx, listUpdate...); err != nil {
				response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
				return errors.WithStack(err)
			}

		}

		if len(request.Msg.GetIdCompany()) > 0 {
			listCompanyDao, err := m.infra.CompanyRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsCompanySelect](
				authdao.CompanySelectWithIdCompany(algouid.FromHexOrNil(request.Msg.GetIdCompany())),
			))
			if err != nil {
				response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
				return errors.WithStack(err)
			}
			if len(listCompanyDao) == 0 {
				response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
				return autherr.ErrCompanyNotFound
			}
		}

		currentUserDetail := listUserDetailDao[0]
		profileCreate := &authdao.UserDetail{
			IdCompany:   algouid.FromHexOrNil(request.Msg.GetIdCompany()),
			IdUser:      idUser,
			Street:      request.Msg.Street,
			IdState:     sql.Null[int64]{V: request.Msg.IdState, Valid: true},
			IdCity:      sql.Null[int64]{V: request.Msg.IdCity, Valid: true},
			IdCountry:   sql.Null[int64]{V: request.Msg.IdCountry, Valid: true},
			PhoneNumber: currentUserDetail.PhoneNumber,
			FirstName:   request.Msg.FirstName,
			LastName:    request.Msg.LastName,
			RefCode:     currentUserDetail.RefCode,
			AtUnix: pdb.AtUnix{
				CreatedAt: tnowUnix,
				UpdatedAt: tnowUnix,
				DeletedAt: 0,
			},
		}
		if len(request.Msg.GetLastName()) > 0 {
			profileCreate.LastName = request.Msg.GetLastName()
		}
		if len(request.Msg.GetFirstName()) > 0 {
			profileCreate.FirstName = request.Msg.GetFirstName()
		}

		profileCreate.IdUserDetail, err = algouid.NewUUID()

		if err = m.infra.User().UserDetailRepository().Create(tranCtx, profileCreate); err != nil {
			response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return response, errors.WithStack(err)
	}
	response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return response, nil
}
func (a *authBackofficeServiceImpl) FetchRole(ctx context.Context, fetchRoleRequest *connect.Request[authv1.BackofficeAuthServiceFetchRoleRequest]) (fetchRoleResponse *connect.Response[authv1.BackofficeAuthServiceFetchRoleResponse], err error) {
	fetchRoleResponse = connect.NewResponse[authv1.BackofficeAuthServiceFetchRoleResponse](&authv1.BackofficeAuthServiceFetchRoleResponse{
		Error:      &errmsgv1.ErrorMessage{},
		Pagination: &utilsv1.PaginationResponse{},
		Items:      make([]*authv1.RoleModel, 0),
	})
	userCtx := usercontext.FromContext(ctx)
	if userCtx == nil {
		fetchRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return fetchRoleResponse, errors.New("user_context is nil")
	}
	currentRole, err := a.infra.PolicyManager().GetRole(ctx, userCtx.IdAuthRole)
	if err != nil {
		fetchRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return fetchRoleResponse, errors.WithStack(err)
	}

	var (
		requestFetchRole                    = fetchRoleRequest.Msg
		pageSize, pageNumber, offset, limit = rpcutils.ToOffsetLimit(requestFetchRole.GetPagination().GetPageSize(), requestFetchRole.GetPagination().GetPageNumber())
		total                               int64
	)
	defer func() {
		fetchRoleResponse.Msg.Pagination.PageSize = pageSize
		fetchRoleResponse.Msg.Pagination.CurrentPage = pageNumber
		fetchRoleResponse.Msg.Pagination.Total = total
		fetchRoleResponse.Msg.Pagination.TotalPages = rpcutils.ToTotalPage(pageSize, total)
	}()

	nameSearchEscape := html.EscapeString(requestFetchRole.GetNameSearch())

	params := pdb.OptionParams[authdao.ParamsRoleSelect](
		authdao.RoleSelectWithIdRole(algouid.FromHexOrNil(requestFetchRole.GetIdRole())),
		authdao.RoleSelectWithRoleNameSearch(nameSearchEscape),
		authdao.RoleSelectWithPriorityGte(currentRole.Priority),
		authdao.RoleSelectWithActiveOrDeactivate(requestFetchRole.GetState().GetIsActive(), requestFetchRole.GetState().GetIsDeactivate()),
		authdao.RoleSelectOrderByPriority(),
		authdao.RoleSelectWithPagination(offset, limit),
	)
	total, err = a.infra.RoleRepository().Count(ctx, params)
	if err != nil {
		fetchRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return fetchRoleResponse, errors.WithStack(err)
	}
	if total == 0 {
		fetchRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
		return fetchRoleResponse, nil
	}
	listRole, err := a.infra.RoleRepository().Select(ctx, params)
	if err != nil {
		fetchRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return fetchRoleResponse, errors.WithStack(err)
	}
	var (
		listRoleModels = make([]*authv1.RoleModel, 0, len(listRole))
	)
	for _, v := range listRole {
		listRoleModels = append(listRoleModels, &authv1.RoleModel{
			IdRole:   v.IdRole.Hex(),
			Name:     v.RoleName,
			Priority: v.Priority,
			IsActive: v.IsActive(),
		})
	}
	fetchRoleResponse.Msg.Items = listRoleModels
	fetchRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return fetchRoleResponse, nil
}

func (a *authBackofficeServiceImpl) CreateRole(ctx context.Context, createRoleRequest *connect.Request[authv1.BackofficeAuthServiceCreateRoleRequest]) (createRoleResponse *connect.Response[authv1.BackofficeAuthServiceCreateRoleResponse], err error) {
	createRoleResponse = connect.NewResponse[authv1.BackofficeAuthServiceCreateRoleResponse](
		&authv1.BackofficeAuthServiceCreateRoleResponse{
			Error: &errmsgv1.ErrorMessage{},
		})
	userCtx := usercontext.FromContext(ctx)
	if userCtx == nil {
		createRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return createRoleResponse, errors.New("user_context is nil")
	}
	// TODO: check logic this, only super admin can create this role
	if err = createRoleValidateRequest(createRoleRequest.Msg); err != nil {
		createRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		createRoleResponse.Msg.Error.Message = err.Error()
		return createRoleResponse, err
	}
	roleOfThisUser, err := a.infra.PolicyManager().GetRole(ctx, userCtx.IdAuthRole)
	if err != nil {
		createRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return createRoleResponse, errors.WithStack(err)
	}
	if createRoleRequest.Msg.GetPriority() < roleOfThisUser.Priority {
		createRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_ROLE_PRIORITY_MUST_BE_HIGHER
		return createRoleResponse, errors.New("user_role_priority_higher_than_target_role")
	}

	tnowUnix := time.Now().Unix()
	var (
		requestCreateRole = createRoleRequest.Msg
		roleNameEscape    = html.EscapeString(requestCreateRole.GetName())
	)
	if err = a.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		countRole, err := a.infra.RoleRepository().Count(tranCtx, pdb.OptionParams[authdao.ParamsRoleSelect](
			authdao.RoleSelectWithRoleName(roleNameEscape),
		))
		if err != nil {
			createRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if countRole > 0 {
			createRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_ROLE_EXIST
			return autherr.ErrRoleExist
		}
		newRole := &authdao.Role{
			RoleName: roleNameEscape,
			AtUnix: pdb.AtUnix{
				CreatedAt: tnowUnix,
				UpdatedAt: tnowUnix,
				DeletedAt: 0,
			},
		}
		newRole.IdRole, err = algouid.NewUUID()
		if err != nil {
			createRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}

		if err = a.infra.RoleRepository().Create(tranCtx, newRole); err != nil {
			createRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return createRoleResponse, errors.WithStack(err)
	}
	createRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return createRoleResponse, nil
}

func (a *authBackofficeServiceImpl) UpdateRole(ctx context.Context, updateRoleRequest *connect.Request[authv1.BackofficeAuthServiceUpdateRoleRequest]) (updateRoleResponse *connect.Response[authv1.BackofficeAuthServiceUpdateRoleResponse], err error) {
	updateRoleResponse = connect.NewResponse[authv1.BackofficeAuthServiceUpdateRoleResponse](
		&authv1.BackofficeAuthServiceUpdateRoleResponse{
			Error: &errmsgv1.ErrorMessage{},
		})
	if err = updateRoleValidateRequest(updateRoleRequest.Msg); err != nil {
		updateRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		updateRoleResponse.Msg.Error.Message = err.Error()
		return updateRoleResponse, err
	}
	var (
		requestUpdateRole = updateRoleRequest.Msg
	)

	idAuthRole, err := algouid.FromHex(requestUpdateRole.GetIdRole())
	if err != nil {
		updateRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return updateRoleResponse, errors.WithStack(err)
	}

	if err = a.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		listRoleDao, err := a.infra.RoleRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsRoleSelect](
			authdao.RoleSelectWithIdRole(idAuthRole),
		))
		if err != nil {
			updateRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listRoleDao) == 0 {
			updateRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_ROLE_NOT_EXIST
			return autherr.ErrRoleNotExist
		}

		currentRole := listRoleDao[0]
		params := pdb.OptionParams[authdao.ParamsRoleUpdate](
			authdao.RoleUpdateWithIdRole(currentRole.IdRole),
			authdao.RoleUpdateRoleName(currentRole.RoleName, requestUpdateRole.GetName()),
			authdao.RoleUpdateActiveState(currentRole.IsActive(), requestUpdateRole.GetState().GetIsActive()),
		)
		if !params.IsChange {
			updateRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
			return nil
		}

		if err = a.infra.RoleRepository().Update(tranCtx, params); err != nil {
			updateRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return updateRoleResponse, errors.WithStack(err)
	}
	updateRoleResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return updateRoleResponse, nil
}

func (a *authBackofficeServiceImpl) FetchService(ctx context.Context, fetchServiceRequest *connect.Request[authv1.BackofficeAuthServiceFetchServiceRequest]) (fetchServiceResponse *connect.Response[authv1.BackofficeAuthServiceFetchServiceResponse], err error) {
	fetchServiceResponse = connect.NewResponse[authv1.BackofficeAuthServiceFetchServiceResponse](&authv1.BackofficeAuthServiceFetchServiceResponse{
		Error:      &errmsgv1.ErrorMessage{},
		Pagination: &utilsv1.PaginationResponse{},
		Items:      make([]*authv1.ServiceModel, 0),
	})
	var (
		requestFetchService                 = fetchServiceRequest.Msg
		pageSize, pageNumber, offset, limit = rpcutils.ToOffsetLimit(requestFetchService.GetPagination().GetPageSize(), requestFetchService.GetPagination().GetPageNumber())
		total                               int64
	)
	defer func() {
		fetchServiceResponse.Msg.Pagination.PageSize = pageSize
		fetchServiceResponse.Msg.Pagination.CurrentPage = pageNumber
		fetchServiceResponse.Msg.Pagination.Total = total
		fetchServiceResponse.Msg.Pagination.TotalPages = total / pageSize
		if fetchServiceResponse.Msg.Pagination.TotalPages == 0 && total > 0 {
			fetchServiceResponse.Msg.Pagination.TotalPages = 1
		}
	}()

	paramsFilter := pdb.OptionParams[authdao.ParamsServiceSelect](
		authdao.ServiceSelectWithIdService(algouid.FromHexOrNil(requestFetchService.GetIdService())),
		authdao.ServiceSelectWithNameSearch(html.EscapeString(requestFetchService.GetNameSearch())),
		authdao.ServiceSelectWithActiveOrDeactivate(requestFetchService.GetState().GetIsActive(), requestFetchService.GetState().GetIsDeactivate()),
		authdao.ServiceSelectWithPagination(offset, limit),
	)
	total, err = a.infra.ServiceRepository().Count(ctx, paramsFilter)
	if err != nil {
		fetchServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return fetchServiceResponse, errors.WithStack(err)
	}
	if total == 0 {
		fetchServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
		return fetchServiceResponse, nil
	}
	listServiceDao, err := a.infra.ServiceRepository().Select(ctx, paramsFilter)
	if err != nil {
		fetchServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return fetchServiceResponse, errors.WithStack(err)
	}
	var (
		listServiceModels = make([]*authv1.ServiceModel, 0, len(listServiceDao))
	)

	for _, v := range listServiceDao {
		listServiceModels = append(listServiceModels, &authv1.ServiceModel{
			IdService: v.IdService.Hex(),
			Name:      v.Name,
			IsActive:  v.IsActive(),
		})
	}
	fetchServiceResponse.Msg.Items = listServiceModels
	fetchServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return fetchServiceResponse, nil
}

func (a *authBackofficeServiceImpl) CreateService(ctx context.Context, createServiceRequest *connect.Request[authv1.BackofficeAuthServiceCreateServiceRequest]) (createServiceResponse *connect.Response[authv1.BackofficeAuthServiceCreateServiceResponse], err error) {
	createServiceResponse = connect.NewResponse[authv1.BackofficeAuthServiceCreateServiceResponse](
		&authv1.BackofficeAuthServiceCreateServiceResponse{
			Error: &errmsgv1.ErrorMessage{},
		})
	if err = createServiceValidateRequest(createServiceRequest.Msg); err != nil {
		createServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		createServiceResponse.Msg.Error.Message = err.Error()
		return createServiceResponse, err
	}
	var (
		tnowUnix             = time.Now().Unix()
		requestCreateService = createServiceRequest.Msg
		serviceNameEscape    = html.EscapeString(requestCreateService.GetName())
	)
	if err = a.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		// find service with name exist
		listServiceExist, err := a.infra.ServiceRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsServiceSelect](
			authdao.ServiceSelectWithExist(serviceNameEscape),
		))
		if err != nil {
			createServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}

		if len(listServiceExist) > 0 {
			createServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_SERVICE_EXIST
			return autherr.ErrServiceExist
		}
		newService := &authdao.Service{
			Name: serviceNameEscape,
			AtUnix: pdb.AtUnix{
				CreatedAt: tnowUnix,
				UpdatedAt: tnowUnix,
				DeletedAt: 0,
			},
		}
		newService.IdService, err = algouid.NewUUID()
		if err != nil {
			createServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if err = a.infra.ServiceRepository().Create(tranCtx, newService); err != nil {
			createServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return createServiceResponse, errors.WithStack(err)
	}
	createServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return createServiceResponse, nil
}

func (a *authBackofficeServiceImpl) UpdateService(ctx context.Context, updateServiceRequest *connect.Request[authv1.BackofficeAuthServiceUpdateServiceRequest]) (updateServiceResponse *connect.Response[authv1.BackofficeAuthServiceUpdateServiceResponse], err error) {
	updateServiceResponse = connect.NewResponse[authv1.BackofficeAuthServiceUpdateServiceResponse](
		&authv1.BackofficeAuthServiceUpdateServiceResponse{
			Error: &errmsgv1.ErrorMessage{},
		})

	if err = updateServiceValidateRequest(updateServiceRequest.Msg); err != nil {
		updateServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		updateServiceResponse.Msg.Error.Message = err.Error()
		return updateServiceResponse, err
	}

	var (
		requestUpdateService = updateServiceRequest.Msg
		serviceNameEscape    = html.EscapeString(requestUpdateService.GetName())
	)

	idAuthService, err := algouid.FromHex(requestUpdateService.GetIdService())
	if err != nil {
		updateServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return updateServiceResponse, errors.WithStack(err)
	}
	if err = a.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {

		listServiceDao, err := a.infra.ServiceRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsServiceSelect](
			authdao.ServiceSelectWithIdService(idAuthService),
		))
		if err != nil {
			updateServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listServiceDao) == 0 {
			updateServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_SERVICE_NOT_EXIST
			return autherr.ErrServiceNotExist
		}
		currentService := listServiceDao[0]

		params := pdb.OptionParams[authdao.ParamsServiceUpdate](
			authdao.ServiceUpdateWithIdService(currentService.IdService),
			authdao.ServiceUpdateName(currentService.Name, serviceNameEscape),
			authdao.ServiceUpdateActiveState(currentService.IsActive(), requestUpdateService.GetState().GetIsActive()),
		)
		if !params.IsChange {
			updateServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
			return nil
		}
		if params.Name.Valid {
			listServiceExist, err := a.infra.ServiceRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsServiceSelect](
				authdao.ServiceSelectWithName(serviceNameEscape),
			))
			if err != nil {
				updateServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
				return errors.WithStack(err)
			}
			if len(listServiceExist) > 0 {
				updateServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_SERVICE_EXIST
				return autherr.ErrServiceExist
			}
		}

		if err = a.infra.ServiceRepository().Update(tranCtx, params); err != nil {
			updateServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return updateServiceResponse, errors.WithStack(err)
	}
	updateServiceResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return updateServiceResponse, nil
}

func (a *authBackofficeServiceImpl) FetchPath(ctx context.Context, fetchPathRequest *connect.Request[authv1.BackofficeAuthServiceFetchPathRequest]) (fetchPathResponse *connect.Response[authv1.BackofficeAuthServiceFetchPathResponse], err error) {
	fetchPathResponse = connect.NewResponse[authv1.BackofficeAuthServiceFetchPathResponse](&authv1.BackofficeAuthServiceFetchPathResponse{
		Error:      &errmsgv1.ErrorMessage{},
		Pagination: &utilsv1.PaginationResponse{},
		Items:      make([]*authv1.PathModel, 0),
	})
	var (
		requestFetchPath                    = fetchPathRequest.Msg
		pageSize, pageNumber, offset, limit = rpcutils.ToOffsetLimit(requestFetchPath.GetPagination().GetPageSize(), requestFetchPath.GetPagination().GetPageNumber())
		total                               int64
		absolutePathSearchEscape            = html.EscapeString(requestFetchPath.GetAbsolutePathSearch())
	)
	defer func() {
		fetchPathResponse.Msg.Pagination.PageSize = pageSize
		fetchPathResponse.Msg.Pagination.CurrentPage = pageNumber
		fetchPathResponse.Msg.Pagination.Total = total
		fetchPathResponse.Msg.Pagination.TotalPages = total / pageSize
		if fetchPathResponse.Msg.Pagination.TotalPages == 0 && total > 0 {
			fetchPathResponse.Msg.Pagination.TotalPages = 1
		}
	}()

	paramGetPath := pdb.OptionParams[authdao.ParamsPathSelect](
		authdao.PathSelectWithIdPath(algouid.FromHexOrNil(requestFetchPath.GetIdPath())),
		authdao.PathSelectWithIdService(algouid.FromHexOrNil(requestFetchPath.GetIdService())),
		authdao.PathSelectWithAbsolutePathSearch(absolutePathSearchEscape),
		authdao.PathSelectWithActiveOrDeactivate(requestFetchPath.GetState().GetIsActive(), requestFetchPath.GetState().GetIsDeactivate()),
		authdao.PathSelectWithPagination(offset, limit),
	)
	total, err = a.infra.PathRepository().Count(ctx, paramGetPath)
	if err != nil {
		fetchPathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return fetchPathResponse, errors.WithStack(err)
	}
	if total == 0 {
		fetchPathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
		return fetchPathResponse, nil
	}
	listAuthPathDao, err := a.infra.PathRepository().Select(ctx, paramGetPath)
	if err != nil {
		fetchPathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return fetchPathResponse, errors.WithStack(err)
	}
	var (
		listAuthPathModels = make([]*authv1.PathModel, 0, len(listAuthPathDao))
		mapIdService       = make(map[algouid.UUID]struct{}, len(listAuthPathDao))
	)
	for i := range listAuthPathDao {
		mapIdService[listAuthPathDao[i].IdService] = struct{}{}
	}
	serviceBundle, err := a.infra.BundleLoader().LoadBundleService(ctx, a.infra.ServiceRepository(), mapIdService)
	if err != nil {
		fetchPathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return fetchPathResponse, errors.WithStack(err)
	}

	for _, v := range listAuthPathDao {
		service, ok := serviceBundle[v.IdService]
		if !ok {
			fetchPathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return fetchPathResponse, errors.New("service_bundle error")
		}
		listAuthPathModels = append(listAuthPathModels, &authv1.PathModel{
			Service: &authv1.ServiceModel{
				IdService: service.IdService.Hex(),
				Name:      service.Name,
				IsActive:  service.IsActive(),
			},
			IdPath:       v.IdPath.Hex(),
			AbsolutePath: v.AbsolutePath,
			IsActive:     service.IsActive() && v.IsActive(),
		})
	}
	fetchPathResponse.Msg.Items = listAuthPathModels
	fetchPathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return fetchPathResponse, nil
}

func (a *authBackofficeServiceImpl) CreatePath(ctx context.Context, createPathRequest *connect.Request[authv1.BackofficeAuthServiceCreatePathRequest]) (createPathResponse *connect.Response[authv1.BackofficeAuthServiceCreatePathResponse], err error) {
	createPathResponse = connect.NewResponse[authv1.BackofficeAuthServiceCreatePathResponse](&authv1.BackofficeAuthServiceCreatePathResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	if err = createPathValidateRequest(createPathRequest.Msg); err != nil {
		createPathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		createPathResponse.Msg.Error.Message = err.Error()
		return createPathResponse, err
	}
	var (
		requestCreatePath  = createPathRequest.Msg
		absolutePathEscape = html.EscapeString(requestCreatePath.GetAbsolutePath())
		timeNowUnix        = time.Now().Unix()
	)
	idAuthService, err := algouid.FromHex(requestCreatePath.GetIdService())
	if err != nil {
		createPathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return createPathResponse, errors.WithStack(err)
	}

	if err = a.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		listServiceDao, err := a.infra.ServiceRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsServiceSelect](
			authdao.ServiceSelectWithIdService(idAuthService),
		))
		if err != nil {
			createPathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listServiceDao) == 0 {
			createPathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_SERVICE_NOT_EXIST
			return autherr.ErrServiceNotExist
		}
		currentService := listServiceDao[0]
		// check path exist

		pathExist, err := a.infra.PathRepository().Select(ctx, pdb.OptionParams[authdao.ParamsPathSelect](
			authdao.PathSelectWithIdServiceAndAbsolutePathExist(currentService.IdService, absolutePathEscape),
		))
		if err != nil {
			createPathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(pathExist) > 0 {
			createPathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_PATH_EXIST
			return autherr.ErrPathExist
		}
		newPath := &authdao.Path{
			IdService:    currentService.IdService,
			AbsolutePath: absolutePathEscape,
			AtUnix: pdb.AtUnix{
				CreatedAt: timeNowUnix,
				UpdatedAt: timeNowUnix,
				DeletedAt: 0,
			},
		}
		newPath.IdPath, err = algouid.NewUUID()
		if err != nil {
			createPathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}

		if err = a.infra.PathRepository().Create(tranCtx, newPath); err != nil {
			createPathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}

		return nil
	}); err != nil {
		return createPathResponse, errors.WithStack(err)
	}
	createPathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return createPathResponse, nil
}

func (a *authBackofficeServiceImpl) UpdatePath(ctx context.Context, updatePathRequest *connect.Request[authv1.BackofficeAuthServiceUpdatePathRequest]) (updatePathResponse *connect.Response[authv1.BackofficeAuthServiceUpdatePathResponse], err error) {
	updatePathResponse = connect.NewResponse[authv1.BackofficeAuthServiceUpdatePathResponse](
		&authv1.BackofficeAuthServiceUpdatePathResponse{
			Error: &errmsgv1.ErrorMessage{},
		})
	if err = updatePathValidateRequest(updatePathRequest.Msg); err != nil {
		updatePathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		updatePathResponse.Msg.Error.Message = err.Error()
		return updatePathResponse, err
	}
	var (
		requestUpdatePath  = updatePathRequest.Msg
		absolutePathEscape = html.EscapeString(requestUpdatePath.GetAbsolutePath())
	)

	idAuthPath, err := algouid.FromHex(updatePathRequest.Msg.GetIdPath())
	if err != nil {
		updatePathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return updatePathResponse, errors.WithStack(err)
	}

	if err = a.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		listPathDao, err := a.infra.PathRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsPathSelect](
			authdao.PathSelectWithIdPath(idAuthPath),
		))
		if err != nil {
			updatePathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listPathDao) == 0 {
			updatePathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_PATH_NOT_EXIST
			return autherr.ErrPathNotExist
		}
		currentPath := listPathDao[0]
		var (
			updatePathParam = pdb.OptionParams[authdao.ParamsPathUpdate](
				authdao.PathUpdateWithIdPath(currentPath.IdPath),
				authdao.PathUpdateAbsolutePath(currentPath.AbsolutePath, absolutePathEscape),
				authdao.PathUpdateActiveState(currentPath.IsActive(), requestUpdatePath.GetState().GetIsActive()),
			)
		)
		if !updatePathParam.IsChange {
			updatePathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
			return nil
		}

		if updatePathParam.AbsolutePath.Valid {
			totalPathExist, err := a.infra.PathRepository().Count(ctx, pdb.OptionParams[authdao.ParamsPathSelect](
				authdao.PathSelectWithAbsolutePath(absolutePathEscape),
			))
			if err != nil {
				updatePathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
				return errors.WithStack(err)
			}
			if totalPathExist > 0 {
				updatePathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_PATH_EXIST
				return autherr.ErrPathExist
			}
		}

		if err = a.infra.PathRepository().Update(tranCtx, updatePathParam); err != nil {
			updatePathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return updatePathResponse, errors.WithStack(err)
	}
	updatePathResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return updatePathResponse, nil
}

func (a *authBackofficeServiceImpl) FetchPolicy(ctx context.Context, fetchPolicyRequest *connect.Request[authv1.BackofficeAuthServiceFetchPolicyRequest]) (fetchPolicyResponse *connect.Response[authv1.BackofficeAuthServiceFetchPolicyResponse], err error) {
	fetchPolicyResponse = connect.NewResponse[authv1.BackofficeAuthServiceFetchPolicyResponse](&authv1.BackofficeAuthServiceFetchPolicyResponse{
		Error:      &errmsgv1.ErrorMessage{},
		Pagination: &utilsv1.PaginationResponse{},
		Items:      make([]*authv1.PolicyModel, 0),
	})
	var (
		requestFetchPolicy                  = fetchPolicyRequest.Msg
		pageSize, pageNumber, offset, limit = rpcutils.ToOffsetLimit(requestFetchPolicy.GetPagination().GetPageSize(), requestFetchPolicy.GetPagination().GetPageNumber())
		total                               int64
	)
	defer func() {
		fetchPolicyResponse.Msg.Pagination.PageSize = pageSize
		fetchPolicyResponse.Msg.Pagination.CurrentPage = pageNumber
		fetchPolicyResponse.Msg.Pagination.Total = total
		fetchPolicyResponse.Msg.Pagination.TotalPages = total / pageSize
		if fetchPolicyResponse.Msg.Pagination.TotalPages == 0 && total > 0 {
			fetchPolicyResponse.Msg.Pagination.TotalPages = 1
		}
	}()
	params := pdb.OptionParams[authdao.ParamsPolicySelect](
		authdao.PolicySelectWithIdPolicy(algouid.FromHexOrNil(requestFetchPolicy.GetIdPolicy())),
		authdao.PolicySelectWithIdRole(algouid.FromHexOrNil(requestFetchPolicy.GetIdRole())),
		authdao.PolicySelectWithIdPath(algouid.FromHexOrNil(requestFetchPolicy.GetIdPath())),
		authdao.PolicySelectWithActiveOrDeactivate(requestFetchPolicy.GetState().GetIsActive(), requestFetchPolicy.GetState().GetIsDeactivate()),
		authdao.PolicySelectWithPagination(offset, limit),
	)
	total, err = a.infra.PolicyRepository().Count(ctx, params)
	if err != nil {
		fetchPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return fetchPolicyResponse, errors.WithStack(err)
	}
	if total == 0 {
		fetchPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
		return fetchPolicyResponse, nil
	}
	listPoliciesDao, err := a.infra.PolicyRepository().Select(ctx, params)
	if err != nil {
		fetchPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return fetchPolicyResponse, errors.WithStack(err)
	}
	// collect bundle
	var (
		errGroup, _  = errgroup.WithContext(ctx)
		mapIdService = make(map[algouid.UUID]struct{})
		mapIdPath    = make(map[algouid.UUID]struct{})
		mapIdRole    = make(map[algouid.UUID]struct{})

		pathBundle    map[algouid.UUID]*authdao.Path
		roleBundle    map[algouid.UUID]*authdao.Role
		serviceBundle map[algouid.UUID]*authdao.Service
	)
	for _, v := range listPoliciesDao {
		mapIdPath[v.IdPath] = struct{}{}
		mapIdRole[v.IdRole] = struct{}{}
	}
	errGroup.Go(func() error {
		pathBundle, err = a.infra.BundleLoader().LoadBundlePath(ctx, a.infra.PathRepository(), mapIdPath)
		if err != nil {
			fetchPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		for _, v := range pathBundle {
			mapIdService[v.IdService] = struct{}{}
		}
		serviceBundle, err = a.infra.BundleLoader().LoadBundleService(ctx, a.infra.ServiceRepository(), mapIdService)
		if err != nil {
			fetchPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		return nil
	})
	errGroup.Go(func() error {
		roleBundle, err = a.infra.BundleLoader().LoadBundleRole(ctx, a.infra.RoleRepository(), mapIdRole)
		if err != nil {
			fetchPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		return nil
	})
	if err = errGroup.Wait(); err != nil {
		return fetchPolicyResponse, errors.WithStack(err)
	}
	var (
		listPolicyModels = make([]*authv1.PolicyModel, 0, len(listPoliciesDao))
	)
	for _, v := range listPoliciesDao {
		role, ok := roleBundle[v.IdRole]
		if !ok {
			fetchPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return fetchPolicyResponse, errors.New("role_bundle error")
		}
		path, ok := pathBundle[v.IdPath]
		if !ok {
			fetchPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return fetchPolicyResponse, errors.New("path_bundle error")
		}
		service, ok := serviceBundle[path.IdService]
		if !ok {
			fetchPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return fetchPolicyResponse, errors.New("service_bundle error")
		}
		listPolicyModels = append(listPolicyModels, &authv1.PolicyModel{
			IdPolicy: v.IdPolicy.Hex(),
			Service: &authv1.ServiceModel{
				IdService: service.IdService.Hex(),
				Name:      service.Name,
				IsActive:  service.IsActive()},
			Path: &authv1.PathModel{
				IdPath:       path.IdPath.Hex(),
				AbsolutePath: path.AbsolutePath,
				IsActive:     path.IsActive(),
			},
			Role: &authv1.RoleModel{
				IdRole:   role.IdRole.Hex(),
				Name:     role.RoleName,
				IsActive: role.IsActive(),
			},
			IsActive: v.IsActive(),
			State:    service.IsActive() && path.IsActive() && role.IsActive() && v.IsActive(),
		})
	}
	fetchPolicyResponse.Msg.Items = listPolicyModels
	fetchPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return fetchPolicyResponse, nil
}

func (a *authBackofficeServiceImpl) CreatePolicy(ctx context.Context, createPolicyRequest *connect.Request[authv1.BackofficeAuthServiceCreatePolicyRequest]) (createPolicyResponse *connect.Response[authv1.BackofficeAuthServiceCreatePolicyResponse], err error) {
	createPolicyResponse = connect.NewResponse[authv1.BackofficeAuthServiceCreatePolicyResponse](&authv1.BackofficeAuthServiceCreatePolicyResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	if err = createPolicyValidateRequest(createPolicyRequest.Msg); err != nil {
		createPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		createPolicyResponse.Msg.Error.Message = err.Error()
		return createPolicyResponse, errors.WithStack(err)
	}

	var (
		tnowUnix            = time.Now().Unix()
		requestCreatePolicy = createPolicyRequest.Msg
	)

	idAuthPath, err := algouid.FromHex(requestCreatePolicy.GetIdPath())
	if err != nil {
		createPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return createPolicyResponse, errors.WithStack(err)
	}
	idAuthRole, err := algouid.FromHex(requestCreatePolicy.GetIdRole())
	if err != nil {
		createPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return createPolicyResponse, errors.WithStack(err)
	}

	if err = a.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		listRoleDao, err := a.infra.RoleRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsRoleSelect](
			authdao.RoleSelectWithIdRole(idAuthRole),
		))
		if err != nil {
			createPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listRoleDao) == 0 {
			createPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_ROLE_NOT_EXIST
			return autherr.ErrRoleNotExist
		}
		currentRole := listRoleDao[0]

		listPathDao, err := a.infra.PathRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsPathSelect](
			authdao.PathSelectWithIdPath(idAuthPath),
		))
		if err != nil {
			createPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listPathDao) == 0 {
			createPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_PATH_NOT_EXIST
			return autherr.ErrPathNotExist
		}
		currentPath := listPathDao[0]

		countPolicy, err := a.infra.PolicyRepository().Count(tranCtx, pdb.OptionParams[authdao.ParamsPolicySelect](
			authdao.PolicySelectWithIdPath(currentPath.IdPath),
			authdao.PolicySelectWithIdRole(currentRole.IdRole),
		))
		if err != nil {
			createPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if countPolicy > 0 {
			createPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_POLICY_EXIST
			return autherr.ErrPolicyExist
		}

		newPolicy := &authdao.Policy{
			IdRole: currentRole.IdRole,
			IdPath: currentPath.IdPath,
			AtUnix: pdb.AtUnix{
				CreatedAt: tnowUnix,
				UpdatedAt: tnowUnix,
				DeletedAt: 0,
			},
		}

		newPolicy.IdPolicy, err = algouid.NewUUID()
		if err != nil {
			createPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if err = a.infra.PolicyRepository().Create(tranCtx, newPolicy); err != nil {
			createPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}

		return nil
	}); err != nil {
		return createPolicyResponse, errors.WithStack(err)
	}
	createPolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return createPolicyResponse, nil
}

func (a *authBackofficeServiceImpl) UpdatePolicy(ctx context.Context, updatePolicyRequest *connect.Request[authv1.BackofficeAuthServiceUpdatePolicyRequest]) (updatePolicyResponse *connect.Response[authv1.BackofficeAuthServiceUpdatePolicyResponse], err error) {
	updatePolicyResponse = connect.NewResponse[authv1.BackofficeAuthServiceUpdatePolicyResponse](&authv1.BackofficeAuthServiceUpdatePolicyResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	if err = updatePolicyValidateRequest(updatePolicyRequest.Msg); err != nil {
		updatePolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		updatePolicyResponse.Msg.Error.Message = err.Error()
		return updatePolicyResponse, errors.WithStack(err)
	}
	var (
		requestUpdatePolicy = updatePolicyRequest.Msg
	)
	idAuthPolicy, err := algouid.FromHex(updatePolicyRequest.Msg.GetIdPolicy())
	if err != nil {
		updatePolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return updatePolicyResponse, errors.WithStack(err)
	}

	if err = a.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		listPolicyDao, err := a.infra.PolicyRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsPolicySelect](
			authdao.PolicySelectWithIdPolicy(idAuthPolicy),
		))
		if err != nil {
			updatePolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listPolicyDao) == 0 {
			updatePolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_POLICY_NOT_EXIST
			return autherr.ErrPolicyNotExist
		}
		currentPolicy := listPolicyDao[0]

		params := pdb.OptionParams[authdao.ParamsPolicyUpdate](
			authdao.PolicyUpdateWithIdPolicy(currentPolicy.IdPolicy),
			authdao.PolicyUpdateActiveState(currentPolicy.IsActive(), requestUpdatePolicy.GetState().GetIsActive()),
		)
		if !params.IsChange {
			updatePolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
			return nil
		}
		if err = a.infra.PolicyRepository().Update(tranCtx, params); err != nil {
			updatePolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return updatePolicyResponse, errors.WithStack(err)
	}
	updatePolicyResponse.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return updatePolicyResponse, nil
}

// InitTotp implements authv1connect.BackofficeAuthServiceHandler.
func (a *authBackofficeServiceImpl) InitTotp(ctx context.Context, request *connect.Request[authv1.BackofficeAuthServiceInitTotpRequest]) (response *connect.Response[authv1.BackofficeAuthServiceInitTotpResponse], err error) {
	response = connect.NewResponse[authv1.BackofficeAuthServiceInitTotpResponse](&authv1.BackofficeAuthServiceInitTotpResponse{
		Error: &errmsgv1.ErrorMessage{},
	})

	userCtx := usercontext.FromContext(ctx)
	if userCtx == nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.New("user context is nil")
	}
	idApp, err := algouid.FromHex(userCtx.IdAuthApp)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return response, errors.WithStack(err)
	}

	listAppDao, err := a.infra.AppRepository().Select(ctx, pdb.OptionParams[authdao.ParamsAppSelect](
		authdao.AppSelectWithIdApp(idApp),
		authdao.AppSelectWithActiveOrDeactivate(true, false),
	))
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	if len(listAppDao) == 0 {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, autherr.ErrAppNotExist
	}
	currentApp := listAppDao[0]

	idUser, err := algouid.FromHex(userCtx.IdAuthUser)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return response, errors.WithStack(err)
	}
	listUser, err := a.infra.User().UserRepository().Select(ctx, pdb.OptionParams[authdao.ParamsUserSelect](
		authdao.UserSelectWithIdUser(idUser),
		authdao.UserSelectWithActiveOrDeactivate(true, false),
		authdao.UserSelectWithPagination(0, 1),
	))
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	if len(listUser) == 0 {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, autherr.ErrUserNotExist
	}
	currentUser := listUser[0]
	domainSplit := strings.Split(currentApp.Domain, ".")
	issuer := currentApp.Domain
	if len(domainSplit) > 2 {
		issuer = strings.Join(domainSplit[len(domainSplit)-2:], ".")
	}
	key, err := a.infra.CommonFunction().GenerateTotp(issuer, currentUser.Email)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	base32String := key.Secret()
	image, err := key.Image(128, 128)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	base64, err := a.infra.CommonFunction().ConvertImageToBase64(image)
	if err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	response.Msg.QrCode = base64
	response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS

	if err = a.infra.TotpManager().SetTotpSecret(ctx, userCtx.IdAuthUser, totpredis.TotpInitialRedisValue{
		SecretKey: base32String,
	}); err != nil {
		response.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return response, errors.WithStack(err)
	}
	return response, nil
}

// RemoveTotp implements authv1connect.BackofficeAuthServiceHandler.
func (a *authBackofficeServiceImpl) RemoveTotp(ctx context.Context, req *connect.Request[authv1.BackofficeAuthServiceRemoveTotpRequest]) (res *connect.Response[authv1.BackofficeAuthServiceRemoveTotpResponse], err error) {
	res = connect.NewResponse[authv1.BackofficeAuthServiceRemoveTotpResponse](&authv1.BackofficeAuthServiceRemoveTotpResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	userCtx := usercontext.FromContext(ctx)
	if userCtx == nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.New("user context is nil")
	}
	idUser, err := algouid.FromHex(userCtx.IdAuthUser)
	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return res, errors.WithStack(err)
	}

	if err = a.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		// find app exist
		listAppDao, err := a.infra.User().UserTotpRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsUserTotpSelect](
			authdao.UserTotpSelectWithIdUserTotp(idUser),
		))
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}

		if len(listAppDao) == 0 {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		currentUser := listAppDao[0]
		paramUpdate := pdb.OptionParams[authdao.ParamsUserTotpUpdate](
			authdao.UserTotpUpdateActiveState(currentUser.IsActive(), false),
			authdao.UserTotpUpdateTotpType(currentUser.TotpType, currentUser.TotpType),
			authdao.UserTotpUpdateSecret(currentUser.Secret, currentUser.Secret),
			authdao.UserTotpUpdateWithidUserTotp(currentUser.IdUserTotp),
			authdao.UserTotpUpdateWithIdUser(currentUser.IdUser),
		)

		if err = a.infra.User().UserTotpRepository().Update(tranCtx, paramUpdate); err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}

		return nil
	}); err != nil {
		return res, errors.WithStack(err)
	}
	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return res, nil
}

// VerifyTotp implements authv1connect.BackofficeAuthServiceHandler.
func (a *authBackofficeServiceImpl) VerifyTotp(ctx context.Context, req *connect.Request[authv1.BackofficeAuthServiceVerifyTotpRequest]) (res *connect.Response[authv1.BackofficeAuthServiceVerifyTotpResponse], err error) {
	res = connect.NewResponse[authv1.BackofficeAuthServiceVerifyTotpResponse](&authv1.BackofficeAuthServiceVerifyTotpResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	userCtx := usercontext.FromContext(ctx)
	if userCtx == nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.New("user context is nil")
	}

	idUser, err := algouid.FromHex(userCtx.IdAuthUser)
	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return res, errors.WithStack(err)
	}

	redisValue, err := a.infra.TotpManager().GetTotpSecret(ctx, userCtx.IdAuthUser)
	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_TOTP_VERIFY_INCORRECT
		return res, errors.WithStack(err)
	}
	secret := redisValue.SecretKey
	if !totp.Validate(req.Msg.Totp, secret) {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_TOTP_VERIFY_INCORRECT
		return res, errors.New("totp incorrect")
	}
	var (
		tnowUnix = time.Now().Unix()
	)

	if err = a.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		// find app exist
		listUserTotpDao, err := a.infra.User().UserTotpRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsUserTotpSelect](
			authdao.UserTotpSelectWithIdUserTotp(idUser),
		))
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		newTotpUser := &authdao.UserTotp{
			IdUserTotp: idUser,
			IdUser:     idUser,
			TotpType:   authenum.TotpType2FA,
			Secret:     secret,
			AtUnix: pdb.AtUnix{
				CreatedAt: tnowUnix,
				UpdatedAt: tnowUnix,
				DeletedAt: 0,
			},
		}

		if listUserTotpDao == nil || len(listUserTotpDao) == 0 {
			if err = a.infra.User().UserTotpRepository().Create(tranCtx, newTotpUser); err != nil {
				res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
				return errors.WithStack(err)
			}
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
			return nil
		}
		currentUser := listUserTotpDao[0]
		paramUpdate := pdb.OptionParams[authdao.ParamsUserTotpUpdate](
			authdao.UserTotpUpdateTotpType(currentUser.TotpType, newTotpUser.TotpType),
			authdao.UserTotpUpdateSecret(currentUser.Secret, newTotpUser.Secret),
			authdao.UserTotpUpdateWithidUserTotp(newTotpUser.IdUserTotp),
			authdao.UserTotpUpdateWithIdUser(newTotpUser.IdUser),
			authdao.UserTotpUpdateActiveState(currentUser.IsActive(), true),
		)

		if err = a.infra.User().UserTotpRepository().Update(tranCtx, paramUpdate); err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return res, errors.WithStack(err)
	}

	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return res, nil
}

func (m *authBackofficeServiceImpl) FetchConfigMail(ctx context.Context, c *connect.Request[authv1.BackofficeAuthServiceFetchConfigMailRequest]) (res *connect.Response[authv1.BackofficeAuthServiceFetchConfigMailResponse], err error) {
	//TODO implement me
	res = connect.NewResponse[authv1.BackofficeAuthServiceFetchConfigMailResponse](&authv1.BackofficeAuthServiceFetchConfigMailResponse{
		Items: make([]*authv1.ConfigMailModel, 0),
		Error: &errmsgv1.ErrorMessage{},
		Pagination: &utilsv1.PaginationResponse{
			CurrentPage: 0,
			PageSize:    0,
			Total:       0,
			TotalPages:  0,
		},
	})
	var (
		requestFetch                        = c.Msg
		pageSize, pageNumber, offset, limit = rpcutils.ToOffsetLimit(requestFetch.GetPagination().GetPageSize(), requestFetch.GetPagination().GetPageNumber())
		total                               int64
	)
	defer func() {
		res.Msg.Pagination.PageSize = pageSize
		res.Msg.Pagination.CurrentPage = pageNumber
		res.Msg.Pagination.Total = total
		res.Msg.Pagination.TotalPages = rpcutils.ToTotalPage(pageSize, total)
	}()

	params := pdb.OptionParams[authdao.ParamsConfigMailSelect](
		authdao.ConfigMailSelectWithSmtpServerAddress(requestFetch.GetSmtpServerAddress()),
		authdao.ConfigMailSelectWithAuthUsername(requestFetch.GetAuthUsername()),
		authdao.ConfigMailSelectWithSenderEmail(requestFetch.GetSenderEmail()),

		authdao.ConfigMailSelectWithPagination(offset, limit),
		authdao.ConfigMailSelectWithActiveOrDeactivate(requestFetch.GetState().GetIsActive(), requestFetch.GetState().GetIsDeactivate()),
	)

	total, err = m.infra.ConfigMailRepository().Count(ctx, params)
	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}
	if total == 0 {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
		return res, nil
	}
	listDao, err := m.infra.ConfigMailRepository().Select(ctx, params)
	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}
	listDto := make([]*authv1.ConfigMailModel, 0, len(listDao))
	for _, daoItem := range listDao {
		dtoItem := &authv1.ConfigMailModel{
			IdConfigMail:      daoItem.IdConfigMail.Hex(),
			IdAuthApp:         daoItem.IdAuthApp.Hex(),
			SmtpServerAddress: daoItem.SmtpServerAddress,
			SmtpServerPort:    daoItem.SmtpServerPort,
			AuthUsername:      daoItem.AuthUsername,
			AuthPassword:      daoItem.AuthPassword,
			SenderEmail:       daoItem.SenderEmail,
			IsActive:          daoItem.IsActive(),
		}
		listDto = append(listDto, dtoItem)
	}
	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	res.Msg.Items = listDto
	return res, nil
}

func (m *authBackofficeServiceImpl) CreateConfigMail(ctx context.Context, c *connect.Request[authv1.BackofficeAuthServiceCreateConfigMailRequest]) (res *connect.Response[authv1.BackofficeAuthServiceCreateConfigMailResponse], err error) {
	//TODO implement me
	res = connect.NewResponse[authv1.BackofficeAuthServiceCreateConfigMailResponse](&authv1.BackofficeAuthServiceCreateConfigMailResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	// if err = createConfigMailValidate(c.Msg); err != nil {
	//     res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
	//     return res, errors.WithStack(err)
	// }

	item := c.Msg
	if err = m.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {

		listAppExistDao, err := m.infra.AppRepository().Count(tranCtx, pdb.OptionParams[authdao.ParamsAppSelect](
			authdao.AppSelectWithIdApp(algouid.FromHexOrNil(item.GetIdAuthApp())),
		))
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if listAppExistDao == 0 {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
			return errors.New("app id not exist")
		}

		options := pdb.OptionParams[authdao.ParamsConfigMailSelect](
			authdao.ConfigMailSelectWithIdAuthApp(algouid.FromHexOrNil(item.GetIdAuthApp())),
		)

		listExistDao, err := m.infra.ConfigMailRepository().Count(tranCtx, options)
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if listExistDao > 0 {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
			return errors.New("app id exist")
		}

		tnow := time.Now().Unix()

		newDao := &authdao.ConfigMail{
			IdAuthApp:         algouid.FromHexOrNil(item.GetIdAuthApp()),
			SmtpServerAddress: item.GetSmtpServerAddress(),
			SmtpServerPort:    item.GetSmtpServerPort(),
			AuthUsername:      item.GetAuthUsername(),
			AuthPassword:      item.GetAuthPassword(),
			SenderEmail:       item.GetSenderEmail(),
			AtUnix: pdb.AtUnix{
				CreatedAt: tnow,
				UpdatedAt: tnow,
				DeletedAt: 0,
			},
		}
		newDao.IdConfigMail, err = algouid.NewUUID()
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}

		if err = m.infra.ConfigMailRepository().Create(tranCtx, newDao); err != nil {
			return errors.WithStack(err)
		}

		return nil
	}); err != nil {
		return res, errors.WithStack(err)
	}

	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return res, nil

}

func (m *authBackofficeServiceImpl) UpdateConfigMail(ctx context.Context, c *connect.Request[authv1.BackofficeAuthServiceUpdateConfigMailRequest]) (res *connect.Response[authv1.BackofficeAuthServiceUpdateConfigMailResponse], err error) {
	//TODO implement me
	res = connect.NewResponse[authv1.BackofficeAuthServiceUpdateConfigMailResponse](&authv1.BackofficeAuthServiceUpdateConfigMailResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	// if err = updateConfigMailValidate(c.Msg); err != nil {
	//     res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
	//     return res, errors.WithStack(err)
	// }
	if err = m.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		// find app exist
		options := pdb.OptionParams[authdao.ParamsConfigMailSelect](
			authdao.ConfigMailSelectWithIdAuthApp(algouid.FromHexOrNil(c.Msg.GetIdAuthApp())),
		)

		listAppExistDao, err := m.infra.ConfigMailRepository().Count(tranCtx, options)
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if listAppExistDao == 0 {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
			return errors.New("app id not exist")
		}

		listExistDao, err := m.infra.ConfigMailRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsConfigMailSelect](
			authdao.ConfigMailSelectWithIdConfigMail(algouid.FromHexOrNil(c.Msg.GetIdConfigMail())),
		))
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listExistDao) == 0 {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_MERCHANT_UPDATE_CONFIG_MAIL_NOT_EXIST
			return autherr.ErrConfigMailNotFound
		}
		current := listExistDao[0]

		paramUpdate := pdb.OptionParams[authdao.ParamsConfigMailUpdate](
			authdao.ConfigMailUpdateActiveState(current.IsActive(), c.Msg.GetState().GetIsActive()),
			authdao.ConfigMailUpdateIdAuthApp(current.IdAuthApp, current.IdAuthApp),
			authdao.ConfigMailUpdateSmtpServerAddress(current.SmtpServerAddress, c.Msg.GetSmtpServerAddress()),
			authdao.ConfigMailUpdateSmtpServerPort(current.SmtpServerPort, c.Msg.GetSmtpServerPort()),
			authdao.ConfigMailUpdateAuthUsername(current.AuthUsername, c.Msg.GetAuthUsername()),
			authdao.ConfigMailUpdateAuthPassword(current.AuthPassword, c.Msg.GetAuthPassword()),
			authdao.ConfigMailUpdateSenderEmail(current.SenderEmail, c.Msg.GetSenderEmail()),
		)
		paramUpdate.IdConfigMail = current.IdConfigMail

		if err = m.infra.ConfigMailRepository().Update(tranCtx, paramUpdate); err != nil {
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return res, errors.WithStack(err)
	}
	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return res, nil

}

func (m *authBackofficeServiceImpl) FetchConfigTemplateEmail(ctx context.Context, c *connect.Request[authv1.BackofficeAuthServiceFetchConfigTemplateEmailRequest]) (res *connect.Response[authv1.BackofficeAuthServiceFetchConfigTemplateEmailResponse], err error) {
	//TODO implement me
	res = connect.NewResponse[authv1.BackofficeAuthServiceFetchConfigTemplateEmailResponse](&authv1.BackofficeAuthServiceFetchConfigTemplateEmailResponse{
		Items: make([]*authv1.ConfigTemplateEmailModel, 0),
		Error: &errmsgv1.ErrorMessage{},
		Pagination: &utilsv1.PaginationResponse{
			CurrentPage: 0,
			PageSize:    0,
			Total:       0,
			TotalPages:  0,
		},
	})
	var (
		requestFetch                        = c.Msg
		pageSize, pageNumber, offset, limit = rpcutils.ToOffsetLimit(requestFetch.GetPagination().GetPageSize(), requestFetch.GetPagination().GetPageNumber())
		total                               int64
	)
	defer func() {
		res.Msg.Pagination.PageSize = pageSize
		res.Msg.Pagination.CurrentPage = pageNumber
		res.Msg.Pagination.Total = total
		res.Msg.Pagination.TotalPages = rpcutils.ToTotalPage(pageSize, total)
	}()

	params := pdb.OptionParams[authdao.ParamsConfigTemplateEmailSelect](
		authdao.ConfigTemplateEmailSelectWithName(authinfra.ConvertTemplateEmailTypeToTemplateEmailDao(requestFetch.GetName())),
		authdao.ConfigTemplateEmailSelectWithIdAuthApp(algouid.FromHexOrNil(requestFetch.GetIdAuthApp())),
		authdao.ConfigTemplateEmailSelectWithPagination(offset, limit),
		authdao.ConfigTemplateEmailSelectWithActiveOrDeactivate(requestFetch.GetState().GetIsActive(), requestFetch.GetState().GetIsDeactivate()),
	)

	total, err = m.infra.ConfigTemplateEmailRepository().Count(ctx, params)
	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}
	if total == 0 {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
		return res, nil
	}
	listDao, err := m.infra.ConfigTemplateEmailRepository().Select(ctx, params)
	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}
	listDto := make([]*authv1.ConfigTemplateEmailModel, 0, len(listDao))
	for _, daoItem := range listDao {
		dtoItem := &authv1.ConfigTemplateEmailModel{
			IdConfigTemplateEmail: daoItem.IdConfigTemplateEmail.Hex(),
			IdAuthApp:             daoItem.IdAuthApp.Hex(),
			Name:                  authinfra.ConvertTemplateEmailTypeDaoToTemplateEmail(daoItem.Name),
			Content:               daoItem.Content,
			IsActive:              daoItem.IsActive(),
		}
		listDto = append(listDto, dtoItem)
	}
	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	res.Msg.Items = listDto
	return res, nil
}

func (m *authBackofficeServiceImpl) CreateConfigTemplateEmail(ctx context.Context, c *connect.Request[authv1.BackofficeAuthServiceCreateConfigTemplateEmailRequest]) (res *connect.Response[authv1.BackofficeAuthServiceCreateConfigTemplateEmailResponse], err error) {
	//TODO implement me
	res = connect.NewResponse[authv1.BackofficeAuthServiceCreateConfigTemplateEmailResponse](&authv1.BackofficeAuthServiceCreateConfigTemplateEmailResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	if err = createConfigTemplateEmailValidate(c.Msg); err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return res, errors.WithStack(err)
	}

	item := c.Msg
	if err = m.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		listAppExistDao, err := m.infra.AppRepository().Count(tranCtx, pdb.OptionParams[authdao.ParamsAppSelect](
			authdao.AppSelectWithIdApp(algouid.FromHexOrNil(item.GetIdAuthApp())),
		))
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if listAppExistDao == 0 {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
			return errors.New("app id not exist")
		}

		listExistDao, err := m.infra.ConfigTemplateEmailRepository().Count(tranCtx, pdb.OptionParams[authdao.ParamsConfigTemplateEmailSelect](
			authdao.ConfigTemplateEmailSelectWithIdAuthApp(algouid.FromHexOrNil(item.GetIdAuthApp())),
			authdao.ConfigTemplateEmailSelectWithName(authinfra.ConvertTemplateEmailTypeToTemplateEmailDao(item.GetName())),
		))
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if listExistDao > 0 {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_CONFIG_EXIST
			return errors.New("config exist")
		}

		tnow := time.Now().Unix()

		newDao := &authdao.ConfigTemplateEmail{
			IdAuthApp: algouid.FromHexOrNil(item.GetIdAuthApp()),
			Name:      authinfra.ConvertTemplateEmailTypeToTemplateEmailDao(item.GetName()),
			Content:   item.GetContent(),
			AtUnix: pdb.AtUnix{
				CreatedAt: tnow,
				UpdatedAt: tnow,
				DeletedAt: 0,
			},
		}
		newDao.IdConfigTemplateEmail, err = algouid.NewUUID()
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}

		if err = m.infra.ConfigTemplateEmailRepository().Create(tranCtx, newDao); err != nil {
			return errors.WithStack(err)
		}

		return nil
	}); err != nil {
		return res, errors.WithStack(err)
	}

	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return res, nil

}

func (m *authBackofficeServiceImpl) UpdateConfigTemplateEmail(ctx context.Context, c *connect.Request[authv1.BackofficeAuthServiceUpdateConfigTemplateEmailRequest]) (res *connect.Response[authv1.BackofficeAuthServiceUpdateConfigTemplateEmailResponse], err error) {
	//TODO implement me
	res = connect.NewResponse[authv1.BackofficeAuthServiceUpdateConfigTemplateEmailResponse](&authv1.BackofficeAuthServiceUpdateConfigTemplateEmailResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	if err = updateConfigTemplateEmailValidate(c.Msg); err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return res, errors.WithStack(err)
	}
	if err = m.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		// find app exist
		listExistDao, err := m.infra.ConfigTemplateEmailRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsConfigTemplateEmailSelect](
			authdao.ConfigTemplateEmailSelectWithIdConfigTemplateEmail(algouid.FromHexOrNil(c.Msg.GetIdConfigTemplateEmail())),
		))
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listExistDao) == 0 {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
			return autherr.ErrNotExist
		}
		current := listExistDao[0]

		paramUpdate := pdb.OptionParams[authdao.ParamsConfigTemplateEmailUpdate](
			authdao.ConfigTemplateEmailUpdateActiveState(current.IsActive(), c.Msg.GetState().GetIsActive()),
			authdao.ConfigTemplateEmailUpdateIdAuthApp(current.IdAuthApp, current.IdAuthApp),
			authdao.ConfigTemplateEmailUpdateName(current.Name, current.Name),
			authdao.ConfigTemplateEmailUpdateContent(current.Content, c.Msg.GetContent()),
		)
		paramUpdate.IdConfigTemplateEmail = current.IdConfigTemplateEmail

		if err = m.infra.ConfigTemplateEmailRepository().Update(tranCtx, paramUpdate); err != nil {
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return res, errors.WithStack(err)
	}
	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return res, nil

}

func (m *authBackofficeServiceImpl) FetchOAuthConfig(ctx context.Context, c *connect.Request[authv1.BackofficeAuthServiceFetchOAuthConfigRequest]) (res *connect.Response[authv1.BackofficeAuthServiceFetchOAuthConfigResponse], err error) {
	//TODO implement me
	res = connect.NewResponse[authv1.BackofficeAuthServiceFetchOAuthConfigResponse](&authv1.BackofficeAuthServiceFetchOAuthConfigResponse{
		Items: make([]*authv1.OAuthConfigModel, 0),
		Error: &errmsgv1.ErrorMessage{},
		Pagination: &utilsv1.PaginationResponse{
			CurrentPage: 0,
			PageSize:    0,
			Total:       0,
			TotalPages:  0,
		},
	})
	var (
		requestFetch                        = c.Msg
		pageSize, pageNumber, offset, limit = rpcutils.ToOffsetLimit(requestFetch.GetPagination().GetPageSize(), requestFetch.GetPagination().GetPageNumber())
		total                               int64
	)
	defer func() {
		res.Msg.Pagination.PageSize = pageSize
		res.Msg.Pagination.CurrentPage = pageNumber
		res.Msg.Pagination.Total = total
		res.Msg.Pagination.TotalPages = rpcutils.ToTotalPage(pageSize, total)
	}()

	params := pdb.OptionParams[authdao.ParamsOAuthConfigSelect](
		authdao.OAuthConfigSelectWithIdAuthApp(algouid.FromHexOrNil(requestFetch.GetIdAuthApp())),
		authdao.OAuthConfigSelectWithPagination(offset, limit),
		authdao.OAuthConfigSelectWithActiveOrDeactivate(requestFetch.GetState().GetIsActive(), requestFetch.GetState().GetIsDeactivate()),
	)

	total, err = m.infra.OAuthConfigRepository().Count(ctx, params)
	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}
	if total == 0 {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
		return res, nil
	}
	listDao, err := m.infra.OAuthConfigRepository().Select(ctx, params)
	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}
	listDto := make([]*authv1.OAuthConfigModel, 0, len(listDao))
	for _, daoItem := range listDao {
		dtoItem := &authv1.OAuthConfigModel{
			IdOAuthConfig: daoItem.IdOAuthConfig.Hex(),
			IdAuthApp:     daoItem.IdAuthApp.Hex(),
			ClientId:      daoItem.ClientId,
			ClientSecret:  daoItem.ClientSecret,
			IsActive:      daoItem.IsActive(),
		}
		listDto = append(listDto, dtoItem)
	}
	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	res.Msg.Items = listDto
	return res, nil
}

func (m *authBackofficeServiceImpl) CreateOAuthConfig(ctx context.Context, c *connect.Request[authv1.BackofficeAuthServiceCreateOAuthConfigRequest]) (res *connect.Response[authv1.BackofficeAuthServiceCreateOAuthConfigResponse], err error) {
	//TODO implement me
	res = connect.NewResponse[authv1.BackofficeAuthServiceCreateOAuthConfigResponse](&authv1.BackofficeAuthServiceCreateOAuthConfigResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	if err = createOAuthConfigValidate(c.Msg); err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return res, errors.WithStack(err)
	}

	item := c.Msg
	if err = m.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		options := pdb.OptionParams[authdao.ParamsOAuthConfigSelect](
			authdao.OAuthConfigSelectWithIdAuthApp(algouid.FromHexOrNil(item.GetIdAuthApp())),
		)

		listExistDao, err := m.infra.OAuthConfigRepository().Count(tranCtx, options)
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if listExistDao > 0 {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_CONFIG_EXIST
			return errors.New("OAuthConfig exist")
		}
		tnow := time.Now().Unix()

		newDao := &authdao.OAuthConfig{
			IdAuthApp:    algouid.FromHexOrNil(item.GetIdAuthApp()),
			ClientId:     item.GetClientId(),
			ClientSecret: item.GetClientSecret(),
			AtUnix: pdb.AtUnix{
				CreatedAt: tnow,
				UpdatedAt: tnow,
				DeletedAt: 0,
			},
		}
		newDao.IdOAuthConfig, err = algouid.NewUUID()
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}

		if err = m.infra.OAuthConfigRepository().Create(tranCtx, newDao); err != nil {
			return errors.WithStack(err)
		}

		return nil
	}); err != nil {
		return res, errors.WithStack(err)
	}

	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return res, nil

}

func (m *authBackofficeServiceImpl) UpdateOAuthConfig(ctx context.Context, c *connect.Request[authv1.BackofficeAuthServiceUpdateOAuthConfigRequest]) (res *connect.Response[authv1.BackofficeAuthServiceUpdateOAuthConfigResponse], err error) {
	//TODO implement me
	res = connect.NewResponse[authv1.BackofficeAuthServiceUpdateOAuthConfigResponse](&authv1.BackofficeAuthServiceUpdateOAuthConfigResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	if err = updateOAuthConfigValidate(c.Msg); err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return res, errors.WithStack(err)
	}
	if err = m.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		// find app exist
		listExistDao, err := m.infra.OAuthConfigRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsOAuthConfigSelect](
			authdao.OAuthConfigSelectWithIdOAuthConfig(algouid.FromHexOrNil(c.Msg.GetIdOAuthConfig())),
		))
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listExistDao) == 0 {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_CONFIG_NOT_EXIST
			return autherr.ErrNotExist
		}
		current := listExistDao[0]

		paramUpdate := pdb.OptionParams[authdao.ParamsOAuthConfigUpdate](
			authdao.OAuthConfigUpdateActiveState(current.IsActive(), c.Msg.GetState().GetIsActive()),
			authdao.OAuthConfigUpdateIdAuthApp(current.IdAuthApp, algouid.FromHexOrNil(c.Msg.GetIdAuthApp())),
			authdao.OAuthConfigUpdateClientId(current.ClientId, c.Msg.GetClientId()),
			authdao.OAuthConfigUpdateClientSecret(current.ClientSecret, c.Msg.GetClientSecret()),
		)
		paramUpdate.IdOAuthConfig = current.IdOAuthConfig

		if err = m.infra.OAuthConfigRepository().Update(tranCtx, paramUpdate); err != nil {
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return res, errors.WithStack(err)
	}
	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return res, nil

}

func (m *authBackofficeServiceImpl) FetchCompany(ctx context.Context, c *connect.Request[authv1.BackofficeAuthServiceFetchCompanyRequest]) (res *connect.Response[authv1.BackofficeAuthServiceFetchCompanyResponse], err error) {
	//TODO implement me
	res = connect.NewResponse[authv1.BackofficeAuthServiceFetchCompanyResponse](&authv1.BackofficeAuthServiceFetchCompanyResponse{
		Items: make([]*authv1.CompanyModel, 0),
		Error: &errmsgv1.ErrorMessage{},
		Pagination: &utilsv1.PaginationResponse{
			CurrentPage: 0,
			PageSize:    0,
			Total:       0,
			TotalPages:  0,
		},
	})
	var (
		requestFetch                        = c.Msg
		pageSize, pageNumber, offset, limit = rpcutils.ToOffsetLimit(requestFetch.GetPagination().GetPageSize(), requestFetch.GetPagination().GetPageNumber())
		total                               int64
	)
	defer func() {
		res.Msg.Pagination.PageSize = pageSize
		res.Msg.Pagination.CurrentPage = pageNumber
		res.Msg.Pagination.Total = total
		res.Msg.Pagination.TotalPages = rpcutils.ToTotalPage(pageSize, total)
	}()

	params := pdb.OptionParams[authdao.ParamsCompanySelect](
		authdao.CompanySelectWithName(requestFetch.GetName()),
		authdao.CompanySelectWithStreet(requestFetch.GetStreet()),
		authdao.CompanySelectWithState(requestFetch.GetIdState()),
		authdao.CompanySelectWithCity(requestFetch.GetIdCity()),
		authdao.CompanySelectWithIdAuthApp(algouid.FromHexOrNil(requestFetch.GetIdAuthApp())),
		authdao.CompanySelectWithCountry(requestFetch.GetIdCountry()),
		authdao.CompanySelectWithMst(requestFetch.GetMst()),
		authdao.CompanySelectWithVerifyOrNotVerify(requestFetch.GetVerify().GetIsTrue(), requestFetch.GetVerify().GetIsFalse()),
		authdao.CompanySelectWithPagination(offset, limit),
		authdao.CompanySelectWithActiveOrDeactivate(requestFetch.GetState().GetIsActive(), requestFetch.GetState().GetIsDeactivate()),
	)

	total, err = m.infra.CompanyRepository().Count(ctx, params)
	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}
	if total == 0 {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
		return res, nil
	}
	listDao, err := m.infra.CompanyRepository().Select(ctx, params)
	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
		return res, errors.WithStack(err)
	}
	listDto := make([]*authv1.CompanyModel, 0, len(listDao))
	for _, daoItem := range listDao {
		dtoItem := &authv1.CompanyModel{
			IdCompany: daoItem.IdCompany.Hex(),
			Name:      daoItem.Name,
			Logo:      daoItem.Logo,
			Street:    daoItem.Street,
			IdState:   daoItem.IdState.V,
			IdCity:    daoItem.IdCity.V,
			IdCountry: daoItem.IdCountry.V,
			Verify:    daoItem.Verify,
			Mst:       daoItem.Mst,
			IdAuthApp: daoItem.IdAuthApp.V.Hex(),
			IsActive:  daoItem.IsActive(),
		}
		listDto = append(listDto, dtoItem)
	}
	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	res.Msg.Items = listDto
	return res, nil
}

func (m *authBackofficeServiceImpl) CreateCompany(ctx context.Context, c *connect.Request[authv1.BackofficeAuthServiceCreateCompanyRequest]) (res *connect.Response[authv1.BackofficeAuthServiceCreateCompanyResponse], err error) {
	//TODO implement me
	res = connect.NewResponse[authv1.BackofficeAuthServiceCreateCompanyResponse](&authv1.BackofficeAuthServiceCreateCompanyResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	if err = createCompanyValidate(c.Msg); err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return res, errors.WithStack(err)
	}
	appUUID, err := algouid.FromHex(c.Msg.GetIdAuthApp())
	if err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return res, errors.WithStack(err)
	}
	item := c.Msg
	if err = m.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {

		listAppDao, err := m.infra.AppRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsAppSelect](
			authdao.AppSelectWithIdApp(appUUID),
		))
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listAppDao) == 0 {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_APP_NOT_EXIST
			return autherr.ErrAppNotExist
		}

		options := pdb.OptionParams[authdao.ParamsCompanySelect](
			authdao.CompanySelectWithMst(item.GetMst()),
			authdao.CompanySelectWithIdAuthApp(appUUID),
		)

		listExistDao, err := m.infra.CompanyRepository().Count(tranCtx, options)
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if listExistDao > 0 {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_AUTH_CONFIG_EXIST
			return errors.New("company exist")
		}
		tnow := time.Now().Unix()
		validResponse, err := m.infra.ExternalService().MiscInternalClient().ValidPaymentAddress(tranCtx, connect.NewRequest(&paymentaddressv1.InternalPaymentAddressServiceValidPaymentAddressRequest{
			IdCountry: c.Msg.GetIdCountry(),
			IdState:   c.Msg.GetIdState(),
			IdCity:    c.Msg.GetIdCity(),
		}))

		if err != nil || validResponse == nil {

			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
			return errors.WithStack(err)
		}
		if validResponse.Msg.Error.Code != errmsgv1.ErrorCode_ERROR_CODE_SUCCESS {
			res.Msg.Error.Code = validResponse.Msg.Error.Code
			return errors.WithStack(err)
		}
		newDao := &authdao.Company{
			Name:      item.GetName(),
			Logo:      item.GetLogo(),
			Street:    item.GetStreet(),
			IdAuthApp: sql.Null[algouid.UUID]{appUUID, true},
			IdState:   sql.Null[int64]{item.GetIdState(), true},
			IdCity:    sql.Null[int64]{item.GetIdCity(), true},
			IdCountry: sql.Null[int64]{item.GetIdCountry(), true},
			Mst:       item.GetMst(),
			Verify:    false,
			AtUnix: pdb.AtUnix{
				CreatedAt: tnow,
				UpdatedAt: tnow,
				DeletedAt: 0,
			},
		}
		newDao.IdCompany, err = algouid.NewUUID()
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}

		if err = m.infra.CompanyRepository().Create(tranCtx, newDao); err != nil {
			return errors.WithStack(err)
		}

		return nil
	}); err != nil {
		return res, errors.WithStack(err)
	}

	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return res, nil

}

func (m *authBackofficeServiceImpl) UpdateCompany(ctx context.Context, c *connect.Request[authv1.BackofficeAuthServiceUpdateCompanyRequest]) (res *connect.Response[authv1.BackofficeAuthServiceUpdateCompanyResponse], err error) {
	//TODO implement me
	res = connect.NewResponse[authv1.BackofficeAuthServiceUpdateCompanyResponse](&authv1.BackofficeAuthServiceUpdateCompanyResponse{
		Error: &errmsgv1.ErrorMessage{},
	})
	if err = updateCompanyValidate(c.Msg); err != nil {
		res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
		return res, errors.WithStack(err)
	}
	if err = m.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		// find app exist
		listExistDao, err := m.infra.CompanyRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsCompanySelect](
			authdao.CompanySelectWithIdCompany(algouid.FromHexOrNil(c.Msg.GetIdCompany())),
		))
		if err != nil {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
			return errors.WithStack(err)
		}
		if len(listExistDao) == 0 {
			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_MERCHANT_UPDATE_COMPANY_NOT_EXIST
			return autherr.ErrCompanyNotFound
		}
		current := listExistDao[0]
		if c.Msg.GetMst() != current.Mst {
			listExistMstDao, err := m.infra.CompanyRepository().Select(tranCtx, pdb.OptionParams[authdao.ParamsCompanySelect](
				authdao.CompanySelectWithMst(c.Msg.GetMst()),
				authdao.CompanySelectWithIdAuthApp(current.IdAuthApp.V),
			))
			if err != nil {
				res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR
				return errors.WithStack(err)
			}
			if len(listExistMstDao) > 0 {
				res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_MERCHANT_UPDATE_COMPANY_MEST_EXIST
				return autherr.ErrMstExist
			}
		}

		validResponse, err := m.infra.ExternalService().MiscInternalClient().ValidPaymentAddress(tranCtx, connect.NewRequest(&paymentaddressv1.InternalPaymentAddressServiceValidPaymentAddressRequest{
			IdCountry: c.Msg.GetIdCountry(),
			IdState:   c.Msg.GetIdState(),
			IdCity:    c.Msg.GetIdCity(),
		}))

		if err != nil || validResponse == nil {

			res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_INVALID_REQUEST
			return errors.WithStack(err)
		}
		if validResponse.Msg.Error.Code != errmsgv1.ErrorCode_ERROR_CODE_SUCCESS {
			res.Msg.Error.Code = validResponse.Msg.Error.Code
			return errors.WithStack(err)
		}

		paramUpdate := pdb.OptionParams[authdao.ParamsCompanyUpdate](
			authdao.CompanyUpdateActiveState(current.IsActive(), c.Msg.GetState().GetIsActive()),
			authdao.CompanyUpdateStreet(current.Street, c.Msg.GetStreet()),
			authdao.CompanyUpdateState(current.IdState.V, c.Msg.GetIdState()),
			authdao.CompanyUpdateCity(current.IdCity.V, c.Msg.GetIdCity()),
			authdao.CompanyUpdateCountry(current.IdCountry.V, c.Msg.GetIdCountry()),
			authdao.CompanyUpdateMst(current.Mst, c.Msg.GetMst()),
			authdao.CompanyUpdateVerify(c.Msg.GetVerify()),
			authdao.CompanyUpdateLogo(current.Logo, c.Msg.GetLogo()),
			authdao.CompanyUpdateName(current.Name, c.Msg.GetName()),
		)
		paramUpdate.IdCompany = current.IdCompany

		if err = m.infra.CompanyRepository().Update(tranCtx, paramUpdate); err != nil {
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return res, errors.WithStack(err)
	}
	res.Msg.Error.Code = errmsgv1.ErrorCode_ERROR_CODE_SUCCESS
	return res, nil
}
