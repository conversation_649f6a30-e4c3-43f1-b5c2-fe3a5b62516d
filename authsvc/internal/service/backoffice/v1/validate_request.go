package backofficev1svc

import (
	"html"
	"strings"

	algoenumv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	authv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1"
	"git.tmproxy-infra.com/algo/authsvc/internal/autherr"
)

func createAppValidateRequest(request *authv1.BackofficeAuthServiceCreateAppRequest) (err error) {
	request.Name = strings.Trim(request.GetName(), " ")
	request.Domain = strings.Trim(request.GetDomain(), " ")
	switch {
	case request.GetAppType() == algoenumv1.AppType_APP_TYPE_UNSPECIFIED:
		return autherr.AppTypeRequiredError
	case request.GetAppType() == algoenumv1.AppType_APP_TYPE_MERCHANT:
		return autherr.AppTypeNotSupportError
	case len(request.GetName()) == 0:
		return autherr.NameRequiredError
	case len(request.GetDomain()) == 0:
		return autherr.DomainRequiredError
	case request.GetTokenTtl() <= 0:
		return autherr.TokenTTLRequiredError

	}
	return nil
}

func updateAppValidateRequest(request *authv1.BackofficeAuthServiceUpdateAppRequest) (err error) {
	switch {
	case len(request.GetIdApp()) == 0:
		return autherr.IdAppRequiredError
	case request.GetTokenTtl() <= 0:
		return autherr.TokenTTLRequiredError
	case request.GetState().GetIsActive() == request.GetState().GetIsDeactivate():
		return autherr.StateIsActiveAndIsDeactivateNotBeTheSameRequiredErr
	case request.GetAppType() == algoenumv1.AppType_APP_TYPE_UNSPECIFIED:
		return autherr.AppTypeRequiredError
		//case request.GetAppType() == algoenumv1.AppType_APP_TYPE_MERCHANT:
		//	return autherr.AppTypeNotSupportError
	}
	return nil
}

func createUserValidateRequest(request *authv1.BackofficeAuthServiceCreateUserRequest) (err error) {
	request.IdApp = strings.TrimSpace(request.GetIdApp())
	request.Email = strings.TrimSpace(html.EscapeString(request.GetEmail()))
	request.FirstName = strings.TrimSpace(html.EscapeString(request.GetFirstName()))
	request.LastName = strings.TrimSpace(html.EscapeString(request.GetLastName()))
	request.PhoneNumber = strings.TrimSpace(html.EscapeString(request.GetPhoneNumber()))

	switch {
	case len(request.GetIdApp()) == 0:
		return autherr.IdAppRequiredError
	case len(request.GetEmail()) == 0:
		return autherr.EmailRequiredError
	case len(request.GetPassword()) == 0:
		return autherr.PasswordRequiredError
	case len(request.GetPassword()) < 8:
		return autherr.PasswordLengthMustBeGte8RequiredError
	case len(request.IdRole) == 0:
		return autherr.IdRoleRequiredError
	case len(request.GetFirstName()) == 0:
		return autherr.FirstNameRequiredError
	case len(request.GetLastName()) == 0:
		return autherr.LastNameRequiredError
	case len(request.GetPhoneNumber()) == 0:
		return autherr.PhoneNumberRequiredError
	}
	return nil
}

func updateUserValidateRequest(request *authv1.BackofficeAuthServiceUpdateUserRequest) (err error) {
	switch {
	case len(request.GetIdUser()) == 0:
		return autherr.IdUserRequiredError
	case len(request.GetIdRole()) == 0:
		return autherr.IdRoleRequiredError
	case request.GetState().GetIsActive() == request.GetState().GetIsDeactivate():
		return autherr.StateIsActiveAndIsDeactivateNotBeTheSameRequiredErr
	}
	return nil
}

func createRoleValidateRequest(request *authv1.BackofficeAuthServiceCreateRoleRequest) (err error) {
	switch {
	case len(request.GetName()) == 0:
		return autherr.NameRequiredError
	}
	return nil
}
func updateRoleValidateRequest(request *authv1.BackofficeAuthServiceUpdateRoleRequest) (err error) {
	switch {
	case len(request.GetIdRole()) == 0:
		return autherr.IdRoleRequiredError
	case len(request.GetName()) == 0:
		return autherr.NameRequiredError
	case request.GetState().GetIsActive() == request.GetState().GetIsDeactivate():
		return autherr.StateIsActiveAndIsDeactivateNotBeTheSameRequiredErr
	}
	return nil
}

func createServiceValidateRequest(request *authv1.BackofficeAuthServiceCreateServiceRequest) (err error) {
	switch {
	case len(request.GetName()) == 0:
		return autherr.NameRequiredError
	}
	return nil
}

func updateServiceValidateRequest(request *authv1.BackofficeAuthServiceUpdateServiceRequest) (err error) {
	switch {
	case len(request.GetIdService()) == 0:
		return autherr.IdServiceRequiredError
	case len(request.GetName()) == 0:
		return autherr.NameRequiredError
	case request.GetState().GetIsActive() == request.GetState().GetIsDeactivate():
		return autherr.StateIsActiveAndIsDeactivateNotBeTheSameRequiredErr
	}
	return nil
}

func createPathValidateRequest(request *authv1.BackofficeAuthServiceCreatePathRequest) (err error) {
	switch {
	case len(request.GetIdService()) == 0:
		return autherr.IdServiceRequiredError
	case len(request.GetAbsolutePath()) == 0:
		return autherr.AbsolutePathRequiredError
	}
	return nil
}

func updatePathValidateRequest(request *authv1.BackofficeAuthServiceUpdatePathRequest) (err error) {
	switch {
	case len(request.GetIdPath()) == 0:
		return autherr.IdPathRequiredError
	case len(request.GetAbsolutePath()) == 0:
		return autherr.AbsolutePathRequiredError
	case request.GetState().GetIsActive() == request.GetState().GetIsDeactivate():
		return autherr.StateIsActiveAndIsDeactivateNotBeTheSameRequiredErr
	}
	return nil
}
func createPolicyValidateRequest(request *authv1.BackofficeAuthServiceCreatePolicyRequest) (err error) {
	switch {
	case len(request.GetIdPath()) == 0:
		return autherr.IdPathRequiredError
	case len(request.GetIdRole()) == 0:
		return autherr.IdRoleRequiredError
	}
	return nil
}
func updatePolicyValidateRequest(request *authv1.BackofficeAuthServiceUpdatePolicyRequest) (err error) {
	switch {
	case len(request.GetIdPolicy()) == 0:
		return autherr.IdPolicyRequiredError
	case request.GetState().GetIsActive() == request.GetState().GetIsDeactivate():
		return autherr.StateIsActiveAndIsDeactivateNotBeTheSameRequiredErr
	}

	return nil
}
func createConfigTemplateEmailValidate(request *authv1.BackofficeAuthServiceCreateConfigTemplateEmailRequest) (err error) {
	switch {
	case len(request.GetIdAuthApp()) == 0:
		return autherr.IdAppRequiredError
	case request.GetName() == 0:
		return autherr.NameRequiredError
	}
	return nil
}
func updateConfigTemplateEmailValidate(request *authv1.BackofficeAuthServiceUpdateConfigTemplateEmailRequest) (err error) {
	switch {
	case len(request.GetIdConfigTemplateEmail()) == 0:
		return autherr.ConfigIdRequiredError
	}
	return nil
}

func updateRefCodeValidateRequest(request *authv1.BackofficeAuthServiceUpdateRefCodeRequest) (err error) {

	switch {
	case len(request.GetRefCode()) == 0:
		return autherr.RefCodeRequiredError
	case len(request.GetUserId()) == 0:
		return autherr.IdUserRequiredError
	}
	return nil
}
func updateOAuthConfigValidate(request *authv1.BackofficeAuthServiceUpdateOAuthConfigRequest) (err error) {

	switch {
	case len(request.GetClientId()) == 0:
		return autherr.ClientIdRequiredError
	case len(request.GetIdOAuthConfig()) == 0:
		return autherr.ConfigIdRequiredError
	case len(request.GetClientSecret()) == 0:
		return autherr.ClientSecretRequiredError
	}
	return nil
}

func createOAuthConfigValidate(request *authv1.BackofficeAuthServiceCreateOAuthConfigRequest) (err error) {
	switch {
	case len(request.GetClientId()) == 0:
		return autherr.ClientIdRequiredError
	case len(request.GetClientSecret()) == 0:
		return autherr.ClientSecretRequiredError
	case len(request.GetIdAuthApp()) == 0:
		return autherr.IdAppRequiredError
	}
	return nil
}

func createCompanyValidate(request *authv1.BackofficeAuthServiceCreateCompanyRequest) error {
	switch {
	case len(request.GetName()) == 0:
		return autherr.NameRequiredError
	case len(request.GetMst()) == 0:
		return autherr.MstRequiredError
	}
	return nil
}
func updateCompanyValidate(request *authv1.BackofficeAuthServiceUpdateCompanyRequest) error {
	switch {
	case len(request.GetName()) == 0:
		return autherr.NameRequiredError
	case len(request.GetMst()) == 0:
		return autherr.MstRequiredError
	case len(request.GetIdCompany()) == 0:
		return autherr.IdCompanyRequiredError
	}
	return nil
}
