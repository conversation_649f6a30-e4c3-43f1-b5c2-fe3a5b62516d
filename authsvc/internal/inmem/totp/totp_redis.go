package totpredis

import (
	"context"
	"strings"
	"time"

	"git.tmproxy-infra.com/algo/common/pkg/algoredis"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
)

const (
	totpRedisPath = "totp_redis"
)

type BackOfficeConfig struct {
	PublicKey  string `redis:"public_key"`
	PrivateKey string `redis:"private_key"`
	Issuer     string `redis:"issuer"`
	TokenTTL   int64  `redis:"token_ttl"`
}

type totpRedis struct {
	prefix      string
	redisClient algoredis.RedisReadWrite
}

func NewTotpRedis(prefix string, redisClient algoredis.RedisReadWrite) Service {
	return &totpRedis{prefix: prefix, redisClient: redisClient}
}
func (c *totpRedis) GetTotpSecret(ctx context.Context, idUser string) (*TotpInitialRedisValue, error) {
	reader, err := c.redisClient.Reader(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result := &TotpInitialRedisValue{}
	err = reader.HGetAll(ctx, c.withPrefix(totpRedisPath, idUser)).Scan(result)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return result, nil
}

func (c *totpRedis) SetTotpSecret(ctx context.Context, idUser string, value TotpInitialRedisValue) error {
	writer, err := c.redisClient.Writer(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	_, err = writer.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
		path := c.withPrefix(totpRedisPath, idUser)
		if err := pipe.HSet(ctx, path, value).Err(); err != nil {
			return errors.WithStack(err)
		}
		if err := pipe.Expire(ctx, path, 15*time.Minute).Err(); err != nil {
			return errors.WithStack(err)
		}

		return nil
	})
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}
func (c *totpRedis) withPrefix(key ...string) (outputKey string) {
	listStr := []string{c.prefix}
	listStr = append(listStr, key...)
	return strings.Join(listStr, ">")
}

type Service interface {
	GetTotpSecret(ctx context.Context, idUser string) (*TotpInitialRedisValue, error)
	SetTotpSecret(ctx context.Context, idUser string, value TotpInitialRedisValue) error
}
