package configredis

import (
	"context"
	"fmt"
	"git.tmproxy-infra.com/algo/common/pkg/algoredis"
	"github.com/pkg/errors"
	"time"
)

type ConfigTypeRedis string

const (
	MAIL_TEMPLATE ConfigTypeRedis = "mail_template"
)

type configRedis struct {
	prefix      string
	redisClient algoredis.RedisReadWrite
}

func (c configRedis) GetConfig(ctx context.Context, path ConfigTypeRedis, key string, idAuthApp string, config interface{}) (err error) {
	//TODO implement me
	reader, err := c.redisClient.Reader(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	var (
		keyCache = idAuthApp + ">" + string(path) + ">" + key
	)
	if err = reader.HGetAll(ctx, keyCache).Scan(config); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (c configRedis) SetConfig(ctx context.Context, path ConfigTypeRedis, key string, idAuthApp string, config interface{}) (err error) {
	//TODO implement me
	writer, err := c.redisClient.Writer(ctx)
	if err != nil {
		return errors.WithStack(err)
	}

	var (
		keyCache   = idAuthApp + ">" + string(path) + ">" + key
		exDuration = 10 * time.Minute
	)

	if err = writer.SetEx(ctx, c.withPrefix(keyCache), config, exDuration).Err(); err != nil {
		return errors.WithStack(err)
	}
	return nil
}
func (s *configRedis) withPrefix(key string) (outputKey string) {
	return fmt.Sprintf("%s>%s", s.prefix, key)
}

func NewConfigRedis(prefix string, redisClient algoredis.RedisReadWrite) Service {
	return &configRedis{prefix: prefix, redisClient: redisClient}
}

type Service interface {
	GetConfig(ctx context.Context, path ConfigTypeRedis, key string, idAuthApp string, config interface{}) (err error)
	SetConfig(ctx context.Context, path ConfigTypeRedis, key string, idAuthApp string, config interface{}) (err error)
}
