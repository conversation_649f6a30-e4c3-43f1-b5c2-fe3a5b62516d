package sessionredis

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"aidanwoods.dev/go-paseto"
	"git.tmproxy-infra.com/algo/authsvc/internal/autherr"
	"git.tmproxy-infra.com/algo/common/pkg/algoredis"
	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/usercontext"
	"github.com/goccy/go-json"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
)

const (
	listPasetoKey            = "list_paseto_key"
	prefixAuthApp            = "auth_app"
	prefixUidRefreshToken    = "uid_refresh_token"
	prefixRefreshToken       = "refresh_token"
	prefixIdUserRefreshToken = "id_user_refresh_token"
	prefixUidAccessToken     = "uid_access_token	"
	prefixIdUserAccessToken  = "id_user_access_token"

	_cuckooFilterPrefix      = "cuckoo_filter_refresh_token"
	PATH_OTP                 = "login_otp"
	PATH_COUNT_BY_IP         = "count_by_ip"
	PATH_OTP_FORGOT_PASSWORD = "otp_forgot_password"
	PATH_REVOKE_TOKEN        = "revoke_token"
)

type PasetoFooter struct {
	IdApp string `json:"ia"`
}

type PasetoConfig struct {
	PublicKey  string `redis:"public_key"`
	PrivateKey string `redis:"private_key"`
	Issuer     string `redis:"issuer"`
	TokenTTL   int64  `redis:"token_ttl"`
}

// sessionRedis SaveAsyncMetric Key
type sessionRedis struct {
	prefix      string
	redisClient algoredis.RedisReadWrite
}

func (s *sessionRedis) RevokeToken(ctx context.Context, token string, appId string) (err error) {
	var pathRevoke = s.withPrefix(PATH_REVOKE_TOKEN, appId)
	writer, err := s.redisClient.Writer(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	_, err = writer.CFInfo(ctx, pathRevoke).Result()
	if err != nil {
		_, err := writer.CFReserve(ctx, pathRevoke, 1000000).Result()
		if err != nil {
			return errors.WithStack(err)
		}
	}
	_, err = writer.CFAdd(ctx, pathRevoke, token).Result()
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *sessionRedis) IsTokenRevoked(ctx context.Context, token string, appId string) (res bool, err error) {
	reader, err := s.redisClient.Reader(ctx)
	if err != nil {
		return false, errors.WithStack(err)
	}
	var pathRevoke = s.withPrefix(PATH_REVOKE_TOKEN, appId)

	itemExists := reader.CFExists(ctx, pathRevoke, token)
	if itemExists.Err() != nil {
		return false, errors.WithStack(err)
	}
	return itemExists.Val(), nil
}

func (s *sessionRedis) SetCountByIp(ctx context.Context, ip string, count int, duration time.Duration) (err error) {
	writer, err := s.redisClient.Writer(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	_, err = writer.Set(ctx, s.withPrefix(PATH_COUNT_BY_IP, ip), count, duration).Result()
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *sessionRedis) CountByIp(ctx context.Context, ip string) (count int, err error) {
	//TODO implement me
	reader, err := s.redisClient.Reader(ctx)
	if err != nil {
		return 0, errors.WithStack(err)
	}

	countStr, err := reader.Get(ctx, s.withPrefix(PATH_COUNT_BY_IP, ip)).Result()
	if err != nil {
		err = s.SetCountByIp(ctx, ip, 1, 2*time.Minute)
		if err != nil {
			return 0, errors.WithStack(err)
		}
		return 1, nil
	}
	count, err = strconv.Atoi(countStr)
	return count, nil
}

func (s *sessionRedis) ResetCountByIp(ctx context.Context, ip string) (err error) {
	//TODO implement me
	writer, err := s.redisClient.Writer(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	_, err = writer.Del(ctx, s.withPrefix(PATH_COUNT_BY_IP, ip)).Result()
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func NewSessionRedis(prefix string, redisClient algoredis.RedisReadWrite) Service {
	return &sessionRedis{prefix: prefix, redisClient: redisClient}
}
func (s *sessionRedis) ClearAllPasetoConfig(ctx context.Context) (err error) {
	writer, err := s.redisClient.Writer(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	listIdAuthApp, err := writer.SMembers(ctx, s.withPrefix(listPasetoKey)).Result()
	if err != nil {
		return errors.WithStack(err)
	}
	for _, v := range listIdAuthApp {
		if err = writer.Del(ctx, v).Err(); err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

func (s *sessionRedis) SetPasetoConfig(ctx context.Context, idAuthApp string, public, private, issuer string, ttl int64) error {
	writer, err := s.redisClient.Writer(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	keyAuthApp := s.withPrefix(prefixAuthApp, idAuthApp)
	if _, err = writer.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
		if err = writer.SAdd(ctx, s.withPrefix(listPasetoKey), keyAuthApp).Err(); err != nil {
			return errors.WithStack(err)
		}
		if err = pipe.HMSet(ctx, keyAuthApp, &PasetoConfig{
			PublicKey:  public,
			PrivateKey: private,
			Issuer:     issuer,
			TokenTTL:   ttl,
		}).Err(); err != nil {
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return errors.WithStack(err)
	}
	if err = writer.CFInfo(ctx, s.cuckooFilterByApp(idAuthApp)).Err(); err != nil {
		err = writer.CFReserve(ctx, s.cuckooFilterByApp(idAuthApp), 200000).Err()
		if err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

type Service interface {
	GenerateToken(ctx context.Context, idAuthApp string, params *usercontext.UserContext) (token string, err error)
	RemoveToken(ctx context.Context, userID string) (err error)
	Verify(ctx context.Context, token string) (userCtx *usercontext.UserContext, err error)
	SetPasetoConfig(ctx context.Context, idAuthApp string, public, private, issuer string, ttl int64) error
	ClearAllPasetoConfig(ctx context.Context) (err error)
	VerifyRefreshToken(ctx context.Context, token string) (userCtx *usercontext.UserContext, uuid string, err error)
	GenerateRefreshToken(ctx context.Context, idAuthApp string, idDevice string, params *usercontext.UserContext) (refreshToken string, err error)
	RemoveRefreshToken(ctx context.Context, userID string, token string) (err error)
	InitOtp(ctx context.Context, key ...string) (otp string, err error)
	GetOtp(ctx context.Context, key ...string) (otp string, duration time.Duration, err error)
	RemoveOtp(ctx context.Context, key ...string) (err error)
	CountByIp(ctx context.Context, ip string) (count int, err error)
	SetCountByIp(ctx context.Context, ip string, count int, duration time.Duration) (err error)
	ResetCountByIp(ctx context.Context, ip string) (err error)
	RevokeToken(ctx context.Context, token string, appId string) (err error)
	IsTokenRevoked(ctx context.Context, token string, appId string) (res bool, err error)
}

func (s *sessionRedis) InitOtp(ctx context.Context, key ...string) (otp string, err error) {
	writer, err := s.redisClient.Writer(ctx)

	if err != nil {
		return "", errors.WithStack(err)
	}

	var (
		keyOtp     = s.withPrefix(key...)
		exDuration = 10 * time.Minute
	)
	otp = ""
	for i := 0; i < 6; i++ {
		otp += fmt.Sprintf("%d", rand.Intn(10))
	}
	if err = writer.SetEx(ctx, keyOtp, otp, exDuration).Err(); err != nil {
		return "", errors.WithStack(err)
	}
	return otp, nil
}

func (s *sessionRedis) GetOtp(ctx context.Context, key ...string) (otp string, duration time.Duration, err error) {
	var (
		keyOtp = s.withPrefix(key...)
	)
	reader, err := s.redisClient.Reader(ctx)
	if err != nil {
		return "", 0, errors.WithStack(err)
	}

	_, err = reader.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		otp, err = reader.Get(ctx, keyOtp).Result()
		if err != nil {
			return errors.WithStack(err)
		}
		duration, err = reader.TTL(ctx, keyOtp).Result()
		if err != nil {
			return errors.WithStack(err)
		}
		return nil
	})
	if err != nil {
		return "", 0, errors.WithStack(err)
	}
	return otp, duration, nil
}

func (s *sessionRedis) RemoveOtp(ctx context.Context, key ...string) (err error) {
	var (
		keyOtp = s.withPrefix(key...)
	)
	writer, err := s.redisClient.Writer(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	err = writer.Del(ctx, keyOtp).Err()
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *sessionRedis) GenerateToken(ctx context.Context, idAuthApp string, params *usercontext.UserContext) (token string, err error) {
	if err = s.RemoveToken(ctx, params.IdAuthUser); err != nil {
		return "", errors.WithStack(err)
	}
	randUid, err := algouid.NewUUID()
	if err != nil {
		return "", errors.WithStack(err)
	}
	appKey := s.withPrefix(prefixAuthApp, idAuthApp)

	appConfig := &PasetoConfig{}
	writer, err := s.redisClient.Writer(ctx)
	if err != nil {
		return "", errors.WithStack(err)
	}
	if err = writer.HGetAll(ctx, appKey).Scan(appConfig); err != nil {
		return "", errors.WithStack(err)
	}
	exDuration := time.Duration(appConfig.TokenTTL) * time.Second
	privateKey, err := paseto.NewV4AsymmetricSecretKeyFromHex(appConfig.PrivateKey)
	if err != nil {
		return "", errors.WithStack(err)
	}

	pasetoFooter, err := json.Marshal(PasetoFooter{IdApp: idAuthApp})
	if err != nil {
		return "", errors.WithStack(err)
	}
	tokenCreate := paseto.NewToken()
	tokenCreate.SetIssuer(idAuthApp)
	tokenCreate.SetExpiration(time.Now().Add(exDuration))
	tokenCreate.SetFooter(pasetoFooter)
	tokenCreate.SetSubject(randUid.Hex())
	token = tokenCreate.V4Sign(privateKey, nil)

	if _, err = writer.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		uidKey := s.withPrefix(prefixUidAccessToken, randUid.Hex())
		if err = pipe.HMSet(ctx, uidKey, params).Err(); err != nil {
			return errors.WithStack(err)
		}
		if err = pipe.Expire(ctx, uidKey, exDuration).Err(); err != nil {
			return errors.WithStack(err)
		}
		if err = pipe.SetEx(ctx, s.withPrefix(prefixIdUserAccessToken, params.IdAuthUser), uidKey, exDuration).Err(); err != nil {
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return "", errors.WithStack(err)
	}

	return token, nil
}

func (s *sessionRedis) RemoveToken(ctx context.Context, userID string) (err error) {
	writer, err := s.redisClient.Writer(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	userIdKey := s.withPrefix(prefixIdUserAccessToken, userID)

	uid := writer.Get(ctx, userIdKey).String()
	if uid == "" {
		return nil
	}
	_, err = writer.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		if err = pipe.Del(ctx, userIdKey).Err(); err != nil {
			return errors.WithStack(err)
		}
		if err = pipe.Del(ctx, uid).Err(); err != nil {
			return errors.WithStack(err)
		}
		return nil
	})
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}
func (s *sessionRedis) Verify(ctx context.Context, token string) (userCtx *usercontext.UserContext, err error) {
	reader, err := s.redisClient.Reader(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	parser := paseto.NewParser()
	footerData, err := parser.UnsafeParseFooter(paseto.V4Public, token)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var footer PasetoFooter
	if err = json.Unmarshal(footerData, &footer); err != nil {
		return nil, errors.WithStack(err)
	}

	appKey := s.withPrefix(prefixAuthApp, footer.IdApp)

	appConfig := &PasetoConfig{}

	if err = reader.HGetAll(ctx, appKey).Scan(appConfig); err != nil {
		return nil, errors.WithStack(err)
	}

	publicKey, err := paseto.NewV4AsymmetricPublicKeyFromHex(appConfig.PublicKey)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	tokenID, err := parser.ParseV4Public(publicKey, token, nil)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	randUID, err := tokenID.GetSubject()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	tokenIDKey := s.withPrefix(prefixUidAccessToken, randUID)
	gtp := &usercontext.UserContext{}
	err = reader.HGetAll(ctx, tokenIDKey).Scan(gtp)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return gtp, nil
}

func (s *sessionRedis) GenerateRefreshToken(ctx context.Context, idAuthApp string, deviceId string, params *usercontext.UserContext) (refreshToken string, err error) {

	randUid, err := algouid.NewUUID()
	if err != nil {
		return "", errors.WithStack(err)
	}
	appKey := s.withPrefix(prefixAuthApp, idAuthApp)

	appConfig := &PasetoConfig{}

	writer, err := s.redisClient.Writer(ctx)
	if err != nil {
		return "", errors.WithStack(err)
	}
	if err = writer.HGetAll(ctx, appKey).Scan(appConfig); err != nil {
		return "", errors.WithStack(err)
	}
	privateKey, err := paseto.NewV4AsymmetricSecretKeyFromHex(appConfig.PrivateKey)
	if err != nil {
		return "", errors.WithStack(err)
	}

	pasetoFooter, err := json.Marshal(PasetoFooter{IdApp: idAuthApp})
	exDuration := 30 * 24 * time.Hour
	exRefreshDuration := time.Now().Add(exDuration)
	refreshTokenCreate := paseto.NewToken()
	refreshTokenCreate.SetIssuer(idAuthApp)
	refreshTokenCreate.SetExpiration(exRefreshDuration)
	refreshTokenCreate.SetAudience(params.IdAuthUser)
	refreshTokenCreate.SetFooter(pasetoFooter)
	refreshTokenCreate.SetSubject(randUid.Hex())
	refreshToken = refreshTokenCreate.V4Sign(privateKey, nil)

	uidKey := s.withPrefix(prefixUidRefreshToken, randUid.Hex())
	if _, err = writer.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		err = pipe.CFAdd(ctx, s.cuckooFilterByApp(idAuthApp), hashRefreshToken(refreshToken)).Err()
		if err != nil {
			return errors.WithStack(err)
		}

		if err = pipe.HMSet(ctx, uidKey, params).Err(); err != nil {
			return errors.WithStack(err)
		}
		if err = pipe.Expire(ctx, uidKey, exDuration).Err(); err != nil {
			return errors.WithStack(err)
		}
		if err = pipe.SetEx(ctx, s.withPrefix(prefixIdUserRefreshToken, params.IdAuthUser), uidKey, exDuration).Err(); err != nil {
			return errors.WithStack(err)
		}
		return nil
	}); err != nil {
		return "", errors.WithStack(err)
	}

	return refreshToken, nil
}
func (s *sessionRedis) VerifyRefreshToken(ctx context.Context, refreshToken string) (userCtx *usercontext.UserContext, uuid string, err error) {
	reader, err := s.redisClient.Reader(ctx)
	parser := paseto.NewParser()
	footerData, err := parser.UnsafeParseFooter(paseto.V4Public, refreshToken)
	if err != nil {
		return nil, "", errors.WithStack(err)
	}

	var footer PasetoFooter
	if err = json.Unmarshal(footerData, &footer); err != nil {
		return nil, "", errors.WithStack(err)
	}

	appKey := s.withPrefix(prefixAuthApp, footer.IdApp)

	itemExists := reader.CFExists(ctx, s.cuckooFilterByApp(footer.IdApp), hashRefreshToken(refreshToken))
	if itemExists.Err() != nil {
		return nil, "", errors.WithStack(err)
	}

	if itemExists.Val() == false {
		return nil, "", autherr.ErrNotExist
	}

	appConfig := &PasetoConfig{}

	if err = reader.HGetAll(ctx, appKey).Scan(appConfig); err != nil {
		return nil, "", errors.WithStack(err)
	}

	publicKey, err := paseto.NewV4AsymmetricPublicKeyFromHex(appConfig.PublicKey)
	if err != nil {
		return nil, "", errors.WithStack(err)
	}
	tokenID, err := parser.ParseV4Public(publicKey, refreshToken, nil)
	//tokenID, err := pasetoService.Verify(refreshToken)
	if err != nil {
		return nil, "", errors.WithStack(err)
	}
	randUID, err := tokenID.GetSubject()
	if err != nil {
		return nil, "", errors.WithStack(err)
	}

	userId, err := tokenID.GetAudience()
	if err != nil {
		return nil, "", errors.WithStack(err)
	}

	tokenIDKey := s.withPrefix(prefixUidRefreshToken, randUID)
	gtp := &usercontext.UserContext{}
	err = reader.HGetAll(ctx, tokenIDKey).Scan(gtp)
	if err != nil {
		err = s.RemoveRefreshToken(ctx, userId, refreshToken)
		if err != nil {
			return nil, "", errors.WithStack(err)
		}
		return nil, "", errors.WithStack(err)
	}

	return gtp, randUID, nil
}
func (s *sessionRedis) RemoveRefreshToken(ctx context.Context, uuid string, token string) (err error) {
	parser := paseto.NewParser()
	footerData, err := parser.UnsafeParseFooter(paseto.V4Public, token)
	if err != nil {
		return errors.WithStack(err)
	}

	var footer PasetoFooter
	if err = json.Unmarshal(footerData, &footer); err != nil {
		return errors.WithStack(err)
	}
	var (
		cuckooFilterByApp = _cuckooFilterPrefix + ">" + footer.IdApp
	)

	writer, err := s.redisClient.Writer(ctx)
	if err != nil {
		return errors.WithStack(err)
	}

	rsDel := writer.CFDel(ctx, s.withPrefix(cuckooFilterByApp), token)
	if rsDel.Err() != nil {
		return errors.WithStack(err)
	}

	if err = writer.Del(ctx, s.withPrefix(prefixUidRefreshToken, uuid)).Err(); err != nil {
		return errors.WithStack(err)
	}
	return nil
}
func (s *sessionRedis) cuckooFilterByApp(idAuthApp string) (outputKey string) {
	return s.withPrefix(_cuckooFilterPrefix, idAuthApp)
}
func hashRefreshToken(token string) (outputKey string) {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

func (s *sessionRedis) withPrefix(key ...string) (outputKey string) {
	listStr := []string{s.prefix}
	listStr = append(listStr, key...)
	return strings.Join(listStr, ">")
}
