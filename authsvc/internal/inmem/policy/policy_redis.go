package policyredis

import (
	"context"
	"strings"

	"git.tmproxy-infra.com/algo/common/pkg/algoredis"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
)

const (
	listIdAuthRoleKey = "list_id_auth_role"
	rolePriority      = "role_priority"
	//keyRole           = "role"
	//keyBloomFilter    = "bloom_filter"
)

type policyRedis struct {
	prefix      string
	redisClient algoredis.RedisReadWrite
}

func (c *policyRedis) GetAllPathOfRole(ctx context.Context, idAuthRole string) ([]string, error) {
	reader, err := c.redisClient.Reader(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return reader.SMembers(ctx, c.withPrefix("role", idAuthRole)).Result()
}

type Service interface {
	IsAllow(ctx context.Context, idAuthRole, absolutePath string) (bool, error)

	Clear(ctx context.Context) (err error)
	Set(ctx context.Context, idAuthRole string, absolutePath string, roleValue RoleRedisValue) error

	GetAllPathOfRole(ctx context.Context, idAuthRole string) ([]string, error)
	GetRole(ctx context.Context, idAuthRole string) (*RoleRedisValue, error)
	//InitBloomFilterPerIdAuthRole(ctx context.Context, idAuthRole string) (err error)
}

func NewPolicyRedis(prefix string, client algoredis.RedisReadWrite) Service {

	return &policyRedis{redisClient: client, prefix: prefix}
}

// sets listIdRole / for clear
// sets idAuthRole /abc/xyz /bc/sadsad // for fetch me
// bf idAuthRole /path for filterr

func (c *policyRedis) IsAllow(ctx context.Context, idAuthRole string, absolutePath string) (bool, error) {
	reader, err := c.redisClient.Reader(ctx)
	if err != nil {
		return false, errors.WithStack(err)
	}

	//return c.redisClient.BFExists(ctx, c.withPrefix("bloom_filter", idAuthRole), absolutePath).Result()
	return reader.SIsMember(ctx, c.withPrefix("role", idAuthRole), absolutePath).Result()
}

//func (c *policyRedis) InitBloomFilterPerIdAuthRole(ctx context.Context, idAuthRole string) (err error) {
//	key := c.withPrefix("bloom_filter", idAuthRole)
//	if err = c.redisClient.Del(ctx, key).Err(); err != nil {
//		return errors.WithStack(err)
//	}
//	if err = c.redisClient.BFReserve(ctx, key, 0.001, 10000).Err(); err != nil {
//		return errors.WithStack(err)
//	}
//	return nil
//}

func (c *policyRedis) Set(ctx context.Context, idAuthRole string, absolutePath string, roleValue RoleRedisValue) (err error) {
	writer, err := c.redisClient.Writer(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	_, err = writer.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
		if err = pipe.HMSet(ctx, c.withPrefix(rolePriority, idAuthRole), roleValue).Err(); err != nil {
			return errors.WithStack(err)
		}
		if err = pipe.SAdd(ctx, c.withPrefix(listIdAuthRoleKey), idAuthRole).Err(); err != nil {
			return errors.WithStack(err)
		}
		if err = pipe.SAdd(ctx, c.withPrefix("role", idAuthRole), absolutePath).Err(); err != nil {
			return errors.WithStack(err)
		}
		//if err = pipe.BFAdd(ctx, c.withPrefix("bloom_filter", idAuthRole), absolutePath).Err(); err != nil {
		//	return errors.WithStack(err)
		//}
		return nil
	})
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (c *policyRedis) GetRole(ctx context.Context, idAuthRole string) (*RoleRedisValue, error) {
	reader, err := c.redisClient.Reader(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	roleInRedis := &RoleRedisValue{}
	err = reader.HGetAll(ctx, c.withPrefix(rolePriority, idAuthRole)).Scan(roleInRedis)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return roleInRedis, nil
}

func (c *policyRedis) Clear(ctx context.Context) (err error) {
	writer, err := c.redisClient.Writer(ctx)
	if err != nil {
		return errors.WithStack(err)
	}
	listIdRole, err := writer.SMembers(ctx, c.withPrefix(listIdAuthRoleKey)).Result()
	if err != nil {
		return errors.WithStack(err)
	}
	_, err = writer.Pipelined(ctx, func(pipe redis.Pipeliner) error {
		for _, v := range listIdRole {
			if err = pipe.Del(ctx, c.withPrefix("role", v)).Err(); err != nil {
				return errors.WithStack(err)
			}
			if err = pipe.Del(ctx, c.withPrefix(rolePriority, v)).Err(); err != nil {
				return errors.WithStack(err)
			}
		}
		if err = pipe.Del(ctx, c.withPrefix(listIdAuthRoleKey)).Err(); err != nil {
			return errors.WithStack(err)
		}
		return nil
	})
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (c *policyRedis) withPrefix(key ...string) (outputKey string) {
	listStr := []string{c.prefix}
	listStr = append(listStr, key...)
	return strings.Join(listStr, ">")
}
