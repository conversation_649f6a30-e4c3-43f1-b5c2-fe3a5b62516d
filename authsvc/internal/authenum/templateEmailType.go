package authenum

import (
	"database/sql/driver"
	"fmt"
	"github.com/goccy/go-json"
	"github.com/pkg/errors"
	"strings"
)

type TemplateEmailType struct {
	value string
}

func (i *TemplateEmailType) Compare(i2 TemplateEmailType) bool {
	return i.value == i2.value
}

func (i *TemplateEmailType) UnmarshalJSON(bytes []byte) error {
	pt := newTemplateEmailTypeFromString(string(bytes))
	i.value = pt.value
	return nil
}

func (i *TemplateEmailType) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.value)
}

var _ json.Marshaler = (*TemplateEmailType)(nil)
var _ json.Unmarshaler = (*TemplateEmailType)(nil)

func (i *TemplateEmailType) IsValid() bool {
	return len(i.value) > 0
}

func (i *TemplateEmailType) String() string {
	return i.value
}

var (
	MailOTP               = TemplateEmailType{"MAIL_OTP"}
	MailOTPForgotPassword = TemplateEmailType{"MAIL_OTP_FORGOT_PASSWORD"}
)

func newTemplateEmailTypeFromString(s string) TemplateEmailType {
	s = strings.Trim(s, "\"")
	switch s {
	case MailOTP.value:
		return MailOTP
	case MailOTPForgotPassword.value:
		return MailOTPForgotPassword

	}

	return TemplateEmailType{}
}

func (i TemplateEmailType) Value() (driver.Value, error) {
	if !i.IsValid() {
		return nil, nil
	}
	return i.value, nil

}
func (i *TemplateEmailType) Scan(src interface{}) (err error) {

	var sc string
	switch src.(type) {
	case string:
		sc = src.(string)
	default:
		return fmt.Errorf("incompatible type for app_type")
	}
	ps := newTemplateEmailTypeFromString(sc)
	if !ps.IsValid() {
		return errors.WithStack(fmt.Errorf("invalid app_type: " + sc))
	}
	i.value = ps.value
	return nil
}
