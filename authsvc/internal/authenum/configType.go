package authenum

import (
	"database/sql/driver"
	"fmt"
	"github.com/goccy/go-json"
	"github.com/pkg/errors"
	"strings"
)

type ConfigType struct {
	value string
}

func (i *ConfigType) Compare(i2 ConfigType) bool {
	return i.value == i2.value
}

func (i *ConfigType) UnmarshalJSON(bytes []byte) error {
	pt := newConfigTypeFromString(string(bytes))
	i.value = pt.value
	return nil
}

func (i *ConfigType) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.value)
}

var _ json.Marshaler = (*ConfigType)(nil)
var _ json.Unmarshaler = (*ConfigType)(nil)

func (i *ConfigType) IsValid() bool {
	return len(i.value) > 0
}

func (i *ConfigType) String() string {
	return i.value
}

var (
	ConfigMailAws         = ConfigType{"MAIL_AWS"}
	ConfigTemplateMailOtp = ConfigType{"TEMPLATE_MAIL_OTP"}
)

func newConfigTypeFromString(s string) ConfigType {
	s = strings.Trim(s, "\"")
	switch s {
	case ConfigMailAws.value:
		return ConfigMailAws
	case ConfigTemplateMailOtp.value:
		return ConfigTemplateMailOtp
	}
	return ConfigType{}
}

func (i ConfigType) Value() (driver.Value, error) {
	if !i.IsValid() {
		return nil, nil
	}
	return i.value, nil

}
func (i *ConfigType) Scan(src interface{}) (err error) {

	var sc string
	switch src.(type) {
	case string:
		sc = src.(string)
	default:
		return fmt.Errorf("incompatible type for app_type")
	}
	ps := newConfigTypeFromString(sc)
	if !ps.IsValid() {
		return errors.WithStack(fmt.Errorf("invalid app_type: " + sc))
	}
	i.value = ps.value
	return nil
}
