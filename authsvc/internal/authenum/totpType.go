package authenum

import (
	"database/sql/driver"
	"fmt"
	"github.com/goccy/go-json"
	"github.com/pkg/errors"
	"strings"
)

type TotpType struct {
	value string
}

func (i *TotpType) Compare(i2 TotpType) bool {
	return i.value == i2.value
}

func (i *TotpType) UnmarshalJSON(bytes []byte) error {
	pt := newTotpTypeFromString(string(bytes))
	i.value = pt.value
	return nil
}

func (i *TotpType) MarshalJSON() ([]byte, error) {
	return json.Marshal(i.value)
}

var _ json.Marshaler = (*TotpType)(nil)
var _ json.Unmarshaler = (*TotpType)(nil)

func (i *TotpType) IsValid() bool {
	return len(i.value) > 0
}

func (i *TotpType) String() string {
	return i.value
}

var (
	TotpType2FA   = TotpType{"2fa"}
	TotpTypeEmail = TotpType{"email"}
)

func newTotpTypeFromString(s string) TotpType {
	s = strings.Trim(s, "\"")
	switch s {
	case TotpType2FA.value:
		return TotpType2FA
	case TotpTypeEmail.value:
		return TotpTypeEmail
	}
	return TotpType{}
}

func (i TotpType) Value() (driver.Value, error) {
	if !i.IsValid() {
		return nil, nil
	}
	return i.value, nil

}
func (i *TotpType) Scan(src interface{}) (err error) {

	var sc string
	switch src.(type) {
	case string:
		sc = src.(string)
	default:
		return fmt.Errorf("incompatible type for app_type")
	}
	ps := newTotpTypeFromString(sc)
	if !ps.IsValid() {
		return errors.WithStack(fmt.Errorf("invalid app_type: " + sc))
	}
	i.value = ps.value
	return nil
}
