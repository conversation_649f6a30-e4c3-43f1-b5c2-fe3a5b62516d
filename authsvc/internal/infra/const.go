package authinfra

const (
	MaxLengthSalt    = 16
	MaxLengthRefCode = 4
)

const (
	IdRoleBackOfficePublic = "0192052b9d1f786383a238a6e0b7b187"
	IdRoleMerchantAdmin    = "019276f3d22770b6904f6a462ad84be5"
	IdRoleMerchantUser     = "019276f40b0770b6991c76737151d4a8"
)

type PasetoConfig struct {
	PublicKey  string `redis:"public_key"`
	PrivateKey string `redis:"private_key"`
	Issuer     string `redis:"issuer"`
	TokenTTL   int64  `redis:"token_ttl"`
}
