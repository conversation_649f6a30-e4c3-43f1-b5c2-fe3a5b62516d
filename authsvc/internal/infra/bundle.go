package authinfra

import (
	"context"
	"fmt"
	cpauthdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	"github.com/pkg/errors"

	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
)

type bundleLoader struct {
}

func (bundleLoader) LoadBundleApp(ctx context.Context, appRepo cpauthdao.AuthAppRepository, mapIdAuthApp map[algouid.UUID]struct{}) (mapAuthApp map[algouid.UUID]*cpauthdao.App, err error) {
	if len(mapIdAuthApp) == 0 {
		return mapAuthApp, nil
	}
	listIdAuthApp := make([]algouid.UUID, 0, len(mapIdAuthApp))
	for k := range mapIdAuthApp {
		listIdAuthApp = append(listIdAuthApp, k)
	}

	listAuthAppDao, err := appRepo.Select(ctx, pdb.OptionParams[cpauthdao.ParamsAppSelect](
		cpauthdao.AppSelectWithListIdApp(listIdAuthApp),
	))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	mapAuthApp = make(map[algouid.UUID]*cpauthdao.App, len(listAuthAppDao))
	for i := range len(listAuthAppDao) {
		mapAuthApp[listAuthAppDao[i].IdApp] = listAuthAppDao[i]
	}
	return mapAuthApp, nil
}

func (bundleLoader) LoadBundlePath(ctx context.Context, authUserPathRepo cpauthdao.PathRepository, mapIdPath map[algouid.UUID]struct{}) (mapAuthUserRole map[algouid.UUID]*cpauthdao.Path, err error) {
	if len(mapIdPath) == 0 {
		return mapAuthUserRole, nil
	}
	listIdAuthPath := make([]algouid.UUID, 0, len(mapIdPath))
	for k := range mapIdPath {
		listIdAuthPath = append(listIdAuthPath, k)
	}

	listAuthPathDao, err := authUserPathRepo.Select(ctx, pdb.OptionParams[cpauthdao.ParamsPathSelect](
		cpauthdao.PathSelectWithListIdPath(listIdAuthPath),
	))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	mapAuthUserRole = make(map[algouid.UUID]*cpauthdao.Path, len(listAuthPathDao))
	for i := range len(listAuthPathDao) {
		mapAuthUserRole[listAuthPathDao[i].IdPath] = listAuthPathDao[i]
	}
	return mapAuthUserRole, nil
}

func (bundleLoader) LoadBundleRole(ctx context.Context, roleRepo cpauthdao.RoleRepository, mapIDRole map[algouid.UUID]struct{}) (mapRole map[algouid.UUID]*cpauthdao.Role, err error) {
	if len(mapIDRole) == 0 {
		return mapRole, nil
	}
	listIdRole := make([]algouid.UUID, 0, len(mapIDRole))
	for k := range mapIDRole {
		listIdRole = append(listIdRole, k)
	}

	listRoleDao, err := roleRepo.Select(ctx, pdb.OptionParams[cpauthdao.ParamsRoleSelect](
		cpauthdao.RoleSelectWithListIdRole(listIdRole),
	))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(listRoleDao) != len(mapIDRole) {
		return nil, fmt.Errorf("%w", fmt.Errorf("critical error,logic wrong role_bundle"))
	}
	mapRole = make(map[algouid.UUID]*cpauthdao.Role, len(listRoleDao))
	for i := range listRoleDao {
		mapRole[listRoleDao[i].IdRole] = listRoleDao[i]
	}

	return mapRole, nil
}

func (bundleLoader) LoadBundleService(ctx context.Context, serviceRepo cpauthdao.ServiceRepository, mapIDService map[algouid.UUID]struct{}) (mapService map[algouid.UUID]*cpauthdao.Service, err error) {
	if len(mapIDService) == 0 {
		return mapService, nil
	}
	listIdService := make([]algouid.UUID, 0, len(mapIDService))
	for k := range mapIDService {
		listIdService = append(listIdService, k)
	}
	listServiceDao, err := serviceRepo.Select(ctx, pdb.OptionParams[cpauthdao.ParamsServiceSelect](
		cpauthdao.ServiceSelectWithListIdService(listIdService),
	))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(listServiceDao) != len(mapIDService) {
		return nil, fmt.Errorf("%w", fmt.Errorf("critical error,logic wrong service_bundle"))
	}
	mapService = make(map[algouid.UUID]*cpauthdao.Service, len(listServiceDao))
	for i := range listServiceDao {
		mapService[listServiceDao[i].IdService] = listServiceDao[i]
	}

	return mapService, nil
}

func (bundleLoader) LoadBundleUserDetail(ctx context.Context, appRepo cpauthdao.UserDetailRepository, mapIdUserDetail map[algouid.UUID]struct{}) (mapUserDetail map[algouid.UUID]*cpauthdao.UserDetail, err error) {
	if len(mapIdUserDetail) == 0 {
		return mapUserDetail, nil
	}
	listIdUser := make([]algouid.UUID, 0, len(mapIdUserDetail))
	for k := range mapIdUserDetail {
		listIdUser = append(listIdUser, k)
	}

	listUserDetailDao, err := appRepo.Select(ctx, pdb.OptionParams[cpauthdao.ParamsUserDetailSelect](
		cpauthdao.UserDetailSelectWithListIdUser(listIdUser),
	))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	mapUserDetail = make(map[algouid.UUID]*cpauthdao.UserDetail, len(listUserDetailDao))
	for i := range len(listUserDetailDao) {
		mapUserDetail[listUserDetailDao[i].IdUser] = listUserDetailDao[i]
	}
	return mapUserDetail, nil
}
