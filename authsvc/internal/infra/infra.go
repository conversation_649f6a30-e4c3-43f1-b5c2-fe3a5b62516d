package authinfra

import (
	"context"
	"git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1/authv1connect"
	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	"git.tmproxy-infra.com/algo/authsvc/internal/infra/externalsvc"
	"git.tmproxy-infra.com/algo/authsvc/internal/infra/userinfra"
	configredis "git.tmproxy-infra.com/algo/authsvc/internal/inmem/config"
	policyredis "git.tmproxy-infra.com/algo/authsvc/internal/inmem/policy"
	sessionredis "git.tmproxy-infra.com/algo/authsvc/internal/inmem/session"
	totpredis "git.tmproxy-infra.com/algo/authsvc/internal/inmem/totp"
	authrepo "git.tmproxy-infra.com/algo/authsvc/internal/repository"
	"git.tmproxy-infra.com/algo/common/pkg/algoredis"
	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"github.com/hibiken/asynq"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
)

type authInfra struct {
	ctx         context.Context
	logger      *zap.Logger
	transaction pdb.Transaction

	sessionManager    sessionredis.Service
	policyManager     policyredis.Service
	backofficeManager totpredis.Service
	configManager     configredis.Service

	userInfra userinfra.Infra

	cpAppRepo                 authdao.AuthAppRepository
	cpRoleRepo                authdao.RoleRepository
	cpAuthServiceRepo         authdao.ServiceRepository
	cpAuthPolicyRepo          authdao.PolicyRepository
	cpAuthPathRepo            authdao.PathRepository
	cpConfigRepo              authdao.ConfigRepository
	cpCompanyRepo             authdao.CompanyRepository
	cpConfigMailRepo          authdao.ConfigMailRepository
	cpConfigTemplateEmailRepo authdao.ConfigTemplateEmailRepository
	cpDeviceRepo              authdao.DeviceRepository
	cpOAuthConfigRepo         authdao.OAuthConfigRepository

	// internal service
	bundleLoader bundleLoader

	commonFunction *commonFunction

	//urlInternalService string
	urlBillingService string
	asynqClient       *asynq.Client

	authInternalHandler authv1connect.InternalAuthServiceHandler

	exService externalsvc.ExService
}

func (a *authInfra) ExternalService() externalsvc.ExService {
	return a.exService
}

//func (a *authInfra) GetBillingTransactionInternal() transactionv1connect.InternalTransactionServiceHandler {
//	return transactionv1connect.NewInternalTransactionServiceClient(
//		&http.Client{Timeout: 5 * time.Second},
//		a.urlBillingService,
//		connect.WithInterceptors(
//			interceptorutils.NewLoggingInterceptor(a.logger.Named("call_billing_internal_logging")),
//		),
//		connect.WithGRPC())
//}

func (a *authInfra) Logger() *zap.Logger {
	return a.logger
}

func (a *authInfra) GetAuthInternalHandler() authv1connect.InternalAuthServiceHandler {
	return a.authInternalHandler
}

func (a *authInfra) SetAuthInternalHandler(authInternalHandler authv1connect.InternalAuthServiceHandler) {
	a.authInternalHandler = authInternalHandler
}

func (a *authInfra) SetBillingInternalUrl(billingInternalUrl string) {
	a.urlBillingService = billingInternalUrl
}

type Infra interface {
	Logger() *zap.Logger
	Transaction() pdb.Transaction
	SessionManager() sessionredis.Service
	PolicyManager() policyredis.Service
	TotpManager() totpredis.Service
	ConfigManager() configredis.Service
	AppRepository() authdao.AuthAppRepository
	ConfigRepository() authdao.ConfigRepository
	CompanyRepository() authdao.CompanyRepository
	ConfigMailRepository() authdao.ConfigMailRepository
	ConfigTemplateEmailRepository() authdao.ConfigTemplateEmailRepository
	OAuthConfigRepository() authdao.OAuthConfigRepository

	User() userinfra.Infra

	RoleRepository() authdao.RoleRepository
	ServiceRepository() authdao.ServiceRepository
	PolicyRepository() authdao.PolicyRepository
	PathRepository() authdao.PathRepository
	DeviceRepository() authdao.DeviceRepository

	BundleLoader() bundleLoader
	CommonFunction() *commonFunction
	GetAuthInternalHandler() authv1connect.InternalAuthServiceHandler
	//GetBillingTransactionInternal() transactionv1connect.InternalTransactionServiceHandler
	ReloadCachePolicy(ctx context.Context) (err error)

	SetAuthInternalHandler(authInternalHandler authv1connect.InternalAuthServiceHandler)
	Reload(ctx context.Context) (err error)
	AsynqClient() *asynq.Client

	ExternalService() externalsvc.ExService
}

func (a *authInfra) User() userinfra.Infra {
	return a.userInfra
}

func (a *authInfra) Transaction() pdb.Transaction {
	return a.transaction
}

func (a *authInfra) ConfigManager() configredis.Service {
	return a.configManager
}
func (a *authInfra) SessionManager() sessionredis.Service {
	return a.sessionManager
}
func (a *authInfra) PolicyManager() policyredis.Service {
	return a.policyManager
}
func (a *authInfra) TotpManager() totpredis.Service {
	return a.backofficeManager
}
func (a *authInfra) AppRepository() authdao.AuthAppRepository {
	return a.cpAppRepo
}
func (a *authInfra) ConfigRepository() authdao.ConfigRepository {
	return a.cpConfigRepo
}
func (a *authInfra) CompanyRepository() authdao.CompanyRepository {
	return a.cpCompanyRepo
}
func (a *authInfra) ConfigTemplateEmailRepository() authdao.ConfigTemplateEmailRepository {
	return a.cpConfigTemplateEmailRepo
}
func (a *authInfra) OAuthConfigRepository() authdao.OAuthConfigRepository {
	return a.cpOAuthConfigRepo
}
func (a *authInfra) DeviceRepository() authdao.DeviceRepository {
	return a.cpDeviceRepo
}

func (a *authInfra) RoleRepository() authdao.RoleRepository {
	return a.cpRoleRepo
}

func (a *authInfra) ServiceRepository() authdao.ServiceRepository {
	return a.cpAuthServiceRepo
}

func (a *authInfra) PolicyRepository() authdao.PolicyRepository {
	return a.cpAuthPolicyRepo
}

func (a *authInfra) PathRepository() authdao.PathRepository {
	return a.cpAuthPathRepo
}
func (a *authInfra) ConfigMailRepository() authdao.ConfigMailRepository {
	return a.cpConfigMailRepo
}

func (a *authInfra) BundleLoader() bundleLoader {
	return a.bundleLoader
}
func (a *authInfra) AsynqClient() *asynq.Client {
	return a.asynqClient
}

func (a *authInfra) CommonFunction() *commonFunction {
	return a.commonFunction
}

func NewService(ctx context.Context, logger *zap.Logger, db pdb.DB, transaction pdb.Transaction, redisClient algoredis.RedisReadWrite, urlBillingInternal, urlMiscInternal string, asynqClient *asynq.Client) (Infra, error) {
	cpConfigRepo := authrepo.NewAuthConfigRepository(db, "auth_config")
	cpAppRepo := authrepo.NewAppRepository(db, "auth_app")
	cpRoleRepo := authrepo.NewAuthRoleRepository(db, "auth_role")
	cpAuthServiceRepo := authrepo.NewAuthServiceRepository(db, "auth_service")
	cpAuthPathRepo := authrepo.NewAuthPathRepository(db, "auth_path")
	cpAuthPolicy := authrepo.NewAuthPolicyRepository(db, "auth_policy")
	cpCompanyRepo := authrepo.NewCompanyRepository(db, "auth_company")
	cpConfigMailRepo := authrepo.NewConfigMailRepository(db, "auth_config_mail")
	cpConfigTemplateEmailRepo := authrepo.NewConfigTemplateEmailRepository(db, "auth_config_template_email")
	cpDeviceRepo := authrepo.NewDeviceRepository(db, "auth_device")
	cpOAuthConfigRepo := authrepo.NewOAuthConfigRepository(db, "oauth_config")

	authInf := &authInfra{
		ctx:                       ctx,
		logger:                    logger,
		transaction:               transaction,
		cpAppRepo:                 cpAppRepo,
		userInfra:                 userinfra.NewInfra(db),
		cpRoleRepo:                cpRoleRepo,
		cpAuthServiceRepo:         cpAuthServiceRepo,
		cpAuthPolicyRepo:          cpAuthPolicy,
		cpAuthPathRepo:            cpAuthPathRepo,
		cpConfigRepo:              cpConfigRepo,
		bundleLoader:              bundleLoader{},
		sessionManager:            sessionredis.NewSessionRedis("auth_session", redisClient),
		policyManager:             policyredis.NewPolicyRedis("auth_policy", redisClient),
		backofficeManager:         totpredis.NewTotpRedis("auth_backoffice", redisClient),
		configManager:             configredis.NewConfigRedis("auth_config", redisClient),
		urlBillingService:         urlBillingInternal,
		cpCompanyRepo:             cpCompanyRepo,
		cpConfigMailRepo:          cpConfigMailRepo,
		cpConfigTemplateEmailRepo: cpConfigTemplateEmailRepo,
		cpDeviceRepo:              cpDeviceRepo,
		asynqClient:               asynqClient,
		cpOAuthConfigRepo:         cpOAuthConfigRepo,
	}
	authInf.commonFunction = newCommonFunction(authInf)
	authInf.exService = externalsvc.NewExService(logger, urlMiscInternal, urlBillingInternal)

	return authInf, nil
}
func (a *authInfra) Reload(ctx context.Context) (err error) {
	errgWg, ctxeg := errgroup.WithContext(ctx)

	errgWg.Go(func() error {
		return a.ReloadAllPasetoService(ctxeg)
	})
	errgWg.Go(func() error {
		return a.ReloadCachePolicy(ctxeg)
	})
	return errgWg.Wait()
}

func (a *authInfra) ReloadAllPasetoService(ctx context.Context) error {
	listAppDao, err := a.AppRepository().Select(ctx, pdb.OptionParams[authdao.ParamsAppSelect](
		authdao.AppSelectWithActiveOrDeactivate(true, false),
	))
	if err != nil {
		return errors.WithStack(err)
	}
	for _, v := range listAppDao {
		if err = a.SessionManager().SetPasetoConfig(ctx, v.IdApp.Hex(), v.PublicEd25519, v.PrivateEd25519, v.Domain, v.TokenTTL); err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}
func (a *authInfra) ReloadCachePolicy(ctx context.Context) (err error) {
	if err = a.PolicyManager().Clear(ctx); err != nil {
		return errors.WithStack(err)
	}
	listPolicy, err := a.PolicyRepository().Select(ctx, pdb.OptionParams[authdao.ParamsPolicySelect](
		authdao.PolicySelectWithActiveOrDeactivate(true, false),
	))
	if err != nil {
		return errors.WithStack(err)
	}
	var (
		mapIdPath    = make(map[algouid.UUID]struct{})
		mapIdRole    = make(map[algouid.UUID]struct{})
		mapIdService = make(map[algouid.UUID]struct{})
	)
	for _, v := range listPolicy {
		mapIdPath[v.IdPath] = struct{}{}
		mapIdRole[v.IdRole] = struct{}{}
	}

	pathBundle, err := a.BundleLoader().LoadBundlePath(ctx, a.PathRepository(), mapIdPath)
	if err != nil {
		return errors.WithStack(err)
	}
	roleBundle, err := a.BundleLoader().LoadBundleRole(ctx, a.RoleRepository(), mapIdRole)
	if err != nil {
		return errors.WithStack(err)
	}

	for _, v := range pathBundle {
		mapIdService[v.IdService] = struct{}{}
	}
	serviceBundle, err := a.BundleLoader().LoadBundleService(ctx, a.ServiceRepository(), mapIdService)
	if err != nil {
		return errors.WithStack(err)
	}

	for _, v := range listPolicy {
		pathOfPolicy, ok := pathBundle[v.IdPath]
		if !ok {
			return errors.Errorf("path of policy %s not found, path_bundle error", v.IdPath.Hex())
		}
		serviceOfPath, ok := serviceBundle[pathOfPolicy.IdService]
		if !ok {
			return errors.Errorf("service of path %s not found, service_bundle error", pathOfPolicy.IdService.Hex())
		}
		roleOfPolicy, ok := roleBundle[v.IdRole]
		if !ok {
			return errors.Errorf("role of policy %s not found, role_bundle error", v.IdRole.Hex())
		}
		isGranted := serviceOfPath.IsActive() && roleOfPolicy.IsActive() && pathOfPolicy.IsActive() && v.IsActive()
		if !isGranted {
			continue
		}
		if err = a.PolicyManager().Set(ctx, v.IdRole.Hex(), pathOfPolicy.AbsolutePath, policyredis.RoleRedisValue{
			RoleName: roleOfPolicy.RoleName,
			Priority: roleOfPolicy.Priority,
		}); err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}
