package authinfra

import (
	algoenumv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	"git.tmproxy-infra.com/algo/authsvc/internal/authenum"
	"git.tmproxy-infra.com/algo/common/enumtype"
)

func ConvertAppTypeEnumToAppTypeDao(appType algoenumv1.AppType) enumtype.AppType {
	switch appType {
	case algoenumv1.AppType_APP_TYPE_CORE:
		return enumtype.CoreAppType
	case algoenumv1.AppType_APP_TYPE_MERCHANT:
		return enumtype.MerchantAppType
	}
	return enumtype.AppType{}
}
func ConvertAppTypeDaoToAppType(appType enumtype.AppType) algoenumv1.AppType {
	switch appType {
	case enumtype.CoreAppType:
		return algoenumv1.AppType_APP_TYPE_CORE
	case enumtype.MerchantAppType:
		return algoenumv1.AppType_APP_TYPE_MERCHANT
	}
	return algoenumv1.AppType_APP_TYPE_UNSPECIFIED
}

func ConvertTemplateEmailTypeDaoToTemplateEmail(appType authenum.TemplateEmailType) algoenumv1.TemplateEmailType {
	switch appType {
	case authenum.MailOTP:
		return algoenumv1.TemplateEmailType_TEMPLATE_EMAIL_TYPE_OTP
	case authenum.MailOTPForgotPassword:
		return algoenumv1.TemplateEmailType_TEMPLATE_EMAIL_TYPE_OTP_FORGOT_PASSWORD

	}
	return algoenumv1.TemplateEmailType_TEMPLATE_EMAIL_TYPE_UNSPECIFIED
}

func ConvertTemplateEmailTypeToTemplateEmailDao(appType algoenumv1.TemplateEmailType) authenum.TemplateEmailType {
	switch appType {
	case algoenumv1.TemplateEmailType_TEMPLATE_EMAIL_TYPE_OTP:
		return authenum.MailOTP
	case algoenumv1.TemplateEmailType_TEMPLATE_EMAIL_TYPE_OTP_FORGOT_PASSWORD:
		return authenum.MailOTPForgotPassword
	}
	return authenum.TemplateEmailType{}
}

func ConvertAppCountryDaoToAppCountryEnum(appCountry algoenumv1.AppCountry) enumtype.AppCountry {
	switch appCountry {
	case algoenumv1.AppCountry_APP_COUNTRY_VN:
		return enumtype.AppCountryVN
	case algoenumv1.AppCountry_APP_COUNTRY_WW:
		return enumtype.AppCountryWW
	}
	return enumtype.AppCountry{}
}
func ConvertAppCountryEnumToAppCountryDao(appCountry enumtype.AppCountry) algoenumv1.AppCountry {
	switch appCountry {
	case enumtype.AppCountryVN:
		return algoenumv1.AppCountry_APP_COUNTRY_VN
	case enumtype.AppCountryWW:
		return algoenumv1.AppCountry_APP_COUNTRY_WW
	}
	return algoenumv1.AppCountry_APP_COUNTRY_UNSPECIFIED
}

//func GenerateEd25519Key() (priv, pub string, err error) {
//	var (
//		b       []byte
//		pubKey  ed25519.PublicKey
//		privKey ed25519.PrivateKey
//	)
//
//	pubKey, privKey, err = ed25519.GenerateKey(rand.Reader)
//	if err != nil {
//		return "", "", errors.WithStack(err)
//	}
//
//	b, err = x509.MarshalPKCS8PrivateKey(privKey)
//	if err != nil {
//		return "", "", errors.WithStack(err)
//	}
//
//	privBlock := &pem.Block{
//		Type:  "PRIVATE KEY",
//		Bytes: b,
//	}
//
//	// public key
//	b, err = x509.MarshalPKIXPublicKey(pubKey)
//	if err != nil {
//		return "", "", errors.WithStack(err)
//	}
//
//	pubBlock := &pem.Block{
//		Type:  "PUBLIC KEY",
//		Bytes: b,
//	}
//	privBytes := pem.EncodeToMemory(privBlock)
//	pubBytes := pem.EncodeToMemory(pubBlock)
//	return hex.EncodeToString(privBytes), hex.EncodeToString(pubBytes), nil
//}
