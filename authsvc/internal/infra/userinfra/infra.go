package userinfra

import (
	authdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	authrepo "git.tmproxy-infra.com/algo/authsvc/internal/repository"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
)

type Infra interface {
	UserRepository() authdao.UserRepository
	UserDetailRepository() authdao.UserDetailRepository
	UserTotpRepository() authdao.UserTotpRepository
}

func (a *userInfra) UserRepository() authdao.UserRepository {
	return a.userRepo
}
func (a *userInfra) UserDetailRepository() authdao.UserDetailRepository {
	return a.userDetailRepo
}

func (a *userInfra) UserTotpRepository() authdao.UserTotpRepository {
	return a.userTotpRepo
}

type userInfra struct {
	userRepo       authdao.UserRepository
	userDetailRepo authdao.UserDetailRepository
	userTotpRepo   authdao.UserTotpRepository
}

func NewInfra(db pdb.DB) Infra {
	ui := &userInfra{
		userRepo:       authrepo.NewUserRepository(db, "auth_user"),
		userDetailRepo: authrepo.NewAuthUserDetailRepository(db, "auth_user_detail"),
		userTotpRepo:   authrepo.NewAuthUserTotpRepository(db, "auth_user_totp"),
	}
	return ui
}
