package externalsvc

import (
	"net/http"
	"time"

	"connectrpc.com/connect"
	"git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/transaction/v1/transactionv1connect"
	"git.tmproxy-infra.com/algo/algoproxy-proto/gen/misc/dbip/v1/dbipv1connect"
	"git.tmproxy-infra.com/algo/algoproxy-proto/gen/misc/paymentaddress/v1/paymentaddressv1connect"
	"git.tmproxy-infra.com/algo/common/pkg/interceptorutils"
	"go.uber.org/zap"
)

type ExService interface {
	BillingUserTransactionInternalClient() transactionv1connect.InternalTransactionServiceClient
	MiscPaymentAddressClient() paymentaddressv1connect.CustomerPaymentAddressServiceClient
	MiscDBIPClient() dbipv1connect.PublicDBIPServiceClient
	MiscInternalClient() paymentaddressv1connect.InternalPaymentAddressServiceClient
}

type exService struct {
	logger             *zap.Logger
	miscInternalURL    string
	billingInternalURL string
}

func (i *exService) MiscDBIPClient() dbipv1connect.PublicDBIPServiceClient {
	return dbipv1connect.NewPublicDBIPServiceClient(
		&http.Client{Timeout: 5 * time.Second},
		i.miscInternalURL,
		connect.WithInterceptors(
			interceptorutils.NewLoggingInterceptor(i.logger.Named("auth_to_misc_internal_misc_db_ip")),
		),
		connect.WithGRPC(),
	)
}
func (i *exService) MiscInternalClient() paymentaddressv1connect.InternalPaymentAddressServiceClient {
	return paymentaddressv1connect.NewInternalPaymentAddressServiceClient(
		&http.Client{Timeout: 5 * time.Second},
		i.miscInternalURL,
		connect.WithInterceptors(
			interceptorutils.NewLoggingInterceptor(i.logger.Named("auth_to_misc_internal")),
		),
		connect.WithGRPC(),
	)
}

func (i *exService) MiscPaymentAddressClient() paymentaddressv1connect.CustomerPaymentAddressServiceClient {
	return paymentaddressv1connect.NewCustomerPaymentAddressServiceClient(
		&http.Client{Timeout: 5 * time.Second},
		i.miscInternalURL,
		connect.WithInterceptors(
			interceptorutils.NewLoggingInterceptor(i.logger.Named("auth_to_misc_internal_payment_address")),
		),
		connect.WithGRPC(),
	)
}

func NewExService(logger *zap.Logger, miscInternalUrl string, billingInternalUrl string) ExService {
	return &exService{logger: logger, miscInternalURL: miscInternalUrl, billingInternalURL: billingInternalUrl}
}

func (i *exService) BillingUserTransactionInternalClient() transactionv1connect.InternalTransactionServiceClient {
	return transactionv1connect.NewInternalTransactionServiceClient(
		&http.Client{Timeout: 5 * time.Second},
		i.billingInternalURL,
		connect.WithInterceptors(
			interceptorutils.NewLoggingInterceptor(i.logger.Named("auth_to_internal_billing")),
		),
		connect.WithGRPC(),
	)
}
