package authinfra

import (
	"bytes"
	"context"
	"crypto/md5"
	"crypto/sha256"
	"crypto/subtle"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	algoenumv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	authv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1"
	"git.tmproxy-infra.com/algo/authsvc/internal/authenum"
	"git.tmproxy-infra.com/algo/authsvc/internal/autherr"
	cpauthdao "git.tmproxy-infra.com/algo/authsvc/internal/dao"
	configredis "git.tmproxy-infra.com/algo/authsvc/internal/inmem/config"
	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	arandom "git.tmproxy-infra.com/algo/common/pkg/random"
	"git.tmproxy-infra.com/algo/common/pkg/usercontext"
	"github.com/pkg/errors"
	"github.com/pquerna/otp"
	"github.com/pquerna/otp/totp"
	"image"
	"image/png"
	"math/rand"
	"reflect"
	"strings"
	"time"
)

const KEY_AUTH_CONTEXT = "authContext"

type commonFunction struct {
	infra *authInfra
}

func newCommonFunction(instance *authInfra) *commonFunction {
	return &commonFunction{infra: instance}

}

func (a *commonFunction) FetchAppByCountry(ctx context.Context, params *cpauthdao.ParamsAppSelect, country string) (listAppModel *authv1.BackofficeAppModel, err error) {
	listApp, _, err := a.FetchApp(ctx, params)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(listApp) == 0 {
		return nil, errors.New("app not found")
	}
	currentApp := listApp[0]
	if country == "vn" {
		for _, app := range listApp {
			if app.AppCountry == algoenumv1.AppCountry_APP_COUNTRY_VN {
				currentApp = app
				break
			}
		}
	}
	return currentApp, nil
}

func (a *commonFunction) FetchApp(ctx context.Context, params *cpauthdao.ParamsAppSelect) (listAppModels []*authv1.BackofficeAppModel, total int64, err error) {
	total, err = a.infra.cpAppRepo.Count(ctx, params)
	if err != nil {
		return listAppModels, 0, errors.WithStack(err)
	}

	listAppDao, err := a.infra.cpAppRepo.Select(ctx, params)
	if err != nil {
		return listAppModels, 0, errors.WithStack(err)
	}
	listAppModels = make([]*authv1.BackofficeAppModel, 0, len(listAppDao))
	for _, v := range listAppDao {
		listAppModels = append(listAppModels, &authv1.BackofficeAppModel{
			IdApp:          v.IdApp.Hex(),
			Name:           v.Name,
			Domain:         v.Domain,
			AppType:        ConvertAppTypeDaoToAppType(v.AppType),
			AppCountry:     ConvertAppCountryEnumToAppCountryDao(v.AppCountry),
			ShortPublicKey: fmt.Sprintf("%x", md5.Sum([]byte(v.PublicEd25519)))[:10],
			TokenTtl:       uint64(v.TokenTTL),
			IsActive:       v.IsActive(),
		})
	}

	return listAppModels, total, nil
}
func (a *commonFunction) Fibonacci(ctx context.Context, n int) (res int64) {
	if n <= 1 {
		return 1
	}
	return a.Fibonacci(ctx, n-1) + a.Fibonacci(ctx, n-2)
}
func (a *commonFunction) GetLockByIp(ctx context.Context, ip string, numberStartLock int) (err error) {
	// TODO : enable on production,
	countByIp, err := a.infra.SessionManager().CountByIp(ctx, ip)
	if err != nil {
		return errors.WithStack(err)
	}
	if countByIp > numberStartLock {
		minusLock := a.infra.CommonFunction().Fibonacci(ctx, countByIp-numberStartLock)
		timeExpired := time.Duration(minusLock) * time.Minute
		err = a.infra.SessionManager().SetCountByIp(ctx, ip, countByIp+1, timeExpired)
		if err != nil {
			return errors.WithStack(err)
		}
		return autherr.ErrLockSpam
	}
	err = a.infra.SessionManager().SetCountByIp(ctx, ip, countByIp+1, 2*time.Minute)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (a *commonFunction) FetchConfigTemplateMail(ctx context.Context, idAuthApp algouid.UUID, name authenum.TemplateEmailType) (res *authv1.ConfigTemplateEmailModel, err error) {
	res = &authv1.ConfigTemplateEmailModel{}

	err = a.infra.ConfigManager().GetConfig(ctx, configredis.MAIL_TEMPLATE, name.String(), idAuthApp.Hex(), res)
	if err == nil || res.Content == "" {
		params := pdb.OptionParams[cpauthdao.ParamsConfigTemplateEmailSelect](
			cpauthdao.ConfigTemplateEmailSelectWithName(name),
			cpauthdao.ConfigTemplateEmailSelectWithIdAuthApp(idAuthApp),
			cpauthdao.ConfigTemplateEmailSelectWithActiveOrDeactivate(true, false),
			cpauthdao.ConfigTemplateEmailSelectWithPagination(0, 1),
		)
		listDao, err := a.infra.cpConfigTemplateEmailRepo.Select(ctx, params)
		if err != nil {
			return res, errors.WithStack(err)
		}
		if len(listDao) == 0 {
			return res, autherr.ErrConfigNotExist
		}
		current := listDao[0]
		res.IdAuthApp = current.IdAuthApp.Hex()
		res.Name = ConvertTemplateEmailTypeDaoToTemplateEmail(current.Name)
		res.Content = current.Content
		res.IsActive = current.IsActive()
		_ = a.infra.ConfigManager().SetConfig(ctx, configredis.MAIL_TEMPLATE, name.String(), idAuthApp.Hex(), res)
	}
	return res, err
}

func (a *commonFunction) GeneratePasswordAndSalt(rawPassword string) (hashPassword string, salt string) {
	salt = arandom.Char(MaxLengthSalt)
	sha256Hasher := sha256.New()
	sumMd5 := md5.Sum([]byte(rawPassword))

	sha256Hasher.Write(sumMd5[:])
	sha256Hasher.Write([]byte(salt))
	hashesByte := sha256Hasher.Sum(nil)
	return hex.EncodeToString(hashesByte[:]), salt
}
func (a *commonFunction) VerifyPasswordAndSalt(rawPassword string, hashPassword string, salt string) (bool, error) {
	sha256Hasher := sha256.New()
	sumMd5 := md5.Sum([]byte(rawPassword))
	sha256Hasher.Write(sumMd5[:])
	sha256Hasher.Write([]byte(salt))
	hashRawPassword := sha256Hasher.Sum(nil)

	hashPasswordByte, err := hex.DecodeString(hashPassword)
	if err != nil {
		return false, errors.WithStack(err)
	}
	if subtle.ConstantTimeCompare(hashPasswordByte, hashRawPassword) == 1 {
		return true, nil
	}
	return false, nil
}

func (a *commonFunction) GenerateTotp(issuer, email string) (*otp.Key, error) {
	return totp.Generate(totp.GenerateOpts{
		Issuer:      issuer,
		AccountName: email,
		Period:      30,
		SecretSize:  20,
		Digits:      otp.DigitsSix,
		Algorithm:   otp.AlgorithmSHA1,
	})
}

func (a *commonFunction) ConvertImageToBase64(img image.Image) (string, error) {
	var buf bytes.Buffer
	err := png.Encode(&buf, img)
	if err != nil {
		return "", err
	}
	encoded := base64.StdEncoding.EncodeToString(buf.Bytes())
	return encoded, nil
}

func (a *commonFunction) FetchConfigsByTypes(ctx context.Context, configTypes map[string]interface{}, idAppHex string) (err error) {

	idApp, err := algouid.FromHex(idAppHex)
	if err != nil {
		return errors.WithStack(err)
	}

	var configKeys []string
	for key := range configTypes {
		configKeys = append(configKeys, key)
	}

	configs, err := a.infra.ConfigRepository().Select(ctx, pdb.OptionParams[cpauthdao.ParamsConfigSelect](
		cpauthdao.ConfigSelectWithAnyType(configKeys),
		cpauthdao.ConfigSelectWithIdAuthApp(idApp),
	))

	if err != nil {
		return errors.WithStack(err)
	}

	if len(configs) != len(configKeys) {
		return errors.New("config_not_exist_" + strings.Join(configKeys, ", "))
	}
	index := 0
	for configType, resVal := range configTypes {
		var foundConfig *cpauthdao.Config
		for _, config := range configs {
			if config.ConfigType == configType {
				foundConfig = config
				break
			}
		}
		if foundConfig == nil {
			return errors.New("config_not_exist_" + configType)
		}

		if reflect.TypeOf(resVal) == reflect.TypeOf(new(string)) {
			configTypes[configType] = foundConfig.Value
			continue
		}

		err = json.Unmarshal([]byte(foundConfig.Value), &resVal)
		index += 1
		if err != nil {
			return errors.WithStack(err)
		}

		configTypes[configType] = resVal
	}

	return nil
}

func (a *commonFunction) GenerateRefCode(identifier string) (ref_code string) {
	ref_code = ""
	parts := strings.Split(identifier, "@")
	const listRandom = "0123456789abcdefghijklmnopqrstuvwxyz"
	i := 0
	for i = 0; i < MaxLengthRefCode; i++ {
		ref_code += string(listRandom[rand.Intn(len(listRandom))])
	}
	ref_code += parts[0]
	return strings.ToLower(ref_code)
}

func (s *commonFunction) GenerateRefreshToken(ctx context.Context, idAuthApp string, deviceId string, params *usercontext.UserContext) (refreshToken string, err error) {

	err = s.infra.Transaction().Transaction(ctx, func(tranCtx context.Context) (err error) {
		listRefreshTokenDao, err := s.infra.DeviceRepository().Select(tranCtx, pdb.OptionParams[cpauthdao.ParamsDeviceSelect](
			cpauthdao.DeviceSelectWithDeviceId(deviceId),
			cpauthdao.DeviceSelectWithIdAuthApp(algouid.FromHexOrNil(idAuthApp)),
			cpauthdao.DeviceSelectWithIdAuthUser(algouid.FromHexOrNil(params.IdAuthUser)),
			cpauthdao.DeviceSelectWithActiveOrDeactivate(true, false),
			cpauthdao.DeviceSelectWithPagination(0, 1),
		))
		if err != nil {
			return errors.WithStack(err)
		}
		if len(listRefreshTokenDao) > 0 && listRefreshTokenDao[0].TokenExpiry < time.Now().Unix() {
			return autherr.ErrTokenExpired
		}
		refreshToken, err = s.infra.SessionManager().GenerateRefreshToken(tranCtx, idAuthApp, deviceId, params)
		if err != nil {
			return errors.WithStack(err)
		}

		tnow := time.Now()
		device := &cpauthdao.Device{
			DeviceId:     deviceId,
			IdAuthUser:   algouid.FromHexOrNil(params.IdAuthUser),
			IdAuthApp:    algouid.FromHexOrNil(idAuthApp),
			RefreshToken: refreshToken,
			DeviceName:   params.UserAgent,
			TokenExpiry:  tnow.Add(30 * 24 * time.Hour).Unix(),
			LastUsedAt:   tnow.Unix(),
			AtUnix: pdb.AtUnix{
				CreatedAt: tnow.Unix(),
				UpdatedAt: tnow.Unix(),
				DeletedAt: 0,
			},
		}

		if len(listRefreshTokenDao) == 0 {
			device.IdDevice, err = algouid.NewUUID()
			if err != nil {
				return errors.WithStack(err)
			}
			if err = s.infra.DeviceRepository().Create(tranCtx, device); err != nil {
				return errors.WithStack(err)
			}
		} else {
			currentDevice := listRefreshTokenDao[0]
			paramUpdate := pdb.OptionParams[cpauthdao.ParamsDeviceUpdate](
				cpauthdao.DeviceUpdateDeviceName(currentDevice.DeviceName, device.DeviceName),
				cpauthdao.DeviceUpdateLastUsedAt(currentDevice.LastUsedAt, device.LastUsedAt),
				cpauthdao.DeviceUpdateRefreshToken(currentDevice.RefreshToken, device.RefreshToken),
				cpauthdao.DeviceUpdateTokenExpiry(currentDevice.TokenExpiry, device.TokenExpiry),
				cpauthdao.DeviceUpdateDeviceId(currentDevice.DeviceId, device.DeviceId),
			)
			paramUpdate.IdDevice = currentDevice.IdDevice

			if err = s.infra.DeviceRepository().Update(tranCtx, paramUpdate); err != nil {
				return errors.WithStack(err)
			}
		}
		return nil
	})
	if err != nil {
		return "", errors.WithStack(err)
	}

	return refreshToken, nil
}
