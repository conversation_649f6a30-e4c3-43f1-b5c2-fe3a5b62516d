CREATE TABLE IF NOT EXISTS "oauth_config" (
                                    "id_o_auth_config" UUID PRIMARY KEY,
                                    "id_auth_app" UUID,
                                    "client_id" VARCHAR,
                                    "client_secret" VARCHAR,
                                    "created_at" BIGINT NOT NULL,
                                    "updated_at" BIGINT NOT NULL,
                                    "deleted_at" BIGINT NOT NULL
);
CREATE INDEX "idx_oauth_config_id_auth_app_deleted_at" ON "oauth_config" ("id_auth_app","deleted_at");