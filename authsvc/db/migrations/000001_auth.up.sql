CREATE TABLE if not exists "auth_app" (
                            "id_auth_app" uuid PRIMARY KEY,
                            "name" varchar(64) UNIQUE NOT NULL,
                            "app_type" varchar(64) NOT NULL,
                            "domain" varchar(128) NOT NULL,
                            "token_ttl" bigint NOT NULL,
                            "private_ed25519" varchar(256) NOT NULL,
                            "public_ed25519" varchar(256) NOT NULL,
                            "app_country" varchar(64) NOT NULL,
                            "created_at" bigint NOT NULL,
                            "updated_at" bigint NOT NULL,
                            "deleted_at" bigint NOT NULL
);

CREATE INDEX "idx_auth_app_type" ON "auth_app" ("app_type","domain","app_country");
CREATE INDEX "idx_auth_app_deleted_at" ON "auth_app" ("deleted_at");

CREATE TABLE if not exists "auth_role" (
                             "id_auth_role" uuid PRIMARY KEY,
                             "role_name" varchar(64) UNIQUE NOT NULL,
                             "priority" bigint NOT NULL,
                             "created_at" bigint NOT NULL,
                             "updated_at" bigint NOT NULL,
                             "deleted_at" bigint NOT NULL
);
CREATE INDEX "idx_auth_role_deleted_at" ON "auth_role" ("deleted_at");

CREATE TABLE if not exists "auth_user" (
                             "id_auth_user" uuid PRIMARY KEY,
                             "id_auth_app" uuid NOT NULL,
                             "id_auth_role" uuid NOT NULL,
                             "email" varchar(64) NOT NULL,
                             "hash_password" varchar(64) NOT NULL,
                             "salt" varchar(16) NOT NULL,
                             "created_at" bigint NOT NULL,
                             "updated_at" bigint NOT NULL,
                             "deleted_at" bigint NOT NULL
);

CREATE INDEX "idx_auth_user_deleted_at" ON "auth_user" ("deleted_at");
CREATE INDEX "idx_auth_user_auth_app" ON "auth_user" ("id_auth_app");

CREATE TABLE if not exists "auth_service" (
                                "id_auth_service" uuid PRIMARY KEY,
                                "name" varchar(255) UNIQUE NOT NULL,
                                "created_at" bigint NOT NULL,
                                "updated_at" bigint NOT NULL,
                                "deleted_at" bigint NOT NULL
);

CREATE TABLE if not exists "auth_policy" (
                               "id_auth_policy" uuid PRIMARY KEY,
                               "id_auth_path" uuid NOT NULL,
                               "id_auth_role" uuid NOT NULL,
                               "created_at" bigint NOT NULL,
                               "updated_at" bigint NOT NULL,
                               "deleted_at" bigint NOT NULL
);
CREATE UNIQUE INDEX "idx_unique_policy" ON auth_policy (id_auth_path, id_auth_role);

CREATE TABLE if not exists "auth_path" (
                             "id_auth_path" uuid PRIMARY KEY,
                             "id_auth_service" uuid NOT NULL,
                             "absolute_path" varchar(256) UNIQUE NOT NULL,
                             "created_at" bigint NOT NULL,
                             "updated_at" bigint NOT NULL,
                             "deleted_at" bigint NOT NULL
);

CREATE TABLE if not exists  "auth_user_detail" (
                                     "id_auth_user_detail" uuid NOT NULL,
                                     "id_auth_user" uuid NOT NULL,
                                     "first_name" varchar(128) NOT NULL,
                                     "last_name" varchar(128) NOT NULL,
                                     "phone_number" varchar(128) NOT NULL,
                                     "ref_code" varchar(100) NOT NULL ,
                                     "user_ref_id" uuid not null ,

                                     "created_at" bigint NOT NULL,
                                     "updated_at" bigint NOT NULL,
                                     "deleted_at" bigint NOT NULL
);
CREATE INDEX "idx_auth_user_details_id_user_deleted_at" ON "auth_user_detail" ("id_auth_user","deleted_at");

CREATE TABLE if not exists  "auth_user_totp" (
                                  "id_auth_user_totp" uuid NOT NULL,
                                  "id_auth_user" uuid NOT NULL,
                                  "totp_type" varchar NOT NULL,
                                  "secret" varchar NOT NULL,
                                  "created_at" bigint NOT NULL,
                                  "updated_at" bigint NOT NULL,
                                  "deleted_at" bigint NOT NULL
);
CREATE INDEX "idx_auth_user_totp_id_user_deleted_at" ON "auth_user_totp" ("id_auth_user","deleted_at");
