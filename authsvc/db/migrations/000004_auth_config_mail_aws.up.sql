CREATE TABLE IF NOT EXISTS "auth_config_mail" (
    "id_config_mail" uuid NOT NULL,
    "id_auth_app" uuid,
    "smtp_server_address" varchar(256),
    "smtp_server_port" int,
    "auth_username" varchar(256),
    "auth_password" varchar(256),
    "sender_email" varchar(256),
    "created_at" bigint NOT NULL,
    "updated_at" bigint NOT NULL,
    "deleted_at" bigint NOT NULL
    );

CREATE INDEX "idx_auth_config_mail_id_app_deleted_at" ON "auth_config_mail" ("id_auth_app","deleted_at");