
CREATE TABLE IF NOT EXISTS "auth_config_template_email" (
                                    "id_config_template_email" uuid NOT NULL,
                                    "id_auth_app" uuid,
                                    "name" varchar,
                                    "content" text,
                                    "created_at" bigint NOT NULL,
                                    "updated_at" bigint NOT NULL,
                                    "deleted_at" bigint NOT NULL
);

CREATE INDEX "idx_auth_config_template_mail_id_app_deleted_at" ON "auth_config_template_email" ("id_auth_app","deleted_at");