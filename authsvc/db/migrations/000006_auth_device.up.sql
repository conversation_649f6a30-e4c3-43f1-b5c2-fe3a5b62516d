

CREATE TABLE IF NOT EXISTS "auth_device" (
                                    "id_device" UUID NOT NULL,
                                    "id_auth_app" UUID,
                                    "id_auth_user" UUID,
                                    "device_id" VARCHAR,
                                    "device_name" VARCHAR,
                                    "refresh_token" TEXT,
                                    "token_expiry" BIGINT,
                                    "last_used_at" BIGINT,
                                    "created_at" BIGINT NOT NULL,
                                    "updated_at" BIGINT NOT NULL,
                                    "deleted_at" BIGINT NOT NULL
);

CREATE INDEX "idx_auth_device_id_auth_app_deleted_at" ON "auth_device" ("id_auth_app","id_auth_user","deleted_at");