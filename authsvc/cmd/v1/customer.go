package main

import (
	"connectrpc.com/connect"
	"git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1/authv1connect"
	"git.tmproxy-infra.com/algo/common/pkg/interceptorutils"
	"go.uber.org/zap"
	"net/http"
)

func newCustomerServer(customerService authv1connect.CustomerAuthServiceHandler, internalHandler authv1connect.InternalAuthServiceHandler, logger *zap.Logger, tracer connect.Interceptor, customerPort string) *http.Server {
	pathCustomer, pathCustomerHandler := authv1connect.NewCustomerAuthServiceHandler(customerService, connect.WithInterceptors(
		interceptorutils.NewLoggingInterceptor(logger.Named("auth_customer_logger")),
		authorizationInterceptor(internalHandler),
		tracer,
		//interceptorutils.NewSingleFlightInterceptor(),
	), connect.WithIdempotency(connect.IdempotencyIdempotent))
	return newServer(customerPort, []pathAndHandler{
		{
			Path:    pathCustomer,
			Handler: pathCustomerHandler,
		},
	})
}
