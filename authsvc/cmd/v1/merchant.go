package main

import (
	"connectrpc.com/connect"
	"git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1/authv1connect"
	"git.tmproxy-infra.com/algo/common/pkg/interceptorutils"
	"go.uber.org/zap"
	"net/http"
)

func newMerchantServer(merchantSvc authv1connect.MerchantAuthServiceHandler, internalHandler authv1connect.InternalAuthServiceHandler, logger *zap.Logger, tracer connect.Interceptor, merchantSvcPort string) *http.Server {
	pathMerchantService, pathMerchantHandler := authv1connect.NewMerchantAuthServiceHandler(merchantSvc, connect.WithInterceptors(
		interceptorutils.NewLoggingInterceptor(logger.Named("auth_merchant_logger")),
		authorizationInterceptor(internalHandler),
		tracer,
		//interceptorutils.NewSingleFlightInterceptor(),
	), connect.WithIdempotency(connect.IdempotencyIdempotent))
	return newServer(merchantSvcPort, []pathAndHandler{
		{
			Path:    pathMerchantService,
			Handler: pathMerchantHandler,
		},
	})

}
