package main

import (
	"context"
	"os"
	"os/signal"
	"strings"
	"time"

	sendemailotphandler "git.tmproxy-infra.com/algo/authsvc/internal/asynctask/sendemailotp"
	sendemailotpforgotpasswordhandler "git.tmproxy-infra.com/algo/authsvc/internal/asynctask/sendemailotpforgotpassword"
	authinfra "git.tmproxy-infra.com/algo/authsvc/internal/infra"
	backofficev1svc "git.tmproxy-infra.com/algo/authsvc/internal/service/backoffice/v1"
	customerv1svc "git.tmproxy-infra.com/algo/authsvc/internal/service/customer/v1"
	internalv1svc "git.tmproxy-infra.com/algo/authsvc/internal/service/internalauth/v1"
	merchantv1svc "git.tmproxy-infra.com/algo/authsvc/internal/service/merchant/v1"
	"git.tmproxy-infra.com/algo/common/asynqtask/sendemailotp"
	"git.tmproxy-infra.com/algo/common/asynqtask/sendemailotpforgotpassword"
	"git.tmproxy-infra.com/algo/common/pkg/algoredis"
	"git.tmproxy-infra.com/algo/common/pkg/algouid"
	"git.tmproxy-infra.com/algo/common/pkg/healthcheck"
	"git.tmproxy-infra.com/algo/common/pkg/interceptorutils"
	"git.tmproxy-infra.com/algo/common/pkg/pdb"
	"github.com/hibiken/asynq"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/sony/gobreaker/v2"
	"github.com/sourcegraph/conc"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
)

func main() {
	ctx := context.Background()
	logger, err := zap.NewProduction()
	nameService := "authsvc.Customer"

	jaegerCollectorURL := os.Getenv("JAEGER_COLLECTOR_URL")
	dbURL := os.Getenv("AUTH_DATABASE_URL")
	redisStackSentinels := strings.Split(os.Getenv("AUTH_REDIS_STACK_URL"), ",")
	redisStackMasterName := os.Getenv("AUTH_REDIS_STACK_MASTER_NAME")
	redisStackAuthPass := os.Getenv("AUTH_REDIS_STACK_AUTH_PASS")

	customerPort := os.Getenv("CUSTOMER_SVC_PORT")
	merchantSvcPort := os.Getenv("MERCHANT_SVC_PORT")
	backofficeSvcPort := os.Getenv("BACKOFFICE_SVC_PORT")
	internalSvcPort := os.Getenv("INTERNAL_SVC_PORT")

	billingInternalURL := os.Getenv("BILLING_INTERNAL_URL")
	miscInternalURL := os.Getenv("MISC_INTERNAL_URL")
	dbConfigPgPool, err := pgxpool.ParseConfig(dbURL)
	if err != nil {
		logger.Fatal("error parse dbURL", zap.Error(err))
	}
	dbConfigPgPool.HealthCheckPeriod = 3 * time.Second
	dbPool, err := pgxpool.NewWithConfig(ctx, dbConfigPgPool)
	if err != nil {
		logger.Fatal("error init pg_pool", zap.Error(err))
	}

	// ping check Postgresql
	redisHA, err := algoredis.NewStackSentinel(ctx, nameService, &redis.FailoverOptions{
		SentinelAddrs: redisStackSentinels,
		MasterName:    redisStackMasterName,
		Password:      redisStackAuthPass,
	}, func(counts gobreaker.Counts) bool { // circuit breaker
		failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
		return counts.Requests >= 3 && failureRatio >= 0.6
	})
	if err != nil {
		logger.Fatal("error init redis-ha", zap.Error(err))
	}
	redisConnOpt := asynq.RedisFailoverClientOpt{
		MasterName:    redisStackMasterName,
		SentinelAddrs: redisStackSentinels,
		DB:            0,
		Password:      redisStackAuthPass, // Nếu có
	}

	asynqClient := asynq.NewClient(redisConnOpt)
	defer asynqClient.Close()
	asynqServer := asynq.NewServer(
		redisConnOpt,
		asynq.Config{
			Concurrency: 50, // Số lượng worker đồng thời
			Queues: map[string]int{
				"critical": 1000, // Ưu tiên cao nhất
				"default":  1000,
				"low":      1,
			},
		},
	)

	tracerProvider, err := interceptorutils.NewTraceProvider(ctx,
		nameService,
		otlptracegrpc.WithInsecure(),
		otlptracegrpc.WithEndpoint(jaegerCollectorURL),
		otlptracegrpc.WithCompressor("gzip"))
	if err != nil {
		logger.Fatal("error init tracer", zap.Error(err))
	}
	tracerInterceptor := interceptorutils.NewTraceInterceptor(nameService, tracerProvider)
	healthCheckInfra := func() (err error) {
		ctxTimeoutHealthCheck, cancelCtxHealthCheck := context.WithTimeout(ctx, 30*time.Second)
		defer cancelCtxHealthCheck()
		errWg, _ := errgroup.WithContext(ctxTimeoutHealthCheck)
		errWg.Go(func() error {
			stmt, err := dbPool.Query(ctxTimeoutHealthCheck, "select 1;")
			if err != nil {
				return errors.WithStack(err)
			}
			defer stmt.Close()
			return nil
		})
		errWg.Go(func() error {
			uidTest, err := algouid.NewUUID()
			if err != nil {
				return errors.WithStack(err)
			}
			writer, err := redisHA.Writer(ctxTimeoutHealthCheck)
			if err != nil {
				return errors.WithStack(err)
			}
			err = writer.Set(ctxTimeoutHealthCheck, uidTest.String(), 1, 10).Err()
			if err != nil {
				return errors.WithStack(err)
			}
			return nil
		})
		errWg.Go(func() error {
			return asynqServer.Ping()
		})
		if err := errWg.Wait(); err != nil {
			return errors.WithStack(err)
		}
		return nil
	}

	if err = healthCheckInfra(); err != nil {
		logger.Fatal("health check failed", zap.Error(err))
		return
	}
	var (
		dbService, transaction = pdb.NewDBService(dbPool)
	)
	infra, err := authinfra.NewService(ctx, logger, dbService, transaction, redisHA, billingInternalURL, miscInternalURL, asynqClient)
	if err != nil {
		logger.Fatal("error init service", zap.Error(err))
	}
	sendEmailOtpHandler := sendemailotphandler.NewSendEmailOtpHandler(infra)
	sendEmailOtpForgotPasswordHandler := sendemailotpforgotpasswordhandler.NewSendEmailOtpForgotPasswordHandler(infra)
	asynqMuxServer := asynq.NewServeMux()
	asynqMuxServer.HandleFunc(sendemailotp.TaskAuthSendEmailOTP, sendEmailOtpHandler.HandleSendEmailOTP)
	asynqMuxServer.HandleFunc(sendemailotpforgotpassword.TaskAuthSendEmailOTPForgotPassword, sendEmailOtpForgotPasswordHandler.HandleSendEmailOTPForgotPassword)
	internal := internalv1svc.NewAuthInternalService(infra)
	infra.SetAuthInternalHandler(internal)
	if err = infra.Reload(ctx); err != nil {
		logger.Fatal("error reload infra", zap.Error(err))
	}

	customerServer := newCustomerServer(customerv1svc.NewCustomerAuthService(infra), internal, logger, tracerInterceptor, customerPort)
	merchantServer := newMerchantServer(merchantv1svc.NewMerchantAuthService(infra), internal, logger, tracerInterceptor, merchantSvcPort)
	backOfficeServer := newBackOfficeServer(backofficev1svc.NewBackofficeAuthService(infra), internal, logger, tracerInterceptor, backofficeSvcPort)
	internalServer := newInternalServer(internal, logger, tracerInterceptor, internalSvcPort)

	healthCheck := healthcheck.NewHealthCheck("", "80", "/healthz")

	wg := conc.NewWaitGroup()
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt)
	wg.Go(func() {
		logger.Info("start server: " + customerPort)
		_ = customerServer.ListenAndServe()
	})
	wg.Go(func() {
		logger.Info("start server: " + merchantSvcPort)
		_ = merchantServer.ListenAndServe()
	})
	wg.Go(func() {
		logger.Info("start server: " + backofficeSvcPort)
		_ = backOfficeServer.ListenAndServe()
	})
	wg.Go(func() {
		logger.Info("start server: " + backofficeSvcPort)
		_ = internalServer.ListenAndServe()
	})
	wg.Go(func() {
		logger.Info("serve /healthz: 80")
		_ = healthCheck.Listen()
	})
	wg.Go(func() {
		logger.Info("serve /healthz: 80")
		if err = asynqServer.Start(asynqMuxServer); err != nil {
			logger.Error("error start asynq server", zap.Error(err))
		}
	})
	<-c
	ctxCloseServer, _ := context.WithTimeout(ctx, 5*time.Second)
	eg, _ := errgroup.WithContext(ctxCloseServer)
	eg.Go(func() error {
		return customerServer.Shutdown(ctxCloseServer)
	})
	eg.Go(func() error {
		return merchantServer.Shutdown(ctxCloseServer)
	})
	eg.Go(func() error {
		return backOfficeServer.Shutdown(ctxCloseServer)
	})
	eg.Go(func() error {
		return internalServer.Shutdown(ctxCloseServer)
	})
	eg.Go(func() error {
		return healthCheck.Stop(ctxCloseServer)
	})
	eg.Go(func() error {
		asynqServer.Shutdown()
		return nil
	})
	if err = eg.Wait(); err != nil {
		logger.Error("error shutdown server", zap.Error(err))
	}
	wg.Wait()
}
