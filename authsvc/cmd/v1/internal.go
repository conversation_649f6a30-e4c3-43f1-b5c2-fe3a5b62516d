package main

import (
	"connectrpc.com/connect"
	"git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1/authv1connect"
	"git.tmproxy-infra.com/algo/common/pkg/interceptorutils"
	"go.uber.org/zap"
	"net/http"
)

func newInternalServer(internalAuthSvc authv1connect.InternalAuthServiceHandler, logger *zap.Logger, tracer connect.Interceptor, backOfficePort string) *http.Server {
	internalServiceMux, internalHandler := authv1connect.NewInternalAuthServiceHandler(internalAuthSvc, connect.WithInterceptors(
		interceptorutils.NewLoggingInterceptor(logger.Named("auth_internal_logging")),
		tracer,

		//NewAuthorizationInterceptor(infra.GetAuthInternalHandler()), // TODO: check permission internal service
		interceptorutils.NewSingleFlightInterceptor()), connect.WithIdempotency(connect.IdempotencyIdempotent))
	backofficeAuthServer := newServer(backOfficePort, []pathAndHandler{
		{
			Path:    internalServiceMux,
			Handler: internalHandler,
		},
	})
	return backofficeAuthServer
}
