package main

import (
	"context"
	"net"
	"net/http"
	"strings"
	"time"

	"connectrpc.com/connect"
	authv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1"
	"git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1/authv1connect"
	errmsgv1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	"git.tmproxy-infra.com/algo/common/pkg/usercontext"
	"github.com/jackc/pgx/v5"
	"go.uber.org/zap"
	"golang.org/x/net/http2"
	"golang.org/x/net/http2/h2c"
)

func authorizationInterceptor(internalAuthService authv1connect.InternalAuthServiceClient) connect.UnaryInterceptorFunc {
	return func(next connect.UnaryFunc) connect.UnaryFunc {
		return func(ctx context.Context, req connect.AnyRequest) (connect.AnyResponse, error) {
			authFailResponse := &authv1.InternalAuthServiceVerifyResponse{
				User:  nil,
				Error: &errmsgv1.ErrorMessage{},
			}
			var (
				token     = tokenFromHeader(req.Header().Get("Authorization"))
				xRealIp   = req.Header().Get("X-Real-Ip")
				xRealHost = req.Header().Get("X-Real-Host")
			)
			verifyRequest := connect.NewRequest(&authv1.InternalAuthServiceVerifyRequest{
				UserIp:       xRealIp,
				Token:        token,
				AbsolutePath: req.Spec().Procedure,
			})
			verifyRequest.Header().Set("X-Real-Host", xRealHost)
			verifyRequest.Header().Set("X-Real-IP", xRealIp)
			veriResponse, err := internalAuthService.Verify(ctx, verifyRequest)
			if err != nil {
				authFailResponse.Error.Code = veriResponse.Msg.Error.Code
				return connect.NewResponse[authv1.InternalAuthServiceVerifyResponse](authFailResponse), err
			}
			if veriResponse.Msg.Error.Code != errmsgv1.ErrorCode_ERROR_CODE_SUCCESS && veriResponse.Msg.Error.Code != errmsgv1.ErrorCode_ERROR_CODE_PUBLIC_URL {
				authFailResponse.Error.Code = veriResponse.Msg.Error.Code
				return connect.NewResponse[authv1.InternalAuthServiceVerifyResponse](authFailResponse), nil
			}

			resp := veriResponse.Msg
			ctxWithUserCtx := usercontext.InjectCtx(ctx, &usercontext.UserContext{
				IdAuthApp:  resp.GetUser().GetIdApp(),
				IdAuthUser: resp.GetUser().GetIdUser(),
				IdAuthRole: resp.GetUser().GetIdRole(),
				UserAgent:  req.Header().Get("User-Agent"),
				UserIP:     req.Header().Get("X-Real-IP"),
			})
			return next(ctxWithUserCtx, req)
		}
	}
}

type pathAndHandler struct {
	Path    string
	Handler http.Handler
}

func newServer(port string, listHandler []pathAndHandler) *http.Server {
	mux := http.NewServeMux()
	for _, item := range listHandler {
		mux.Handle(item.Path, item.Handler)
	}
	return &http.Server{
		Addr:                         net.JoinHostPort("", port),
		Handler:                      h2c.NewHandler(mux, &http2.Server{}),
		DisableGeneralOptionsHandler: false,
		TLSConfig:                    nil,
		ReadTimeout:                  5 * time.Second,
		ReadHeaderTimeout:            5 * time.Second,
		WriteTimeout:                 5 * time.Second,
		IdleTimeout:                  5 * time.Second,
		MaxHeaderBytes:               5 << 20,
	}
}

type myQueryTracer struct {
	log *zap.Logger
}

func (tracer *myQueryTracer) TraceQueryStart(
	ctx context.Context,
	_ *pgx.Conn,
	data pgx.TraceQueryStartData) context.Context {
	tracer.log.Info("query: ", zap.String("sql", data.SQL), zap.Any("args", data.Args))
	return ctx
}

func (tracer *myQueryTracer) TraceQueryEnd(ctx context.Context, conn *pgx.Conn, data pgx.TraceQueryEndData) {
	tracer.log.Info("query: ", zap.Error(data.Err), zap.Any("command tag", data.CommandTag))
}
func tokenFromHeader(headerValue string) (token string) {
	if len(headerValue) > 7 && strings.ToLower(headerValue[0:6]) == "bearer" {
		return headerValue[7:]
	}
	return ""
}
