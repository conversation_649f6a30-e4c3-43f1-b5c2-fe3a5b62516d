package main

import (
	"connectrpc.com/connect"
	"git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1/authv1connect"
	"git.tmproxy-infra.com/algo/common/pkg/interceptorutils"
	"go.uber.org/zap"
	"net/http"
)

func newBackOfficeServer(backofficeHandler authv1connect.BackofficeAuthServiceHandler, internalHandler authv1connect.InternalAuthServiceHandler, logger *zap.Logger, tracer connect.Interceptor, backOfficePort string) *http.Server {
	pathBackOffice, backOfficeHandler := authv1connect.NewBackofficeAuthServiceHandler(backofficeHandler, connect.WithInterceptors(
		interceptorutils.NewLoggingInterceptor(logger.Named("auth_backoffice_logger")),
		authorizationInterceptor(internalHandler),
		tracer,
		//interceptorutils.NewSingleFlightInterceptor(), TODO: implement decentralized singleflight
	), connect.WithIdempotency(connect.IdempotencyIdempotent))
	backofficeAuthServer := newServer(backOfficePort, []pathAndHandler{
		{
			Path:    pathBackOffice,
			Handler: backOfficeHandler,
		},
	})
	return backofficeAuthServer
}
