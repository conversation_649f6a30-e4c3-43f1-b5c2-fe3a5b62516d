FROM golang:1.24.1-bookworm AS builder
ARG ALGOPROXY_FETCH_REPO_TOKEN

RUN apt install git

# <PERSON><PERSON>u hình Git để sử dụng SSH thay vì HTTPS
RUN git config --global url."https://ALGOPROXY_FETCH_REPO_TOKEN:$<EMAIL>/".insteadOf "https://git.tmproxy-infra.com/"

# Set GOPRIVATE cho private repositories
ENV GOPRIVATE=git.tmproxy-infra.com

WORKDIR /app
COPY . .
RUN go mod tidy
RUN CGO_ENABLED=0 GOOS=linux go build -o authapp cmd/v1/*.go


FROM debian:bookworm-slim AS authv1
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
COPY --from=builder /app/authapp /authapp
CMD ["/authapp"]
EXPOSE 80 10000 11000 15000 20000

