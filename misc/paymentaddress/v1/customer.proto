syntax = "proto3";
package misc.paymentaddress.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/misc/paymentaddress/v1;paymentaddressv1";

import "utils/v1/utils.proto";
import "errmsg/v1/errormsg.proto";
import "misc/paymentaddress/v1/entity.proto";

service CustomerPaymentAddressService {
   rpc FetchCountries(CustomerPaymentAddressServiceFetchCountriesRequest) returns (CustomerPaymentAddressServiceFetchCountriesResponse);
   rpc FetchStates(CustomerPaymentAddressServiceFetchStatesRequest) returns (CustomerPaymentAddressServiceFetchStatesResponse);
   rpc FetchCities(CustomerPaymentAddressServiceFetchCitiesRequest) returns (CustomerPaymentAddressServiceFetchCitiesResponse);
}

message CustomerPaymentAddressServiceFetchCountriesRequest {
  string country_name_search = 1;
  utils.v1.PaginationRequest pagination = 2;
}

message CustomerPaymentAddressServiceFetchCountriesResponse {
  errmsg.v1.ErrorMessage  error =1;
  utils.v1.PaginationResponse pagination = 2;
  repeated Country countries = 3;
}

message CustomerPaymentAddressServiceFetchStatesRequest {
  int64 id_country  = 1;
  string state_name_search = 2;
  utils.v1.PaginationRequest pagination = 3;
}
message CustomerPaymentAddressServiceFetchStatesResponse {
  errmsg.v1.ErrorMessage  error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated State states = 3;
}

message CustomerPaymentAddressServiceFetchCitiesRequest {
  int64 id_state  = 1;
  string city_name_search = 2;
  utils.v1.PaginationRequest pagination = 3;
}
message CustomerPaymentAddressServiceFetchCitiesResponse {
  errmsg.v1.ErrorMessage  error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated City cities = 3;
}

