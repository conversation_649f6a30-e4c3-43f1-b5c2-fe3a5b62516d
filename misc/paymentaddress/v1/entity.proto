syntax = "proto3";
package misc.paymentaddress.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/misc/paymentaddress/v1;paymentaddressv1";

message State {
  int64 id_state = 1;
  string name = 2;
}
message City {
  int64 id_city = 1;
  string name = 2;
}



message Country {
  int64 id_country = 1;
  string name = 2;
  string iso2 = 3;
  string iso3 = 4;
  string phone_code = 5;
  string emoji = 6;
}