syntax = "proto3";
package misc.paymentaddress.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/misc/paymentaddress/v1;paymentaddressv1";

import "errmsg/v1/errormsg.proto";
import "misc/paymentaddress/v1/entity.proto";

service InternalPaymentAddressService {
  rpc ValidPaymentAddress(InternalPaymentAddressServiceValidPaymentAddressRequest) returns(InternalPaymentAddressServiceValidPaymentAddressResponse);
}

message  InternalPaymentAddressServiceValidPaymentAddressRequest {
  int64 id_country = 1;
  int64 id_state = 2;
  int64 id_city = 3;
}

message InternalPaymentAddressServiceValidPaymentAddressResponse {
  errmsg.v1.ErrorMessage error = 1;
  Country country = 2;
  State state = 3;
  City city = 4;
}