syntax = "proto3";
package misc.dbip.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/misc/dbip/v1;dbipv1";
import "utils/v1/utils.proto";

import "errmsg/v1/errormsg.proto";
import "misc/paymentaddress/v1/entity.proto";

service PublicDBIPService {
  rpc IPInfo(PublicDBIPServiceIPInfoRequest) returns (PublicDBIPServiceIPInfoResponse);

  rpc FetchCountries(PublicDBIPServiceFetchCountriesRequest) returns (PublicDBIPServiceFetchCountriesResponse);
  rpc FetchStates(PublicDBIPServiceFetchStatesRequest) returns (PublicDBIPServiceFetchStatesResponse);
  rpc FetchCities(PublicDBIPServiceFetchCitiesRequest) returns (PublicDBIPServiceFetchCitiesResponse);

}

message PublicDBIPServiceIPInfoRequest{
  string ip_addr = 1;
}
message PublicDBIPServiceIPInfoResponse {
  errmsg.v1.ErrorMessage error =1;
  IPInfo ip_info = 2;
}
message IPInfo {
  NamesRecord region = 1;
  NamesRecord country = 2;
  NamesRecord state = 3;
  NamesRecord city = 4;
  GeoLocation geo_location = 5;
  ConnectionInfo connection_info = 6;
}

message NamesRecord {
  string iso_code = 1;
  string name = 2;
  int64 geoname_id = 3;
}

message GeoLocation {
  double lat = 1;
  double long = 2;
  string time_zone = 3;
  string weather_station = 4;
}
message ConnectionInfo {
  int64 as_number = 1;
  string as_name = 2;
  string organization = 3;
  string connection_type = 4;
  string user_type  = 5;
}


// ------------------------------------------------------
message PublicDBIPServiceFetchCountriesRequest {
  string country_name_search = 1;
  utils.v1.PaginationRequest pagination = 2;
}

message PublicDBIPServiceFetchCountriesResponse {
  errmsg.v1.ErrorMessage  error =1;
  utils.v1.PaginationResponse pagination = 2;
  repeated misc.paymentaddress.v1.Country countries = 3;
}

message PublicDBIPServiceFetchStatesRequest {
  int64 id_country  = 1;
  string state_name_search = 2;
  utils.v1.PaginationRequest pagination = 3;
}
message PublicDBIPServiceFetchStatesResponse {
  errmsg.v1.ErrorMessage  error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated misc.paymentaddress.v1.State states = 3;
}

message PublicDBIPServiceFetchCitiesRequest {
  int64 id_state  = 1;
  string city_name_search = 2;
  utils.v1.PaginationRequest pagination = 3;
}
message PublicDBIPServiceFetchCitiesResponse {
  errmsg.v1.ErrorMessage  error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated misc.paymentaddress.v1.City cities = 3;
}

