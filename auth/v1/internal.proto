syntax = "proto3";
package auth.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1;authv1";

import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";
import "algoenum/v1/app_type.proto";
import "algoenum/v1/app_country.proto";

service InternalAuthService {
  rpc Login(InternalAuthServiceLoginRequest) returns (InternalAuthServiceLoginResponse);
  rpc Verify(InternalAuthServiceVerifyRequest) returns (InternalAuthServiceVerifyResponse);

  rpc FetchApp(InternalAuthServiceFetchAppRequest) returns (InternalAuthServiceFetchAppResponse);
  rpc FetchUser(InternalAuthServiceFetchUserRequest) returns(InternalAuthServiceFetchUserResponse);

  rpc CreateAppMerchant(InternalAuthServiceCreateAppMerchantRequest) returns (InternalAuthServiceCreateAppMerchantResponse);
  rpc FetchUserInfo(InternalAuthServiceFetchUserInfoRequest) returns (InternalAuthServiceFetchUserInfoResponse);

  rpc SendMailHtml(InternalAuthServiceSendMailHtmlRequest) returns (InternalAuthServiceSendMailHtmlResponse);
  rpc RefreshToken(InternalAuthServiceRefreshTokenRequest) returns (InternalAuthServiceRefreshTokenResponse);

  rpc ForgotPassword(InternalAuthServiceForgotPasswordRequest) returns (InternalAuthServiceForgotPasswordResponse);
}
message InternalAuthServiceFetchUserRequest {
  repeated string list_id_user = 1;
}
message InternalAuthServiceFetchUserResponse {
    InternalAuthServiceFetchUserUserModel users = 1;
}

message InternalAuthServiceFetchUserUserModel {
  string id_user = 1;
  string email = 2;
}


message InternalAuthServiceCustomerLoginRequest {
  string email = 1;
  string password = 2;
}
message InternalAuthServiceCustomerLoginResponse {
  errmsg.v1.ErrorMessage error = 1;
  string token = 2;
}

message InternalAuthServiceFetchUserInfoRequest{
  repeated string list_id_user = 1;
}

message InternalAuthServiceFetchUserInfoResponse{
  repeated InternalUserInfoModel users = 1;
  errmsg.v1.ErrorMessage error = 2;
}

message InternalUserInfoModel {
  string id_user = 1;
  string email = 2;
  string first_name =3;
  string last_name = 4;
  bool is_active = 5;
}


message InternalAuthServiceCreateAppMerchantRequest {
  string id_app = 1;
  string name = 2;
  string domain  = 3;
  algoenum.v1.AppCountry app_country = 4;
}
message InternalAuthServiceCreateAppMerchantResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message AuthInternalServiceCreateUserRequest  {
  string id_app = 1;
  string name_search = 2;
  algoenum.v1.AppType app_type = 3;
  utils.v1.State state = 4;
  utils.v1.PaginationRequest pagination = 5;
}
message AuthInternalServiceCreateUserResponse  {
  string id_user = 1;
  errmsg.v1.ErrorMessage error = 2;
}


message InternalAuthServiceFetchAppRequest{
  repeated string list_id_app = 1;
  string name_search = 2;
  string domain_search = 3;
  algoenum.v1.AppType app_type = 4;
  algoenum.v1.AppCountry app_country = 5;
  utils.v1.State state = 6;
  utils.v1.PaginationRequest pagination = 7;
}
message InternalAuthServiceFetchAppResponse{
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated InternalAppModel items = 3;
}

message InternalAuthServiceMerchantLoginRequest {
  string email = 1;
  string password = 2;
  string ip_address = 3;
  string user_agent = 4;
  string otp = 5;
}

message InternalAuthServiceMerchantLoginResponse {
  InternalUserModel user = 1;
  string token = 2;
  errmsg.v1.ErrorMessage error = 3;
}


message InternalAuthServiceLoginRequest {
  string domain = 1;
  string email = 2;
  string password = 3;
  string ip_address = 4;
  string user_agent = 5;
  string otp = 6;
  string device_id = 7;
}

message InternalAuthServiceLoginResponse {
  InternalUserModel user = 1;
  string token = 2;
  string refresh_token = 3;
  errmsg.v1.ErrorMessage error = 4;
}

message InternalAuthServiceLogoutRequest {
  string domain = 1;
  string token = 2;
}
message InternalAuthServiceLogoutResponse {
  bool success = 1;
  errmsg.v1.ErrorMessage error = 2;
}

message InternalAuthServiceRegisterRequest {
  string id_app = 1;
  string id_user = 2;
  string id_role = 3;
  string email = 4;
  string password = 5;
}
message InternalAuthServiceRegisterResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message InternalAuthServiceChangePasswordRequest {
  InternalUserModel user = 1;
  string old_password = 2;
  string new_password = 3;
}

message InternalAuthServiceChangePasswordResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message InternalAuthServiceVerifyRequest {
//  string domain = 1;
  string token = 1;
  string absolute_path = 2;
  string user_ip = 3;
}
message InternalAuthServiceVerifyResponse {
 InternalUserModel user = 1;
  errmsg.v1.ErrorMessage error = 3;
}


message InternalUserModel {
  string id_app = 1;
  string id_user = 2;
  string id_role  = 3;
  string user_ip =4;
  double lat = 5;
  double lng = 6;
}



message InternalAppModel {
  string id_app = 1;
  string name = 2;
  string domain =3;
  algoenum.v1.AppType app_type = 4;
  string short_public_key = 5;
  uint64 token_ttl = 6;
  bool is_active = 7;
  algoenum.v1.AppCountry app_country = 8;

}


// -------------------------------------
message InternalAuthServiceSendMailHtmlRequest {
  repeated string to = 1;
  repeated string cc = 2;
  string subject = 3;
  string body = 4;
  string id_app = 5;
}
message InternalAuthServiceSendMailHtmlResponse {
  errmsg.v1.ErrorMessage error = 1;
}
// -------------------------------------
message InternalAuthServiceRefreshTokenRequest {
  string id_app = 1;
  string device_id = 2;
  string refresh_token = 3;
}
message InternalAuthServiceRefreshTokenResponse {
  errmsg.v1.ErrorMessage error = 1;
  string token = 2;
  string refresh_token = 4;
}
// -------------------------------------
message InternalAuthServiceForgotPasswordRequest {
  string email = 1; 
  string domain = 2;
  string otp = 3;
  string new_password = 4;
  string ip_address = 5;
}
message InternalAuthServiceForgotPasswordResponse {
  errmsg.v1.ErrorMessage error = 1;
}

