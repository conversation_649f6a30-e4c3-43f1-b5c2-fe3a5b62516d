syntax = "proto3";
package auth.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1;authv1";

import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";
import "algoenum/v1/app_type.proto";
import "algoenum/v1/template_email.proto";
import "algoenum/v1/app_country.proto";
service BackofficeAuthService {
  rpc Login(BackofficeAuthServiceLoginRequest) returns (BackofficeAuthServiceLoginResponse);
  rpc Me(BackofficeAuthServiceMeRequest) returns (BackofficeAuthServiceMeResponse);
  rpc ChangePassword(BackofficeAuthServiceChangePasswordRequest) returns(BackofficeAuthServiceChangePasswordResponse);
  rpc RefreshToken(BackofficeAuthServiceRefreshTokenRequest) returns (BackofficeAuthServiceRefreshTokenResponse);
  rpc ForgotPassword(BackofficeAuthServiceForgotPasswordRequest) returns (BackofficeAuthServiceForgotPasswordResponse);

  rpc ReloadEnforcer(BackofficeAuthServiceReloadEnforcerRequest) returns (BackofficeAuthServiceReloadEnforcerResponse);
  rpc CreateApp(BackofficeAuthServiceCreateAppRequest) returns (BackofficeAuthServiceCreateAppResponse);
  rpc FetchApp(BackofficeAuthServiceFetchAppRequest) returns (BackofficeAuthServiceFetchAppResponse) ;
  rpc UpdateApp(BackofficeAuthServiceUpdateAppRequest) returns (BackofficeAuthServiceUpdateAppResponse);

  rpc FetchUser(BackofficeAuthServiceFetchUserRequest) returns(BackofficeAuthServiceFetchUserResponse);
  rpc CreateUser(BackofficeAuthServiceCreateUserRequest) returns (BackofficeAuthServiceCreateUserResponse);
  rpc UpdateUser(BackofficeAuthServiceUpdateUserRequest) returns (BackofficeAuthServiceUpdateUserResponse);

  rpc FetchRole(BackofficeAuthServiceFetchRoleRequest) returns (BackofficeAuthServiceFetchRoleResponse);
  rpc CreateRole(BackofficeAuthServiceCreateRoleRequest) returns (BackofficeAuthServiceCreateRoleResponse);
  rpc UpdateRole(BackofficeAuthServiceUpdateRoleRequest) returns (BackofficeAuthServiceUpdateRoleResponse);

  rpc FetchService(BackofficeAuthServiceFetchServiceRequest) returns (BackofficeAuthServiceFetchServiceResponse);
  rpc CreateService(BackofficeAuthServiceCreateServiceRequest) returns (BackofficeAuthServiceCreateServiceResponse);
  rpc UpdateService(BackofficeAuthServiceUpdateServiceRequest) returns (BackofficeAuthServiceUpdateServiceResponse);

  rpc FetchPath(BackofficeAuthServiceFetchPathRequest) returns (BackofficeAuthServiceFetchPathResponse);
  rpc CreatePath(BackofficeAuthServiceCreatePathRequest) returns (BackofficeAuthServiceCreatePathResponse);
  rpc UpdatePath(BackofficeAuthServiceUpdatePathRequest) returns (BackofficeAuthServiceUpdatePathResponse);

  rpc FetchPolicy(BackofficeAuthServiceFetchPolicyRequest) returns (BackofficeAuthServiceFetchPolicyResponse);
  rpc CreatePolicy(BackofficeAuthServiceCreatePolicyRequest) returns (BackofficeAuthServiceCreatePolicyResponse);
  rpc UpdatePolicy(BackofficeAuthServiceUpdatePolicyRequest) returns (BackofficeAuthServiceUpdatePolicyResponse);

  rpc InitTotp(BackofficeAuthServiceInitTotpRequest) returns (BackofficeAuthServiceInitTotpResponse);
  rpc VerifyTotp(BackofficeAuthServiceVerifyTotpRequest) returns (BackofficeAuthServiceVerifyTotpResponse);
  rpc RemoveTotp(BackofficeAuthServiceRemoveTotpRequest) returns (BackofficeAuthServiceRemoveTotpResponse);

  rpc FetchConfigMail(BackofficeAuthServiceFetchConfigMailRequest) returns(BackofficeAuthServiceFetchConfigMailResponse);
  rpc CreateConfigMail(BackofficeAuthServiceCreateConfigMailRequest) returns (BackofficeAuthServiceCreateConfigMailResponse);
  rpc UpdateConfigMail(BackofficeAuthServiceUpdateConfigMailRequest) returns (BackofficeAuthServiceUpdateConfigMailResponse);
    
  rpc FetchConfigTemplateEmail(BackofficeAuthServiceFetchConfigTemplateEmailRequest) returns(BackofficeAuthServiceFetchConfigTemplateEmailResponse);
  rpc CreateConfigTemplateEmail(BackofficeAuthServiceCreateConfigTemplateEmailRequest) returns (BackofficeAuthServiceCreateConfigTemplateEmailResponse);
  rpc UpdateConfigTemplateEmail(BackofficeAuthServiceUpdateConfigTemplateEmailRequest) returns (BackofficeAuthServiceUpdateConfigTemplateEmailResponse);
    
  rpc UpdateRefCode(BackofficeAuthServiceUpdateRefCodeRequest) returns (BackofficeAuthServiceUpdateRefCodeResponse);

  rpc FetchOAuthConfig(BackofficeAuthServiceFetchOAuthConfigRequest) returns(BackofficeAuthServiceFetchOAuthConfigResponse);
  rpc CreateOAuthConfig(BackofficeAuthServiceCreateOAuthConfigRequest) returns (BackofficeAuthServiceCreateOAuthConfigResponse);
  rpc UpdateOAuthConfig(BackofficeAuthServiceUpdateOAuthConfigRequest) returns (BackofficeAuthServiceUpdateOAuthConfigResponse);

  rpc FetchCompany(BackofficeAuthServiceFetchCompanyRequest) returns(BackofficeAuthServiceFetchCompanyResponse);
  rpc CreateCompany(BackofficeAuthServiceCreateCompanyRequest) returns (BackofficeAuthServiceCreateCompanyResponse);
  rpc UpdateCompany(BackofficeAuthServiceUpdateCompanyRequest) returns (BackofficeAuthServiceUpdateCompanyResponse);

  rpc UpdateProfile(BackofficeAuthServiceUpdateProfileRequest) returns (BackofficeAuthServiceUpdateProfileResponse);

    
}

message BackofficeAuthServiceChangePasswordRequest {
  string old_password = 1;
  string new_password = 2;
}
message BackofficeAuthServiceChangePasswordResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeAuthServiceMeRequest{}

message BackofficeAuthServiceMeResponse{
  errmsg.v1.ErrorMessage error = 1;
  repeated string paths  = 2;
  BackofficeUserDetail user_detail = 3;
}

message BackofficeUserDetail {
  string first_name = 1;
  string last_name = 2;
  string email = 3;
  string phone_number = 4;
  string ref_code = 5;
  string user_ref_id = 6;
  string street = 7;
  int64 id_state = 8;
  int64 id_city = 9;
  int64 id_country = 10;
  string id_company = 11;

}



message BackofficeAuthServiceReloadEnforcerRequest {}

message BackofficeAuthServiceReloadEnforcerResponse{
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeAuthServiceLoginRequest{
  string email = 1;
  string password = 2;
  string otp = 3;
  string device_id = 4;
}

message BackofficeAuthServiceLoginResponse {
  errmsg.v1.ErrorMessage error = 1;
  string token = 2;
  string refresh_token = 3;

}


message BackofficeAuthServiceCreateAppRequest {
  string name = 1;
  algoenum.v1.AppType app_type = 2;
  string domain = 3;
  uint64 token_ttl = 4;
  algoenum.v1.AppCountry app_country = 5;

}

message BackofficeAuthServiceCreateAppResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message BackofficeAuthServiceFetchAppRequest {
  string id_app = 1;
  string name_search = 2;
  algoenum.v1.AppType app_type = 3;
  algoenum.v1.AppCountry app_country = 4;
  utils.v1.State state = 5;
  utils.v1.PaginationRequest pagination = 6;
}


message BackofficeAuthServiceFetchAppResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated BackofficeAppModel items = 3;
}

message BackofficeAuthServiceUpdateAppRequest {
  string id_app = 1;
  string name = 2;
  algoenum.v1.AppType app_type = 3;
  uint64 token_ttl = 4;
  bool  is_change_paseto_key = 5;
  utils.v1.State state = 6;
  algoenum.v1.AppCountry app_country = 7;

}
message BackofficeAuthServiceUpdateAppResponse {
  errmsg.v1.ErrorMessage error = 1;
}
// ----------------------------------
message BackofficeAuthServiceFetchUserRequest {
  string id_app = 1;
  string id_user = 2;
  string id_role = 3;
  string email_search = 4;

  utils.v1.State state = 5;
  utils.v1.PaginationRequest pagination = 6;
}
message BackofficeAuthServiceFetchUserResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated BackofficeUserModel items = 3;
}


message BackofficeUserModel {
    string id_user = 1;
    AuthUserApp app = 2;
    BackofficeUserRoleModel role = 3;
    BackofficeUserDetail user_details = 4;
    string email = 5;
    bool is_enable_totp = 6;
    bool is_active = 7;
}


message BackofficeUserRoleModel {
  string id_role = 1;
  string name = 2;
  int64 priority = 3;
  bool is_active = 4;
}


message AuthUserApp {
  string id_app = 1;
  string name = 2;
  bool is_active = 3;
}


message BackofficeAuthServiceCreateUserRequest {
  string id_app = 1;
  string id_role = 2;
  string email = 3;
  string password = 4;
  string first_name = 5;
  string last_name = 6;
  string phone_number = 7;
}

message BackofficeAuthServiceCreateUserResponse {
  string id_user = 1;
  errmsg.v1.ErrorMessage error = 2;
}

message BackofficeAuthServiceUpdateUserRequest{
  string id_user = 1;
  string id_role = 2;
  string password = 3;
  utils.v1.State state = 4;
}
message BackofficeAuthServiceUpdateUserResponse{
  errmsg.v1.ErrorMessage error = 1;
}

// -------------------------------------

message BackofficeAuthServiceFetchRoleRequest {
  string id_role = 1;
  string name_search = 2;
  utils.v1.PaginationRequest pagination = 3;
  utils.v1.State state = 4;
}
message BackofficeAuthServiceFetchRoleResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated RoleModel items = 3;
}

message RoleModel {
  string id_role = 1;
  string name = 2;
  int64 priority = 3;
  bool is_active = 4;
}

message BackofficeAuthServiceCreateRoleRequest{
  string name = 1;
  int64 priority = 2;
}
message BackofficeAuthServiceCreateRoleResponse{
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeAuthServiceUpdateRoleRequest{
  string id_role = 1;
  string name  = 2;
  utils.v1.State state = 3;
}

message BackofficeAuthServiceUpdateRoleResponse{
  errmsg.v1.ErrorMessage error = 1;
}

// ------------------
message BackofficeAuthServiceFetchServiceRequest {
  string id_service = 1;
  string name_search = 2;
  utils.v1.State state =3;
  utils.v1.PaginationRequest pagination = 4;
}
message BackofficeAuthServiceFetchServiceResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated ServiceModel items = 3;

}

message ServiceModel {
  string id_service = 1;
  string name = 2;
  bool  is_active =3;
}

message BackofficeAuthServiceCreateServiceRequest{
  string name  = 1;
}

message BackofficeAuthServiceCreateServiceResponse{
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeAuthServiceUpdateServiceRequest{
  string id_service = 1;
  string name  = 2;
  utils.v1.State state = 3;
}
message BackofficeAuthServiceUpdateServiceResponse{
  errmsg.v1.ErrorMessage error = 1;
}
// ----------------------------
message BackofficeAuthServiceFetchPathRequest {
  string id_path = 1;
  string id_service = 2;
  string absolute_path_search = 3;
  utils.v1.State state = 4 ;
  utils.v1.PaginationRequest pagination = 5;
}
message BackofficeAuthServiceFetchPathResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated PathModel items = 3;
}

message PathModel {
  ServiceModel service = 1;
  string id_path = 2;
  string absolute_path = 3;
  bool is_active = 4;
}

message BackofficeAuthServiceCreatePathRequest{
  string id_service = 1;
  string absolute_path = 2;
}
message BackofficeAuthServiceCreatePathResponse{
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeAuthServiceUpdatePathRequest{
  string id_path = 1;
  string absolute_path = 2;
  utils.v1.State state = 3;
}

message BackofficeAuthServiceUpdatePathResponse{
  errmsg.v1.ErrorMessage error = 1;
}



// ------------------------------
message BackofficeAuthServiceFetchPolicyRequest {
  string id_policy = 1;
  string id_path = 2;
  string id_role = 3;
  utils.v1.State state = 4;
  utils.v1.PaginationRequest pagination = 5;
}
message BackofficeAuthServiceFetchPolicyResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated PolicyModel items = 3;
}

message PolicyModel {
  string id_policy = 1;
  ServiceModel service = 2;
  PathModel path = 3;
  RoleModel role = 4;
  bool is_active = 5;
  bool state  = 6;
}


message BackofficeAuthServiceCreatePolicyRequest{
  string id_path = 1;
  string id_role = 2;
}
message BackofficeAuthServiceCreatePolicyResponse{
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeAuthServiceUpdatePolicyRequest{
  string id_policy = 1;
  utils.v1.State state = 2;
}
message BackofficeAuthServiceUpdatePolicyResponse{
  errmsg.v1.ErrorMessage error = 1;
}



message BackofficeAppModel {
  string id_app = 1;
  string name = 2;
  string domain =3;
  algoenum.v1.AppType app_type = 4;
  string short_public_key = 5;
  uint64 token_ttl = 6;
  bool is_active = 7;
  algoenum.v1.AppCountry app_country = 8;

}

// ------------------------------
message BackofficeAuthServiceInitTotpRequest {
}
message BackofficeAuthServiceInitTotpResponse {
  string qr_code = 1;
  errmsg.v1.ErrorMessage error = 2;
}

message BackofficeAuthServiceVerifyTotpRequest {
  string totp = 1;
}
message BackofficeAuthServiceVerifyTotpResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message BackofficeAuthServiceRemoveTotpRequest {
}
message BackofficeAuthServiceRemoveTotpResponse {
  errmsg.v1.ErrorMessage error = 1;
}


//------------------------- 

message BackofficeAuthServiceFetchConfigMailRequest {
    
	string smtp_server_address = 1;
	string auth_username = 2;
	string sender_email = 3;
	utils.v1.State state = 4;
	utils.v1.PaginationRequest pagination = 5;
        
}
message BackofficeAuthServiceFetchConfigMailResponse {
    errmsg.v1.ErrorMessage error = 1;
    repeated ConfigMailModel items = 2;
    utils.v1.PaginationResponse pagination = 3;
}

message BackofficeAuthServiceCreateConfigMailRequest {
    
	string id_auth_app = 1;
	string smtp_server_address = 2;
	uint64 smtp_server_port = 3;
	string auth_username = 4;
	string auth_password = 5;
	string sender_email = 6;

}
message BackofficeAuthServiceCreateConfigMailResponse {
    errmsg.v1.ErrorMessage error = 1;
}

message BackofficeAuthServiceUpdateConfigMailRequest {
    
	string id_auth_app = 1;
	string smtp_server_address = 2;
	uint64 smtp_server_port = 3;
	string auth_username = 4;
	string auth_password = 5;
	string sender_email = 6;
	utils.v1.State state = 7;

	string id_config_mail = 8;
 
    
}
message BackofficeAuthServiceUpdateConfigMailResponse {
    errmsg.v1.ErrorMessage error = 1;
}

message ConfigMailModel {
	string id_config_mail = 1;
	string id_auth_app = 2;
	string smtp_server_address = 3;
	uint64 smtp_server_port = 4;
	string auth_username = 5;
	string auth_password = 6;
	string sender_email = 7;
	bool is_active = 8;
}

//------------------------- 

message BackofficeAuthServiceFetchConfigTemplateEmailRequest {
    
	algoenum.v1.TemplateEmailType name = 1;
	string id_auth_app = 2;
	utils.v1.State state = 3;
	utils.v1.PaginationRequest pagination = 4;
        
}
message BackofficeAuthServiceFetchConfigTemplateEmailResponse {
    errmsg.v1.ErrorMessage error = 1;
    repeated ConfigTemplateEmailModel items = 2;
    utils.v1.PaginationResponse pagination = 3;
}

message BackofficeAuthServiceCreateConfigTemplateEmailRequest {
    
	string id_auth_app = 1;
	algoenum.v1.TemplateEmailType name = 2;
	string content = 3;

}
message BackofficeAuthServiceCreateConfigTemplateEmailResponse {
    errmsg.v1.ErrorMessage error = 1;
}

message BackofficeAuthServiceUpdateConfigTemplateEmailRequest {
    
	string content = 1;
	utils.v1.State state = 2;
	string id_config_template_email = 3;
    
}
message BackofficeAuthServiceUpdateConfigTemplateEmailResponse {
    errmsg.v1.ErrorMessage error = 1;
}

message ConfigTemplateEmailModel {
	string id_config_template_email = 1;
	string id_auth_app = 2;
	algoenum.v1.TemplateEmailType name = 3;
	string content = 4;
	bool is_active = 5;
}


message BackofficeAuthServiceRefreshTokenRequest{
    string refresh_token = 1;
    string device_id = 2;

}

message BackofficeAuthServiceRefreshTokenResponse {
  errmsg.v1.ErrorMessage error = 1;
  string token = 2;
  string refresh_token = 3;
}

//-------------------------------
message BackofficeAuthServiceUpdateRefCodeRequest{
  string user_id = 1;
  string ref_code = 2;
}

message BackofficeAuthServiceUpdateRefCodeResponse {
  errmsg.v1.ErrorMessage error = 1;
}

// -------------------------------------
message BackofficeAuthServiceForgotPasswordRequest {
  string email = 1; 
  string otp = 2;
  string new_password = 3;
}
message BackofficeAuthServiceForgotPasswordResponse {
  errmsg.v1.ErrorMessage error = 1;
}

//------------------------- 

message BackofficeAuthServiceFetchOAuthConfigRequest {
    
	string id_auth_app = 1;
	utils.v1.State state = 2;
	utils.v1.PaginationRequest pagination = 3;
        
}
message BackofficeAuthServiceFetchOAuthConfigResponse {
    errmsg.v1.ErrorMessage error = 1;
    repeated OAuthConfigModel items = 2;
    utils.v1.PaginationResponse pagination = 3;
}

message BackofficeAuthServiceCreateOAuthConfigRequest {
    
	string id_auth_app = 1;
	string client_id = 2;
	string client_secret = 3;

}
message BackofficeAuthServiceCreateOAuthConfigResponse {
    errmsg.v1.ErrorMessage error = 1;
}

message BackofficeAuthServiceUpdateOAuthConfigRequest {
    
	string id_auth_app = 1;
	string client_id = 2;
	string client_secret = 3;
	utils.v1.State state = 4;

	string id_o_auth_config = 5;
 
    
}
message BackofficeAuthServiceUpdateOAuthConfigResponse {
    errmsg.v1.ErrorMessage error = 1;
}

message OAuthConfigModel {
	string id_o_auth_config = 1;
	string id_auth_app = 2;
	string client_id = 3;
	string client_secret = 4;
	bool is_active = 5;
}


//---------------------------------
message BackofficeAuthServiceFetchCompanyRequest {
  string name = 1;
  string id_auth_app = 2;
  string street = 3;
  int64 id_state = 4;
  int64 id_city = 5;
  int64 id_country = 6;
  string mst = 7;
  utils.v1.Bool verify = 8;
  utils.v1.State state = 9;
  utils.v1.PaginationRequest pagination = 10;
}
message BackofficeAuthServiceFetchCompanyResponse {
  errmsg.v1.ErrorMessage error = 1;
  repeated CompanyModel items = 2;
  utils.v1.PaginationResponse pagination = 3;

}

message BackofficeAuthServiceCreateCompanyRequest {
  string name = 1;
  string street = 2;
  int64 id_state = 3;
  int64 id_city = 4;
  int64 id_country = 5;
  string logo = 6;
  string mst = 7;
  string id_auth_app = 8;

}
message BackofficeAuthServiceCreateCompanyResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeAuthServiceUpdateCompanyRequest {
  string name = 1;
  string street = 2;
  int64 id_state = 3;
  int64 id_city = 4;
  int64 id_country = 6;
  string logo = 7;
  string mst = 8;
  bool verify = 9;
  utils.v1.State state = 10;
  string id_company = 11;
  string id_auth_app = 12;

}
message BackofficeAuthServiceUpdateCompanyResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message CompanyModel {
  string id_company = 1;
  string name = 2;
  string street = 3;
  int64 id_state = 4;
  int64 id_city = 5;
  int64 id_country = 6;
  string logo = 7;
  string mst = 8;
  bool verify = 9;
  bool is_active = 10;
  string id_auth_app = 11;
}

//---------------------------------

message BackofficeAuthServiceUpdateProfileRequest {
  string first_name = 1;
  string last_name = 2;
  string street = 3;
  int64 id_state = 4;
  int64 id_city = 5;
  int64 id_country = 6;
  string id_company = 7;
  string id_user = 8;
  string id_auth_app = 9;
}

message BackofficeAuthServiceUpdateProfileResponse {
  errmsg.v1.ErrorMessage error = 1;
}