syntax = "proto3";
package auth.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1;authv1";
import "auth/v1/backoffice_auth.proto";
import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";


service MerchantAuthService {
  rpc Login(MerchantAuthServiceLoginRequest) returns (MerchantAuthServiceLoginResponse);
  rpc Me(MerchantAuthServiceMeRequest) returns (MerchantAuthServiceMeResponse);
  rpc ChangePassword(MerchantAuthServiceChangePasswordRequest) returns (MerchantAuthServiceChangePasswordResponse);
  rpc RefreshToken(MerchantAuthServiceRefreshTokenRequest) returns (MerchantAuthServiceRefreshTokenResponse);
  rpc ForgotPassword(MerchantAuthServiceForgotPasswordRequest) returns (MerchantAuthServiceForgotPasswordResponse);


  rpc FetchUser(MerchantAuthServiceFetchUserRequest) returns(MerchantAuthServiceFetchUserResponse);
  rpc UpdateUser(MerchantAuthServiceUpdateUserRequest) returns (MerchantAuthServiceUpdateUserResponse);
  rpc FetchRole(MerchantAuthServiceFetchRoleRequest) returns (MerchantAuthServiceFetchRoleResponse);



}
message MerchantAuthServiceChangePasswordRequest {
  string old_password = 1;
  string new_password = 2;
}
message MerchantAuthServiceChangePasswordResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message MerchantAuthServiceFetchRoleRequest {
  string id_role = 1;
  string name_search = 2;
  utils.v1.PaginationRequest pagination = 3;
}

message MerchantAuthServiceFetchRoleResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated MerchantRoleModel items = 3;
}
message MerchantRoleModel{
  string id_role = 1;
  string name = 2;
}

message MerchantAuthServiceUpdateUserRequest {
  string id_user = 1;
  utils.v1.State state = 2;
}
message MerchantAuthServiceUpdateUserResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message MerchantAuthServiceFetchUserRequest {
  string id_user = 1;
  string id_role = 2;
  string email_search = 3;
  utils.v1.State state = 4;
  utils.v1.PaginationRequest pagination = 5;
}
message MerchantAuthServiceFetchUserResponse {
  errmsg.v1.ErrorMessage error = 1;
  repeated MerchantServiceUserModel users = 2;
  utils.v1.PaginationResponse pagination = 3;
}

message MerchantServiceUserModel {
  string id_user = 1;
  MerchantUserRoleModel role = 2;
  string email = 3;
  bool is_enable_totp = 4;
  bool is_active = 5;
}

message MerchantUserRoleModel {
  string id_role = 1;
  string name = 2;
  bool is_active = 3;
}


message MerchantAuthServiceSignUpRequest{
  string email = 1;
  string password = 2;
}

message MerchantAuthServiceSignUpResponse{
  errmsg.v1.ErrorMessage error = 1;
}

message MerchantAuthServiceLoginRequest{
  string domain = 1;
  string email = 2;
  string password = 3;
  string otp = 4;
  string device_id = 5;

}


message MerchantAuthServiceLoginResponse{
  string token =1;
  string refresh_token = 2;
  errmsg.v1.ErrorMessage error = 3;
}


message MerchantAuthServiceMeRequest{}

message MerchantAuthServiceMeResponse{
  repeated string paths =1;
  errmsg.v1.ErrorMessage error = 2;
  MerchantMeUserDetails user_detail = 3;
  MerchantAuthServiceDetail merchant_detail = 4;
  CompanyModel company = 5;
}

message MerchantMeUserDetails {
  string first_name = 1;
  string last_name = 2;
  string email = 3;
  string phone_number = 4;
  string street = 5;
  int64 id_state = 6;
  int64 id_city = 7;
  int64 id_country = 8;
  string id_company = 9;
}

message MerchantAuthServiceDetail {
  string domain = 1;
  string name = 2;
}




message MerchantAuthServiceRefreshTokenRequest{
    string refresh_token = 1;
    string device_id = 2;
}

message MerchantAuthServiceRefreshTokenResponse {
  errmsg.v1.ErrorMessage error = 1;
  string token = 2;
  string refresh_token = 3;
}

// -------------------------------------
message MerchantAuthServiceForgotPasswordRequest {
  string email = 1; 
  string domain = 2;
  string otp = 3;
  string new_password = 4;
}
message MerchantAuthServiceForgotPasswordResponse {
  errmsg.v1.ErrorMessage error = 1;
}