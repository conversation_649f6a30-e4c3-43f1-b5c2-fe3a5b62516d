syntax = "proto3";
package auth.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1;authv1";

import "errmsg/v1/errormsg.proto";

service CustomerAuthService {
  rpc SignUp(CustomerAuthServiceSignUpRequest) returns(CustomerAuthServiceSignUpResponse);
  rpc VerifySignUp(CustomerAuthServiceVerifySignUpRequest) returns(CustomerAuthServiceVerifySignUpResponse);

  rpc Login(CustomerAuthServiceLoginRequest) returns (CustomerAuthServiceLoginResponse);
  rpc Me(CustomerAuthServiceMeRequest) returns (CustomerAuthServiceMeResponse);
  rpc ChangePassword(CustomerAuthServiceChangePasswordRequest) returns (CustomerAuthServiceChangePasswordResponse);
  rpc RefreshToken(CustomerAuthServiceRefreshTokenRequest) returns (CustomerAuthServiceRefreshTokenResponse);
  rpc ForgotPassword(CustomerAuthServiceForgotPasswordRequest) returns (CustomerAuthServiceForgotPasswordResponse);
  rpc LoginOAuth(CustomerAuthServiceLoginOAuthRequest) returns (CustomerAuthServiceLoginOAuthResponse);
  rpc FetchOAuthApp(CustomerAuthServiceFetchOAuthAppRequest) returns (CustomerAuthServiceFetchOAuthAppResponse);

  //  rpc Logout(CustomerAuthServiceLogoutRequest) returns (CustomerAuthServiceLogoutResponse);
  //  rpc ChangePassword(CustomerAuthServiceChangePasswordRequest) returns (CustomerAuthServiceChangePasswordResponse);
}
message CustomerAuthServiceChangePasswordRequest {
  string old_password = 1;
  string new_password = 2;
}
message CustomerAuthServiceChangePasswordResponse {
  errmsg.v1.ErrorMessage error = 1;
}
message CustomerAuthServiceSignUpRequest{
  string email = 1;
  string password = 2;
  string first_name = 3;
  string last_name = 4;
  string phone_number = 5;
  string ref_code = 6;
  string street = 7;
  int64 id_state = 8;
  int64 id_city = 9;
  int64 id_country = 10;
}
message CustomerAuthServiceSignUpResponse{
  int64 token_ttl  = 1;
  errmsg.v1.ErrorMessage error = 2;
}

message CustomerAuthServiceVerifySignUpRequest{
  string token = 1;
}
message CustomerAuthServiceVerifySignUpResponse{
  errmsg.v1.ErrorMessage error = 1;
}

message CustomerAuthServiceLoginRequest{
  string email = 1;
  string password = 2;
  string otp = 3;
  string device_id = 4;


}
message CustomerAuthServiceLoginResponse{
  string token =1;
  string refresh_token = 2;
  errmsg.v1.ErrorMessage error = 3;
}


message CustomerAuthServiceMeRequest{}
message CustomerAuthServiceMeResponse{
  CustomerMeUserDetails user_detail = 1;
  repeated string paths = 2;
  errmsg.v1.ErrorMessage error = 3;
}

message CustomerMeUserDetails {
  string first_name = 1;
  string last_name = 2;
  string email = 3;
  string phone_number = 4;
  string ref_code = 5;
  string user_ref_id = 6;
  
}



message CustomerAuthServiceRefreshTokenRequest{
    string refresh_token = 1;
    string device_id = 2;
}

message CustomerAuthServiceRefreshTokenResponse {
  errmsg.v1.ErrorMessage error = 1;
  string token = 2;
  string refresh_token = 3;
}
// -------------------------------------
message CustomerAuthServiceForgotPasswordRequest {
  string email = 1; 
  string domain = 2;
  string otp = 3;
  string new_password = 4;
}
message CustomerAuthServiceForgotPasswordResponse {
  errmsg.v1.ErrorMessage error = 1;
}

// -------------------------------------
message CustomerAuthServiceLoginOAuthRequest {
  string oauth_token = 1;
  string device_id = 2;
}
message CustomerAuthServiceLoginOAuthResponse {
  errmsg.v1.ErrorMessage error = 1;
  string token = 2;
  string refresh_token = 3;
}

// -------------------------------------
message CustomerAuthServiceFetchOAuthAppRequest {
}
message CustomerAuthServiceFetchOAuthAppResponse {
  errmsg.v1.ErrorMessage error = 1;
  string client_id = 2;
}
