syntax = "proto3";
package errmsg.v1;

option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1;errmsgv1";
import "google/protobuf/any.proto";

enum ErrorCode {
  ERROR_CODE_UNSPECIFIED = 0;
  ERROR_CODE_PUBLIC_URL = 9999;
  ERROR_CODE_CROSS_SERVICE_DATA_MISMATCH = 10000;
  ERROR_CODE_CROSS_SERVICE_TELCO_ERROR = 10001;
  ERROR_CODE_CROSS_SERVICE_LOCATION_ERROR = 10002;

  ERROR_CODE_SUCCESS = 1;
  ERROR_CODE_INTERNAL_SERVER_ERROR = 2;
  ERROR_CODE_INVALID_REQUEST = 3;
  ERROR_CODE_UNAUTHORIZED = 4;
  ERROR_CODE_ABORTED = 5;
  ERROR_CODE_USERNAME_OR_PASSWORD_DOES_NOT_MATCH = 6;
  ERROR_CODE_USER_HAS_BEEN_BANNED = 7;
  ERROR_CODE_EMAIL_EXIST = 8;
  ERROR_CODE_EMAIL_NOT_EXIST = 9;
  ERROR_CODE_OLD_PASSWORD_INCORRECT  = 10;


  // auth service
  ERROR_CODE_AUTH_APP_EXIST = 1000;
  ERROR_CODE_AUTH_APP_NOT_EXIST = 1001;
  ERROR_CODE_AUTH_USER_NOT_EXIST = 1002;
  ERROR_CODE_AUTH_USER_EXIST = 1003;
  ERROR_CODE_AUTH_ROLE_EXIST = 1004;
  ERROR_CODE_AUTH_ROLE_NOT_EXIST = 1005;
  ERROR_CODE_AUTH_SERVICE_EXIST = 1006;
  ERROR_CODE_AUTH_SERVICE_NOT_EXIST = 1007;
  ERROR_CODE_AUTH_PATH_EXIST = 1008;
  ERROR_CODE_AUTH_PATH_NOT_EXIST = 1009;
  ERROR_CODE_AUTH_POLICY_EXIST = 1010;
  ERROR_CODE_AUTH_POLICY_NOT_EXIST = 1011;
  ERROR_CODE_AUTH_PERMISSION_DENY = 1012;
  ERROR_CODE_AUTH_ROLE_PRIORITY_MUST_BE_HIGHER = 1013;
  ERROR_CODE_AUTH_OLD_PASSWORD_INCORRECT = 1014;
  ERROR_CODE_AUTH_TOTP_VERIFY_INCORRECT = 1016;
  ERROR_CODE_AUTH_TOTP_VERIFY_CORRECT = 1017;
  ERROR_CODE_AUTH_CONFIG_NOT_EXIST = 1018;
  ERROR_CODE_AUTH_CONFIG_EXIST = 1019;
  ERROR_CODE_AUTH_REFRESH_TOKEN_EXPRIRY = 1020;
  ERROR_CODE_AUTH_OTP_REQUIRED = 1021;
  ERROR_CODE_AUTH_TOTP_REQUIRED = 1022;
  ERROR_CODE_AUTH_OTP_INCORRECT = 1023;
  ERROR_CODE_AUTH_REF_CODE_INCORRECT = 1024;
  ERROR_CODE_AUTH_REF_EXIST = 1025;
  ERROR_CODE_AUTH_OTP_VERIFY_INCORRECT = 1226;
  ERROR_CODE_AUTH_TOKEN_VERIFY_INCORRECT = 1227;
  ERROR_CODE_AUTH_USER_INACTIVE = 1228;

  ERROR_CODE_DNS_DNS_EXIST = 2000;
  ERROR_CODE_DNS_DNS_NOT_EXIST = 2001;
  ERROR_CODE_DNS_DNS1_AND_DNS2_NOT_SAME_IP_TYPE = 2002;


  ERROR_CODE_TELCO_TELCO_EXIST = 3000;
  ERROR_CODE_TELCO_TELCO_NOT_EXIST = 3001;


  ERROR_CODE_LOCATION_LOCATION_EXIST = 4000;
  ERROR_CODE_LOCATION_LOCATION_NOT_EXIST = 4001;


  ERROR_CODE_RES_RES_ACCOUNT_EXIST = 5001;
  ERROR_CODE_RES_RES_ACCOUNT_NOT_EXIST = 5002;
  ERROR_CODE_RES_RES_ACCOUNT_HAS_BINDING_ANOTHER_PORT = 5003;
  ERROR_CODE_RES_RES_NODE_EXIST = 5004;
  ERROR_CODE_RES_RES_NODE_NOT_EXIST = 5005;
  ERROR_CODE_RES_RES_NODE_AND_RES_ACCOUNT_NOT_SAME_LOCATION = 5006;

  ERROR_CODE_RES_RES_PORT_EXIST = 5007;
  ERROR_CODE_RES_RES_PORT_NOT_EXIST = 5008;
  ERROR_CODE_RES_RES_PORT_TOTAL_RES_PORT_MUST_BE_GREATER_THAN_CURRENT = 5009;
  ERROR_CODE_RES_RES_PORT_MUST_HAVE_ACCOUNT_BEFORE_ACTIVE = 5010;
  ERROR_CODE_RES_RES_DEVICE_NOT_EXIST = 5011;

  ERROR_CODE_MERCHANT_NOT_EXIST = 6000;
  ERROR_CODE_MERCHANT_USER_NOT_EXIST = 6001;
  ERROR_CODE_MERCHANT_PRODUCT_BASE_EXIST = 6002;
  ERROR_CODE_MERCHANT_PRODUCT_BASE_NOT_EXIST = 6003;
  ERROR_CODE_MERCHANT_PRODUCT_EXIST = 6004;
  ERROR_CODE_MERCHANT_PRODUCT_NOT_EXIST = 6005;
  ERROR_CODE_MERCHANT_API_NOT_EXIST = 6006;
  ERROR_CODE_MERCHANT_INSUFFICIENT_USER_BALANCE = 6007;
  ERROR_CODE_MERCHANT_PRODUCT_PRICE_INVALID = 6008;
  ERROR_CODE_MERCHANT_UPDATE_BALANCE_MISSING_NOTE = 6009;
  ERROR_CODE_MERCHANT_UPDATE_BALANCE_TRANSACTION_TYPE_INVALID = 6010;
  ERROR_CODE_MERCHANT_UPDATE_COMPANY_NOT_EXIST = 6011;
  ERROR_CODE_MERCHANT_UPDATE_COMPANY_MEST_EXIST = 6012;
  ERROR_CODE_MERCHANT_UPDATE_CONFIG_MAIL_NOT_EXIST = 6013;
  

  ERROR_CODE_BILLING_PLAN_MUST_HAVE_PRICE_FOR_ACTIVE_STATE = 7002;
  ERROR_CODE_BILLING_PLAN_PRICE_FOR_BILLING_CYCLE_EXIST = 7008;
  ERROR_CODE_BILLING_PLAN_PRICE_NOT_EXIST = 7009;
  ERROR_CODE_BILLING_ORDER_INSUFFICIENT_FUNDS = 7010;




  ERROR_CODE_BILLING_PLAN_EXIST = 8000;
  ERROR_CODE_BILLING_PLAN_NOT_EXIST = 8001;
  ERROR_CODE_PROXY_MANAGER_PLAN_LOCATION_NOT_SAME_LEVEL = 8002;
  ERROR_CODE_PROXY_MANAGER_LOCATION_IS_NOT_AVAILABLE_IN_THIS_PLAN = 8003;
  ERROR_CODE_PROXY_MANAGER_CANNOT_ACTIVE_PLAN_WHEN_DEFAULT_PROFILE_OF_PLAN_NOT_EXIST = 8004;

  ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_SUBSCRIPTION_NOT_EXPIRED= 8005;
  ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_SUBSCRIPTION_EXPIRED= 8006;
  ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_SUBSCRIPTION_NOT_EXIST = 8007;
  ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_PROXY_TOKEN_USED = 8008;
  ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_PROXY_TOKEN_NOT_EXIST = 8009;
  ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_PROXY_NOT_FOUND = 8010;
  ERROR_CODE_PROXY_MANAGER_PLAN_MUST_HAVE_AT_LEAST_ONE_LOCATION = 8011;
  ERROR_CODE_PROXY_MANAGER_PLAN_MUST_HAVE_AT_LEAST_ONE_BACK_CONNECT = 8012;


  ERROR_CODE_BILLING_MERCHANT_EXIST = 7101;
  ERROR_CODE_BILLING_MERCHANT_NOT_EXIST = 7102;
  ERROR_CODE_BILLING_MERCHANT_CANNOT_UPDATE_CURRENCY = 7103;

  ERROR_CODE_BILLING_PAYMENT_GATEWAY_TYPE_NOT_EXIST = 7201;
  ERROR_CODE_BILLING_PAYMENT_GATEWAY_TYPE_EXIST = 7202;
  ERROR_CODE_BILLING_PAYMENT_GATEWAY_EXIST = 7203;
  ERROR_CODE_BILLING_PAYMENT_GATEWAY_NOT_EXIST = 7204;
  ERROR_CODE_BILLING_PAYMENT_GATEWAY_DO_NOT_SUPPORT_CURRENCY_OF_MERCHANT = 7205;
  ERROR_CODE_BILLING_PAYMENT_GATEWAY_AMOUNT_TOP_UP_EXIST = 7206;
  ERROR_CODE_BILLING_PAYMENT_GATEWAY_AMOUNT_TOP_UP_NOT_EXIST = 7207;
  ERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_DO_NOT_ENABLED_ON_THIS_MERCHANT = 7208;
  ERROR_CODE_BILLING_PAYMENT_GATEWAY_APPOTA_PAYMENT_GATEWAY_DO_NOT_ENABLED_ON_THIS_MERCHANT = 7209;
  ERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_CONFIG_INCORRECT = 7210;

  ERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_PRODUCT_ID_NOT_EXIST = 7211;
  ERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_MUST_HAVE_ONE_PRODUCT = 7212;
  ERROR_CODE_BILLING_PAYMENT_GATEWAY_DO_NOT_SUPPORT_COUNTRY = 7213;
  ERROR_CODE_BILLING_PAYMENT_GATEWAY_SEPAY_PAYMENT_GATEWAY_API_KEY_EXIST = 7214;
  ERROR_CODE_BILLING_PAYMENT_GATEWAY_SEPAY_PAYMENT_GATEWAY_ACCOUNT_NUMBER_EXIST = 7215;


  ERROR_CODE_BACK_CONNECT_BACK_CONNECT_MANAGER_NOT_EXIST = 7301;
  ERROR_CODE_BACK_CONNECT_BACK_CONNECT_MANAGER_EXIST = 7302;
  ERROR_CODE_BACK_CONNECT_BACK_CONNECT_PORT_NOT_EXIST = 7303;
  ERROR_CODE_BACK_CONNECT_BACK_CONNECT_PORT_EXIST = 7304;
  ERROR_CODE_BACK_CONNECT_NOT_AVAILABLE = 7305;





  ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_NOT_EXIST = 7401;
  ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_IS_DEACTIVATE = 7402;
  ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_DEACTIVATE_DEFAULT_PROFILE = 7403;
  ERROR_CODE_PROXY_PROFILE_PROXY_CANNOT_SET_PRIVATE_PROFILE_TO_ANOTHER_PROXY_TOKEN = 7404;
  ERROR_CODE_PROXY_PROFILE_PROXY_DNS_NOT_SAME_IP_TYPE = 7405;
  ERROR_CODE_PROXY_PROFILE_PROXY_NOT_HAVE_LOCATION = 7406;
  ERROR_CODE_PROXY_PROFILE_PROXY_DEFAULT_PROFILE_MUST_HAVE_AT_LEAST_ONE_LOCATION = 7407;
  ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_DELETE_DEFAULT_PROFILE_OF_PROXY_TOKEN = 7408;
  ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_MODIFY_PROFILE_OF_ANOTHER_USER = 7409;
  ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_RESET_DEFAULT_PROFILE  = 7410;
  ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_LOCATION_NOT_SAME_LEVEL  = 7411;
  ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_LOCATION_STATE_MUST_BE_SAME_COUNTRY  = 7412;

  ERROR_CODE_PROXY_TOKEN_MIN_USED_NOT_REACHED = 7501;
  ERROR_CODE_PROXY_TOKEN_PROXY_OUT_OF_STOCK = 7502;


  ERROR_CODE_PROXY_POOL_NOT_EXIST = 7600;



  ERROR_CODE_MISC_PAYMENT_ADDRESS_ADDRESS_INVALID = 9001;

}


message ErrorMessage {
  ErrorCode code = 1;
  string message = 2;
  map<string,google.protobuf.Any> extras = 3;
}

