// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: billing/transaction/v1/internal.proto

package transactionv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InternalTransactionServiceCreateUserBalanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdUser        string                 `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	IdApp         string                 `protobuf:"bytes,2,opt,name=id_app,json=idApp,proto3" json:"id_app,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalTransactionServiceCreateUserBalanceRequest) Reset() {
	*x = InternalTransactionServiceCreateUserBalanceRequest{}
	mi := &file_billing_transaction_v1_internal_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalTransactionServiceCreateUserBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalTransactionServiceCreateUserBalanceRequest) ProtoMessage() {}

func (x *InternalTransactionServiceCreateUserBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_internal_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalTransactionServiceCreateUserBalanceRequest.ProtoReflect.Descriptor instead.
func (*InternalTransactionServiceCreateUserBalanceRequest) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_internal_proto_rawDescGZIP(), []int{0}
}

func (x *InternalTransactionServiceCreateUserBalanceRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *InternalTransactionServiceCreateUserBalanceRequest) GetIdApp() string {
	if x != nil {
		return x.IdApp
	}
	return ""
}

type InternalTransactionServiceCreateUserBalanceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalTransactionServiceCreateUserBalanceResponse) Reset() {
	*x = InternalTransactionServiceCreateUserBalanceResponse{}
	mi := &file_billing_transaction_v1_internal_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalTransactionServiceCreateUserBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalTransactionServiceCreateUserBalanceResponse) ProtoMessage() {}

func (x *InternalTransactionServiceCreateUserBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_internal_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalTransactionServiceCreateUserBalanceResponse.ProtoReflect.Descriptor instead.
func (*InternalTransactionServiceCreateUserBalanceResponse) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_internal_proto_rawDescGZIP(), []int{1}
}

func (x *InternalTransactionServiceCreateUserBalanceResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_billing_transaction_v1_internal_proto protoreflect.FileDescriptor

const file_billing_transaction_v1_internal_proto_rawDesc = "" +
	"\n" +
	"%billing/transaction/v1/internal.proto\x12\x16billing.transaction.v1\x1a\x18errmsg/v1/errormsg.proto\"d\n" +
	"2InternalTransactionServiceCreateUserBalanceRequest\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x12\x15\n" +
	"\x06id_app\x18\x02 \x01(\tR\x05idApp\"d\n" +
	"3InternalTransactionServiceCreateUserBalanceResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error2\xcb\x01\n" +
	"\x1aInternalTransactionService\x12\xac\x01\n" +
	"\x11CreateUserBalance\x12J.billing.transaction.v1.InternalTransactionServiceCreateUserBalanceRequest\x1aK.billing.transaction.v1.InternalTransactionServiceCreateUserBalanceResponseBUZSgit.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/transaction/v1;transactionv1b\x06proto3"

var (
	file_billing_transaction_v1_internal_proto_rawDescOnce sync.Once
	file_billing_transaction_v1_internal_proto_rawDescData []byte
)

func file_billing_transaction_v1_internal_proto_rawDescGZIP() []byte {
	file_billing_transaction_v1_internal_proto_rawDescOnce.Do(func() {
		file_billing_transaction_v1_internal_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_billing_transaction_v1_internal_proto_rawDesc), len(file_billing_transaction_v1_internal_proto_rawDesc)))
	})
	return file_billing_transaction_v1_internal_proto_rawDescData
}

var file_billing_transaction_v1_internal_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_billing_transaction_v1_internal_proto_goTypes = []any{
	(*InternalTransactionServiceCreateUserBalanceRequest)(nil),  // 0: billing.transaction.v1.InternalTransactionServiceCreateUserBalanceRequest
	(*InternalTransactionServiceCreateUserBalanceResponse)(nil), // 1: billing.transaction.v1.InternalTransactionServiceCreateUserBalanceResponse
	(*v1.ErrorMessage)(nil), // 2: errmsg.v1.ErrorMessage
}
var file_billing_transaction_v1_internal_proto_depIdxs = []int32{
	2, // 0: billing.transaction.v1.InternalTransactionServiceCreateUserBalanceResponse.error:type_name -> errmsg.v1.ErrorMessage
	0, // 1: billing.transaction.v1.InternalTransactionService.CreateUserBalance:input_type -> billing.transaction.v1.InternalTransactionServiceCreateUserBalanceRequest
	1, // 2: billing.transaction.v1.InternalTransactionService.CreateUserBalance:output_type -> billing.transaction.v1.InternalTransactionServiceCreateUserBalanceResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_billing_transaction_v1_internal_proto_init() }
func file_billing_transaction_v1_internal_proto_init() {
	if File_billing_transaction_v1_internal_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_billing_transaction_v1_internal_proto_rawDesc), len(file_billing_transaction_v1_internal_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_billing_transaction_v1_internal_proto_goTypes,
		DependencyIndexes: file_billing_transaction_v1_internal_proto_depIdxs,
		MessageInfos:      file_billing_transaction_v1_internal_proto_msgTypes,
	}.Build()
	File_billing_transaction_v1_internal_proto = out.File
	file_billing_transaction_v1_internal_proto_goTypes = nil
	file_billing_transaction_v1_internal_proto_depIdxs = nil
}
