// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: billing/transaction/v1/merchant.proto

package transactionv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MerchantTransactionServiceFetchTransactionRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	IdUser            string                 `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	IdPaymentGateway  string                 `protobuf:"bytes,2,opt,name=id_payment_gateway,json=idPaymentGateway,proto3" json:"id_payment_gateway,omitempty"`
	TransactionType   v1.TransactionType     `protobuf:"varint,3,opt,name=transaction_type,json=transactionType,proto3,enum=algoenum.v1.TransactionType" json:"transaction_type,omitempty"`
	TransactionStatus v1.TransactionStatus   `protobuf:"varint,4,opt,name=transaction_status,json=transactionStatus,proto3,enum=algoenum.v1.TransactionStatus" json:"transaction_status,omitempty"`
	FromUnix          int64                  `protobuf:"varint,5,opt,name=from_unix,json=fromUnix,proto3" json:"from_unix,omitempty"`
	ToUnix            int64                  `protobuf:"varint,6,opt,name=to_unix,json=toUnix,proto3" json:"to_unix,omitempty"`
	Pagination        *v11.PaginationRequest `protobuf:"bytes,7,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *MerchantTransactionServiceFetchTransactionRequest) Reset() {
	*x = MerchantTransactionServiceFetchTransactionRequest{}
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantTransactionServiceFetchTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantTransactionServiceFetchTransactionRequest) ProtoMessage() {}

func (x *MerchantTransactionServiceFetchTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantTransactionServiceFetchTransactionRequest.ProtoReflect.Descriptor instead.
func (*MerchantTransactionServiceFetchTransactionRequest) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_merchant_proto_rawDescGZIP(), []int{0}
}

func (x *MerchantTransactionServiceFetchTransactionRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *MerchantTransactionServiceFetchTransactionRequest) GetIdPaymentGateway() string {
	if x != nil {
		return x.IdPaymentGateway
	}
	return ""
}

func (x *MerchantTransactionServiceFetchTransactionRequest) GetTransactionType() v1.TransactionType {
	if x != nil {
		return x.TransactionType
	}
	return v1.TransactionType(0)
}

func (x *MerchantTransactionServiceFetchTransactionRequest) GetTransactionStatus() v1.TransactionStatus {
	if x != nil {
		return x.TransactionStatus
	}
	return v1.TransactionStatus(0)
}

func (x *MerchantTransactionServiceFetchTransactionRequest) GetFromUnix() int64 {
	if x != nil {
		return x.FromUnix
	}
	return 0
}

func (x *MerchantTransactionServiceFetchTransactionRequest) GetToUnix() int64 {
	if x != nil {
		return x.ToUnix
	}
	return 0
}

func (x *MerchantTransactionServiceFetchTransactionRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantTransactionServiceFetchTransactionUserBalanceTransaction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdUserBalance string                 `protobuf:"bytes,1,opt,name=id_user_balance,json=idUserBalance,proto3" json:"id_user_balance,omitempty"`
	BalanceType   v1.BalanceType         `protobuf:"varint,2,opt,name=balance_type,json=balanceType,proto3,enum=algoenum.v1.BalanceType" json:"balance_type,omitempty"`
	Amount        float64                `protobuf:"fixed64,3,opt,name=amount,proto3" json:"amount,omitempty"`
	BalanceBefore float64                `protobuf:"fixed64,4,opt,name=balance_before,json=balanceBefore,proto3" json:"balance_before,omitempty"`
	BalanceAfter  float64                `protobuf:"fixed64,5,opt,name=balance_after,json=balanceAfter,proto3" json:"balance_after,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantTransactionServiceFetchTransactionUserBalanceTransaction) Reset() {
	*x = MerchantTransactionServiceFetchTransactionUserBalanceTransaction{}
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantTransactionServiceFetchTransactionUserBalanceTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantTransactionServiceFetchTransactionUserBalanceTransaction) ProtoMessage() {}

func (x *MerchantTransactionServiceFetchTransactionUserBalanceTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantTransactionServiceFetchTransactionUserBalanceTransaction.ProtoReflect.Descriptor instead.
func (*MerchantTransactionServiceFetchTransactionUserBalanceTransaction) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_merchant_proto_rawDescGZIP(), []int{1}
}

func (x *MerchantTransactionServiceFetchTransactionUserBalanceTransaction) GetIdUserBalance() string {
	if x != nil {
		return x.IdUserBalance
	}
	return ""
}

func (x *MerchantTransactionServiceFetchTransactionUserBalanceTransaction) GetBalanceType() v1.BalanceType {
	if x != nil {
		return x.BalanceType
	}
	return v1.BalanceType(0)
}

func (x *MerchantTransactionServiceFetchTransactionUserBalanceTransaction) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *MerchantTransactionServiceFetchTransactionUserBalanceTransaction) GetBalanceBefore() float64 {
	if x != nil {
		return x.BalanceBefore
	}
	return 0
}

func (x *MerchantTransactionServiceFetchTransactionUserBalanceTransaction) GetBalanceAfter() float64 {
	if x != nil {
		return x.BalanceAfter
	}
	return 0
}

type MerchantTransactionServiceFetchTransactionResponse struct {
	state         protoimpl.MessageState                        `protogen:"open.v1"`
	Transactions  []*MerchantTransactionServiceFetchTransaction `protobuf:"bytes,1,rep,name=transactions,proto3" json:"transactions,omitempty"`
	Pagination    *v11.PaginationResponse                       `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Error         *v12.ErrorMessage                             `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantTransactionServiceFetchTransactionResponse) Reset() {
	*x = MerchantTransactionServiceFetchTransactionResponse{}
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantTransactionServiceFetchTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantTransactionServiceFetchTransactionResponse) ProtoMessage() {}

func (x *MerchantTransactionServiceFetchTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantTransactionServiceFetchTransactionResponse.ProtoReflect.Descriptor instead.
func (*MerchantTransactionServiceFetchTransactionResponse) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_merchant_proto_rawDescGZIP(), []int{2}
}

func (x *MerchantTransactionServiceFetchTransactionResponse) GetTransactions() []*MerchantTransactionServiceFetchTransaction {
	if x != nil {
		return x.Transactions
	}
	return nil
}

func (x *MerchantTransactionServiceFetchTransactionResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *MerchantTransactionServiceFetchTransactionResponse) GetError() *v12.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type MerchantTransactionServiceFetchTransaction struct {
	state                  protoimpl.MessageState                                              `protogen:"open.v1"`
	IdTransaction          string                                                              `protobuf:"bytes,1,opt,name=id_transaction,json=idTransaction,proto3" json:"id_transaction,omitempty"`
	Merchant               *MerchantTransactionServiceFetchTransactionMerchant                 `protobuf:"bytes,2,opt,name=merchant,proto3" json:"merchant,omitempty"`
	User                   *MerchantTransactionServiceFetchTransactionUser                     `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	TransactionType        v1.TransactionType                                                  `protobuf:"varint,4,opt,name=transaction_type,json=transactionType,proto3,enum=algoenum.v1.TransactionType" json:"transaction_type,omitempty"`
	TransactionStatus      v1.TransactionStatus                                                `protobuf:"varint,5,opt,name=transaction_status,json=transactionStatus,proto3,enum=algoenum.v1.TransactionStatus" json:"transaction_status,omitempty"`
	UserBalanceTransaction []*MerchantTransactionServiceFetchTransactionUserBalanceTransaction `protobuf:"bytes,6,rep,name=user_balance_transaction,json=userBalanceTransaction,proto3" json:"user_balance_transaction,omitempty"`
	DebitTransaction       *MerchantTransactionServiceFetchTransactionDebitTransaction         `protobuf:"bytes,7,opt,name=debit_transaction,json=debitTransaction,proto3" json:"debit_transaction,omitempty"`
	CreditTransaction      *MerchantTransactionServiceFetchTransactionCreditTransaction        `protobuf:"bytes,8,opt,name=credit_transaction,json=creditTransaction,proto3" json:"credit_transaction,omitempty"`
	TotalAmount            float64                                                             `protobuf:"fixed64,9,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	Currency               v1.Currency                                                         `protobuf:"varint,10,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	CreatedAt              int64                                                               `protobuf:"varint,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *MerchantTransactionServiceFetchTransaction) Reset() {
	*x = MerchantTransactionServiceFetchTransaction{}
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantTransactionServiceFetchTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantTransactionServiceFetchTransaction) ProtoMessage() {}

func (x *MerchantTransactionServiceFetchTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantTransactionServiceFetchTransaction.ProtoReflect.Descriptor instead.
func (*MerchantTransactionServiceFetchTransaction) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_merchant_proto_rawDescGZIP(), []int{3}
}

func (x *MerchantTransactionServiceFetchTransaction) GetIdTransaction() string {
	if x != nil {
		return x.IdTransaction
	}
	return ""
}

func (x *MerchantTransactionServiceFetchTransaction) GetMerchant() *MerchantTransactionServiceFetchTransactionMerchant {
	if x != nil {
		return x.Merchant
	}
	return nil
}

func (x *MerchantTransactionServiceFetchTransaction) GetUser() *MerchantTransactionServiceFetchTransactionUser {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *MerchantTransactionServiceFetchTransaction) GetTransactionType() v1.TransactionType {
	if x != nil {
		return x.TransactionType
	}
	return v1.TransactionType(0)
}

func (x *MerchantTransactionServiceFetchTransaction) GetTransactionStatus() v1.TransactionStatus {
	if x != nil {
		return x.TransactionStatus
	}
	return v1.TransactionStatus(0)
}

func (x *MerchantTransactionServiceFetchTransaction) GetUserBalanceTransaction() []*MerchantTransactionServiceFetchTransactionUserBalanceTransaction {
	if x != nil {
		return x.UserBalanceTransaction
	}
	return nil
}

func (x *MerchantTransactionServiceFetchTransaction) GetDebitTransaction() *MerchantTransactionServiceFetchTransactionDebitTransaction {
	if x != nil {
		return x.DebitTransaction
	}
	return nil
}

func (x *MerchantTransactionServiceFetchTransaction) GetCreditTransaction() *MerchantTransactionServiceFetchTransactionCreditTransaction {
	if x != nil {
		return x.CreditTransaction
	}
	return nil
}

func (x *MerchantTransactionServiceFetchTransaction) GetTotalAmount() float64 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *MerchantTransactionServiceFetchTransaction) GetCurrency() v1.Currency {
	if x != nil {
		return x.Currency
	}
	return v1.Currency(0)
}

func (x *MerchantTransactionServiceFetchTransaction) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

type MerchantTransactionServiceFetchTransactionMerchant struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant    string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IsActive      bool                   `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantTransactionServiceFetchTransactionMerchant) Reset() {
	*x = MerchantTransactionServiceFetchTransactionMerchant{}
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantTransactionServiceFetchTransactionMerchant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantTransactionServiceFetchTransactionMerchant) ProtoMessage() {}

func (x *MerchantTransactionServiceFetchTransactionMerchant) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantTransactionServiceFetchTransactionMerchant.ProtoReflect.Descriptor instead.
func (*MerchantTransactionServiceFetchTransactionMerchant) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_merchant_proto_rawDescGZIP(), []int{4}
}

func (x *MerchantTransactionServiceFetchTransactionMerchant) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *MerchantTransactionServiceFetchTransactionMerchant) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MerchantTransactionServiceFetchTransactionMerchant) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type MerchantTransactionServiceFetchTransactionCreditTransaction struct {
	state           protoimpl.MessageState                                                     `protogen:"open.v1"`
	PaymentGateway  *MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway `protobuf:"bytes,1,opt,name=payment_gateway,json=paymentGateway,proto3" json:"payment_gateway,omitempty"`
	Description     string                                                                     `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	HashTransaction string                                                                     `protobuf:"bytes,3,opt,name=hash_transaction,json=hashTransaction,proto3" json:"hash_transaction,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *MerchantTransactionServiceFetchTransactionCreditTransaction) Reset() {
	*x = MerchantTransactionServiceFetchTransactionCreditTransaction{}
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantTransactionServiceFetchTransactionCreditTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantTransactionServiceFetchTransactionCreditTransaction) ProtoMessage() {}

func (x *MerchantTransactionServiceFetchTransactionCreditTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantTransactionServiceFetchTransactionCreditTransaction.ProtoReflect.Descriptor instead.
func (*MerchantTransactionServiceFetchTransactionCreditTransaction) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_merchant_proto_rawDescGZIP(), []int{5}
}

func (x *MerchantTransactionServiceFetchTransactionCreditTransaction) GetPaymentGateway() *MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway {
	if x != nil {
		return x.PaymentGateway
	}
	return nil
}

func (x *MerchantTransactionServiceFetchTransactionCreditTransaction) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *MerchantTransactionServiceFetchTransactionCreditTransaction) GetHashTransaction() string {
	if x != nil {
		return x.HashTransaction
	}
	return ""
}

type MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGateway   string                 `protobuf:"bytes,1,opt,name=id_payment_gateway,json=idPaymentGateway,proto3" json:"id_payment_gateway,omitempty"`
	PaymentGatewayType v1.PaymentGatewayType  `protobuf:"varint,2,opt,name=payment_gateway_type,json=paymentGatewayType,proto3,enum=algoenum.v1.PaymentGatewayType" json:"payment_gateway_type,omitempty"`
	AccountHolderName  string                 `protobuf:"bytes,3,opt,name=account_holder_name,json=accountHolderName,proto3" json:"account_holder_name,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway) Reset() {
	*x = MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway{}
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway) ProtoMessage() {}

func (x *MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway.ProtoReflect.Descriptor instead.
func (*MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_merchant_proto_rawDescGZIP(), []int{6}
}

func (x *MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway) GetIdPaymentGateway() string {
	if x != nil {
		return x.IdPaymentGateway
	}
	return ""
}

func (x *MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway) GetPaymentGatewayType() v1.PaymentGatewayType {
	if x != nil {
		return x.PaymentGatewayType
	}
	return v1.PaymentGatewayType(0)
}

func (x *MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway) GetAccountHolderName() string {
	if x != nil {
		return x.AccountHolderName
	}
	return ""
}

type MerchantTransactionServiceFetchTransactionDebitTransaction struct {
	state         protoimpl.MessageState                                                      `protogen:"open.v1"`
	Plan          *MerchantTransactionServiceFetchTransactionDebitTransactionPlan             `protobuf:"bytes,1,opt,name=plan,proto3" json:"plan,omitempty"`
	Subscription  *MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscription `protobuf:"bytes,2,opt,name=subscription,proto3" json:"subscription,omitempty"`
	OrderType     v1.OrderType                                                                `protobuf:"varint,3,opt,name=order_type,json=orderType,proto3,enum=algoenum.v1.OrderType" json:"order_type,omitempty"`
	TotalCost     float64                                                                     `protobuf:"fixed64,4,opt,name=total_cost,json=totalCost,proto3" json:"total_cost,omitempty"`
	TotalPurchase float64                                                                     `protobuf:"fixed64,5,opt,name=total_purchase,json=totalPurchase,proto3" json:"total_purchase,omitempty"`
	TotalDiscount float64                                                                     `protobuf:"fixed64,6,opt,name=total_discount,json=totalDiscount,proto3" json:"total_discount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransaction) Reset() {
	*x = MerchantTransactionServiceFetchTransactionDebitTransaction{}
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantTransactionServiceFetchTransactionDebitTransaction) ProtoMessage() {}

func (x *MerchantTransactionServiceFetchTransactionDebitTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantTransactionServiceFetchTransactionDebitTransaction.ProtoReflect.Descriptor instead.
func (*MerchantTransactionServiceFetchTransactionDebitTransaction) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_merchant_proto_rawDescGZIP(), []int{7}
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransaction) GetPlan() *MerchantTransactionServiceFetchTransactionDebitTransactionPlan {
	if x != nil {
		return x.Plan
	}
	return nil
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransaction) GetSubscription() *MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscription {
	if x != nil {
		return x.Subscription
	}
	return nil
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransaction) GetOrderType() v1.OrderType {
	if x != nil {
		return x.OrderType
	}
	return v1.OrderType(0)
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransaction) GetTotalCost() float64 {
	if x != nil {
		return x.TotalCost
	}
	return 0
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransaction) GetTotalPurchase() float64 {
	if x != nil {
		return x.TotalPurchase
	}
	return 0
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransaction) GetTotalDiscount() float64 {
	if x != nil {
		return x.TotalDiscount
	}
	return 0
}

type MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscription struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdSubscription string                 `protobuf:"bytes,1,opt,name=id_subscription,json=idSubscription,proto3" json:"id_subscription,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscription) Reset() {
	*x = MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscription{}
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscription) ProtoMessage() {}

func (x *MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscription) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscription.ProtoReflect.Descriptor instead.
func (*MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscription) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_merchant_proto_rawDescGZIP(), []int{8}
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscription) GetIdSubscription() string {
	if x != nil {
		return x.IdSubscription
	}
	return ""
}

type MerchantTransactionServiceFetchTransactionDebitTransactionPlan struct {
	state         protoimpl.MessageState                                               `protogen:"open.v1"`
	IdPlan        string                                                               `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	Name          string                                                               `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	PlanPrice     *MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice `protobuf:"bytes,3,opt,name=plan_price,json=planPrice,proto3" json:"plan_price,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransactionPlan) Reset() {
	*x = MerchantTransactionServiceFetchTransactionDebitTransactionPlan{}
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransactionPlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantTransactionServiceFetchTransactionDebitTransactionPlan) ProtoMessage() {}

func (x *MerchantTransactionServiceFetchTransactionDebitTransactionPlan) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantTransactionServiceFetchTransactionDebitTransactionPlan.ProtoReflect.Descriptor instead.
func (*MerchantTransactionServiceFetchTransactionDebitTransactionPlan) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_merchant_proto_rawDescGZIP(), []int{9}
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransactionPlan) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransactionPlan) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransactionPlan) GetPlanPrice() *MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice {
	if x != nil {
		return x.PlanPrice
	}
	return nil
}

type MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	IdPlanPrice       string                 `protobuf:"bytes,1,opt,name=id_plan_price,json=idPlanPrice,proto3" json:"id_plan_price,omitempty"`
	CostPrice         float64                `protobuf:"fixed64,2,opt,name=cost_price,json=costPrice,proto3" json:"cost_price,omitempty"`
	PurchasePrice     float64                `protobuf:"fixed64,3,opt,name=purchase_price,json=purchasePrice,proto3" json:"purchase_price,omitempty"`
	DataTransferInGb  float64                `protobuf:"fixed64,4,opt,name=data_transfer_in_gb,json=dataTransferInGb,proto3" json:"data_transfer_in_gb,omitempty"`
	BillingCycleInSec int64                  `protobuf:"varint,5,opt,name=billing_cycle_in_sec,json=billingCycleInSec,proto3" json:"billing_cycle_in_sec,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice) Reset() {
	*x = MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice{}
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice) ProtoMessage() {}

func (x *MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice.ProtoReflect.Descriptor instead.
func (*MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_merchant_proto_rawDescGZIP(), []int{10}
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice) GetIdPlanPrice() string {
	if x != nil {
		return x.IdPlanPrice
	}
	return ""
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice) GetCostPrice() float64 {
	if x != nil {
		return x.CostPrice
	}
	return 0
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice) GetPurchasePrice() float64 {
	if x != nil {
		return x.PurchasePrice
	}
	return 0
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice) GetDataTransferInGb() float64 {
	if x != nil {
		return x.DataTransferInGb
	}
	return 0
}

func (x *MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice) GetBillingCycleInSec() int64 {
	if x != nil {
		return x.BillingCycleInSec
	}
	return 0
}

type MerchantTransactionServiceFetchTransactionUser struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdUser        string                 `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	Email         string                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	FirstName     string                 `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName      string                 `protobuf:"bytes,4,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	IsActive      bool                   `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantTransactionServiceFetchTransactionUser) Reset() {
	*x = MerchantTransactionServiceFetchTransactionUser{}
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantTransactionServiceFetchTransactionUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantTransactionServiceFetchTransactionUser) ProtoMessage() {}

func (x *MerchantTransactionServiceFetchTransactionUser) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantTransactionServiceFetchTransactionUser.ProtoReflect.Descriptor instead.
func (*MerchantTransactionServiceFetchTransactionUser) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_merchant_proto_rawDescGZIP(), []int{11}
}

func (x *MerchantTransactionServiceFetchTransactionUser) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *MerchantTransactionServiceFetchTransactionUser) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *MerchantTransactionServiceFetchTransactionUser) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *MerchantTransactionServiceFetchTransactionUser) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *MerchantTransactionServiceFetchTransactionUser) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type MerchantTransactionServiceFetchUserBalanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdUser        string                 `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	BalanceType   v1.BalanceType         `protobuf:"varint,2,opt,name=balance_type,json=balanceType,proto3,enum=algoenum.v1.BalanceType" json:"balance_type,omitempty"`
	Pagination    *v11.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantTransactionServiceFetchUserBalanceRequest) Reset() {
	*x = MerchantTransactionServiceFetchUserBalanceRequest{}
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantTransactionServiceFetchUserBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantTransactionServiceFetchUserBalanceRequest) ProtoMessage() {}

func (x *MerchantTransactionServiceFetchUserBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantTransactionServiceFetchUserBalanceRequest.ProtoReflect.Descriptor instead.
func (*MerchantTransactionServiceFetchUserBalanceRequest) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_merchant_proto_rawDescGZIP(), []int{12}
}

func (x *MerchantTransactionServiceFetchUserBalanceRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *MerchantTransactionServiceFetchUserBalanceRequest) GetBalanceType() v1.BalanceType {
	if x != nil {
		return x.BalanceType
	}
	return v1.BalanceType(0)
}

func (x *MerchantTransactionServiceFetchUserBalanceRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantTransactionServiceFetchUserBalanceResponse struct {
	state         protoimpl.MessageState                        `protogen:"open.v1"`
	Users         []*MerchantTransactionServiceUserBalanceUsers `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	Error         *v12.ErrorMessage                             `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse                       `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantTransactionServiceFetchUserBalanceResponse) Reset() {
	*x = MerchantTransactionServiceFetchUserBalanceResponse{}
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantTransactionServiceFetchUserBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantTransactionServiceFetchUserBalanceResponse) ProtoMessage() {}

func (x *MerchantTransactionServiceFetchUserBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantTransactionServiceFetchUserBalanceResponse.ProtoReflect.Descriptor instead.
func (*MerchantTransactionServiceFetchUserBalanceResponse) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_merchant_proto_rawDescGZIP(), []int{13}
}

func (x *MerchantTransactionServiceFetchUserBalanceResponse) GetUsers() []*MerchantTransactionServiceUserBalanceUsers {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *MerchantTransactionServiceFetchUserBalanceResponse) GetError() *v12.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantTransactionServiceFetchUserBalanceResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantTransactionServiceUserBalanceUsers struct {
	state         protoimpl.MessageState                         `protogen:"open.v1"`
	IdUser        string                                         `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	TotalBalance  float64                                        `protobuf:"fixed64,2,opt,name=total_balance,json=totalBalance,proto3" json:"total_balance,omitempty"`
	UserBalances  []*MerchantTransactionServiceUserBalanceOfUser `protobuf:"bytes,4,rep,name=user_balances,json=userBalances,proto3" json:"user_balances,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantTransactionServiceUserBalanceUsers) Reset() {
	*x = MerchantTransactionServiceUserBalanceUsers{}
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantTransactionServiceUserBalanceUsers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantTransactionServiceUserBalanceUsers) ProtoMessage() {}

func (x *MerchantTransactionServiceUserBalanceUsers) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantTransactionServiceUserBalanceUsers.ProtoReflect.Descriptor instead.
func (*MerchantTransactionServiceUserBalanceUsers) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_merchant_proto_rawDescGZIP(), []int{14}
}

func (x *MerchantTransactionServiceUserBalanceUsers) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *MerchantTransactionServiceUserBalanceUsers) GetTotalBalance() float64 {
	if x != nil {
		return x.TotalBalance
	}
	return 0
}

func (x *MerchantTransactionServiceUserBalanceUsers) GetUserBalances() []*MerchantTransactionServiceUserBalanceOfUser {
	if x != nil {
		return x.UserBalances
	}
	return nil
}

type MerchantTransactionServiceUserBalanceOfUser struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	IdUserBalance         string                 `protobuf:"bytes,1,opt,name=id_user_balance,json=idUserBalance,proto3" json:"id_user_balance,omitempty"`
	BalanceType           v1.BalanceType         `protobuf:"varint,2,opt,name=balance_type,json=balanceType,proto3,enum=algoenum.v1.BalanceType" json:"balance_type,omitempty"`
	BalanceChargePriority int64                  `protobuf:"varint,3,opt,name=balance_charge_priority,json=balanceChargePriority,proto3" json:"balance_charge_priority,omitempty"`
	CurrentBalance        float64                `protobuf:"fixed64,4,opt,name=current_balance,json=currentBalance,proto3" json:"current_balance,omitempty"`
	Currency              v1.Currency            `protobuf:"varint,5,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *MerchantTransactionServiceUserBalanceOfUser) Reset() {
	*x = MerchantTransactionServiceUserBalanceOfUser{}
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantTransactionServiceUserBalanceOfUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantTransactionServiceUserBalanceOfUser) ProtoMessage() {}

func (x *MerchantTransactionServiceUserBalanceOfUser) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_merchant_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantTransactionServiceUserBalanceOfUser.ProtoReflect.Descriptor instead.
func (*MerchantTransactionServiceUserBalanceOfUser) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_merchant_proto_rawDescGZIP(), []int{15}
}

func (x *MerchantTransactionServiceUserBalanceOfUser) GetIdUserBalance() string {
	if x != nil {
		return x.IdUserBalance
	}
	return ""
}

func (x *MerchantTransactionServiceUserBalanceOfUser) GetBalanceType() v1.BalanceType {
	if x != nil {
		return x.BalanceType
	}
	return v1.BalanceType(0)
}

func (x *MerchantTransactionServiceUserBalanceOfUser) GetBalanceChargePriority() int64 {
	if x != nil {
		return x.BalanceChargePriority
	}
	return 0
}

func (x *MerchantTransactionServiceUserBalanceOfUser) GetCurrentBalance() float64 {
	if x != nil {
		return x.CurrentBalance
	}
	return 0
}

func (x *MerchantTransactionServiceUserBalanceOfUser) GetCurrency() v1.Currency {
	if x != nil {
		return x.Currency
	}
	return v1.Currency(0)
}

var File_billing_transaction_v1_merchant_proto protoreflect.FileDescriptor

const file_billing_transaction_v1_merchant_proto_rawDesc = "" +
	"\n" +
	"%billing/transaction/v1/merchant.proto\x12\x16billing.transaction.v1\x1a\x14utils/v1/utils.proto\x1a\x18errmsg/v1/errormsg.proto\x1a\x1aalgoenum/v1/currency.proto\x1a\"algoenum/v1/transaction_type.proto\x1a$algoenum/v1/transaction_status.proto\x1a\x1ealgoenum/v1/balance_type.proto\x1a\x1calgoenum/v1/order_type.proto\x1a&algoenum/v1/payment_gateway_type.proto\"\x85\x03\n" +
	"1MerchantTransactionServiceFetchTransactionRequest\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x12,\n" +
	"\x12id_payment_gateway\x18\x02 \x01(\tR\x10idPaymentGateway\x12G\n" +
	"\x10transaction_type\x18\x03 \x01(\x0e2\x1c.algoenum.v1.TransactionTypeR\x0ftransactionType\x12M\n" +
	"\x12transaction_status\x18\x04 \x01(\x0e2\x1e.algoenum.v1.TransactionStatusR\x11transactionStatus\x12\x1b\n" +
	"\tfrom_unix\x18\x05 \x01(\x03R\bfromUnix\x12\x17\n" +
	"\ato_unix\x18\x06 \x01(\x03R\x06toUnix\x12;\n" +
	"\n" +
	"pagination\x18\a \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x8b\x02\n" +
	"@MerchantTransactionServiceFetchTransactionUserBalanceTransaction\x12&\n" +
	"\x0fid_user_balance\x18\x01 \x01(\tR\ridUserBalance\x12;\n" +
	"\fbalance_type\x18\x02 \x01(\x0e2\x18.algoenum.v1.BalanceTypeR\vbalanceType\x12\x16\n" +
	"\x06amount\x18\x03 \x01(\x01R\x06amount\x12%\n" +
	"\x0ebalance_before\x18\x04 \x01(\x01R\rbalanceBefore\x12#\n" +
	"\rbalance_after\x18\x05 \x01(\x01R\fbalanceAfter\"\x89\x02\n" +
	"2MerchantTransactionServiceFetchTransactionResponse\x12f\n" +
	"\ftransactions\x18\x01 \x03(\v2B.billing.transaction.v1.MerchantTransactionServiceFetchTransactionR\ftransactions\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xbf\a\n" +
	"*MerchantTransactionServiceFetchTransaction\x12%\n" +
	"\x0eid_transaction\x18\x01 \x01(\tR\ridTransaction\x12f\n" +
	"\bmerchant\x18\x02 \x01(\v2J.billing.transaction.v1.MerchantTransactionServiceFetchTransactionMerchantR\bmerchant\x12Z\n" +
	"\x04user\x18\x03 \x01(\v2F.billing.transaction.v1.MerchantTransactionServiceFetchTransactionUserR\x04user\x12G\n" +
	"\x10transaction_type\x18\x04 \x01(\x0e2\x1c.algoenum.v1.TransactionTypeR\x0ftransactionType\x12M\n" +
	"\x12transaction_status\x18\x05 \x01(\x0e2\x1e.algoenum.v1.TransactionStatusR\x11transactionStatus\x12\x92\x01\n" +
	"\x18user_balance_transaction\x18\x06 \x03(\v2X.billing.transaction.v1.MerchantTransactionServiceFetchTransactionUserBalanceTransactionR\x16userBalanceTransaction\x12\x7f\n" +
	"\x11debit_transaction\x18\a \x01(\v2R.billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransactionR\x10debitTransaction\x12\x82\x01\n" +
	"\x12credit_transaction\x18\b \x01(\v2S.billing.transaction.v1.MerchantTransactionServiceFetchTransactionCreditTransactionR\x11creditTransaction\x12!\n" +
	"\ftotal_amount\x18\t \x01(\x01R\vtotalAmount\x121\n" +
	"\bcurrency\x18\n" +
	" \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\x12\x1d\n" +
	"\n" +
	"created_at\x18\v \x01(\x03R\tcreatedAt\"\x86\x01\n" +
	"2MerchantTransactionServiceFetchTransactionMerchant\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1b\n" +
	"\tis_active\x18\x03 \x01(\bR\bisActive\"\x97\x02\n" +
	";MerchantTransactionServiceFetchTransactionCreditTransaction\x12\x8a\x01\n" +
	"\x0fpayment_gateway\x18\x01 \x01(\v2a.billing.transaction.v1.MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGatewayR\x0epaymentGateway\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12)\n" +
	"\x10hash_transaction\x18\x03 \x01(\tR\x0fhashTransaction\"\xfc\x01\n" +
	"IMerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway\x12,\n" +
	"\x12id_payment_gateway\x18\x01 \x01(\tR\x10idPaymentGateway\x12Q\n" +
	"\x14payment_gateway_type\x18\x02 \x01(\x0e2\x1f.algoenum.v1.PaymentGatewayTypeR\x12paymentGatewayType\x12.\n" +
	"\x13account_holder_name\x18\x03 \x01(\tR\x11accountHolderName\"\xd5\x03\n" +
	":MerchantTransactionServiceFetchTransactionDebitTransaction\x12j\n" +
	"\x04plan\x18\x01 \x01(\v2V.billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransactionPlanR\x04plan\x12\x86\x01\n" +
	"\fsubscription\x18\x02 \x01(\v2b.billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscriptionR\fsubscription\x125\n" +
	"\n" +
	"order_type\x18\x03 \x01(\x0e2\x16.algoenum.v1.OrderTypeR\torderType\x12\x1d\n" +
	"\n" +
	"total_cost\x18\x04 \x01(\x01R\ttotalCost\x12%\n" +
	"\x0etotal_purchase\x18\x05 \x01(\x01R\rtotalPurchase\x12%\n" +
	"\x0etotal_discount\x18\x06 \x01(\x01R\rtotalDiscount\"u\n" +
	"JMerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscription\x12'\n" +
	"\x0fid_subscription\x18\x01 \x01(\tR\x0eidSubscription\"\xe9\x01\n" +
	">MerchantTransactionServiceFetchTransactionDebitTransactionPlan\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12z\n" +
	"\n" +
	"plan_price\x18\x03 \x01(\v2[.billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransactionPlanPriceR\tplanPrice\"\x8f\x02\n" +
	"CMerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice\x12\"\n" +
	"\rid_plan_price\x18\x01 \x01(\tR\vidPlanPrice\x12\x1d\n" +
	"\n" +
	"cost_price\x18\x02 \x01(\x01R\tcostPrice\x12%\n" +
	"\x0epurchase_price\x18\x03 \x01(\x01R\rpurchasePrice\x12-\n" +
	"\x13data_transfer_in_gb\x18\x04 \x01(\x01R\x10dataTransferInGb\x12/\n" +
	"\x14billing_cycle_in_sec\x18\x05 \x01(\x03R\x11billingCycleInSec\"\xb8\x01\n" +
	".MerchantTransactionServiceFetchTransactionUser\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\x12\x1d\n" +
	"\n" +
	"first_name\x18\x03 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x04 \x01(\tR\blastName\x12\x1b\n" +
	"\tis_active\x18\x05 \x01(\bR\bisActive\"\xc6\x01\n" +
	"1MerchantTransactionServiceFetchUserBalanceRequest\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x12;\n" +
	"\fbalance_type\x18\x02 \x01(\x0e2\x18.algoenum.v1.BalanceTypeR\vbalanceType\x12;\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xfb\x01\n" +
	"2MerchantTransactionServiceFetchUserBalanceResponse\x12X\n" +
	"\x05users\x18\x01 \x03(\v2B.billing.transaction.v1.MerchantTransactionServiceUserBalanceUsersR\x05users\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\xd4\x01\n" +
	"*MerchantTransactionServiceUserBalanceUsers\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x12#\n" +
	"\rtotal_balance\x18\x02 \x01(\x01R\ftotalBalance\x12h\n" +
	"\ruser_balances\x18\x04 \x03(\v2C.billing.transaction.v1.MerchantTransactionServiceUserBalanceOfUserR\fuserBalances\"\xa6\x02\n" +
	"+MerchantTransactionServiceUserBalanceOfUser\x12&\n" +
	"\x0fid_user_balance\x18\x01 \x01(\tR\ridUserBalance\x12;\n" +
	"\fbalance_type\x18\x02 \x01(\x0e2\x18.algoenum.v1.BalanceTypeR\vbalanceType\x126\n" +
	"\x17balance_charge_priority\x18\x03 \x01(\x03R\x15balanceChargePriority\x12'\n" +
	"\x0fcurrent_balance\x18\x04 \x01(\x01R\x0ecurrentBalance\x121\n" +
	"\bcurrency\x18\x05 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency2\xf4\x02\n" +
	"\x1aMerchantTransactionService\x12\xa9\x01\n" +
	"\x10FetchUserBalance\x12I.billing.transaction.v1.MerchantTransactionServiceFetchUserBalanceRequest\x1aJ.billing.transaction.v1.MerchantTransactionServiceFetchUserBalanceResponse\x12\xa9\x01\n" +
	"\x10FetchTransaction\x12I.billing.transaction.v1.MerchantTransactionServiceFetchTransactionRequest\x1aJ.billing.transaction.v1.MerchantTransactionServiceFetchTransactionResponseBUZSgit.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/transaction/v1;transactionv1b\x06proto3"

var (
	file_billing_transaction_v1_merchant_proto_rawDescOnce sync.Once
	file_billing_transaction_v1_merchant_proto_rawDescData []byte
)

func file_billing_transaction_v1_merchant_proto_rawDescGZIP() []byte {
	file_billing_transaction_v1_merchant_proto_rawDescOnce.Do(func() {
		file_billing_transaction_v1_merchant_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_billing_transaction_v1_merchant_proto_rawDesc), len(file_billing_transaction_v1_merchant_proto_rawDesc)))
	})
	return file_billing_transaction_v1_merchant_proto_rawDescData
}

var file_billing_transaction_v1_merchant_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_billing_transaction_v1_merchant_proto_goTypes = []any{
	(*MerchantTransactionServiceFetchTransactionRequest)(nil),                          // 0: billing.transaction.v1.MerchantTransactionServiceFetchTransactionRequest
	(*MerchantTransactionServiceFetchTransactionUserBalanceTransaction)(nil),           // 1: billing.transaction.v1.MerchantTransactionServiceFetchTransactionUserBalanceTransaction
	(*MerchantTransactionServiceFetchTransactionResponse)(nil),                         // 2: billing.transaction.v1.MerchantTransactionServiceFetchTransactionResponse
	(*MerchantTransactionServiceFetchTransaction)(nil),                                 // 3: billing.transaction.v1.MerchantTransactionServiceFetchTransaction
	(*MerchantTransactionServiceFetchTransactionMerchant)(nil),                         // 4: billing.transaction.v1.MerchantTransactionServiceFetchTransactionMerchant
	(*MerchantTransactionServiceFetchTransactionCreditTransaction)(nil),                // 5: billing.transaction.v1.MerchantTransactionServiceFetchTransactionCreditTransaction
	(*MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway)(nil),  // 6: billing.transaction.v1.MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway
	(*MerchantTransactionServiceFetchTransactionDebitTransaction)(nil),                 // 7: billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransaction
	(*MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscription)(nil), // 8: billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscription
	(*MerchantTransactionServiceFetchTransactionDebitTransactionPlan)(nil),             // 9: billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransactionPlan
	(*MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice)(nil),        // 10: billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice
	(*MerchantTransactionServiceFetchTransactionUser)(nil),                             // 11: billing.transaction.v1.MerchantTransactionServiceFetchTransactionUser
	(*MerchantTransactionServiceFetchUserBalanceRequest)(nil),                          // 12: billing.transaction.v1.MerchantTransactionServiceFetchUserBalanceRequest
	(*MerchantTransactionServiceFetchUserBalanceResponse)(nil),                         // 13: billing.transaction.v1.MerchantTransactionServiceFetchUserBalanceResponse
	(*MerchantTransactionServiceUserBalanceUsers)(nil),                                 // 14: billing.transaction.v1.MerchantTransactionServiceUserBalanceUsers
	(*MerchantTransactionServiceUserBalanceOfUser)(nil),                                // 15: billing.transaction.v1.MerchantTransactionServiceUserBalanceOfUser
	(v1.TransactionType)(0),        // 16: algoenum.v1.TransactionType
	(v1.TransactionStatus)(0),      // 17: algoenum.v1.TransactionStatus
	(*v11.PaginationRequest)(nil),  // 18: utils.v1.PaginationRequest
	(v1.BalanceType)(0),            // 19: algoenum.v1.BalanceType
	(*v11.PaginationResponse)(nil), // 20: utils.v1.PaginationResponse
	(*v12.ErrorMessage)(nil),       // 21: errmsg.v1.ErrorMessage
	(v1.Currency)(0),               // 22: algoenum.v1.Currency
	(v1.PaymentGatewayType)(0),     // 23: algoenum.v1.PaymentGatewayType
	(v1.OrderType)(0),              // 24: algoenum.v1.OrderType
}
var file_billing_transaction_v1_merchant_proto_depIdxs = []int32{
	16, // 0: billing.transaction.v1.MerchantTransactionServiceFetchTransactionRequest.transaction_type:type_name -> algoenum.v1.TransactionType
	17, // 1: billing.transaction.v1.MerchantTransactionServiceFetchTransactionRequest.transaction_status:type_name -> algoenum.v1.TransactionStatus
	18, // 2: billing.transaction.v1.MerchantTransactionServiceFetchTransactionRequest.pagination:type_name -> utils.v1.PaginationRequest
	19, // 3: billing.transaction.v1.MerchantTransactionServiceFetchTransactionUserBalanceTransaction.balance_type:type_name -> algoenum.v1.BalanceType
	3,  // 4: billing.transaction.v1.MerchantTransactionServiceFetchTransactionResponse.transactions:type_name -> billing.transaction.v1.MerchantTransactionServiceFetchTransaction
	20, // 5: billing.transaction.v1.MerchantTransactionServiceFetchTransactionResponse.pagination:type_name -> utils.v1.PaginationResponse
	21, // 6: billing.transaction.v1.MerchantTransactionServiceFetchTransactionResponse.error:type_name -> errmsg.v1.ErrorMessage
	4,  // 7: billing.transaction.v1.MerchantTransactionServiceFetchTransaction.merchant:type_name -> billing.transaction.v1.MerchantTransactionServiceFetchTransactionMerchant
	11, // 8: billing.transaction.v1.MerchantTransactionServiceFetchTransaction.user:type_name -> billing.transaction.v1.MerchantTransactionServiceFetchTransactionUser
	16, // 9: billing.transaction.v1.MerchantTransactionServiceFetchTransaction.transaction_type:type_name -> algoenum.v1.TransactionType
	17, // 10: billing.transaction.v1.MerchantTransactionServiceFetchTransaction.transaction_status:type_name -> algoenum.v1.TransactionStatus
	1,  // 11: billing.transaction.v1.MerchantTransactionServiceFetchTransaction.user_balance_transaction:type_name -> billing.transaction.v1.MerchantTransactionServiceFetchTransactionUserBalanceTransaction
	7,  // 12: billing.transaction.v1.MerchantTransactionServiceFetchTransaction.debit_transaction:type_name -> billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransaction
	5,  // 13: billing.transaction.v1.MerchantTransactionServiceFetchTransaction.credit_transaction:type_name -> billing.transaction.v1.MerchantTransactionServiceFetchTransactionCreditTransaction
	22, // 14: billing.transaction.v1.MerchantTransactionServiceFetchTransaction.currency:type_name -> algoenum.v1.Currency
	6,  // 15: billing.transaction.v1.MerchantTransactionServiceFetchTransactionCreditTransaction.payment_gateway:type_name -> billing.transaction.v1.MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway
	23, // 16: billing.transaction.v1.MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway.payment_gateway_type:type_name -> algoenum.v1.PaymentGatewayType
	9,  // 17: billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransaction.plan:type_name -> billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransactionPlan
	8,  // 18: billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransaction.subscription:type_name -> billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscription
	24, // 19: billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransaction.order_type:type_name -> algoenum.v1.OrderType
	10, // 20: billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransactionPlan.plan_price:type_name -> billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice
	19, // 21: billing.transaction.v1.MerchantTransactionServiceFetchUserBalanceRequest.balance_type:type_name -> algoenum.v1.BalanceType
	18, // 22: billing.transaction.v1.MerchantTransactionServiceFetchUserBalanceRequest.pagination:type_name -> utils.v1.PaginationRequest
	14, // 23: billing.transaction.v1.MerchantTransactionServiceFetchUserBalanceResponse.users:type_name -> billing.transaction.v1.MerchantTransactionServiceUserBalanceUsers
	21, // 24: billing.transaction.v1.MerchantTransactionServiceFetchUserBalanceResponse.error:type_name -> errmsg.v1.ErrorMessage
	20, // 25: billing.transaction.v1.MerchantTransactionServiceFetchUserBalanceResponse.pagination:type_name -> utils.v1.PaginationResponse
	15, // 26: billing.transaction.v1.MerchantTransactionServiceUserBalanceUsers.user_balances:type_name -> billing.transaction.v1.MerchantTransactionServiceUserBalanceOfUser
	19, // 27: billing.transaction.v1.MerchantTransactionServiceUserBalanceOfUser.balance_type:type_name -> algoenum.v1.BalanceType
	22, // 28: billing.transaction.v1.MerchantTransactionServiceUserBalanceOfUser.currency:type_name -> algoenum.v1.Currency
	12, // 29: billing.transaction.v1.MerchantTransactionService.FetchUserBalance:input_type -> billing.transaction.v1.MerchantTransactionServiceFetchUserBalanceRequest
	0,  // 30: billing.transaction.v1.MerchantTransactionService.FetchTransaction:input_type -> billing.transaction.v1.MerchantTransactionServiceFetchTransactionRequest
	13, // 31: billing.transaction.v1.MerchantTransactionService.FetchUserBalance:output_type -> billing.transaction.v1.MerchantTransactionServiceFetchUserBalanceResponse
	2,  // 32: billing.transaction.v1.MerchantTransactionService.FetchTransaction:output_type -> billing.transaction.v1.MerchantTransactionServiceFetchTransactionResponse
	31, // [31:33] is the sub-list for method output_type
	29, // [29:31] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_billing_transaction_v1_merchant_proto_init() }
func file_billing_transaction_v1_merchant_proto_init() {
	if File_billing_transaction_v1_merchant_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_billing_transaction_v1_merchant_proto_rawDesc), len(file_billing_transaction_v1_merchant_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_billing_transaction_v1_merchant_proto_goTypes,
		DependencyIndexes: file_billing_transaction_v1_merchant_proto_depIdxs,
		MessageInfos:      file_billing_transaction_v1_merchant_proto_msgTypes,
	}.Build()
	File_billing_transaction_v1_merchant_proto = out.File
	file_billing_transaction_v1_merchant_proto_goTypes = nil
	file_billing_transaction_v1_merchant_proto_depIdxs = nil
}
