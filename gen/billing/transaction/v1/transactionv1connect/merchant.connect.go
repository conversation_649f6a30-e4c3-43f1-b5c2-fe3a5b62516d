// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: billing/transaction/v1/merchant.proto

package transactionv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/transaction/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// MerchantTransactionServiceName is the fully-qualified name of the MerchantTransactionService
	// service.
	MerchantTransactionServiceName = "billing.transaction.v1.MerchantTransactionService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// MerchantTransactionServiceFetchUserBalanceProcedure is the fully-qualified name of the
	// MerchantTransactionService's FetchUserBalance RPC.
	MerchantTransactionServiceFetchUserBalanceProcedure = "/billing.transaction.v1.MerchantTransactionService/FetchUserBalance"
	// MerchantTransactionServiceFetchTransactionProcedure is the fully-qualified name of the
	// MerchantTransactionService's FetchTransaction RPC.
	MerchantTransactionServiceFetchTransactionProcedure = "/billing.transaction.v1.MerchantTransactionService/FetchTransaction"
)

// MerchantTransactionServiceClient is a client for the
// billing.transaction.v1.MerchantTransactionService service.
type MerchantTransactionServiceClient interface {
	FetchUserBalance(context.Context, *connect.Request[v1.MerchantTransactionServiceFetchUserBalanceRequest]) (*connect.Response[v1.MerchantTransactionServiceFetchUserBalanceResponse], error)
	FetchTransaction(context.Context, *connect.Request[v1.MerchantTransactionServiceFetchTransactionRequest]) (*connect.Response[v1.MerchantTransactionServiceFetchTransactionResponse], error)
}

// NewMerchantTransactionServiceClient constructs a client for the
// billing.transaction.v1.MerchantTransactionService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewMerchantTransactionServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) MerchantTransactionServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	merchantTransactionServiceMethods := v1.File_billing_transaction_v1_merchant_proto.Services().ByName("MerchantTransactionService").Methods()
	return &merchantTransactionServiceClient{
		fetchUserBalance: connect.NewClient[v1.MerchantTransactionServiceFetchUserBalanceRequest, v1.MerchantTransactionServiceFetchUserBalanceResponse](
			httpClient,
			baseURL+MerchantTransactionServiceFetchUserBalanceProcedure,
			connect.WithSchema(merchantTransactionServiceMethods.ByName("FetchUserBalance")),
			connect.WithClientOptions(opts...),
		),
		fetchTransaction: connect.NewClient[v1.MerchantTransactionServiceFetchTransactionRequest, v1.MerchantTransactionServiceFetchTransactionResponse](
			httpClient,
			baseURL+MerchantTransactionServiceFetchTransactionProcedure,
			connect.WithSchema(merchantTransactionServiceMethods.ByName("FetchTransaction")),
			connect.WithClientOptions(opts...),
		),
	}
}

// merchantTransactionServiceClient implements MerchantTransactionServiceClient.
type merchantTransactionServiceClient struct {
	fetchUserBalance *connect.Client[v1.MerchantTransactionServiceFetchUserBalanceRequest, v1.MerchantTransactionServiceFetchUserBalanceResponse]
	fetchTransaction *connect.Client[v1.MerchantTransactionServiceFetchTransactionRequest, v1.MerchantTransactionServiceFetchTransactionResponse]
}

// FetchUserBalance calls billing.transaction.v1.MerchantTransactionService.FetchUserBalance.
func (c *merchantTransactionServiceClient) FetchUserBalance(ctx context.Context, req *connect.Request[v1.MerchantTransactionServiceFetchUserBalanceRequest]) (*connect.Response[v1.MerchantTransactionServiceFetchUserBalanceResponse], error) {
	return c.fetchUserBalance.CallUnary(ctx, req)
}

// FetchTransaction calls billing.transaction.v1.MerchantTransactionService.FetchTransaction.
func (c *merchantTransactionServiceClient) FetchTransaction(ctx context.Context, req *connect.Request[v1.MerchantTransactionServiceFetchTransactionRequest]) (*connect.Response[v1.MerchantTransactionServiceFetchTransactionResponse], error) {
	return c.fetchTransaction.CallUnary(ctx, req)
}

// MerchantTransactionServiceHandler is an implementation of the
// billing.transaction.v1.MerchantTransactionService service.
type MerchantTransactionServiceHandler interface {
	FetchUserBalance(context.Context, *connect.Request[v1.MerchantTransactionServiceFetchUserBalanceRequest]) (*connect.Response[v1.MerchantTransactionServiceFetchUserBalanceResponse], error)
	FetchTransaction(context.Context, *connect.Request[v1.MerchantTransactionServiceFetchTransactionRequest]) (*connect.Response[v1.MerchantTransactionServiceFetchTransactionResponse], error)
}

// NewMerchantTransactionServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewMerchantTransactionServiceHandler(svc MerchantTransactionServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	merchantTransactionServiceMethods := v1.File_billing_transaction_v1_merchant_proto.Services().ByName("MerchantTransactionService").Methods()
	merchantTransactionServiceFetchUserBalanceHandler := connect.NewUnaryHandler(
		MerchantTransactionServiceFetchUserBalanceProcedure,
		svc.FetchUserBalance,
		connect.WithSchema(merchantTransactionServiceMethods.ByName("FetchUserBalance")),
		connect.WithHandlerOptions(opts...),
	)
	merchantTransactionServiceFetchTransactionHandler := connect.NewUnaryHandler(
		MerchantTransactionServiceFetchTransactionProcedure,
		svc.FetchTransaction,
		connect.WithSchema(merchantTransactionServiceMethods.ByName("FetchTransaction")),
		connect.WithHandlerOptions(opts...),
	)
	return "/billing.transaction.v1.MerchantTransactionService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case MerchantTransactionServiceFetchUserBalanceProcedure:
			merchantTransactionServiceFetchUserBalanceHandler.ServeHTTP(w, r)
		case MerchantTransactionServiceFetchTransactionProcedure:
			merchantTransactionServiceFetchTransactionHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedMerchantTransactionServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedMerchantTransactionServiceHandler struct{}

func (UnimplementedMerchantTransactionServiceHandler) FetchUserBalance(context.Context, *connect.Request[v1.MerchantTransactionServiceFetchUserBalanceRequest]) (*connect.Response[v1.MerchantTransactionServiceFetchUserBalanceResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.transaction.v1.MerchantTransactionService.FetchUserBalance is not implemented"))
}

func (UnimplementedMerchantTransactionServiceHandler) FetchTransaction(context.Context, *connect.Request[v1.MerchantTransactionServiceFetchTransactionRequest]) (*connect.Response[v1.MerchantTransactionServiceFetchTransactionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.transaction.v1.MerchantTransactionService.FetchTransaction is not implemented"))
}
