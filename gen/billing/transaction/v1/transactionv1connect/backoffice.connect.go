// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: billing/transaction/v1/backoffice.proto

package transactionv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/transaction/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackofficeTransactionServiceName is the fully-qualified name of the BackofficeTransactionService
	// service.
	BackofficeTransactionServiceName = "billing.transaction.v1.BackofficeTransactionService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackofficeTransactionServiceFetchUserBalanceProcedure is the fully-qualified name of the
	// BackofficeTransactionService's FetchUserBalance RPC.
	BackofficeTransactionServiceFetchUserBalanceProcedure = "/billing.transaction.v1.BackofficeTransactionService/FetchUserBalance"
	// BackofficeTransactionServiceFetchTransactionProcedure is the fully-qualified name of the
	// BackofficeTransactionService's FetchTransaction RPC.
	BackofficeTransactionServiceFetchTransactionProcedure = "/billing.transaction.v1.BackofficeTransactionService/FetchTransaction"
	// BackofficeTransactionServiceAddCreditUserProcedure is the fully-qualified name of the
	// BackofficeTransactionService's AddCreditUser RPC.
	BackofficeTransactionServiceAddCreditUserProcedure = "/billing.transaction.v1.BackofficeTransactionService/AddCreditUser"
)

// BackofficeTransactionServiceClient is a client for the
// billing.transaction.v1.BackofficeTransactionService service.
type BackofficeTransactionServiceClient interface {
	FetchUserBalance(context.Context, *connect.Request[v1.BackofficeTransactionServiceFetchUserBalanceRequest]) (*connect.Response[v1.BackofficeTransactionServiceFetchUserBalanceResponse], error)
	FetchTransaction(context.Context, *connect.Request[v1.BackofficeTransactionServiceFetchTransactionRequest]) (*connect.Response[v1.BackofficeTransactionServiceFetchTransactionResponse], error)
	// for testing, only add to promotion balance
	AddCreditUser(context.Context, *connect.Request[v1.BackofficeTransactionServiceAddCreditUserRequest]) (*connect.Response[v1.BackofficeTransactionServiceAddCreditUserResponse], error)
}

// NewBackofficeTransactionServiceClient constructs a client for the
// billing.transaction.v1.BackofficeTransactionService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackofficeTransactionServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackofficeTransactionServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backofficeTransactionServiceMethods := v1.File_billing_transaction_v1_backoffice_proto.Services().ByName("BackofficeTransactionService").Methods()
	return &backofficeTransactionServiceClient{
		fetchUserBalance: connect.NewClient[v1.BackofficeTransactionServiceFetchUserBalanceRequest, v1.BackofficeTransactionServiceFetchUserBalanceResponse](
			httpClient,
			baseURL+BackofficeTransactionServiceFetchUserBalanceProcedure,
			connect.WithSchema(backofficeTransactionServiceMethods.ByName("FetchUserBalance")),
			connect.WithClientOptions(opts...),
		),
		fetchTransaction: connect.NewClient[v1.BackofficeTransactionServiceFetchTransactionRequest, v1.BackofficeTransactionServiceFetchTransactionResponse](
			httpClient,
			baseURL+BackofficeTransactionServiceFetchTransactionProcedure,
			connect.WithSchema(backofficeTransactionServiceMethods.ByName("FetchTransaction")),
			connect.WithClientOptions(opts...),
		),
		addCreditUser: connect.NewClient[v1.BackofficeTransactionServiceAddCreditUserRequest, v1.BackofficeTransactionServiceAddCreditUserResponse](
			httpClient,
			baseURL+BackofficeTransactionServiceAddCreditUserProcedure,
			connect.WithSchema(backofficeTransactionServiceMethods.ByName("AddCreditUser")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backofficeTransactionServiceClient implements BackofficeTransactionServiceClient.
type backofficeTransactionServiceClient struct {
	fetchUserBalance *connect.Client[v1.BackofficeTransactionServiceFetchUserBalanceRequest, v1.BackofficeTransactionServiceFetchUserBalanceResponse]
	fetchTransaction *connect.Client[v1.BackofficeTransactionServiceFetchTransactionRequest, v1.BackofficeTransactionServiceFetchTransactionResponse]
	addCreditUser    *connect.Client[v1.BackofficeTransactionServiceAddCreditUserRequest, v1.BackofficeTransactionServiceAddCreditUserResponse]
}

// FetchUserBalance calls billing.transaction.v1.BackofficeTransactionService.FetchUserBalance.
func (c *backofficeTransactionServiceClient) FetchUserBalance(ctx context.Context, req *connect.Request[v1.BackofficeTransactionServiceFetchUserBalanceRequest]) (*connect.Response[v1.BackofficeTransactionServiceFetchUserBalanceResponse], error) {
	return c.fetchUserBalance.CallUnary(ctx, req)
}

// FetchTransaction calls billing.transaction.v1.BackofficeTransactionService.FetchTransaction.
func (c *backofficeTransactionServiceClient) FetchTransaction(ctx context.Context, req *connect.Request[v1.BackofficeTransactionServiceFetchTransactionRequest]) (*connect.Response[v1.BackofficeTransactionServiceFetchTransactionResponse], error) {
	return c.fetchTransaction.CallUnary(ctx, req)
}

// AddCreditUser calls billing.transaction.v1.BackofficeTransactionService.AddCreditUser.
func (c *backofficeTransactionServiceClient) AddCreditUser(ctx context.Context, req *connect.Request[v1.BackofficeTransactionServiceAddCreditUserRequest]) (*connect.Response[v1.BackofficeTransactionServiceAddCreditUserResponse], error) {
	return c.addCreditUser.CallUnary(ctx, req)
}

// BackofficeTransactionServiceHandler is an implementation of the
// billing.transaction.v1.BackofficeTransactionService service.
type BackofficeTransactionServiceHandler interface {
	FetchUserBalance(context.Context, *connect.Request[v1.BackofficeTransactionServiceFetchUserBalanceRequest]) (*connect.Response[v1.BackofficeTransactionServiceFetchUserBalanceResponse], error)
	FetchTransaction(context.Context, *connect.Request[v1.BackofficeTransactionServiceFetchTransactionRequest]) (*connect.Response[v1.BackofficeTransactionServiceFetchTransactionResponse], error)
	// for testing, only add to promotion balance
	AddCreditUser(context.Context, *connect.Request[v1.BackofficeTransactionServiceAddCreditUserRequest]) (*connect.Response[v1.BackofficeTransactionServiceAddCreditUserResponse], error)
}

// NewBackofficeTransactionServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackofficeTransactionServiceHandler(svc BackofficeTransactionServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backofficeTransactionServiceMethods := v1.File_billing_transaction_v1_backoffice_proto.Services().ByName("BackofficeTransactionService").Methods()
	backofficeTransactionServiceFetchUserBalanceHandler := connect.NewUnaryHandler(
		BackofficeTransactionServiceFetchUserBalanceProcedure,
		svc.FetchUserBalance,
		connect.WithSchema(backofficeTransactionServiceMethods.ByName("FetchUserBalance")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeTransactionServiceFetchTransactionHandler := connect.NewUnaryHandler(
		BackofficeTransactionServiceFetchTransactionProcedure,
		svc.FetchTransaction,
		connect.WithSchema(backofficeTransactionServiceMethods.ByName("FetchTransaction")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeTransactionServiceAddCreditUserHandler := connect.NewUnaryHandler(
		BackofficeTransactionServiceAddCreditUserProcedure,
		svc.AddCreditUser,
		connect.WithSchema(backofficeTransactionServiceMethods.ByName("AddCreditUser")),
		connect.WithHandlerOptions(opts...),
	)
	return "/billing.transaction.v1.BackofficeTransactionService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackofficeTransactionServiceFetchUserBalanceProcedure:
			backofficeTransactionServiceFetchUserBalanceHandler.ServeHTTP(w, r)
		case BackofficeTransactionServiceFetchTransactionProcedure:
			backofficeTransactionServiceFetchTransactionHandler.ServeHTTP(w, r)
		case BackofficeTransactionServiceAddCreditUserProcedure:
			backofficeTransactionServiceAddCreditUserHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackofficeTransactionServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackofficeTransactionServiceHandler struct{}

func (UnimplementedBackofficeTransactionServiceHandler) FetchUserBalance(context.Context, *connect.Request[v1.BackofficeTransactionServiceFetchUserBalanceRequest]) (*connect.Response[v1.BackofficeTransactionServiceFetchUserBalanceResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.transaction.v1.BackofficeTransactionService.FetchUserBalance is not implemented"))
}

func (UnimplementedBackofficeTransactionServiceHandler) FetchTransaction(context.Context, *connect.Request[v1.BackofficeTransactionServiceFetchTransactionRequest]) (*connect.Response[v1.BackofficeTransactionServiceFetchTransactionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.transaction.v1.BackofficeTransactionService.FetchTransaction is not implemented"))
}

func (UnimplementedBackofficeTransactionServiceHandler) AddCreditUser(context.Context, *connect.Request[v1.BackofficeTransactionServiceAddCreditUserRequest]) (*connect.Response[v1.BackofficeTransactionServiceAddCreditUserResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.transaction.v1.BackofficeTransactionService.AddCreditUser is not implemented"))
}
