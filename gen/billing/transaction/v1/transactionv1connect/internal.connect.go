// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: billing/transaction/v1/internal.proto

package transactionv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/transaction/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// InternalTransactionServiceName is the fully-qualified name of the InternalTransactionService
	// service.
	InternalTransactionServiceName = "billing.transaction.v1.InternalTransactionService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// InternalTransactionServiceCreateUserBalanceProcedure is the fully-qualified name of the
	// InternalTransactionService's CreateUserBalance RPC.
	InternalTransactionServiceCreateUserBalanceProcedure = "/billing.transaction.v1.InternalTransactionService/CreateUserBalance"
)

// InternalTransactionServiceClient is a client for the
// billing.transaction.v1.InternalTransactionService service.
type InternalTransactionServiceClient interface {
	CreateUserBalance(context.Context, *connect.Request[v1.InternalTransactionServiceCreateUserBalanceRequest]) (*connect.Response[v1.InternalTransactionServiceCreateUserBalanceResponse], error)
}

// NewInternalTransactionServiceClient constructs a client for the
// billing.transaction.v1.InternalTransactionService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewInternalTransactionServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) InternalTransactionServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	internalTransactionServiceMethods := v1.File_billing_transaction_v1_internal_proto.Services().ByName("InternalTransactionService").Methods()
	return &internalTransactionServiceClient{
		createUserBalance: connect.NewClient[v1.InternalTransactionServiceCreateUserBalanceRequest, v1.InternalTransactionServiceCreateUserBalanceResponse](
			httpClient,
			baseURL+InternalTransactionServiceCreateUserBalanceProcedure,
			connect.WithSchema(internalTransactionServiceMethods.ByName("CreateUserBalance")),
			connect.WithClientOptions(opts...),
		),
	}
}

// internalTransactionServiceClient implements InternalTransactionServiceClient.
type internalTransactionServiceClient struct {
	createUserBalance *connect.Client[v1.InternalTransactionServiceCreateUserBalanceRequest, v1.InternalTransactionServiceCreateUserBalanceResponse]
}

// CreateUserBalance calls billing.transaction.v1.InternalTransactionService.CreateUserBalance.
func (c *internalTransactionServiceClient) CreateUserBalance(ctx context.Context, req *connect.Request[v1.InternalTransactionServiceCreateUserBalanceRequest]) (*connect.Response[v1.InternalTransactionServiceCreateUserBalanceResponse], error) {
	return c.createUserBalance.CallUnary(ctx, req)
}

// InternalTransactionServiceHandler is an implementation of the
// billing.transaction.v1.InternalTransactionService service.
type InternalTransactionServiceHandler interface {
	CreateUserBalance(context.Context, *connect.Request[v1.InternalTransactionServiceCreateUserBalanceRequest]) (*connect.Response[v1.InternalTransactionServiceCreateUserBalanceResponse], error)
}

// NewInternalTransactionServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewInternalTransactionServiceHandler(svc InternalTransactionServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	internalTransactionServiceMethods := v1.File_billing_transaction_v1_internal_proto.Services().ByName("InternalTransactionService").Methods()
	internalTransactionServiceCreateUserBalanceHandler := connect.NewUnaryHandler(
		InternalTransactionServiceCreateUserBalanceProcedure,
		svc.CreateUserBalance,
		connect.WithSchema(internalTransactionServiceMethods.ByName("CreateUserBalance")),
		connect.WithHandlerOptions(opts...),
	)
	return "/billing.transaction.v1.InternalTransactionService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case InternalTransactionServiceCreateUserBalanceProcedure:
			internalTransactionServiceCreateUserBalanceHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedInternalTransactionServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedInternalTransactionServiceHandler struct{}

func (UnimplementedInternalTransactionServiceHandler) CreateUserBalance(context.Context, *connect.Request[v1.InternalTransactionServiceCreateUserBalanceRequest]) (*connect.Response[v1.InternalTransactionServiceCreateUserBalanceResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.transaction.v1.InternalTransactionService.CreateUserBalance is not implemented"))
}
