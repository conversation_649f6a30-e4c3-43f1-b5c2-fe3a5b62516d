// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: billing/transaction/v1/customer.proto

package transactionv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/transaction/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CustomerTransactionServiceName is the fully-qualified name of the CustomerTransactionService
	// service.
	CustomerTransactionServiceName = "billing.transaction.v1.CustomerTransactionService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CustomerTransactionServiceFetchBalanceProcedure is the fully-qualified name of the
	// CustomerTransactionService's FetchBalance RPC.
	CustomerTransactionServiceFetchBalanceProcedure = "/billing.transaction.v1.CustomerTransactionService/FetchBalance"
	// CustomerTransactionServiceFetchTransactionProcedure is the fully-qualified name of the
	// CustomerTransactionService's FetchTransaction RPC.
	CustomerTransactionServiceFetchTransactionProcedure = "/billing.transaction.v1.CustomerTransactionService/FetchTransaction"
)

// CustomerTransactionServiceClient is a client for the
// billing.transaction.v1.CustomerTransactionService service.
type CustomerTransactionServiceClient interface {
	FetchBalance(context.Context, *connect.Request[v1.CustomerTransactionServiceFetchBalanceRequest]) (*connect.Response[v1.CustomerTransactionServiceFetchBalanceResponse], error)
	FetchTransaction(context.Context, *connect.Request[v1.CustomerTransactionServiceFetchTransactionRequest]) (*connect.Response[v1.CustomerTransactionServiceFetchTransactionResponse], error)
}

// NewCustomerTransactionServiceClient constructs a client for the
// billing.transaction.v1.CustomerTransactionService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCustomerTransactionServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CustomerTransactionServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	customerTransactionServiceMethods := v1.File_billing_transaction_v1_customer_proto.Services().ByName("CustomerTransactionService").Methods()
	return &customerTransactionServiceClient{
		fetchBalance: connect.NewClient[v1.CustomerTransactionServiceFetchBalanceRequest, v1.CustomerTransactionServiceFetchBalanceResponse](
			httpClient,
			baseURL+CustomerTransactionServiceFetchBalanceProcedure,
			connect.WithSchema(customerTransactionServiceMethods.ByName("FetchBalance")),
			connect.WithClientOptions(opts...),
		),
		fetchTransaction: connect.NewClient[v1.CustomerTransactionServiceFetchTransactionRequest, v1.CustomerTransactionServiceFetchTransactionResponse](
			httpClient,
			baseURL+CustomerTransactionServiceFetchTransactionProcedure,
			connect.WithSchema(customerTransactionServiceMethods.ByName("FetchTransaction")),
			connect.WithClientOptions(opts...),
		),
	}
}

// customerTransactionServiceClient implements CustomerTransactionServiceClient.
type customerTransactionServiceClient struct {
	fetchBalance     *connect.Client[v1.CustomerTransactionServiceFetchBalanceRequest, v1.CustomerTransactionServiceFetchBalanceResponse]
	fetchTransaction *connect.Client[v1.CustomerTransactionServiceFetchTransactionRequest, v1.CustomerTransactionServiceFetchTransactionResponse]
}

// FetchBalance calls billing.transaction.v1.CustomerTransactionService.FetchBalance.
func (c *customerTransactionServiceClient) FetchBalance(ctx context.Context, req *connect.Request[v1.CustomerTransactionServiceFetchBalanceRequest]) (*connect.Response[v1.CustomerTransactionServiceFetchBalanceResponse], error) {
	return c.fetchBalance.CallUnary(ctx, req)
}

// FetchTransaction calls billing.transaction.v1.CustomerTransactionService.FetchTransaction.
func (c *customerTransactionServiceClient) FetchTransaction(ctx context.Context, req *connect.Request[v1.CustomerTransactionServiceFetchTransactionRequest]) (*connect.Response[v1.CustomerTransactionServiceFetchTransactionResponse], error) {
	return c.fetchTransaction.CallUnary(ctx, req)
}

// CustomerTransactionServiceHandler is an implementation of the
// billing.transaction.v1.CustomerTransactionService service.
type CustomerTransactionServiceHandler interface {
	FetchBalance(context.Context, *connect.Request[v1.CustomerTransactionServiceFetchBalanceRequest]) (*connect.Response[v1.CustomerTransactionServiceFetchBalanceResponse], error)
	FetchTransaction(context.Context, *connect.Request[v1.CustomerTransactionServiceFetchTransactionRequest]) (*connect.Response[v1.CustomerTransactionServiceFetchTransactionResponse], error)
}

// NewCustomerTransactionServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCustomerTransactionServiceHandler(svc CustomerTransactionServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	customerTransactionServiceMethods := v1.File_billing_transaction_v1_customer_proto.Services().ByName("CustomerTransactionService").Methods()
	customerTransactionServiceFetchBalanceHandler := connect.NewUnaryHandler(
		CustomerTransactionServiceFetchBalanceProcedure,
		svc.FetchBalance,
		connect.WithSchema(customerTransactionServiceMethods.ByName("FetchBalance")),
		connect.WithHandlerOptions(opts...),
	)
	customerTransactionServiceFetchTransactionHandler := connect.NewUnaryHandler(
		CustomerTransactionServiceFetchTransactionProcedure,
		svc.FetchTransaction,
		connect.WithSchema(customerTransactionServiceMethods.ByName("FetchTransaction")),
		connect.WithHandlerOptions(opts...),
	)
	return "/billing.transaction.v1.CustomerTransactionService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CustomerTransactionServiceFetchBalanceProcedure:
			customerTransactionServiceFetchBalanceHandler.ServeHTTP(w, r)
		case CustomerTransactionServiceFetchTransactionProcedure:
			customerTransactionServiceFetchTransactionHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCustomerTransactionServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCustomerTransactionServiceHandler struct{}

func (UnimplementedCustomerTransactionServiceHandler) FetchBalance(context.Context, *connect.Request[v1.CustomerTransactionServiceFetchBalanceRequest]) (*connect.Response[v1.CustomerTransactionServiceFetchBalanceResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.transaction.v1.CustomerTransactionService.FetchBalance is not implemented"))
}

func (UnimplementedCustomerTransactionServiceHandler) FetchTransaction(context.Context, *connect.Request[v1.CustomerTransactionServiceFetchTransactionRequest]) (*connect.Response[v1.CustomerTransactionServiceFetchTransactionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.transaction.v1.CustomerTransactionService.FetchTransaction is not implemented"))
}
