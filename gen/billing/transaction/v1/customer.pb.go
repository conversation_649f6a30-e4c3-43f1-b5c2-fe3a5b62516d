// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: billing/transaction/v1/customer.proto

package transactionv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CustomerTransactionServiceFetchBalanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerTransactionServiceFetchBalanceRequest) Reset() {
	*x = CustomerTransactionServiceFetchBalanceRequest{}
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerTransactionServiceFetchBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerTransactionServiceFetchBalanceRequest) ProtoMessage() {}

func (x *CustomerTransactionServiceFetchBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerTransactionServiceFetchBalanceRequest.ProtoReflect.Descriptor instead.
func (*CustomerTransactionServiceFetchBalanceRequest) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_customer_proto_rawDescGZIP(), []int{0}
}

type CustomerTransactionServiceFetchBalanceResponse struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	Currency      v1.Currency                          `protobuf:"varint,1,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	Balances      []*CustomerTransactionServiceBalance `protobuf:"bytes,2,rep,name=balances,proto3" json:"balances,omitempty"`
	Error         *v11.ErrorMessage                    `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerTransactionServiceFetchBalanceResponse) Reset() {
	*x = CustomerTransactionServiceFetchBalanceResponse{}
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerTransactionServiceFetchBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerTransactionServiceFetchBalanceResponse) ProtoMessage() {}

func (x *CustomerTransactionServiceFetchBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerTransactionServiceFetchBalanceResponse.ProtoReflect.Descriptor instead.
func (*CustomerTransactionServiceFetchBalanceResponse) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_customer_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerTransactionServiceFetchBalanceResponse) GetCurrency() v1.Currency {
	if x != nil {
		return x.Currency
	}
	return v1.Currency(0)
}

func (x *CustomerTransactionServiceFetchBalanceResponse) GetBalances() []*CustomerTransactionServiceBalance {
	if x != nil {
		return x.Balances
	}
	return nil
}

func (x *CustomerTransactionServiceFetchBalanceResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerTransactionServiceBalance struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	BalanceType    v1.BalanceType         `protobuf:"varint,1,opt,name=balance_type,json=balanceType,proto3,enum=algoenum.v1.BalanceType" json:"balance_type,omitempty"`
	CurrentBalance float64                `protobuf:"fixed64,2,opt,name=current_balance,json=currentBalance,proto3" json:"current_balance,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerTransactionServiceBalance) Reset() {
	*x = CustomerTransactionServiceBalance{}
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerTransactionServiceBalance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerTransactionServiceBalance) ProtoMessage() {}

func (x *CustomerTransactionServiceBalance) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerTransactionServiceBalance.ProtoReflect.Descriptor instead.
func (*CustomerTransactionServiceBalance) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_customer_proto_rawDescGZIP(), []int{2}
}

func (x *CustomerTransactionServiceBalance) GetBalanceType() v1.BalanceType {
	if x != nil {
		return x.BalanceType
	}
	return v1.BalanceType(0)
}

func (x *CustomerTransactionServiceBalance) GetCurrentBalance() float64 {
	if x != nil {
		return x.CurrentBalance
	}
	return 0
}

type CustomerTransactionServiceFetchTransactionRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGateway  string                 `protobuf:"bytes,2,opt,name=id_payment_gateway,json=idPaymentGateway,proto3" json:"id_payment_gateway,omitempty"`
	TransactionType   v1.TransactionType     `protobuf:"varint,3,opt,name=transaction_type,json=transactionType,proto3,enum=algoenum.v1.TransactionType" json:"transaction_type,omitempty"`
	TransactionStatus v1.TransactionStatus   `protobuf:"varint,4,opt,name=transaction_status,json=transactionStatus,proto3,enum=algoenum.v1.TransactionStatus" json:"transaction_status,omitempty"`
	FromUnix          int64                  `protobuf:"varint,5,opt,name=from_unix,json=fromUnix,proto3" json:"from_unix,omitempty"`
	ToUnix            int64                  `protobuf:"varint,6,opt,name=to_unix,json=toUnix,proto3" json:"to_unix,omitempty"`
	Pagination        *v12.PaginationRequest `protobuf:"bytes,7,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CustomerTransactionServiceFetchTransactionRequest) Reset() {
	*x = CustomerTransactionServiceFetchTransactionRequest{}
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerTransactionServiceFetchTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerTransactionServiceFetchTransactionRequest) ProtoMessage() {}

func (x *CustomerTransactionServiceFetchTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerTransactionServiceFetchTransactionRequest.ProtoReflect.Descriptor instead.
func (*CustomerTransactionServiceFetchTransactionRequest) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_customer_proto_rawDescGZIP(), []int{3}
}

func (x *CustomerTransactionServiceFetchTransactionRequest) GetIdPaymentGateway() string {
	if x != nil {
		return x.IdPaymentGateway
	}
	return ""
}

func (x *CustomerTransactionServiceFetchTransactionRequest) GetTransactionType() v1.TransactionType {
	if x != nil {
		return x.TransactionType
	}
	return v1.TransactionType(0)
}

func (x *CustomerTransactionServiceFetchTransactionRequest) GetTransactionStatus() v1.TransactionStatus {
	if x != nil {
		return x.TransactionStatus
	}
	return v1.TransactionStatus(0)
}

func (x *CustomerTransactionServiceFetchTransactionRequest) GetFromUnix() int64 {
	if x != nil {
		return x.FromUnix
	}
	return 0
}

func (x *CustomerTransactionServiceFetchTransactionRequest) GetToUnix() int64 {
	if x != nil {
		return x.ToUnix
	}
	return 0
}

func (x *CustomerTransactionServiceFetchTransactionRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerTransactionServiceFetchTransactionUserBalanceTransaction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdUserBalance string                 `protobuf:"bytes,1,opt,name=id_user_balance,json=idUserBalance,proto3" json:"id_user_balance,omitempty"`
	BalanceType   v1.BalanceType         `protobuf:"varint,2,opt,name=balance_type,json=balanceType,proto3,enum=algoenum.v1.BalanceType" json:"balance_type,omitempty"`
	Amount        float64                `protobuf:"fixed64,3,opt,name=amount,proto3" json:"amount,omitempty"`
	BalanceBefore float64                `protobuf:"fixed64,4,opt,name=balance_before,json=balanceBefore,proto3" json:"balance_before,omitempty"`
	BalanceAfter  float64                `protobuf:"fixed64,5,opt,name=balance_after,json=balanceAfter,proto3" json:"balance_after,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerTransactionServiceFetchTransactionUserBalanceTransaction) Reset() {
	*x = CustomerTransactionServiceFetchTransactionUserBalanceTransaction{}
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerTransactionServiceFetchTransactionUserBalanceTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerTransactionServiceFetchTransactionUserBalanceTransaction) ProtoMessage() {}

func (x *CustomerTransactionServiceFetchTransactionUserBalanceTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerTransactionServiceFetchTransactionUserBalanceTransaction.ProtoReflect.Descriptor instead.
func (*CustomerTransactionServiceFetchTransactionUserBalanceTransaction) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_customer_proto_rawDescGZIP(), []int{4}
}

func (x *CustomerTransactionServiceFetchTransactionUserBalanceTransaction) GetIdUserBalance() string {
	if x != nil {
		return x.IdUserBalance
	}
	return ""
}

func (x *CustomerTransactionServiceFetchTransactionUserBalanceTransaction) GetBalanceType() v1.BalanceType {
	if x != nil {
		return x.BalanceType
	}
	return v1.BalanceType(0)
}

func (x *CustomerTransactionServiceFetchTransactionUserBalanceTransaction) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *CustomerTransactionServiceFetchTransactionUserBalanceTransaction) GetBalanceBefore() float64 {
	if x != nil {
		return x.BalanceBefore
	}
	return 0
}

func (x *CustomerTransactionServiceFetchTransactionUserBalanceTransaction) GetBalanceAfter() float64 {
	if x != nil {
		return x.BalanceAfter
	}
	return 0
}

type CustomerTransactionServiceFetchTransactionResponse struct {
	state         protoimpl.MessageState                        `protogen:"open.v1"`
	Transactions  []*CustomerTransactionServiceFetchTransaction `protobuf:"bytes,1,rep,name=transactions,proto3" json:"transactions,omitempty"`
	Pagination    *v12.PaginationResponse                       `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Error         *v11.ErrorMessage                             `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerTransactionServiceFetchTransactionResponse) Reset() {
	*x = CustomerTransactionServiceFetchTransactionResponse{}
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerTransactionServiceFetchTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerTransactionServiceFetchTransactionResponse) ProtoMessage() {}

func (x *CustomerTransactionServiceFetchTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerTransactionServiceFetchTransactionResponse.ProtoReflect.Descriptor instead.
func (*CustomerTransactionServiceFetchTransactionResponse) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_customer_proto_rawDescGZIP(), []int{5}
}

func (x *CustomerTransactionServiceFetchTransactionResponse) GetTransactions() []*CustomerTransactionServiceFetchTransaction {
	if x != nil {
		return x.Transactions
	}
	return nil
}

func (x *CustomerTransactionServiceFetchTransactionResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *CustomerTransactionServiceFetchTransactionResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerTransactionServiceFetchTransaction struct {
	state                  protoimpl.MessageState                                              `protogen:"open.v1"`
	IdTransaction          string                                                              `protobuf:"bytes,1,opt,name=id_transaction,json=idTransaction,proto3" json:"id_transaction,omitempty"`
	TransactionType        v1.TransactionType                                                  `protobuf:"varint,2,opt,name=transaction_type,json=transactionType,proto3,enum=algoenum.v1.TransactionType" json:"transaction_type,omitempty"`
	TransactionStatus      v1.TransactionStatus                                                `protobuf:"varint,3,opt,name=transaction_status,json=transactionStatus,proto3,enum=algoenum.v1.TransactionStatus" json:"transaction_status,omitempty"`
	UserBalanceTransaction []*CustomerTransactionServiceFetchTransactionUserBalanceTransaction `protobuf:"bytes,4,rep,name=user_balance_transaction,json=userBalanceTransaction,proto3" json:"user_balance_transaction,omitempty"`
	DebitTransaction       *CustomerTransactionServiceFetchTransactionDebitTransaction         `protobuf:"bytes,5,opt,name=debit_transaction,json=debitTransaction,proto3" json:"debit_transaction,omitempty"`
	CreditTransaction      *CustomerTransactionServiceFetchTransactionCreditTransaction        `protobuf:"bytes,6,opt,name=credit_transaction,json=creditTransaction,proto3" json:"credit_transaction,omitempty"`
	TotalAmount            float64                                                             `protobuf:"fixed64,7,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	Currency               v1.Currency                                                         `protobuf:"varint,8,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	CreatedAt              int64                                                               `protobuf:"varint,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *CustomerTransactionServiceFetchTransaction) Reset() {
	*x = CustomerTransactionServiceFetchTransaction{}
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerTransactionServiceFetchTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerTransactionServiceFetchTransaction) ProtoMessage() {}

func (x *CustomerTransactionServiceFetchTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerTransactionServiceFetchTransaction.ProtoReflect.Descriptor instead.
func (*CustomerTransactionServiceFetchTransaction) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_customer_proto_rawDescGZIP(), []int{6}
}

func (x *CustomerTransactionServiceFetchTransaction) GetIdTransaction() string {
	if x != nil {
		return x.IdTransaction
	}
	return ""
}

func (x *CustomerTransactionServiceFetchTransaction) GetTransactionType() v1.TransactionType {
	if x != nil {
		return x.TransactionType
	}
	return v1.TransactionType(0)
}

func (x *CustomerTransactionServiceFetchTransaction) GetTransactionStatus() v1.TransactionStatus {
	if x != nil {
		return x.TransactionStatus
	}
	return v1.TransactionStatus(0)
}

func (x *CustomerTransactionServiceFetchTransaction) GetUserBalanceTransaction() []*CustomerTransactionServiceFetchTransactionUserBalanceTransaction {
	if x != nil {
		return x.UserBalanceTransaction
	}
	return nil
}

func (x *CustomerTransactionServiceFetchTransaction) GetDebitTransaction() *CustomerTransactionServiceFetchTransactionDebitTransaction {
	if x != nil {
		return x.DebitTransaction
	}
	return nil
}

func (x *CustomerTransactionServiceFetchTransaction) GetCreditTransaction() *CustomerTransactionServiceFetchTransactionCreditTransaction {
	if x != nil {
		return x.CreditTransaction
	}
	return nil
}

func (x *CustomerTransactionServiceFetchTransaction) GetTotalAmount() float64 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *CustomerTransactionServiceFetchTransaction) GetCurrency() v1.Currency {
	if x != nil {
		return x.Currency
	}
	return v1.Currency(0)
}

func (x *CustomerTransactionServiceFetchTransaction) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

type CustomerTransactionServiceFetchTransactionCreditTransaction struct {
	state           protoimpl.MessageState                                                     `protogen:"open.v1"`
	PaymentGateway  *CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway `protobuf:"bytes,1,opt,name=payment_gateway,json=paymentGateway,proto3" json:"payment_gateway,omitempty"`
	Description     string                                                                     `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	HashTransaction string                                                                     `protobuf:"bytes,3,opt,name=hash_transaction,json=hashTransaction,proto3" json:"hash_transaction,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CustomerTransactionServiceFetchTransactionCreditTransaction) Reset() {
	*x = CustomerTransactionServiceFetchTransactionCreditTransaction{}
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerTransactionServiceFetchTransactionCreditTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerTransactionServiceFetchTransactionCreditTransaction) ProtoMessage() {}

func (x *CustomerTransactionServiceFetchTransactionCreditTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerTransactionServiceFetchTransactionCreditTransaction.ProtoReflect.Descriptor instead.
func (*CustomerTransactionServiceFetchTransactionCreditTransaction) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_customer_proto_rawDescGZIP(), []int{7}
}

func (x *CustomerTransactionServiceFetchTransactionCreditTransaction) GetPaymentGateway() *CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway {
	if x != nil {
		return x.PaymentGateway
	}
	return nil
}

func (x *CustomerTransactionServiceFetchTransactionCreditTransaction) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CustomerTransactionServiceFetchTransactionCreditTransaction) GetHashTransaction() string {
	if x != nil {
		return x.HashTransaction
	}
	return ""
}

type CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGateway   string                 `protobuf:"bytes,1,opt,name=id_payment_gateway,json=idPaymentGateway,proto3" json:"id_payment_gateway,omitempty"`
	PaymentGatewayType v1.PaymentGatewayType  `protobuf:"varint,2,opt,name=payment_gateway_type,json=paymentGatewayType,proto3,enum=algoenum.v1.PaymentGatewayType" json:"payment_gateway_type,omitempty"`
	AccountHolderName  string                 `protobuf:"bytes,3,opt,name=account_holder_name,json=accountHolderName,proto3" json:"account_holder_name,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway) Reset() {
	*x = CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway{}
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway) ProtoMessage() {}

func (x *CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway.ProtoReflect.Descriptor instead.
func (*CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_customer_proto_rawDescGZIP(), []int{8}
}

func (x *CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway) GetIdPaymentGateway() string {
	if x != nil {
		return x.IdPaymentGateway
	}
	return ""
}

func (x *CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway) GetPaymentGatewayType() v1.PaymentGatewayType {
	if x != nil {
		return x.PaymentGatewayType
	}
	return v1.PaymentGatewayType(0)
}

func (x *CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway) GetAccountHolderName() string {
	if x != nil {
		return x.AccountHolderName
	}
	return ""
}

type CustomerTransactionServiceFetchTransactionDebitTransaction struct {
	state         protoimpl.MessageState                                                      `protogen:"open.v1"`
	Plan          *CustomerTransactionServiceFetchTransactionDebitTransactionPlan             `protobuf:"bytes,1,opt,name=plan,proto3" json:"plan,omitempty"`
	Subscription  *CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscription `protobuf:"bytes,2,opt,name=subscription,proto3" json:"subscription,omitempty"`
	OrderType     v1.OrderType                                                                `protobuf:"varint,3,opt,name=order_type,json=orderType,proto3,enum=algoenum.v1.OrderType" json:"order_type,omitempty"`
	TotalPurchase float64                                                                     `protobuf:"fixed64,4,opt,name=total_purchase,json=totalPurchase,proto3" json:"total_purchase,omitempty"`
	TotalDiscount float64                                                                     `protobuf:"fixed64,5,opt,name=total_discount,json=totalDiscount,proto3" json:"total_discount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransaction) Reset() {
	*x = CustomerTransactionServiceFetchTransactionDebitTransaction{}
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerTransactionServiceFetchTransactionDebitTransaction) ProtoMessage() {}

func (x *CustomerTransactionServiceFetchTransactionDebitTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerTransactionServiceFetchTransactionDebitTransaction.ProtoReflect.Descriptor instead.
func (*CustomerTransactionServiceFetchTransactionDebitTransaction) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_customer_proto_rawDescGZIP(), []int{9}
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransaction) GetPlan() *CustomerTransactionServiceFetchTransactionDebitTransactionPlan {
	if x != nil {
		return x.Plan
	}
	return nil
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransaction) GetSubscription() *CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscription {
	if x != nil {
		return x.Subscription
	}
	return nil
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransaction) GetOrderType() v1.OrderType {
	if x != nil {
		return x.OrderType
	}
	return v1.OrderType(0)
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransaction) GetTotalPurchase() float64 {
	if x != nil {
		return x.TotalPurchase
	}
	return 0
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransaction) GetTotalDiscount() float64 {
	if x != nil {
		return x.TotalDiscount
	}
	return 0
}

type CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscription struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdSubscription string                 `protobuf:"bytes,1,opt,name=id_subscription,json=idSubscription,proto3" json:"id_subscription,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscription) Reset() {
	*x = CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscription{}
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscription) ProtoMessage() {}

func (x *CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscription) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscription.ProtoReflect.Descriptor instead.
func (*CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscription) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_customer_proto_rawDescGZIP(), []int{10}
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscription) GetIdSubscription() string {
	if x != nil {
		return x.IdSubscription
	}
	return ""
}

type CustomerTransactionServiceFetchTransactionDebitTransactionPlan struct {
	state         protoimpl.MessageState                                               `protogen:"open.v1"`
	IdPlan        string                                                               `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	Name          string                                                               `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	PlanPrice     *CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice `protobuf:"bytes,3,opt,name=plan_price,json=planPrice,proto3" json:"plan_price,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransactionPlan) Reset() {
	*x = CustomerTransactionServiceFetchTransactionDebitTransactionPlan{}
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransactionPlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerTransactionServiceFetchTransactionDebitTransactionPlan) ProtoMessage() {}

func (x *CustomerTransactionServiceFetchTransactionDebitTransactionPlan) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerTransactionServiceFetchTransactionDebitTransactionPlan.ProtoReflect.Descriptor instead.
func (*CustomerTransactionServiceFetchTransactionDebitTransactionPlan) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_customer_proto_rawDescGZIP(), []int{11}
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransactionPlan) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransactionPlan) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransactionPlan) GetPlanPrice() *CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice {
	if x != nil {
		return x.PlanPrice
	}
	return nil
}

type CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	IdPlanPrice       string                 `protobuf:"bytes,1,opt,name=id_plan_price,json=idPlanPrice,proto3" json:"id_plan_price,omitempty"`
	PurchasePrice     float64                `protobuf:"fixed64,2,opt,name=purchase_price,json=purchasePrice,proto3" json:"purchase_price,omitempty"`
	DataTransferInGb  float64                `protobuf:"fixed64,3,opt,name=data_transfer_in_gb,json=dataTransferInGb,proto3" json:"data_transfer_in_gb,omitempty"`
	BillingCycleInSec int64                  `protobuf:"varint,4,opt,name=billing_cycle_in_sec,json=billingCycleInSec,proto3" json:"billing_cycle_in_sec,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice) Reset() {
	*x = CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice{}
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice) ProtoMessage() {}

func (x *CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_customer_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice.ProtoReflect.Descriptor instead.
func (*CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_customer_proto_rawDescGZIP(), []int{12}
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice) GetIdPlanPrice() string {
	if x != nil {
		return x.IdPlanPrice
	}
	return ""
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice) GetPurchasePrice() float64 {
	if x != nil {
		return x.PurchasePrice
	}
	return 0
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice) GetDataTransferInGb() float64 {
	if x != nil {
		return x.DataTransferInGb
	}
	return 0
}

func (x *CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice) GetBillingCycleInSec() int64 {
	if x != nil {
		return x.BillingCycleInSec
	}
	return 0
}

var File_billing_transaction_v1_customer_proto protoreflect.FileDescriptor

const file_billing_transaction_v1_customer_proto_rawDesc = "" +
	"\n" +
	"%billing/transaction/v1/customer.proto\x12\x16billing.transaction.v1\x1a\x14utils/v1/utils.proto\x1a\x18errmsg/v1/errormsg.proto\x1a\x1aalgoenum/v1/currency.proto\x1a&algoenum/v1/payment_gateway_type.proto\x1a\x1ealgoenum/v1/balance_type.proto\x1a$algoenum/v1/transaction_status.proto\x1a\"algoenum/v1/transaction_type.proto\x1a\x1calgoenum/v1/order_type.proto\"/\n" +
	"-CustomerTransactionServiceFetchBalanceRequest\"\xe9\x01\n" +
	".CustomerTransactionServiceFetchBalanceResponse\x121\n" +
	"\bcurrency\x18\x01 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\x12U\n" +
	"\bbalances\x18\x02 \x03(\v29.billing.transaction.v1.CustomerTransactionServiceBalanceR\bbalances\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x89\x01\n" +
	"!CustomerTransactionServiceBalance\x12;\n" +
	"\fbalance_type\x18\x01 \x01(\x0e2\x18.algoenum.v1.BalanceTypeR\vbalanceType\x12'\n" +
	"\x0fcurrent_balance\x18\x02 \x01(\x01R\x0ecurrentBalance\"\xec\x02\n" +
	"1CustomerTransactionServiceFetchTransactionRequest\x12,\n" +
	"\x12id_payment_gateway\x18\x02 \x01(\tR\x10idPaymentGateway\x12G\n" +
	"\x10transaction_type\x18\x03 \x01(\x0e2\x1c.algoenum.v1.TransactionTypeR\x0ftransactionType\x12M\n" +
	"\x12transaction_status\x18\x04 \x01(\x0e2\x1e.algoenum.v1.TransactionStatusR\x11transactionStatus\x12\x1b\n" +
	"\tfrom_unix\x18\x05 \x01(\x03R\bfromUnix\x12\x17\n" +
	"\ato_unix\x18\x06 \x01(\x03R\x06toUnix\x12;\n" +
	"\n" +
	"pagination\x18\a \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x8b\x02\n" +
	"@CustomerTransactionServiceFetchTransactionUserBalanceTransaction\x12&\n" +
	"\x0fid_user_balance\x18\x01 \x01(\tR\ridUserBalance\x12;\n" +
	"\fbalance_type\x18\x02 \x01(\x0e2\x18.algoenum.v1.BalanceTypeR\vbalanceType\x12\x16\n" +
	"\x06amount\x18\x03 \x01(\x01R\x06amount\x12%\n" +
	"\x0ebalance_before\x18\x04 \x01(\x01R\rbalanceBefore\x12#\n" +
	"\rbalance_after\x18\x05 \x01(\x01R\fbalanceAfter\"\x89\x02\n" +
	"2CustomerTransactionServiceFetchTransactionResponse\x12f\n" +
	"\ftransactions\x18\x01 \x03(\v2B.billing.transaction.v1.CustomerTransactionServiceFetchTransactionR\ftransactions\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xfb\x05\n" +
	"*CustomerTransactionServiceFetchTransaction\x12%\n" +
	"\x0eid_transaction\x18\x01 \x01(\tR\ridTransaction\x12G\n" +
	"\x10transaction_type\x18\x02 \x01(\x0e2\x1c.algoenum.v1.TransactionTypeR\x0ftransactionType\x12M\n" +
	"\x12transaction_status\x18\x03 \x01(\x0e2\x1e.algoenum.v1.TransactionStatusR\x11transactionStatus\x12\x92\x01\n" +
	"\x18user_balance_transaction\x18\x04 \x03(\v2X.billing.transaction.v1.CustomerTransactionServiceFetchTransactionUserBalanceTransactionR\x16userBalanceTransaction\x12\x7f\n" +
	"\x11debit_transaction\x18\x05 \x01(\v2R.billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransactionR\x10debitTransaction\x12\x82\x01\n" +
	"\x12credit_transaction\x18\x06 \x01(\v2S.billing.transaction.v1.CustomerTransactionServiceFetchTransactionCreditTransactionR\x11creditTransaction\x12!\n" +
	"\ftotal_amount\x18\a \x01(\x01R\vtotalAmount\x121\n" +
	"\bcurrency\x18\b \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\x12\x1d\n" +
	"\n" +
	"created_at\x18\t \x01(\x03R\tcreatedAt\"\x97\x02\n" +
	";CustomerTransactionServiceFetchTransactionCreditTransaction\x12\x8a\x01\n" +
	"\x0fpayment_gateway\x18\x01 \x01(\v2a.billing.transaction.v1.CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGatewayR\x0epaymentGateway\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12)\n" +
	"\x10hash_transaction\x18\x03 \x01(\tR\x0fhashTransaction\"\xfc\x01\n" +
	"ICustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway\x12,\n" +
	"\x12id_payment_gateway\x18\x01 \x01(\tR\x10idPaymentGateway\x12Q\n" +
	"\x14payment_gateway_type\x18\x02 \x01(\x0e2\x1f.algoenum.v1.PaymentGatewayTypeR\x12paymentGatewayType\x12.\n" +
	"\x13account_holder_name\x18\x03 \x01(\tR\x11accountHolderName\"\xb6\x03\n" +
	":CustomerTransactionServiceFetchTransactionDebitTransaction\x12j\n" +
	"\x04plan\x18\x01 \x01(\v2V.billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransactionPlanR\x04plan\x12\x86\x01\n" +
	"\fsubscription\x18\x02 \x01(\v2b.billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscriptionR\fsubscription\x125\n" +
	"\n" +
	"order_type\x18\x03 \x01(\x0e2\x16.algoenum.v1.OrderTypeR\torderType\x12%\n" +
	"\x0etotal_purchase\x18\x04 \x01(\x01R\rtotalPurchase\x12%\n" +
	"\x0etotal_discount\x18\x05 \x01(\x01R\rtotalDiscount\"u\n" +
	"JCustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscription\x12'\n" +
	"\x0fid_subscription\x18\x01 \x01(\tR\x0eidSubscription\"\xe9\x01\n" +
	">CustomerTransactionServiceFetchTransactionDebitTransactionPlan\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12z\n" +
	"\n" +
	"plan_price\x18\x03 \x01(\v2[.billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransactionPlanPriceR\tplanPrice\"\xf0\x01\n" +
	"CCustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice\x12\"\n" +
	"\rid_plan_price\x18\x01 \x01(\tR\vidPlanPrice\x12%\n" +
	"\x0epurchase_price\x18\x02 \x01(\x01R\rpurchasePrice\x12-\n" +
	"\x13data_transfer_in_gb\x18\x03 \x01(\x01R\x10dataTransferInGb\x12/\n" +
	"\x14billing_cycle_in_sec\x18\x04 \x01(\x03R\x11billingCycleInSec2\xe8\x02\n" +
	"\x1aCustomerTransactionService\x12\x9d\x01\n" +
	"\fFetchBalance\x12E.billing.transaction.v1.CustomerTransactionServiceFetchBalanceRequest\x1aF.billing.transaction.v1.CustomerTransactionServiceFetchBalanceResponse\x12\xa9\x01\n" +
	"\x10FetchTransaction\x12I.billing.transaction.v1.CustomerTransactionServiceFetchTransactionRequest\x1aJ.billing.transaction.v1.CustomerTransactionServiceFetchTransactionResponseBUZSgit.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/transaction/v1;transactionv1b\x06proto3"

var (
	file_billing_transaction_v1_customer_proto_rawDescOnce sync.Once
	file_billing_transaction_v1_customer_proto_rawDescData []byte
)

func file_billing_transaction_v1_customer_proto_rawDescGZIP() []byte {
	file_billing_transaction_v1_customer_proto_rawDescOnce.Do(func() {
		file_billing_transaction_v1_customer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_billing_transaction_v1_customer_proto_rawDesc), len(file_billing_transaction_v1_customer_proto_rawDesc)))
	})
	return file_billing_transaction_v1_customer_proto_rawDescData
}

var file_billing_transaction_v1_customer_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_billing_transaction_v1_customer_proto_goTypes = []any{
	(*CustomerTransactionServiceFetchBalanceRequest)(nil),                              // 0: billing.transaction.v1.CustomerTransactionServiceFetchBalanceRequest
	(*CustomerTransactionServiceFetchBalanceResponse)(nil),                             // 1: billing.transaction.v1.CustomerTransactionServiceFetchBalanceResponse
	(*CustomerTransactionServiceBalance)(nil),                                          // 2: billing.transaction.v1.CustomerTransactionServiceBalance
	(*CustomerTransactionServiceFetchTransactionRequest)(nil),                          // 3: billing.transaction.v1.CustomerTransactionServiceFetchTransactionRequest
	(*CustomerTransactionServiceFetchTransactionUserBalanceTransaction)(nil),           // 4: billing.transaction.v1.CustomerTransactionServiceFetchTransactionUserBalanceTransaction
	(*CustomerTransactionServiceFetchTransactionResponse)(nil),                         // 5: billing.transaction.v1.CustomerTransactionServiceFetchTransactionResponse
	(*CustomerTransactionServiceFetchTransaction)(nil),                                 // 6: billing.transaction.v1.CustomerTransactionServiceFetchTransaction
	(*CustomerTransactionServiceFetchTransactionCreditTransaction)(nil),                // 7: billing.transaction.v1.CustomerTransactionServiceFetchTransactionCreditTransaction
	(*CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway)(nil),  // 8: billing.transaction.v1.CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway
	(*CustomerTransactionServiceFetchTransactionDebitTransaction)(nil),                 // 9: billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransaction
	(*CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscription)(nil), // 10: billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscription
	(*CustomerTransactionServiceFetchTransactionDebitTransactionPlan)(nil),             // 11: billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransactionPlan
	(*CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice)(nil),        // 12: billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice
	(v1.Currency)(0),               // 13: algoenum.v1.Currency
	(*v11.ErrorMessage)(nil),       // 14: errmsg.v1.ErrorMessage
	(v1.BalanceType)(0),            // 15: algoenum.v1.BalanceType
	(v1.TransactionType)(0),        // 16: algoenum.v1.TransactionType
	(v1.TransactionStatus)(0),      // 17: algoenum.v1.TransactionStatus
	(*v12.PaginationRequest)(nil),  // 18: utils.v1.PaginationRequest
	(*v12.PaginationResponse)(nil), // 19: utils.v1.PaginationResponse
	(v1.PaymentGatewayType)(0),     // 20: algoenum.v1.PaymentGatewayType
	(v1.OrderType)(0),              // 21: algoenum.v1.OrderType
}
var file_billing_transaction_v1_customer_proto_depIdxs = []int32{
	13, // 0: billing.transaction.v1.CustomerTransactionServiceFetchBalanceResponse.currency:type_name -> algoenum.v1.Currency
	2,  // 1: billing.transaction.v1.CustomerTransactionServiceFetchBalanceResponse.balances:type_name -> billing.transaction.v1.CustomerTransactionServiceBalance
	14, // 2: billing.transaction.v1.CustomerTransactionServiceFetchBalanceResponse.error:type_name -> errmsg.v1.ErrorMessage
	15, // 3: billing.transaction.v1.CustomerTransactionServiceBalance.balance_type:type_name -> algoenum.v1.BalanceType
	16, // 4: billing.transaction.v1.CustomerTransactionServiceFetchTransactionRequest.transaction_type:type_name -> algoenum.v1.TransactionType
	17, // 5: billing.transaction.v1.CustomerTransactionServiceFetchTransactionRequest.transaction_status:type_name -> algoenum.v1.TransactionStatus
	18, // 6: billing.transaction.v1.CustomerTransactionServiceFetchTransactionRequest.pagination:type_name -> utils.v1.PaginationRequest
	15, // 7: billing.transaction.v1.CustomerTransactionServiceFetchTransactionUserBalanceTransaction.balance_type:type_name -> algoenum.v1.BalanceType
	6,  // 8: billing.transaction.v1.CustomerTransactionServiceFetchTransactionResponse.transactions:type_name -> billing.transaction.v1.CustomerTransactionServiceFetchTransaction
	19, // 9: billing.transaction.v1.CustomerTransactionServiceFetchTransactionResponse.pagination:type_name -> utils.v1.PaginationResponse
	14, // 10: billing.transaction.v1.CustomerTransactionServiceFetchTransactionResponse.error:type_name -> errmsg.v1.ErrorMessage
	16, // 11: billing.transaction.v1.CustomerTransactionServiceFetchTransaction.transaction_type:type_name -> algoenum.v1.TransactionType
	17, // 12: billing.transaction.v1.CustomerTransactionServiceFetchTransaction.transaction_status:type_name -> algoenum.v1.TransactionStatus
	4,  // 13: billing.transaction.v1.CustomerTransactionServiceFetchTransaction.user_balance_transaction:type_name -> billing.transaction.v1.CustomerTransactionServiceFetchTransactionUserBalanceTransaction
	9,  // 14: billing.transaction.v1.CustomerTransactionServiceFetchTransaction.debit_transaction:type_name -> billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransaction
	7,  // 15: billing.transaction.v1.CustomerTransactionServiceFetchTransaction.credit_transaction:type_name -> billing.transaction.v1.CustomerTransactionServiceFetchTransactionCreditTransaction
	13, // 16: billing.transaction.v1.CustomerTransactionServiceFetchTransaction.currency:type_name -> algoenum.v1.Currency
	8,  // 17: billing.transaction.v1.CustomerTransactionServiceFetchTransactionCreditTransaction.payment_gateway:type_name -> billing.transaction.v1.CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway
	20, // 18: billing.transaction.v1.CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway.payment_gateway_type:type_name -> algoenum.v1.PaymentGatewayType
	11, // 19: billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransaction.plan:type_name -> billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransactionPlan
	10, // 20: billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransaction.subscription:type_name -> billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscription
	21, // 21: billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransaction.order_type:type_name -> algoenum.v1.OrderType
	12, // 22: billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransactionPlan.plan_price:type_name -> billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice
	0,  // 23: billing.transaction.v1.CustomerTransactionService.FetchBalance:input_type -> billing.transaction.v1.CustomerTransactionServiceFetchBalanceRequest
	3,  // 24: billing.transaction.v1.CustomerTransactionService.FetchTransaction:input_type -> billing.transaction.v1.CustomerTransactionServiceFetchTransactionRequest
	1,  // 25: billing.transaction.v1.CustomerTransactionService.FetchBalance:output_type -> billing.transaction.v1.CustomerTransactionServiceFetchBalanceResponse
	5,  // 26: billing.transaction.v1.CustomerTransactionService.FetchTransaction:output_type -> billing.transaction.v1.CustomerTransactionServiceFetchTransactionResponse
	25, // [25:27] is the sub-list for method output_type
	23, // [23:25] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_billing_transaction_v1_customer_proto_init() }
func file_billing_transaction_v1_customer_proto_init() {
	if File_billing_transaction_v1_customer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_billing_transaction_v1_customer_proto_rawDesc), len(file_billing_transaction_v1_customer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_billing_transaction_v1_customer_proto_goTypes,
		DependencyIndexes: file_billing_transaction_v1_customer_proto_depIdxs,
		MessageInfos:      file_billing_transaction_v1_customer_proto_msgTypes,
	}.Build()
	File_billing_transaction_v1_customer_proto = out.File
	file_billing_transaction_v1_customer_proto_goTypes = nil
	file_billing_transaction_v1_customer_proto_depIdxs = nil
}
