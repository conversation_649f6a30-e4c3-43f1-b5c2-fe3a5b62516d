// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: billing/transaction/v1/backoffice.proto

package transactionv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackofficeTransactionServiceAddCreditUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdUser        string                 `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	Amount        float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Currency      v1.Currency            `protobuf:"varint,3,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTransactionServiceAddCreditUserRequest) Reset() {
	*x = BackofficeTransactionServiceAddCreditUserRequest{}
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTransactionServiceAddCreditUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTransactionServiceAddCreditUserRequest) ProtoMessage() {}

func (x *BackofficeTransactionServiceAddCreditUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTransactionServiceAddCreditUserRequest.ProtoReflect.Descriptor instead.
func (*BackofficeTransactionServiceAddCreditUserRequest) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_backoffice_proto_rawDescGZIP(), []int{0}
}

func (x *BackofficeTransactionServiceAddCreditUserRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *BackofficeTransactionServiceAddCreditUserRequest) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *BackofficeTransactionServiceAddCreditUserRequest) GetCurrency() v1.Currency {
	if x != nil {
		return x.Currency
	}
	return v1.Currency(0)
}

type BackofficeTransactionServiceAddCreditUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTransactionServiceAddCreditUserResponse) Reset() {
	*x = BackofficeTransactionServiceAddCreditUserResponse{}
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTransactionServiceAddCreditUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTransactionServiceAddCreditUserResponse) ProtoMessage() {}

func (x *BackofficeTransactionServiceAddCreditUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTransactionServiceAddCreditUserResponse.ProtoReflect.Descriptor instead.
func (*BackofficeTransactionServiceAddCreditUserResponse) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_backoffice_proto_rawDescGZIP(), []int{1}
}

func (x *BackofficeTransactionServiceAddCreditUserResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeTransactionServiceFetchTransactionRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant           string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	IdUser               string                 `protobuf:"bytes,2,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	IdPaymentGatewayType string                 `protobuf:"bytes,3,opt,name=id_payment_gateway_type,json=idPaymentGatewayType,proto3" json:"id_payment_gateway_type,omitempty"`
	IdPaymentGateway     string                 `protobuf:"bytes,4,opt,name=id_payment_gateway,json=idPaymentGateway,proto3" json:"id_payment_gateway,omitempty"`
	TransactionType      v1.TransactionType     `protobuf:"varint,5,opt,name=transaction_type,json=transactionType,proto3,enum=algoenum.v1.TransactionType" json:"transaction_type,omitempty"`
	TransactionStatus    v1.TransactionStatus   `protobuf:"varint,6,opt,name=transaction_status,json=transactionStatus,proto3,enum=algoenum.v1.TransactionStatus" json:"transaction_status,omitempty"`
	FromUnix             int64                  `protobuf:"varint,10,opt,name=from_unix,json=fromUnix,proto3" json:"from_unix,omitempty"`
	ToUnix               int64                  `protobuf:"varint,11,opt,name=to_unix,json=toUnix,proto3" json:"to_unix,omitempty"`
	Pagination           *v12.PaginationRequest `protobuf:"bytes,12,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *BackofficeTransactionServiceFetchTransactionRequest) Reset() {
	*x = BackofficeTransactionServiceFetchTransactionRequest{}
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTransactionServiceFetchTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTransactionServiceFetchTransactionRequest) ProtoMessage() {}

func (x *BackofficeTransactionServiceFetchTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTransactionServiceFetchTransactionRequest.ProtoReflect.Descriptor instead.
func (*BackofficeTransactionServiceFetchTransactionRequest) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_backoffice_proto_rawDescGZIP(), []int{2}
}

func (x *BackofficeTransactionServiceFetchTransactionRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchTransactionRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchTransactionRequest) GetIdPaymentGatewayType() string {
	if x != nil {
		return x.IdPaymentGatewayType
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchTransactionRequest) GetIdPaymentGateway() string {
	if x != nil {
		return x.IdPaymentGateway
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchTransactionRequest) GetTransactionType() v1.TransactionType {
	if x != nil {
		return x.TransactionType
	}
	return v1.TransactionType(0)
}

func (x *BackofficeTransactionServiceFetchTransactionRequest) GetTransactionStatus() v1.TransactionStatus {
	if x != nil {
		return x.TransactionStatus
	}
	return v1.TransactionStatus(0)
}

func (x *BackofficeTransactionServiceFetchTransactionRequest) GetFromUnix() int64 {
	if x != nil {
		return x.FromUnix
	}
	return 0
}

func (x *BackofficeTransactionServiceFetchTransactionRequest) GetToUnix() int64 {
	if x != nil {
		return x.ToUnix
	}
	return 0
}

func (x *BackofficeTransactionServiceFetchTransactionRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeTransactionServiceFetchTransactionUserBalanceTransaction struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdUserBalance string                 `protobuf:"bytes,1,opt,name=id_user_balance,json=idUserBalance,proto3" json:"id_user_balance,omitempty"`
	BalanceType   v1.BalanceType         `protobuf:"varint,2,opt,name=balance_type,json=balanceType,proto3,enum=algoenum.v1.BalanceType" json:"balance_type,omitempty"`
	Amount        float64                `protobuf:"fixed64,3,opt,name=amount,proto3" json:"amount,omitempty"`
	BalanceBefore float64                `protobuf:"fixed64,4,opt,name=balance_before,json=balanceBefore,proto3" json:"balance_before,omitempty"`
	BalanceAfter  float64                `protobuf:"fixed64,5,opt,name=balance_after,json=balanceAfter,proto3" json:"balance_after,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTransactionServiceFetchTransactionUserBalanceTransaction) Reset() {
	*x = BackofficeTransactionServiceFetchTransactionUserBalanceTransaction{}
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTransactionServiceFetchTransactionUserBalanceTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTransactionServiceFetchTransactionUserBalanceTransaction) ProtoMessage() {}

func (x *BackofficeTransactionServiceFetchTransactionUserBalanceTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTransactionServiceFetchTransactionUserBalanceTransaction.ProtoReflect.Descriptor instead.
func (*BackofficeTransactionServiceFetchTransactionUserBalanceTransaction) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_backoffice_proto_rawDescGZIP(), []int{3}
}

func (x *BackofficeTransactionServiceFetchTransactionUserBalanceTransaction) GetIdUserBalance() string {
	if x != nil {
		return x.IdUserBalance
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchTransactionUserBalanceTransaction) GetBalanceType() v1.BalanceType {
	if x != nil {
		return x.BalanceType
	}
	return v1.BalanceType(0)
}

func (x *BackofficeTransactionServiceFetchTransactionUserBalanceTransaction) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *BackofficeTransactionServiceFetchTransactionUserBalanceTransaction) GetBalanceBefore() float64 {
	if x != nil {
		return x.BalanceBefore
	}
	return 0
}

func (x *BackofficeTransactionServiceFetchTransactionUserBalanceTransaction) GetBalanceAfter() float64 {
	if x != nil {
		return x.BalanceAfter
	}
	return 0
}

type BackofficeTransactionServiceFetchTransactionResponse struct {
	state         protoimpl.MessageState                          `protogen:"open.v1"`
	Transactions  []*BackofficeTransactionServiceFetchTransaction `protobuf:"bytes,1,rep,name=transactions,proto3" json:"transactions,omitempty"`
	Pagination    *v12.PaginationResponse                         `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Error         *v11.ErrorMessage                               `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTransactionServiceFetchTransactionResponse) Reset() {
	*x = BackofficeTransactionServiceFetchTransactionResponse{}
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTransactionServiceFetchTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTransactionServiceFetchTransactionResponse) ProtoMessage() {}

func (x *BackofficeTransactionServiceFetchTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTransactionServiceFetchTransactionResponse.ProtoReflect.Descriptor instead.
func (*BackofficeTransactionServiceFetchTransactionResponse) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_backoffice_proto_rawDescGZIP(), []int{4}
}

func (x *BackofficeTransactionServiceFetchTransactionResponse) GetTransactions() []*BackofficeTransactionServiceFetchTransaction {
	if x != nil {
		return x.Transactions
	}
	return nil
}

func (x *BackofficeTransactionServiceFetchTransactionResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficeTransactionServiceFetchTransactionResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeTransactionServiceFetchTransaction struct {
	state                  protoimpl.MessageState                                                `protogen:"open.v1"`
	IdTransaction          string                                                                `protobuf:"bytes,1,opt,name=id_transaction,json=idTransaction,proto3" json:"id_transaction,omitempty"`
	Merchant               *BackofficeTransactionServiceFetchTransactionMerchant                 `protobuf:"bytes,2,opt,name=merchant,proto3" json:"merchant,omitempty"`
	User                   *BackofficeTransactionServiceFetchTransactionUser                     `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	TransactionType        v1.TransactionType                                                    `protobuf:"varint,4,opt,name=transaction_type,json=transactionType,proto3,enum=algoenum.v1.TransactionType" json:"transaction_type,omitempty"`
	TransactionStatus      v1.TransactionStatus                                                  `protobuf:"varint,5,opt,name=transaction_status,json=transactionStatus,proto3,enum=algoenum.v1.TransactionStatus" json:"transaction_status,omitempty"`
	UserBalanceTransaction []*BackofficeTransactionServiceFetchTransactionUserBalanceTransaction `protobuf:"bytes,6,rep,name=user_balance_transaction,json=userBalanceTransaction,proto3" json:"user_balance_transaction,omitempty"`
	DebitTransaction       *BackofficeTransactionServiceFetchTransactionDebitTransaction         `protobuf:"bytes,7,opt,name=debit_transaction,json=debitTransaction,proto3" json:"debit_transaction,omitempty"`
	CreditTransaction      *BackofficeTransactionServiceFetchTransactionCreditTransaction        `protobuf:"bytes,8,opt,name=credit_transaction,json=creditTransaction,proto3" json:"credit_transaction,omitempty"`
	TotalAmount            float64                                                               `protobuf:"fixed64,9,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	Currency               v1.Currency                                                           `protobuf:"varint,10,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	CreatedAt              int64                                                                 `protobuf:"varint,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *BackofficeTransactionServiceFetchTransaction) Reset() {
	*x = BackofficeTransactionServiceFetchTransaction{}
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTransactionServiceFetchTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTransactionServiceFetchTransaction) ProtoMessage() {}

func (x *BackofficeTransactionServiceFetchTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTransactionServiceFetchTransaction.ProtoReflect.Descriptor instead.
func (*BackofficeTransactionServiceFetchTransaction) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_backoffice_proto_rawDescGZIP(), []int{5}
}

func (x *BackofficeTransactionServiceFetchTransaction) GetIdTransaction() string {
	if x != nil {
		return x.IdTransaction
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchTransaction) GetMerchant() *BackofficeTransactionServiceFetchTransactionMerchant {
	if x != nil {
		return x.Merchant
	}
	return nil
}

func (x *BackofficeTransactionServiceFetchTransaction) GetUser() *BackofficeTransactionServiceFetchTransactionUser {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *BackofficeTransactionServiceFetchTransaction) GetTransactionType() v1.TransactionType {
	if x != nil {
		return x.TransactionType
	}
	return v1.TransactionType(0)
}

func (x *BackofficeTransactionServiceFetchTransaction) GetTransactionStatus() v1.TransactionStatus {
	if x != nil {
		return x.TransactionStatus
	}
	return v1.TransactionStatus(0)
}

func (x *BackofficeTransactionServiceFetchTransaction) GetUserBalanceTransaction() []*BackofficeTransactionServiceFetchTransactionUserBalanceTransaction {
	if x != nil {
		return x.UserBalanceTransaction
	}
	return nil
}

func (x *BackofficeTransactionServiceFetchTransaction) GetDebitTransaction() *BackofficeTransactionServiceFetchTransactionDebitTransaction {
	if x != nil {
		return x.DebitTransaction
	}
	return nil
}

func (x *BackofficeTransactionServiceFetchTransaction) GetCreditTransaction() *BackofficeTransactionServiceFetchTransactionCreditTransaction {
	if x != nil {
		return x.CreditTransaction
	}
	return nil
}

func (x *BackofficeTransactionServiceFetchTransaction) GetTotalAmount() float64 {
	if x != nil {
		return x.TotalAmount
	}
	return 0
}

func (x *BackofficeTransactionServiceFetchTransaction) GetCurrency() v1.Currency {
	if x != nil {
		return x.Currency
	}
	return v1.Currency(0)
}

func (x *BackofficeTransactionServiceFetchTransaction) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

type BackofficeTransactionServiceFetchTransactionMerchant struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant    string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IsActive      bool                   `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTransactionServiceFetchTransactionMerchant) Reset() {
	*x = BackofficeTransactionServiceFetchTransactionMerchant{}
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTransactionServiceFetchTransactionMerchant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTransactionServiceFetchTransactionMerchant) ProtoMessage() {}

func (x *BackofficeTransactionServiceFetchTransactionMerchant) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTransactionServiceFetchTransactionMerchant.ProtoReflect.Descriptor instead.
func (*BackofficeTransactionServiceFetchTransactionMerchant) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_backoffice_proto_rawDescGZIP(), []int{6}
}

func (x *BackofficeTransactionServiceFetchTransactionMerchant) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchTransactionMerchant) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchTransactionMerchant) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficeTransactionServiceFetchTransactionCreditTransaction struct {
	state           protoimpl.MessageState                                                       `protogen:"open.v1"`
	PaymentGateway  *BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway `protobuf:"bytes,1,opt,name=payment_gateway,json=paymentGateway,proto3" json:"payment_gateway,omitempty"`
	Description     string                                                                       `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	HashTransaction string                                                                       `protobuf:"bytes,3,opt,name=hash_transaction,json=hashTransaction,proto3" json:"hash_transaction,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *BackofficeTransactionServiceFetchTransactionCreditTransaction) Reset() {
	*x = BackofficeTransactionServiceFetchTransactionCreditTransaction{}
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTransactionServiceFetchTransactionCreditTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTransactionServiceFetchTransactionCreditTransaction) ProtoMessage() {}

func (x *BackofficeTransactionServiceFetchTransactionCreditTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTransactionServiceFetchTransactionCreditTransaction.ProtoReflect.Descriptor instead.
func (*BackofficeTransactionServiceFetchTransactionCreditTransaction) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_backoffice_proto_rawDescGZIP(), []int{7}
}

func (x *BackofficeTransactionServiceFetchTransactionCreditTransaction) GetPaymentGateway() *BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway {
	if x != nil {
		return x.PaymentGateway
	}
	return nil
}

func (x *BackofficeTransactionServiceFetchTransactionCreditTransaction) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchTransactionCreditTransaction) GetHashTransaction() string {
	if x != nil {
		return x.HashTransaction
	}
	return ""
}

type BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGateway   string                 `protobuf:"bytes,1,opt,name=id_payment_gateway,json=idPaymentGateway,proto3" json:"id_payment_gateway,omitempty"`
	PaymentGatewayType v1.PaymentGatewayType  `protobuf:"varint,2,opt,name=payment_gateway_type,json=paymentGatewayType,proto3,enum=algoenum.v1.PaymentGatewayType" json:"payment_gateway_type,omitempty"`
	AccountHolderName  string                 `protobuf:"bytes,3,opt,name=account_holder_name,json=accountHolderName,proto3" json:"account_holder_name,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway) Reset() {
	*x = BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway{}
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway) ProtoMessage() {}

func (x *BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway.ProtoReflect.Descriptor instead.
func (*BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_backoffice_proto_rawDescGZIP(), []int{8}
}

func (x *BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway) GetIdPaymentGateway() string {
	if x != nil {
		return x.IdPaymentGateway
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway) GetPaymentGatewayType() v1.PaymentGatewayType {
	if x != nil {
		return x.PaymentGatewayType
	}
	return v1.PaymentGatewayType(0)
}

func (x *BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway) GetAccountHolderName() string {
	if x != nil {
		return x.AccountHolderName
	}
	return ""
}

type BackofficeTransactionServiceFetchTransactionDebitTransaction struct {
	state         protoimpl.MessageState                                                        `protogen:"open.v1"`
	Plan          *BackofficeTransactionServiceFetchTransactionDebitTransactionPlan             `protobuf:"bytes,1,opt,name=plan,proto3" json:"plan,omitempty"`
	Subscription  *BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscription `protobuf:"bytes,2,opt,name=subscription,proto3" json:"subscription,omitempty"`
	OrderType     v1.OrderType                                                                  `protobuf:"varint,3,opt,name=order_type,json=orderType,proto3,enum=algoenum.v1.OrderType" json:"order_type,omitempty"`
	TotalCost     float64                                                                       `protobuf:"fixed64,4,opt,name=total_cost,json=totalCost,proto3" json:"total_cost,omitempty"`
	TotalPurchase float64                                                                       `protobuf:"fixed64,5,opt,name=total_purchase,json=totalPurchase,proto3" json:"total_purchase,omitempty"`
	TotalDiscount float64                                                                       `protobuf:"fixed64,6,opt,name=total_discount,json=totalDiscount,proto3" json:"total_discount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransaction) Reset() {
	*x = BackofficeTransactionServiceFetchTransactionDebitTransaction{}
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTransactionServiceFetchTransactionDebitTransaction) ProtoMessage() {}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTransactionServiceFetchTransactionDebitTransaction.ProtoReflect.Descriptor instead.
func (*BackofficeTransactionServiceFetchTransactionDebitTransaction) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_backoffice_proto_rawDescGZIP(), []int{9}
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransaction) GetPlan() *BackofficeTransactionServiceFetchTransactionDebitTransactionPlan {
	if x != nil {
		return x.Plan
	}
	return nil
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransaction) GetSubscription() *BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscription {
	if x != nil {
		return x.Subscription
	}
	return nil
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransaction) GetOrderType() v1.OrderType {
	if x != nil {
		return x.OrderType
	}
	return v1.OrderType(0)
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransaction) GetTotalCost() float64 {
	if x != nil {
		return x.TotalCost
	}
	return 0
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransaction) GetTotalPurchase() float64 {
	if x != nil {
		return x.TotalPurchase
	}
	return 0
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransaction) GetTotalDiscount() float64 {
	if x != nil {
		return x.TotalDiscount
	}
	return 0
}

type BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscription struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdSubscription string                 `protobuf:"bytes,1,opt,name=id_subscription,json=idSubscription,proto3" json:"id_subscription,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscription) Reset() {
	*x = BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscription{}
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscription) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscription) ProtoMessage() {}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscription) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscription.ProtoReflect.Descriptor instead.
func (*BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscription) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_backoffice_proto_rawDescGZIP(), []int{10}
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscription) GetIdSubscription() string {
	if x != nil {
		return x.IdSubscription
	}
	return ""
}

type BackofficeTransactionServiceFetchTransactionDebitTransactionPlan struct {
	state         protoimpl.MessageState                                                 `protogen:"open.v1"`
	IdPlan        string                                                                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	Name          string                                                                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	PlanPrice     *BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice `protobuf:"bytes,3,opt,name=plan_price,json=planPrice,proto3" json:"plan_price,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransactionPlan) Reset() {
	*x = BackofficeTransactionServiceFetchTransactionDebitTransactionPlan{}
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransactionPlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTransactionServiceFetchTransactionDebitTransactionPlan) ProtoMessage() {}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransactionPlan) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTransactionServiceFetchTransactionDebitTransactionPlan.ProtoReflect.Descriptor instead.
func (*BackofficeTransactionServiceFetchTransactionDebitTransactionPlan) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_backoffice_proto_rawDescGZIP(), []int{11}
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransactionPlan) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransactionPlan) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransactionPlan) GetPlanPrice() *BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice {
	if x != nil {
		return x.PlanPrice
	}
	return nil
}

type BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	IdPlanPrice       string                 `protobuf:"bytes,1,opt,name=id_plan_price,json=idPlanPrice,proto3" json:"id_plan_price,omitempty"`
	CostPrice         float64                `protobuf:"fixed64,2,opt,name=cost_price,json=costPrice,proto3" json:"cost_price,omitempty"`
	PurchasePrice     float64                `protobuf:"fixed64,3,opt,name=purchase_price,json=purchasePrice,proto3" json:"purchase_price,omitempty"`
	DataTransferInGb  float64                `protobuf:"fixed64,4,opt,name=data_transfer_in_gb,json=dataTransferInGb,proto3" json:"data_transfer_in_gb,omitempty"`
	BillingCycleInSec int64                  `protobuf:"varint,5,opt,name=billing_cycle_in_sec,json=billingCycleInSec,proto3" json:"billing_cycle_in_sec,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice) Reset() {
	*x = BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice{}
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice) ProtoMessage() {}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice.ProtoReflect.Descriptor instead.
func (*BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_backoffice_proto_rawDescGZIP(), []int{12}
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice) GetIdPlanPrice() string {
	if x != nil {
		return x.IdPlanPrice
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice) GetCostPrice() float64 {
	if x != nil {
		return x.CostPrice
	}
	return 0
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice) GetPurchasePrice() float64 {
	if x != nil {
		return x.PurchasePrice
	}
	return 0
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice) GetDataTransferInGb() float64 {
	if x != nil {
		return x.DataTransferInGb
	}
	return 0
}

func (x *BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice) GetBillingCycleInSec() int64 {
	if x != nil {
		return x.BillingCycleInSec
	}
	return 0
}

type BackofficeTransactionServiceFetchTransactionUser struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdUser        string                 `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	Email         string                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	FirstName     string                 `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName      string                 `protobuf:"bytes,4,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	IsActive      bool                   `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTransactionServiceFetchTransactionUser) Reset() {
	*x = BackofficeTransactionServiceFetchTransactionUser{}
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTransactionServiceFetchTransactionUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTransactionServiceFetchTransactionUser) ProtoMessage() {}

func (x *BackofficeTransactionServiceFetchTransactionUser) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTransactionServiceFetchTransactionUser.ProtoReflect.Descriptor instead.
func (*BackofficeTransactionServiceFetchTransactionUser) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_backoffice_proto_rawDescGZIP(), []int{13}
}

func (x *BackofficeTransactionServiceFetchTransactionUser) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchTransactionUser) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchTransactionUser) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchTransactionUser) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchTransactionUser) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficeTransactionServiceFetchUserBalanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant    string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	IdUser        string                 `protobuf:"bytes,2,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	BalanceType   v1.BalanceType         `protobuf:"varint,3,opt,name=balance_type,json=balanceType,proto3,enum=algoenum.v1.BalanceType" json:"balance_type,omitempty"`
	Pagination    *v12.PaginationRequest `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTransactionServiceFetchUserBalanceRequest) Reset() {
	*x = BackofficeTransactionServiceFetchUserBalanceRequest{}
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTransactionServiceFetchUserBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTransactionServiceFetchUserBalanceRequest) ProtoMessage() {}

func (x *BackofficeTransactionServiceFetchUserBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTransactionServiceFetchUserBalanceRequest.ProtoReflect.Descriptor instead.
func (*BackofficeTransactionServiceFetchUserBalanceRequest) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_backoffice_proto_rawDescGZIP(), []int{14}
}

func (x *BackofficeTransactionServiceFetchUserBalanceRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchUserBalanceRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *BackofficeTransactionServiceFetchUserBalanceRequest) GetBalanceType() v1.BalanceType {
	if x != nil {
		return x.BalanceType
	}
	return v1.BalanceType(0)
}

func (x *BackofficeTransactionServiceFetchUserBalanceRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeTransactionServiceFetchUserBalanceResponse struct {
	state         protoimpl.MessageState                     `protogen:"open.v1"`
	Users         []*BackofficeTransactionServiceUserBalance `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	Error         *v11.ErrorMessage                          `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v12.PaginationResponse                    `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTransactionServiceFetchUserBalanceResponse) Reset() {
	*x = BackofficeTransactionServiceFetchUserBalanceResponse{}
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTransactionServiceFetchUserBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTransactionServiceFetchUserBalanceResponse) ProtoMessage() {}

func (x *BackofficeTransactionServiceFetchUserBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTransactionServiceFetchUserBalanceResponse.ProtoReflect.Descriptor instead.
func (*BackofficeTransactionServiceFetchUserBalanceResponse) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_backoffice_proto_rawDescGZIP(), []int{15}
}

func (x *BackofficeTransactionServiceFetchUserBalanceResponse) GetUsers() []*BackofficeTransactionServiceUserBalance {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *BackofficeTransactionServiceFetchUserBalanceResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeTransactionServiceFetchUserBalanceResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeTransactionServiceUserBalance struct {
	state         protoimpl.MessageState                 `protogen:"open.v1"`
	IdUser        string                                 `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	TotalBalance  float64                                `protobuf:"fixed64,2,opt,name=total_balance,json=totalBalance,proto3" json:"total_balance,omitempty"`
	Balances      []*BackofficeTransactionServiceBalance `protobuf:"bytes,3,rep,name=balances,proto3" json:"balances,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTransactionServiceUserBalance) Reset() {
	*x = BackofficeTransactionServiceUserBalance{}
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTransactionServiceUserBalance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTransactionServiceUserBalance) ProtoMessage() {}

func (x *BackofficeTransactionServiceUserBalance) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTransactionServiceUserBalance.ProtoReflect.Descriptor instead.
func (*BackofficeTransactionServiceUserBalance) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_backoffice_proto_rawDescGZIP(), []int{16}
}

func (x *BackofficeTransactionServiceUserBalance) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *BackofficeTransactionServiceUserBalance) GetTotalBalance() float64 {
	if x != nil {
		return x.TotalBalance
	}
	return 0
}

func (x *BackofficeTransactionServiceUserBalance) GetBalances() []*BackofficeTransactionServiceBalance {
	if x != nil {
		return x.Balances
	}
	return nil
}

type BackofficeTransactionServiceBalance struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	IdUserBalance         string                 `protobuf:"bytes,1,opt,name=id_user_balance,json=idUserBalance,proto3" json:"id_user_balance,omitempty"`
	BalanceType           v1.BalanceType         `protobuf:"varint,2,opt,name=balance_type,json=balanceType,proto3,enum=algoenum.v1.BalanceType" json:"balance_type,omitempty"`
	BalanceChargePriority int64                  `protobuf:"varint,3,opt,name=balance_charge_priority,json=balanceChargePriority,proto3" json:"balance_charge_priority,omitempty"`
	CurrentBalance        float64                `protobuf:"fixed64,4,opt,name=current_balance,json=currentBalance,proto3" json:"current_balance,omitempty"`
	Currency              v1.Currency            `protobuf:"varint,5,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	IsActive              bool                   `protobuf:"varint,6,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *BackofficeTransactionServiceBalance) Reset() {
	*x = BackofficeTransactionServiceBalance{}
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTransactionServiceBalance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTransactionServiceBalance) ProtoMessage() {}

func (x *BackofficeTransactionServiceBalance) ProtoReflect() protoreflect.Message {
	mi := &file_billing_transaction_v1_backoffice_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTransactionServiceBalance.ProtoReflect.Descriptor instead.
func (*BackofficeTransactionServiceBalance) Descriptor() ([]byte, []int) {
	return file_billing_transaction_v1_backoffice_proto_rawDescGZIP(), []int{17}
}

func (x *BackofficeTransactionServiceBalance) GetIdUserBalance() string {
	if x != nil {
		return x.IdUserBalance
	}
	return ""
}

func (x *BackofficeTransactionServiceBalance) GetBalanceType() v1.BalanceType {
	if x != nil {
		return x.BalanceType
	}
	return v1.BalanceType(0)
}

func (x *BackofficeTransactionServiceBalance) GetBalanceChargePriority() int64 {
	if x != nil {
		return x.BalanceChargePriority
	}
	return 0
}

func (x *BackofficeTransactionServiceBalance) GetCurrentBalance() float64 {
	if x != nil {
		return x.CurrentBalance
	}
	return 0
}

func (x *BackofficeTransactionServiceBalance) GetCurrency() v1.Currency {
	if x != nil {
		return x.Currency
	}
	return v1.Currency(0)
}

func (x *BackofficeTransactionServiceBalance) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

var File_billing_transaction_v1_backoffice_proto protoreflect.FileDescriptor

const file_billing_transaction_v1_backoffice_proto_rawDesc = "" +
	"\n" +
	"'billing/transaction/v1/backoffice.proto\x12\x16billing.transaction.v1\x1a\x14utils/v1/utils.proto\x1a\x18errmsg/v1/errormsg.proto\x1a\x1aalgoenum/v1/currency.proto\x1a&algoenum/v1/payment_gateway_type.proto\x1a\"algoenum/v1/transaction_type.proto\x1a$algoenum/v1/transaction_status.proto\x1a\x1ealgoenum/v1/balance_type.proto\x1a\x1calgoenum/v1/order_type.proto\"\x96\x01\n" +
	"0BackofficeTransactionServiceAddCreditUserRequest\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x121\n" +
	"\bcurrency\x18\x03 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\"b\n" +
	"1BackofficeTransactionServiceAddCreditUserResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xdf\x03\n" +
	"3BackofficeTransactionServiceFetchTransactionRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12\x17\n" +
	"\aid_user\x18\x02 \x01(\tR\x06idUser\x125\n" +
	"\x17id_payment_gateway_type\x18\x03 \x01(\tR\x14idPaymentGatewayType\x12,\n" +
	"\x12id_payment_gateway\x18\x04 \x01(\tR\x10idPaymentGateway\x12G\n" +
	"\x10transaction_type\x18\x05 \x01(\x0e2\x1c.algoenum.v1.TransactionTypeR\x0ftransactionType\x12M\n" +
	"\x12transaction_status\x18\x06 \x01(\x0e2\x1e.algoenum.v1.TransactionStatusR\x11transactionStatus\x12\x1b\n" +
	"\tfrom_unix\x18\n" +
	" \x01(\x03R\bfromUnix\x12\x17\n" +
	"\ato_unix\x18\v \x01(\x03R\x06toUnix\x12;\n" +
	"\n" +
	"pagination\x18\f \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x8d\x02\n" +
	"BBackofficeTransactionServiceFetchTransactionUserBalanceTransaction\x12&\n" +
	"\x0fid_user_balance\x18\x01 \x01(\tR\ridUserBalance\x12;\n" +
	"\fbalance_type\x18\x02 \x01(\x0e2\x18.algoenum.v1.BalanceTypeR\vbalanceType\x12\x16\n" +
	"\x06amount\x18\x03 \x01(\x01R\x06amount\x12%\n" +
	"\x0ebalance_before\x18\x04 \x01(\x01R\rbalanceBefore\x12#\n" +
	"\rbalance_after\x18\x05 \x01(\x01R\fbalanceAfter\"\x8d\x02\n" +
	"4BackofficeTransactionServiceFetchTransactionResponse\x12h\n" +
	"\ftransactions\x18\x01 \x03(\v2D.billing.transaction.v1.BackofficeTransactionServiceFetchTransactionR\ftransactions\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xcc\a\n" +
	",BackofficeTransactionServiceFetchTransaction\x12%\n" +
	"\x0eid_transaction\x18\x01 \x01(\tR\ridTransaction\x12h\n" +
	"\bmerchant\x18\x02 \x01(\v2L.billing.transaction.v1.BackofficeTransactionServiceFetchTransactionMerchantR\bmerchant\x12\\\n" +
	"\x04user\x18\x03 \x01(\v2H.billing.transaction.v1.BackofficeTransactionServiceFetchTransactionUserR\x04user\x12G\n" +
	"\x10transaction_type\x18\x04 \x01(\x0e2\x1c.algoenum.v1.TransactionTypeR\x0ftransactionType\x12M\n" +
	"\x12transaction_status\x18\x05 \x01(\x0e2\x1e.algoenum.v1.TransactionStatusR\x11transactionStatus\x12\x94\x01\n" +
	"\x18user_balance_transaction\x18\x06 \x03(\v2Z.billing.transaction.v1.BackofficeTransactionServiceFetchTransactionUserBalanceTransactionR\x16userBalanceTransaction\x12\x81\x01\n" +
	"\x11debit_transaction\x18\a \x01(\v2T.billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransactionR\x10debitTransaction\x12\x84\x01\n" +
	"\x12credit_transaction\x18\b \x01(\v2U.billing.transaction.v1.BackofficeTransactionServiceFetchTransactionCreditTransactionR\x11creditTransaction\x12!\n" +
	"\ftotal_amount\x18\t \x01(\x01R\vtotalAmount\x121\n" +
	"\bcurrency\x18\n" +
	" \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\x12\x1d\n" +
	"\n" +
	"created_at\x18\v \x01(\x03R\tcreatedAt\"\x88\x01\n" +
	"4BackofficeTransactionServiceFetchTransactionMerchant\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1b\n" +
	"\tis_active\x18\x03 \x01(\bR\bisActive\"\x9b\x02\n" +
	"=BackofficeTransactionServiceFetchTransactionCreditTransaction\x12\x8c\x01\n" +
	"\x0fpayment_gateway\x18\x01 \x01(\v2c.billing.transaction.v1.BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGatewayR\x0epaymentGateway\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12)\n" +
	"\x10hash_transaction\x18\x03 \x01(\tR\x0fhashTransaction\"\xfe\x01\n" +
	"KBackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway\x12,\n" +
	"\x12id_payment_gateway\x18\x01 \x01(\tR\x10idPaymentGateway\x12Q\n" +
	"\x14payment_gateway_type\x18\x02 \x01(\x0e2\x1f.algoenum.v1.PaymentGatewayTypeR\x12paymentGatewayType\x12.\n" +
	"\x13account_holder_name\x18\x03 \x01(\tR\x11accountHolderName\"\xdb\x03\n" +
	"<BackofficeTransactionServiceFetchTransactionDebitTransaction\x12l\n" +
	"\x04plan\x18\x01 \x01(\v2X.billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransactionPlanR\x04plan\x12\x88\x01\n" +
	"\fsubscription\x18\x02 \x01(\v2d.billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscriptionR\fsubscription\x125\n" +
	"\n" +
	"order_type\x18\x03 \x01(\x0e2\x16.algoenum.v1.OrderTypeR\torderType\x12\x1d\n" +
	"\n" +
	"total_cost\x18\x04 \x01(\x01R\ttotalCost\x12%\n" +
	"\x0etotal_purchase\x18\x05 \x01(\x01R\rtotalPurchase\x12%\n" +
	"\x0etotal_discount\x18\x06 \x01(\x01R\rtotalDiscount\"w\n" +
	"LBackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscription\x12'\n" +
	"\x0fid_subscription\x18\x01 \x01(\tR\x0eidSubscription\"\xed\x01\n" +
	"@BackofficeTransactionServiceFetchTransactionDebitTransactionPlan\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12|\n" +
	"\n" +
	"plan_price\x18\x03 \x01(\v2].billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPriceR\tplanPrice\"\x91\x02\n" +
	"EBackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice\x12\"\n" +
	"\rid_plan_price\x18\x01 \x01(\tR\vidPlanPrice\x12\x1d\n" +
	"\n" +
	"cost_price\x18\x02 \x01(\x01R\tcostPrice\x12%\n" +
	"\x0epurchase_price\x18\x03 \x01(\x01R\rpurchasePrice\x12-\n" +
	"\x13data_transfer_in_gb\x18\x04 \x01(\x01R\x10dataTransferInGb\x12/\n" +
	"\x14billing_cycle_in_sec\x18\x05 \x01(\x03R\x11billingCycleInSec\"\xba\x01\n" +
	"0BackofficeTransactionServiceFetchTransactionUser\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\x12\x1d\n" +
	"\n" +
	"first_name\x18\x03 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x04 \x01(\tR\blastName\x12\x1b\n" +
	"\tis_active\x18\x05 \x01(\bR\bisActive\"\xe9\x01\n" +
	"3BackofficeTransactionServiceFetchUserBalanceRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12\x17\n" +
	"\aid_user\x18\x02 \x01(\tR\x06idUser\x12;\n" +
	"\fbalance_type\x18\x03 \x01(\x0e2\x18.algoenum.v1.BalanceTypeR\vbalanceType\x12;\n" +
	"\n" +
	"pagination\x18\x04 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xfa\x01\n" +
	"4BackofficeTransactionServiceFetchUserBalanceResponse\x12U\n" +
	"\x05users\x18\x01 \x03(\v2?.billing.transaction.v1.BackofficeTransactionServiceUserBalanceR\x05users\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\xc0\x01\n" +
	"'BackofficeTransactionServiceUserBalance\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x12#\n" +
	"\rtotal_balance\x18\x02 \x01(\x01R\ftotalBalance\x12W\n" +
	"\bbalances\x18\x03 \x03(\v2;.billing.transaction.v1.BackofficeTransactionServiceBalanceR\bbalances\"\xbb\x02\n" +
	"#BackofficeTransactionServiceBalance\x12&\n" +
	"\x0fid_user_balance\x18\x01 \x01(\tR\ridUserBalance\x12;\n" +
	"\fbalance_type\x18\x02 \x01(\x0e2\x18.algoenum.v1.BalanceTypeR\vbalanceType\x126\n" +
	"\x17balance_charge_priority\x18\x03 \x01(\x03R\x15balanceChargePriority\x12'\n" +
	"\x0fcurrent_balance\x18\x04 \x01(\x01R\x0ecurrentBalance\x121\n" +
	"\bcurrency\x18\x05 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\x12\x1b\n" +
	"\tis_active\x18\x06 \x01(\bR\bisActive2\xa5\x04\n" +
	"\x1cBackofficeTransactionService\x12\xad\x01\n" +
	"\x10FetchUserBalance\x12K.billing.transaction.v1.BackofficeTransactionServiceFetchUserBalanceRequest\x1aL.billing.transaction.v1.BackofficeTransactionServiceFetchUserBalanceResponse\x12\xad\x01\n" +
	"\x10FetchTransaction\x12K.billing.transaction.v1.BackofficeTransactionServiceFetchTransactionRequest\x1aL.billing.transaction.v1.BackofficeTransactionServiceFetchTransactionResponse\x12\xa4\x01\n" +
	"\rAddCreditUser\x12H.billing.transaction.v1.BackofficeTransactionServiceAddCreditUserRequest\x1aI.billing.transaction.v1.BackofficeTransactionServiceAddCreditUserResponseBUZSgit.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/transaction/v1;transactionv1b\x06proto3"

var (
	file_billing_transaction_v1_backoffice_proto_rawDescOnce sync.Once
	file_billing_transaction_v1_backoffice_proto_rawDescData []byte
)

func file_billing_transaction_v1_backoffice_proto_rawDescGZIP() []byte {
	file_billing_transaction_v1_backoffice_proto_rawDescOnce.Do(func() {
		file_billing_transaction_v1_backoffice_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_billing_transaction_v1_backoffice_proto_rawDesc), len(file_billing_transaction_v1_backoffice_proto_rawDesc)))
	})
	return file_billing_transaction_v1_backoffice_proto_rawDescData
}

var file_billing_transaction_v1_backoffice_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_billing_transaction_v1_backoffice_proto_goTypes = []any{
	(*BackofficeTransactionServiceAddCreditUserRequest)(nil),                             // 0: billing.transaction.v1.BackofficeTransactionServiceAddCreditUserRequest
	(*BackofficeTransactionServiceAddCreditUserResponse)(nil),                            // 1: billing.transaction.v1.BackofficeTransactionServiceAddCreditUserResponse
	(*BackofficeTransactionServiceFetchTransactionRequest)(nil),                          // 2: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionRequest
	(*BackofficeTransactionServiceFetchTransactionUserBalanceTransaction)(nil),           // 3: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionUserBalanceTransaction
	(*BackofficeTransactionServiceFetchTransactionResponse)(nil),                         // 4: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionResponse
	(*BackofficeTransactionServiceFetchTransaction)(nil),                                 // 5: billing.transaction.v1.BackofficeTransactionServiceFetchTransaction
	(*BackofficeTransactionServiceFetchTransactionMerchant)(nil),                         // 6: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionMerchant
	(*BackofficeTransactionServiceFetchTransactionCreditTransaction)(nil),                // 7: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionCreditTransaction
	(*BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway)(nil),  // 8: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway
	(*BackofficeTransactionServiceFetchTransactionDebitTransaction)(nil),                 // 9: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransaction
	(*BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscription)(nil), // 10: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscription
	(*BackofficeTransactionServiceFetchTransactionDebitTransactionPlan)(nil),             // 11: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransactionPlan
	(*BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice)(nil),        // 12: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice
	(*BackofficeTransactionServiceFetchTransactionUser)(nil),                             // 13: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionUser
	(*BackofficeTransactionServiceFetchUserBalanceRequest)(nil),                          // 14: billing.transaction.v1.BackofficeTransactionServiceFetchUserBalanceRequest
	(*BackofficeTransactionServiceFetchUserBalanceResponse)(nil),                         // 15: billing.transaction.v1.BackofficeTransactionServiceFetchUserBalanceResponse
	(*BackofficeTransactionServiceUserBalance)(nil),                                      // 16: billing.transaction.v1.BackofficeTransactionServiceUserBalance
	(*BackofficeTransactionServiceBalance)(nil),                                          // 17: billing.transaction.v1.BackofficeTransactionServiceBalance
	(v1.Currency)(0),               // 18: algoenum.v1.Currency
	(*v11.ErrorMessage)(nil),       // 19: errmsg.v1.ErrorMessage
	(v1.TransactionType)(0),        // 20: algoenum.v1.TransactionType
	(v1.TransactionStatus)(0),      // 21: algoenum.v1.TransactionStatus
	(*v12.PaginationRequest)(nil),  // 22: utils.v1.PaginationRequest
	(v1.BalanceType)(0),            // 23: algoenum.v1.BalanceType
	(*v12.PaginationResponse)(nil), // 24: utils.v1.PaginationResponse
	(v1.PaymentGatewayType)(0),     // 25: algoenum.v1.PaymentGatewayType
	(v1.OrderType)(0),              // 26: algoenum.v1.OrderType
}
var file_billing_transaction_v1_backoffice_proto_depIdxs = []int32{
	18, // 0: billing.transaction.v1.BackofficeTransactionServiceAddCreditUserRequest.currency:type_name -> algoenum.v1.Currency
	19, // 1: billing.transaction.v1.BackofficeTransactionServiceAddCreditUserResponse.error:type_name -> errmsg.v1.ErrorMessage
	20, // 2: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionRequest.transaction_type:type_name -> algoenum.v1.TransactionType
	21, // 3: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionRequest.transaction_status:type_name -> algoenum.v1.TransactionStatus
	22, // 4: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionRequest.pagination:type_name -> utils.v1.PaginationRequest
	23, // 5: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionUserBalanceTransaction.balance_type:type_name -> algoenum.v1.BalanceType
	5,  // 6: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionResponse.transactions:type_name -> billing.transaction.v1.BackofficeTransactionServiceFetchTransaction
	24, // 7: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionResponse.pagination:type_name -> utils.v1.PaginationResponse
	19, // 8: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionResponse.error:type_name -> errmsg.v1.ErrorMessage
	6,  // 9: billing.transaction.v1.BackofficeTransactionServiceFetchTransaction.merchant:type_name -> billing.transaction.v1.BackofficeTransactionServiceFetchTransactionMerchant
	13, // 10: billing.transaction.v1.BackofficeTransactionServiceFetchTransaction.user:type_name -> billing.transaction.v1.BackofficeTransactionServiceFetchTransactionUser
	20, // 11: billing.transaction.v1.BackofficeTransactionServiceFetchTransaction.transaction_type:type_name -> algoenum.v1.TransactionType
	21, // 12: billing.transaction.v1.BackofficeTransactionServiceFetchTransaction.transaction_status:type_name -> algoenum.v1.TransactionStatus
	3,  // 13: billing.transaction.v1.BackofficeTransactionServiceFetchTransaction.user_balance_transaction:type_name -> billing.transaction.v1.BackofficeTransactionServiceFetchTransactionUserBalanceTransaction
	9,  // 14: billing.transaction.v1.BackofficeTransactionServiceFetchTransaction.debit_transaction:type_name -> billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransaction
	7,  // 15: billing.transaction.v1.BackofficeTransactionServiceFetchTransaction.credit_transaction:type_name -> billing.transaction.v1.BackofficeTransactionServiceFetchTransactionCreditTransaction
	18, // 16: billing.transaction.v1.BackofficeTransactionServiceFetchTransaction.currency:type_name -> algoenum.v1.Currency
	8,  // 17: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionCreditTransaction.payment_gateway:type_name -> billing.transaction.v1.BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway
	25, // 18: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway.payment_gateway_type:type_name -> algoenum.v1.PaymentGatewayType
	11, // 19: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransaction.plan:type_name -> billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransactionPlan
	10, // 20: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransaction.subscription:type_name -> billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscription
	26, // 21: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransaction.order_type:type_name -> algoenum.v1.OrderType
	12, // 22: billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransactionPlan.plan_price:type_name -> billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice
	23, // 23: billing.transaction.v1.BackofficeTransactionServiceFetchUserBalanceRequest.balance_type:type_name -> algoenum.v1.BalanceType
	22, // 24: billing.transaction.v1.BackofficeTransactionServiceFetchUserBalanceRequest.pagination:type_name -> utils.v1.PaginationRequest
	16, // 25: billing.transaction.v1.BackofficeTransactionServiceFetchUserBalanceResponse.users:type_name -> billing.transaction.v1.BackofficeTransactionServiceUserBalance
	19, // 26: billing.transaction.v1.BackofficeTransactionServiceFetchUserBalanceResponse.error:type_name -> errmsg.v1.ErrorMessage
	24, // 27: billing.transaction.v1.BackofficeTransactionServiceFetchUserBalanceResponse.pagination:type_name -> utils.v1.PaginationResponse
	17, // 28: billing.transaction.v1.BackofficeTransactionServiceUserBalance.balances:type_name -> billing.transaction.v1.BackofficeTransactionServiceBalance
	23, // 29: billing.transaction.v1.BackofficeTransactionServiceBalance.balance_type:type_name -> algoenum.v1.BalanceType
	18, // 30: billing.transaction.v1.BackofficeTransactionServiceBalance.currency:type_name -> algoenum.v1.Currency
	14, // 31: billing.transaction.v1.BackofficeTransactionService.FetchUserBalance:input_type -> billing.transaction.v1.BackofficeTransactionServiceFetchUserBalanceRequest
	2,  // 32: billing.transaction.v1.BackofficeTransactionService.FetchTransaction:input_type -> billing.transaction.v1.BackofficeTransactionServiceFetchTransactionRequest
	0,  // 33: billing.transaction.v1.BackofficeTransactionService.AddCreditUser:input_type -> billing.transaction.v1.BackofficeTransactionServiceAddCreditUserRequest
	15, // 34: billing.transaction.v1.BackofficeTransactionService.FetchUserBalance:output_type -> billing.transaction.v1.BackofficeTransactionServiceFetchUserBalanceResponse
	4,  // 35: billing.transaction.v1.BackofficeTransactionService.FetchTransaction:output_type -> billing.transaction.v1.BackofficeTransactionServiceFetchTransactionResponse
	1,  // 36: billing.transaction.v1.BackofficeTransactionService.AddCreditUser:output_type -> billing.transaction.v1.BackofficeTransactionServiceAddCreditUserResponse
	34, // [34:37] is the sub-list for method output_type
	31, // [31:34] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_billing_transaction_v1_backoffice_proto_init() }
func file_billing_transaction_v1_backoffice_proto_init() {
	if File_billing_transaction_v1_backoffice_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_billing_transaction_v1_backoffice_proto_rawDesc), len(file_billing_transaction_v1_backoffice_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_billing_transaction_v1_backoffice_proto_goTypes,
		DependencyIndexes: file_billing_transaction_v1_backoffice_proto_depIdxs,
		MessageInfos:      file_billing_transaction_v1_backoffice_proto_msgTypes,
	}.Build()
	File_billing_transaction_v1_backoffice_proto = out.File
	file_billing_transaction_v1_backoffice_proto_goTypes = nil
	file_billing_transaction_v1_backoffice_proto_depIdxs = nil
}
