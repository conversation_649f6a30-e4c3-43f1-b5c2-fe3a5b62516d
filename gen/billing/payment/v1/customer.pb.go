// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: billing/payment/v1/customer.proto

package paymentv1

import (
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CustomerPaymentServiceFetchSePayAmountTopUpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPaymentServiceFetchSePayAmountTopUpRequest) Reset() {
	*x = CustomerPaymentServiceFetchSePayAmountTopUpRequest{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServiceFetchSePayAmountTopUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServiceFetchSePayAmountTopUpRequest) ProtoMessage() {}

func (x *CustomerPaymentServiceFetchSePayAmountTopUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServiceFetchSePayAmountTopUpRequest.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServiceFetchSePayAmountTopUpRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerPaymentServiceFetchSePayAmountTopUpRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerPaymentServiceFetchSePayAmountTopUpResponse struct {
	state             protoimpl.MessageState                    `protogen:"open.v1"`
	Error             *v11.ErrorMessage                         `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination        *v1.PaginationResponse                    `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	SepayAmountTopUps []*CustomerPaymentServiceSepayAmountTopUp `protobuf:"bytes,3,rep,name=sepay_amount_top_ups,json=sepayAmountTopUps,proto3" json:"sepay_amount_top_ups,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CustomerPaymentServiceFetchSePayAmountTopUpResponse) Reset() {
	*x = CustomerPaymentServiceFetchSePayAmountTopUpResponse{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServiceFetchSePayAmountTopUpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServiceFetchSePayAmountTopUpResponse) ProtoMessage() {}

func (x *CustomerPaymentServiceFetchSePayAmountTopUpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServiceFetchSePayAmountTopUpResponse.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServiceFetchSePayAmountTopUpResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerPaymentServiceFetchSePayAmountTopUpResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerPaymentServiceFetchSePayAmountTopUpResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *CustomerPaymentServiceFetchSePayAmountTopUpResponse) GetSepayAmountTopUps() []*CustomerPaymentServiceSepayAmountTopUp {
	if x != nil {
		return x.SepayAmountTopUps
	}
	return nil
}

type CustomerPaymentServiceSepayAmountTopUp struct {
	state                            protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewaySepayAmountTopUp string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_sepay_amount_top_up,json=idPaymentGatewaySepayAmountTopUp,proto3" json:"id_payment_gateway_sepay_amount_top_up,omitempty"`
	Amount                           float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Promotion                        float64                `protobuf:"fixed64,3,opt,name=promotion,proto3" json:"promotion,omitempty"`
	Currency                         v12.Currency           `protobuf:"varint,4,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	unknownFields                    protoimpl.UnknownFields
	sizeCache                        protoimpl.SizeCache
}

func (x *CustomerPaymentServiceSepayAmountTopUp) Reset() {
	*x = CustomerPaymentServiceSepayAmountTopUp{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServiceSepayAmountTopUp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServiceSepayAmountTopUp) ProtoMessage() {}

func (x *CustomerPaymentServiceSepayAmountTopUp) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServiceSepayAmountTopUp.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServiceSepayAmountTopUp) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{2}
}

func (x *CustomerPaymentServiceSepayAmountTopUp) GetIdPaymentGatewaySepayAmountTopUp() string {
	if x != nil {
		return x.IdPaymentGatewaySepayAmountTopUp
	}
	return ""
}

func (x *CustomerPaymentServiceSepayAmountTopUp) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *CustomerPaymentServiceSepayAmountTopUp) GetPromotion() float64 {
	if x != nil {
		return x.Promotion
	}
	return 0
}

func (x *CustomerPaymentServiceSepayAmountTopUp) GetCurrency() v12.Currency {
	if x != nil {
		return x.Currency
	}
	return v12.Currency(0)
}

type CustomerPaymentServiceCreateSePayPaymentRequest struct {
	state                            protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewaySepayAmountTopUp string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_sepay_amount_top_up,json=idPaymentGatewaySepayAmountTopUp,proto3" json:"id_payment_gateway_sepay_amount_top_up,omitempty"`
	unknownFields                    protoimpl.UnknownFields
	sizeCache                        protoimpl.SizeCache
}

func (x *CustomerPaymentServiceCreateSePayPaymentRequest) Reset() {
	*x = CustomerPaymentServiceCreateSePayPaymentRequest{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServiceCreateSePayPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServiceCreateSePayPaymentRequest) ProtoMessage() {}

func (x *CustomerPaymentServiceCreateSePayPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServiceCreateSePayPaymentRequest.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServiceCreateSePayPaymentRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{3}
}

func (x *CustomerPaymentServiceCreateSePayPaymentRequest) GetIdPaymentGatewaySepayAmountTopUp() string {
	if x != nil {
		return x.IdPaymentGatewaySepayAmountTopUp
	}
	return ""
}

type CustomerPaymentServiceCreateSePayPaymentResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Error             *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	ImageQr           string                 `protobuf:"bytes,2,opt,name=image_qr,json=imageQr,proto3" json:"image_qr,omitempty"`
	BankType          v12.VNBankType         `protobuf:"varint,3,opt,name=bank_type,json=bankType,proto3,enum=algoenum.v1.VNBankType" json:"bank_type,omitempty"`
	AccountHolderName string                 `protobuf:"bytes,4,opt,name=account_holder_name,json=accountHolderName,proto3" json:"account_holder_name,omitempty"`
	AccountNumber     string                 `protobuf:"bytes,5,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	PaymentDesc       string                 `protobuf:"bytes,6,opt,name=payment_desc,json=paymentDesc,proto3" json:"payment_desc,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CustomerPaymentServiceCreateSePayPaymentResponse) Reset() {
	*x = CustomerPaymentServiceCreateSePayPaymentResponse{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServiceCreateSePayPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServiceCreateSePayPaymentResponse) ProtoMessage() {}

func (x *CustomerPaymentServiceCreateSePayPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServiceCreateSePayPaymentResponse.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServiceCreateSePayPaymentResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{4}
}

func (x *CustomerPaymentServiceCreateSePayPaymentResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerPaymentServiceCreateSePayPaymentResponse) GetImageQr() string {
	if x != nil {
		return x.ImageQr
	}
	return ""
}

func (x *CustomerPaymentServiceCreateSePayPaymentResponse) GetBankType() v12.VNBankType {
	if x != nil {
		return x.BankType
	}
	return v12.VNBankType(0)
}

func (x *CustomerPaymentServiceCreateSePayPaymentResponse) GetAccountHolderName() string {
	if x != nil {
		return x.AccountHolderName
	}
	return ""
}

func (x *CustomerPaymentServiceCreateSePayPaymentResponse) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *CustomerPaymentServiceCreateSePayPaymentResponse) GetPaymentDesc() string {
	if x != nil {
		return x.PaymentDesc
	}
	return ""
}

type CustomerPaymentServiceCreateAppotaPaymentRequest struct {
	state                             protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewayAppotaAmountTopUp string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_appota_amount_top_up,json=idPaymentGatewayAppotaAmountTopUp,proto3" json:"id_payment_gateway_appota_amount_top_up,omitempty"`
	FirstName                         string                 `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName                          string                 `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	BankCode                          string                 `protobuf:"bytes,4,opt,name=bank_code,json=bankCode,proto3" json:"bank_code,omitempty"`
	unknownFields                     protoimpl.UnknownFields
	sizeCache                         protoimpl.SizeCache
}

func (x *CustomerPaymentServiceCreateAppotaPaymentRequest) Reset() {
	*x = CustomerPaymentServiceCreateAppotaPaymentRequest{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServiceCreateAppotaPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServiceCreateAppotaPaymentRequest) ProtoMessage() {}

func (x *CustomerPaymentServiceCreateAppotaPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServiceCreateAppotaPaymentRequest.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServiceCreateAppotaPaymentRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{5}
}

func (x *CustomerPaymentServiceCreateAppotaPaymentRequest) GetIdPaymentGatewayAppotaAmountTopUp() string {
	if x != nil {
		return x.IdPaymentGatewayAppotaAmountTopUp
	}
	return ""
}

func (x *CustomerPaymentServiceCreateAppotaPaymentRequest) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *CustomerPaymentServiceCreateAppotaPaymentRequest) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *CustomerPaymentServiceCreateAppotaPaymentRequest) GetBankCode() string {
	if x != nil {
		return x.BankCode
	}
	return ""
}

type CustomerPaymentServiceCreateAppotaPaymentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	PaymentLink   string                 `protobuf:"bytes,2,opt,name=payment_link,json=paymentLink,proto3" json:"payment_link,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPaymentServiceCreateAppotaPaymentResponse) Reset() {
	*x = CustomerPaymentServiceCreateAppotaPaymentResponse{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServiceCreateAppotaPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServiceCreateAppotaPaymentResponse) ProtoMessage() {}

func (x *CustomerPaymentServiceCreateAppotaPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServiceCreateAppotaPaymentResponse.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServiceCreateAppotaPaymentResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{6}
}

func (x *CustomerPaymentServiceCreateAppotaPaymentResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerPaymentServiceCreateAppotaPaymentResponse) GetPaymentLink() string {
	if x != nil {
		return x.PaymentLink
	}
	return ""
}

type CustomerPaymentServiceCreateDodoPaymentRequest struct {
	state                           protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewayDodoAmountTopUp string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_dodo_amount_top_up,json=idPaymentGatewayDodoAmountTopUp,proto3" json:"id_payment_gateway_dodo_amount_top_up,omitempty"`
	FirstName                       string                 `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName                        string                 `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	UserCity                        string                 `protobuf:"bytes,4,opt,name=user_city,json=userCity,proto3" json:"user_city,omitempty"`
	UserCountry                     string                 `protobuf:"bytes,5,opt,name=user_country,json=userCountry,proto3" json:"user_country,omitempty"`
	UserState                       string                 `protobuf:"bytes,6,opt,name=user_state,json=userState,proto3" json:"user_state,omitempty"`
	UserPhone                       string                 `protobuf:"bytes,7,opt,name=user_phone,json=userPhone,proto3" json:"user_phone,omitempty"`
	UserStreet                      string                 `protobuf:"bytes,8,opt,name=user_street,json=userStreet,proto3" json:"user_street,omitempty"`
	UserZipcode                     string                 `protobuf:"bytes,9,opt,name=user_zipcode,json=userZipcode,proto3" json:"user_zipcode,omitempty"`
	unknownFields                   protoimpl.UnknownFields
	sizeCache                       protoimpl.SizeCache
}

func (x *CustomerPaymentServiceCreateDodoPaymentRequest) Reset() {
	*x = CustomerPaymentServiceCreateDodoPaymentRequest{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServiceCreateDodoPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServiceCreateDodoPaymentRequest) ProtoMessage() {}

func (x *CustomerPaymentServiceCreateDodoPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServiceCreateDodoPaymentRequest.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServiceCreateDodoPaymentRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{7}
}

func (x *CustomerPaymentServiceCreateDodoPaymentRequest) GetIdPaymentGatewayDodoAmountTopUp() string {
	if x != nil {
		return x.IdPaymentGatewayDodoAmountTopUp
	}
	return ""
}

func (x *CustomerPaymentServiceCreateDodoPaymentRequest) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *CustomerPaymentServiceCreateDodoPaymentRequest) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *CustomerPaymentServiceCreateDodoPaymentRequest) GetUserCity() string {
	if x != nil {
		return x.UserCity
	}
	return ""
}

func (x *CustomerPaymentServiceCreateDodoPaymentRequest) GetUserCountry() string {
	if x != nil {
		return x.UserCountry
	}
	return ""
}

func (x *CustomerPaymentServiceCreateDodoPaymentRequest) GetUserState() string {
	if x != nil {
		return x.UserState
	}
	return ""
}

func (x *CustomerPaymentServiceCreateDodoPaymentRequest) GetUserPhone() string {
	if x != nil {
		return x.UserPhone
	}
	return ""
}

func (x *CustomerPaymentServiceCreateDodoPaymentRequest) GetUserStreet() string {
	if x != nil {
		return x.UserStreet
	}
	return ""
}

func (x *CustomerPaymentServiceCreateDodoPaymentRequest) GetUserZipcode() string {
	if x != nil {
		return x.UserZipcode
	}
	return ""
}

type CustomerPaymentServiceCreateDodoPaymentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	PaymentLink   string                 `protobuf:"bytes,2,opt,name=payment_link,json=paymentLink,proto3" json:"payment_link,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPaymentServiceCreateDodoPaymentResponse) Reset() {
	*x = CustomerPaymentServiceCreateDodoPaymentResponse{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServiceCreateDodoPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServiceCreateDodoPaymentResponse) ProtoMessage() {}

func (x *CustomerPaymentServiceCreateDodoPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServiceCreateDodoPaymentResponse.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServiceCreateDodoPaymentResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{8}
}

func (x *CustomerPaymentServiceCreateDodoPaymentResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerPaymentServiceCreateDodoPaymentResponse) GetPaymentLink() string {
	if x != nil {
		return x.PaymentLink
	}
	return ""
}

type CustomerPaymentServiceFetchDodoAmountTopUpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPaymentServiceFetchDodoAmountTopUpRequest) Reset() {
	*x = CustomerPaymentServiceFetchDodoAmountTopUpRequest{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServiceFetchDodoAmountTopUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServiceFetchDodoAmountTopUpRequest) ProtoMessage() {}

func (x *CustomerPaymentServiceFetchDodoAmountTopUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServiceFetchDodoAmountTopUpRequest.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServiceFetchDodoAmountTopUpRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{9}
}

func (x *CustomerPaymentServiceFetchDodoAmountTopUpRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerPaymentServiceFetchDodoAmountTopUpResponse struct {
	state            protoimpl.MessageState                   `protogen:"open.v1"`
	Error            *v11.ErrorMessage                        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination       *v1.PaginationResponse                   `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	DodoAmountTopUps []*CustomerPaymentServiceDodoAmountTopUp `protobuf:"bytes,3,rep,name=dodo_amount_top_ups,json=dodoAmountTopUps,proto3" json:"dodo_amount_top_ups,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CustomerPaymentServiceFetchDodoAmountTopUpResponse) Reset() {
	*x = CustomerPaymentServiceFetchDodoAmountTopUpResponse{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServiceFetchDodoAmountTopUpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServiceFetchDodoAmountTopUpResponse) ProtoMessage() {}

func (x *CustomerPaymentServiceFetchDodoAmountTopUpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServiceFetchDodoAmountTopUpResponse.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServiceFetchDodoAmountTopUpResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{10}
}

func (x *CustomerPaymentServiceFetchDodoAmountTopUpResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerPaymentServiceFetchDodoAmountTopUpResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *CustomerPaymentServiceFetchDodoAmountTopUpResponse) GetDodoAmountTopUps() []*CustomerPaymentServiceDodoAmountTopUp {
	if x != nil {
		return x.DodoAmountTopUps
	}
	return nil
}

type CustomerPaymentServiceDodoAmountTopUp struct {
	state                           protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewayDodoAmountTopUp string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_dodo_amount_top_up,json=idPaymentGatewayDodoAmountTopUp,proto3" json:"id_payment_gateway_dodo_amount_top_up,omitempty"`
	Amount                          float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Promotion                       float64                `protobuf:"fixed64,3,opt,name=promotion,proto3" json:"promotion,omitempty"`
	Currency                        v12.Currency           `protobuf:"varint,4,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	unknownFields                   protoimpl.UnknownFields
	sizeCache                       protoimpl.SizeCache
}

func (x *CustomerPaymentServiceDodoAmountTopUp) Reset() {
	*x = CustomerPaymentServiceDodoAmountTopUp{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServiceDodoAmountTopUp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServiceDodoAmountTopUp) ProtoMessage() {}

func (x *CustomerPaymentServiceDodoAmountTopUp) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServiceDodoAmountTopUp.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServiceDodoAmountTopUp) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{11}
}

func (x *CustomerPaymentServiceDodoAmountTopUp) GetIdPaymentGatewayDodoAmountTopUp() string {
	if x != nil {
		return x.IdPaymentGatewayDodoAmountTopUp
	}
	return ""
}

func (x *CustomerPaymentServiceDodoAmountTopUp) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *CustomerPaymentServiceDodoAmountTopUp) GetPromotion() float64 {
	if x != nil {
		return x.Promotion
	}
	return 0
}

func (x *CustomerPaymentServiceDodoAmountTopUp) GetCurrency() v12.Currency {
	if x != nil {
		return x.Currency
	}
	return v12.Currency(0)
}

type CustomerPaymentServiceFetchAppotaAmountTopUpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPaymentServiceFetchAppotaAmountTopUpRequest) Reset() {
	*x = CustomerPaymentServiceFetchAppotaAmountTopUpRequest{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServiceFetchAppotaAmountTopUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServiceFetchAppotaAmountTopUpRequest) ProtoMessage() {}

func (x *CustomerPaymentServiceFetchAppotaAmountTopUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServiceFetchAppotaAmountTopUpRequest.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServiceFetchAppotaAmountTopUpRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{12}
}

func (x *CustomerPaymentServiceFetchAppotaAmountTopUpRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerPaymentServiceFetchAppotaAmountTopUpResponse struct {
	state              protoimpl.MessageState                     `protogen:"open.v1"`
	Error              *v11.ErrorMessage                          `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination         *v1.PaginationResponse                     `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	AppotaAmountTopUps []*CustomerPaymentServiceAppotaAmountTopUp `protobuf:"bytes,3,rep,name=appota_amount_top_ups,json=appotaAmountTopUps,proto3" json:"appota_amount_top_ups,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *CustomerPaymentServiceFetchAppotaAmountTopUpResponse) Reset() {
	*x = CustomerPaymentServiceFetchAppotaAmountTopUpResponse{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServiceFetchAppotaAmountTopUpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServiceFetchAppotaAmountTopUpResponse) ProtoMessage() {}

func (x *CustomerPaymentServiceFetchAppotaAmountTopUpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServiceFetchAppotaAmountTopUpResponse.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServiceFetchAppotaAmountTopUpResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{13}
}

func (x *CustomerPaymentServiceFetchAppotaAmountTopUpResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerPaymentServiceFetchAppotaAmountTopUpResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *CustomerPaymentServiceFetchAppotaAmountTopUpResponse) GetAppotaAmountTopUps() []*CustomerPaymentServiceAppotaAmountTopUp {
	if x != nil {
		return x.AppotaAmountTopUps
	}
	return nil
}

type CustomerPaymentServiceAppotaAmountTopUp struct {
	state                             protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewayAppotaAmountTopUp string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_appota_amount_top_up,json=idPaymentGatewayAppotaAmountTopUp,proto3" json:"id_payment_gateway_appota_amount_top_up,omitempty"`
	Amount                            float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Promotion                         float64                `protobuf:"fixed64,3,opt,name=promotion,proto3" json:"promotion,omitempty"`
	Currency                          v12.Currency           `protobuf:"varint,4,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	unknownFields                     protoimpl.UnknownFields
	sizeCache                         protoimpl.SizeCache
}

func (x *CustomerPaymentServiceAppotaAmountTopUp) Reset() {
	*x = CustomerPaymentServiceAppotaAmountTopUp{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServiceAppotaAmountTopUp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServiceAppotaAmountTopUp) ProtoMessage() {}

func (x *CustomerPaymentServiceAppotaAmountTopUp) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServiceAppotaAmountTopUp.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServiceAppotaAmountTopUp) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{14}
}

func (x *CustomerPaymentServiceAppotaAmountTopUp) GetIdPaymentGatewayAppotaAmountTopUp() string {
	if x != nil {
		return x.IdPaymentGatewayAppotaAmountTopUp
	}
	return ""
}

func (x *CustomerPaymentServiceAppotaAmountTopUp) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *CustomerPaymentServiceAppotaAmountTopUp) GetPromotion() float64 {
	if x != nil {
		return x.Promotion
	}
	return 0
}

func (x *CustomerPaymentServiceAppotaAmountTopUp) GetCurrency() v12.Currency {
	if x != nil {
		return x.Currency
	}
	return v12.Currency(0)
}

type CustomerPaymentServiceFetchPaymentGatewayRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPaymentServiceFetchPaymentGatewayRequest) Reset() {
	*x = CustomerPaymentServiceFetchPaymentGatewayRequest{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServiceFetchPaymentGatewayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServiceFetchPaymentGatewayRequest) ProtoMessage() {}

func (x *CustomerPaymentServiceFetchPaymentGatewayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServiceFetchPaymentGatewayRequest.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServiceFetchPaymentGatewayRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{15}
}

type CustomerPaymentServiceFetchPaymentGatewayResponse struct {
	state           protoimpl.MessageState                  `protogen:"open.v1"`
	PaymentGateways []*CustomerPaymentServicePaymentGateway `protobuf:"bytes,1,rep,name=payment_gateways,json=paymentGateways,proto3" json:"payment_gateways,omitempty"`
	Error           *v11.ErrorMessage                       `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Pagination      *v1.PaginationResponse                  `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CustomerPaymentServiceFetchPaymentGatewayResponse) Reset() {
	*x = CustomerPaymentServiceFetchPaymentGatewayResponse{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServiceFetchPaymentGatewayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServiceFetchPaymentGatewayResponse) ProtoMessage() {}

func (x *CustomerPaymentServiceFetchPaymentGatewayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServiceFetchPaymentGatewayResponse.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServiceFetchPaymentGatewayResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{16}
}

func (x *CustomerPaymentServiceFetchPaymentGatewayResponse) GetPaymentGateways() []*CustomerPaymentServicePaymentGateway {
	if x != nil {
		return x.PaymentGateways
	}
	return nil
}

func (x *CustomerPaymentServiceFetchPaymentGatewayResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerPaymentServiceFetchPaymentGatewayResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerPaymentServicePaymentGateway struct {
	state              protoimpl.MessageState                                  `protogen:"open.v1"`
	IdPaymentGateway   string                                                  `protobuf:"bytes,1,opt,name=id_payment_gateway,json=idPaymentGateway,proto3" json:"id_payment_gateway,omitempty"`
	PaymentGatewayType *CustomerPaymentServicePaymentGatewayPaymentGatewayType `protobuf:"bytes,3,opt,name=payment_gateway_type,json=paymentGatewayType,proto3" json:"payment_gateway_type,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *CustomerPaymentServicePaymentGateway) Reset() {
	*x = CustomerPaymentServicePaymentGateway{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServicePaymentGateway) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServicePaymentGateway) ProtoMessage() {}

func (x *CustomerPaymentServicePaymentGateway) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServicePaymentGateway.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServicePaymentGateway) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{17}
}

func (x *CustomerPaymentServicePaymentGateway) GetIdPaymentGateway() string {
	if x != nil {
		return x.IdPaymentGateway
	}
	return ""
}

func (x *CustomerPaymentServicePaymentGateway) GetPaymentGatewayType() *CustomerPaymentServicePaymentGatewayPaymentGatewayType {
	if x != nil {
		return x.PaymentGatewayType
	}
	return nil
}

type CustomerPaymentServicePaymentGatewayPaymentGatewayType struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	PaymentGatewayType v12.PaymentGatewayType `protobuf:"varint,2,opt,name=payment_gateway_type,json=paymentGatewayType,proto3,enum=algoenum.v1.PaymentGatewayType" json:"payment_gateway_type,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *CustomerPaymentServicePaymentGatewayPaymentGatewayType) Reset() {
	*x = CustomerPaymentServicePaymentGatewayPaymentGatewayType{}
	mi := &file_billing_payment_v1_customer_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentServicePaymentGatewayPaymentGatewayType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentServicePaymentGatewayPaymentGatewayType) ProtoMessage() {}

func (x *CustomerPaymentServicePaymentGatewayPaymentGatewayType) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_customer_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentServicePaymentGatewayPaymentGatewayType.ProtoReflect.Descriptor instead.
func (*CustomerPaymentServicePaymentGatewayPaymentGatewayType) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_customer_proto_rawDescGZIP(), []int{18}
}

func (x *CustomerPaymentServicePaymentGatewayPaymentGatewayType) GetPaymentGatewayType() v12.PaymentGatewayType {
	if x != nil {
		return x.PaymentGatewayType
	}
	return v12.PaymentGatewayType(0)
}

var File_billing_payment_v1_customer_proto protoreflect.FileDescriptor

const file_billing_payment_v1_customer_proto_rawDesc = "" +
	"\n" +
	"!billing/payment/v1/customer.proto\x12\x12billing.payment.v1\x1a\x18errmsg/v1/errormsg.proto\x1a&algoenum/v1/payment_gateway_type.proto\x1a\x19algoenum/v1/vn_bank.proto\x1a\x14utils/v1/utils.proto\x1a\x1aalgoenum/v1/currency.proto\"q\n" +
	"2CustomerPaymentServiceFetchSePayAmountTopUpRequest\x12;\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x8f\x02\n" +
	"3CustomerPaymentServiceFetchSePayAmountTopUpResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12k\n" +
	"\x14sepay_amount_top_ups\x18\x03 \x03(\v2:.billing.payment.v1.CustomerPaymentServiceSepayAmountTopUpR\x11sepayAmountTopUps\"\xe3\x01\n" +
	"&CustomerPaymentServiceSepayAmountTopUp\x12P\n" +
	"&id_payment_gateway_sepay_amount_top_up\x18\x01 \x01(\tR idPaymentGatewaySepayAmountTopUp\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12\x1c\n" +
	"\tpromotion\x18\x03 \x01(\x01R\tpromotion\x121\n" +
	"\bcurrency\x18\x04 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\"\x83\x01\n" +
	"/CustomerPaymentServiceCreateSePayPaymentRequest\x12P\n" +
	"&id_payment_gateway_sepay_amount_top_up\x18\x01 \x01(\tR idPaymentGatewaySepayAmountTopUp\"\xac\x02\n" +
	"0CustomerPaymentServiceCreateSePayPaymentResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12\x19\n" +
	"\bimage_qr\x18\x02 \x01(\tR\aimageQr\x124\n" +
	"\tbank_type\x18\x03 \x01(\x0e2\x17.algoenum.v1.VNBankTypeR\bbankType\x12.\n" +
	"\x13account_holder_name\x18\x04 \x01(\tR\x11accountHolderName\x12%\n" +
	"\x0eaccount_number\x18\x05 \x01(\tR\raccountNumber\x12!\n" +
	"\fpayment_desc\x18\x06 \x01(\tR\vpaymentDesc\"\xdf\x01\n" +
	"0CustomerPaymentServiceCreateAppotaPaymentRequest\x12R\n" +
	"'id_payment_gateway_appota_amount_top_up\x18\x01 \x01(\tR!idPaymentGatewayAppotaAmountTopUp\x12\x1d\n" +
	"\n" +
	"first_name\x18\x02 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x03 \x01(\tR\blastName\x12\x1b\n" +
	"\tbank_code\x18\x04 \x01(\tR\bbankCode\"\x85\x01\n" +
	"1CustomerPaymentServiceCreateAppotaPaymentResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12!\n" +
	"\fpayment_link\x18\x02 \x01(\tR\vpaymentLink\"\xfe\x02\n" +
	".CustomerPaymentServiceCreateDodoPaymentRequest\x12N\n" +
	"%id_payment_gateway_dodo_amount_top_up\x18\x01 \x01(\tR\x1fidPaymentGatewayDodoAmountTopUp\x12\x1d\n" +
	"\n" +
	"first_name\x18\x02 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x03 \x01(\tR\blastName\x12\x1b\n" +
	"\tuser_city\x18\x04 \x01(\tR\buserCity\x12!\n" +
	"\fuser_country\x18\x05 \x01(\tR\vuserCountry\x12\x1d\n" +
	"\n" +
	"user_state\x18\x06 \x01(\tR\tuserState\x12\x1d\n" +
	"\n" +
	"user_phone\x18\a \x01(\tR\tuserPhone\x12\x1f\n" +
	"\vuser_street\x18\b \x01(\tR\n" +
	"userStreet\x12!\n" +
	"\fuser_zipcode\x18\t \x01(\tR\vuserZipcode\"\x83\x01\n" +
	"/CustomerPaymentServiceCreateDodoPaymentResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12!\n" +
	"\fpayment_link\x18\x02 \x01(\tR\vpaymentLink\"p\n" +
	"1CustomerPaymentServiceFetchDodoAmountTopUpRequest\x12;\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x8b\x02\n" +
	"2CustomerPaymentServiceFetchDodoAmountTopUpResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12h\n" +
	"\x13dodo_amount_top_ups\x18\x03 \x03(\v29.billing.payment.v1.CustomerPaymentServiceDodoAmountTopUpR\x10dodoAmountTopUps\"\xe0\x01\n" +
	"%CustomerPaymentServiceDodoAmountTopUp\x12N\n" +
	"%id_payment_gateway_dodo_amount_top_up\x18\x01 \x01(\tR\x1fidPaymentGatewayDodoAmountTopUp\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12\x1c\n" +
	"\tpromotion\x18\x03 \x01(\x01R\tpromotion\x121\n" +
	"\bcurrency\x18\x04 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\"r\n" +
	"3CustomerPaymentServiceFetchAppotaAmountTopUpRequest\x12;\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x93\x02\n" +
	"4CustomerPaymentServiceFetchAppotaAmountTopUpResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12n\n" +
	"\x15appota_amount_top_ups\x18\x03 \x03(\v2;.billing.payment.v1.CustomerPaymentServiceAppotaAmountTopUpR\x12appotaAmountTopUps\"\xe6\x01\n" +
	"'CustomerPaymentServiceAppotaAmountTopUp\x12R\n" +
	"'id_payment_gateway_appota_amount_top_up\x18\x01 \x01(\tR!idPaymentGatewayAppotaAmountTopUp\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12\x1c\n" +
	"\tpromotion\x18\x03 \x01(\x01R\tpromotion\x121\n" +
	"\bcurrency\x18\x04 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\"2\n" +
	"0CustomerPaymentServiceFetchPaymentGatewayRequest\"\x85\x02\n" +
	"1CustomerPaymentServiceFetchPaymentGatewayResponse\x12c\n" +
	"\x10payment_gateways\x18\x01 \x03(\v28.billing.payment.v1.CustomerPaymentServicePaymentGatewayR\x0fpaymentGateways\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\xd2\x01\n" +
	"$CustomerPaymentServicePaymentGateway\x12,\n" +
	"\x12id_payment_gateway\x18\x01 \x01(\tR\x10idPaymentGateway\x12|\n" +
	"\x14payment_gateway_type\x18\x03 \x01(\v2J.billing.payment.v1.CustomerPaymentServicePaymentGatewayPaymentGatewayTypeR\x12paymentGatewayType\"\x8b\x01\n" +
	"6CustomerPaymentServicePaymentGatewayPaymentGatewayType\x12Q\n" +
	"\x14payment_gateway_type\x18\x02 \x01(\x0e2\x1f.algoenum.v1.PaymentGatewayTypeR\x12paymentGatewayType2\xa4\t\n" +
	"\x16CustomerPaymentService\x12\xa2\x01\n" +
	"\x13FetchPaymentGateway\x12D.billing.payment.v1.CustomerPaymentServiceFetchPaymentGatewayRequest\x1aE.billing.payment.v1.CustomerPaymentServiceFetchPaymentGatewayResponse\x12\xab\x01\n" +
	"\x16FetchAppotaAmountTopUp\x12G.billing.payment.v1.CustomerPaymentServiceFetchAppotaAmountTopUpRequest\x1aH.billing.payment.v1.CustomerPaymentServiceFetchAppotaAmountTopUpResponse\x12\xa2\x01\n" +
	"\x13CreateAppotaPayment\x12D.billing.payment.v1.CustomerPaymentServiceCreateAppotaPaymentRequest\x1aE.billing.payment.v1.CustomerPaymentServiceCreateAppotaPaymentResponse\x12\xa5\x01\n" +
	"\x14FetchDodoAmountTopUp\x12E.billing.payment.v1.CustomerPaymentServiceFetchDodoAmountTopUpRequest\x1aF.billing.payment.v1.CustomerPaymentServiceFetchDodoAmountTopUpResponse\x12\x9c\x01\n" +
	"\x11CreateDodoPayment\x12B.billing.payment.v1.CustomerPaymentServiceCreateDodoPaymentRequest\x1aC.billing.payment.v1.CustomerPaymentServiceCreateDodoPaymentResponse\x12\xa8\x01\n" +
	"\x15FetchSePayAmountTopUp\x12F.billing.payment.v1.CustomerPaymentServiceFetchSePayAmountTopUpRequest\x1aG.billing.payment.v1.CustomerPaymentServiceFetchSePayAmountTopUpResponse\x12\x9f\x01\n" +
	"\x12CreateSePayPayment\x12C.billing.payment.v1.CustomerPaymentServiceCreateSePayPaymentRequest\x1aD.billing.payment.v1.CustomerPaymentServiceCreateSePayPaymentResponseBMZKgit.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/payment/v1;paymentv1b\x06proto3"

var (
	file_billing_payment_v1_customer_proto_rawDescOnce sync.Once
	file_billing_payment_v1_customer_proto_rawDescData []byte
)

func file_billing_payment_v1_customer_proto_rawDescGZIP() []byte {
	file_billing_payment_v1_customer_proto_rawDescOnce.Do(func() {
		file_billing_payment_v1_customer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_billing_payment_v1_customer_proto_rawDesc), len(file_billing_payment_v1_customer_proto_rawDesc)))
	})
	return file_billing_payment_v1_customer_proto_rawDescData
}

var file_billing_payment_v1_customer_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_billing_payment_v1_customer_proto_goTypes = []any{
	(*CustomerPaymentServiceFetchSePayAmountTopUpRequest)(nil),     // 0: billing.payment.v1.CustomerPaymentServiceFetchSePayAmountTopUpRequest
	(*CustomerPaymentServiceFetchSePayAmountTopUpResponse)(nil),    // 1: billing.payment.v1.CustomerPaymentServiceFetchSePayAmountTopUpResponse
	(*CustomerPaymentServiceSepayAmountTopUp)(nil),                 // 2: billing.payment.v1.CustomerPaymentServiceSepayAmountTopUp
	(*CustomerPaymentServiceCreateSePayPaymentRequest)(nil),        // 3: billing.payment.v1.CustomerPaymentServiceCreateSePayPaymentRequest
	(*CustomerPaymentServiceCreateSePayPaymentResponse)(nil),       // 4: billing.payment.v1.CustomerPaymentServiceCreateSePayPaymentResponse
	(*CustomerPaymentServiceCreateAppotaPaymentRequest)(nil),       // 5: billing.payment.v1.CustomerPaymentServiceCreateAppotaPaymentRequest
	(*CustomerPaymentServiceCreateAppotaPaymentResponse)(nil),      // 6: billing.payment.v1.CustomerPaymentServiceCreateAppotaPaymentResponse
	(*CustomerPaymentServiceCreateDodoPaymentRequest)(nil),         // 7: billing.payment.v1.CustomerPaymentServiceCreateDodoPaymentRequest
	(*CustomerPaymentServiceCreateDodoPaymentResponse)(nil),        // 8: billing.payment.v1.CustomerPaymentServiceCreateDodoPaymentResponse
	(*CustomerPaymentServiceFetchDodoAmountTopUpRequest)(nil),      // 9: billing.payment.v1.CustomerPaymentServiceFetchDodoAmountTopUpRequest
	(*CustomerPaymentServiceFetchDodoAmountTopUpResponse)(nil),     // 10: billing.payment.v1.CustomerPaymentServiceFetchDodoAmountTopUpResponse
	(*CustomerPaymentServiceDodoAmountTopUp)(nil),                  // 11: billing.payment.v1.CustomerPaymentServiceDodoAmountTopUp
	(*CustomerPaymentServiceFetchAppotaAmountTopUpRequest)(nil),    // 12: billing.payment.v1.CustomerPaymentServiceFetchAppotaAmountTopUpRequest
	(*CustomerPaymentServiceFetchAppotaAmountTopUpResponse)(nil),   // 13: billing.payment.v1.CustomerPaymentServiceFetchAppotaAmountTopUpResponse
	(*CustomerPaymentServiceAppotaAmountTopUp)(nil),                // 14: billing.payment.v1.CustomerPaymentServiceAppotaAmountTopUp
	(*CustomerPaymentServiceFetchPaymentGatewayRequest)(nil),       // 15: billing.payment.v1.CustomerPaymentServiceFetchPaymentGatewayRequest
	(*CustomerPaymentServiceFetchPaymentGatewayResponse)(nil),      // 16: billing.payment.v1.CustomerPaymentServiceFetchPaymentGatewayResponse
	(*CustomerPaymentServicePaymentGateway)(nil),                   // 17: billing.payment.v1.CustomerPaymentServicePaymentGateway
	(*CustomerPaymentServicePaymentGatewayPaymentGatewayType)(nil), // 18: billing.payment.v1.CustomerPaymentServicePaymentGatewayPaymentGatewayType
	(*v1.PaginationRequest)(nil),                                   // 19: utils.v1.PaginationRequest
	(*v11.ErrorMessage)(nil),                                       // 20: errmsg.v1.ErrorMessage
	(*v1.PaginationResponse)(nil),                                  // 21: utils.v1.PaginationResponse
	(v12.Currency)(0),                                              // 22: algoenum.v1.Currency
	(v12.VNBankType)(0),                                            // 23: algoenum.v1.VNBankType
	(v12.PaymentGatewayType)(0),                                    // 24: algoenum.v1.PaymentGatewayType
}
var file_billing_payment_v1_customer_proto_depIdxs = []int32{
	19, // 0: billing.payment.v1.CustomerPaymentServiceFetchSePayAmountTopUpRequest.pagination:type_name -> utils.v1.PaginationRequest
	20, // 1: billing.payment.v1.CustomerPaymentServiceFetchSePayAmountTopUpResponse.error:type_name -> errmsg.v1.ErrorMessage
	21, // 2: billing.payment.v1.CustomerPaymentServiceFetchSePayAmountTopUpResponse.pagination:type_name -> utils.v1.PaginationResponse
	2,  // 3: billing.payment.v1.CustomerPaymentServiceFetchSePayAmountTopUpResponse.sepay_amount_top_ups:type_name -> billing.payment.v1.CustomerPaymentServiceSepayAmountTopUp
	22, // 4: billing.payment.v1.CustomerPaymentServiceSepayAmountTopUp.currency:type_name -> algoenum.v1.Currency
	20, // 5: billing.payment.v1.CustomerPaymentServiceCreateSePayPaymentResponse.error:type_name -> errmsg.v1.ErrorMessage
	23, // 6: billing.payment.v1.CustomerPaymentServiceCreateSePayPaymentResponse.bank_type:type_name -> algoenum.v1.VNBankType
	20, // 7: billing.payment.v1.CustomerPaymentServiceCreateAppotaPaymentResponse.error:type_name -> errmsg.v1.ErrorMessage
	20, // 8: billing.payment.v1.CustomerPaymentServiceCreateDodoPaymentResponse.error:type_name -> errmsg.v1.ErrorMessage
	19, // 9: billing.payment.v1.CustomerPaymentServiceFetchDodoAmountTopUpRequest.pagination:type_name -> utils.v1.PaginationRequest
	20, // 10: billing.payment.v1.CustomerPaymentServiceFetchDodoAmountTopUpResponse.error:type_name -> errmsg.v1.ErrorMessage
	21, // 11: billing.payment.v1.CustomerPaymentServiceFetchDodoAmountTopUpResponse.pagination:type_name -> utils.v1.PaginationResponse
	11, // 12: billing.payment.v1.CustomerPaymentServiceFetchDodoAmountTopUpResponse.dodo_amount_top_ups:type_name -> billing.payment.v1.CustomerPaymentServiceDodoAmountTopUp
	22, // 13: billing.payment.v1.CustomerPaymentServiceDodoAmountTopUp.currency:type_name -> algoenum.v1.Currency
	19, // 14: billing.payment.v1.CustomerPaymentServiceFetchAppotaAmountTopUpRequest.pagination:type_name -> utils.v1.PaginationRequest
	20, // 15: billing.payment.v1.CustomerPaymentServiceFetchAppotaAmountTopUpResponse.error:type_name -> errmsg.v1.ErrorMessage
	21, // 16: billing.payment.v1.CustomerPaymentServiceFetchAppotaAmountTopUpResponse.pagination:type_name -> utils.v1.PaginationResponse
	14, // 17: billing.payment.v1.CustomerPaymentServiceFetchAppotaAmountTopUpResponse.appota_amount_top_ups:type_name -> billing.payment.v1.CustomerPaymentServiceAppotaAmountTopUp
	22, // 18: billing.payment.v1.CustomerPaymentServiceAppotaAmountTopUp.currency:type_name -> algoenum.v1.Currency
	17, // 19: billing.payment.v1.CustomerPaymentServiceFetchPaymentGatewayResponse.payment_gateways:type_name -> billing.payment.v1.CustomerPaymentServicePaymentGateway
	20, // 20: billing.payment.v1.CustomerPaymentServiceFetchPaymentGatewayResponse.error:type_name -> errmsg.v1.ErrorMessage
	21, // 21: billing.payment.v1.CustomerPaymentServiceFetchPaymentGatewayResponse.pagination:type_name -> utils.v1.PaginationResponse
	18, // 22: billing.payment.v1.CustomerPaymentServicePaymentGateway.payment_gateway_type:type_name -> billing.payment.v1.CustomerPaymentServicePaymentGatewayPaymentGatewayType
	24, // 23: billing.payment.v1.CustomerPaymentServicePaymentGatewayPaymentGatewayType.payment_gateway_type:type_name -> algoenum.v1.PaymentGatewayType
	15, // 24: billing.payment.v1.CustomerPaymentService.FetchPaymentGateway:input_type -> billing.payment.v1.CustomerPaymentServiceFetchPaymentGatewayRequest
	12, // 25: billing.payment.v1.CustomerPaymentService.FetchAppotaAmountTopUp:input_type -> billing.payment.v1.CustomerPaymentServiceFetchAppotaAmountTopUpRequest
	5,  // 26: billing.payment.v1.CustomerPaymentService.CreateAppotaPayment:input_type -> billing.payment.v1.CustomerPaymentServiceCreateAppotaPaymentRequest
	9,  // 27: billing.payment.v1.CustomerPaymentService.FetchDodoAmountTopUp:input_type -> billing.payment.v1.CustomerPaymentServiceFetchDodoAmountTopUpRequest
	7,  // 28: billing.payment.v1.CustomerPaymentService.CreateDodoPayment:input_type -> billing.payment.v1.CustomerPaymentServiceCreateDodoPaymentRequest
	0,  // 29: billing.payment.v1.CustomerPaymentService.FetchSePayAmountTopUp:input_type -> billing.payment.v1.CustomerPaymentServiceFetchSePayAmountTopUpRequest
	3,  // 30: billing.payment.v1.CustomerPaymentService.CreateSePayPayment:input_type -> billing.payment.v1.CustomerPaymentServiceCreateSePayPaymentRequest
	16, // 31: billing.payment.v1.CustomerPaymentService.FetchPaymentGateway:output_type -> billing.payment.v1.CustomerPaymentServiceFetchPaymentGatewayResponse
	13, // 32: billing.payment.v1.CustomerPaymentService.FetchAppotaAmountTopUp:output_type -> billing.payment.v1.CustomerPaymentServiceFetchAppotaAmountTopUpResponse
	6,  // 33: billing.payment.v1.CustomerPaymentService.CreateAppotaPayment:output_type -> billing.payment.v1.CustomerPaymentServiceCreateAppotaPaymentResponse
	10, // 34: billing.payment.v1.CustomerPaymentService.FetchDodoAmountTopUp:output_type -> billing.payment.v1.CustomerPaymentServiceFetchDodoAmountTopUpResponse
	8,  // 35: billing.payment.v1.CustomerPaymentService.CreateDodoPayment:output_type -> billing.payment.v1.CustomerPaymentServiceCreateDodoPaymentResponse
	1,  // 36: billing.payment.v1.CustomerPaymentService.FetchSePayAmountTopUp:output_type -> billing.payment.v1.CustomerPaymentServiceFetchSePayAmountTopUpResponse
	4,  // 37: billing.payment.v1.CustomerPaymentService.CreateSePayPayment:output_type -> billing.payment.v1.CustomerPaymentServiceCreateSePayPaymentResponse
	31, // [31:38] is the sub-list for method output_type
	24, // [24:31] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_billing_payment_v1_customer_proto_init() }
func file_billing_payment_v1_customer_proto_init() {
	if File_billing_payment_v1_customer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_billing_payment_v1_customer_proto_rawDesc), len(file_billing_payment_v1_customer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_billing_payment_v1_customer_proto_goTypes,
		DependencyIndexes: file_billing_payment_v1_customer_proto_depIdxs,
		MessageInfos:      file_billing_payment_v1_customer_proto_msgTypes,
	}.Build()
	File_billing_payment_v1_customer_proto = out.File
	file_billing_payment_v1_customer_proto_goTypes = nil
	file_billing_payment_v1_customer_proto_depIdxs = nil
}
