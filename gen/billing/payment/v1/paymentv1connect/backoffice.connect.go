// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: billing/payment/v1/backoffice.proto

package paymentv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/payment/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackofficePaymentServiceName is the fully-qualified name of the BackofficePaymentService service.
	BackofficePaymentServiceName = "billing.payment.v1.BackofficePaymentService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackofficePaymentServiceFetchPaymentGatewayTypeProcedure is the fully-qualified name of the
	// BackofficePaymentService's FetchPaymentGatewayType RPC.
	BackofficePaymentServiceFetchPaymentGatewayTypeProcedure = "/billing.payment.v1.BackofficePaymentService/FetchPaymentGatewayType"
	// BackofficePaymentServiceUpdatePaymentGatewayTypeProcedure is the fully-qualified name of the
	// BackofficePaymentService's UpdatePaymentGatewayType RPC.
	BackofficePaymentServiceUpdatePaymentGatewayTypeProcedure = "/billing.payment.v1.BackofficePaymentService/UpdatePaymentGatewayType"
	// BackofficePaymentServiceFetchPaymentGatewayProcedure is the fully-qualified name of the
	// BackofficePaymentService's FetchPaymentGateway RPC.
	BackofficePaymentServiceFetchPaymentGatewayProcedure = "/billing.payment.v1.BackofficePaymentService/FetchPaymentGateway"
	// BackofficePaymentServiceUpdatePaymentGatewayStateProcedure is the fully-qualified name of the
	// BackofficePaymentService's UpdatePaymentGatewayState RPC.
	BackofficePaymentServiceUpdatePaymentGatewayStateProcedure = "/billing.payment.v1.BackofficePaymentService/UpdatePaymentGatewayState"
	// BackofficePaymentServiceFetchAppotaPaymentGatewayDetailProcedure is the fully-qualified name of
	// the BackofficePaymentService's FetchAppotaPaymentGatewayDetail RPC.
	BackofficePaymentServiceFetchAppotaPaymentGatewayDetailProcedure = "/billing.payment.v1.BackofficePaymentService/FetchAppotaPaymentGatewayDetail"
	// BackofficePaymentServiceFetchDodoPaymentGatewayDetailProcedure is the fully-qualified name of the
	// BackofficePaymentService's FetchDodoPaymentGatewayDetail RPC.
	BackofficePaymentServiceFetchDodoPaymentGatewayDetailProcedure = "/billing.payment.v1.BackofficePaymentService/FetchDodoPaymentGatewayDetail"
	// BackofficePaymentServiceFetchSePayPaymentGatewayDetailProcedure is the fully-qualified name of
	// the BackofficePaymentService's FetchSePayPaymentGatewayDetail RPC.
	BackofficePaymentServiceFetchSePayPaymentGatewayDetailProcedure = "/billing.payment.v1.BackofficePaymentService/FetchSePayPaymentGatewayDetail"
	// BackofficePaymentServiceCreateAppotaPaymentGatewayProcedure is the fully-qualified name of the
	// BackofficePaymentService's CreateAppotaPaymentGateway RPC.
	BackofficePaymentServiceCreateAppotaPaymentGatewayProcedure = "/billing.payment.v1.BackofficePaymentService/CreateAppotaPaymentGateway"
	// BackofficePaymentServiceCreateDodoPaymentGatewayProcedure is the fully-qualified name of the
	// BackofficePaymentService's CreateDodoPaymentGateway RPC.
	BackofficePaymentServiceCreateDodoPaymentGatewayProcedure = "/billing.payment.v1.BackofficePaymentService/CreateDodoPaymentGateway"
	// BackofficePaymentServiceCreateSePayPaymentGatewayProcedure is the fully-qualified name of the
	// BackofficePaymentService's CreateSePayPaymentGateway RPC.
	BackofficePaymentServiceCreateSePayPaymentGatewayProcedure = "/billing.payment.v1.BackofficePaymentService/CreateSePayPaymentGateway"
	// BackofficePaymentServiceCreateSePayAmountTopUpProcedure is the fully-qualified name of the
	// BackofficePaymentService's CreateSePayAmountTopUp RPC.
	BackofficePaymentServiceCreateSePayAmountTopUpProcedure = "/billing.payment.v1.BackofficePaymentService/CreateSePayAmountTopUp"
	// BackofficePaymentServiceCreateDodoAmountTopUpProcedure is the fully-qualified name of the
	// BackofficePaymentService's CreateDodoAmountTopUp RPC.
	BackofficePaymentServiceCreateDodoAmountTopUpProcedure = "/billing.payment.v1.BackofficePaymentService/CreateDodoAmountTopUp"
	// BackofficePaymentServiceCreateAppotaAmountTopUpProcedure is the fully-qualified name of the
	// BackofficePaymentService's CreateAppotaAmountTopUp RPC.
	BackofficePaymentServiceCreateAppotaAmountTopUpProcedure = "/billing.payment.v1.BackofficePaymentService/CreateAppotaAmountTopUp"
	// BackofficePaymentServiceFetchAppotaAmountTopUpProcedure is the fully-qualified name of the
	// BackofficePaymentService's FetchAppotaAmountTopUp RPC.
	BackofficePaymentServiceFetchAppotaAmountTopUpProcedure = "/billing.payment.v1.BackofficePaymentService/FetchAppotaAmountTopUp"
	// BackofficePaymentServiceFetchDodoAmountTopUpProcedure is the fully-qualified name of the
	// BackofficePaymentService's FetchDodoAmountTopUp RPC.
	BackofficePaymentServiceFetchDodoAmountTopUpProcedure = "/billing.payment.v1.BackofficePaymentService/FetchDodoAmountTopUp"
	// BackofficePaymentServiceFetchSePayAmountTopUpProcedure is the fully-qualified name of the
	// BackofficePaymentService's FetchSePayAmountTopUp RPC.
	BackofficePaymentServiceFetchSePayAmountTopUpProcedure = "/billing.payment.v1.BackofficePaymentService/FetchSePayAmountTopUp"
)

// BackofficePaymentServiceClient is a client for the billing.payment.v1.BackofficePaymentService
// service.
type BackofficePaymentServiceClient interface {
	FetchPaymentGatewayType(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchPaymentGatewayTypeRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchPaymentGatewayTypeResponse], error)
	UpdatePaymentGatewayType(context.Context, *connect.Request[v1.BackofficePaymentServiceUpdatePaymentGatewayTypeRequest]) (*connect.Response[v1.BackofficePaymentServiceUpdatePaymentGatewayTypeResponse], error)
	FetchPaymentGateway(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchPaymentGatewayRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchPaymentGatewayResponse], error)
	UpdatePaymentGatewayState(context.Context, *connect.Request[v1.BackofficePaymentServiceUpdatePaymentGatewayStateRequest]) (*connect.Response[v1.BackofficePaymentServiceUpdatePaymentGatewayStateResponse], error)
	FetchAppotaPaymentGatewayDetail(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse], error)
	FetchDodoPaymentGatewayDetail(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse], error)
	FetchSePayPaymentGatewayDetail(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse], error)
	CreateAppotaPaymentGateway(context.Context, *connect.Request[v1.BackofficePaymentServiceCreateAppotaPaymentGatewayRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateAppotaPaymentGatewayResponse], error)
	CreateDodoPaymentGateway(context.Context, *connect.Request[v1.BackofficePaymentServiceCreateDodoPaymentGatewayRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateDodoPaymentGatewayResponse], error)
	CreateSePayPaymentGateway(context.Context, *connect.Request[v1.BackofficePaymentServiceCreateSePayPaymentGatewayRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateSePayPaymentGatewayResponse], error)
	CreateSePayAmountTopUp(context.Context, *connect.Request[v1.BackofficePaymentServiceCreateSePayAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateSePayAmountTopUpResponse], error)
	CreateDodoAmountTopUp(context.Context, *connect.Request[v1.BackofficePaymentServiceCreateDodoAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateDodoAmountTopUpResponse], error)
	CreateAppotaAmountTopUp(context.Context, *connect.Request[v1.BackofficePaymentServiceCreateAppotaAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateAppotaAmountTopUpResponse], error)
	FetchAppotaAmountTopUp(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchAppotaAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchAppotaAmountTopUpResponse], error)
	FetchDodoAmountTopUp(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchDodoAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchDodoAmountTopUpResponse], error)
	FetchSePayAmountTopUp(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchSePayAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchSePayAmountTopUpResponse], error)
}

// NewBackofficePaymentServiceClient constructs a client for the
// billing.payment.v1.BackofficePaymentService service. By default, it uses the Connect protocol
// with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed requests. To
// use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or connect.WithGRPCWeb()
// options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackofficePaymentServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackofficePaymentServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backofficePaymentServiceMethods := v1.File_billing_payment_v1_backoffice_proto.Services().ByName("BackofficePaymentService").Methods()
	return &backofficePaymentServiceClient{
		fetchPaymentGatewayType: connect.NewClient[v1.BackofficePaymentServiceFetchPaymentGatewayTypeRequest, v1.BackofficePaymentServiceFetchPaymentGatewayTypeResponse](
			httpClient,
			baseURL+BackofficePaymentServiceFetchPaymentGatewayTypeProcedure,
			connect.WithSchema(backofficePaymentServiceMethods.ByName("FetchPaymentGatewayType")),
			connect.WithClientOptions(opts...),
		),
		updatePaymentGatewayType: connect.NewClient[v1.BackofficePaymentServiceUpdatePaymentGatewayTypeRequest, v1.BackofficePaymentServiceUpdatePaymentGatewayTypeResponse](
			httpClient,
			baseURL+BackofficePaymentServiceUpdatePaymentGatewayTypeProcedure,
			connect.WithSchema(backofficePaymentServiceMethods.ByName("UpdatePaymentGatewayType")),
			connect.WithClientOptions(opts...),
		),
		fetchPaymentGateway: connect.NewClient[v1.BackofficePaymentServiceFetchPaymentGatewayRequest, v1.BackofficePaymentServiceFetchPaymentGatewayResponse](
			httpClient,
			baseURL+BackofficePaymentServiceFetchPaymentGatewayProcedure,
			connect.WithSchema(backofficePaymentServiceMethods.ByName("FetchPaymentGateway")),
			connect.WithClientOptions(opts...),
		),
		updatePaymentGatewayState: connect.NewClient[v1.BackofficePaymentServiceUpdatePaymentGatewayStateRequest, v1.BackofficePaymentServiceUpdatePaymentGatewayStateResponse](
			httpClient,
			baseURL+BackofficePaymentServiceUpdatePaymentGatewayStateProcedure,
			connect.WithSchema(backofficePaymentServiceMethods.ByName("UpdatePaymentGatewayState")),
			connect.WithClientOptions(opts...),
		),
		fetchAppotaPaymentGatewayDetail: connect.NewClient[v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest, v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse](
			httpClient,
			baseURL+BackofficePaymentServiceFetchAppotaPaymentGatewayDetailProcedure,
			connect.WithSchema(backofficePaymentServiceMethods.ByName("FetchAppotaPaymentGatewayDetail")),
			connect.WithClientOptions(opts...),
		),
		fetchDodoPaymentGatewayDetail: connect.NewClient[v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest, v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse](
			httpClient,
			baseURL+BackofficePaymentServiceFetchDodoPaymentGatewayDetailProcedure,
			connect.WithSchema(backofficePaymentServiceMethods.ByName("FetchDodoPaymentGatewayDetail")),
			connect.WithClientOptions(opts...),
		),
		fetchSePayPaymentGatewayDetail: connect.NewClient[v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest, v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse](
			httpClient,
			baseURL+BackofficePaymentServiceFetchSePayPaymentGatewayDetailProcedure,
			connect.WithSchema(backofficePaymentServiceMethods.ByName("FetchSePayPaymentGatewayDetail")),
			connect.WithClientOptions(opts...),
		),
		createAppotaPaymentGateway: connect.NewClient[v1.BackofficePaymentServiceCreateAppotaPaymentGatewayRequest, v1.BackofficePaymentServiceCreateAppotaPaymentGatewayResponse](
			httpClient,
			baseURL+BackofficePaymentServiceCreateAppotaPaymentGatewayProcedure,
			connect.WithSchema(backofficePaymentServiceMethods.ByName("CreateAppotaPaymentGateway")),
			connect.WithClientOptions(opts...),
		),
		createDodoPaymentGateway: connect.NewClient[v1.BackofficePaymentServiceCreateDodoPaymentGatewayRequest, v1.BackofficePaymentServiceCreateDodoPaymentGatewayResponse](
			httpClient,
			baseURL+BackofficePaymentServiceCreateDodoPaymentGatewayProcedure,
			connect.WithSchema(backofficePaymentServiceMethods.ByName("CreateDodoPaymentGateway")),
			connect.WithClientOptions(opts...),
		),
		createSePayPaymentGateway: connect.NewClient[v1.BackofficePaymentServiceCreateSePayPaymentGatewayRequest, v1.BackofficePaymentServiceCreateSePayPaymentGatewayResponse](
			httpClient,
			baseURL+BackofficePaymentServiceCreateSePayPaymentGatewayProcedure,
			connect.WithSchema(backofficePaymentServiceMethods.ByName("CreateSePayPaymentGateway")),
			connect.WithClientOptions(opts...),
		),
		createSePayAmountTopUp: connect.NewClient[v1.BackofficePaymentServiceCreateSePayAmountTopUpRequest, v1.BackofficePaymentServiceCreateSePayAmountTopUpResponse](
			httpClient,
			baseURL+BackofficePaymentServiceCreateSePayAmountTopUpProcedure,
			connect.WithSchema(backofficePaymentServiceMethods.ByName("CreateSePayAmountTopUp")),
			connect.WithClientOptions(opts...),
		),
		createDodoAmountTopUp: connect.NewClient[v1.BackofficePaymentServiceCreateDodoAmountTopUpRequest, v1.BackofficePaymentServiceCreateDodoAmountTopUpResponse](
			httpClient,
			baseURL+BackofficePaymentServiceCreateDodoAmountTopUpProcedure,
			connect.WithSchema(backofficePaymentServiceMethods.ByName("CreateDodoAmountTopUp")),
			connect.WithClientOptions(opts...),
		),
		createAppotaAmountTopUp: connect.NewClient[v1.BackofficePaymentServiceCreateAppotaAmountTopUpRequest, v1.BackofficePaymentServiceCreateAppotaAmountTopUpResponse](
			httpClient,
			baseURL+BackofficePaymentServiceCreateAppotaAmountTopUpProcedure,
			connect.WithSchema(backofficePaymentServiceMethods.ByName("CreateAppotaAmountTopUp")),
			connect.WithClientOptions(opts...),
		),
		fetchAppotaAmountTopUp: connect.NewClient[v1.BackofficePaymentServiceFetchAppotaAmountTopUpRequest, v1.BackofficePaymentServiceFetchAppotaAmountTopUpResponse](
			httpClient,
			baseURL+BackofficePaymentServiceFetchAppotaAmountTopUpProcedure,
			connect.WithSchema(backofficePaymentServiceMethods.ByName("FetchAppotaAmountTopUp")),
			connect.WithClientOptions(opts...),
		),
		fetchDodoAmountTopUp: connect.NewClient[v1.BackofficePaymentServiceFetchDodoAmountTopUpRequest, v1.BackofficePaymentServiceFetchDodoAmountTopUpResponse](
			httpClient,
			baseURL+BackofficePaymentServiceFetchDodoAmountTopUpProcedure,
			connect.WithSchema(backofficePaymentServiceMethods.ByName("FetchDodoAmountTopUp")),
			connect.WithClientOptions(opts...),
		),
		fetchSePayAmountTopUp: connect.NewClient[v1.BackofficePaymentServiceFetchSePayAmountTopUpRequest, v1.BackofficePaymentServiceFetchSePayAmountTopUpResponse](
			httpClient,
			baseURL+BackofficePaymentServiceFetchSePayAmountTopUpProcedure,
			connect.WithSchema(backofficePaymentServiceMethods.ByName("FetchSePayAmountTopUp")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backofficePaymentServiceClient implements BackofficePaymentServiceClient.
type backofficePaymentServiceClient struct {
	fetchPaymentGatewayType         *connect.Client[v1.BackofficePaymentServiceFetchPaymentGatewayTypeRequest, v1.BackofficePaymentServiceFetchPaymentGatewayTypeResponse]
	updatePaymentGatewayType        *connect.Client[v1.BackofficePaymentServiceUpdatePaymentGatewayTypeRequest, v1.BackofficePaymentServiceUpdatePaymentGatewayTypeResponse]
	fetchPaymentGateway             *connect.Client[v1.BackofficePaymentServiceFetchPaymentGatewayRequest, v1.BackofficePaymentServiceFetchPaymentGatewayResponse]
	updatePaymentGatewayState       *connect.Client[v1.BackofficePaymentServiceUpdatePaymentGatewayStateRequest, v1.BackofficePaymentServiceUpdatePaymentGatewayStateResponse]
	fetchAppotaPaymentGatewayDetail *connect.Client[v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest, v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse]
	fetchDodoPaymentGatewayDetail   *connect.Client[v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest, v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse]
	fetchSePayPaymentGatewayDetail  *connect.Client[v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest, v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse]
	createAppotaPaymentGateway      *connect.Client[v1.BackofficePaymentServiceCreateAppotaPaymentGatewayRequest, v1.BackofficePaymentServiceCreateAppotaPaymentGatewayResponse]
	createDodoPaymentGateway        *connect.Client[v1.BackofficePaymentServiceCreateDodoPaymentGatewayRequest, v1.BackofficePaymentServiceCreateDodoPaymentGatewayResponse]
	createSePayPaymentGateway       *connect.Client[v1.BackofficePaymentServiceCreateSePayPaymentGatewayRequest, v1.BackofficePaymentServiceCreateSePayPaymentGatewayResponse]
	createSePayAmountTopUp          *connect.Client[v1.BackofficePaymentServiceCreateSePayAmountTopUpRequest, v1.BackofficePaymentServiceCreateSePayAmountTopUpResponse]
	createDodoAmountTopUp           *connect.Client[v1.BackofficePaymentServiceCreateDodoAmountTopUpRequest, v1.BackofficePaymentServiceCreateDodoAmountTopUpResponse]
	createAppotaAmountTopUp         *connect.Client[v1.BackofficePaymentServiceCreateAppotaAmountTopUpRequest, v1.BackofficePaymentServiceCreateAppotaAmountTopUpResponse]
	fetchAppotaAmountTopUp          *connect.Client[v1.BackofficePaymentServiceFetchAppotaAmountTopUpRequest, v1.BackofficePaymentServiceFetchAppotaAmountTopUpResponse]
	fetchDodoAmountTopUp            *connect.Client[v1.BackofficePaymentServiceFetchDodoAmountTopUpRequest, v1.BackofficePaymentServiceFetchDodoAmountTopUpResponse]
	fetchSePayAmountTopUp           *connect.Client[v1.BackofficePaymentServiceFetchSePayAmountTopUpRequest, v1.BackofficePaymentServiceFetchSePayAmountTopUpResponse]
}

// FetchPaymentGatewayType calls
// billing.payment.v1.BackofficePaymentService.FetchPaymentGatewayType.
func (c *backofficePaymentServiceClient) FetchPaymentGatewayType(ctx context.Context, req *connect.Request[v1.BackofficePaymentServiceFetchPaymentGatewayTypeRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchPaymentGatewayTypeResponse], error) {
	return c.fetchPaymentGatewayType.CallUnary(ctx, req)
}

// UpdatePaymentGatewayType calls
// billing.payment.v1.BackofficePaymentService.UpdatePaymentGatewayType.
func (c *backofficePaymentServiceClient) UpdatePaymentGatewayType(ctx context.Context, req *connect.Request[v1.BackofficePaymentServiceUpdatePaymentGatewayTypeRequest]) (*connect.Response[v1.BackofficePaymentServiceUpdatePaymentGatewayTypeResponse], error) {
	return c.updatePaymentGatewayType.CallUnary(ctx, req)
}

// FetchPaymentGateway calls billing.payment.v1.BackofficePaymentService.FetchPaymentGateway.
func (c *backofficePaymentServiceClient) FetchPaymentGateway(ctx context.Context, req *connect.Request[v1.BackofficePaymentServiceFetchPaymentGatewayRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchPaymentGatewayResponse], error) {
	return c.fetchPaymentGateway.CallUnary(ctx, req)
}

// UpdatePaymentGatewayState calls
// billing.payment.v1.BackofficePaymentService.UpdatePaymentGatewayState.
func (c *backofficePaymentServiceClient) UpdatePaymentGatewayState(ctx context.Context, req *connect.Request[v1.BackofficePaymentServiceUpdatePaymentGatewayStateRequest]) (*connect.Response[v1.BackofficePaymentServiceUpdatePaymentGatewayStateResponse], error) {
	return c.updatePaymentGatewayState.CallUnary(ctx, req)
}

// FetchAppotaPaymentGatewayDetail calls
// billing.payment.v1.BackofficePaymentService.FetchAppotaPaymentGatewayDetail.
func (c *backofficePaymentServiceClient) FetchAppotaPaymentGatewayDetail(ctx context.Context, req *connect.Request[v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse], error) {
	return c.fetchAppotaPaymentGatewayDetail.CallUnary(ctx, req)
}

// FetchDodoPaymentGatewayDetail calls
// billing.payment.v1.BackofficePaymentService.FetchDodoPaymentGatewayDetail.
func (c *backofficePaymentServiceClient) FetchDodoPaymentGatewayDetail(ctx context.Context, req *connect.Request[v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse], error) {
	return c.fetchDodoPaymentGatewayDetail.CallUnary(ctx, req)
}

// FetchSePayPaymentGatewayDetail calls
// billing.payment.v1.BackofficePaymentService.FetchSePayPaymentGatewayDetail.
func (c *backofficePaymentServiceClient) FetchSePayPaymentGatewayDetail(ctx context.Context, req *connect.Request[v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse], error) {
	return c.fetchSePayPaymentGatewayDetail.CallUnary(ctx, req)
}

// CreateAppotaPaymentGateway calls
// billing.payment.v1.BackofficePaymentService.CreateAppotaPaymentGateway.
func (c *backofficePaymentServiceClient) CreateAppotaPaymentGateway(ctx context.Context, req *connect.Request[v1.BackofficePaymentServiceCreateAppotaPaymentGatewayRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateAppotaPaymentGatewayResponse], error) {
	return c.createAppotaPaymentGateway.CallUnary(ctx, req)
}

// CreateDodoPaymentGateway calls
// billing.payment.v1.BackofficePaymentService.CreateDodoPaymentGateway.
func (c *backofficePaymentServiceClient) CreateDodoPaymentGateway(ctx context.Context, req *connect.Request[v1.BackofficePaymentServiceCreateDodoPaymentGatewayRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateDodoPaymentGatewayResponse], error) {
	return c.createDodoPaymentGateway.CallUnary(ctx, req)
}

// CreateSePayPaymentGateway calls
// billing.payment.v1.BackofficePaymentService.CreateSePayPaymentGateway.
func (c *backofficePaymentServiceClient) CreateSePayPaymentGateway(ctx context.Context, req *connect.Request[v1.BackofficePaymentServiceCreateSePayPaymentGatewayRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateSePayPaymentGatewayResponse], error) {
	return c.createSePayPaymentGateway.CallUnary(ctx, req)
}

// CreateSePayAmountTopUp calls billing.payment.v1.BackofficePaymentService.CreateSePayAmountTopUp.
func (c *backofficePaymentServiceClient) CreateSePayAmountTopUp(ctx context.Context, req *connect.Request[v1.BackofficePaymentServiceCreateSePayAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateSePayAmountTopUpResponse], error) {
	return c.createSePayAmountTopUp.CallUnary(ctx, req)
}

// CreateDodoAmountTopUp calls billing.payment.v1.BackofficePaymentService.CreateDodoAmountTopUp.
func (c *backofficePaymentServiceClient) CreateDodoAmountTopUp(ctx context.Context, req *connect.Request[v1.BackofficePaymentServiceCreateDodoAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateDodoAmountTopUpResponse], error) {
	return c.createDodoAmountTopUp.CallUnary(ctx, req)
}

// CreateAppotaAmountTopUp calls
// billing.payment.v1.BackofficePaymentService.CreateAppotaAmountTopUp.
func (c *backofficePaymentServiceClient) CreateAppotaAmountTopUp(ctx context.Context, req *connect.Request[v1.BackofficePaymentServiceCreateAppotaAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateAppotaAmountTopUpResponse], error) {
	return c.createAppotaAmountTopUp.CallUnary(ctx, req)
}

// FetchAppotaAmountTopUp calls billing.payment.v1.BackofficePaymentService.FetchAppotaAmountTopUp.
func (c *backofficePaymentServiceClient) FetchAppotaAmountTopUp(ctx context.Context, req *connect.Request[v1.BackofficePaymentServiceFetchAppotaAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchAppotaAmountTopUpResponse], error) {
	return c.fetchAppotaAmountTopUp.CallUnary(ctx, req)
}

// FetchDodoAmountTopUp calls billing.payment.v1.BackofficePaymentService.FetchDodoAmountTopUp.
func (c *backofficePaymentServiceClient) FetchDodoAmountTopUp(ctx context.Context, req *connect.Request[v1.BackofficePaymentServiceFetchDodoAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchDodoAmountTopUpResponse], error) {
	return c.fetchDodoAmountTopUp.CallUnary(ctx, req)
}

// FetchSePayAmountTopUp calls billing.payment.v1.BackofficePaymentService.FetchSePayAmountTopUp.
func (c *backofficePaymentServiceClient) FetchSePayAmountTopUp(ctx context.Context, req *connect.Request[v1.BackofficePaymentServiceFetchSePayAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchSePayAmountTopUpResponse], error) {
	return c.fetchSePayAmountTopUp.CallUnary(ctx, req)
}

// BackofficePaymentServiceHandler is an implementation of the
// billing.payment.v1.BackofficePaymentService service.
type BackofficePaymentServiceHandler interface {
	FetchPaymentGatewayType(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchPaymentGatewayTypeRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchPaymentGatewayTypeResponse], error)
	UpdatePaymentGatewayType(context.Context, *connect.Request[v1.BackofficePaymentServiceUpdatePaymentGatewayTypeRequest]) (*connect.Response[v1.BackofficePaymentServiceUpdatePaymentGatewayTypeResponse], error)
	FetchPaymentGateway(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchPaymentGatewayRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchPaymentGatewayResponse], error)
	UpdatePaymentGatewayState(context.Context, *connect.Request[v1.BackofficePaymentServiceUpdatePaymentGatewayStateRequest]) (*connect.Response[v1.BackofficePaymentServiceUpdatePaymentGatewayStateResponse], error)
	FetchAppotaPaymentGatewayDetail(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse], error)
	FetchDodoPaymentGatewayDetail(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse], error)
	FetchSePayPaymentGatewayDetail(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse], error)
	CreateAppotaPaymentGateway(context.Context, *connect.Request[v1.BackofficePaymentServiceCreateAppotaPaymentGatewayRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateAppotaPaymentGatewayResponse], error)
	CreateDodoPaymentGateway(context.Context, *connect.Request[v1.BackofficePaymentServiceCreateDodoPaymentGatewayRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateDodoPaymentGatewayResponse], error)
	CreateSePayPaymentGateway(context.Context, *connect.Request[v1.BackofficePaymentServiceCreateSePayPaymentGatewayRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateSePayPaymentGatewayResponse], error)
	CreateSePayAmountTopUp(context.Context, *connect.Request[v1.BackofficePaymentServiceCreateSePayAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateSePayAmountTopUpResponse], error)
	CreateDodoAmountTopUp(context.Context, *connect.Request[v1.BackofficePaymentServiceCreateDodoAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateDodoAmountTopUpResponse], error)
	CreateAppotaAmountTopUp(context.Context, *connect.Request[v1.BackofficePaymentServiceCreateAppotaAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateAppotaAmountTopUpResponse], error)
	FetchAppotaAmountTopUp(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchAppotaAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchAppotaAmountTopUpResponse], error)
	FetchDodoAmountTopUp(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchDodoAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchDodoAmountTopUpResponse], error)
	FetchSePayAmountTopUp(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchSePayAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchSePayAmountTopUpResponse], error)
}

// NewBackofficePaymentServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackofficePaymentServiceHandler(svc BackofficePaymentServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backofficePaymentServiceMethods := v1.File_billing_payment_v1_backoffice_proto.Services().ByName("BackofficePaymentService").Methods()
	backofficePaymentServiceFetchPaymentGatewayTypeHandler := connect.NewUnaryHandler(
		BackofficePaymentServiceFetchPaymentGatewayTypeProcedure,
		svc.FetchPaymentGatewayType,
		connect.WithSchema(backofficePaymentServiceMethods.ByName("FetchPaymentGatewayType")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePaymentServiceUpdatePaymentGatewayTypeHandler := connect.NewUnaryHandler(
		BackofficePaymentServiceUpdatePaymentGatewayTypeProcedure,
		svc.UpdatePaymentGatewayType,
		connect.WithSchema(backofficePaymentServiceMethods.ByName("UpdatePaymentGatewayType")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePaymentServiceFetchPaymentGatewayHandler := connect.NewUnaryHandler(
		BackofficePaymentServiceFetchPaymentGatewayProcedure,
		svc.FetchPaymentGateway,
		connect.WithSchema(backofficePaymentServiceMethods.ByName("FetchPaymentGateway")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePaymentServiceUpdatePaymentGatewayStateHandler := connect.NewUnaryHandler(
		BackofficePaymentServiceUpdatePaymentGatewayStateProcedure,
		svc.UpdatePaymentGatewayState,
		connect.WithSchema(backofficePaymentServiceMethods.ByName("UpdatePaymentGatewayState")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePaymentServiceFetchAppotaPaymentGatewayDetailHandler := connect.NewUnaryHandler(
		BackofficePaymentServiceFetchAppotaPaymentGatewayDetailProcedure,
		svc.FetchAppotaPaymentGatewayDetail,
		connect.WithSchema(backofficePaymentServiceMethods.ByName("FetchAppotaPaymentGatewayDetail")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePaymentServiceFetchDodoPaymentGatewayDetailHandler := connect.NewUnaryHandler(
		BackofficePaymentServiceFetchDodoPaymentGatewayDetailProcedure,
		svc.FetchDodoPaymentGatewayDetail,
		connect.WithSchema(backofficePaymentServiceMethods.ByName("FetchDodoPaymentGatewayDetail")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePaymentServiceFetchSePayPaymentGatewayDetailHandler := connect.NewUnaryHandler(
		BackofficePaymentServiceFetchSePayPaymentGatewayDetailProcedure,
		svc.FetchSePayPaymentGatewayDetail,
		connect.WithSchema(backofficePaymentServiceMethods.ByName("FetchSePayPaymentGatewayDetail")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePaymentServiceCreateAppotaPaymentGatewayHandler := connect.NewUnaryHandler(
		BackofficePaymentServiceCreateAppotaPaymentGatewayProcedure,
		svc.CreateAppotaPaymentGateway,
		connect.WithSchema(backofficePaymentServiceMethods.ByName("CreateAppotaPaymentGateway")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePaymentServiceCreateDodoPaymentGatewayHandler := connect.NewUnaryHandler(
		BackofficePaymentServiceCreateDodoPaymentGatewayProcedure,
		svc.CreateDodoPaymentGateway,
		connect.WithSchema(backofficePaymentServiceMethods.ByName("CreateDodoPaymentGateway")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePaymentServiceCreateSePayPaymentGatewayHandler := connect.NewUnaryHandler(
		BackofficePaymentServiceCreateSePayPaymentGatewayProcedure,
		svc.CreateSePayPaymentGateway,
		connect.WithSchema(backofficePaymentServiceMethods.ByName("CreateSePayPaymentGateway")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePaymentServiceCreateSePayAmountTopUpHandler := connect.NewUnaryHandler(
		BackofficePaymentServiceCreateSePayAmountTopUpProcedure,
		svc.CreateSePayAmountTopUp,
		connect.WithSchema(backofficePaymentServiceMethods.ByName("CreateSePayAmountTopUp")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePaymentServiceCreateDodoAmountTopUpHandler := connect.NewUnaryHandler(
		BackofficePaymentServiceCreateDodoAmountTopUpProcedure,
		svc.CreateDodoAmountTopUp,
		connect.WithSchema(backofficePaymentServiceMethods.ByName("CreateDodoAmountTopUp")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePaymentServiceCreateAppotaAmountTopUpHandler := connect.NewUnaryHandler(
		BackofficePaymentServiceCreateAppotaAmountTopUpProcedure,
		svc.CreateAppotaAmountTopUp,
		connect.WithSchema(backofficePaymentServiceMethods.ByName("CreateAppotaAmountTopUp")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePaymentServiceFetchAppotaAmountTopUpHandler := connect.NewUnaryHandler(
		BackofficePaymentServiceFetchAppotaAmountTopUpProcedure,
		svc.FetchAppotaAmountTopUp,
		connect.WithSchema(backofficePaymentServiceMethods.ByName("FetchAppotaAmountTopUp")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePaymentServiceFetchDodoAmountTopUpHandler := connect.NewUnaryHandler(
		BackofficePaymentServiceFetchDodoAmountTopUpProcedure,
		svc.FetchDodoAmountTopUp,
		connect.WithSchema(backofficePaymentServiceMethods.ByName("FetchDodoAmountTopUp")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePaymentServiceFetchSePayAmountTopUpHandler := connect.NewUnaryHandler(
		BackofficePaymentServiceFetchSePayAmountTopUpProcedure,
		svc.FetchSePayAmountTopUp,
		connect.WithSchema(backofficePaymentServiceMethods.ByName("FetchSePayAmountTopUp")),
		connect.WithHandlerOptions(opts...),
	)
	return "/billing.payment.v1.BackofficePaymentService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackofficePaymentServiceFetchPaymentGatewayTypeProcedure:
			backofficePaymentServiceFetchPaymentGatewayTypeHandler.ServeHTTP(w, r)
		case BackofficePaymentServiceUpdatePaymentGatewayTypeProcedure:
			backofficePaymentServiceUpdatePaymentGatewayTypeHandler.ServeHTTP(w, r)
		case BackofficePaymentServiceFetchPaymentGatewayProcedure:
			backofficePaymentServiceFetchPaymentGatewayHandler.ServeHTTP(w, r)
		case BackofficePaymentServiceUpdatePaymentGatewayStateProcedure:
			backofficePaymentServiceUpdatePaymentGatewayStateHandler.ServeHTTP(w, r)
		case BackofficePaymentServiceFetchAppotaPaymentGatewayDetailProcedure:
			backofficePaymentServiceFetchAppotaPaymentGatewayDetailHandler.ServeHTTP(w, r)
		case BackofficePaymentServiceFetchDodoPaymentGatewayDetailProcedure:
			backofficePaymentServiceFetchDodoPaymentGatewayDetailHandler.ServeHTTP(w, r)
		case BackofficePaymentServiceFetchSePayPaymentGatewayDetailProcedure:
			backofficePaymentServiceFetchSePayPaymentGatewayDetailHandler.ServeHTTP(w, r)
		case BackofficePaymentServiceCreateAppotaPaymentGatewayProcedure:
			backofficePaymentServiceCreateAppotaPaymentGatewayHandler.ServeHTTP(w, r)
		case BackofficePaymentServiceCreateDodoPaymentGatewayProcedure:
			backofficePaymentServiceCreateDodoPaymentGatewayHandler.ServeHTTP(w, r)
		case BackofficePaymentServiceCreateSePayPaymentGatewayProcedure:
			backofficePaymentServiceCreateSePayPaymentGatewayHandler.ServeHTTP(w, r)
		case BackofficePaymentServiceCreateSePayAmountTopUpProcedure:
			backofficePaymentServiceCreateSePayAmountTopUpHandler.ServeHTTP(w, r)
		case BackofficePaymentServiceCreateDodoAmountTopUpProcedure:
			backofficePaymentServiceCreateDodoAmountTopUpHandler.ServeHTTP(w, r)
		case BackofficePaymentServiceCreateAppotaAmountTopUpProcedure:
			backofficePaymentServiceCreateAppotaAmountTopUpHandler.ServeHTTP(w, r)
		case BackofficePaymentServiceFetchAppotaAmountTopUpProcedure:
			backofficePaymentServiceFetchAppotaAmountTopUpHandler.ServeHTTP(w, r)
		case BackofficePaymentServiceFetchDodoAmountTopUpProcedure:
			backofficePaymentServiceFetchDodoAmountTopUpHandler.ServeHTTP(w, r)
		case BackofficePaymentServiceFetchSePayAmountTopUpProcedure:
			backofficePaymentServiceFetchSePayAmountTopUpHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackofficePaymentServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackofficePaymentServiceHandler struct{}

func (UnimplementedBackofficePaymentServiceHandler) FetchPaymentGatewayType(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchPaymentGatewayTypeRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchPaymentGatewayTypeResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.BackofficePaymentService.FetchPaymentGatewayType is not implemented"))
}

func (UnimplementedBackofficePaymentServiceHandler) UpdatePaymentGatewayType(context.Context, *connect.Request[v1.BackofficePaymentServiceUpdatePaymentGatewayTypeRequest]) (*connect.Response[v1.BackofficePaymentServiceUpdatePaymentGatewayTypeResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.BackofficePaymentService.UpdatePaymentGatewayType is not implemented"))
}

func (UnimplementedBackofficePaymentServiceHandler) FetchPaymentGateway(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchPaymentGatewayRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchPaymentGatewayResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.BackofficePaymentService.FetchPaymentGateway is not implemented"))
}

func (UnimplementedBackofficePaymentServiceHandler) UpdatePaymentGatewayState(context.Context, *connect.Request[v1.BackofficePaymentServiceUpdatePaymentGatewayStateRequest]) (*connect.Response[v1.BackofficePaymentServiceUpdatePaymentGatewayStateResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.BackofficePaymentService.UpdatePaymentGatewayState is not implemented"))
}

func (UnimplementedBackofficePaymentServiceHandler) FetchAppotaPaymentGatewayDetail(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.BackofficePaymentService.FetchAppotaPaymentGatewayDetail is not implemented"))
}

func (UnimplementedBackofficePaymentServiceHandler) FetchDodoPaymentGatewayDetail(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.BackofficePaymentService.FetchDodoPaymentGatewayDetail is not implemented"))
}

func (UnimplementedBackofficePaymentServiceHandler) FetchSePayPaymentGatewayDetail(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.BackofficePaymentService.FetchSePayPaymentGatewayDetail is not implemented"))
}

func (UnimplementedBackofficePaymentServiceHandler) CreateAppotaPaymentGateway(context.Context, *connect.Request[v1.BackofficePaymentServiceCreateAppotaPaymentGatewayRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateAppotaPaymentGatewayResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.BackofficePaymentService.CreateAppotaPaymentGateway is not implemented"))
}

func (UnimplementedBackofficePaymentServiceHandler) CreateDodoPaymentGateway(context.Context, *connect.Request[v1.BackofficePaymentServiceCreateDodoPaymentGatewayRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateDodoPaymentGatewayResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.BackofficePaymentService.CreateDodoPaymentGateway is not implemented"))
}

func (UnimplementedBackofficePaymentServiceHandler) CreateSePayPaymentGateway(context.Context, *connect.Request[v1.BackofficePaymentServiceCreateSePayPaymentGatewayRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateSePayPaymentGatewayResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.BackofficePaymentService.CreateSePayPaymentGateway is not implemented"))
}

func (UnimplementedBackofficePaymentServiceHandler) CreateSePayAmountTopUp(context.Context, *connect.Request[v1.BackofficePaymentServiceCreateSePayAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateSePayAmountTopUpResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.BackofficePaymentService.CreateSePayAmountTopUp is not implemented"))
}

func (UnimplementedBackofficePaymentServiceHandler) CreateDodoAmountTopUp(context.Context, *connect.Request[v1.BackofficePaymentServiceCreateDodoAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateDodoAmountTopUpResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.BackofficePaymentService.CreateDodoAmountTopUp is not implemented"))
}

func (UnimplementedBackofficePaymentServiceHandler) CreateAppotaAmountTopUp(context.Context, *connect.Request[v1.BackofficePaymentServiceCreateAppotaAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceCreateAppotaAmountTopUpResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.BackofficePaymentService.CreateAppotaAmountTopUp is not implemented"))
}

func (UnimplementedBackofficePaymentServiceHandler) FetchAppotaAmountTopUp(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchAppotaAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchAppotaAmountTopUpResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.BackofficePaymentService.FetchAppotaAmountTopUp is not implemented"))
}

func (UnimplementedBackofficePaymentServiceHandler) FetchDodoAmountTopUp(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchDodoAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchDodoAmountTopUpResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.BackofficePaymentService.FetchDodoAmountTopUp is not implemented"))
}

func (UnimplementedBackofficePaymentServiceHandler) FetchSePayAmountTopUp(context.Context, *connect.Request[v1.BackofficePaymentServiceFetchSePayAmountTopUpRequest]) (*connect.Response[v1.BackofficePaymentServiceFetchSePayAmountTopUpResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.BackofficePaymentService.FetchSePayAmountTopUp is not implemented"))
}
