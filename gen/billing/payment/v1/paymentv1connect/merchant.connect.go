// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: billing/payment/v1/merchant.proto

package paymentv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/payment/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// MerchantPaymentServiceName is the fully-qualified name of the MerchantPaymentService service.
	MerchantPaymentServiceName = "billing.payment.v1.MerchantPaymentService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// MerchantPaymentServiceFetchPaymentGatewayProcedure is the fully-qualified name of the
	// MerchantPaymentService's FetchPaymentGateway RPC.
	MerchantPaymentServiceFetchPaymentGatewayProcedure = "/billing.payment.v1.MerchantPaymentService/FetchPaymentGateway"
	// MerchantPaymentServiceFetchAppotaAmountTopUpProcedure is the fully-qualified name of the
	// MerchantPaymentService's FetchAppotaAmountTopUp RPC.
	MerchantPaymentServiceFetchAppotaAmountTopUpProcedure = "/billing.payment.v1.MerchantPaymentService/FetchAppotaAmountTopUp"
	// MerchantPaymentServiceFetchDodoAmountTopUpProcedure is the fully-qualified name of the
	// MerchantPaymentService's FetchDodoAmountTopUp RPC.
	MerchantPaymentServiceFetchDodoAmountTopUpProcedure = "/billing.payment.v1.MerchantPaymentService/FetchDodoAmountTopUp"
	// MerchantPaymentServiceFetchSePayAmountTopUpProcedure is the fully-qualified name of the
	// MerchantPaymentService's FetchSePayAmountTopUp RPC.
	MerchantPaymentServiceFetchSePayAmountTopUpProcedure = "/billing.payment.v1.MerchantPaymentService/FetchSePayAmountTopUp"
	// MerchantPaymentServiceCreateDodoPaymentProcedure is the fully-qualified name of the
	// MerchantPaymentService's CreateDodoPayment RPC.
	MerchantPaymentServiceCreateDodoPaymentProcedure = "/billing.payment.v1.MerchantPaymentService/CreateDodoPayment"
	// MerchantPaymentServiceCreateSePayPaymentProcedure is the fully-qualified name of the
	// MerchantPaymentService's CreateSePayPayment RPC.
	MerchantPaymentServiceCreateSePayPaymentProcedure = "/billing.payment.v1.MerchantPaymentService/CreateSePayPayment"
)

// MerchantPaymentServiceClient is a client for the billing.payment.v1.MerchantPaymentService
// service.
type MerchantPaymentServiceClient interface {
	FetchPaymentGateway(context.Context, *connect.Request[v1.MerchantPaymentServiceFetchPaymentGatewayRequest]) (*connect.Response[v1.MerchantPaymentServiceFetchPaymentGatewayResponse], error)
	// amount top_up
	FetchAppotaAmountTopUp(context.Context, *connect.Request[v1.MerchantPaymentServiceFetchAppotaAmountTopUpRequest]) (*connect.Response[v1.MerchantPaymentServiceFetchAppotaAmountTopUpResponse], error)
	FetchDodoAmountTopUp(context.Context, *connect.Request[v1.MerchantPaymentServiceFetchDodoAmountTopUpRequest]) (*connect.Response[v1.MerchantPaymentServiceFetchDodoAmountTopUpResponse], error)
	FetchSePayAmountTopUp(context.Context, *connect.Request[v1.MerchantPaymentServiceFetchSePayAmountTopUpRequest]) (*connect.Response[v1.MerchantPaymentServiceFetchSePayAmountTopUpResponse], error)
	CreateDodoPayment(context.Context, *connect.Request[v1.MerchantPaymentServiceCreateDodoPaymentRequest]) (*connect.Response[v1.MerchantPaymentServiceCreateDodoPaymentResponse], error)
	CreateSePayPayment(context.Context, *connect.Request[v1.MerchantPaymentServiceCreateSePayPaymentRequest]) (*connect.Response[v1.MerchantPaymentServiceCreateSePayPaymentResponse], error)
}

// NewMerchantPaymentServiceClient constructs a client for the
// billing.payment.v1.MerchantPaymentService service. By default, it uses the Connect protocol with
// the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed requests. To use
// the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewMerchantPaymentServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) MerchantPaymentServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	merchantPaymentServiceMethods := v1.File_billing_payment_v1_merchant_proto.Services().ByName("MerchantPaymentService").Methods()
	return &merchantPaymentServiceClient{
		fetchPaymentGateway: connect.NewClient[v1.MerchantPaymentServiceFetchPaymentGatewayRequest, v1.MerchantPaymentServiceFetchPaymentGatewayResponse](
			httpClient,
			baseURL+MerchantPaymentServiceFetchPaymentGatewayProcedure,
			connect.WithSchema(merchantPaymentServiceMethods.ByName("FetchPaymentGateway")),
			connect.WithClientOptions(opts...),
		),
		fetchAppotaAmountTopUp: connect.NewClient[v1.MerchantPaymentServiceFetchAppotaAmountTopUpRequest, v1.MerchantPaymentServiceFetchAppotaAmountTopUpResponse](
			httpClient,
			baseURL+MerchantPaymentServiceFetchAppotaAmountTopUpProcedure,
			connect.WithSchema(merchantPaymentServiceMethods.ByName("FetchAppotaAmountTopUp")),
			connect.WithClientOptions(opts...),
		),
		fetchDodoAmountTopUp: connect.NewClient[v1.MerchantPaymentServiceFetchDodoAmountTopUpRequest, v1.MerchantPaymentServiceFetchDodoAmountTopUpResponse](
			httpClient,
			baseURL+MerchantPaymentServiceFetchDodoAmountTopUpProcedure,
			connect.WithSchema(merchantPaymentServiceMethods.ByName("FetchDodoAmountTopUp")),
			connect.WithClientOptions(opts...),
		),
		fetchSePayAmountTopUp: connect.NewClient[v1.MerchantPaymentServiceFetchSePayAmountTopUpRequest, v1.MerchantPaymentServiceFetchSePayAmountTopUpResponse](
			httpClient,
			baseURL+MerchantPaymentServiceFetchSePayAmountTopUpProcedure,
			connect.WithSchema(merchantPaymentServiceMethods.ByName("FetchSePayAmountTopUp")),
			connect.WithClientOptions(opts...),
		),
		createDodoPayment: connect.NewClient[v1.MerchantPaymentServiceCreateDodoPaymentRequest, v1.MerchantPaymentServiceCreateDodoPaymentResponse](
			httpClient,
			baseURL+MerchantPaymentServiceCreateDodoPaymentProcedure,
			connect.WithSchema(merchantPaymentServiceMethods.ByName("CreateDodoPayment")),
			connect.WithClientOptions(opts...),
		),
		createSePayPayment: connect.NewClient[v1.MerchantPaymentServiceCreateSePayPaymentRequest, v1.MerchantPaymentServiceCreateSePayPaymentResponse](
			httpClient,
			baseURL+MerchantPaymentServiceCreateSePayPaymentProcedure,
			connect.WithSchema(merchantPaymentServiceMethods.ByName("CreateSePayPayment")),
			connect.WithClientOptions(opts...),
		),
	}
}

// merchantPaymentServiceClient implements MerchantPaymentServiceClient.
type merchantPaymentServiceClient struct {
	fetchPaymentGateway    *connect.Client[v1.MerchantPaymentServiceFetchPaymentGatewayRequest, v1.MerchantPaymentServiceFetchPaymentGatewayResponse]
	fetchAppotaAmountTopUp *connect.Client[v1.MerchantPaymentServiceFetchAppotaAmountTopUpRequest, v1.MerchantPaymentServiceFetchAppotaAmountTopUpResponse]
	fetchDodoAmountTopUp   *connect.Client[v1.MerchantPaymentServiceFetchDodoAmountTopUpRequest, v1.MerchantPaymentServiceFetchDodoAmountTopUpResponse]
	fetchSePayAmountTopUp  *connect.Client[v1.MerchantPaymentServiceFetchSePayAmountTopUpRequest, v1.MerchantPaymentServiceFetchSePayAmountTopUpResponse]
	createDodoPayment      *connect.Client[v1.MerchantPaymentServiceCreateDodoPaymentRequest, v1.MerchantPaymentServiceCreateDodoPaymentResponse]
	createSePayPayment     *connect.Client[v1.MerchantPaymentServiceCreateSePayPaymentRequest, v1.MerchantPaymentServiceCreateSePayPaymentResponse]
}

// FetchPaymentGateway calls billing.payment.v1.MerchantPaymentService.FetchPaymentGateway.
func (c *merchantPaymentServiceClient) FetchPaymentGateway(ctx context.Context, req *connect.Request[v1.MerchantPaymentServiceFetchPaymentGatewayRequest]) (*connect.Response[v1.MerchantPaymentServiceFetchPaymentGatewayResponse], error) {
	return c.fetchPaymentGateway.CallUnary(ctx, req)
}

// FetchAppotaAmountTopUp calls billing.payment.v1.MerchantPaymentService.FetchAppotaAmountTopUp.
func (c *merchantPaymentServiceClient) FetchAppotaAmountTopUp(ctx context.Context, req *connect.Request[v1.MerchantPaymentServiceFetchAppotaAmountTopUpRequest]) (*connect.Response[v1.MerchantPaymentServiceFetchAppotaAmountTopUpResponse], error) {
	return c.fetchAppotaAmountTopUp.CallUnary(ctx, req)
}

// FetchDodoAmountTopUp calls billing.payment.v1.MerchantPaymentService.FetchDodoAmountTopUp.
func (c *merchantPaymentServiceClient) FetchDodoAmountTopUp(ctx context.Context, req *connect.Request[v1.MerchantPaymentServiceFetchDodoAmountTopUpRequest]) (*connect.Response[v1.MerchantPaymentServiceFetchDodoAmountTopUpResponse], error) {
	return c.fetchDodoAmountTopUp.CallUnary(ctx, req)
}

// FetchSePayAmountTopUp calls billing.payment.v1.MerchantPaymentService.FetchSePayAmountTopUp.
func (c *merchantPaymentServiceClient) FetchSePayAmountTopUp(ctx context.Context, req *connect.Request[v1.MerchantPaymentServiceFetchSePayAmountTopUpRequest]) (*connect.Response[v1.MerchantPaymentServiceFetchSePayAmountTopUpResponse], error) {
	return c.fetchSePayAmountTopUp.CallUnary(ctx, req)
}

// CreateDodoPayment calls billing.payment.v1.MerchantPaymentService.CreateDodoPayment.
func (c *merchantPaymentServiceClient) CreateDodoPayment(ctx context.Context, req *connect.Request[v1.MerchantPaymentServiceCreateDodoPaymentRequest]) (*connect.Response[v1.MerchantPaymentServiceCreateDodoPaymentResponse], error) {
	return c.createDodoPayment.CallUnary(ctx, req)
}

// CreateSePayPayment calls billing.payment.v1.MerchantPaymentService.CreateSePayPayment.
func (c *merchantPaymentServiceClient) CreateSePayPayment(ctx context.Context, req *connect.Request[v1.MerchantPaymentServiceCreateSePayPaymentRequest]) (*connect.Response[v1.MerchantPaymentServiceCreateSePayPaymentResponse], error) {
	return c.createSePayPayment.CallUnary(ctx, req)
}

// MerchantPaymentServiceHandler is an implementation of the
// billing.payment.v1.MerchantPaymentService service.
type MerchantPaymentServiceHandler interface {
	FetchPaymentGateway(context.Context, *connect.Request[v1.MerchantPaymentServiceFetchPaymentGatewayRequest]) (*connect.Response[v1.MerchantPaymentServiceFetchPaymentGatewayResponse], error)
	// amount top_up
	FetchAppotaAmountTopUp(context.Context, *connect.Request[v1.MerchantPaymentServiceFetchAppotaAmountTopUpRequest]) (*connect.Response[v1.MerchantPaymentServiceFetchAppotaAmountTopUpResponse], error)
	FetchDodoAmountTopUp(context.Context, *connect.Request[v1.MerchantPaymentServiceFetchDodoAmountTopUpRequest]) (*connect.Response[v1.MerchantPaymentServiceFetchDodoAmountTopUpResponse], error)
	FetchSePayAmountTopUp(context.Context, *connect.Request[v1.MerchantPaymentServiceFetchSePayAmountTopUpRequest]) (*connect.Response[v1.MerchantPaymentServiceFetchSePayAmountTopUpResponse], error)
	CreateDodoPayment(context.Context, *connect.Request[v1.MerchantPaymentServiceCreateDodoPaymentRequest]) (*connect.Response[v1.MerchantPaymentServiceCreateDodoPaymentResponse], error)
	CreateSePayPayment(context.Context, *connect.Request[v1.MerchantPaymentServiceCreateSePayPaymentRequest]) (*connect.Response[v1.MerchantPaymentServiceCreateSePayPaymentResponse], error)
}

// NewMerchantPaymentServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewMerchantPaymentServiceHandler(svc MerchantPaymentServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	merchantPaymentServiceMethods := v1.File_billing_payment_v1_merchant_proto.Services().ByName("MerchantPaymentService").Methods()
	merchantPaymentServiceFetchPaymentGatewayHandler := connect.NewUnaryHandler(
		MerchantPaymentServiceFetchPaymentGatewayProcedure,
		svc.FetchPaymentGateway,
		connect.WithSchema(merchantPaymentServiceMethods.ByName("FetchPaymentGateway")),
		connect.WithHandlerOptions(opts...),
	)
	merchantPaymentServiceFetchAppotaAmountTopUpHandler := connect.NewUnaryHandler(
		MerchantPaymentServiceFetchAppotaAmountTopUpProcedure,
		svc.FetchAppotaAmountTopUp,
		connect.WithSchema(merchantPaymentServiceMethods.ByName("FetchAppotaAmountTopUp")),
		connect.WithHandlerOptions(opts...),
	)
	merchantPaymentServiceFetchDodoAmountTopUpHandler := connect.NewUnaryHandler(
		MerchantPaymentServiceFetchDodoAmountTopUpProcedure,
		svc.FetchDodoAmountTopUp,
		connect.WithSchema(merchantPaymentServiceMethods.ByName("FetchDodoAmountTopUp")),
		connect.WithHandlerOptions(opts...),
	)
	merchantPaymentServiceFetchSePayAmountTopUpHandler := connect.NewUnaryHandler(
		MerchantPaymentServiceFetchSePayAmountTopUpProcedure,
		svc.FetchSePayAmountTopUp,
		connect.WithSchema(merchantPaymentServiceMethods.ByName("FetchSePayAmountTopUp")),
		connect.WithHandlerOptions(opts...),
	)
	merchantPaymentServiceCreateDodoPaymentHandler := connect.NewUnaryHandler(
		MerchantPaymentServiceCreateDodoPaymentProcedure,
		svc.CreateDodoPayment,
		connect.WithSchema(merchantPaymentServiceMethods.ByName("CreateDodoPayment")),
		connect.WithHandlerOptions(opts...),
	)
	merchantPaymentServiceCreateSePayPaymentHandler := connect.NewUnaryHandler(
		MerchantPaymentServiceCreateSePayPaymentProcedure,
		svc.CreateSePayPayment,
		connect.WithSchema(merchantPaymentServiceMethods.ByName("CreateSePayPayment")),
		connect.WithHandlerOptions(opts...),
	)
	return "/billing.payment.v1.MerchantPaymentService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case MerchantPaymentServiceFetchPaymentGatewayProcedure:
			merchantPaymentServiceFetchPaymentGatewayHandler.ServeHTTP(w, r)
		case MerchantPaymentServiceFetchAppotaAmountTopUpProcedure:
			merchantPaymentServiceFetchAppotaAmountTopUpHandler.ServeHTTP(w, r)
		case MerchantPaymentServiceFetchDodoAmountTopUpProcedure:
			merchantPaymentServiceFetchDodoAmountTopUpHandler.ServeHTTP(w, r)
		case MerchantPaymentServiceFetchSePayAmountTopUpProcedure:
			merchantPaymentServiceFetchSePayAmountTopUpHandler.ServeHTTP(w, r)
		case MerchantPaymentServiceCreateDodoPaymentProcedure:
			merchantPaymentServiceCreateDodoPaymentHandler.ServeHTTP(w, r)
		case MerchantPaymentServiceCreateSePayPaymentProcedure:
			merchantPaymentServiceCreateSePayPaymentHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedMerchantPaymentServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedMerchantPaymentServiceHandler struct{}

func (UnimplementedMerchantPaymentServiceHandler) FetchPaymentGateway(context.Context, *connect.Request[v1.MerchantPaymentServiceFetchPaymentGatewayRequest]) (*connect.Response[v1.MerchantPaymentServiceFetchPaymentGatewayResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.MerchantPaymentService.FetchPaymentGateway is not implemented"))
}

func (UnimplementedMerchantPaymentServiceHandler) FetchAppotaAmountTopUp(context.Context, *connect.Request[v1.MerchantPaymentServiceFetchAppotaAmountTopUpRequest]) (*connect.Response[v1.MerchantPaymentServiceFetchAppotaAmountTopUpResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.MerchantPaymentService.FetchAppotaAmountTopUp is not implemented"))
}

func (UnimplementedMerchantPaymentServiceHandler) FetchDodoAmountTopUp(context.Context, *connect.Request[v1.MerchantPaymentServiceFetchDodoAmountTopUpRequest]) (*connect.Response[v1.MerchantPaymentServiceFetchDodoAmountTopUpResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.MerchantPaymentService.FetchDodoAmountTopUp is not implemented"))
}

func (UnimplementedMerchantPaymentServiceHandler) FetchSePayAmountTopUp(context.Context, *connect.Request[v1.MerchantPaymentServiceFetchSePayAmountTopUpRequest]) (*connect.Response[v1.MerchantPaymentServiceFetchSePayAmountTopUpResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.MerchantPaymentService.FetchSePayAmountTopUp is not implemented"))
}

func (UnimplementedMerchantPaymentServiceHandler) CreateDodoPayment(context.Context, *connect.Request[v1.MerchantPaymentServiceCreateDodoPaymentRequest]) (*connect.Response[v1.MerchantPaymentServiceCreateDodoPaymentResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.MerchantPaymentService.CreateDodoPayment is not implemented"))
}

func (UnimplementedMerchantPaymentServiceHandler) CreateSePayPayment(context.Context, *connect.Request[v1.MerchantPaymentServiceCreateSePayPaymentRequest]) (*connect.Response[v1.MerchantPaymentServiceCreateSePayPaymentResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.MerchantPaymentService.CreateSePayPayment is not implemented"))
}
