// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: billing/payment/v1/customer.proto

package paymentv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/payment/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CustomerPaymentServiceName is the fully-qualified name of the CustomerPaymentService service.
	CustomerPaymentServiceName = "billing.payment.v1.CustomerPaymentService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CustomerPaymentServiceFetchPaymentGatewayProcedure is the fully-qualified name of the
	// CustomerPaymentService's FetchPaymentGateway RPC.
	CustomerPaymentServiceFetchPaymentGatewayProcedure = "/billing.payment.v1.CustomerPaymentService/FetchPaymentGateway"
	// CustomerPaymentServiceFetchAppotaAmountTopUpProcedure is the fully-qualified name of the
	// CustomerPaymentService's FetchAppotaAmountTopUp RPC.
	CustomerPaymentServiceFetchAppotaAmountTopUpProcedure = "/billing.payment.v1.CustomerPaymentService/FetchAppotaAmountTopUp"
	// CustomerPaymentServiceCreateAppotaPaymentProcedure is the fully-qualified name of the
	// CustomerPaymentService's CreateAppotaPayment RPC.
	CustomerPaymentServiceCreateAppotaPaymentProcedure = "/billing.payment.v1.CustomerPaymentService/CreateAppotaPayment"
	// CustomerPaymentServiceFetchDodoAmountTopUpProcedure is the fully-qualified name of the
	// CustomerPaymentService's FetchDodoAmountTopUp RPC.
	CustomerPaymentServiceFetchDodoAmountTopUpProcedure = "/billing.payment.v1.CustomerPaymentService/FetchDodoAmountTopUp"
	// CustomerPaymentServiceCreateDodoPaymentProcedure is the fully-qualified name of the
	// CustomerPaymentService's CreateDodoPayment RPC.
	CustomerPaymentServiceCreateDodoPaymentProcedure = "/billing.payment.v1.CustomerPaymentService/CreateDodoPayment"
	// CustomerPaymentServiceFetchSePayAmountTopUpProcedure is the fully-qualified name of the
	// CustomerPaymentService's FetchSePayAmountTopUp RPC.
	CustomerPaymentServiceFetchSePayAmountTopUpProcedure = "/billing.payment.v1.CustomerPaymentService/FetchSePayAmountTopUp"
	// CustomerPaymentServiceCreateSePayPaymentProcedure is the fully-qualified name of the
	// CustomerPaymentService's CreateSePayPayment RPC.
	CustomerPaymentServiceCreateSePayPaymentProcedure = "/billing.payment.v1.CustomerPaymentService/CreateSePayPayment"
)

// CustomerPaymentServiceClient is a client for the billing.payment.v1.CustomerPaymentService
// service.
type CustomerPaymentServiceClient interface {
	FetchPaymentGateway(context.Context, *connect.Request[v1.CustomerPaymentServiceFetchPaymentGatewayRequest]) (*connect.Response[v1.CustomerPaymentServiceFetchPaymentGatewayResponse], error)
	FetchAppotaAmountTopUp(context.Context, *connect.Request[v1.CustomerPaymentServiceFetchAppotaAmountTopUpRequest]) (*connect.Response[v1.CustomerPaymentServiceFetchAppotaAmountTopUpResponse], error)
	CreateAppotaPayment(context.Context, *connect.Request[v1.CustomerPaymentServiceCreateAppotaPaymentRequest]) (*connect.Response[v1.CustomerPaymentServiceCreateAppotaPaymentResponse], error)
	FetchDodoAmountTopUp(context.Context, *connect.Request[v1.CustomerPaymentServiceFetchDodoAmountTopUpRequest]) (*connect.Response[v1.CustomerPaymentServiceFetchDodoAmountTopUpResponse], error)
	CreateDodoPayment(context.Context, *connect.Request[v1.CustomerPaymentServiceCreateDodoPaymentRequest]) (*connect.Response[v1.CustomerPaymentServiceCreateDodoPaymentResponse], error)
	FetchSePayAmountTopUp(context.Context, *connect.Request[v1.CustomerPaymentServiceFetchSePayAmountTopUpRequest]) (*connect.Response[v1.CustomerPaymentServiceFetchSePayAmountTopUpResponse], error)
	CreateSePayPayment(context.Context, *connect.Request[v1.CustomerPaymentServiceCreateSePayPaymentRequest]) (*connect.Response[v1.CustomerPaymentServiceCreateSePayPaymentResponse], error)
}

// NewCustomerPaymentServiceClient constructs a client for the
// billing.payment.v1.CustomerPaymentService service. By default, it uses the Connect protocol with
// the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed requests. To use
// the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCustomerPaymentServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CustomerPaymentServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	customerPaymentServiceMethods := v1.File_billing_payment_v1_customer_proto.Services().ByName("CustomerPaymentService").Methods()
	return &customerPaymentServiceClient{
		fetchPaymentGateway: connect.NewClient[v1.CustomerPaymentServiceFetchPaymentGatewayRequest, v1.CustomerPaymentServiceFetchPaymentGatewayResponse](
			httpClient,
			baseURL+CustomerPaymentServiceFetchPaymentGatewayProcedure,
			connect.WithSchema(customerPaymentServiceMethods.ByName("FetchPaymentGateway")),
			connect.WithClientOptions(opts...),
		),
		fetchAppotaAmountTopUp: connect.NewClient[v1.CustomerPaymentServiceFetchAppotaAmountTopUpRequest, v1.CustomerPaymentServiceFetchAppotaAmountTopUpResponse](
			httpClient,
			baseURL+CustomerPaymentServiceFetchAppotaAmountTopUpProcedure,
			connect.WithSchema(customerPaymentServiceMethods.ByName("FetchAppotaAmountTopUp")),
			connect.WithClientOptions(opts...),
		),
		createAppotaPayment: connect.NewClient[v1.CustomerPaymentServiceCreateAppotaPaymentRequest, v1.CustomerPaymentServiceCreateAppotaPaymentResponse](
			httpClient,
			baseURL+CustomerPaymentServiceCreateAppotaPaymentProcedure,
			connect.WithSchema(customerPaymentServiceMethods.ByName("CreateAppotaPayment")),
			connect.WithClientOptions(opts...),
		),
		fetchDodoAmountTopUp: connect.NewClient[v1.CustomerPaymentServiceFetchDodoAmountTopUpRequest, v1.CustomerPaymentServiceFetchDodoAmountTopUpResponse](
			httpClient,
			baseURL+CustomerPaymentServiceFetchDodoAmountTopUpProcedure,
			connect.WithSchema(customerPaymentServiceMethods.ByName("FetchDodoAmountTopUp")),
			connect.WithClientOptions(opts...),
		),
		createDodoPayment: connect.NewClient[v1.CustomerPaymentServiceCreateDodoPaymentRequest, v1.CustomerPaymentServiceCreateDodoPaymentResponse](
			httpClient,
			baseURL+CustomerPaymentServiceCreateDodoPaymentProcedure,
			connect.WithSchema(customerPaymentServiceMethods.ByName("CreateDodoPayment")),
			connect.WithClientOptions(opts...),
		),
		fetchSePayAmountTopUp: connect.NewClient[v1.CustomerPaymentServiceFetchSePayAmountTopUpRequest, v1.CustomerPaymentServiceFetchSePayAmountTopUpResponse](
			httpClient,
			baseURL+CustomerPaymentServiceFetchSePayAmountTopUpProcedure,
			connect.WithSchema(customerPaymentServiceMethods.ByName("FetchSePayAmountTopUp")),
			connect.WithClientOptions(opts...),
		),
		createSePayPayment: connect.NewClient[v1.CustomerPaymentServiceCreateSePayPaymentRequest, v1.CustomerPaymentServiceCreateSePayPaymentResponse](
			httpClient,
			baseURL+CustomerPaymentServiceCreateSePayPaymentProcedure,
			connect.WithSchema(customerPaymentServiceMethods.ByName("CreateSePayPayment")),
			connect.WithClientOptions(opts...),
		),
	}
}

// customerPaymentServiceClient implements CustomerPaymentServiceClient.
type customerPaymentServiceClient struct {
	fetchPaymentGateway    *connect.Client[v1.CustomerPaymentServiceFetchPaymentGatewayRequest, v1.CustomerPaymentServiceFetchPaymentGatewayResponse]
	fetchAppotaAmountTopUp *connect.Client[v1.CustomerPaymentServiceFetchAppotaAmountTopUpRequest, v1.CustomerPaymentServiceFetchAppotaAmountTopUpResponse]
	createAppotaPayment    *connect.Client[v1.CustomerPaymentServiceCreateAppotaPaymentRequest, v1.CustomerPaymentServiceCreateAppotaPaymentResponse]
	fetchDodoAmountTopUp   *connect.Client[v1.CustomerPaymentServiceFetchDodoAmountTopUpRequest, v1.CustomerPaymentServiceFetchDodoAmountTopUpResponse]
	createDodoPayment      *connect.Client[v1.CustomerPaymentServiceCreateDodoPaymentRequest, v1.CustomerPaymentServiceCreateDodoPaymentResponse]
	fetchSePayAmountTopUp  *connect.Client[v1.CustomerPaymentServiceFetchSePayAmountTopUpRequest, v1.CustomerPaymentServiceFetchSePayAmountTopUpResponse]
	createSePayPayment     *connect.Client[v1.CustomerPaymentServiceCreateSePayPaymentRequest, v1.CustomerPaymentServiceCreateSePayPaymentResponse]
}

// FetchPaymentGateway calls billing.payment.v1.CustomerPaymentService.FetchPaymentGateway.
func (c *customerPaymentServiceClient) FetchPaymentGateway(ctx context.Context, req *connect.Request[v1.CustomerPaymentServiceFetchPaymentGatewayRequest]) (*connect.Response[v1.CustomerPaymentServiceFetchPaymentGatewayResponse], error) {
	return c.fetchPaymentGateway.CallUnary(ctx, req)
}

// FetchAppotaAmountTopUp calls billing.payment.v1.CustomerPaymentService.FetchAppotaAmountTopUp.
func (c *customerPaymentServiceClient) FetchAppotaAmountTopUp(ctx context.Context, req *connect.Request[v1.CustomerPaymentServiceFetchAppotaAmountTopUpRequest]) (*connect.Response[v1.CustomerPaymentServiceFetchAppotaAmountTopUpResponse], error) {
	return c.fetchAppotaAmountTopUp.CallUnary(ctx, req)
}

// CreateAppotaPayment calls billing.payment.v1.CustomerPaymentService.CreateAppotaPayment.
func (c *customerPaymentServiceClient) CreateAppotaPayment(ctx context.Context, req *connect.Request[v1.CustomerPaymentServiceCreateAppotaPaymentRequest]) (*connect.Response[v1.CustomerPaymentServiceCreateAppotaPaymentResponse], error) {
	return c.createAppotaPayment.CallUnary(ctx, req)
}

// FetchDodoAmountTopUp calls billing.payment.v1.CustomerPaymentService.FetchDodoAmountTopUp.
func (c *customerPaymentServiceClient) FetchDodoAmountTopUp(ctx context.Context, req *connect.Request[v1.CustomerPaymentServiceFetchDodoAmountTopUpRequest]) (*connect.Response[v1.CustomerPaymentServiceFetchDodoAmountTopUpResponse], error) {
	return c.fetchDodoAmountTopUp.CallUnary(ctx, req)
}

// CreateDodoPayment calls billing.payment.v1.CustomerPaymentService.CreateDodoPayment.
func (c *customerPaymentServiceClient) CreateDodoPayment(ctx context.Context, req *connect.Request[v1.CustomerPaymentServiceCreateDodoPaymentRequest]) (*connect.Response[v1.CustomerPaymentServiceCreateDodoPaymentResponse], error) {
	return c.createDodoPayment.CallUnary(ctx, req)
}

// FetchSePayAmountTopUp calls billing.payment.v1.CustomerPaymentService.FetchSePayAmountTopUp.
func (c *customerPaymentServiceClient) FetchSePayAmountTopUp(ctx context.Context, req *connect.Request[v1.CustomerPaymentServiceFetchSePayAmountTopUpRequest]) (*connect.Response[v1.CustomerPaymentServiceFetchSePayAmountTopUpResponse], error) {
	return c.fetchSePayAmountTopUp.CallUnary(ctx, req)
}

// CreateSePayPayment calls billing.payment.v1.CustomerPaymentService.CreateSePayPayment.
func (c *customerPaymentServiceClient) CreateSePayPayment(ctx context.Context, req *connect.Request[v1.CustomerPaymentServiceCreateSePayPaymentRequest]) (*connect.Response[v1.CustomerPaymentServiceCreateSePayPaymentResponse], error) {
	return c.createSePayPayment.CallUnary(ctx, req)
}

// CustomerPaymentServiceHandler is an implementation of the
// billing.payment.v1.CustomerPaymentService service.
type CustomerPaymentServiceHandler interface {
	FetchPaymentGateway(context.Context, *connect.Request[v1.CustomerPaymentServiceFetchPaymentGatewayRequest]) (*connect.Response[v1.CustomerPaymentServiceFetchPaymentGatewayResponse], error)
	FetchAppotaAmountTopUp(context.Context, *connect.Request[v1.CustomerPaymentServiceFetchAppotaAmountTopUpRequest]) (*connect.Response[v1.CustomerPaymentServiceFetchAppotaAmountTopUpResponse], error)
	CreateAppotaPayment(context.Context, *connect.Request[v1.CustomerPaymentServiceCreateAppotaPaymentRequest]) (*connect.Response[v1.CustomerPaymentServiceCreateAppotaPaymentResponse], error)
	FetchDodoAmountTopUp(context.Context, *connect.Request[v1.CustomerPaymentServiceFetchDodoAmountTopUpRequest]) (*connect.Response[v1.CustomerPaymentServiceFetchDodoAmountTopUpResponse], error)
	CreateDodoPayment(context.Context, *connect.Request[v1.CustomerPaymentServiceCreateDodoPaymentRequest]) (*connect.Response[v1.CustomerPaymentServiceCreateDodoPaymentResponse], error)
	FetchSePayAmountTopUp(context.Context, *connect.Request[v1.CustomerPaymentServiceFetchSePayAmountTopUpRequest]) (*connect.Response[v1.CustomerPaymentServiceFetchSePayAmountTopUpResponse], error)
	CreateSePayPayment(context.Context, *connect.Request[v1.CustomerPaymentServiceCreateSePayPaymentRequest]) (*connect.Response[v1.CustomerPaymentServiceCreateSePayPaymentResponse], error)
}

// NewCustomerPaymentServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCustomerPaymentServiceHandler(svc CustomerPaymentServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	customerPaymentServiceMethods := v1.File_billing_payment_v1_customer_proto.Services().ByName("CustomerPaymentService").Methods()
	customerPaymentServiceFetchPaymentGatewayHandler := connect.NewUnaryHandler(
		CustomerPaymentServiceFetchPaymentGatewayProcedure,
		svc.FetchPaymentGateway,
		connect.WithSchema(customerPaymentServiceMethods.ByName("FetchPaymentGateway")),
		connect.WithHandlerOptions(opts...),
	)
	customerPaymentServiceFetchAppotaAmountTopUpHandler := connect.NewUnaryHandler(
		CustomerPaymentServiceFetchAppotaAmountTopUpProcedure,
		svc.FetchAppotaAmountTopUp,
		connect.WithSchema(customerPaymentServiceMethods.ByName("FetchAppotaAmountTopUp")),
		connect.WithHandlerOptions(opts...),
	)
	customerPaymentServiceCreateAppotaPaymentHandler := connect.NewUnaryHandler(
		CustomerPaymentServiceCreateAppotaPaymentProcedure,
		svc.CreateAppotaPayment,
		connect.WithSchema(customerPaymentServiceMethods.ByName("CreateAppotaPayment")),
		connect.WithHandlerOptions(opts...),
	)
	customerPaymentServiceFetchDodoAmountTopUpHandler := connect.NewUnaryHandler(
		CustomerPaymentServiceFetchDodoAmountTopUpProcedure,
		svc.FetchDodoAmountTopUp,
		connect.WithSchema(customerPaymentServiceMethods.ByName("FetchDodoAmountTopUp")),
		connect.WithHandlerOptions(opts...),
	)
	customerPaymentServiceCreateDodoPaymentHandler := connect.NewUnaryHandler(
		CustomerPaymentServiceCreateDodoPaymentProcedure,
		svc.CreateDodoPayment,
		connect.WithSchema(customerPaymentServiceMethods.ByName("CreateDodoPayment")),
		connect.WithHandlerOptions(opts...),
	)
	customerPaymentServiceFetchSePayAmountTopUpHandler := connect.NewUnaryHandler(
		CustomerPaymentServiceFetchSePayAmountTopUpProcedure,
		svc.FetchSePayAmountTopUp,
		connect.WithSchema(customerPaymentServiceMethods.ByName("FetchSePayAmountTopUp")),
		connect.WithHandlerOptions(opts...),
	)
	customerPaymentServiceCreateSePayPaymentHandler := connect.NewUnaryHandler(
		CustomerPaymentServiceCreateSePayPaymentProcedure,
		svc.CreateSePayPayment,
		connect.WithSchema(customerPaymentServiceMethods.ByName("CreateSePayPayment")),
		connect.WithHandlerOptions(opts...),
	)
	return "/billing.payment.v1.CustomerPaymentService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CustomerPaymentServiceFetchPaymentGatewayProcedure:
			customerPaymentServiceFetchPaymentGatewayHandler.ServeHTTP(w, r)
		case CustomerPaymentServiceFetchAppotaAmountTopUpProcedure:
			customerPaymentServiceFetchAppotaAmountTopUpHandler.ServeHTTP(w, r)
		case CustomerPaymentServiceCreateAppotaPaymentProcedure:
			customerPaymentServiceCreateAppotaPaymentHandler.ServeHTTP(w, r)
		case CustomerPaymentServiceFetchDodoAmountTopUpProcedure:
			customerPaymentServiceFetchDodoAmountTopUpHandler.ServeHTTP(w, r)
		case CustomerPaymentServiceCreateDodoPaymentProcedure:
			customerPaymentServiceCreateDodoPaymentHandler.ServeHTTP(w, r)
		case CustomerPaymentServiceFetchSePayAmountTopUpProcedure:
			customerPaymentServiceFetchSePayAmountTopUpHandler.ServeHTTP(w, r)
		case CustomerPaymentServiceCreateSePayPaymentProcedure:
			customerPaymentServiceCreateSePayPaymentHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCustomerPaymentServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCustomerPaymentServiceHandler struct{}

func (UnimplementedCustomerPaymentServiceHandler) FetchPaymentGateway(context.Context, *connect.Request[v1.CustomerPaymentServiceFetchPaymentGatewayRequest]) (*connect.Response[v1.CustomerPaymentServiceFetchPaymentGatewayResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.CustomerPaymentService.FetchPaymentGateway is not implemented"))
}

func (UnimplementedCustomerPaymentServiceHandler) FetchAppotaAmountTopUp(context.Context, *connect.Request[v1.CustomerPaymentServiceFetchAppotaAmountTopUpRequest]) (*connect.Response[v1.CustomerPaymentServiceFetchAppotaAmountTopUpResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.CustomerPaymentService.FetchAppotaAmountTopUp is not implemented"))
}

func (UnimplementedCustomerPaymentServiceHandler) CreateAppotaPayment(context.Context, *connect.Request[v1.CustomerPaymentServiceCreateAppotaPaymentRequest]) (*connect.Response[v1.CustomerPaymentServiceCreateAppotaPaymentResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.CustomerPaymentService.CreateAppotaPayment is not implemented"))
}

func (UnimplementedCustomerPaymentServiceHandler) FetchDodoAmountTopUp(context.Context, *connect.Request[v1.CustomerPaymentServiceFetchDodoAmountTopUpRequest]) (*connect.Response[v1.CustomerPaymentServiceFetchDodoAmountTopUpResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.CustomerPaymentService.FetchDodoAmountTopUp is not implemented"))
}

func (UnimplementedCustomerPaymentServiceHandler) CreateDodoPayment(context.Context, *connect.Request[v1.CustomerPaymentServiceCreateDodoPaymentRequest]) (*connect.Response[v1.CustomerPaymentServiceCreateDodoPaymentResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.CustomerPaymentService.CreateDodoPayment is not implemented"))
}

func (UnimplementedCustomerPaymentServiceHandler) FetchSePayAmountTopUp(context.Context, *connect.Request[v1.CustomerPaymentServiceFetchSePayAmountTopUpRequest]) (*connect.Response[v1.CustomerPaymentServiceFetchSePayAmountTopUpResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.CustomerPaymentService.FetchSePayAmountTopUp is not implemented"))
}

func (UnimplementedCustomerPaymentServiceHandler) CreateSePayPayment(context.Context, *connect.Request[v1.CustomerPaymentServiceCreateSePayPaymentRequest]) (*connect.Response[v1.CustomerPaymentServiceCreateSePayPaymentResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.payment.v1.CustomerPaymentService.CreateSePayPayment is not implemented"))
}
