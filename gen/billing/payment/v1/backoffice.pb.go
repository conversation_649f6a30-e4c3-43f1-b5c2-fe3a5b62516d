// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: billing/payment/v1/backoffice.proto

package paymentv1

import (
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackofficePaymentServiceFetchSePayAmountTopUpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant    string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	State         *v1.State              `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePaymentServiceFetchSePayAmountTopUpRequest) Reset() {
	*x = BackofficePaymentServiceFetchSePayAmountTopUpRequest{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceFetchSePayAmountTopUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceFetchSePayAmountTopUpRequest) ProtoMessage() {}

func (x *BackofficePaymentServiceFetchSePayAmountTopUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceFetchSePayAmountTopUpRequest.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceFetchSePayAmountTopUpRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{0}
}

func (x *BackofficePaymentServiceFetchSePayAmountTopUpRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficePaymentServiceFetchSePayAmountTopUpRequest) GetState() *v1.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficePaymentServiceFetchSePayAmountTopUpRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficePaymentServiceFetchSePayAmountTopUpResponse struct {
	state             protoimpl.MessageState                      `protogen:"open.v1"`
	Error             *v11.ErrorMessage                           `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination        *v1.PaginationResponse                      `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	SepayAmountTopUps []*BackofficePaymentServiceSePayAmountTopUp `protobuf:"bytes,3,rep,name=sepay_amount_top_ups,json=sepayAmountTopUps,proto3" json:"sepay_amount_top_ups,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BackofficePaymentServiceFetchSePayAmountTopUpResponse) Reset() {
	*x = BackofficePaymentServiceFetchSePayAmountTopUpResponse{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceFetchSePayAmountTopUpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceFetchSePayAmountTopUpResponse) ProtoMessage() {}

func (x *BackofficePaymentServiceFetchSePayAmountTopUpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceFetchSePayAmountTopUpResponse.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceFetchSePayAmountTopUpResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{1}
}

func (x *BackofficePaymentServiceFetchSePayAmountTopUpResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficePaymentServiceFetchSePayAmountTopUpResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficePaymentServiceFetchSePayAmountTopUpResponse) GetSepayAmountTopUps() []*BackofficePaymentServiceSePayAmountTopUp {
	if x != nil {
		return x.SepayAmountTopUps
	}
	return nil
}

type BackofficePaymentServiceSePayAmountTopUp struct {
	state                            protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewaySepayAmountTopUp string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_sepay_amount_top_up,json=idPaymentGatewaySepayAmountTopUp,proto3" json:"id_payment_gateway_sepay_amount_top_up,omitempty"`
	Amount                           float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Promotion                        float64                `protobuf:"fixed64,3,opt,name=promotion,proto3" json:"promotion,omitempty"`
	Currency                         v12.Currency           `protobuf:"varint,4,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	unknownFields                    protoimpl.UnknownFields
	sizeCache                        protoimpl.SizeCache
}

func (x *BackofficePaymentServiceSePayAmountTopUp) Reset() {
	*x = BackofficePaymentServiceSePayAmountTopUp{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceSePayAmountTopUp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceSePayAmountTopUp) ProtoMessage() {}

func (x *BackofficePaymentServiceSePayAmountTopUp) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceSePayAmountTopUp.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceSePayAmountTopUp) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{2}
}

func (x *BackofficePaymentServiceSePayAmountTopUp) GetIdPaymentGatewaySepayAmountTopUp() string {
	if x != nil {
		return x.IdPaymentGatewaySepayAmountTopUp
	}
	return ""
}

func (x *BackofficePaymentServiceSePayAmountTopUp) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *BackofficePaymentServiceSePayAmountTopUp) GetPromotion() float64 {
	if x != nil {
		return x.Promotion
	}
	return 0
}

func (x *BackofficePaymentServiceSePayAmountTopUp) GetCurrency() v12.Currency {
	if x != nil {
		return x.Currency
	}
	return v12.Currency(0)
}

type BackofficePaymentServiceFetchDodoAmountTopUpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant    string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	State         *v1.State              `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePaymentServiceFetchDodoAmountTopUpRequest) Reset() {
	*x = BackofficePaymentServiceFetchDodoAmountTopUpRequest{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceFetchDodoAmountTopUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceFetchDodoAmountTopUpRequest) ProtoMessage() {}

func (x *BackofficePaymentServiceFetchDodoAmountTopUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceFetchDodoAmountTopUpRequest.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceFetchDodoAmountTopUpRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{3}
}

func (x *BackofficePaymentServiceFetchDodoAmountTopUpRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficePaymentServiceFetchDodoAmountTopUpRequest) GetState() *v1.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficePaymentServiceFetchDodoAmountTopUpRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficePaymentServiceFetchDodoAmountTopUpResponse struct {
	state            protoimpl.MessageState                     `protogen:"open.v1"`
	Error            *v11.ErrorMessage                          `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination       *v1.PaginationResponse                     `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	DodoAmountTopUps []*BackofficePaymentServiceDodoAmountTopUp `protobuf:"bytes,3,rep,name=dodo_amount_top_ups,json=dodoAmountTopUps,proto3" json:"dodo_amount_top_ups,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BackofficePaymentServiceFetchDodoAmountTopUpResponse) Reset() {
	*x = BackofficePaymentServiceFetchDodoAmountTopUpResponse{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceFetchDodoAmountTopUpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceFetchDodoAmountTopUpResponse) ProtoMessage() {}

func (x *BackofficePaymentServiceFetchDodoAmountTopUpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceFetchDodoAmountTopUpResponse.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceFetchDodoAmountTopUpResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{4}
}

func (x *BackofficePaymentServiceFetchDodoAmountTopUpResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficePaymentServiceFetchDodoAmountTopUpResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficePaymentServiceFetchDodoAmountTopUpResponse) GetDodoAmountTopUps() []*BackofficePaymentServiceDodoAmountTopUp {
	if x != nil {
		return x.DodoAmountTopUps
	}
	return nil
}

type BackofficePaymentServiceDodoAmountTopUp struct {
	state                           protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewayDodoAmountTopUp string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_dodo_amount_top_up,json=idPaymentGatewayDodoAmountTopUp,proto3" json:"id_payment_gateway_dodo_amount_top_up,omitempty"`
	Amount                          float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Promotion                       float64                `protobuf:"fixed64,3,opt,name=promotion,proto3" json:"promotion,omitempty"`
	ProductIdDodo                   string                 `protobuf:"bytes,4,opt,name=product_id_dodo,json=productIdDodo,proto3" json:"product_id_dodo,omitempty"`
	Currency                        v12.Currency           `protobuf:"varint,5,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	unknownFields                   protoimpl.UnknownFields
	sizeCache                       protoimpl.SizeCache
}

func (x *BackofficePaymentServiceDodoAmountTopUp) Reset() {
	*x = BackofficePaymentServiceDodoAmountTopUp{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceDodoAmountTopUp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceDodoAmountTopUp) ProtoMessage() {}

func (x *BackofficePaymentServiceDodoAmountTopUp) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceDodoAmountTopUp.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceDodoAmountTopUp) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{5}
}

func (x *BackofficePaymentServiceDodoAmountTopUp) GetIdPaymentGatewayDodoAmountTopUp() string {
	if x != nil {
		return x.IdPaymentGatewayDodoAmountTopUp
	}
	return ""
}

func (x *BackofficePaymentServiceDodoAmountTopUp) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *BackofficePaymentServiceDodoAmountTopUp) GetPromotion() float64 {
	if x != nil {
		return x.Promotion
	}
	return 0
}

func (x *BackofficePaymentServiceDodoAmountTopUp) GetProductIdDodo() string {
	if x != nil {
		return x.ProductIdDodo
	}
	return ""
}

func (x *BackofficePaymentServiceDodoAmountTopUp) GetCurrency() v12.Currency {
	if x != nil {
		return x.Currency
	}
	return v12.Currency(0)
}

type BackofficePaymentServiceFetchAppotaAmountTopUpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant    string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	State         *v1.State              `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePaymentServiceFetchAppotaAmountTopUpRequest) Reset() {
	*x = BackofficePaymentServiceFetchAppotaAmountTopUpRequest{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceFetchAppotaAmountTopUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceFetchAppotaAmountTopUpRequest) ProtoMessage() {}

func (x *BackofficePaymentServiceFetchAppotaAmountTopUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceFetchAppotaAmountTopUpRequest.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceFetchAppotaAmountTopUpRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{6}
}

func (x *BackofficePaymentServiceFetchAppotaAmountTopUpRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficePaymentServiceFetchAppotaAmountTopUpRequest) GetState() *v1.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficePaymentServiceFetchAppotaAmountTopUpRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficePaymentServiceFetchAppotaAmountTopUpResponse struct {
	state              protoimpl.MessageState                       `protogen:"open.v1"`
	Error              *v11.ErrorMessage                            `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination         *v1.PaginationResponse                       `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	AppotaAmountTopUps []*BackofficePaymentServiceAppotaAmountTopUp `protobuf:"bytes,3,rep,name=appota_amount_top_ups,json=appotaAmountTopUps,proto3" json:"appota_amount_top_ups,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *BackofficePaymentServiceFetchAppotaAmountTopUpResponse) Reset() {
	*x = BackofficePaymentServiceFetchAppotaAmountTopUpResponse{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceFetchAppotaAmountTopUpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceFetchAppotaAmountTopUpResponse) ProtoMessage() {}

func (x *BackofficePaymentServiceFetchAppotaAmountTopUpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceFetchAppotaAmountTopUpResponse.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceFetchAppotaAmountTopUpResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{7}
}

func (x *BackofficePaymentServiceFetchAppotaAmountTopUpResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficePaymentServiceFetchAppotaAmountTopUpResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficePaymentServiceFetchAppotaAmountTopUpResponse) GetAppotaAmountTopUps() []*BackofficePaymentServiceAppotaAmountTopUp {
	if x != nil {
		return x.AppotaAmountTopUps
	}
	return nil
}

type BackofficePaymentServiceAppotaAmountTopUp struct {
	state                             protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewayAppotaAmountTopUp string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_appota_amount_top_up,json=idPaymentGatewayAppotaAmountTopUp,proto3" json:"id_payment_gateway_appota_amount_top_up,omitempty"`
	Amount                            float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Promotion                         float64                `protobuf:"fixed64,3,opt,name=promotion,proto3" json:"promotion,omitempty"`
	Currency                          v12.Currency           `protobuf:"varint,4,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	unknownFields                     protoimpl.UnknownFields
	sizeCache                         protoimpl.SizeCache
}

func (x *BackofficePaymentServiceAppotaAmountTopUp) Reset() {
	*x = BackofficePaymentServiceAppotaAmountTopUp{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceAppotaAmountTopUp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceAppotaAmountTopUp) ProtoMessage() {}

func (x *BackofficePaymentServiceAppotaAmountTopUp) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceAppotaAmountTopUp.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceAppotaAmountTopUp) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{8}
}

func (x *BackofficePaymentServiceAppotaAmountTopUp) GetIdPaymentGatewayAppotaAmountTopUp() string {
	if x != nil {
		return x.IdPaymentGatewayAppotaAmountTopUp
	}
	return ""
}

func (x *BackofficePaymentServiceAppotaAmountTopUp) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *BackofficePaymentServiceAppotaAmountTopUp) GetPromotion() float64 {
	if x != nil {
		return x.Promotion
	}
	return 0
}

func (x *BackofficePaymentServiceAppotaAmountTopUp) GetCurrency() v12.Currency {
	if x != nil {
		return x.Currency
	}
	return v12.Currency(0)
}

type BackofficePaymentServiceCreateSePayPaymentGatewayRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant          string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	VnBank              v12.VNBankType         `protobuf:"varint,2,opt,name=vn_bank,json=vnBank,proto3,enum=algoenum.v1.VNBankType" json:"vn_bank,omitempty"`
	AccountHolderName   string                 `protobuf:"bytes,3,opt,name=account_holder_name,json=accountHolderName,proto3" json:"account_holder_name,omitempty"`
	AccountNumber       string                 `protobuf:"bytes,4,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	Prefix              string                 `protobuf:"bytes,5,opt,name=prefix,proto3" json:"prefix,omitempty"`
	Suffix              string                 `protobuf:"bytes,6,opt,name=suffix,proto3" json:"suffix,omitempty"`
	ApiKey              string                 `protobuf:"bytes,7,opt,name=api_key,json=apiKey,proto3" json:"api_key,omitempty"`
	IsEnableForCustomer bool                   `protobuf:"varint,8,opt,name=is_enable_for_customer,json=isEnableForCustomer,proto3" json:"is_enable_for_customer,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *BackofficePaymentServiceCreateSePayPaymentGatewayRequest) Reset() {
	*x = BackofficePaymentServiceCreateSePayPaymentGatewayRequest{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceCreateSePayPaymentGatewayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceCreateSePayPaymentGatewayRequest) ProtoMessage() {}

func (x *BackofficePaymentServiceCreateSePayPaymentGatewayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceCreateSePayPaymentGatewayRequest.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceCreateSePayPaymentGatewayRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{9}
}

func (x *BackofficePaymentServiceCreateSePayPaymentGatewayRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficePaymentServiceCreateSePayPaymentGatewayRequest) GetVnBank() v12.VNBankType {
	if x != nil {
		return x.VnBank
	}
	return v12.VNBankType(0)
}

func (x *BackofficePaymentServiceCreateSePayPaymentGatewayRequest) GetAccountHolderName() string {
	if x != nil {
		return x.AccountHolderName
	}
	return ""
}

func (x *BackofficePaymentServiceCreateSePayPaymentGatewayRequest) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *BackofficePaymentServiceCreateSePayPaymentGatewayRequest) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *BackofficePaymentServiceCreateSePayPaymentGatewayRequest) GetSuffix() string {
	if x != nil {
		return x.Suffix
	}
	return ""
}

func (x *BackofficePaymentServiceCreateSePayPaymentGatewayRequest) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *BackofficePaymentServiceCreateSePayPaymentGatewayRequest) GetIsEnableForCustomer() bool {
	if x != nil {
		return x.IsEnableForCustomer
	}
	return false
}

type BackofficePaymentServiceCreateSePayPaymentGatewayResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePaymentServiceCreateSePayPaymentGatewayResponse) Reset() {
	*x = BackofficePaymentServiceCreateSePayPaymentGatewayResponse{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceCreateSePayPaymentGatewayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceCreateSePayPaymentGatewayResponse) ProtoMessage() {}

func (x *BackofficePaymentServiceCreateSePayPaymentGatewayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceCreateSePayPaymentGatewayResponse.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceCreateSePayPaymentGatewayResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{10}
}

func (x *BackofficePaymentServiceCreateSePayPaymentGatewayResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficePaymentServiceCreateDodoPaymentGatewayRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant          string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	SigningKey          string                 `protobuf:"bytes,2,opt,name=signing_key,json=signingKey,proto3" json:"signing_key,omitempty"`
	ApiKey              string                 `protobuf:"bytes,3,opt,name=api_key,json=apiKey,proto3" json:"api_key,omitempty"`
	BaseUrl             string                 `protobuf:"bytes,4,opt,name=base_url,json=baseUrl,proto3" json:"base_url,omitempty"`
	IsEnableForCustomer bool                   `protobuf:"varint,5,opt,name=is_enable_for_customer,json=isEnableForCustomer,proto3" json:"is_enable_for_customer,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *BackofficePaymentServiceCreateDodoPaymentGatewayRequest) Reset() {
	*x = BackofficePaymentServiceCreateDodoPaymentGatewayRequest{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceCreateDodoPaymentGatewayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceCreateDodoPaymentGatewayRequest) ProtoMessage() {}

func (x *BackofficePaymentServiceCreateDodoPaymentGatewayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceCreateDodoPaymentGatewayRequest.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceCreateDodoPaymentGatewayRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{11}
}

func (x *BackofficePaymentServiceCreateDodoPaymentGatewayRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficePaymentServiceCreateDodoPaymentGatewayRequest) GetSigningKey() string {
	if x != nil {
		return x.SigningKey
	}
	return ""
}

func (x *BackofficePaymentServiceCreateDodoPaymentGatewayRequest) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *BackofficePaymentServiceCreateDodoPaymentGatewayRequest) GetBaseUrl() string {
	if x != nil {
		return x.BaseUrl
	}
	return ""
}

func (x *BackofficePaymentServiceCreateDodoPaymentGatewayRequest) GetIsEnableForCustomer() bool {
	if x != nil {
		return x.IsEnableForCustomer
	}
	return false
}

type BackofficePaymentServiceCreateDodoPaymentGatewayResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePaymentServiceCreateDodoPaymentGatewayResponse) Reset() {
	*x = BackofficePaymentServiceCreateDodoPaymentGatewayResponse{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceCreateDodoPaymentGatewayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceCreateDodoPaymentGatewayResponse) ProtoMessage() {}

func (x *BackofficePaymentServiceCreateDodoPaymentGatewayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceCreateDodoPaymentGatewayResponse.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceCreateDodoPaymentGatewayResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{12}
}

func (x *BackofficePaymentServiceCreateDodoPaymentGatewayResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficePaymentServiceCreateAppotaPaymentGatewayRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant          string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	PartnerCode         string                 `protobuf:"bytes,2,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	ApiKey              string                 `protobuf:"bytes,3,opt,name=api_key,json=apiKey,proto3" json:"api_key,omitempty"`
	SecretKey           string                 `protobuf:"bytes,4,opt,name=secret_key,json=secretKey,proto3" json:"secret_key,omitempty"`
	IsEnableForCustomer bool                   `protobuf:"varint,5,opt,name=is_enable_for_customer,json=isEnableForCustomer,proto3" json:"is_enable_for_customer,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *BackofficePaymentServiceCreateAppotaPaymentGatewayRequest) Reset() {
	*x = BackofficePaymentServiceCreateAppotaPaymentGatewayRequest{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceCreateAppotaPaymentGatewayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceCreateAppotaPaymentGatewayRequest) ProtoMessage() {}

func (x *BackofficePaymentServiceCreateAppotaPaymentGatewayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceCreateAppotaPaymentGatewayRequest.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceCreateAppotaPaymentGatewayRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{13}
}

func (x *BackofficePaymentServiceCreateAppotaPaymentGatewayRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficePaymentServiceCreateAppotaPaymentGatewayRequest) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *BackofficePaymentServiceCreateAppotaPaymentGatewayRequest) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *BackofficePaymentServiceCreateAppotaPaymentGatewayRequest) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

func (x *BackofficePaymentServiceCreateAppotaPaymentGatewayRequest) GetIsEnableForCustomer() bool {
	if x != nil {
		return x.IsEnableForCustomer
	}
	return false
}

type BackofficePaymentServiceCreateAppotaPaymentGatewayResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePaymentServiceCreateAppotaPaymentGatewayResponse) Reset() {
	*x = BackofficePaymentServiceCreateAppotaPaymentGatewayResponse{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceCreateAppotaPaymentGatewayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceCreateAppotaPaymentGatewayResponse) ProtoMessage() {}

func (x *BackofficePaymentServiceCreateAppotaPaymentGatewayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceCreateAppotaPaymentGatewayResponse.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceCreateAppotaPaymentGatewayResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{14}
}

func (x *BackofficePaymentServiceCreateAppotaPaymentGatewayResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficePaymentServiceCreateSePayAmountTopUpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant    string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	Amount        float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Promotion     float64                `protobuf:"fixed64,3,opt,name=promotion,proto3" json:"promotion,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePaymentServiceCreateSePayAmountTopUpRequest) Reset() {
	*x = BackofficePaymentServiceCreateSePayAmountTopUpRequest{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceCreateSePayAmountTopUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceCreateSePayAmountTopUpRequest) ProtoMessage() {}

func (x *BackofficePaymentServiceCreateSePayAmountTopUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceCreateSePayAmountTopUpRequest.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceCreateSePayAmountTopUpRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{15}
}

func (x *BackofficePaymentServiceCreateSePayAmountTopUpRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficePaymentServiceCreateSePayAmountTopUpRequest) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *BackofficePaymentServiceCreateSePayAmountTopUpRequest) GetPromotion() float64 {
	if x != nil {
		return x.Promotion
	}
	return 0
}

type BackofficePaymentServiceCreateSePayAmountTopUpResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePaymentServiceCreateSePayAmountTopUpResponse) Reset() {
	*x = BackofficePaymentServiceCreateSePayAmountTopUpResponse{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceCreateSePayAmountTopUpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceCreateSePayAmountTopUpResponse) ProtoMessage() {}

func (x *BackofficePaymentServiceCreateSePayAmountTopUpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceCreateSePayAmountTopUpResponse.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceCreateSePayAmountTopUpResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{16}
}

func (x *BackofficePaymentServiceCreateSePayAmountTopUpResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficePaymentServiceCreateAppotaAmountTopUpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant    string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	Amount        float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Promotion     float64                `protobuf:"fixed64,3,opt,name=promotion,proto3" json:"promotion,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePaymentServiceCreateAppotaAmountTopUpRequest) Reset() {
	*x = BackofficePaymentServiceCreateAppotaAmountTopUpRequest{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceCreateAppotaAmountTopUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceCreateAppotaAmountTopUpRequest) ProtoMessage() {}

func (x *BackofficePaymentServiceCreateAppotaAmountTopUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceCreateAppotaAmountTopUpRequest.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceCreateAppotaAmountTopUpRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{17}
}

func (x *BackofficePaymentServiceCreateAppotaAmountTopUpRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficePaymentServiceCreateAppotaAmountTopUpRequest) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *BackofficePaymentServiceCreateAppotaAmountTopUpRequest) GetPromotion() float64 {
	if x != nil {
		return x.Promotion
	}
	return 0
}

type BackofficePaymentServiceCreateAppotaAmountTopUpResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePaymentServiceCreateAppotaAmountTopUpResponse) Reset() {
	*x = BackofficePaymentServiceCreateAppotaAmountTopUpResponse{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceCreateAppotaAmountTopUpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceCreateAppotaAmountTopUpResponse) ProtoMessage() {}

func (x *BackofficePaymentServiceCreateAppotaAmountTopUpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceCreateAppotaAmountTopUpResponse.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceCreateAppotaAmountTopUpResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{18}
}

func (x *BackofficePaymentServiceCreateAppotaAmountTopUpResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficePaymentServiceCreateDodoAmountTopUpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant    string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	ProductIdDodo string                 `protobuf:"bytes,2,opt,name=product_id_dodo,json=productIdDodo,proto3" json:"product_id_dodo,omitempty"`
	Amount        float64                `protobuf:"fixed64,3,opt,name=amount,proto3" json:"amount,omitempty"`
	Promotion     float64                `protobuf:"fixed64,4,opt,name=promotion,proto3" json:"promotion,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePaymentServiceCreateDodoAmountTopUpRequest) Reset() {
	*x = BackofficePaymentServiceCreateDodoAmountTopUpRequest{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceCreateDodoAmountTopUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceCreateDodoAmountTopUpRequest) ProtoMessage() {}

func (x *BackofficePaymentServiceCreateDodoAmountTopUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceCreateDodoAmountTopUpRequest.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceCreateDodoAmountTopUpRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{19}
}

func (x *BackofficePaymentServiceCreateDodoAmountTopUpRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficePaymentServiceCreateDodoAmountTopUpRequest) GetProductIdDodo() string {
	if x != nil {
		return x.ProductIdDodo
	}
	return ""
}

func (x *BackofficePaymentServiceCreateDodoAmountTopUpRequest) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *BackofficePaymentServiceCreateDodoAmountTopUpRequest) GetPromotion() float64 {
	if x != nil {
		return x.Promotion
	}
	return 0
}

type BackofficePaymentServiceCreateDodoAmountTopUpResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePaymentServiceCreateDodoAmountTopUpResponse) Reset() {
	*x = BackofficePaymentServiceCreateDodoAmountTopUpResponse{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceCreateDodoAmountTopUpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceCreateDodoAmountTopUpResponse) ProtoMessage() {}

func (x *BackofficePaymentServiceCreateDodoAmountTopUpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceCreateDodoAmountTopUpResponse.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceCreateDodoAmountTopUpResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{20}
}

func (x *BackofficePaymentServiceCreateDodoAmountTopUpResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGateway string                 `protobuf:"bytes,1,opt,name=id_payment_gateway,json=idPaymentGateway,proto3" json:"id_payment_gateway,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest) Reset() {
	*x = BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest) ProtoMessage() {}

func (x *BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{21}
}

func (x *BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest) GetIdPaymentGateway() string {
	if x != nil {
		return x.IdPaymentGateway
	}
	return ""
}

type BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse struct {
	state                     protoimpl.MessageState                             `protogen:"open.v1"`
	Error                     *v11.ErrorMessage                                  `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	SepayPaymentGatewayDetail *BackofficePaymentServiceSePayPaymentGatewayDetail `protobuf:"bytes,2,opt,name=sepay_payment_gateway_detail,json=sepayPaymentGatewayDetail,proto3" json:"sepay_payment_gateway_detail,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse) Reset() {
	*x = BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse) ProtoMessage() {}

func (x *BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{22}
}

func (x *BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse) GetSepayPaymentGatewayDetail() *BackofficePaymentServiceSePayPaymentGatewayDetail {
	if x != nil {
		return x.SepayPaymentGatewayDetail
	}
	return nil
}

type BackofficePaymentServiceSePayPaymentGatewayDetail struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	BankName          string                 `protobuf:"bytes,1,opt,name=bank_name,json=bankName,proto3" json:"bank_name,omitempty"`
	AccountHolderName string                 `protobuf:"bytes,2,opt,name=account_holder_name,json=accountHolderName,proto3" json:"account_holder_name,omitempty"`
	AccountNumber     string                 `protobuf:"bytes,3,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	Prefix            string                 `protobuf:"bytes,4,opt,name=prefix,proto3" json:"prefix,omitempty"`
	Suffix            string                 `protobuf:"bytes,5,opt,name=suffix,proto3" json:"suffix,omitempty"`
	ApiKey            string                 `protobuf:"bytes,6,opt,name=api_key,json=apiKey,proto3" json:"api_key,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BackofficePaymentServiceSePayPaymentGatewayDetail) Reset() {
	*x = BackofficePaymentServiceSePayPaymentGatewayDetail{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceSePayPaymentGatewayDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceSePayPaymentGatewayDetail) ProtoMessage() {}

func (x *BackofficePaymentServiceSePayPaymentGatewayDetail) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceSePayPaymentGatewayDetail.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceSePayPaymentGatewayDetail) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{23}
}

func (x *BackofficePaymentServiceSePayPaymentGatewayDetail) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

func (x *BackofficePaymentServiceSePayPaymentGatewayDetail) GetAccountHolderName() string {
	if x != nil {
		return x.AccountHolderName
	}
	return ""
}

func (x *BackofficePaymentServiceSePayPaymentGatewayDetail) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *BackofficePaymentServiceSePayPaymentGatewayDetail) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *BackofficePaymentServiceSePayPaymentGatewayDetail) GetSuffix() string {
	if x != nil {
		return x.Suffix
	}
	return ""
}

func (x *BackofficePaymentServiceSePayPaymentGatewayDetail) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

type BackofficePaymentServiceFetchPaymentGatewayTypeRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewayType string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_type,json=idPaymentGatewayType,proto3" json:"id_payment_gateway_type,omitempty"`
	PaymentGatewayType   v12.PaymentGatewayType `protobuf:"varint,2,opt,name=payment_gateway_type,json=paymentGatewayType,proto3,enum=algoenum.v1.PaymentGatewayType" json:"payment_gateway_type,omitempty"`
	Currency             v12.Currency           `protobuf:"varint,3,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	State                *v1.State              `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	Pagination           *v1.PaginationRequest  `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *BackofficePaymentServiceFetchPaymentGatewayTypeRequest) Reset() {
	*x = BackofficePaymentServiceFetchPaymentGatewayTypeRequest{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceFetchPaymentGatewayTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceFetchPaymentGatewayTypeRequest) ProtoMessage() {}

func (x *BackofficePaymentServiceFetchPaymentGatewayTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceFetchPaymentGatewayTypeRequest.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceFetchPaymentGatewayTypeRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{24}
}

func (x *BackofficePaymentServiceFetchPaymentGatewayTypeRequest) GetIdPaymentGatewayType() string {
	if x != nil {
		return x.IdPaymentGatewayType
	}
	return ""
}

func (x *BackofficePaymentServiceFetchPaymentGatewayTypeRequest) GetPaymentGatewayType() v12.PaymentGatewayType {
	if x != nil {
		return x.PaymentGatewayType
	}
	return v12.PaymentGatewayType(0)
}

func (x *BackofficePaymentServiceFetchPaymentGatewayTypeRequest) GetCurrency() v12.Currency {
	if x != nil {
		return x.Currency
	}
	return v12.Currency(0)
}

func (x *BackofficePaymentServiceFetchPaymentGatewayTypeRequest) GetState() *v1.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficePaymentServiceFetchPaymentGatewayTypeRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficePaymentServiceFetchPaymentGatewayTypeResponse struct {
	state               protoimpl.MessageState          `protogen:"open.v1"`
	PaymentGatewayTypes []*BackofficePaymentGatewayType `protobuf:"bytes,1,rep,name=payment_gateway_types,json=paymentGatewayTypes,proto3" json:"payment_gateway_types,omitempty"`
	Error               *v11.ErrorMessage               `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Pagination          *v1.PaginationResponse          `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *BackofficePaymentServiceFetchPaymentGatewayTypeResponse) Reset() {
	*x = BackofficePaymentServiceFetchPaymentGatewayTypeResponse{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceFetchPaymentGatewayTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceFetchPaymentGatewayTypeResponse) ProtoMessage() {}

func (x *BackofficePaymentServiceFetchPaymentGatewayTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceFetchPaymentGatewayTypeResponse.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceFetchPaymentGatewayTypeResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{25}
}

func (x *BackofficePaymentServiceFetchPaymentGatewayTypeResponse) GetPaymentGatewayTypes() []*BackofficePaymentGatewayType {
	if x != nil {
		return x.PaymentGatewayTypes
	}
	return nil
}

func (x *BackofficePaymentServiceFetchPaymentGatewayTypeResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficePaymentServiceFetchPaymentGatewayTypeResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficePaymentGatewayType struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewayType string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_type,json=idPaymentGatewayType,proto3" json:"id_payment_gateway_type,omitempty"`
	PaymentGatewayType   v12.PaymentGatewayType `protobuf:"varint,2,opt,name=payment_gateway_type,json=paymentGatewayType,proto3,enum=algoenum.v1.PaymentGatewayType" json:"payment_gateway_type,omitempty"`
	Currency             v12.Currency           `protobuf:"varint,3,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	IsActive             bool                   `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *BackofficePaymentGatewayType) Reset() {
	*x = BackofficePaymentGatewayType{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentGatewayType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentGatewayType) ProtoMessage() {}

func (x *BackofficePaymentGatewayType) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentGatewayType.ProtoReflect.Descriptor instead.
func (*BackofficePaymentGatewayType) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{26}
}

func (x *BackofficePaymentGatewayType) GetIdPaymentGatewayType() string {
	if x != nil {
		return x.IdPaymentGatewayType
	}
	return ""
}

func (x *BackofficePaymentGatewayType) GetPaymentGatewayType() v12.PaymentGatewayType {
	if x != nil {
		return x.PaymentGatewayType
	}
	return v12.PaymentGatewayType(0)
}

func (x *BackofficePaymentGatewayType) GetCurrency() v12.Currency {
	if x != nil {
		return x.Currency
	}
	return v12.Currency(0)
}

func (x *BackofficePaymentGatewayType) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficePaymentServiceUpdatePaymentGatewayStateRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGateway string                 `protobuf:"bytes,1,opt,name=id_payment_gateway,json=idPaymentGateway,proto3" json:"id_payment_gateway,omitempty"`
	State            *v1.State              `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BackofficePaymentServiceUpdatePaymentGatewayStateRequest) Reset() {
	*x = BackofficePaymentServiceUpdatePaymentGatewayStateRequest{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceUpdatePaymentGatewayStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceUpdatePaymentGatewayStateRequest) ProtoMessage() {}

func (x *BackofficePaymentServiceUpdatePaymentGatewayStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceUpdatePaymentGatewayStateRequest.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceUpdatePaymentGatewayStateRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{27}
}

func (x *BackofficePaymentServiceUpdatePaymentGatewayStateRequest) GetIdPaymentGateway() string {
	if x != nil {
		return x.IdPaymentGateway
	}
	return ""
}

func (x *BackofficePaymentServiceUpdatePaymentGatewayStateRequest) GetState() *v1.State {
	if x != nil {
		return x.State
	}
	return nil
}

type BackofficePaymentServiceUpdatePaymentGatewayStateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePaymentServiceUpdatePaymentGatewayStateResponse) Reset() {
	*x = BackofficePaymentServiceUpdatePaymentGatewayStateResponse{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceUpdatePaymentGatewayStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceUpdatePaymentGatewayStateResponse) ProtoMessage() {}

func (x *BackofficePaymentServiceUpdatePaymentGatewayStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceUpdatePaymentGatewayStateResponse.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceUpdatePaymentGatewayStateResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{28}
}

func (x *BackofficePaymentServiceUpdatePaymentGatewayStateResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficePaymentServiceUpdatePaymentGatewayTypeRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewayType string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_type,json=idPaymentGatewayType,proto3" json:"id_payment_gateway_type,omitempty"`
	State                *v1.State              `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *BackofficePaymentServiceUpdatePaymentGatewayTypeRequest) Reset() {
	*x = BackofficePaymentServiceUpdatePaymentGatewayTypeRequest{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceUpdatePaymentGatewayTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceUpdatePaymentGatewayTypeRequest) ProtoMessage() {}

func (x *BackofficePaymentServiceUpdatePaymentGatewayTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceUpdatePaymentGatewayTypeRequest.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceUpdatePaymentGatewayTypeRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{29}
}

func (x *BackofficePaymentServiceUpdatePaymentGatewayTypeRequest) GetIdPaymentGatewayType() string {
	if x != nil {
		return x.IdPaymentGatewayType
	}
	return ""
}

func (x *BackofficePaymentServiceUpdatePaymentGatewayTypeRequest) GetState() *v1.State {
	if x != nil {
		return x.State
	}
	return nil
}

type BackofficePaymentServiceUpdatePaymentGatewayTypeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePaymentServiceUpdatePaymentGatewayTypeResponse) Reset() {
	*x = BackofficePaymentServiceUpdatePaymentGatewayTypeResponse{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceUpdatePaymentGatewayTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceUpdatePaymentGatewayTypeResponse) ProtoMessage() {}

func (x *BackofficePaymentServiceUpdatePaymentGatewayTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceUpdatePaymentGatewayTypeResponse.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceUpdatePaymentGatewayTypeResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{30}
}

func (x *BackofficePaymentServiceUpdatePaymentGatewayTypeResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficePaymentServicePaymentGatewayMerchant struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant    string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Currency      v12.Currency           `protobuf:"varint,3,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	IsActive      bool                   `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePaymentServicePaymentGatewayMerchant) Reset() {
	*x = BackofficePaymentServicePaymentGatewayMerchant{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServicePaymentGatewayMerchant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServicePaymentGatewayMerchant) ProtoMessage() {}

func (x *BackofficePaymentServicePaymentGatewayMerchant) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServicePaymentGatewayMerchant.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServicePaymentGatewayMerchant) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{31}
}

func (x *BackofficePaymentServicePaymentGatewayMerchant) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficePaymentServicePaymentGatewayMerchant) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficePaymentServicePaymentGatewayMerchant) GetCurrency() v12.Currency {
	if x != nil {
		return x.Currency
	}
	return v12.Currency(0)
}

func (x *BackofficePaymentServicePaymentGatewayMerchant) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficePaymentGateway struct {
	state               protoimpl.MessageState                          `protogen:"open.v1"`
	IdPaymentGateway    string                                          `protobuf:"bytes,1,opt,name=id_payment_gateway,json=idPaymentGateway,proto3" json:"id_payment_gateway,omitempty"`
	Merchant            *BackofficePaymentServicePaymentGatewayMerchant `protobuf:"bytes,2,opt,name=merchant,proto3" json:"merchant,omitempty"`
	PaymentGatewayType  *BackofficePaymentGatewayType                   `protobuf:"bytes,3,opt,name=payment_gateway_type,json=paymentGatewayType,proto3" json:"payment_gateway_type,omitempty"`
	AccountHolderName   string                                          `protobuf:"bytes,4,opt,name=account_holder_name,json=accountHolderName,proto3" json:"account_holder_name,omitempty"`
	IsActive            bool                                            `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	IsEnableForCustomer bool                                            `protobuf:"varint,6,opt,name=is_enable_for_customer,json=isEnableForCustomer,proto3" json:"is_enable_for_customer,omitempty"`
	State               bool                                            `protobuf:"varint,7,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *BackofficePaymentGateway) Reset() {
	*x = BackofficePaymentGateway{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentGateway) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentGateway) ProtoMessage() {}

func (x *BackofficePaymentGateway) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentGateway.ProtoReflect.Descriptor instead.
func (*BackofficePaymentGateway) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{32}
}

func (x *BackofficePaymentGateway) GetIdPaymentGateway() string {
	if x != nil {
		return x.IdPaymentGateway
	}
	return ""
}

func (x *BackofficePaymentGateway) GetMerchant() *BackofficePaymentServicePaymentGatewayMerchant {
	if x != nil {
		return x.Merchant
	}
	return nil
}

func (x *BackofficePaymentGateway) GetPaymentGatewayType() *BackofficePaymentGatewayType {
	if x != nil {
		return x.PaymentGatewayType
	}
	return nil
}

func (x *BackofficePaymentGateway) GetAccountHolderName() string {
	if x != nil {
		return x.AccountHolderName
	}
	return ""
}

func (x *BackofficePaymentGateway) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *BackofficePaymentGateway) GetIsEnableForCustomer() bool {
	if x != nil {
		return x.IsEnableForCustomer
	}
	return false
}

func (x *BackofficePaymentGateway) GetState() bool {
	if x != nil {
		return x.State
	}
	return false
}

type BackofficePaymentServiceFetchPaymentGatewayRequest struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant              string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	IdPaymentGatewayType    string                 `protobuf:"bytes,2,opt,name=id_payment_gateway_type,json=idPaymentGatewayType,proto3" json:"id_payment_gateway_type,omitempty"`
	AccountHolderNameSearch string                 `protobuf:"bytes,3,opt,name=account_holder_name_search,json=accountHolderNameSearch,proto3" json:"account_holder_name_search,omitempty"`
	State                   *v1.State              `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	Pagination              *v1.PaginationRequest  `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *BackofficePaymentServiceFetchPaymentGatewayRequest) Reset() {
	*x = BackofficePaymentServiceFetchPaymentGatewayRequest{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceFetchPaymentGatewayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceFetchPaymentGatewayRequest) ProtoMessage() {}

func (x *BackofficePaymentServiceFetchPaymentGatewayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceFetchPaymentGatewayRequest.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceFetchPaymentGatewayRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{33}
}

func (x *BackofficePaymentServiceFetchPaymentGatewayRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficePaymentServiceFetchPaymentGatewayRequest) GetIdPaymentGatewayType() string {
	if x != nil {
		return x.IdPaymentGatewayType
	}
	return ""
}

func (x *BackofficePaymentServiceFetchPaymentGatewayRequest) GetAccountHolderNameSearch() string {
	if x != nil {
		return x.AccountHolderNameSearch
	}
	return ""
}

func (x *BackofficePaymentServiceFetchPaymentGatewayRequest) GetState() *v1.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficePaymentServiceFetchPaymentGatewayRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficePaymentServiceFetchPaymentGatewayResponse struct {
	state           protoimpl.MessageState      `protogen:"open.v1"`
	PaymentGateways []*BackofficePaymentGateway `protobuf:"bytes,1,rep,name=payment_gateways,json=paymentGateways,proto3" json:"payment_gateways,omitempty"`
	Error           *v11.ErrorMessage           `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Pagination      *v1.PaginationResponse      `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *BackofficePaymentServiceFetchPaymentGatewayResponse) Reset() {
	*x = BackofficePaymentServiceFetchPaymentGatewayResponse{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceFetchPaymentGatewayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceFetchPaymentGatewayResponse) ProtoMessage() {}

func (x *BackofficePaymentServiceFetchPaymentGatewayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceFetchPaymentGatewayResponse.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceFetchPaymentGatewayResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{34}
}

func (x *BackofficePaymentServiceFetchPaymentGatewayResponse) GetPaymentGateways() []*BackofficePaymentGateway {
	if x != nil {
		return x.PaymentGateways
	}
	return nil
}

func (x *BackofficePaymentServiceFetchPaymentGatewayResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficePaymentServiceFetchPaymentGatewayResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGateway string                 `protobuf:"bytes,1,opt,name=id_payment_gateway,json=idPaymentGateway,proto3" json:"id_payment_gateway,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest) Reset() {
	*x = BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest) ProtoMessage() {}

func (x *BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{35}
}

func (x *BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest) GetIdPaymentGateway() string {
	if x != nil {
		return x.IdPaymentGateway
	}
	return ""
}

type BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse struct {
	state                      protoimpl.MessageState                              `protogen:"open.v1"`
	Error                      *v11.ErrorMessage                                   `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	AppotaPaymentGatewayDetail *BackofficePaymentServiceAppotaPaymentGatewayDetail `protobuf:"bytes,2,opt,name=appota_payment_gateway_detail,json=appotaPaymentGatewayDetail,proto3" json:"appota_payment_gateway_detail,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse) Reset() {
	*x = BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse) ProtoMessage() {}

func (x *BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{36}
}

func (x *BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse) GetAppotaPaymentGatewayDetail() *BackofficePaymentServiceAppotaPaymentGatewayDetail {
	if x != nil {
		return x.AppotaPaymentGatewayDetail
	}
	return nil
}

type BackofficePaymentServiceAppotaPaymentGatewayDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PartnerCode   string                 `protobuf:"bytes,1,opt,name=partner_code,json=partnerCode,proto3" json:"partner_code,omitempty"`
	ApiKey        string                 `protobuf:"bytes,2,opt,name=api_key,json=apiKey,proto3" json:"api_key,omitempty"`
	SecretKey     string                 `protobuf:"bytes,3,opt,name=secret_key,json=secretKey,proto3" json:"secret_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePaymentServiceAppotaPaymentGatewayDetail) Reset() {
	*x = BackofficePaymentServiceAppotaPaymentGatewayDetail{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceAppotaPaymentGatewayDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceAppotaPaymentGatewayDetail) ProtoMessage() {}

func (x *BackofficePaymentServiceAppotaPaymentGatewayDetail) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceAppotaPaymentGatewayDetail.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceAppotaPaymentGatewayDetail) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{37}
}

func (x *BackofficePaymentServiceAppotaPaymentGatewayDetail) GetPartnerCode() string {
	if x != nil {
		return x.PartnerCode
	}
	return ""
}

func (x *BackofficePaymentServiceAppotaPaymentGatewayDetail) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *BackofficePaymentServiceAppotaPaymentGatewayDetail) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

type BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGateway string                 `protobuf:"bytes,1,opt,name=id_payment_gateway,json=idPaymentGateway,proto3" json:"id_payment_gateway,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest) Reset() {
	*x = BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest) ProtoMessage() {}

func (x *BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{38}
}

func (x *BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest) GetIdPaymentGateway() string {
	if x != nil {
		return x.IdPaymentGateway
	}
	return ""
}

type BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse struct {
	state                    protoimpl.MessageState                            `protogen:"open.v1"`
	Error                    *v11.ErrorMessage                                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	DodoPaymentGatewayDetail *BackofficePaymentServiceDodoPaymentGatewayDetail `protobuf:"bytes,2,opt,name=dodo_payment_gateway_detail,json=dodoPaymentGatewayDetail,proto3" json:"dodo_payment_gateway_detail,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse) Reset() {
	*x = BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse) ProtoMessage() {}

func (x *BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{39}
}

func (x *BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse) GetDodoPaymentGatewayDetail() *BackofficePaymentServiceDodoPaymentGatewayDetail {
	if x != nil {
		return x.DodoPaymentGatewayDetail
	}
	return nil
}

type BackofficePaymentServiceDodoPaymentGatewayDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SigningKey    string                 `protobuf:"bytes,1,opt,name=signing_key,json=signingKey,proto3" json:"signing_key,omitempty"`
	ApiKey        string                 `protobuf:"bytes,2,opt,name=api_key,json=apiKey,proto3" json:"api_key,omitempty"`
	BaseUrl       string                 `protobuf:"bytes,3,opt,name=base_url,json=baseUrl,proto3" json:"base_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePaymentServiceDodoPaymentGatewayDetail) Reset() {
	*x = BackofficePaymentServiceDodoPaymentGatewayDetail{}
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePaymentServiceDodoPaymentGatewayDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePaymentServiceDodoPaymentGatewayDetail) ProtoMessage() {}

func (x *BackofficePaymentServiceDodoPaymentGatewayDetail) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_backoffice_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePaymentServiceDodoPaymentGatewayDetail.ProtoReflect.Descriptor instead.
func (*BackofficePaymentServiceDodoPaymentGatewayDetail) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_backoffice_proto_rawDescGZIP(), []int{40}
}

func (x *BackofficePaymentServiceDodoPaymentGatewayDetail) GetSigningKey() string {
	if x != nil {
		return x.SigningKey
	}
	return ""
}

func (x *BackofficePaymentServiceDodoPaymentGatewayDetail) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *BackofficePaymentServiceDodoPaymentGatewayDetail) GetBaseUrl() string {
	if x != nil {
		return x.BaseUrl
	}
	return ""
}

var File_billing_payment_v1_backoffice_proto protoreflect.FileDescriptor

const file_billing_payment_v1_backoffice_proto_rawDesc = "" +
	"\n" +
	"#billing/payment/v1/backoffice.proto\x12\x12billing.payment.v1\x1a\x14utils/v1/utils.proto\x1a\x18errmsg/v1/errormsg.proto\x1a\x1aalgoenum/v1/currency.proto\x1a&algoenum/v1/payment_gateway_type.proto\x1a\x19algoenum/v1/vn_bank.proto\"\xbb\x01\n" +
	"4BackofficePaymentServiceFetchSePayAmountTopUpRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12%\n" +
	"\x05state\x18\x02 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x93\x02\n" +
	"5BackofficePaymentServiceFetchSePayAmountTopUpResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12m\n" +
	"\x14sepay_amount_top_ups\x18\x03 \x03(\v2<.billing.payment.v1.BackofficePaymentServiceSePayAmountTopUpR\x11sepayAmountTopUps\"\xe5\x01\n" +
	"(BackofficePaymentServiceSePayAmountTopUp\x12P\n" +
	"&id_payment_gateway_sepay_amount_top_up\x18\x01 \x01(\tR idPaymentGatewaySepayAmountTopUp\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12\x1c\n" +
	"\tpromotion\x18\x03 \x01(\x01R\tpromotion\x121\n" +
	"\bcurrency\x18\x04 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\"\xba\x01\n" +
	"3BackofficePaymentServiceFetchDodoAmountTopUpRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12%\n" +
	"\x05state\x18\x02 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x8f\x02\n" +
	"4BackofficePaymentServiceFetchDodoAmountTopUpResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12j\n" +
	"\x13dodo_amount_top_ups\x18\x03 \x03(\v2;.billing.payment.v1.BackofficePaymentServiceDodoAmountTopUpR\x10dodoAmountTopUps\"\x8a\x02\n" +
	"'BackofficePaymentServiceDodoAmountTopUp\x12N\n" +
	"%id_payment_gateway_dodo_amount_top_up\x18\x01 \x01(\tR\x1fidPaymentGatewayDodoAmountTopUp\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12\x1c\n" +
	"\tpromotion\x18\x03 \x01(\x01R\tpromotion\x12&\n" +
	"\x0fproduct_id_dodo\x18\x04 \x01(\tR\rproductIdDodo\x121\n" +
	"\bcurrency\x18\x05 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\"\xbc\x01\n" +
	"5BackofficePaymentServiceFetchAppotaAmountTopUpRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12%\n" +
	"\x05state\x18\x02 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x97\x02\n" +
	"6BackofficePaymentServiceFetchAppotaAmountTopUpResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12p\n" +
	"\x15appota_amount_top_ups\x18\x03 \x03(\v2=.billing.payment.v1.BackofficePaymentServiceAppotaAmountTopUpR\x12appotaAmountTopUps\"\xe8\x01\n" +
	")BackofficePaymentServiceAppotaAmountTopUp\x12R\n" +
	"'id_payment_gateway_appota_amount_top_up\x18\x01 \x01(\tR!idPaymentGatewayAppotaAmountTopUp\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12\x1c\n" +
	"\tpromotion\x18\x03 \x01(\x01R\tpromotion\x121\n" +
	"\bcurrency\x18\x04 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\"\xe2\x02\n" +
	"8BackofficePaymentServiceCreateSePayPaymentGatewayRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x120\n" +
	"\avn_bank\x18\x02 \x01(\x0e2\x17.algoenum.v1.VNBankTypeR\x06vnBank\x12.\n" +
	"\x13account_holder_name\x18\x03 \x01(\tR\x11accountHolderName\x12%\n" +
	"\x0eaccount_number\x18\x04 \x01(\tR\raccountNumber\x12\x16\n" +
	"\x06prefix\x18\x05 \x01(\tR\x06prefix\x12\x16\n" +
	"\x06suffix\x18\x06 \x01(\tR\x06suffix\x12\x17\n" +
	"\aapi_key\x18\a \x01(\tR\x06apiKey\x123\n" +
	"\x16is_enable_for_customer\x18\b \x01(\bR\x13isEnableForCustomer\"j\n" +
	"9BackofficePaymentServiceCreateSePayPaymentGatewayResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xe4\x01\n" +
	"7BackofficePaymentServiceCreateDodoPaymentGatewayRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12\x1f\n" +
	"\vsigning_key\x18\x02 \x01(\tR\n" +
	"signingKey\x12\x17\n" +
	"\aapi_key\x18\x03 \x01(\tR\x06apiKey\x12\x19\n" +
	"\bbase_url\x18\x04 \x01(\tR\abaseUrl\x123\n" +
	"\x16is_enable_for_customer\x18\x05 \x01(\bR\x13isEnableForCustomer\"i\n" +
	"8BackofficePaymentServiceCreateDodoPaymentGatewayResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xec\x01\n" +
	"9BackofficePaymentServiceCreateAppotaPaymentGatewayRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12!\n" +
	"\fpartner_code\x18\x02 \x01(\tR\vpartnerCode\x12\x17\n" +
	"\aapi_key\x18\x03 \x01(\tR\x06apiKey\x12\x1d\n" +
	"\n" +
	"secret_key\x18\x04 \x01(\tR\tsecretKey\x123\n" +
	"\x16is_enable_for_customer\x18\x05 \x01(\bR\x13isEnableForCustomer\"k\n" +
	":BackofficePaymentServiceCreateAppotaPaymentGatewayResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x8e\x01\n" +
	"5BackofficePaymentServiceCreateSePayAmountTopUpRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12\x1c\n" +
	"\tpromotion\x18\x03 \x01(\x01R\tpromotion\"g\n" +
	"6BackofficePaymentServiceCreateSePayAmountTopUpResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x8f\x01\n" +
	"6BackofficePaymentServiceCreateAppotaAmountTopUpRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12\x1c\n" +
	"\tpromotion\x18\x03 \x01(\x01R\tpromotion\"h\n" +
	"7BackofficePaymentServiceCreateAppotaAmountTopUpResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xb5\x01\n" +
	"4BackofficePaymentServiceCreateDodoAmountTopUpRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12&\n" +
	"\x0fproduct_id_dodo\x18\x02 \x01(\tR\rproductIdDodo\x12\x16\n" +
	"\x06amount\x18\x03 \x01(\x01R\x06amount\x12\x1c\n" +
	"\tpromotion\x18\x04 \x01(\x01R\tpromotion\"f\n" +
	"5BackofficePaymentServiceCreateDodoAmountTopUpResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"m\n" +
	"=BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest\x12,\n" +
	"\x12id_payment_gateway\x18\x01 \x01(\tR\x10idPaymentGateway\"\xf8\x01\n" +
	">BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12\x86\x01\n" +
	"\x1csepay_payment_gateway_detail\x18\x02 \x01(\v2E.billing.payment.v1.BackofficePaymentServiceSePayPaymentGatewayDetailR\x19sepayPaymentGatewayDetail\"\xf0\x01\n" +
	"1BackofficePaymentServiceSePayPaymentGatewayDetail\x12\x1b\n" +
	"\tbank_name\x18\x01 \x01(\tR\bbankName\x12.\n" +
	"\x13account_holder_name\x18\x02 \x01(\tR\x11accountHolderName\x12%\n" +
	"\x0eaccount_number\x18\x03 \x01(\tR\raccountNumber\x12\x16\n" +
	"\x06prefix\x18\x04 \x01(\tR\x06prefix\x12\x16\n" +
	"\x06suffix\x18\x05 \x01(\tR\x06suffix\x12\x17\n" +
	"\aapi_key\x18\x06 \x01(\tR\x06apiKey\"\xd9\x02\n" +
	"6BackofficePaymentServiceFetchPaymentGatewayTypeRequest\x125\n" +
	"\x17id_payment_gateway_type\x18\x01 \x01(\tR\x14idPaymentGatewayType\x12Q\n" +
	"\x14payment_gateway_type\x18\x02 \x01(\x0e2\x1f.algoenum.v1.PaymentGatewayTypeR\x12paymentGatewayType\x121\n" +
	"\bcurrency\x18\x03 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\x12%\n" +
	"\x05state\x18\x04 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x8c\x02\n" +
	"7BackofficePaymentServiceFetchPaymentGatewayTypeResponse\x12d\n" +
	"\x15payment_gateway_types\x18\x01 \x03(\v20.billing.payment.v1.BackofficePaymentGatewayTypeR\x13paymentGatewayTypes\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\xf8\x01\n" +
	"\x1cBackofficePaymentGatewayType\x125\n" +
	"\x17id_payment_gateway_type\x18\x01 \x01(\tR\x14idPaymentGatewayType\x12Q\n" +
	"\x14payment_gateway_type\x18\x02 \x01(\x0e2\x1f.algoenum.v1.PaymentGatewayTypeR\x12paymentGatewayType\x121\n" +
	"\bcurrency\x18\x03 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\x12\x1b\n" +
	"\tis_active\x18\x04 \x01(\bR\bisActive\"\x8f\x01\n" +
	"8BackofficePaymentServiceUpdatePaymentGatewayStateRequest\x12,\n" +
	"\x12id_payment_gateway\x18\x01 \x01(\tR\x10idPaymentGateway\x12%\n" +
	"\x05state\x18\x02 \x01(\v2\x0f.utils.v1.StateR\x05state\"j\n" +
	"9BackofficePaymentServiceUpdatePaymentGatewayStateResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x97\x01\n" +
	"7BackofficePaymentServiceUpdatePaymentGatewayTypeRequest\x125\n" +
	"\x17id_payment_gateway_type\x18\x01 \x01(\tR\x14idPaymentGatewayType\x12%\n" +
	"\x05state\x18\x02 \x01(\v2\x0f.utils.v1.StateR\x05state\"i\n" +
	"8BackofficePaymentServiceUpdatePaymentGatewayTypeResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xb5\x01\n" +
	".BackofficePaymentServicePaymentGatewayMerchant\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x121\n" +
	"\bcurrency\x18\x03 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\x12\x1b\n" +
	"\tis_active\x18\x04 \x01(\bR\bisActive\"\xa4\x03\n" +
	"\x18BackofficePaymentGateway\x12,\n" +
	"\x12id_payment_gateway\x18\x01 \x01(\tR\x10idPaymentGateway\x12^\n" +
	"\bmerchant\x18\x02 \x01(\v2B.billing.payment.v1.BackofficePaymentServicePaymentGatewayMerchantR\bmerchant\x12b\n" +
	"\x14payment_gateway_type\x18\x03 \x01(\v20.billing.payment.v1.BackofficePaymentGatewayTypeR\x12paymentGatewayType\x12.\n" +
	"\x13account_holder_name\x18\x04 \x01(\tR\x11accountHolderName\x12\x1b\n" +
	"\tis_active\x18\x05 \x01(\bR\bisActive\x123\n" +
	"\x16is_enable_for_customer\x18\x06 \x01(\bR\x13isEnableForCustomer\x12\x14\n" +
	"\x05state\x18\a \x01(\bR\x05state\"\xad\x02\n" +
	"2BackofficePaymentServiceFetchPaymentGatewayRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x125\n" +
	"\x17id_payment_gateway_type\x18\x02 \x01(\tR\x14idPaymentGatewayType\x12;\n" +
	"\x1aaccount_holder_name_search\x18\x03 \x01(\tR\x17accountHolderNameSearch\x12%\n" +
	"\x05state\x18\x04 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xfb\x01\n" +
	"3BackofficePaymentServiceFetchPaymentGatewayResponse\x12W\n" +
	"\x10payment_gateways\x18\x01 \x03(\v2,.billing.payment.v1.BackofficePaymentGatewayR\x0fpaymentGateways\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"n\n" +
	">BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest\x12,\n" +
	"\x12id_payment_gateway\x18\x01 \x01(\tR\x10idPaymentGateway\"\xfc\x01\n" +
	"?BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12\x89\x01\n" +
	"\x1dappota_payment_gateway_detail\x18\x02 \x01(\v2F.billing.payment.v1.BackofficePaymentServiceAppotaPaymentGatewayDetailR\x1aappotaPaymentGatewayDetail\"\x8f\x01\n" +
	"2BackofficePaymentServiceAppotaPaymentGatewayDetail\x12!\n" +
	"\fpartner_code\x18\x01 \x01(\tR\vpartnerCode\x12\x17\n" +
	"\aapi_key\x18\x02 \x01(\tR\x06apiKey\x12\x1d\n" +
	"\n" +
	"secret_key\x18\x03 \x01(\tR\tsecretKey\"l\n" +
	"<BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest\x12,\n" +
	"\x12id_payment_gateway\x18\x01 \x01(\tR\x10idPaymentGateway\"\xf4\x01\n" +
	"=BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12\x83\x01\n" +
	"\x1bdodo_payment_gateway_detail\x18\x02 \x01(\v2D.billing.payment.v1.BackofficePaymentServiceDodoPaymentGatewayDetailR\x18dodoPaymentGatewayDetail\"\x87\x01\n" +
	"0BackofficePaymentServiceDodoPaymentGatewayDetail\x12\x1f\n" +
	"\vsigning_key\x18\x01 \x01(\tR\n" +
	"signingKey\x12\x17\n" +
	"\aapi_key\x18\x02 \x01(\tR\x06apiKey\x12\x19\n" +
	"\bbase_url\x18\x03 \x01(\tR\abaseUrl2\x9d\x17\n" +
	"\x18BackofficePaymentService\x12\xb2\x01\n" +
	"\x17FetchPaymentGatewayType\x12J.billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayTypeRequest\x1aK.billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayTypeResponse\x12\xb5\x01\n" +
	"\x18UpdatePaymentGatewayType\x12K.billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayTypeRequest\x1aL.billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayTypeResponse\x12\xa6\x01\n" +
	"\x13FetchPaymentGateway\x12F.billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayRequest\x1aG.billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayResponse\x12\xb8\x01\n" +
	"\x19UpdatePaymentGatewayState\x12L.billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayStateRequest\x1aM.billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayStateResponse\x12\xca\x01\n" +
	"\x1fFetchAppotaPaymentGatewayDetail\x12R.billing.payment.v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest\x1aS.billing.payment.v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse\x12\xc4\x01\n" +
	"\x1dFetchDodoPaymentGatewayDetail\x12P.billing.payment.v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest\x1aQ.billing.payment.v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse\x12\xc7\x01\n" +
	"\x1eFetchSePayPaymentGatewayDetail\x12Q.billing.payment.v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest\x1aR.billing.payment.v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse\x12\xbb\x01\n" +
	"\x1aCreateAppotaPaymentGateway\x12M.billing.payment.v1.BackofficePaymentServiceCreateAppotaPaymentGatewayRequest\x1aN.billing.payment.v1.BackofficePaymentServiceCreateAppotaPaymentGatewayResponse\x12\xb5\x01\n" +
	"\x18CreateDodoPaymentGateway\x12K.billing.payment.v1.BackofficePaymentServiceCreateDodoPaymentGatewayRequest\x1aL.billing.payment.v1.BackofficePaymentServiceCreateDodoPaymentGatewayResponse\x12\xb8\x01\n" +
	"\x19CreateSePayPaymentGateway\x12L.billing.payment.v1.BackofficePaymentServiceCreateSePayPaymentGatewayRequest\x1aM.billing.payment.v1.BackofficePaymentServiceCreateSePayPaymentGatewayResponse\x12\xaf\x01\n" +
	"\x16CreateSePayAmountTopUp\x12I.billing.payment.v1.BackofficePaymentServiceCreateSePayAmountTopUpRequest\x1aJ.billing.payment.v1.BackofficePaymentServiceCreateSePayAmountTopUpResponse\x12\xac\x01\n" +
	"\x15CreateDodoAmountTopUp\x12H.billing.payment.v1.BackofficePaymentServiceCreateDodoAmountTopUpRequest\x1aI.billing.payment.v1.BackofficePaymentServiceCreateDodoAmountTopUpResponse\x12\xb2\x01\n" +
	"\x17CreateAppotaAmountTopUp\x12J.billing.payment.v1.BackofficePaymentServiceCreateAppotaAmountTopUpRequest\x1aK.billing.payment.v1.BackofficePaymentServiceCreateAppotaAmountTopUpResponse\x12\xaf\x01\n" +
	"\x16FetchAppotaAmountTopUp\x12I.billing.payment.v1.BackofficePaymentServiceFetchAppotaAmountTopUpRequest\x1aJ.billing.payment.v1.BackofficePaymentServiceFetchAppotaAmountTopUpResponse\x12\xa9\x01\n" +
	"\x14FetchDodoAmountTopUp\x12G.billing.payment.v1.BackofficePaymentServiceFetchDodoAmountTopUpRequest\x1aH.billing.payment.v1.BackofficePaymentServiceFetchDodoAmountTopUpResponse\x12\xac\x01\n" +
	"\x15FetchSePayAmountTopUp\x12H.billing.payment.v1.BackofficePaymentServiceFetchSePayAmountTopUpRequest\x1aI.billing.payment.v1.BackofficePaymentServiceFetchSePayAmountTopUpResponseBMZKgit.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/payment/v1;paymentv1b\x06proto3"

var (
	file_billing_payment_v1_backoffice_proto_rawDescOnce sync.Once
	file_billing_payment_v1_backoffice_proto_rawDescData []byte
)

func file_billing_payment_v1_backoffice_proto_rawDescGZIP() []byte {
	file_billing_payment_v1_backoffice_proto_rawDescOnce.Do(func() {
		file_billing_payment_v1_backoffice_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_billing_payment_v1_backoffice_proto_rawDesc), len(file_billing_payment_v1_backoffice_proto_rawDesc)))
	})
	return file_billing_payment_v1_backoffice_proto_rawDescData
}

var file_billing_payment_v1_backoffice_proto_msgTypes = make([]protoimpl.MessageInfo, 41)
var file_billing_payment_v1_backoffice_proto_goTypes = []any{
	(*BackofficePaymentServiceFetchSePayAmountTopUpRequest)(nil),            // 0: billing.payment.v1.BackofficePaymentServiceFetchSePayAmountTopUpRequest
	(*BackofficePaymentServiceFetchSePayAmountTopUpResponse)(nil),           // 1: billing.payment.v1.BackofficePaymentServiceFetchSePayAmountTopUpResponse
	(*BackofficePaymentServiceSePayAmountTopUp)(nil),                        // 2: billing.payment.v1.BackofficePaymentServiceSePayAmountTopUp
	(*BackofficePaymentServiceFetchDodoAmountTopUpRequest)(nil),             // 3: billing.payment.v1.BackofficePaymentServiceFetchDodoAmountTopUpRequest
	(*BackofficePaymentServiceFetchDodoAmountTopUpResponse)(nil),            // 4: billing.payment.v1.BackofficePaymentServiceFetchDodoAmountTopUpResponse
	(*BackofficePaymentServiceDodoAmountTopUp)(nil),                         // 5: billing.payment.v1.BackofficePaymentServiceDodoAmountTopUp
	(*BackofficePaymentServiceFetchAppotaAmountTopUpRequest)(nil),           // 6: billing.payment.v1.BackofficePaymentServiceFetchAppotaAmountTopUpRequest
	(*BackofficePaymentServiceFetchAppotaAmountTopUpResponse)(nil),          // 7: billing.payment.v1.BackofficePaymentServiceFetchAppotaAmountTopUpResponse
	(*BackofficePaymentServiceAppotaAmountTopUp)(nil),                       // 8: billing.payment.v1.BackofficePaymentServiceAppotaAmountTopUp
	(*BackofficePaymentServiceCreateSePayPaymentGatewayRequest)(nil),        // 9: billing.payment.v1.BackofficePaymentServiceCreateSePayPaymentGatewayRequest
	(*BackofficePaymentServiceCreateSePayPaymentGatewayResponse)(nil),       // 10: billing.payment.v1.BackofficePaymentServiceCreateSePayPaymentGatewayResponse
	(*BackofficePaymentServiceCreateDodoPaymentGatewayRequest)(nil),         // 11: billing.payment.v1.BackofficePaymentServiceCreateDodoPaymentGatewayRequest
	(*BackofficePaymentServiceCreateDodoPaymentGatewayResponse)(nil),        // 12: billing.payment.v1.BackofficePaymentServiceCreateDodoPaymentGatewayResponse
	(*BackofficePaymentServiceCreateAppotaPaymentGatewayRequest)(nil),       // 13: billing.payment.v1.BackofficePaymentServiceCreateAppotaPaymentGatewayRequest
	(*BackofficePaymentServiceCreateAppotaPaymentGatewayResponse)(nil),      // 14: billing.payment.v1.BackofficePaymentServiceCreateAppotaPaymentGatewayResponse
	(*BackofficePaymentServiceCreateSePayAmountTopUpRequest)(nil),           // 15: billing.payment.v1.BackofficePaymentServiceCreateSePayAmountTopUpRequest
	(*BackofficePaymentServiceCreateSePayAmountTopUpResponse)(nil),          // 16: billing.payment.v1.BackofficePaymentServiceCreateSePayAmountTopUpResponse
	(*BackofficePaymentServiceCreateAppotaAmountTopUpRequest)(nil),          // 17: billing.payment.v1.BackofficePaymentServiceCreateAppotaAmountTopUpRequest
	(*BackofficePaymentServiceCreateAppotaAmountTopUpResponse)(nil),         // 18: billing.payment.v1.BackofficePaymentServiceCreateAppotaAmountTopUpResponse
	(*BackofficePaymentServiceCreateDodoAmountTopUpRequest)(nil),            // 19: billing.payment.v1.BackofficePaymentServiceCreateDodoAmountTopUpRequest
	(*BackofficePaymentServiceCreateDodoAmountTopUpResponse)(nil),           // 20: billing.payment.v1.BackofficePaymentServiceCreateDodoAmountTopUpResponse
	(*BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest)(nil),   // 21: billing.payment.v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest
	(*BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse)(nil),  // 22: billing.payment.v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse
	(*BackofficePaymentServiceSePayPaymentGatewayDetail)(nil),               // 23: billing.payment.v1.BackofficePaymentServiceSePayPaymentGatewayDetail
	(*BackofficePaymentServiceFetchPaymentGatewayTypeRequest)(nil),          // 24: billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayTypeRequest
	(*BackofficePaymentServiceFetchPaymentGatewayTypeResponse)(nil),         // 25: billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayTypeResponse
	(*BackofficePaymentGatewayType)(nil),                                    // 26: billing.payment.v1.BackofficePaymentGatewayType
	(*BackofficePaymentServiceUpdatePaymentGatewayStateRequest)(nil),        // 27: billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayStateRequest
	(*BackofficePaymentServiceUpdatePaymentGatewayStateResponse)(nil),       // 28: billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayStateResponse
	(*BackofficePaymentServiceUpdatePaymentGatewayTypeRequest)(nil),         // 29: billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayTypeRequest
	(*BackofficePaymentServiceUpdatePaymentGatewayTypeResponse)(nil),        // 30: billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayTypeResponse
	(*BackofficePaymentServicePaymentGatewayMerchant)(nil),                  // 31: billing.payment.v1.BackofficePaymentServicePaymentGatewayMerchant
	(*BackofficePaymentGateway)(nil),                                        // 32: billing.payment.v1.BackofficePaymentGateway
	(*BackofficePaymentServiceFetchPaymentGatewayRequest)(nil),              // 33: billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayRequest
	(*BackofficePaymentServiceFetchPaymentGatewayResponse)(nil),             // 34: billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayResponse
	(*BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest)(nil),  // 35: billing.payment.v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest
	(*BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse)(nil), // 36: billing.payment.v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse
	(*BackofficePaymentServiceAppotaPaymentGatewayDetail)(nil),              // 37: billing.payment.v1.BackofficePaymentServiceAppotaPaymentGatewayDetail
	(*BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest)(nil),    // 38: billing.payment.v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest
	(*BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse)(nil),   // 39: billing.payment.v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse
	(*BackofficePaymentServiceDodoPaymentGatewayDetail)(nil),                // 40: billing.payment.v1.BackofficePaymentServiceDodoPaymentGatewayDetail
	(*v1.State)(nil),              // 41: utils.v1.State
	(*v1.PaginationRequest)(nil),  // 42: utils.v1.PaginationRequest
	(*v11.ErrorMessage)(nil),      // 43: errmsg.v1.ErrorMessage
	(*v1.PaginationResponse)(nil), // 44: utils.v1.PaginationResponse
	(v12.Currency)(0),             // 45: algoenum.v1.Currency
	(v12.VNBankType)(0),           // 46: algoenum.v1.VNBankType
	(v12.PaymentGatewayType)(0),   // 47: algoenum.v1.PaymentGatewayType
}
var file_billing_payment_v1_backoffice_proto_depIdxs = []int32{
	41, // 0: billing.payment.v1.BackofficePaymentServiceFetchSePayAmountTopUpRequest.state:type_name -> utils.v1.State
	42, // 1: billing.payment.v1.BackofficePaymentServiceFetchSePayAmountTopUpRequest.pagination:type_name -> utils.v1.PaginationRequest
	43, // 2: billing.payment.v1.BackofficePaymentServiceFetchSePayAmountTopUpResponse.error:type_name -> errmsg.v1.ErrorMessage
	44, // 3: billing.payment.v1.BackofficePaymentServiceFetchSePayAmountTopUpResponse.pagination:type_name -> utils.v1.PaginationResponse
	2,  // 4: billing.payment.v1.BackofficePaymentServiceFetchSePayAmountTopUpResponse.sepay_amount_top_ups:type_name -> billing.payment.v1.BackofficePaymentServiceSePayAmountTopUp
	45, // 5: billing.payment.v1.BackofficePaymentServiceSePayAmountTopUp.currency:type_name -> algoenum.v1.Currency
	41, // 6: billing.payment.v1.BackofficePaymentServiceFetchDodoAmountTopUpRequest.state:type_name -> utils.v1.State
	42, // 7: billing.payment.v1.BackofficePaymentServiceFetchDodoAmountTopUpRequest.pagination:type_name -> utils.v1.PaginationRequest
	43, // 8: billing.payment.v1.BackofficePaymentServiceFetchDodoAmountTopUpResponse.error:type_name -> errmsg.v1.ErrorMessage
	44, // 9: billing.payment.v1.BackofficePaymentServiceFetchDodoAmountTopUpResponse.pagination:type_name -> utils.v1.PaginationResponse
	5,  // 10: billing.payment.v1.BackofficePaymentServiceFetchDodoAmountTopUpResponse.dodo_amount_top_ups:type_name -> billing.payment.v1.BackofficePaymentServiceDodoAmountTopUp
	45, // 11: billing.payment.v1.BackofficePaymentServiceDodoAmountTopUp.currency:type_name -> algoenum.v1.Currency
	41, // 12: billing.payment.v1.BackofficePaymentServiceFetchAppotaAmountTopUpRequest.state:type_name -> utils.v1.State
	42, // 13: billing.payment.v1.BackofficePaymentServiceFetchAppotaAmountTopUpRequest.pagination:type_name -> utils.v1.PaginationRequest
	43, // 14: billing.payment.v1.BackofficePaymentServiceFetchAppotaAmountTopUpResponse.error:type_name -> errmsg.v1.ErrorMessage
	44, // 15: billing.payment.v1.BackofficePaymentServiceFetchAppotaAmountTopUpResponse.pagination:type_name -> utils.v1.PaginationResponse
	8,  // 16: billing.payment.v1.BackofficePaymentServiceFetchAppotaAmountTopUpResponse.appota_amount_top_ups:type_name -> billing.payment.v1.BackofficePaymentServiceAppotaAmountTopUp
	45, // 17: billing.payment.v1.BackofficePaymentServiceAppotaAmountTopUp.currency:type_name -> algoenum.v1.Currency
	46, // 18: billing.payment.v1.BackofficePaymentServiceCreateSePayPaymentGatewayRequest.vn_bank:type_name -> algoenum.v1.VNBankType
	43, // 19: billing.payment.v1.BackofficePaymentServiceCreateSePayPaymentGatewayResponse.error:type_name -> errmsg.v1.ErrorMessage
	43, // 20: billing.payment.v1.BackofficePaymentServiceCreateDodoPaymentGatewayResponse.error:type_name -> errmsg.v1.ErrorMessage
	43, // 21: billing.payment.v1.BackofficePaymentServiceCreateAppotaPaymentGatewayResponse.error:type_name -> errmsg.v1.ErrorMessage
	43, // 22: billing.payment.v1.BackofficePaymentServiceCreateSePayAmountTopUpResponse.error:type_name -> errmsg.v1.ErrorMessage
	43, // 23: billing.payment.v1.BackofficePaymentServiceCreateAppotaAmountTopUpResponse.error:type_name -> errmsg.v1.ErrorMessage
	43, // 24: billing.payment.v1.BackofficePaymentServiceCreateDodoAmountTopUpResponse.error:type_name -> errmsg.v1.ErrorMessage
	43, // 25: billing.payment.v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse.error:type_name -> errmsg.v1.ErrorMessage
	23, // 26: billing.payment.v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse.sepay_payment_gateway_detail:type_name -> billing.payment.v1.BackofficePaymentServiceSePayPaymentGatewayDetail
	47, // 27: billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayTypeRequest.payment_gateway_type:type_name -> algoenum.v1.PaymentGatewayType
	45, // 28: billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayTypeRequest.currency:type_name -> algoenum.v1.Currency
	41, // 29: billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayTypeRequest.state:type_name -> utils.v1.State
	42, // 30: billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayTypeRequest.pagination:type_name -> utils.v1.PaginationRequest
	26, // 31: billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayTypeResponse.payment_gateway_types:type_name -> billing.payment.v1.BackofficePaymentGatewayType
	43, // 32: billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayTypeResponse.error:type_name -> errmsg.v1.ErrorMessage
	44, // 33: billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayTypeResponse.pagination:type_name -> utils.v1.PaginationResponse
	47, // 34: billing.payment.v1.BackofficePaymentGatewayType.payment_gateway_type:type_name -> algoenum.v1.PaymentGatewayType
	45, // 35: billing.payment.v1.BackofficePaymentGatewayType.currency:type_name -> algoenum.v1.Currency
	41, // 36: billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayStateRequest.state:type_name -> utils.v1.State
	43, // 37: billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayStateResponse.error:type_name -> errmsg.v1.ErrorMessage
	41, // 38: billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayTypeRequest.state:type_name -> utils.v1.State
	43, // 39: billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayTypeResponse.error:type_name -> errmsg.v1.ErrorMessage
	45, // 40: billing.payment.v1.BackofficePaymentServicePaymentGatewayMerchant.currency:type_name -> algoenum.v1.Currency
	31, // 41: billing.payment.v1.BackofficePaymentGateway.merchant:type_name -> billing.payment.v1.BackofficePaymentServicePaymentGatewayMerchant
	26, // 42: billing.payment.v1.BackofficePaymentGateway.payment_gateway_type:type_name -> billing.payment.v1.BackofficePaymentGatewayType
	41, // 43: billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayRequest.state:type_name -> utils.v1.State
	42, // 44: billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayRequest.pagination:type_name -> utils.v1.PaginationRequest
	32, // 45: billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayResponse.payment_gateways:type_name -> billing.payment.v1.BackofficePaymentGateway
	43, // 46: billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayResponse.error:type_name -> errmsg.v1.ErrorMessage
	44, // 47: billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayResponse.pagination:type_name -> utils.v1.PaginationResponse
	43, // 48: billing.payment.v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse.error:type_name -> errmsg.v1.ErrorMessage
	37, // 49: billing.payment.v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse.appota_payment_gateway_detail:type_name -> billing.payment.v1.BackofficePaymentServiceAppotaPaymentGatewayDetail
	43, // 50: billing.payment.v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse.error:type_name -> errmsg.v1.ErrorMessage
	40, // 51: billing.payment.v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse.dodo_payment_gateway_detail:type_name -> billing.payment.v1.BackofficePaymentServiceDodoPaymentGatewayDetail
	24, // 52: billing.payment.v1.BackofficePaymentService.FetchPaymentGatewayType:input_type -> billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayTypeRequest
	29, // 53: billing.payment.v1.BackofficePaymentService.UpdatePaymentGatewayType:input_type -> billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayTypeRequest
	33, // 54: billing.payment.v1.BackofficePaymentService.FetchPaymentGateway:input_type -> billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayRequest
	27, // 55: billing.payment.v1.BackofficePaymentService.UpdatePaymentGatewayState:input_type -> billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayStateRequest
	35, // 56: billing.payment.v1.BackofficePaymentService.FetchAppotaPaymentGatewayDetail:input_type -> billing.payment.v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest
	38, // 57: billing.payment.v1.BackofficePaymentService.FetchDodoPaymentGatewayDetail:input_type -> billing.payment.v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest
	21, // 58: billing.payment.v1.BackofficePaymentService.FetchSePayPaymentGatewayDetail:input_type -> billing.payment.v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest
	13, // 59: billing.payment.v1.BackofficePaymentService.CreateAppotaPaymentGateway:input_type -> billing.payment.v1.BackofficePaymentServiceCreateAppotaPaymentGatewayRequest
	11, // 60: billing.payment.v1.BackofficePaymentService.CreateDodoPaymentGateway:input_type -> billing.payment.v1.BackofficePaymentServiceCreateDodoPaymentGatewayRequest
	9,  // 61: billing.payment.v1.BackofficePaymentService.CreateSePayPaymentGateway:input_type -> billing.payment.v1.BackofficePaymentServiceCreateSePayPaymentGatewayRequest
	15, // 62: billing.payment.v1.BackofficePaymentService.CreateSePayAmountTopUp:input_type -> billing.payment.v1.BackofficePaymentServiceCreateSePayAmountTopUpRequest
	19, // 63: billing.payment.v1.BackofficePaymentService.CreateDodoAmountTopUp:input_type -> billing.payment.v1.BackofficePaymentServiceCreateDodoAmountTopUpRequest
	17, // 64: billing.payment.v1.BackofficePaymentService.CreateAppotaAmountTopUp:input_type -> billing.payment.v1.BackofficePaymentServiceCreateAppotaAmountTopUpRequest
	6,  // 65: billing.payment.v1.BackofficePaymentService.FetchAppotaAmountTopUp:input_type -> billing.payment.v1.BackofficePaymentServiceFetchAppotaAmountTopUpRequest
	3,  // 66: billing.payment.v1.BackofficePaymentService.FetchDodoAmountTopUp:input_type -> billing.payment.v1.BackofficePaymentServiceFetchDodoAmountTopUpRequest
	0,  // 67: billing.payment.v1.BackofficePaymentService.FetchSePayAmountTopUp:input_type -> billing.payment.v1.BackofficePaymentServiceFetchSePayAmountTopUpRequest
	25, // 68: billing.payment.v1.BackofficePaymentService.FetchPaymentGatewayType:output_type -> billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayTypeResponse
	30, // 69: billing.payment.v1.BackofficePaymentService.UpdatePaymentGatewayType:output_type -> billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayTypeResponse
	34, // 70: billing.payment.v1.BackofficePaymentService.FetchPaymentGateway:output_type -> billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayResponse
	28, // 71: billing.payment.v1.BackofficePaymentService.UpdatePaymentGatewayState:output_type -> billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayStateResponse
	36, // 72: billing.payment.v1.BackofficePaymentService.FetchAppotaPaymentGatewayDetail:output_type -> billing.payment.v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse
	39, // 73: billing.payment.v1.BackofficePaymentService.FetchDodoPaymentGatewayDetail:output_type -> billing.payment.v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse
	22, // 74: billing.payment.v1.BackofficePaymentService.FetchSePayPaymentGatewayDetail:output_type -> billing.payment.v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse
	14, // 75: billing.payment.v1.BackofficePaymentService.CreateAppotaPaymentGateway:output_type -> billing.payment.v1.BackofficePaymentServiceCreateAppotaPaymentGatewayResponse
	12, // 76: billing.payment.v1.BackofficePaymentService.CreateDodoPaymentGateway:output_type -> billing.payment.v1.BackofficePaymentServiceCreateDodoPaymentGatewayResponse
	10, // 77: billing.payment.v1.BackofficePaymentService.CreateSePayPaymentGateway:output_type -> billing.payment.v1.BackofficePaymentServiceCreateSePayPaymentGatewayResponse
	16, // 78: billing.payment.v1.BackofficePaymentService.CreateSePayAmountTopUp:output_type -> billing.payment.v1.BackofficePaymentServiceCreateSePayAmountTopUpResponse
	20, // 79: billing.payment.v1.BackofficePaymentService.CreateDodoAmountTopUp:output_type -> billing.payment.v1.BackofficePaymentServiceCreateDodoAmountTopUpResponse
	18, // 80: billing.payment.v1.BackofficePaymentService.CreateAppotaAmountTopUp:output_type -> billing.payment.v1.BackofficePaymentServiceCreateAppotaAmountTopUpResponse
	7,  // 81: billing.payment.v1.BackofficePaymentService.FetchAppotaAmountTopUp:output_type -> billing.payment.v1.BackofficePaymentServiceFetchAppotaAmountTopUpResponse
	4,  // 82: billing.payment.v1.BackofficePaymentService.FetchDodoAmountTopUp:output_type -> billing.payment.v1.BackofficePaymentServiceFetchDodoAmountTopUpResponse
	1,  // 83: billing.payment.v1.BackofficePaymentService.FetchSePayAmountTopUp:output_type -> billing.payment.v1.BackofficePaymentServiceFetchSePayAmountTopUpResponse
	68, // [68:84] is the sub-list for method output_type
	52, // [52:68] is the sub-list for method input_type
	52, // [52:52] is the sub-list for extension type_name
	52, // [52:52] is the sub-list for extension extendee
	0,  // [0:52] is the sub-list for field type_name
}

func init() { file_billing_payment_v1_backoffice_proto_init() }
func file_billing_payment_v1_backoffice_proto_init() {
	if File_billing_payment_v1_backoffice_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_billing_payment_v1_backoffice_proto_rawDesc), len(file_billing_payment_v1_backoffice_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   41,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_billing_payment_v1_backoffice_proto_goTypes,
		DependencyIndexes: file_billing_payment_v1_backoffice_proto_depIdxs,
		MessageInfos:      file_billing_payment_v1_backoffice_proto_msgTypes,
	}.Build()
	File_billing_payment_v1_backoffice_proto = out.File
	file_billing_payment_v1_backoffice_proto_goTypes = nil
	file_billing_payment_v1_backoffice_proto_depIdxs = nil
}
