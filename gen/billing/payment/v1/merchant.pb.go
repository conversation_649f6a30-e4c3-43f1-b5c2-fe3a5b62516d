// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: billing/payment/v1/merchant.proto

package paymentv1

import (
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MerchantPaymentServiceCreateDodoPaymentRequest struct {
	state                           protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewayDodoAmountTopUp string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_dodo_amount_top_up,json=idPaymentGatewayDodoAmountTopUp,proto3" json:"id_payment_gateway_dodo_amount_top_up,omitempty"`
	FirstName                       string                 `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName                        string                 `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	UserCity                        string                 `protobuf:"bytes,4,opt,name=user_city,json=userCity,proto3" json:"user_city,omitempty"`
	UserCountry                     string                 `protobuf:"bytes,5,opt,name=user_country,json=userCountry,proto3" json:"user_country,omitempty"`
	UserState                       string                 `protobuf:"bytes,6,opt,name=user_state,json=userState,proto3" json:"user_state,omitempty"`
	UserPhone                       string                 `protobuf:"bytes,7,opt,name=user_phone,json=userPhone,proto3" json:"user_phone,omitempty"`
	UserStreet                      string                 `protobuf:"bytes,8,opt,name=user_street,json=userStreet,proto3" json:"user_street,omitempty"`
	UserZipcode                     string                 `protobuf:"bytes,9,opt,name=user_zipcode,json=userZipcode,proto3" json:"user_zipcode,omitempty"`
	unknownFields                   protoimpl.UnknownFields
	sizeCache                       protoimpl.SizeCache
}

func (x *MerchantPaymentServiceCreateDodoPaymentRequest) Reset() {
	*x = MerchantPaymentServiceCreateDodoPaymentRequest{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceCreateDodoPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceCreateDodoPaymentRequest) ProtoMessage() {}

func (x *MerchantPaymentServiceCreateDodoPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceCreateDodoPaymentRequest.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceCreateDodoPaymentRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{0}
}

func (x *MerchantPaymentServiceCreateDodoPaymentRequest) GetIdPaymentGatewayDodoAmountTopUp() string {
	if x != nil {
		return x.IdPaymentGatewayDodoAmountTopUp
	}
	return ""
}

func (x *MerchantPaymentServiceCreateDodoPaymentRequest) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *MerchantPaymentServiceCreateDodoPaymentRequest) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *MerchantPaymentServiceCreateDodoPaymentRequest) GetUserCity() string {
	if x != nil {
		return x.UserCity
	}
	return ""
}

func (x *MerchantPaymentServiceCreateDodoPaymentRequest) GetUserCountry() string {
	if x != nil {
		return x.UserCountry
	}
	return ""
}

func (x *MerchantPaymentServiceCreateDodoPaymentRequest) GetUserState() string {
	if x != nil {
		return x.UserState
	}
	return ""
}

func (x *MerchantPaymentServiceCreateDodoPaymentRequest) GetUserPhone() string {
	if x != nil {
		return x.UserPhone
	}
	return ""
}

func (x *MerchantPaymentServiceCreateDodoPaymentRequest) GetUserStreet() string {
	if x != nil {
		return x.UserStreet
	}
	return ""
}

func (x *MerchantPaymentServiceCreateDodoPaymentRequest) GetUserZipcode() string {
	if x != nil {
		return x.UserZipcode
	}
	return ""
}

type MerchantPaymentServiceCreateDodoPaymentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	PaymentLink   string                 `protobuf:"bytes,2,opt,name=payment_link,json=paymentLink,proto3" json:"payment_link,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantPaymentServiceCreateDodoPaymentResponse) Reset() {
	*x = MerchantPaymentServiceCreateDodoPaymentResponse{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceCreateDodoPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceCreateDodoPaymentResponse) ProtoMessage() {}

func (x *MerchantPaymentServiceCreateDodoPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceCreateDodoPaymentResponse.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceCreateDodoPaymentResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{1}
}

func (x *MerchantPaymentServiceCreateDodoPaymentResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantPaymentServiceCreateDodoPaymentResponse) GetPaymentLink() string {
	if x != nil {
		return x.PaymentLink
	}
	return ""
}

type MerchantPaymentServiceCreateSePayPaymentRequest struct {
	state                            protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewaySepayAmountTopUp string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_sepay_amount_top_up,json=idPaymentGatewaySepayAmountTopUp,proto3" json:"id_payment_gateway_sepay_amount_top_up,omitempty"`
	unknownFields                    protoimpl.UnknownFields
	sizeCache                        protoimpl.SizeCache
}

func (x *MerchantPaymentServiceCreateSePayPaymentRequest) Reset() {
	*x = MerchantPaymentServiceCreateSePayPaymentRequest{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceCreateSePayPaymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceCreateSePayPaymentRequest) ProtoMessage() {}

func (x *MerchantPaymentServiceCreateSePayPaymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceCreateSePayPaymentRequest.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceCreateSePayPaymentRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{2}
}

func (x *MerchantPaymentServiceCreateSePayPaymentRequest) GetIdPaymentGatewaySepayAmountTopUp() string {
	if x != nil {
		return x.IdPaymentGatewaySepayAmountTopUp
	}
	return ""
}

type MerchantPaymentServiceCreateSePayPaymentResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Error             *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	ImageQr           string                 `protobuf:"bytes,2,opt,name=image_qr,json=imageQr,proto3" json:"image_qr,omitempty"`
	BankType          v11.VNBankType         `protobuf:"varint,3,opt,name=bank_type,json=bankType,proto3,enum=algoenum.v1.VNBankType" json:"bank_type,omitempty"`
	AccountHolderName string                 `protobuf:"bytes,4,opt,name=account_holder_name,json=accountHolderName,proto3" json:"account_holder_name,omitempty"`
	AccountNumber     string                 `protobuf:"bytes,5,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	PaymentDesc       string                 `protobuf:"bytes,6,opt,name=payment_desc,json=paymentDesc,proto3" json:"payment_desc,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *MerchantPaymentServiceCreateSePayPaymentResponse) Reset() {
	*x = MerchantPaymentServiceCreateSePayPaymentResponse{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceCreateSePayPaymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceCreateSePayPaymentResponse) ProtoMessage() {}

func (x *MerchantPaymentServiceCreateSePayPaymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceCreateSePayPaymentResponse.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceCreateSePayPaymentResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{3}
}

func (x *MerchantPaymentServiceCreateSePayPaymentResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantPaymentServiceCreateSePayPaymentResponse) GetImageQr() string {
	if x != nil {
		return x.ImageQr
	}
	return ""
}

func (x *MerchantPaymentServiceCreateSePayPaymentResponse) GetBankType() v11.VNBankType {
	if x != nil {
		return x.BankType
	}
	return v11.VNBankType(0)
}

func (x *MerchantPaymentServiceCreateSePayPaymentResponse) GetAccountHolderName() string {
	if x != nil {
		return x.AccountHolderName
	}
	return ""
}

func (x *MerchantPaymentServiceCreateSePayPaymentResponse) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *MerchantPaymentServiceCreateSePayPaymentResponse) GetPaymentDesc() string {
	if x != nil {
		return x.PaymentDesc
	}
	return ""
}

type MerchantPaymentServiceFetchSePayAmountTopUpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	State         *v12.State             `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v12.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantPaymentServiceFetchSePayAmountTopUpRequest) Reset() {
	*x = MerchantPaymentServiceFetchSePayAmountTopUpRequest{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceFetchSePayAmountTopUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceFetchSePayAmountTopUpRequest) ProtoMessage() {}

func (x *MerchantPaymentServiceFetchSePayAmountTopUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceFetchSePayAmountTopUpRequest.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceFetchSePayAmountTopUpRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{4}
}

func (x *MerchantPaymentServiceFetchSePayAmountTopUpRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *MerchantPaymentServiceFetchSePayAmountTopUpRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantPaymentServiceFetchSePayAmountTopUpResponse struct {
	state             protoimpl.MessageState                    `protogen:"open.v1"`
	Error             *v1.ErrorMessage                          `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination        *v12.PaginationResponse                   `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	SepayAmountTopUps []*MerchantPaymentServiceSePayAmountTopUp `protobuf:"bytes,3,rep,name=sepay_amount_top_ups,json=sepayAmountTopUps,proto3" json:"sepay_amount_top_ups,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *MerchantPaymentServiceFetchSePayAmountTopUpResponse) Reset() {
	*x = MerchantPaymentServiceFetchSePayAmountTopUpResponse{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceFetchSePayAmountTopUpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceFetchSePayAmountTopUpResponse) ProtoMessage() {}

func (x *MerchantPaymentServiceFetchSePayAmountTopUpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceFetchSePayAmountTopUpResponse.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceFetchSePayAmountTopUpResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{5}
}

func (x *MerchantPaymentServiceFetchSePayAmountTopUpResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantPaymentServiceFetchSePayAmountTopUpResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *MerchantPaymentServiceFetchSePayAmountTopUpResponse) GetSepayAmountTopUps() []*MerchantPaymentServiceSePayAmountTopUp {
	if x != nil {
		return x.SepayAmountTopUps
	}
	return nil
}

type MerchantPaymentServiceSePayAmountTopUp struct {
	state                            protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewaySepayAmountTopUp string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_sepay_amount_top_up,json=idPaymentGatewaySepayAmountTopUp,proto3" json:"id_payment_gateway_sepay_amount_top_up,omitempty"`
	Amount                           float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Promotion                        float64                `protobuf:"fixed64,3,opt,name=promotion,proto3" json:"promotion,omitempty"`
	Currency                         v11.Currency           `protobuf:"varint,4,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	unknownFields                    protoimpl.UnknownFields
	sizeCache                        protoimpl.SizeCache
}

func (x *MerchantPaymentServiceSePayAmountTopUp) Reset() {
	*x = MerchantPaymentServiceSePayAmountTopUp{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceSePayAmountTopUp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceSePayAmountTopUp) ProtoMessage() {}

func (x *MerchantPaymentServiceSePayAmountTopUp) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceSePayAmountTopUp.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceSePayAmountTopUp) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{6}
}

func (x *MerchantPaymentServiceSePayAmountTopUp) GetIdPaymentGatewaySepayAmountTopUp() string {
	if x != nil {
		return x.IdPaymentGatewaySepayAmountTopUp
	}
	return ""
}

func (x *MerchantPaymentServiceSePayAmountTopUp) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *MerchantPaymentServiceSePayAmountTopUp) GetPromotion() float64 {
	if x != nil {
		return x.Promotion
	}
	return 0
}

func (x *MerchantPaymentServiceSePayAmountTopUp) GetCurrency() v11.Currency {
	if x != nil {
		return x.Currency
	}
	return v11.Currency(0)
}

type MerchantPaymentServiceFetchSePayPaymentGatewayDetailRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGateway string                 `protobuf:"bytes,1,opt,name=id_payment_gateway,json=idPaymentGateway,proto3" json:"id_payment_gateway,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *MerchantPaymentServiceFetchSePayPaymentGatewayDetailRequest) Reset() {
	*x = MerchantPaymentServiceFetchSePayPaymentGatewayDetailRequest{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceFetchSePayPaymentGatewayDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceFetchSePayPaymentGatewayDetailRequest) ProtoMessage() {}

func (x *MerchantPaymentServiceFetchSePayPaymentGatewayDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceFetchSePayPaymentGatewayDetailRequest.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceFetchSePayPaymentGatewayDetailRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{7}
}

func (x *MerchantPaymentServiceFetchSePayPaymentGatewayDetailRequest) GetIdPaymentGateway() string {
	if x != nil {
		return x.IdPaymentGateway
	}
	return ""
}

type MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponse struct {
	state                     protoimpl.MessageState                           `protogen:"open.v1"`
	Error                     *v1.ErrorMessage                                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	SepayPaymentGatewayDetail *MerchantPaymentServiceSePayPaymentGatewayDetail `protobuf:"bytes,2,opt,name=sepay_payment_gateway_detail,json=sepayPaymentGatewayDetail,proto3" json:"sepay_payment_gateway_detail,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponse) Reset() {
	*x = MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponse{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponse) ProtoMessage() {}

func (x *MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponse.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{8}
}

func (x *MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponse) GetSepayPaymentGatewayDetail() *MerchantPaymentServiceSePayPaymentGatewayDetail {
	if x != nil {
		return x.SepayPaymentGatewayDetail
	}
	return nil
}

type MerchantPaymentServiceSePayPaymentGatewayDetail struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	VnBank            v11.VNBankType         `protobuf:"varint,1,opt,name=vn_bank,json=vnBank,proto3,enum=algoenum.v1.VNBankType" json:"vn_bank,omitempty"`
	AccountHolderName string                 `protobuf:"bytes,2,opt,name=account_holder_name,json=accountHolderName,proto3" json:"account_holder_name,omitempty"`
	AccountNumber     string                 `protobuf:"bytes,3,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	Prefix            string                 `protobuf:"bytes,4,opt,name=prefix,proto3" json:"prefix,omitempty"`
	Suffix            string                 `protobuf:"bytes,5,opt,name=suffix,proto3" json:"suffix,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *MerchantPaymentServiceSePayPaymentGatewayDetail) Reset() {
	*x = MerchantPaymentServiceSePayPaymentGatewayDetail{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceSePayPaymentGatewayDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceSePayPaymentGatewayDetail) ProtoMessage() {}

func (x *MerchantPaymentServiceSePayPaymentGatewayDetail) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceSePayPaymentGatewayDetail.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceSePayPaymentGatewayDetail) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{9}
}

func (x *MerchantPaymentServiceSePayPaymentGatewayDetail) GetVnBank() v11.VNBankType {
	if x != nil {
		return x.VnBank
	}
	return v11.VNBankType(0)
}

func (x *MerchantPaymentServiceSePayPaymentGatewayDetail) GetAccountHolderName() string {
	if x != nil {
		return x.AccountHolderName
	}
	return ""
}

func (x *MerchantPaymentServiceSePayPaymentGatewayDetail) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *MerchantPaymentServiceSePayPaymentGatewayDetail) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *MerchantPaymentServiceSePayPaymentGatewayDetail) GetSuffix() string {
	if x != nil {
		return x.Suffix
	}
	return ""
}

type MerchantPaymentServiceFetchDodoAmountTopUpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	State         *v12.State             `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v12.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantPaymentServiceFetchDodoAmountTopUpRequest) Reset() {
	*x = MerchantPaymentServiceFetchDodoAmountTopUpRequest{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceFetchDodoAmountTopUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceFetchDodoAmountTopUpRequest) ProtoMessage() {}

func (x *MerchantPaymentServiceFetchDodoAmountTopUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceFetchDodoAmountTopUpRequest.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceFetchDodoAmountTopUpRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{10}
}

func (x *MerchantPaymentServiceFetchDodoAmountTopUpRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *MerchantPaymentServiceFetchDodoAmountTopUpRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantPaymentServiceFetchDodoAmountTopUpResponse struct {
	state            protoimpl.MessageState                   `protogen:"open.v1"`
	Error            *v1.ErrorMessage                         `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination       *v12.PaginationResponse                  `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	DodoAmountTopUps []*MerchantPaymentServiceDodoAmountTopUp `protobuf:"bytes,3,rep,name=dodo_amount_top_ups,json=dodoAmountTopUps,proto3" json:"dodo_amount_top_ups,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *MerchantPaymentServiceFetchDodoAmountTopUpResponse) Reset() {
	*x = MerchantPaymentServiceFetchDodoAmountTopUpResponse{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceFetchDodoAmountTopUpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceFetchDodoAmountTopUpResponse) ProtoMessage() {}

func (x *MerchantPaymentServiceFetchDodoAmountTopUpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceFetchDodoAmountTopUpResponse.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceFetchDodoAmountTopUpResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{11}
}

func (x *MerchantPaymentServiceFetchDodoAmountTopUpResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantPaymentServiceFetchDodoAmountTopUpResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *MerchantPaymentServiceFetchDodoAmountTopUpResponse) GetDodoAmountTopUps() []*MerchantPaymentServiceDodoAmountTopUp {
	if x != nil {
		return x.DodoAmountTopUps
	}
	return nil
}

type MerchantPaymentServiceDodoAmountTopUp struct {
	state                           protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewayDodoAmountTopUp string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_dodo_amount_top_up,json=idPaymentGatewayDodoAmountTopUp,proto3" json:"id_payment_gateway_dodo_amount_top_up,omitempty"`
	Amount                          float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Promotion                       float64                `protobuf:"fixed64,3,opt,name=promotion,proto3" json:"promotion,omitempty"`
	ProductIdDodo                   string                 `protobuf:"bytes,4,opt,name=product_id_dodo,json=productIdDodo,proto3" json:"product_id_dodo,omitempty"`
	Currency                        v11.Currency           `protobuf:"varint,5,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	unknownFields                   protoimpl.UnknownFields
	sizeCache                       protoimpl.SizeCache
}

func (x *MerchantPaymentServiceDodoAmountTopUp) Reset() {
	*x = MerchantPaymentServiceDodoAmountTopUp{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceDodoAmountTopUp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceDodoAmountTopUp) ProtoMessage() {}

func (x *MerchantPaymentServiceDodoAmountTopUp) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceDodoAmountTopUp.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceDodoAmountTopUp) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{12}
}

func (x *MerchantPaymentServiceDodoAmountTopUp) GetIdPaymentGatewayDodoAmountTopUp() string {
	if x != nil {
		return x.IdPaymentGatewayDodoAmountTopUp
	}
	return ""
}

func (x *MerchantPaymentServiceDodoAmountTopUp) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *MerchantPaymentServiceDodoAmountTopUp) GetPromotion() float64 {
	if x != nil {
		return x.Promotion
	}
	return 0
}

func (x *MerchantPaymentServiceDodoAmountTopUp) GetProductIdDodo() string {
	if x != nil {
		return x.ProductIdDodo
	}
	return ""
}

func (x *MerchantPaymentServiceDodoAmountTopUp) GetCurrency() v11.Currency {
	if x != nil {
		return x.Currency
	}
	return v11.Currency(0)
}

type MerchantPaymentServiceFetchAppotaAmountTopUpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	State         *v12.State             `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v12.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantPaymentServiceFetchAppotaAmountTopUpRequest) Reset() {
	*x = MerchantPaymentServiceFetchAppotaAmountTopUpRequest{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceFetchAppotaAmountTopUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceFetchAppotaAmountTopUpRequest) ProtoMessage() {}

func (x *MerchantPaymentServiceFetchAppotaAmountTopUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceFetchAppotaAmountTopUpRequest.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceFetchAppotaAmountTopUpRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{13}
}

func (x *MerchantPaymentServiceFetchAppotaAmountTopUpRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *MerchantPaymentServiceFetchAppotaAmountTopUpRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantPaymentServiceFetchAppotaAmountTopUpResponse struct {
	state              protoimpl.MessageState                     `protogen:"open.v1"`
	Error              *v1.ErrorMessage                           `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination         *v12.PaginationResponse                    `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	AppotaAmountTopUps []*MerchantPaymentServiceAppotaAmountTopUp `protobuf:"bytes,3,rep,name=appota_amount_top_ups,json=appotaAmountTopUps,proto3" json:"appota_amount_top_ups,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *MerchantPaymentServiceFetchAppotaAmountTopUpResponse) Reset() {
	*x = MerchantPaymentServiceFetchAppotaAmountTopUpResponse{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceFetchAppotaAmountTopUpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceFetchAppotaAmountTopUpResponse) ProtoMessage() {}

func (x *MerchantPaymentServiceFetchAppotaAmountTopUpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceFetchAppotaAmountTopUpResponse.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceFetchAppotaAmountTopUpResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{14}
}

func (x *MerchantPaymentServiceFetchAppotaAmountTopUpResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantPaymentServiceFetchAppotaAmountTopUpResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *MerchantPaymentServiceFetchAppotaAmountTopUpResponse) GetAppotaAmountTopUps() []*MerchantPaymentServiceAppotaAmountTopUp {
	if x != nil {
		return x.AppotaAmountTopUps
	}
	return nil
}

type MerchantPaymentServiceAppotaAmountTopUp struct {
	state                             protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewayAppotaAmountTopUp string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_appota_amount_top_up,json=idPaymentGatewayAppotaAmountTopUp,proto3" json:"id_payment_gateway_appota_amount_top_up,omitempty"`
	Amount                            float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Promotion                         float64                `protobuf:"fixed64,3,opt,name=promotion,proto3" json:"promotion,omitempty"`
	Currency                          v11.Currency           `protobuf:"varint,4,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	unknownFields                     protoimpl.UnknownFields
	sizeCache                         protoimpl.SizeCache
}

func (x *MerchantPaymentServiceAppotaAmountTopUp) Reset() {
	*x = MerchantPaymentServiceAppotaAmountTopUp{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceAppotaAmountTopUp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceAppotaAmountTopUp) ProtoMessage() {}

func (x *MerchantPaymentServiceAppotaAmountTopUp) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceAppotaAmountTopUp.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceAppotaAmountTopUp) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{15}
}

func (x *MerchantPaymentServiceAppotaAmountTopUp) GetIdPaymentGatewayAppotaAmountTopUp() string {
	if x != nil {
		return x.IdPaymentGatewayAppotaAmountTopUp
	}
	return ""
}

func (x *MerchantPaymentServiceAppotaAmountTopUp) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *MerchantPaymentServiceAppotaAmountTopUp) GetPromotion() float64 {
	if x != nil {
		return x.Promotion
	}
	return 0
}

func (x *MerchantPaymentServiceAppotaAmountTopUp) GetCurrency() v11.Currency {
	if x != nil {
		return x.Currency
	}
	return v11.Currency(0)
}

type MerchantPaymentServiceUpdatePaymentGatewayStateRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGateway string                 `protobuf:"bytes,1,opt,name=id_payment_gateway,json=idPaymentGateway,proto3" json:"id_payment_gateway,omitempty"`
	State            *v12.State             `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *MerchantPaymentServiceUpdatePaymentGatewayStateRequest) Reset() {
	*x = MerchantPaymentServiceUpdatePaymentGatewayStateRequest{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceUpdatePaymentGatewayStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceUpdatePaymentGatewayStateRequest) ProtoMessage() {}

func (x *MerchantPaymentServiceUpdatePaymentGatewayStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceUpdatePaymentGatewayStateRequest.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceUpdatePaymentGatewayStateRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{16}
}

func (x *MerchantPaymentServiceUpdatePaymentGatewayStateRequest) GetIdPaymentGateway() string {
	if x != nil {
		return x.IdPaymentGateway
	}
	return ""
}

func (x *MerchantPaymentServiceUpdatePaymentGatewayStateRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

type MerchantPaymentServiceUpdatePaymentGatewayStateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantPaymentServiceUpdatePaymentGatewayStateResponse) Reset() {
	*x = MerchantPaymentServiceUpdatePaymentGatewayStateResponse{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceUpdatePaymentGatewayStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceUpdatePaymentGatewayStateResponse) ProtoMessage() {}

func (x *MerchantPaymentServiceUpdatePaymentGatewayStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceUpdatePaymentGatewayStateResponse.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceUpdatePaymentGatewayStateResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{17}
}

func (x *MerchantPaymentServiceUpdatePaymentGatewayStateResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type MerchantPaymentServiceFetchPaymentGatewayTypeRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewayType string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_type,json=idPaymentGatewayType,proto3" json:"id_payment_gateway_type,omitempty"`
	PaymentGatewayType   v11.PaymentGatewayType `protobuf:"varint,2,opt,name=payment_gateway_type,json=paymentGatewayType,proto3,enum=algoenum.v1.PaymentGatewayType" json:"payment_gateway_type,omitempty"`
	Currency             v11.Currency           `protobuf:"varint,3,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	State                *v12.State             `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	Pagination           *v12.PaginationRequest `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *MerchantPaymentServiceFetchPaymentGatewayTypeRequest) Reset() {
	*x = MerchantPaymentServiceFetchPaymentGatewayTypeRequest{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceFetchPaymentGatewayTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceFetchPaymentGatewayTypeRequest) ProtoMessage() {}

func (x *MerchantPaymentServiceFetchPaymentGatewayTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceFetchPaymentGatewayTypeRequest.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceFetchPaymentGatewayTypeRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{18}
}

func (x *MerchantPaymentServiceFetchPaymentGatewayTypeRequest) GetIdPaymentGatewayType() string {
	if x != nil {
		return x.IdPaymentGatewayType
	}
	return ""
}

func (x *MerchantPaymentServiceFetchPaymentGatewayTypeRequest) GetPaymentGatewayType() v11.PaymentGatewayType {
	if x != nil {
		return x.PaymentGatewayType
	}
	return v11.PaymentGatewayType(0)
}

func (x *MerchantPaymentServiceFetchPaymentGatewayTypeRequest) GetCurrency() v11.Currency {
	if x != nil {
		return x.Currency
	}
	return v11.Currency(0)
}

func (x *MerchantPaymentServiceFetchPaymentGatewayTypeRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *MerchantPaymentServiceFetchPaymentGatewayTypeRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantPaymentServiceFetchPaymentGatewayTypeResponse struct {
	state               protoimpl.MessageState                      `protogen:"open.v1"`
	PaymentGatewayTypes []*MerchantPaymentServicePaymentGatewayType `protobuf:"bytes,1,rep,name=payment_gateway_types,json=paymentGatewayTypes,proto3" json:"payment_gateway_types,omitempty"`
	Error               *v1.ErrorMessage                            `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Pagination          *v12.PaginationResponse                     `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *MerchantPaymentServiceFetchPaymentGatewayTypeResponse) Reset() {
	*x = MerchantPaymentServiceFetchPaymentGatewayTypeResponse{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceFetchPaymentGatewayTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceFetchPaymentGatewayTypeResponse) ProtoMessage() {}

func (x *MerchantPaymentServiceFetchPaymentGatewayTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceFetchPaymentGatewayTypeResponse.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceFetchPaymentGatewayTypeResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{19}
}

func (x *MerchantPaymentServiceFetchPaymentGatewayTypeResponse) GetPaymentGatewayTypes() []*MerchantPaymentServicePaymentGatewayType {
	if x != nil {
		return x.PaymentGatewayTypes
	}
	return nil
}

func (x *MerchantPaymentServiceFetchPaymentGatewayTypeResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantPaymentServiceFetchPaymentGatewayTypeResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantPaymentServicePaymentGatewayType struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewayType string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_type,json=idPaymentGatewayType,proto3" json:"id_payment_gateway_type,omitempty"`
	PaymentGatewayType   v11.PaymentGatewayType `protobuf:"varint,2,opt,name=payment_gateway_type,json=paymentGatewayType,proto3,enum=algoenum.v1.PaymentGatewayType" json:"payment_gateway_type,omitempty"`
	Name                 string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *MerchantPaymentServicePaymentGatewayType) Reset() {
	*x = MerchantPaymentServicePaymentGatewayType{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServicePaymentGatewayType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServicePaymentGatewayType) ProtoMessage() {}

func (x *MerchantPaymentServicePaymentGatewayType) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServicePaymentGatewayType.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServicePaymentGatewayType) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{20}
}

func (x *MerchantPaymentServicePaymentGatewayType) GetIdPaymentGatewayType() string {
	if x != nil {
		return x.IdPaymentGatewayType
	}
	return ""
}

func (x *MerchantPaymentServicePaymentGatewayType) GetPaymentGatewayType() v11.PaymentGatewayType {
	if x != nil {
		return x.PaymentGatewayType
	}
	return v11.PaymentGatewayType(0)
}

func (x *MerchantPaymentServicePaymentGatewayType) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type MerchantPaymentServiceFetchPaymentGatewayRequest struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGateway        string                 `protobuf:"bytes,1,opt,name=id_payment_gateway,json=idPaymentGateway,proto3" json:"id_payment_gateway,omitempty"`
	AccountHolderNameSearch string                 `protobuf:"bytes,2,opt,name=account_holder_name_search,json=accountHolderNameSearch,proto3" json:"account_holder_name_search,omitempty"`
	Pagination              *v12.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *MerchantPaymentServiceFetchPaymentGatewayRequest) Reset() {
	*x = MerchantPaymentServiceFetchPaymentGatewayRequest{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceFetchPaymentGatewayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceFetchPaymentGatewayRequest) ProtoMessage() {}

func (x *MerchantPaymentServiceFetchPaymentGatewayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceFetchPaymentGatewayRequest.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceFetchPaymentGatewayRequest) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{21}
}

func (x *MerchantPaymentServiceFetchPaymentGatewayRequest) GetIdPaymentGateway() string {
	if x != nil {
		return x.IdPaymentGateway
	}
	return ""
}

func (x *MerchantPaymentServiceFetchPaymentGatewayRequest) GetAccountHolderNameSearch() string {
	if x != nil {
		return x.AccountHolderNameSearch
	}
	return ""
}

func (x *MerchantPaymentServiceFetchPaymentGatewayRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantPaymentServiceFetchPaymentGatewayResponse struct {
	state           protoimpl.MessageState                  `protogen:"open.v1"`
	PaymentGateways []*MerchantPaymentServicePaymentGateway `protobuf:"bytes,1,rep,name=payment_gateways,json=paymentGateways,proto3" json:"payment_gateways,omitempty"`
	Error           *v1.ErrorMessage                        `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Pagination      *v12.PaginationResponse                 `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *MerchantPaymentServiceFetchPaymentGatewayResponse) Reset() {
	*x = MerchantPaymentServiceFetchPaymentGatewayResponse{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServiceFetchPaymentGatewayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServiceFetchPaymentGatewayResponse) ProtoMessage() {}

func (x *MerchantPaymentServiceFetchPaymentGatewayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServiceFetchPaymentGatewayResponse.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServiceFetchPaymentGatewayResponse) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{22}
}

func (x *MerchantPaymentServiceFetchPaymentGatewayResponse) GetPaymentGateways() []*MerchantPaymentServicePaymentGateway {
	if x != nil {
		return x.PaymentGateways
	}
	return nil
}

func (x *MerchantPaymentServiceFetchPaymentGatewayResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantPaymentServiceFetchPaymentGatewayResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantPaymentServicePaymentGateway struct {
	state              protoimpl.MessageState                                  `protogen:"open.v1"`
	IdPaymentGateway   string                                                  `protobuf:"bytes,1,opt,name=id_payment_gateway,json=idPaymentGateway,proto3" json:"id_payment_gateway,omitempty"`
	AccountHolderName  string                                                  `protobuf:"bytes,2,opt,name=account_holder_name,json=accountHolderName,proto3" json:"account_holder_name,omitempty"`
	PaymentGatewayType *MerchantPaymentServicePaymentGatewayPaymentGatewayType `protobuf:"bytes,3,opt,name=payment_gateway_type,json=paymentGatewayType,proto3" json:"payment_gateway_type,omitempty"`
	State              bool                                                    `protobuf:"varint,4,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *MerchantPaymentServicePaymentGateway) Reset() {
	*x = MerchantPaymentServicePaymentGateway{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServicePaymentGateway) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServicePaymentGateway) ProtoMessage() {}

func (x *MerchantPaymentServicePaymentGateway) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServicePaymentGateway.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServicePaymentGateway) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{23}
}

func (x *MerchantPaymentServicePaymentGateway) GetIdPaymentGateway() string {
	if x != nil {
		return x.IdPaymentGateway
	}
	return ""
}

func (x *MerchantPaymentServicePaymentGateway) GetAccountHolderName() string {
	if x != nil {
		return x.AccountHolderName
	}
	return ""
}

func (x *MerchantPaymentServicePaymentGateway) GetPaymentGatewayType() *MerchantPaymentServicePaymentGatewayPaymentGatewayType {
	if x != nil {
		return x.PaymentGatewayType
	}
	return nil
}

func (x *MerchantPaymentServicePaymentGateway) GetState() bool {
	if x != nil {
		return x.State
	}
	return false
}

type MerchantPaymentServicePaymentGatewayPaymentGatewayType struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	IdPaymentGatewayType string                 `protobuf:"bytes,1,opt,name=id_payment_gateway_type,json=idPaymentGatewayType,proto3" json:"id_payment_gateway_type,omitempty"`
	PaymentGatewayType   v11.PaymentGatewayType `protobuf:"varint,2,opt,name=payment_gateway_type,json=paymentGatewayType,proto3,enum=algoenum.v1.PaymentGatewayType" json:"payment_gateway_type,omitempty"`
	IsActive             bool                   `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *MerchantPaymentServicePaymentGatewayPaymentGatewayType) Reset() {
	*x = MerchantPaymentServicePaymentGatewayPaymentGatewayType{}
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPaymentServicePaymentGatewayPaymentGatewayType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPaymentServicePaymentGatewayPaymentGatewayType) ProtoMessage() {}

func (x *MerchantPaymentServicePaymentGatewayPaymentGatewayType) ProtoReflect() protoreflect.Message {
	mi := &file_billing_payment_v1_merchant_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPaymentServicePaymentGatewayPaymentGatewayType.ProtoReflect.Descriptor instead.
func (*MerchantPaymentServicePaymentGatewayPaymentGatewayType) Descriptor() ([]byte, []int) {
	return file_billing_payment_v1_merchant_proto_rawDescGZIP(), []int{24}
}

func (x *MerchantPaymentServicePaymentGatewayPaymentGatewayType) GetIdPaymentGatewayType() string {
	if x != nil {
		return x.IdPaymentGatewayType
	}
	return ""
}

func (x *MerchantPaymentServicePaymentGatewayPaymentGatewayType) GetPaymentGatewayType() v11.PaymentGatewayType {
	if x != nil {
		return x.PaymentGatewayType
	}
	return v11.PaymentGatewayType(0)
}

func (x *MerchantPaymentServicePaymentGatewayPaymentGatewayType) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

var File_billing_payment_v1_merchant_proto protoreflect.FileDescriptor

const file_billing_payment_v1_merchant_proto_rawDesc = "" +
	"\n" +
	"!billing/payment/v1/merchant.proto\x12\x12billing.payment.v1\x1a&algoenum/v1/payment_gateway_type.proto\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\x1a\x1aalgoenum/v1/currency.proto\x1a\x19algoenum/v1/vn_bank.proto\"\xfe\x02\n" +
	".MerchantPaymentServiceCreateDodoPaymentRequest\x12N\n" +
	"%id_payment_gateway_dodo_amount_top_up\x18\x01 \x01(\tR\x1fidPaymentGatewayDodoAmountTopUp\x12\x1d\n" +
	"\n" +
	"first_name\x18\x02 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x03 \x01(\tR\blastName\x12\x1b\n" +
	"\tuser_city\x18\x04 \x01(\tR\buserCity\x12!\n" +
	"\fuser_country\x18\x05 \x01(\tR\vuserCountry\x12\x1d\n" +
	"\n" +
	"user_state\x18\x06 \x01(\tR\tuserState\x12\x1d\n" +
	"\n" +
	"user_phone\x18\a \x01(\tR\tuserPhone\x12\x1f\n" +
	"\vuser_street\x18\b \x01(\tR\n" +
	"userStreet\x12!\n" +
	"\fuser_zipcode\x18\t \x01(\tR\vuserZipcode\"\x83\x01\n" +
	"/MerchantPaymentServiceCreateDodoPaymentResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12!\n" +
	"\fpayment_link\x18\x02 \x01(\tR\vpaymentLink\"\x83\x01\n" +
	"/MerchantPaymentServiceCreateSePayPaymentRequest\x12P\n" +
	"&id_payment_gateway_sepay_amount_top_up\x18\x01 \x01(\tR idPaymentGatewaySepayAmountTopUp\"\xac\x02\n" +
	"0MerchantPaymentServiceCreateSePayPaymentResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12\x19\n" +
	"\bimage_qr\x18\x02 \x01(\tR\aimageQr\x124\n" +
	"\tbank_type\x18\x03 \x01(\x0e2\x17.algoenum.v1.VNBankTypeR\bbankType\x12.\n" +
	"\x13account_holder_name\x18\x04 \x01(\tR\x11accountHolderName\x12%\n" +
	"\x0eaccount_number\x18\x05 \x01(\tR\raccountNumber\x12!\n" +
	"\fpayment_desc\x18\x06 \x01(\tR\vpaymentDesc\"\x98\x01\n" +
	"2MerchantPaymentServiceFetchSePayAmountTopUpRequest\x12%\n" +
	"\x05state\x18\x01 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x8f\x02\n" +
	"3MerchantPaymentServiceFetchSePayAmountTopUpResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12k\n" +
	"\x14sepay_amount_top_ups\x18\x03 \x03(\v2:.billing.payment.v1.MerchantPaymentServiceSePayAmountTopUpR\x11sepayAmountTopUps\"\xe3\x01\n" +
	"&MerchantPaymentServiceSePayAmountTopUp\x12P\n" +
	"&id_payment_gateway_sepay_amount_top_up\x18\x01 \x01(\tR idPaymentGatewaySepayAmountTopUp\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12\x1c\n" +
	"\tpromotion\x18\x03 \x01(\x01R\tpromotion\x121\n" +
	"\bcurrency\x18\x04 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\"k\n" +
	";MerchantPaymentServiceFetchSePayPaymentGatewayDetailRequest\x12,\n" +
	"\x12id_payment_gateway\x18\x01 \x01(\tR\x10idPaymentGateway\"\xf4\x01\n" +
	"<MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12\x84\x01\n" +
	"\x1csepay_payment_gateway_detail\x18\x02 \x01(\v2C.billing.payment.v1.MerchantPaymentServiceSePayPaymentGatewayDetailR\x19sepayPaymentGatewayDetail\"\xea\x01\n" +
	"/MerchantPaymentServiceSePayPaymentGatewayDetail\x120\n" +
	"\avn_bank\x18\x01 \x01(\x0e2\x17.algoenum.v1.VNBankTypeR\x06vnBank\x12.\n" +
	"\x13account_holder_name\x18\x02 \x01(\tR\x11accountHolderName\x12%\n" +
	"\x0eaccount_number\x18\x03 \x01(\tR\raccountNumber\x12\x16\n" +
	"\x06prefix\x18\x04 \x01(\tR\x06prefix\x12\x16\n" +
	"\x06suffix\x18\x05 \x01(\tR\x06suffix\"\x97\x01\n" +
	"1MerchantPaymentServiceFetchDodoAmountTopUpRequest\x12%\n" +
	"\x05state\x18\x01 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x8b\x02\n" +
	"2MerchantPaymentServiceFetchDodoAmountTopUpResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12h\n" +
	"\x13dodo_amount_top_ups\x18\x03 \x03(\v29.billing.payment.v1.MerchantPaymentServiceDodoAmountTopUpR\x10dodoAmountTopUps\"\x88\x02\n" +
	"%MerchantPaymentServiceDodoAmountTopUp\x12N\n" +
	"%id_payment_gateway_dodo_amount_top_up\x18\x01 \x01(\tR\x1fidPaymentGatewayDodoAmountTopUp\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12\x1c\n" +
	"\tpromotion\x18\x03 \x01(\x01R\tpromotion\x12&\n" +
	"\x0fproduct_id_dodo\x18\x04 \x01(\tR\rproductIdDodo\x121\n" +
	"\bcurrency\x18\x05 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\"\x99\x01\n" +
	"3MerchantPaymentServiceFetchAppotaAmountTopUpRequest\x12%\n" +
	"\x05state\x18\x01 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x93\x02\n" +
	"4MerchantPaymentServiceFetchAppotaAmountTopUpResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12n\n" +
	"\x15appota_amount_top_ups\x18\x03 \x03(\v2;.billing.payment.v1.MerchantPaymentServiceAppotaAmountTopUpR\x12appotaAmountTopUps\"\xe6\x01\n" +
	"'MerchantPaymentServiceAppotaAmountTopUp\x12R\n" +
	"'id_payment_gateway_appota_amount_top_up\x18\x01 \x01(\tR!idPaymentGatewayAppotaAmountTopUp\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12\x1c\n" +
	"\tpromotion\x18\x03 \x01(\x01R\tpromotion\x121\n" +
	"\bcurrency\x18\x04 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\"\x8d\x01\n" +
	"6MerchantPaymentServiceUpdatePaymentGatewayStateRequest\x12,\n" +
	"\x12id_payment_gateway\x18\x01 \x01(\tR\x10idPaymentGateway\x12%\n" +
	"\x05state\x18\x02 \x01(\v2\x0f.utils.v1.StateR\x05state\"h\n" +
	"7MerchantPaymentServiceUpdatePaymentGatewayStateResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xd7\x02\n" +
	"4MerchantPaymentServiceFetchPaymentGatewayTypeRequest\x125\n" +
	"\x17id_payment_gateway_type\x18\x01 \x01(\tR\x14idPaymentGatewayType\x12Q\n" +
	"\x14payment_gateway_type\x18\x02 \x01(\x0e2\x1f.algoenum.v1.PaymentGatewayTypeR\x12paymentGatewayType\x121\n" +
	"\bcurrency\x18\x03 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\x12%\n" +
	"\x05state\x18\x04 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x96\x02\n" +
	"5MerchantPaymentServiceFetchPaymentGatewayTypeResponse\x12p\n" +
	"\x15payment_gateway_types\x18\x01 \x03(\v2<.billing.payment.v1.MerchantPaymentServicePaymentGatewayTypeR\x13paymentGatewayTypes\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\xc8\x01\n" +
	"(MerchantPaymentServicePaymentGatewayType\x125\n" +
	"\x17id_payment_gateway_type\x18\x01 \x01(\tR\x14idPaymentGatewayType\x12Q\n" +
	"\x14payment_gateway_type\x18\x02 \x01(\x0e2\x1f.algoenum.v1.PaymentGatewayTypeR\x12paymentGatewayType\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\"\xda\x01\n" +
	"0MerchantPaymentServiceFetchPaymentGatewayRequest\x12,\n" +
	"\x12id_payment_gateway\x18\x01 \x01(\tR\x10idPaymentGateway\x12;\n" +
	"\x1aaccount_holder_name_search\x18\x02 \x01(\tR\x17accountHolderNameSearch\x12;\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x85\x02\n" +
	"1MerchantPaymentServiceFetchPaymentGatewayResponse\x12c\n" +
	"\x10payment_gateways\x18\x01 \x03(\v28.billing.payment.v1.MerchantPaymentServicePaymentGatewayR\x0fpaymentGateways\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\x98\x02\n" +
	"$MerchantPaymentServicePaymentGateway\x12,\n" +
	"\x12id_payment_gateway\x18\x01 \x01(\tR\x10idPaymentGateway\x12.\n" +
	"\x13account_holder_name\x18\x02 \x01(\tR\x11accountHolderName\x12|\n" +
	"\x14payment_gateway_type\x18\x03 \x01(\v2J.billing.payment.v1.MerchantPaymentServicePaymentGatewayPaymentGatewayTypeR\x12paymentGatewayType\x12\x14\n" +
	"\x05state\x18\x04 \x01(\bR\x05state\"\xdf\x01\n" +
	"6MerchantPaymentServicePaymentGatewayPaymentGatewayType\x125\n" +
	"\x17id_payment_gateway_type\x18\x01 \x01(\tR\x14idPaymentGatewayType\x12Q\n" +
	"\x14payment_gateway_type\x18\x02 \x01(\x0e2\x1f.algoenum.v1.PaymentGatewayTypeR\x12paymentGatewayType\x12\x1b\n" +
	"\tis_active\x18\x03 \x01(\bR\bisActive2\xff\a\n" +
	"\x16MerchantPaymentService\x12\xa2\x01\n" +
	"\x13FetchPaymentGateway\x12D.billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayRequest\x1aE.billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayResponse\x12\xab\x01\n" +
	"\x16FetchAppotaAmountTopUp\x12G.billing.payment.v1.MerchantPaymentServiceFetchAppotaAmountTopUpRequest\x1aH.billing.payment.v1.MerchantPaymentServiceFetchAppotaAmountTopUpResponse\x12\xa5\x01\n" +
	"\x14FetchDodoAmountTopUp\x12E.billing.payment.v1.MerchantPaymentServiceFetchDodoAmountTopUpRequest\x1aF.billing.payment.v1.MerchantPaymentServiceFetchDodoAmountTopUpResponse\x12\xa8\x01\n" +
	"\x15FetchSePayAmountTopUp\x12F.billing.payment.v1.MerchantPaymentServiceFetchSePayAmountTopUpRequest\x1aG.billing.payment.v1.MerchantPaymentServiceFetchSePayAmountTopUpResponse\x12\x9c\x01\n" +
	"\x11CreateDodoPayment\x12B.billing.payment.v1.MerchantPaymentServiceCreateDodoPaymentRequest\x1aC.billing.payment.v1.MerchantPaymentServiceCreateDodoPaymentResponse\x12\x9f\x01\n" +
	"\x12CreateSePayPayment\x12C.billing.payment.v1.MerchantPaymentServiceCreateSePayPaymentRequest\x1aD.billing.payment.v1.MerchantPaymentServiceCreateSePayPaymentResponseBMZKgit.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/payment/v1;paymentv1b\x06proto3"

var (
	file_billing_payment_v1_merchant_proto_rawDescOnce sync.Once
	file_billing_payment_v1_merchant_proto_rawDescData []byte
)

func file_billing_payment_v1_merchant_proto_rawDescGZIP() []byte {
	file_billing_payment_v1_merchant_proto_rawDescOnce.Do(func() {
		file_billing_payment_v1_merchant_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_billing_payment_v1_merchant_proto_rawDesc), len(file_billing_payment_v1_merchant_proto_rawDesc)))
	})
	return file_billing_payment_v1_merchant_proto_rawDescData
}

var file_billing_payment_v1_merchant_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_billing_payment_v1_merchant_proto_goTypes = []any{
	(*MerchantPaymentServiceCreateDodoPaymentRequest)(nil),               // 0: billing.payment.v1.MerchantPaymentServiceCreateDodoPaymentRequest
	(*MerchantPaymentServiceCreateDodoPaymentResponse)(nil),              // 1: billing.payment.v1.MerchantPaymentServiceCreateDodoPaymentResponse
	(*MerchantPaymentServiceCreateSePayPaymentRequest)(nil),              // 2: billing.payment.v1.MerchantPaymentServiceCreateSePayPaymentRequest
	(*MerchantPaymentServiceCreateSePayPaymentResponse)(nil),             // 3: billing.payment.v1.MerchantPaymentServiceCreateSePayPaymentResponse
	(*MerchantPaymentServiceFetchSePayAmountTopUpRequest)(nil),           // 4: billing.payment.v1.MerchantPaymentServiceFetchSePayAmountTopUpRequest
	(*MerchantPaymentServiceFetchSePayAmountTopUpResponse)(nil),          // 5: billing.payment.v1.MerchantPaymentServiceFetchSePayAmountTopUpResponse
	(*MerchantPaymentServiceSePayAmountTopUp)(nil),                       // 6: billing.payment.v1.MerchantPaymentServiceSePayAmountTopUp
	(*MerchantPaymentServiceFetchSePayPaymentGatewayDetailRequest)(nil),  // 7: billing.payment.v1.MerchantPaymentServiceFetchSePayPaymentGatewayDetailRequest
	(*MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponse)(nil), // 8: billing.payment.v1.MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponse
	(*MerchantPaymentServiceSePayPaymentGatewayDetail)(nil),              // 9: billing.payment.v1.MerchantPaymentServiceSePayPaymentGatewayDetail
	(*MerchantPaymentServiceFetchDodoAmountTopUpRequest)(nil),            // 10: billing.payment.v1.MerchantPaymentServiceFetchDodoAmountTopUpRequest
	(*MerchantPaymentServiceFetchDodoAmountTopUpResponse)(nil),           // 11: billing.payment.v1.MerchantPaymentServiceFetchDodoAmountTopUpResponse
	(*MerchantPaymentServiceDodoAmountTopUp)(nil),                        // 12: billing.payment.v1.MerchantPaymentServiceDodoAmountTopUp
	(*MerchantPaymentServiceFetchAppotaAmountTopUpRequest)(nil),          // 13: billing.payment.v1.MerchantPaymentServiceFetchAppotaAmountTopUpRequest
	(*MerchantPaymentServiceFetchAppotaAmountTopUpResponse)(nil),         // 14: billing.payment.v1.MerchantPaymentServiceFetchAppotaAmountTopUpResponse
	(*MerchantPaymentServiceAppotaAmountTopUp)(nil),                      // 15: billing.payment.v1.MerchantPaymentServiceAppotaAmountTopUp
	(*MerchantPaymentServiceUpdatePaymentGatewayStateRequest)(nil),       // 16: billing.payment.v1.MerchantPaymentServiceUpdatePaymentGatewayStateRequest
	(*MerchantPaymentServiceUpdatePaymentGatewayStateResponse)(nil),      // 17: billing.payment.v1.MerchantPaymentServiceUpdatePaymentGatewayStateResponse
	(*MerchantPaymentServiceFetchPaymentGatewayTypeRequest)(nil),         // 18: billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayTypeRequest
	(*MerchantPaymentServiceFetchPaymentGatewayTypeResponse)(nil),        // 19: billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayTypeResponse
	(*MerchantPaymentServicePaymentGatewayType)(nil),                     // 20: billing.payment.v1.MerchantPaymentServicePaymentGatewayType
	(*MerchantPaymentServiceFetchPaymentGatewayRequest)(nil),             // 21: billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayRequest
	(*MerchantPaymentServiceFetchPaymentGatewayResponse)(nil),            // 22: billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayResponse
	(*MerchantPaymentServicePaymentGateway)(nil),                         // 23: billing.payment.v1.MerchantPaymentServicePaymentGateway
	(*MerchantPaymentServicePaymentGatewayPaymentGatewayType)(nil),       // 24: billing.payment.v1.MerchantPaymentServicePaymentGatewayPaymentGatewayType
	(*v1.ErrorMessage)(nil),                                              // 25: errmsg.v1.ErrorMessage
	(v11.VNBankType)(0),                                                  // 26: algoenum.v1.VNBankType
	(*v12.State)(nil),                                                    // 27: utils.v1.State
	(*v12.PaginationRequest)(nil),                                        // 28: utils.v1.PaginationRequest
	(*v12.PaginationResponse)(nil),                                       // 29: utils.v1.PaginationResponse
	(v11.Currency)(0),                                                    // 30: algoenum.v1.Currency
	(v11.PaymentGatewayType)(0),                                          // 31: algoenum.v1.PaymentGatewayType
}
var file_billing_payment_v1_merchant_proto_depIdxs = []int32{
	25, // 0: billing.payment.v1.MerchantPaymentServiceCreateDodoPaymentResponse.error:type_name -> errmsg.v1.ErrorMessage
	25, // 1: billing.payment.v1.MerchantPaymentServiceCreateSePayPaymentResponse.error:type_name -> errmsg.v1.ErrorMessage
	26, // 2: billing.payment.v1.MerchantPaymentServiceCreateSePayPaymentResponse.bank_type:type_name -> algoenum.v1.VNBankType
	27, // 3: billing.payment.v1.MerchantPaymentServiceFetchSePayAmountTopUpRequest.state:type_name -> utils.v1.State
	28, // 4: billing.payment.v1.MerchantPaymentServiceFetchSePayAmountTopUpRequest.pagination:type_name -> utils.v1.PaginationRequest
	25, // 5: billing.payment.v1.MerchantPaymentServiceFetchSePayAmountTopUpResponse.error:type_name -> errmsg.v1.ErrorMessage
	29, // 6: billing.payment.v1.MerchantPaymentServiceFetchSePayAmountTopUpResponse.pagination:type_name -> utils.v1.PaginationResponse
	6,  // 7: billing.payment.v1.MerchantPaymentServiceFetchSePayAmountTopUpResponse.sepay_amount_top_ups:type_name -> billing.payment.v1.MerchantPaymentServiceSePayAmountTopUp
	30, // 8: billing.payment.v1.MerchantPaymentServiceSePayAmountTopUp.currency:type_name -> algoenum.v1.Currency
	25, // 9: billing.payment.v1.MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponse.error:type_name -> errmsg.v1.ErrorMessage
	9,  // 10: billing.payment.v1.MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponse.sepay_payment_gateway_detail:type_name -> billing.payment.v1.MerchantPaymentServiceSePayPaymentGatewayDetail
	26, // 11: billing.payment.v1.MerchantPaymentServiceSePayPaymentGatewayDetail.vn_bank:type_name -> algoenum.v1.VNBankType
	27, // 12: billing.payment.v1.MerchantPaymentServiceFetchDodoAmountTopUpRequest.state:type_name -> utils.v1.State
	28, // 13: billing.payment.v1.MerchantPaymentServiceFetchDodoAmountTopUpRequest.pagination:type_name -> utils.v1.PaginationRequest
	25, // 14: billing.payment.v1.MerchantPaymentServiceFetchDodoAmountTopUpResponse.error:type_name -> errmsg.v1.ErrorMessage
	29, // 15: billing.payment.v1.MerchantPaymentServiceFetchDodoAmountTopUpResponse.pagination:type_name -> utils.v1.PaginationResponse
	12, // 16: billing.payment.v1.MerchantPaymentServiceFetchDodoAmountTopUpResponse.dodo_amount_top_ups:type_name -> billing.payment.v1.MerchantPaymentServiceDodoAmountTopUp
	30, // 17: billing.payment.v1.MerchantPaymentServiceDodoAmountTopUp.currency:type_name -> algoenum.v1.Currency
	27, // 18: billing.payment.v1.MerchantPaymentServiceFetchAppotaAmountTopUpRequest.state:type_name -> utils.v1.State
	28, // 19: billing.payment.v1.MerchantPaymentServiceFetchAppotaAmountTopUpRequest.pagination:type_name -> utils.v1.PaginationRequest
	25, // 20: billing.payment.v1.MerchantPaymentServiceFetchAppotaAmountTopUpResponse.error:type_name -> errmsg.v1.ErrorMessage
	29, // 21: billing.payment.v1.MerchantPaymentServiceFetchAppotaAmountTopUpResponse.pagination:type_name -> utils.v1.PaginationResponse
	15, // 22: billing.payment.v1.MerchantPaymentServiceFetchAppotaAmountTopUpResponse.appota_amount_top_ups:type_name -> billing.payment.v1.MerchantPaymentServiceAppotaAmountTopUp
	30, // 23: billing.payment.v1.MerchantPaymentServiceAppotaAmountTopUp.currency:type_name -> algoenum.v1.Currency
	27, // 24: billing.payment.v1.MerchantPaymentServiceUpdatePaymentGatewayStateRequest.state:type_name -> utils.v1.State
	25, // 25: billing.payment.v1.MerchantPaymentServiceUpdatePaymentGatewayStateResponse.error:type_name -> errmsg.v1.ErrorMessage
	31, // 26: billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayTypeRequest.payment_gateway_type:type_name -> algoenum.v1.PaymentGatewayType
	30, // 27: billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayTypeRequest.currency:type_name -> algoenum.v1.Currency
	27, // 28: billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayTypeRequest.state:type_name -> utils.v1.State
	28, // 29: billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayTypeRequest.pagination:type_name -> utils.v1.PaginationRequest
	20, // 30: billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayTypeResponse.payment_gateway_types:type_name -> billing.payment.v1.MerchantPaymentServicePaymentGatewayType
	25, // 31: billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayTypeResponse.error:type_name -> errmsg.v1.ErrorMessage
	29, // 32: billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayTypeResponse.pagination:type_name -> utils.v1.PaginationResponse
	31, // 33: billing.payment.v1.MerchantPaymentServicePaymentGatewayType.payment_gateway_type:type_name -> algoenum.v1.PaymentGatewayType
	28, // 34: billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayRequest.pagination:type_name -> utils.v1.PaginationRequest
	23, // 35: billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayResponse.payment_gateways:type_name -> billing.payment.v1.MerchantPaymentServicePaymentGateway
	25, // 36: billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayResponse.error:type_name -> errmsg.v1.ErrorMessage
	29, // 37: billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayResponse.pagination:type_name -> utils.v1.PaginationResponse
	24, // 38: billing.payment.v1.MerchantPaymentServicePaymentGateway.payment_gateway_type:type_name -> billing.payment.v1.MerchantPaymentServicePaymentGatewayPaymentGatewayType
	31, // 39: billing.payment.v1.MerchantPaymentServicePaymentGatewayPaymentGatewayType.payment_gateway_type:type_name -> algoenum.v1.PaymentGatewayType
	21, // 40: billing.payment.v1.MerchantPaymentService.FetchPaymentGateway:input_type -> billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayRequest
	13, // 41: billing.payment.v1.MerchantPaymentService.FetchAppotaAmountTopUp:input_type -> billing.payment.v1.MerchantPaymentServiceFetchAppotaAmountTopUpRequest
	10, // 42: billing.payment.v1.MerchantPaymentService.FetchDodoAmountTopUp:input_type -> billing.payment.v1.MerchantPaymentServiceFetchDodoAmountTopUpRequest
	4,  // 43: billing.payment.v1.MerchantPaymentService.FetchSePayAmountTopUp:input_type -> billing.payment.v1.MerchantPaymentServiceFetchSePayAmountTopUpRequest
	0,  // 44: billing.payment.v1.MerchantPaymentService.CreateDodoPayment:input_type -> billing.payment.v1.MerchantPaymentServiceCreateDodoPaymentRequest
	2,  // 45: billing.payment.v1.MerchantPaymentService.CreateSePayPayment:input_type -> billing.payment.v1.MerchantPaymentServiceCreateSePayPaymentRequest
	22, // 46: billing.payment.v1.MerchantPaymentService.FetchPaymentGateway:output_type -> billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayResponse
	14, // 47: billing.payment.v1.MerchantPaymentService.FetchAppotaAmountTopUp:output_type -> billing.payment.v1.MerchantPaymentServiceFetchAppotaAmountTopUpResponse
	11, // 48: billing.payment.v1.MerchantPaymentService.FetchDodoAmountTopUp:output_type -> billing.payment.v1.MerchantPaymentServiceFetchDodoAmountTopUpResponse
	5,  // 49: billing.payment.v1.MerchantPaymentService.FetchSePayAmountTopUp:output_type -> billing.payment.v1.MerchantPaymentServiceFetchSePayAmountTopUpResponse
	1,  // 50: billing.payment.v1.MerchantPaymentService.CreateDodoPayment:output_type -> billing.payment.v1.MerchantPaymentServiceCreateDodoPaymentResponse
	3,  // 51: billing.payment.v1.MerchantPaymentService.CreateSePayPayment:output_type -> billing.payment.v1.MerchantPaymentServiceCreateSePayPaymentResponse
	46, // [46:52] is the sub-list for method output_type
	40, // [40:46] is the sub-list for method input_type
	40, // [40:40] is the sub-list for extension type_name
	40, // [40:40] is the sub-list for extension extendee
	0,  // [0:40] is the sub-list for field type_name
}

func init() { file_billing_payment_v1_merchant_proto_init() }
func file_billing_payment_v1_merchant_proto_init() {
	if File_billing_payment_v1_merchant_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_billing_payment_v1_merchant_proto_rawDesc), len(file_billing_payment_v1_merchant_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_billing_payment_v1_merchant_proto_goTypes,
		DependencyIndexes: file_billing_payment_v1_merchant_proto_depIdxs,
		MessageInfos:      file_billing_payment_v1_merchant_proto_msgTypes,
	}.Build()
	File_billing_payment_v1_merchant_proto = out.File
	file_billing_payment_v1_merchant_proto_goTypes = nil
	file_billing_payment_v1_merchant_proto_depIdxs = nil
}
