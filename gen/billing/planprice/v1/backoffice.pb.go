// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: billing/planprice/v1/backoffice.proto

package planpricev1

import (
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackofficePlanPriceServiceFetchPlanPriceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdPlan        string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePlanPriceServiceFetchPlanPriceRequest) Reset() {
	*x = BackofficePlanPriceServiceFetchPlanPriceRequest{}
	mi := &file_billing_planprice_v1_backoffice_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanPriceServiceFetchPlanPriceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanPriceServiceFetchPlanPriceRequest) ProtoMessage() {}

func (x *BackofficePlanPriceServiceFetchPlanPriceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_planprice_v1_backoffice_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanPriceServiceFetchPlanPriceRequest.ProtoReflect.Descriptor instead.
func (*BackofficePlanPriceServiceFetchPlanPriceRequest) Descriptor() ([]byte, []int) {
	return file_billing_planprice_v1_backoffice_proto_rawDescGZIP(), []int{0}
}

func (x *BackofficePlanPriceServiceFetchPlanPriceRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

type BackofficePlanPriceServiceFetchPlanPriceResponse struct {
	state         protoimpl.MessageState                       `protogen:"open.v1"`
	Error         *v1.ErrorMessage                             `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	PlanPrices    []*BackofficePlanPriceServicePlanPriceEntity `protobuf:"bytes,2,rep,name=plan_prices,json=planPrices,proto3" json:"plan_prices,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePlanPriceServiceFetchPlanPriceResponse) Reset() {
	*x = BackofficePlanPriceServiceFetchPlanPriceResponse{}
	mi := &file_billing_planprice_v1_backoffice_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanPriceServiceFetchPlanPriceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanPriceServiceFetchPlanPriceResponse) ProtoMessage() {}

func (x *BackofficePlanPriceServiceFetchPlanPriceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_planprice_v1_backoffice_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanPriceServiceFetchPlanPriceResponse.ProtoReflect.Descriptor instead.
func (*BackofficePlanPriceServiceFetchPlanPriceResponse) Descriptor() ([]byte, []int) {
	return file_billing_planprice_v1_backoffice_proto_rawDescGZIP(), []int{1}
}

func (x *BackofficePlanPriceServiceFetchPlanPriceResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficePlanPriceServiceFetchPlanPriceResponse) GetPlanPrices() []*BackofficePlanPriceServicePlanPriceEntity {
	if x != nil {
		return x.PlanPrices
	}
	return nil
}

type BackofficePlanPriceServicePlanPriceEntity struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	IdPlanPrice         string                 `protobuf:"bytes,1,opt,name=id_plan_price,json=idPlanPrice,proto3" json:"id_plan_price,omitempty"`
	BillingCycleInSec   int64                  `protobuf:"varint,2,opt,name=billing_cycle_in_sec,json=billingCycleInSec,proto3" json:"billing_cycle_in_sec,omitempty"`
	DataTransferInGbyte float64                `protobuf:"fixed64,3,opt,name=data_transfer_in_gbyte,json=dataTransferInGbyte,proto3" json:"data_transfer_in_gbyte,omitempty"`
	CostPrice           float64                `protobuf:"fixed64,4,opt,name=cost_price,json=costPrice,proto3" json:"cost_price,omitempty"`
	PurchasePrice       float64                `protobuf:"fixed64,5,opt,name=purchase_price,json=purchasePrice,proto3" json:"purchase_price,omitempty"`
	Currency            v11.Currency           `protobuf:"varint,6,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	IsActive            bool                   `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *BackofficePlanPriceServicePlanPriceEntity) Reset() {
	*x = BackofficePlanPriceServicePlanPriceEntity{}
	mi := &file_billing_planprice_v1_backoffice_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanPriceServicePlanPriceEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanPriceServicePlanPriceEntity) ProtoMessage() {}

func (x *BackofficePlanPriceServicePlanPriceEntity) ProtoReflect() protoreflect.Message {
	mi := &file_billing_planprice_v1_backoffice_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanPriceServicePlanPriceEntity.ProtoReflect.Descriptor instead.
func (*BackofficePlanPriceServicePlanPriceEntity) Descriptor() ([]byte, []int) {
	return file_billing_planprice_v1_backoffice_proto_rawDescGZIP(), []int{2}
}

func (x *BackofficePlanPriceServicePlanPriceEntity) GetIdPlanPrice() string {
	if x != nil {
		return x.IdPlanPrice
	}
	return ""
}

func (x *BackofficePlanPriceServicePlanPriceEntity) GetBillingCycleInSec() int64 {
	if x != nil {
		return x.BillingCycleInSec
	}
	return 0
}

func (x *BackofficePlanPriceServicePlanPriceEntity) GetDataTransferInGbyte() float64 {
	if x != nil {
		return x.DataTransferInGbyte
	}
	return 0
}

func (x *BackofficePlanPriceServicePlanPriceEntity) GetCostPrice() float64 {
	if x != nil {
		return x.CostPrice
	}
	return 0
}

func (x *BackofficePlanPriceServicePlanPriceEntity) GetPurchasePrice() float64 {
	if x != nil {
		return x.PurchasePrice
	}
	return 0
}

func (x *BackofficePlanPriceServicePlanPriceEntity) GetCurrency() v11.Currency {
	if x != nil {
		return x.Currency
	}
	return v11.Currency(0)
}

func (x *BackofficePlanPriceServicePlanPriceEntity) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficePlanPriceServiceUpdatePlanPriceRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	IdPlanPrice       string                 `protobuf:"bytes,1,opt,name=id_plan_price,json=idPlanPrice,proto3" json:"id_plan_price,omitempty"`
	BillingCycleInSec int64                  `protobuf:"varint,2,opt,name=billing_cycle_in_sec,json=billingCycleInSec,proto3" json:"billing_cycle_in_sec,omitempty"`
	CostPrice         float64                `protobuf:"fixed64,3,opt,name=cost_price,json=costPrice,proto3" json:"cost_price,omitempty"`
	PurchasePrice     float64                `protobuf:"fixed64,4,opt,name=purchase_price,json=purchasePrice,proto3" json:"purchase_price,omitempty"`
	State             *v12.State             `protobuf:"bytes,5,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BackofficePlanPriceServiceUpdatePlanPriceRequest) Reset() {
	*x = BackofficePlanPriceServiceUpdatePlanPriceRequest{}
	mi := &file_billing_planprice_v1_backoffice_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanPriceServiceUpdatePlanPriceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanPriceServiceUpdatePlanPriceRequest) ProtoMessage() {}

func (x *BackofficePlanPriceServiceUpdatePlanPriceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_planprice_v1_backoffice_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanPriceServiceUpdatePlanPriceRequest.ProtoReflect.Descriptor instead.
func (*BackofficePlanPriceServiceUpdatePlanPriceRequest) Descriptor() ([]byte, []int) {
	return file_billing_planprice_v1_backoffice_proto_rawDescGZIP(), []int{3}
}

func (x *BackofficePlanPriceServiceUpdatePlanPriceRequest) GetIdPlanPrice() string {
	if x != nil {
		return x.IdPlanPrice
	}
	return ""
}

func (x *BackofficePlanPriceServiceUpdatePlanPriceRequest) GetBillingCycleInSec() int64 {
	if x != nil {
		return x.BillingCycleInSec
	}
	return 0
}

func (x *BackofficePlanPriceServiceUpdatePlanPriceRequest) GetCostPrice() float64 {
	if x != nil {
		return x.CostPrice
	}
	return 0
}

func (x *BackofficePlanPriceServiceUpdatePlanPriceRequest) GetPurchasePrice() float64 {
	if x != nil {
		return x.PurchasePrice
	}
	return 0
}

func (x *BackofficePlanPriceServiceUpdatePlanPriceRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

type BackofficePlanPriceServiceUpdatePlanPriceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePlanPriceServiceUpdatePlanPriceResponse) Reset() {
	*x = BackofficePlanPriceServiceUpdatePlanPriceResponse{}
	mi := &file_billing_planprice_v1_backoffice_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanPriceServiceUpdatePlanPriceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanPriceServiceUpdatePlanPriceResponse) ProtoMessage() {}

func (x *BackofficePlanPriceServiceUpdatePlanPriceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_planprice_v1_backoffice_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanPriceServiceUpdatePlanPriceResponse.ProtoReflect.Descriptor instead.
func (*BackofficePlanPriceServiceUpdatePlanPriceResponse) Descriptor() ([]byte, []int) {
	return file_billing_planprice_v1_backoffice_proto_rawDescGZIP(), []int{4}
}

func (x *BackofficePlanPriceServiceUpdatePlanPriceResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficePlanPriceServiceCreatePlanPriceRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	IdPlan              string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	BillingCycleInSec   int64                  `protobuf:"varint,2,opt,name=billing_cycle_in_sec,json=billingCycleInSec,proto3" json:"billing_cycle_in_sec,omitempty"`
	DataTransferInGbyte float64                `protobuf:"fixed64,3,opt,name=data_transfer_in_gbyte,json=dataTransferInGbyte,proto3" json:"data_transfer_in_gbyte,omitempty"`
	CostPrice           float64                `protobuf:"fixed64,4,opt,name=cost_price,json=costPrice,proto3" json:"cost_price,omitempty"`
	PurchasePrice       float64                `protobuf:"fixed64,5,opt,name=purchase_price,json=purchasePrice,proto3" json:"purchase_price,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *BackofficePlanPriceServiceCreatePlanPriceRequest) Reset() {
	*x = BackofficePlanPriceServiceCreatePlanPriceRequest{}
	mi := &file_billing_planprice_v1_backoffice_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanPriceServiceCreatePlanPriceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanPriceServiceCreatePlanPriceRequest) ProtoMessage() {}

func (x *BackofficePlanPriceServiceCreatePlanPriceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_planprice_v1_backoffice_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanPriceServiceCreatePlanPriceRequest.ProtoReflect.Descriptor instead.
func (*BackofficePlanPriceServiceCreatePlanPriceRequest) Descriptor() ([]byte, []int) {
	return file_billing_planprice_v1_backoffice_proto_rawDescGZIP(), []int{5}
}

func (x *BackofficePlanPriceServiceCreatePlanPriceRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *BackofficePlanPriceServiceCreatePlanPriceRequest) GetBillingCycleInSec() int64 {
	if x != nil {
		return x.BillingCycleInSec
	}
	return 0
}

func (x *BackofficePlanPriceServiceCreatePlanPriceRequest) GetDataTransferInGbyte() float64 {
	if x != nil {
		return x.DataTransferInGbyte
	}
	return 0
}

func (x *BackofficePlanPriceServiceCreatePlanPriceRequest) GetCostPrice() float64 {
	if x != nil {
		return x.CostPrice
	}
	return 0
}

func (x *BackofficePlanPriceServiceCreatePlanPriceRequest) GetPurchasePrice() float64 {
	if x != nil {
		return x.PurchasePrice
	}
	return 0
}

type BackofficePlanPriceServiceCreatePlanPriceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePlanPriceServiceCreatePlanPriceResponse) Reset() {
	*x = BackofficePlanPriceServiceCreatePlanPriceResponse{}
	mi := &file_billing_planprice_v1_backoffice_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanPriceServiceCreatePlanPriceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanPriceServiceCreatePlanPriceResponse) ProtoMessage() {}

func (x *BackofficePlanPriceServiceCreatePlanPriceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_planprice_v1_backoffice_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanPriceServiceCreatePlanPriceResponse.ProtoReflect.Descriptor instead.
func (*BackofficePlanPriceServiceCreatePlanPriceResponse) Descriptor() ([]byte, []int) {
	return file_billing_planprice_v1_backoffice_proto_rawDescGZIP(), []int{6}
}

func (x *BackofficePlanPriceServiceCreatePlanPriceResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_billing_planprice_v1_backoffice_proto protoreflect.FileDescriptor

const file_billing_planprice_v1_backoffice_proto_rawDesc = "" +
	"\n" +
	"%billing/planprice/v1/backoffice.proto\x12\x14billing.planprice.v1\x1a\x1aalgoenum/v1/currency.proto\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\"J\n" +
	"/BackofficePlanPriceServiceFetchPlanPriceRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\"\xc3\x01\n" +
	"0BackofficePlanPriceServiceFetchPlanPriceResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12`\n" +
	"\vplan_prices\x18\x02 \x03(\v2?.billing.planprice.v1.BackofficePlanPriceServicePlanPriceEntityR\n" +
	"planPrices\"\xcb\x02\n" +
	")BackofficePlanPriceServicePlanPriceEntity\x12\"\n" +
	"\rid_plan_price\x18\x01 \x01(\tR\vidPlanPrice\x12/\n" +
	"\x14billing_cycle_in_sec\x18\x02 \x01(\x03R\x11billingCycleInSec\x123\n" +
	"\x16data_transfer_in_gbyte\x18\x03 \x01(\x01R\x13dataTransferInGbyte\x12\x1d\n" +
	"\n" +
	"cost_price\x18\x04 \x01(\x01R\tcostPrice\x12%\n" +
	"\x0epurchase_price\x18\x05 \x01(\x01R\rpurchasePrice\x121\n" +
	"\bcurrency\x18\x06 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\x12\x1b\n" +
	"\tis_active\x18\a \x01(\bR\bisActive\"\xf4\x01\n" +
	"0BackofficePlanPriceServiceUpdatePlanPriceRequest\x12\"\n" +
	"\rid_plan_price\x18\x01 \x01(\tR\vidPlanPrice\x12/\n" +
	"\x14billing_cycle_in_sec\x18\x02 \x01(\x03R\x11billingCycleInSec\x12\x1d\n" +
	"\n" +
	"cost_price\x18\x03 \x01(\x01R\tcostPrice\x12%\n" +
	"\x0epurchase_price\x18\x04 \x01(\x01R\rpurchasePrice\x12%\n" +
	"\x05state\x18\x05 \x01(\v2\x0f.utils.v1.StateR\x05state\"b\n" +
	"1BackofficePlanPriceServiceUpdatePlanPriceResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xf7\x01\n" +
	"0BackofficePlanPriceServiceCreatePlanPriceRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12/\n" +
	"\x14billing_cycle_in_sec\x18\x02 \x01(\x03R\x11billingCycleInSec\x123\n" +
	"\x16data_transfer_in_gbyte\x18\x03 \x01(\x01R\x13dataTransferInGbyte\x12\x1d\n" +
	"\n" +
	"cost_price\x18\x04 \x01(\x01R\tcostPrice\x12%\n" +
	"\x0epurchase_price\x18\x05 \x01(\x01R\rpurchasePrice\"b\n" +
	"1BackofficePlanPriceServiceCreatePlanPriceResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error2\x88\x04\n" +
	"\x1aBackofficePlanPriceService\x12\x9f\x01\n" +
	"\x0eFetchPlanPrice\x12E.billing.planprice.v1.BackofficePlanPriceServiceFetchPlanPriceRequest\x1aF.billing.planprice.v1.BackofficePlanPriceServiceFetchPlanPriceResponse\x12\xa2\x01\n" +
	"\x0fCreatePlanPrice\x12F.billing.planprice.v1.BackofficePlanPriceServiceCreatePlanPriceRequest\x1aG.billing.planprice.v1.BackofficePlanPriceServiceCreatePlanPriceResponse\x12\xa2\x01\n" +
	"\x0fUpdatePlanPrice\x12F.billing.planprice.v1.BackofficePlanPriceServiceUpdatePlanPriceRequest\x1aG.billing.planprice.v1.BackofficePlanPriceServiceUpdatePlanPriceResponseBQZOgit.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/planprice/v1;planpricev1b\x06proto3"

var (
	file_billing_planprice_v1_backoffice_proto_rawDescOnce sync.Once
	file_billing_planprice_v1_backoffice_proto_rawDescData []byte
)

func file_billing_planprice_v1_backoffice_proto_rawDescGZIP() []byte {
	file_billing_planprice_v1_backoffice_proto_rawDescOnce.Do(func() {
		file_billing_planprice_v1_backoffice_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_billing_planprice_v1_backoffice_proto_rawDesc), len(file_billing_planprice_v1_backoffice_proto_rawDesc)))
	})
	return file_billing_planprice_v1_backoffice_proto_rawDescData
}

var file_billing_planprice_v1_backoffice_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_billing_planprice_v1_backoffice_proto_goTypes = []any{
	(*BackofficePlanPriceServiceFetchPlanPriceRequest)(nil),   // 0: billing.planprice.v1.BackofficePlanPriceServiceFetchPlanPriceRequest
	(*BackofficePlanPriceServiceFetchPlanPriceResponse)(nil),  // 1: billing.planprice.v1.BackofficePlanPriceServiceFetchPlanPriceResponse
	(*BackofficePlanPriceServicePlanPriceEntity)(nil),         // 2: billing.planprice.v1.BackofficePlanPriceServicePlanPriceEntity
	(*BackofficePlanPriceServiceUpdatePlanPriceRequest)(nil),  // 3: billing.planprice.v1.BackofficePlanPriceServiceUpdatePlanPriceRequest
	(*BackofficePlanPriceServiceUpdatePlanPriceResponse)(nil), // 4: billing.planprice.v1.BackofficePlanPriceServiceUpdatePlanPriceResponse
	(*BackofficePlanPriceServiceCreatePlanPriceRequest)(nil),  // 5: billing.planprice.v1.BackofficePlanPriceServiceCreatePlanPriceRequest
	(*BackofficePlanPriceServiceCreatePlanPriceResponse)(nil), // 6: billing.planprice.v1.BackofficePlanPriceServiceCreatePlanPriceResponse
	(*v1.ErrorMessage)(nil),                                   // 7: errmsg.v1.ErrorMessage
	(v11.Currency)(0),                                         // 8: algoenum.v1.Currency
	(*v12.State)(nil),                                         // 9: utils.v1.State
}
var file_billing_planprice_v1_backoffice_proto_depIdxs = []int32{
	7, // 0: billing.planprice.v1.BackofficePlanPriceServiceFetchPlanPriceResponse.error:type_name -> errmsg.v1.ErrorMessage
	2, // 1: billing.planprice.v1.BackofficePlanPriceServiceFetchPlanPriceResponse.plan_prices:type_name -> billing.planprice.v1.BackofficePlanPriceServicePlanPriceEntity
	8, // 2: billing.planprice.v1.BackofficePlanPriceServicePlanPriceEntity.currency:type_name -> algoenum.v1.Currency
	9, // 3: billing.planprice.v1.BackofficePlanPriceServiceUpdatePlanPriceRequest.state:type_name -> utils.v1.State
	7, // 4: billing.planprice.v1.BackofficePlanPriceServiceUpdatePlanPriceResponse.error:type_name -> errmsg.v1.ErrorMessage
	7, // 5: billing.planprice.v1.BackofficePlanPriceServiceCreatePlanPriceResponse.error:type_name -> errmsg.v1.ErrorMessage
	0, // 6: billing.planprice.v1.BackofficePlanPriceService.FetchPlanPrice:input_type -> billing.planprice.v1.BackofficePlanPriceServiceFetchPlanPriceRequest
	5, // 7: billing.planprice.v1.BackofficePlanPriceService.CreatePlanPrice:input_type -> billing.planprice.v1.BackofficePlanPriceServiceCreatePlanPriceRequest
	3, // 8: billing.planprice.v1.BackofficePlanPriceService.UpdatePlanPrice:input_type -> billing.planprice.v1.BackofficePlanPriceServiceUpdatePlanPriceRequest
	1, // 9: billing.planprice.v1.BackofficePlanPriceService.FetchPlanPrice:output_type -> billing.planprice.v1.BackofficePlanPriceServiceFetchPlanPriceResponse
	6, // 10: billing.planprice.v1.BackofficePlanPriceService.CreatePlanPrice:output_type -> billing.planprice.v1.BackofficePlanPriceServiceCreatePlanPriceResponse
	4, // 11: billing.planprice.v1.BackofficePlanPriceService.UpdatePlanPrice:output_type -> billing.planprice.v1.BackofficePlanPriceServiceUpdatePlanPriceResponse
	9, // [9:12] is the sub-list for method output_type
	6, // [6:9] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_billing_planprice_v1_backoffice_proto_init() }
func file_billing_planprice_v1_backoffice_proto_init() {
	if File_billing_planprice_v1_backoffice_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_billing_planprice_v1_backoffice_proto_rawDesc), len(file_billing_planprice_v1_backoffice_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_billing_planprice_v1_backoffice_proto_goTypes,
		DependencyIndexes: file_billing_planprice_v1_backoffice_proto_depIdxs,
		MessageInfos:      file_billing_planprice_v1_backoffice_proto_msgTypes,
	}.Build()
	File_billing_planprice_v1_backoffice_proto = out.File
	file_billing_planprice_v1_backoffice_proto_goTypes = nil
	file_billing_planprice_v1_backoffice_proto_depIdxs = nil
}
