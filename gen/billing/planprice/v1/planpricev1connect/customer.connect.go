// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: billing/planprice/v1/customer.proto

package planpricev1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/planprice/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CustomerPlanPriceServiceName is the fully-qualified name of the CustomerPlanPriceService service.
	CustomerPlanPriceServiceName = "billing.planprice.v1.CustomerPlanPriceService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CustomerPlanPriceServiceFetchPlanPriceProcedure is the fully-qualified name of the
	// CustomerPlanPriceService's FetchPlanPrice RPC.
	CustomerPlanPriceServiceFetchPlanPriceProcedure = "/billing.planprice.v1.CustomerPlanPriceService/FetchPlanPrice"
)

// CustomerPlanPriceServiceClient is a client for the billing.planprice.v1.CustomerPlanPriceService
// service.
type CustomerPlanPriceServiceClient interface {
	FetchPlanPrice(context.Context, *connect.Request[v1.CustomerPlanPriceServiceFetchPlanPriceRequest]) (*connect.Response[v1.CustomerPlanPriceServiceFetchPlanPriceResponse], error)
}

// NewCustomerPlanPriceServiceClient constructs a client for the
// billing.planprice.v1.CustomerPlanPriceService service. By default, it uses the Connect protocol
// with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed requests. To
// use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or connect.WithGRPCWeb()
// options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCustomerPlanPriceServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CustomerPlanPriceServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	customerPlanPriceServiceMethods := v1.File_billing_planprice_v1_customer_proto.Services().ByName("CustomerPlanPriceService").Methods()
	return &customerPlanPriceServiceClient{
		fetchPlanPrice: connect.NewClient[v1.CustomerPlanPriceServiceFetchPlanPriceRequest, v1.CustomerPlanPriceServiceFetchPlanPriceResponse](
			httpClient,
			baseURL+CustomerPlanPriceServiceFetchPlanPriceProcedure,
			connect.WithSchema(customerPlanPriceServiceMethods.ByName("FetchPlanPrice")),
			connect.WithClientOptions(opts...),
		),
	}
}

// customerPlanPriceServiceClient implements CustomerPlanPriceServiceClient.
type customerPlanPriceServiceClient struct {
	fetchPlanPrice *connect.Client[v1.CustomerPlanPriceServiceFetchPlanPriceRequest, v1.CustomerPlanPriceServiceFetchPlanPriceResponse]
}

// FetchPlanPrice calls billing.planprice.v1.CustomerPlanPriceService.FetchPlanPrice.
func (c *customerPlanPriceServiceClient) FetchPlanPrice(ctx context.Context, req *connect.Request[v1.CustomerPlanPriceServiceFetchPlanPriceRequest]) (*connect.Response[v1.CustomerPlanPriceServiceFetchPlanPriceResponse], error) {
	return c.fetchPlanPrice.CallUnary(ctx, req)
}

// CustomerPlanPriceServiceHandler is an implementation of the
// billing.planprice.v1.CustomerPlanPriceService service.
type CustomerPlanPriceServiceHandler interface {
	FetchPlanPrice(context.Context, *connect.Request[v1.CustomerPlanPriceServiceFetchPlanPriceRequest]) (*connect.Response[v1.CustomerPlanPriceServiceFetchPlanPriceResponse], error)
}

// NewCustomerPlanPriceServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCustomerPlanPriceServiceHandler(svc CustomerPlanPriceServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	customerPlanPriceServiceMethods := v1.File_billing_planprice_v1_customer_proto.Services().ByName("CustomerPlanPriceService").Methods()
	customerPlanPriceServiceFetchPlanPriceHandler := connect.NewUnaryHandler(
		CustomerPlanPriceServiceFetchPlanPriceProcedure,
		svc.FetchPlanPrice,
		connect.WithSchema(customerPlanPriceServiceMethods.ByName("FetchPlanPrice")),
		connect.WithHandlerOptions(opts...),
	)
	return "/billing.planprice.v1.CustomerPlanPriceService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CustomerPlanPriceServiceFetchPlanPriceProcedure:
			customerPlanPriceServiceFetchPlanPriceHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCustomerPlanPriceServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCustomerPlanPriceServiceHandler struct{}

func (UnimplementedCustomerPlanPriceServiceHandler) FetchPlanPrice(context.Context, *connect.Request[v1.CustomerPlanPriceServiceFetchPlanPriceRequest]) (*connect.Response[v1.CustomerPlanPriceServiceFetchPlanPriceResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.planprice.v1.CustomerPlanPriceService.FetchPlanPrice is not implemented"))
}
