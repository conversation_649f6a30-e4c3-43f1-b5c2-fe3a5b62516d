// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: billing/planprice/v1/merchant.proto

package planpricev1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/planprice/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// MerchantPlanPriceServiceName is the fully-qualified name of the MerchantPlanPriceService service.
	MerchantPlanPriceServiceName = "billing.planprice.v1.MerchantPlanPriceService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// MerchantPlanPriceServiceFetchPlanPriceProcedure is the fully-qualified name of the
	// MerchantPlanPriceService's FetchPlanPrice RPC.
	MerchantPlanPriceServiceFetchPlanPriceProcedure = "/billing.planprice.v1.MerchantPlanPriceService/FetchPlanPrice"
	// MerchantPlanPriceServiceUpdatePlanPriceProcedure is the fully-qualified name of the
	// MerchantPlanPriceService's UpdatePlanPrice RPC.
	MerchantPlanPriceServiceUpdatePlanPriceProcedure = "/billing.planprice.v1.MerchantPlanPriceService/UpdatePlanPrice"
)

// MerchantPlanPriceServiceClient is a client for the billing.planprice.v1.MerchantPlanPriceService
// service.
type MerchantPlanPriceServiceClient interface {
	FetchPlanPrice(context.Context, *connect.Request[v1.MerchantPlanPriceServiceFetchPlanPriceRequest]) (*connect.Response[v1.MerchantPlanPriceServiceFetchPlanPriceResponse], error)
	UpdatePlanPrice(context.Context, *connect.Request[v1.MerchantPlanPriceServiceUpdatePlanPriceRequest]) (*connect.Response[v1.MerchantPlanPriceServiceUpdatePlanPriceResponse], error)
}

// NewMerchantPlanPriceServiceClient constructs a client for the
// billing.planprice.v1.MerchantPlanPriceService service. By default, it uses the Connect protocol
// with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed requests. To
// use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or connect.WithGRPCWeb()
// options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewMerchantPlanPriceServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) MerchantPlanPriceServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	merchantPlanPriceServiceMethods := v1.File_billing_planprice_v1_merchant_proto.Services().ByName("MerchantPlanPriceService").Methods()
	return &merchantPlanPriceServiceClient{
		fetchPlanPrice: connect.NewClient[v1.MerchantPlanPriceServiceFetchPlanPriceRequest, v1.MerchantPlanPriceServiceFetchPlanPriceResponse](
			httpClient,
			baseURL+MerchantPlanPriceServiceFetchPlanPriceProcedure,
			connect.WithSchema(merchantPlanPriceServiceMethods.ByName("FetchPlanPrice")),
			connect.WithClientOptions(opts...),
		),
		updatePlanPrice: connect.NewClient[v1.MerchantPlanPriceServiceUpdatePlanPriceRequest, v1.MerchantPlanPriceServiceUpdatePlanPriceResponse](
			httpClient,
			baseURL+MerchantPlanPriceServiceUpdatePlanPriceProcedure,
			connect.WithSchema(merchantPlanPriceServiceMethods.ByName("UpdatePlanPrice")),
			connect.WithClientOptions(opts...),
		),
	}
}

// merchantPlanPriceServiceClient implements MerchantPlanPriceServiceClient.
type merchantPlanPriceServiceClient struct {
	fetchPlanPrice  *connect.Client[v1.MerchantPlanPriceServiceFetchPlanPriceRequest, v1.MerchantPlanPriceServiceFetchPlanPriceResponse]
	updatePlanPrice *connect.Client[v1.MerchantPlanPriceServiceUpdatePlanPriceRequest, v1.MerchantPlanPriceServiceUpdatePlanPriceResponse]
}

// FetchPlanPrice calls billing.planprice.v1.MerchantPlanPriceService.FetchPlanPrice.
func (c *merchantPlanPriceServiceClient) FetchPlanPrice(ctx context.Context, req *connect.Request[v1.MerchantPlanPriceServiceFetchPlanPriceRequest]) (*connect.Response[v1.MerchantPlanPriceServiceFetchPlanPriceResponse], error) {
	return c.fetchPlanPrice.CallUnary(ctx, req)
}

// UpdatePlanPrice calls billing.planprice.v1.MerchantPlanPriceService.UpdatePlanPrice.
func (c *merchantPlanPriceServiceClient) UpdatePlanPrice(ctx context.Context, req *connect.Request[v1.MerchantPlanPriceServiceUpdatePlanPriceRequest]) (*connect.Response[v1.MerchantPlanPriceServiceUpdatePlanPriceResponse], error) {
	return c.updatePlanPrice.CallUnary(ctx, req)
}

// MerchantPlanPriceServiceHandler is an implementation of the
// billing.planprice.v1.MerchantPlanPriceService service.
type MerchantPlanPriceServiceHandler interface {
	FetchPlanPrice(context.Context, *connect.Request[v1.MerchantPlanPriceServiceFetchPlanPriceRequest]) (*connect.Response[v1.MerchantPlanPriceServiceFetchPlanPriceResponse], error)
	UpdatePlanPrice(context.Context, *connect.Request[v1.MerchantPlanPriceServiceUpdatePlanPriceRequest]) (*connect.Response[v1.MerchantPlanPriceServiceUpdatePlanPriceResponse], error)
}

// NewMerchantPlanPriceServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewMerchantPlanPriceServiceHandler(svc MerchantPlanPriceServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	merchantPlanPriceServiceMethods := v1.File_billing_planprice_v1_merchant_proto.Services().ByName("MerchantPlanPriceService").Methods()
	merchantPlanPriceServiceFetchPlanPriceHandler := connect.NewUnaryHandler(
		MerchantPlanPriceServiceFetchPlanPriceProcedure,
		svc.FetchPlanPrice,
		connect.WithSchema(merchantPlanPriceServiceMethods.ByName("FetchPlanPrice")),
		connect.WithHandlerOptions(opts...),
	)
	merchantPlanPriceServiceUpdatePlanPriceHandler := connect.NewUnaryHandler(
		MerchantPlanPriceServiceUpdatePlanPriceProcedure,
		svc.UpdatePlanPrice,
		connect.WithSchema(merchantPlanPriceServiceMethods.ByName("UpdatePlanPrice")),
		connect.WithHandlerOptions(opts...),
	)
	return "/billing.planprice.v1.MerchantPlanPriceService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case MerchantPlanPriceServiceFetchPlanPriceProcedure:
			merchantPlanPriceServiceFetchPlanPriceHandler.ServeHTTP(w, r)
		case MerchantPlanPriceServiceUpdatePlanPriceProcedure:
			merchantPlanPriceServiceUpdatePlanPriceHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedMerchantPlanPriceServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedMerchantPlanPriceServiceHandler struct{}

func (UnimplementedMerchantPlanPriceServiceHandler) FetchPlanPrice(context.Context, *connect.Request[v1.MerchantPlanPriceServiceFetchPlanPriceRequest]) (*connect.Response[v1.MerchantPlanPriceServiceFetchPlanPriceResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.planprice.v1.MerchantPlanPriceService.FetchPlanPrice is not implemented"))
}

func (UnimplementedMerchantPlanPriceServiceHandler) UpdatePlanPrice(context.Context, *connect.Request[v1.MerchantPlanPriceServiceUpdatePlanPriceRequest]) (*connect.Response[v1.MerchantPlanPriceServiceUpdatePlanPriceResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.planprice.v1.MerchantPlanPriceService.UpdatePlanPrice is not implemented"))
}
