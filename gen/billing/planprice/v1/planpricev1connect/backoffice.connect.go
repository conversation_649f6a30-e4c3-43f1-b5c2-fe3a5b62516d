// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: billing/planprice/v1/backoffice.proto

package planpricev1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/planprice/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackofficePlanPriceServiceName is the fully-qualified name of the BackofficePlanPriceService
	// service.
	BackofficePlanPriceServiceName = "billing.planprice.v1.BackofficePlanPriceService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackofficePlanPriceServiceFetchPlanPriceProcedure is the fully-qualified name of the
	// BackofficePlanPriceService's FetchPlanPrice RPC.
	BackofficePlanPriceServiceFetchPlanPriceProcedure = "/billing.planprice.v1.BackofficePlanPriceService/FetchPlanPrice"
	// BackofficePlanPriceServiceCreatePlanPriceProcedure is the fully-qualified name of the
	// BackofficePlanPriceService's CreatePlanPrice RPC.
	BackofficePlanPriceServiceCreatePlanPriceProcedure = "/billing.planprice.v1.BackofficePlanPriceService/CreatePlanPrice"
	// BackofficePlanPriceServiceUpdatePlanPriceProcedure is the fully-qualified name of the
	// BackofficePlanPriceService's UpdatePlanPrice RPC.
	BackofficePlanPriceServiceUpdatePlanPriceProcedure = "/billing.planprice.v1.BackofficePlanPriceService/UpdatePlanPrice"
)

// BackofficePlanPriceServiceClient is a client for the
// billing.planprice.v1.BackofficePlanPriceService service.
type BackofficePlanPriceServiceClient interface {
	FetchPlanPrice(context.Context, *connect.Request[v1.BackofficePlanPriceServiceFetchPlanPriceRequest]) (*connect.Response[v1.BackofficePlanPriceServiceFetchPlanPriceResponse], error)
	CreatePlanPrice(context.Context, *connect.Request[v1.BackofficePlanPriceServiceCreatePlanPriceRequest]) (*connect.Response[v1.BackofficePlanPriceServiceCreatePlanPriceResponse], error)
	UpdatePlanPrice(context.Context, *connect.Request[v1.BackofficePlanPriceServiceUpdatePlanPriceRequest]) (*connect.Response[v1.BackofficePlanPriceServiceUpdatePlanPriceResponse], error)
}

// NewBackofficePlanPriceServiceClient constructs a client for the
// billing.planprice.v1.BackofficePlanPriceService service. By default, it uses the Connect protocol
// with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed requests. To
// use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or connect.WithGRPCWeb()
// options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackofficePlanPriceServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackofficePlanPriceServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backofficePlanPriceServiceMethods := v1.File_billing_planprice_v1_backoffice_proto.Services().ByName("BackofficePlanPriceService").Methods()
	return &backofficePlanPriceServiceClient{
		fetchPlanPrice: connect.NewClient[v1.BackofficePlanPriceServiceFetchPlanPriceRequest, v1.BackofficePlanPriceServiceFetchPlanPriceResponse](
			httpClient,
			baseURL+BackofficePlanPriceServiceFetchPlanPriceProcedure,
			connect.WithSchema(backofficePlanPriceServiceMethods.ByName("FetchPlanPrice")),
			connect.WithClientOptions(opts...),
		),
		createPlanPrice: connect.NewClient[v1.BackofficePlanPriceServiceCreatePlanPriceRequest, v1.BackofficePlanPriceServiceCreatePlanPriceResponse](
			httpClient,
			baseURL+BackofficePlanPriceServiceCreatePlanPriceProcedure,
			connect.WithSchema(backofficePlanPriceServiceMethods.ByName("CreatePlanPrice")),
			connect.WithClientOptions(opts...),
		),
		updatePlanPrice: connect.NewClient[v1.BackofficePlanPriceServiceUpdatePlanPriceRequest, v1.BackofficePlanPriceServiceUpdatePlanPriceResponse](
			httpClient,
			baseURL+BackofficePlanPriceServiceUpdatePlanPriceProcedure,
			connect.WithSchema(backofficePlanPriceServiceMethods.ByName("UpdatePlanPrice")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backofficePlanPriceServiceClient implements BackofficePlanPriceServiceClient.
type backofficePlanPriceServiceClient struct {
	fetchPlanPrice  *connect.Client[v1.BackofficePlanPriceServiceFetchPlanPriceRequest, v1.BackofficePlanPriceServiceFetchPlanPriceResponse]
	createPlanPrice *connect.Client[v1.BackofficePlanPriceServiceCreatePlanPriceRequest, v1.BackofficePlanPriceServiceCreatePlanPriceResponse]
	updatePlanPrice *connect.Client[v1.BackofficePlanPriceServiceUpdatePlanPriceRequest, v1.BackofficePlanPriceServiceUpdatePlanPriceResponse]
}

// FetchPlanPrice calls billing.planprice.v1.BackofficePlanPriceService.FetchPlanPrice.
func (c *backofficePlanPriceServiceClient) FetchPlanPrice(ctx context.Context, req *connect.Request[v1.BackofficePlanPriceServiceFetchPlanPriceRequest]) (*connect.Response[v1.BackofficePlanPriceServiceFetchPlanPriceResponse], error) {
	return c.fetchPlanPrice.CallUnary(ctx, req)
}

// CreatePlanPrice calls billing.planprice.v1.BackofficePlanPriceService.CreatePlanPrice.
func (c *backofficePlanPriceServiceClient) CreatePlanPrice(ctx context.Context, req *connect.Request[v1.BackofficePlanPriceServiceCreatePlanPriceRequest]) (*connect.Response[v1.BackofficePlanPriceServiceCreatePlanPriceResponse], error) {
	return c.createPlanPrice.CallUnary(ctx, req)
}

// UpdatePlanPrice calls billing.planprice.v1.BackofficePlanPriceService.UpdatePlanPrice.
func (c *backofficePlanPriceServiceClient) UpdatePlanPrice(ctx context.Context, req *connect.Request[v1.BackofficePlanPriceServiceUpdatePlanPriceRequest]) (*connect.Response[v1.BackofficePlanPriceServiceUpdatePlanPriceResponse], error) {
	return c.updatePlanPrice.CallUnary(ctx, req)
}

// BackofficePlanPriceServiceHandler is an implementation of the
// billing.planprice.v1.BackofficePlanPriceService service.
type BackofficePlanPriceServiceHandler interface {
	FetchPlanPrice(context.Context, *connect.Request[v1.BackofficePlanPriceServiceFetchPlanPriceRequest]) (*connect.Response[v1.BackofficePlanPriceServiceFetchPlanPriceResponse], error)
	CreatePlanPrice(context.Context, *connect.Request[v1.BackofficePlanPriceServiceCreatePlanPriceRequest]) (*connect.Response[v1.BackofficePlanPriceServiceCreatePlanPriceResponse], error)
	UpdatePlanPrice(context.Context, *connect.Request[v1.BackofficePlanPriceServiceUpdatePlanPriceRequest]) (*connect.Response[v1.BackofficePlanPriceServiceUpdatePlanPriceResponse], error)
}

// NewBackofficePlanPriceServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackofficePlanPriceServiceHandler(svc BackofficePlanPriceServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backofficePlanPriceServiceMethods := v1.File_billing_planprice_v1_backoffice_proto.Services().ByName("BackofficePlanPriceService").Methods()
	backofficePlanPriceServiceFetchPlanPriceHandler := connect.NewUnaryHandler(
		BackofficePlanPriceServiceFetchPlanPriceProcedure,
		svc.FetchPlanPrice,
		connect.WithSchema(backofficePlanPriceServiceMethods.ByName("FetchPlanPrice")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePlanPriceServiceCreatePlanPriceHandler := connect.NewUnaryHandler(
		BackofficePlanPriceServiceCreatePlanPriceProcedure,
		svc.CreatePlanPrice,
		connect.WithSchema(backofficePlanPriceServiceMethods.ByName("CreatePlanPrice")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePlanPriceServiceUpdatePlanPriceHandler := connect.NewUnaryHandler(
		BackofficePlanPriceServiceUpdatePlanPriceProcedure,
		svc.UpdatePlanPrice,
		connect.WithSchema(backofficePlanPriceServiceMethods.ByName("UpdatePlanPrice")),
		connect.WithHandlerOptions(opts...),
	)
	return "/billing.planprice.v1.BackofficePlanPriceService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackofficePlanPriceServiceFetchPlanPriceProcedure:
			backofficePlanPriceServiceFetchPlanPriceHandler.ServeHTTP(w, r)
		case BackofficePlanPriceServiceCreatePlanPriceProcedure:
			backofficePlanPriceServiceCreatePlanPriceHandler.ServeHTTP(w, r)
		case BackofficePlanPriceServiceUpdatePlanPriceProcedure:
			backofficePlanPriceServiceUpdatePlanPriceHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackofficePlanPriceServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackofficePlanPriceServiceHandler struct{}

func (UnimplementedBackofficePlanPriceServiceHandler) FetchPlanPrice(context.Context, *connect.Request[v1.BackofficePlanPriceServiceFetchPlanPriceRequest]) (*connect.Response[v1.BackofficePlanPriceServiceFetchPlanPriceResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.planprice.v1.BackofficePlanPriceService.FetchPlanPrice is not implemented"))
}

func (UnimplementedBackofficePlanPriceServiceHandler) CreatePlanPrice(context.Context, *connect.Request[v1.BackofficePlanPriceServiceCreatePlanPriceRequest]) (*connect.Response[v1.BackofficePlanPriceServiceCreatePlanPriceResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.planprice.v1.BackofficePlanPriceService.CreatePlanPrice is not implemented"))
}

func (UnimplementedBackofficePlanPriceServiceHandler) UpdatePlanPrice(context.Context, *connect.Request[v1.BackofficePlanPriceServiceUpdatePlanPriceRequest]) (*connect.Response[v1.BackofficePlanPriceServiceUpdatePlanPriceResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.planprice.v1.BackofficePlanPriceService.UpdatePlanPrice is not implemented"))
}
