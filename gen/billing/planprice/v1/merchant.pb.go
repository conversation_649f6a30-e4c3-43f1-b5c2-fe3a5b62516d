// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: billing/planprice/v1/merchant.proto

package planpricev1

import (
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MerchantPlanPriceServiceFetchPlanPriceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdPlan        string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantPlanPriceServiceFetchPlanPriceRequest) Reset() {
	*x = MerchantPlanPriceServiceFetchPlanPriceRequest{}
	mi := &file_billing_planprice_v1_merchant_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPlanPriceServiceFetchPlanPriceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPlanPriceServiceFetchPlanPriceRequest) ProtoMessage() {}

func (x *MerchantPlanPriceServiceFetchPlanPriceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_planprice_v1_merchant_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPlanPriceServiceFetchPlanPriceRequest.ProtoReflect.Descriptor instead.
func (*MerchantPlanPriceServiceFetchPlanPriceRequest) Descriptor() ([]byte, []int) {
	return file_billing_planprice_v1_merchant_proto_rawDescGZIP(), []int{0}
}

func (x *MerchantPlanPriceServiceFetchPlanPriceRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

type MerchantPlanPriceServiceFetchPlanPriceResponse struct {
	state         protoimpl.MessageState                     `protogen:"open.v1"`
	Error         *v1.ErrorMessage                           `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	PlanPrices    []*MerchantPlanPriceServicePlanPriceEntity `protobuf:"bytes,2,rep,name=plan_prices,json=planPrices,proto3" json:"plan_prices,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantPlanPriceServiceFetchPlanPriceResponse) Reset() {
	*x = MerchantPlanPriceServiceFetchPlanPriceResponse{}
	mi := &file_billing_planprice_v1_merchant_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPlanPriceServiceFetchPlanPriceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPlanPriceServiceFetchPlanPriceResponse) ProtoMessage() {}

func (x *MerchantPlanPriceServiceFetchPlanPriceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_planprice_v1_merchant_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPlanPriceServiceFetchPlanPriceResponse.ProtoReflect.Descriptor instead.
func (*MerchantPlanPriceServiceFetchPlanPriceResponse) Descriptor() ([]byte, []int) {
	return file_billing_planprice_v1_merchant_proto_rawDescGZIP(), []int{1}
}

func (x *MerchantPlanPriceServiceFetchPlanPriceResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantPlanPriceServiceFetchPlanPriceResponse) GetPlanPrices() []*MerchantPlanPriceServicePlanPriceEntity {
	if x != nil {
		return x.PlanPrices
	}
	return nil
}

type MerchantPlanPriceServiceUpdatePlanPriceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdPlanPrice   string                 `protobuf:"bytes,1,opt,name=id_plan_price,json=idPlanPrice,proto3" json:"id_plan_price,omitempty"`
	PurchasePrice float64                `protobuf:"fixed64,2,opt,name=purchase_price,json=purchasePrice,proto3" json:"purchase_price,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantPlanPriceServiceUpdatePlanPriceRequest) Reset() {
	*x = MerchantPlanPriceServiceUpdatePlanPriceRequest{}
	mi := &file_billing_planprice_v1_merchant_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPlanPriceServiceUpdatePlanPriceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPlanPriceServiceUpdatePlanPriceRequest) ProtoMessage() {}

func (x *MerchantPlanPriceServiceUpdatePlanPriceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_planprice_v1_merchant_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPlanPriceServiceUpdatePlanPriceRequest.ProtoReflect.Descriptor instead.
func (*MerchantPlanPriceServiceUpdatePlanPriceRequest) Descriptor() ([]byte, []int) {
	return file_billing_planprice_v1_merchant_proto_rawDescGZIP(), []int{2}
}

func (x *MerchantPlanPriceServiceUpdatePlanPriceRequest) GetIdPlanPrice() string {
	if x != nil {
		return x.IdPlanPrice
	}
	return ""
}

func (x *MerchantPlanPriceServiceUpdatePlanPriceRequest) GetPurchasePrice() float64 {
	if x != nil {
		return x.PurchasePrice
	}
	return 0
}

type MerchantPlanPriceServiceUpdatePlanPriceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantPlanPriceServiceUpdatePlanPriceResponse) Reset() {
	*x = MerchantPlanPriceServiceUpdatePlanPriceResponse{}
	mi := &file_billing_planprice_v1_merchant_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPlanPriceServiceUpdatePlanPriceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPlanPriceServiceUpdatePlanPriceResponse) ProtoMessage() {}

func (x *MerchantPlanPriceServiceUpdatePlanPriceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_planprice_v1_merchant_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPlanPriceServiceUpdatePlanPriceResponse.ProtoReflect.Descriptor instead.
func (*MerchantPlanPriceServiceUpdatePlanPriceResponse) Descriptor() ([]byte, []int) {
	return file_billing_planprice_v1_merchant_proto_rawDescGZIP(), []int{3}
}

func (x *MerchantPlanPriceServiceUpdatePlanPriceResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type MerchantPlanPriceServicePlanPriceEntity struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	IdPlanPrice         string                 `protobuf:"bytes,1,opt,name=id_plan_price,json=idPlanPrice,proto3" json:"id_plan_price,omitempty"`
	BillingCycleInSec   int64                  `protobuf:"varint,2,opt,name=billing_cycle_in_sec,json=billingCycleInSec,proto3" json:"billing_cycle_in_sec,omitempty"`
	DataTransferInGbyte float64                `protobuf:"fixed64,3,opt,name=data_transfer_in_gbyte,json=dataTransferInGbyte,proto3" json:"data_transfer_in_gbyte,omitempty"`
	CostPrice           float64                `protobuf:"fixed64,4,opt,name=cost_price,json=costPrice,proto3" json:"cost_price,omitempty"`
	PurchasePrice       float64                `protobuf:"fixed64,5,opt,name=purchase_price,json=purchasePrice,proto3" json:"purchase_price,omitempty"`
	Currency            v11.Currency           `protobuf:"varint,6,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *MerchantPlanPriceServicePlanPriceEntity) Reset() {
	*x = MerchantPlanPriceServicePlanPriceEntity{}
	mi := &file_billing_planprice_v1_merchant_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPlanPriceServicePlanPriceEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPlanPriceServicePlanPriceEntity) ProtoMessage() {}

func (x *MerchantPlanPriceServicePlanPriceEntity) ProtoReflect() protoreflect.Message {
	mi := &file_billing_planprice_v1_merchant_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPlanPriceServicePlanPriceEntity.ProtoReflect.Descriptor instead.
func (*MerchantPlanPriceServicePlanPriceEntity) Descriptor() ([]byte, []int) {
	return file_billing_planprice_v1_merchant_proto_rawDescGZIP(), []int{4}
}

func (x *MerchantPlanPriceServicePlanPriceEntity) GetIdPlanPrice() string {
	if x != nil {
		return x.IdPlanPrice
	}
	return ""
}

func (x *MerchantPlanPriceServicePlanPriceEntity) GetBillingCycleInSec() int64 {
	if x != nil {
		return x.BillingCycleInSec
	}
	return 0
}

func (x *MerchantPlanPriceServicePlanPriceEntity) GetDataTransferInGbyte() float64 {
	if x != nil {
		return x.DataTransferInGbyte
	}
	return 0
}

func (x *MerchantPlanPriceServicePlanPriceEntity) GetCostPrice() float64 {
	if x != nil {
		return x.CostPrice
	}
	return 0
}

func (x *MerchantPlanPriceServicePlanPriceEntity) GetPurchasePrice() float64 {
	if x != nil {
		return x.PurchasePrice
	}
	return 0
}

func (x *MerchantPlanPriceServicePlanPriceEntity) GetCurrency() v11.Currency {
	if x != nil {
		return x.Currency
	}
	return v11.Currency(0)
}

var File_billing_planprice_v1_merchant_proto protoreflect.FileDescriptor

const file_billing_planprice_v1_merchant_proto_rawDesc = "" +
	"\n" +
	"#billing/planprice/v1/merchant.proto\x12\x14billing.planprice.v1\x1a\x1aalgoenum/v1/currency.proto\x1a\x18errmsg/v1/errormsg.proto\"H\n" +
	"-MerchantPlanPriceServiceFetchPlanPriceRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\"\xbf\x01\n" +
	".MerchantPlanPriceServiceFetchPlanPriceResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12^\n" +
	"\vplan_prices\x18\x02 \x03(\v2=.billing.planprice.v1.MerchantPlanPriceServicePlanPriceEntityR\n" +
	"planPrices\"{\n" +
	".MerchantPlanPriceServiceUpdatePlanPriceRequest\x12\"\n" +
	"\rid_plan_price\x18\x01 \x01(\tR\vidPlanPrice\x12%\n" +
	"\x0epurchase_price\x18\x02 \x01(\x01R\rpurchasePrice\"`\n" +
	"/MerchantPlanPriceServiceUpdatePlanPriceResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xac\x02\n" +
	"'MerchantPlanPriceServicePlanPriceEntity\x12\"\n" +
	"\rid_plan_price\x18\x01 \x01(\tR\vidPlanPrice\x12/\n" +
	"\x14billing_cycle_in_sec\x18\x02 \x01(\x03R\x11billingCycleInSec\x123\n" +
	"\x16data_transfer_in_gbyte\x18\x03 \x01(\x01R\x13dataTransferInGbyte\x12\x1d\n" +
	"\n" +
	"cost_price\x18\x04 \x01(\x01R\tcostPrice\x12%\n" +
	"\x0epurchase_price\x18\x05 \x01(\x01R\rpurchasePrice\x121\n" +
	"\bcurrency\x18\x06 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency2\xd9\x02\n" +
	"\x18MerchantPlanPriceService\x12\x9b\x01\n" +
	"\x0eFetchPlanPrice\x12C.billing.planprice.v1.MerchantPlanPriceServiceFetchPlanPriceRequest\x1aD.billing.planprice.v1.MerchantPlanPriceServiceFetchPlanPriceResponse\x12\x9e\x01\n" +
	"\x0fUpdatePlanPrice\x12D.billing.planprice.v1.MerchantPlanPriceServiceUpdatePlanPriceRequest\x1aE.billing.planprice.v1.MerchantPlanPriceServiceUpdatePlanPriceResponseBQZOgit.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/planprice/v1;planpricev1b\x06proto3"

var (
	file_billing_planprice_v1_merchant_proto_rawDescOnce sync.Once
	file_billing_planprice_v1_merchant_proto_rawDescData []byte
)

func file_billing_planprice_v1_merchant_proto_rawDescGZIP() []byte {
	file_billing_planprice_v1_merchant_proto_rawDescOnce.Do(func() {
		file_billing_planprice_v1_merchant_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_billing_planprice_v1_merchant_proto_rawDesc), len(file_billing_planprice_v1_merchant_proto_rawDesc)))
	})
	return file_billing_planprice_v1_merchant_proto_rawDescData
}

var file_billing_planprice_v1_merchant_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_billing_planprice_v1_merchant_proto_goTypes = []any{
	(*MerchantPlanPriceServiceFetchPlanPriceRequest)(nil),   // 0: billing.planprice.v1.MerchantPlanPriceServiceFetchPlanPriceRequest
	(*MerchantPlanPriceServiceFetchPlanPriceResponse)(nil),  // 1: billing.planprice.v1.MerchantPlanPriceServiceFetchPlanPriceResponse
	(*MerchantPlanPriceServiceUpdatePlanPriceRequest)(nil),  // 2: billing.planprice.v1.MerchantPlanPriceServiceUpdatePlanPriceRequest
	(*MerchantPlanPriceServiceUpdatePlanPriceResponse)(nil), // 3: billing.planprice.v1.MerchantPlanPriceServiceUpdatePlanPriceResponse
	(*MerchantPlanPriceServicePlanPriceEntity)(nil),         // 4: billing.planprice.v1.MerchantPlanPriceServicePlanPriceEntity
	(*v1.ErrorMessage)(nil),                                 // 5: errmsg.v1.ErrorMessage
	(v11.Currency)(0),                                       // 6: algoenum.v1.Currency
}
var file_billing_planprice_v1_merchant_proto_depIdxs = []int32{
	5, // 0: billing.planprice.v1.MerchantPlanPriceServiceFetchPlanPriceResponse.error:type_name -> errmsg.v1.ErrorMessage
	4, // 1: billing.planprice.v1.MerchantPlanPriceServiceFetchPlanPriceResponse.plan_prices:type_name -> billing.planprice.v1.MerchantPlanPriceServicePlanPriceEntity
	5, // 2: billing.planprice.v1.MerchantPlanPriceServiceUpdatePlanPriceResponse.error:type_name -> errmsg.v1.ErrorMessage
	6, // 3: billing.planprice.v1.MerchantPlanPriceServicePlanPriceEntity.currency:type_name -> algoenum.v1.Currency
	0, // 4: billing.planprice.v1.MerchantPlanPriceService.FetchPlanPrice:input_type -> billing.planprice.v1.MerchantPlanPriceServiceFetchPlanPriceRequest
	2, // 5: billing.planprice.v1.MerchantPlanPriceService.UpdatePlanPrice:input_type -> billing.planprice.v1.MerchantPlanPriceServiceUpdatePlanPriceRequest
	1, // 6: billing.planprice.v1.MerchantPlanPriceService.FetchPlanPrice:output_type -> billing.planprice.v1.MerchantPlanPriceServiceFetchPlanPriceResponse
	3, // 7: billing.planprice.v1.MerchantPlanPriceService.UpdatePlanPrice:output_type -> billing.planprice.v1.MerchantPlanPriceServiceUpdatePlanPriceResponse
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_billing_planprice_v1_merchant_proto_init() }
func file_billing_planprice_v1_merchant_proto_init() {
	if File_billing_planprice_v1_merchant_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_billing_planprice_v1_merchant_proto_rawDesc), len(file_billing_planprice_v1_merchant_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_billing_planprice_v1_merchant_proto_goTypes,
		DependencyIndexes: file_billing_planprice_v1_merchant_proto_depIdxs,
		MessageInfos:      file_billing_planprice_v1_merchant_proto_msgTypes,
	}.Build()
	File_billing_planprice_v1_merchant_proto = out.File
	file_billing_planprice_v1_merchant_proto_goTypes = nil
	file_billing_planprice_v1_merchant_proto_depIdxs = nil
}
