// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: billing/planprice/v1/customer.proto

package planpricev1

import (
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CustomerPlanPriceServiceFetchPlanPriceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdPlan        string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPlanPriceServiceFetchPlanPriceRequest) Reset() {
	*x = CustomerPlanPriceServiceFetchPlanPriceRequest{}
	mi := &file_billing_planprice_v1_customer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPlanPriceServiceFetchPlanPriceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPlanPriceServiceFetchPlanPriceRequest) ProtoMessage() {}

func (x *CustomerPlanPriceServiceFetchPlanPriceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_planprice_v1_customer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPlanPriceServiceFetchPlanPriceRequest.ProtoReflect.Descriptor instead.
func (*CustomerPlanPriceServiceFetchPlanPriceRequest) Descriptor() ([]byte, []int) {
	return file_billing_planprice_v1_customer_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerPlanPriceServiceFetchPlanPriceRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

type CustomerPlanPriceServiceFetchPlanPriceResponse struct {
	state         protoimpl.MessageState                     `protogen:"open.v1"`
	Error         *v1.ErrorMessage                           `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	PlanPrices    []*CustomerPlanPriceServicePlanPriceEntity `protobuf:"bytes,2,rep,name=plan_prices,json=planPrices,proto3" json:"plan_prices,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPlanPriceServiceFetchPlanPriceResponse) Reset() {
	*x = CustomerPlanPriceServiceFetchPlanPriceResponse{}
	mi := &file_billing_planprice_v1_customer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPlanPriceServiceFetchPlanPriceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPlanPriceServiceFetchPlanPriceResponse) ProtoMessage() {}

func (x *CustomerPlanPriceServiceFetchPlanPriceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_planprice_v1_customer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPlanPriceServiceFetchPlanPriceResponse.ProtoReflect.Descriptor instead.
func (*CustomerPlanPriceServiceFetchPlanPriceResponse) Descriptor() ([]byte, []int) {
	return file_billing_planprice_v1_customer_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerPlanPriceServiceFetchPlanPriceResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerPlanPriceServiceFetchPlanPriceResponse) GetPlanPrices() []*CustomerPlanPriceServicePlanPriceEntity {
	if x != nil {
		return x.PlanPrices
	}
	return nil
}

type CustomerPlanPriceServicePlanPriceEntity struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	IdPlanPrice         string                 `protobuf:"bytes,1,opt,name=id_plan_price,json=idPlanPrice,proto3" json:"id_plan_price,omitempty"`
	BillingCycleInSec   int64                  `protobuf:"varint,2,opt,name=billing_cycle_in_sec,json=billingCycleInSec,proto3" json:"billing_cycle_in_sec,omitempty"`
	DataTransferInGbyte float64                `protobuf:"fixed64,3,opt,name=data_transfer_in_gbyte,json=dataTransferInGbyte,proto3" json:"data_transfer_in_gbyte,omitempty"`
	Price               float64                `protobuf:"fixed64,5,opt,name=price,proto3" json:"price,omitempty"`
	Currency            v11.Currency           `protobuf:"varint,6,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *CustomerPlanPriceServicePlanPriceEntity) Reset() {
	*x = CustomerPlanPriceServicePlanPriceEntity{}
	mi := &file_billing_planprice_v1_customer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPlanPriceServicePlanPriceEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPlanPriceServicePlanPriceEntity) ProtoMessage() {}

func (x *CustomerPlanPriceServicePlanPriceEntity) ProtoReflect() protoreflect.Message {
	mi := &file_billing_planprice_v1_customer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPlanPriceServicePlanPriceEntity.ProtoReflect.Descriptor instead.
func (*CustomerPlanPriceServicePlanPriceEntity) Descriptor() ([]byte, []int) {
	return file_billing_planprice_v1_customer_proto_rawDescGZIP(), []int{2}
}

func (x *CustomerPlanPriceServicePlanPriceEntity) GetIdPlanPrice() string {
	if x != nil {
		return x.IdPlanPrice
	}
	return ""
}

func (x *CustomerPlanPriceServicePlanPriceEntity) GetBillingCycleInSec() int64 {
	if x != nil {
		return x.BillingCycleInSec
	}
	return 0
}

func (x *CustomerPlanPriceServicePlanPriceEntity) GetDataTransferInGbyte() float64 {
	if x != nil {
		return x.DataTransferInGbyte
	}
	return 0
}

func (x *CustomerPlanPriceServicePlanPriceEntity) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

func (x *CustomerPlanPriceServicePlanPriceEntity) GetCurrency() v11.Currency {
	if x != nil {
		return x.Currency
	}
	return v11.Currency(0)
}

var File_billing_planprice_v1_customer_proto protoreflect.FileDescriptor

const file_billing_planprice_v1_customer_proto_rawDesc = "" +
	"\n" +
	"#billing/planprice/v1/customer.proto\x12\x14billing.planprice.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x1aalgoenum/v1/currency.proto\"H\n" +
	"-CustomerPlanPriceServiceFetchPlanPriceRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\"\xbf\x01\n" +
	".CustomerPlanPriceServiceFetchPlanPriceResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12^\n" +
	"\vplan_prices\x18\x02 \x03(\v2=.billing.planprice.v1.CustomerPlanPriceServicePlanPriceEntityR\n" +
	"planPrices\"\xfc\x01\n" +
	"'CustomerPlanPriceServicePlanPriceEntity\x12\"\n" +
	"\rid_plan_price\x18\x01 \x01(\tR\vidPlanPrice\x12/\n" +
	"\x14billing_cycle_in_sec\x18\x02 \x01(\x03R\x11billingCycleInSec\x123\n" +
	"\x16data_transfer_in_gbyte\x18\x03 \x01(\x01R\x13dataTransferInGbyte\x12\x14\n" +
	"\x05price\x18\x05 \x01(\x01R\x05price\x121\n" +
	"\bcurrency\x18\x06 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency2\xb8\x01\n" +
	"\x18CustomerPlanPriceService\x12\x9b\x01\n" +
	"\x0eFetchPlanPrice\x12C.billing.planprice.v1.CustomerPlanPriceServiceFetchPlanPriceRequest\x1aD.billing.planprice.v1.CustomerPlanPriceServiceFetchPlanPriceResponseBQZOgit.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/planprice/v1;planpricev1b\x06proto3"

var (
	file_billing_planprice_v1_customer_proto_rawDescOnce sync.Once
	file_billing_planprice_v1_customer_proto_rawDescData []byte
)

func file_billing_planprice_v1_customer_proto_rawDescGZIP() []byte {
	file_billing_planprice_v1_customer_proto_rawDescOnce.Do(func() {
		file_billing_planprice_v1_customer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_billing_planprice_v1_customer_proto_rawDesc), len(file_billing_planprice_v1_customer_proto_rawDesc)))
	})
	return file_billing_planprice_v1_customer_proto_rawDescData
}

var file_billing_planprice_v1_customer_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_billing_planprice_v1_customer_proto_goTypes = []any{
	(*CustomerPlanPriceServiceFetchPlanPriceRequest)(nil),  // 0: billing.planprice.v1.CustomerPlanPriceServiceFetchPlanPriceRequest
	(*CustomerPlanPriceServiceFetchPlanPriceResponse)(nil), // 1: billing.planprice.v1.CustomerPlanPriceServiceFetchPlanPriceResponse
	(*CustomerPlanPriceServicePlanPriceEntity)(nil),        // 2: billing.planprice.v1.CustomerPlanPriceServicePlanPriceEntity
	(*v1.ErrorMessage)(nil),                                // 3: errmsg.v1.ErrorMessage
	(v11.Currency)(0),                                      // 4: algoenum.v1.Currency
}
var file_billing_planprice_v1_customer_proto_depIdxs = []int32{
	3, // 0: billing.planprice.v1.CustomerPlanPriceServiceFetchPlanPriceResponse.error:type_name -> errmsg.v1.ErrorMessage
	2, // 1: billing.planprice.v1.CustomerPlanPriceServiceFetchPlanPriceResponse.plan_prices:type_name -> billing.planprice.v1.CustomerPlanPriceServicePlanPriceEntity
	4, // 2: billing.planprice.v1.CustomerPlanPriceServicePlanPriceEntity.currency:type_name -> algoenum.v1.Currency
	0, // 3: billing.planprice.v1.CustomerPlanPriceService.FetchPlanPrice:input_type -> billing.planprice.v1.CustomerPlanPriceServiceFetchPlanPriceRequest
	1, // 4: billing.planprice.v1.CustomerPlanPriceService.FetchPlanPrice:output_type -> billing.planprice.v1.CustomerPlanPriceServiceFetchPlanPriceResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_billing_planprice_v1_customer_proto_init() }
func file_billing_planprice_v1_customer_proto_init() {
	if File_billing_planprice_v1_customer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_billing_planprice_v1_customer_proto_rawDesc), len(file_billing_planprice_v1_customer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_billing_planprice_v1_customer_proto_goTypes,
		DependencyIndexes: file_billing_planprice_v1_customer_proto_depIdxs,
		MessageInfos:      file_billing_planprice_v1_customer_proto_msgTypes,
	}.Build()
	File_billing_planprice_v1_customer_proto = out.File
	file_billing_planprice_v1_customer_proto_goTypes = nil
	file_billing_planprice_v1_customer_proto_depIdxs = nil
}
