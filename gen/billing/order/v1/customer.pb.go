// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: billing/order/v1/customer.proto

package orderv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CustomerOrderServiceCalculateOrderSubscriptionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdPlan        string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdPlanPrice   string                 `protobuf:"bytes,2,opt,name=id_plan_price,json=idPlanPrice,proto3" json:"id_plan_price,omitempty"`
	CouponCode    string                 `protobuf:"bytes,3,opt,name=coupon_code,json=couponCode,proto3" json:"coupon_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerOrderServiceCalculateOrderSubscriptionRequest) Reset() {
	*x = CustomerOrderServiceCalculateOrderSubscriptionRequest{}
	mi := &file_billing_order_v1_customer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerOrderServiceCalculateOrderSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerOrderServiceCalculateOrderSubscriptionRequest) ProtoMessage() {}

func (x *CustomerOrderServiceCalculateOrderSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_order_v1_customer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerOrderServiceCalculateOrderSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*CustomerOrderServiceCalculateOrderSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_billing_order_v1_customer_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerOrderServiceCalculateOrderSubscriptionRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *CustomerOrderServiceCalculateOrderSubscriptionRequest) GetIdPlanPrice() string {
	if x != nil {
		return x.IdPlanPrice
	}
	return ""
}

func (x *CustomerOrderServiceCalculateOrderSubscriptionRequest) GetCouponCode() string {
	if x != nil {
		return x.CouponCode
	}
	return ""
}

type CustomerOrderServiceCalculateOrderSubscriptionResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	OrderAmount    float64                `protobuf:"fixed64,1,opt,name=order_amount,json=orderAmount,proto3" json:"order_amount,omitempty"`
	DebitAmount    float64                `protobuf:"fixed64,2,opt,name=debit_amount,json=debitAmount,proto3" json:"debit_amount,omitempty"`
	DiscountAmount float64                `protobuf:"fixed64,3,opt,name=discount_amount,json=discountAmount,proto3" json:"discount_amount,omitempty"`
	Error          *v1.ErrorMessage       `protobuf:"bytes,4,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerOrderServiceCalculateOrderSubscriptionResponse) Reset() {
	*x = CustomerOrderServiceCalculateOrderSubscriptionResponse{}
	mi := &file_billing_order_v1_customer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerOrderServiceCalculateOrderSubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerOrderServiceCalculateOrderSubscriptionResponse) ProtoMessage() {}

func (x *CustomerOrderServiceCalculateOrderSubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_order_v1_customer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerOrderServiceCalculateOrderSubscriptionResponse.ProtoReflect.Descriptor instead.
func (*CustomerOrderServiceCalculateOrderSubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_billing_order_v1_customer_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerOrderServiceCalculateOrderSubscriptionResponse) GetOrderAmount() float64 {
	if x != nil {
		return x.OrderAmount
	}
	return 0
}

func (x *CustomerOrderServiceCalculateOrderSubscriptionResponse) GetDebitAmount() float64 {
	if x != nil {
		return x.DebitAmount
	}
	return 0
}

func (x *CustomerOrderServiceCalculateOrderSubscriptionResponse) GetDiscountAmount() float64 {
	if x != nil {
		return x.DiscountAmount
	}
	return 0
}

func (x *CustomerOrderServiceCalculateOrderSubscriptionResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerOrderServiceOrderSubscriptionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdPlan        string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdPlanPrice   string                 `protobuf:"bytes,2,opt,name=id_plan_price,json=idPlanPrice,proto3" json:"id_plan_price,omitempty"`
	CouponCode    string                 `protobuf:"bytes,3,opt,name=coupon_code,json=couponCode,proto3" json:"coupon_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerOrderServiceOrderSubscriptionRequest) Reset() {
	*x = CustomerOrderServiceOrderSubscriptionRequest{}
	mi := &file_billing_order_v1_customer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerOrderServiceOrderSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerOrderServiceOrderSubscriptionRequest) ProtoMessage() {}

func (x *CustomerOrderServiceOrderSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_order_v1_customer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerOrderServiceOrderSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*CustomerOrderServiceOrderSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_billing_order_v1_customer_proto_rawDescGZIP(), []int{2}
}

func (x *CustomerOrderServiceOrderSubscriptionRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *CustomerOrderServiceOrderSubscriptionRequest) GetIdPlanPrice() string {
	if x != nil {
		return x.IdPlanPrice
	}
	return ""
}

func (x *CustomerOrderServiceOrderSubscriptionRequest) GetCouponCode() string {
	if x != nil {
		return x.CouponCode
	}
	return ""
}

type CustomerOrderServiceOrderSubscriptionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerOrderServiceOrderSubscriptionResponse) Reset() {
	*x = CustomerOrderServiceOrderSubscriptionResponse{}
	mi := &file_billing_order_v1_customer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerOrderServiceOrderSubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerOrderServiceOrderSubscriptionResponse) ProtoMessage() {}

func (x *CustomerOrderServiceOrderSubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_order_v1_customer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerOrderServiceOrderSubscriptionResponse.ProtoReflect.Descriptor instead.
func (*CustomerOrderServiceOrderSubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_billing_order_v1_customer_proto_rawDescGZIP(), []int{3}
}

func (x *CustomerOrderServiceOrderSubscriptionResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerOrderServiceExtendSubscriptionRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdSubscription string                 `protobuf:"bytes,1,opt,name=id_subscription,json=idSubscription,proto3" json:"id_subscription,omitempty"`
	IdPlanPrice    string                 `protobuf:"bytes,2,opt,name=id_plan_price,json=idPlanPrice,proto3" json:"id_plan_price,omitempty"`
	CouponCode     string                 `protobuf:"bytes,3,opt,name=coupon_code,json=couponCode,proto3" json:"coupon_code,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerOrderServiceExtendSubscriptionRequest) Reset() {
	*x = CustomerOrderServiceExtendSubscriptionRequest{}
	mi := &file_billing_order_v1_customer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerOrderServiceExtendSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerOrderServiceExtendSubscriptionRequest) ProtoMessage() {}

func (x *CustomerOrderServiceExtendSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_order_v1_customer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerOrderServiceExtendSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*CustomerOrderServiceExtendSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_billing_order_v1_customer_proto_rawDescGZIP(), []int{4}
}

func (x *CustomerOrderServiceExtendSubscriptionRequest) GetIdSubscription() string {
	if x != nil {
		return x.IdSubscription
	}
	return ""
}

func (x *CustomerOrderServiceExtendSubscriptionRequest) GetIdPlanPrice() string {
	if x != nil {
		return x.IdPlanPrice
	}
	return ""
}

func (x *CustomerOrderServiceExtendSubscriptionRequest) GetCouponCode() string {
	if x != nil {
		return x.CouponCode
	}
	return ""
}

type CustomerOrderServiceExtendSubscriptionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerOrderServiceExtendSubscriptionResponse) Reset() {
	*x = CustomerOrderServiceExtendSubscriptionResponse{}
	mi := &file_billing_order_v1_customer_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerOrderServiceExtendSubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerOrderServiceExtendSubscriptionResponse) ProtoMessage() {}

func (x *CustomerOrderServiceExtendSubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_order_v1_customer_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerOrderServiceExtendSubscriptionResponse.ProtoReflect.Descriptor instead.
func (*CustomerOrderServiceExtendSubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_billing_order_v1_customer_proto_rawDescGZIP(), []int{5}
}

func (x *CustomerOrderServiceExtendSubscriptionResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_billing_order_v1_customer_proto protoreflect.FileDescriptor

const file_billing_order_v1_customer_proto_rawDesc = "" +
	"\n" +
	"\x1fbilling/order/v1/customer.proto\x12\x10billing.order.v1\x1a\x18errmsg/v1/errormsg.proto\"\x95\x01\n" +
	"5CustomerOrderServiceCalculateOrderSubscriptionRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\"\n" +
	"\rid_plan_price\x18\x02 \x01(\tR\vidPlanPrice\x12\x1f\n" +
	"\vcoupon_code\x18\x03 \x01(\tR\n" +
	"couponCode\"\xd6\x01\n" +
	"6CustomerOrderServiceCalculateOrderSubscriptionResponse\x12!\n" +
	"\forder_amount\x18\x01 \x01(\x01R\vorderAmount\x12!\n" +
	"\fdebit_amount\x18\x02 \x01(\x01R\vdebitAmount\x12'\n" +
	"\x0fdiscount_amount\x18\x03 \x01(\x01R\x0ediscountAmount\x12-\n" +
	"\x05error\x18\x04 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x8c\x01\n" +
	",CustomerOrderServiceOrderSubscriptionRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\"\n" +
	"\rid_plan_price\x18\x02 \x01(\tR\vidPlanPrice\x12\x1f\n" +
	"\vcoupon_code\x18\x03 \x01(\tR\n" +
	"couponCode\"^\n" +
	"-CustomerOrderServiceOrderSubscriptionResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x9d\x01\n" +
	"-CustomerOrderServiceExtendSubscriptionRequest\x12'\n" +
	"\x0fid_subscription\x18\x01 \x01(\tR\x0eidSubscription\x12\"\n" +
	"\rid_plan_price\x18\x02 \x01(\tR\vidPlanPrice\x12\x1f\n" +
	"\vcoupon_code\x18\x03 \x01(\tR\n" +
	"couponCode\"_\n" +
	".CustomerOrderServiceExtendSubscriptionResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error2\xf9\x03\n" +
	"\x14CustomerOrderService\x12\xaf\x01\n" +
	"\x1aCalculateOrderSubscription\x12G.billing.order.v1.CustomerOrderServiceCalculateOrderSubscriptionRequest\x1aH.billing.order.v1.CustomerOrderServiceCalculateOrderSubscriptionResponse\x12\x94\x01\n" +
	"\x11OrderSubscription\x12>.billing.order.v1.CustomerOrderServiceOrderSubscriptionRequest\x1a?.billing.order.v1.CustomerOrderServiceOrderSubscriptionResponse\x12\x97\x01\n" +
	"\x12ExtendSubscription\x12?.billing.order.v1.CustomerOrderServiceExtendSubscriptionRequest\<EMAIL>/algo/algoproxy-proto/gen/billing/order/v1;orderv1b\x06proto3"

var (
	file_billing_order_v1_customer_proto_rawDescOnce sync.Once
	file_billing_order_v1_customer_proto_rawDescData []byte
)

func file_billing_order_v1_customer_proto_rawDescGZIP() []byte {
	file_billing_order_v1_customer_proto_rawDescOnce.Do(func() {
		file_billing_order_v1_customer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_billing_order_v1_customer_proto_rawDesc), len(file_billing_order_v1_customer_proto_rawDesc)))
	})
	return file_billing_order_v1_customer_proto_rawDescData
}

var file_billing_order_v1_customer_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_billing_order_v1_customer_proto_goTypes = []any{
	(*CustomerOrderServiceCalculateOrderSubscriptionRequest)(nil),  // 0: billing.order.v1.CustomerOrderServiceCalculateOrderSubscriptionRequest
	(*CustomerOrderServiceCalculateOrderSubscriptionResponse)(nil), // 1: billing.order.v1.CustomerOrderServiceCalculateOrderSubscriptionResponse
	(*CustomerOrderServiceOrderSubscriptionRequest)(nil),           // 2: billing.order.v1.CustomerOrderServiceOrderSubscriptionRequest
	(*CustomerOrderServiceOrderSubscriptionResponse)(nil),          // 3: billing.order.v1.CustomerOrderServiceOrderSubscriptionResponse
	(*CustomerOrderServiceExtendSubscriptionRequest)(nil),          // 4: billing.order.v1.CustomerOrderServiceExtendSubscriptionRequest
	(*CustomerOrderServiceExtendSubscriptionResponse)(nil),         // 5: billing.order.v1.CustomerOrderServiceExtendSubscriptionResponse
	(*v1.ErrorMessage)(nil),                                        // 6: errmsg.v1.ErrorMessage
}
var file_billing_order_v1_customer_proto_depIdxs = []int32{
	6, // 0: billing.order.v1.CustomerOrderServiceCalculateOrderSubscriptionResponse.error:type_name -> errmsg.v1.ErrorMessage
	6, // 1: billing.order.v1.CustomerOrderServiceOrderSubscriptionResponse.error:type_name -> errmsg.v1.ErrorMessage
	6, // 2: billing.order.v1.CustomerOrderServiceExtendSubscriptionResponse.error:type_name -> errmsg.v1.ErrorMessage
	0, // 3: billing.order.v1.CustomerOrderService.CalculateOrderSubscription:input_type -> billing.order.v1.CustomerOrderServiceCalculateOrderSubscriptionRequest
	2, // 4: billing.order.v1.CustomerOrderService.OrderSubscription:input_type -> billing.order.v1.CustomerOrderServiceOrderSubscriptionRequest
	4, // 5: billing.order.v1.CustomerOrderService.ExtendSubscription:input_type -> billing.order.v1.CustomerOrderServiceExtendSubscriptionRequest
	1, // 6: billing.order.v1.CustomerOrderService.CalculateOrderSubscription:output_type -> billing.order.v1.CustomerOrderServiceCalculateOrderSubscriptionResponse
	3, // 7: billing.order.v1.CustomerOrderService.OrderSubscription:output_type -> billing.order.v1.CustomerOrderServiceOrderSubscriptionResponse
	5, // 8: billing.order.v1.CustomerOrderService.ExtendSubscription:output_type -> billing.order.v1.CustomerOrderServiceExtendSubscriptionResponse
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_billing_order_v1_customer_proto_init() }
func file_billing_order_v1_customer_proto_init() {
	if File_billing_order_v1_customer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_billing_order_v1_customer_proto_rawDesc), len(file_billing_order_v1_customer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_billing_order_v1_customer_proto_goTypes,
		DependencyIndexes: file_billing_order_v1_customer_proto_depIdxs,
		MessageInfos:      file_billing_order_v1_customer_proto_msgTypes,
	}.Build()
	File_billing_order_v1_customer_proto = out.File
	file_billing_order_v1_customer_proto_goTypes = nil
	file_billing_order_v1_customer_proto_depIdxs = nil
}
