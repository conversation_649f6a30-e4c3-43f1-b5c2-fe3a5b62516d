// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: billing/order/v1/customer.proto

package orderv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/order/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CustomerOrderServiceName is the fully-qualified name of the CustomerOrderService service.
	CustomerOrderServiceName = "billing.order.v1.CustomerOrderService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CustomerOrderServiceCalculateOrderSubscriptionProcedure is the fully-qualified name of the
	// CustomerOrderService's CalculateOrderSubscription RPC.
	CustomerOrderServiceCalculateOrderSubscriptionProcedure = "/billing.order.v1.CustomerOrderService/CalculateOrderSubscription"
	// CustomerOrderServiceOrderSubscriptionProcedure is the fully-qualified name of the
	// CustomerOrderService's OrderSubscription RPC.
	CustomerOrderServiceOrderSubscriptionProcedure = "/billing.order.v1.CustomerOrderService/OrderSubscription"
	// CustomerOrderServiceExtendSubscriptionProcedure is the fully-qualified name of the
	// CustomerOrderService's ExtendSubscription RPC.
	CustomerOrderServiceExtendSubscriptionProcedure = "/billing.order.v1.CustomerOrderService/ExtendSubscription"
)

// CustomerOrderServiceClient is a client for the billing.order.v1.CustomerOrderService service.
type CustomerOrderServiceClient interface {
	CalculateOrderSubscription(context.Context, *connect.Request[v1.CustomerOrderServiceCalculateOrderSubscriptionRequest]) (*connect.Response[v1.CustomerOrderServiceCalculateOrderSubscriptionResponse], error)
	OrderSubscription(context.Context, *connect.Request[v1.CustomerOrderServiceOrderSubscriptionRequest]) (*connect.Response[v1.CustomerOrderServiceOrderSubscriptionResponse], error)
	ExtendSubscription(context.Context, *connect.Request[v1.CustomerOrderServiceExtendSubscriptionRequest]) (*connect.Response[v1.CustomerOrderServiceExtendSubscriptionResponse], error)
}

// NewCustomerOrderServiceClient constructs a client for the billing.order.v1.CustomerOrderService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCustomerOrderServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CustomerOrderServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	customerOrderServiceMethods := v1.File_billing_order_v1_customer_proto.Services().ByName("CustomerOrderService").Methods()
	return &customerOrderServiceClient{
		calculateOrderSubscription: connect.NewClient[v1.CustomerOrderServiceCalculateOrderSubscriptionRequest, v1.CustomerOrderServiceCalculateOrderSubscriptionResponse](
			httpClient,
			baseURL+CustomerOrderServiceCalculateOrderSubscriptionProcedure,
			connect.WithSchema(customerOrderServiceMethods.ByName("CalculateOrderSubscription")),
			connect.WithClientOptions(opts...),
		),
		orderSubscription: connect.NewClient[v1.CustomerOrderServiceOrderSubscriptionRequest, v1.CustomerOrderServiceOrderSubscriptionResponse](
			httpClient,
			baseURL+CustomerOrderServiceOrderSubscriptionProcedure,
			connect.WithSchema(customerOrderServiceMethods.ByName("OrderSubscription")),
			connect.WithClientOptions(opts...),
		),
		extendSubscription: connect.NewClient[v1.CustomerOrderServiceExtendSubscriptionRequest, v1.CustomerOrderServiceExtendSubscriptionResponse](
			httpClient,
			baseURL+CustomerOrderServiceExtendSubscriptionProcedure,
			connect.WithSchema(customerOrderServiceMethods.ByName("ExtendSubscription")),
			connect.WithClientOptions(opts...),
		),
	}
}

// customerOrderServiceClient implements CustomerOrderServiceClient.
type customerOrderServiceClient struct {
	calculateOrderSubscription *connect.Client[v1.CustomerOrderServiceCalculateOrderSubscriptionRequest, v1.CustomerOrderServiceCalculateOrderSubscriptionResponse]
	orderSubscription          *connect.Client[v1.CustomerOrderServiceOrderSubscriptionRequest, v1.CustomerOrderServiceOrderSubscriptionResponse]
	extendSubscription         *connect.Client[v1.CustomerOrderServiceExtendSubscriptionRequest, v1.CustomerOrderServiceExtendSubscriptionResponse]
}

// CalculateOrderSubscription calls
// billing.order.v1.CustomerOrderService.CalculateOrderSubscription.
func (c *customerOrderServiceClient) CalculateOrderSubscription(ctx context.Context, req *connect.Request[v1.CustomerOrderServiceCalculateOrderSubscriptionRequest]) (*connect.Response[v1.CustomerOrderServiceCalculateOrderSubscriptionResponse], error) {
	return c.calculateOrderSubscription.CallUnary(ctx, req)
}

// OrderSubscription calls billing.order.v1.CustomerOrderService.OrderSubscription.
func (c *customerOrderServiceClient) OrderSubscription(ctx context.Context, req *connect.Request[v1.CustomerOrderServiceOrderSubscriptionRequest]) (*connect.Response[v1.CustomerOrderServiceOrderSubscriptionResponse], error) {
	return c.orderSubscription.CallUnary(ctx, req)
}

// ExtendSubscription calls billing.order.v1.CustomerOrderService.ExtendSubscription.
func (c *customerOrderServiceClient) ExtendSubscription(ctx context.Context, req *connect.Request[v1.CustomerOrderServiceExtendSubscriptionRequest]) (*connect.Response[v1.CustomerOrderServiceExtendSubscriptionResponse], error) {
	return c.extendSubscription.CallUnary(ctx, req)
}

// CustomerOrderServiceHandler is an implementation of the billing.order.v1.CustomerOrderService
// service.
type CustomerOrderServiceHandler interface {
	CalculateOrderSubscription(context.Context, *connect.Request[v1.CustomerOrderServiceCalculateOrderSubscriptionRequest]) (*connect.Response[v1.CustomerOrderServiceCalculateOrderSubscriptionResponse], error)
	OrderSubscription(context.Context, *connect.Request[v1.CustomerOrderServiceOrderSubscriptionRequest]) (*connect.Response[v1.CustomerOrderServiceOrderSubscriptionResponse], error)
	ExtendSubscription(context.Context, *connect.Request[v1.CustomerOrderServiceExtendSubscriptionRequest]) (*connect.Response[v1.CustomerOrderServiceExtendSubscriptionResponse], error)
}

// NewCustomerOrderServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCustomerOrderServiceHandler(svc CustomerOrderServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	customerOrderServiceMethods := v1.File_billing_order_v1_customer_proto.Services().ByName("CustomerOrderService").Methods()
	customerOrderServiceCalculateOrderSubscriptionHandler := connect.NewUnaryHandler(
		CustomerOrderServiceCalculateOrderSubscriptionProcedure,
		svc.CalculateOrderSubscription,
		connect.WithSchema(customerOrderServiceMethods.ByName("CalculateOrderSubscription")),
		connect.WithHandlerOptions(opts...),
	)
	customerOrderServiceOrderSubscriptionHandler := connect.NewUnaryHandler(
		CustomerOrderServiceOrderSubscriptionProcedure,
		svc.OrderSubscription,
		connect.WithSchema(customerOrderServiceMethods.ByName("OrderSubscription")),
		connect.WithHandlerOptions(opts...),
	)
	customerOrderServiceExtendSubscriptionHandler := connect.NewUnaryHandler(
		CustomerOrderServiceExtendSubscriptionProcedure,
		svc.ExtendSubscription,
		connect.WithSchema(customerOrderServiceMethods.ByName("ExtendSubscription")),
		connect.WithHandlerOptions(opts...),
	)
	return "/billing.order.v1.CustomerOrderService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CustomerOrderServiceCalculateOrderSubscriptionProcedure:
			customerOrderServiceCalculateOrderSubscriptionHandler.ServeHTTP(w, r)
		case CustomerOrderServiceOrderSubscriptionProcedure:
			customerOrderServiceOrderSubscriptionHandler.ServeHTTP(w, r)
		case CustomerOrderServiceExtendSubscriptionProcedure:
			customerOrderServiceExtendSubscriptionHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCustomerOrderServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCustomerOrderServiceHandler struct{}

func (UnimplementedCustomerOrderServiceHandler) CalculateOrderSubscription(context.Context, *connect.Request[v1.CustomerOrderServiceCalculateOrderSubscriptionRequest]) (*connect.Response[v1.CustomerOrderServiceCalculateOrderSubscriptionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.order.v1.CustomerOrderService.CalculateOrderSubscription is not implemented"))
}

func (UnimplementedCustomerOrderServiceHandler) OrderSubscription(context.Context, *connect.Request[v1.CustomerOrderServiceOrderSubscriptionRequest]) (*connect.Response[v1.CustomerOrderServiceOrderSubscriptionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.order.v1.CustomerOrderService.OrderSubscription is not implemented"))
}

func (UnimplementedCustomerOrderServiceHandler) ExtendSubscription(context.Context, *connect.Request[v1.CustomerOrderServiceExtendSubscriptionRequest]) (*connect.Response[v1.CustomerOrderServiceExtendSubscriptionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.order.v1.CustomerOrderService.ExtendSubscription is not implemented"))
}
