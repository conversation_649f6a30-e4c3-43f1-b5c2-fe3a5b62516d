// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: billing/merchant/v1/backoffice.proto

package merchantv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackofficeMerchantServiceCreateMerchantRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Domain        string                 `protobuf:"bytes,2,opt,name=domain,proto3" json:"domain,omitempty"`
	Currency      v1.Currency            `protobuf:"varint,3,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	AppCountry    v1.AppCountry          `protobuf:"varint,4,opt,name=app_country,json=appCountry,proto3,enum=algoenum.v1.AppCountry" json:"app_country,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeMerchantServiceCreateMerchantRequest) Reset() {
	*x = BackofficeMerchantServiceCreateMerchantRequest{}
	mi := &file_billing_merchant_v1_backoffice_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeMerchantServiceCreateMerchantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeMerchantServiceCreateMerchantRequest) ProtoMessage() {}

func (x *BackofficeMerchantServiceCreateMerchantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_merchant_v1_backoffice_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeMerchantServiceCreateMerchantRequest.ProtoReflect.Descriptor instead.
func (*BackofficeMerchantServiceCreateMerchantRequest) Descriptor() ([]byte, []int) {
	return file_billing_merchant_v1_backoffice_proto_rawDescGZIP(), []int{0}
}

func (x *BackofficeMerchantServiceCreateMerchantRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeMerchantServiceCreateMerchantRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *BackofficeMerchantServiceCreateMerchantRequest) GetCurrency() v1.Currency {
	if x != nil {
		return x.Currency
	}
	return v1.Currency(0)
}

func (x *BackofficeMerchantServiceCreateMerchantRequest) GetAppCountry() v1.AppCountry {
	if x != nil {
		return x.AppCountry
	}
	return v1.AppCountry(0)
}

type BackofficeMerchantServiceCreateMerchantResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeMerchantServiceCreateMerchantResponse) Reset() {
	*x = BackofficeMerchantServiceCreateMerchantResponse{}
	mi := &file_billing_merchant_v1_backoffice_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeMerchantServiceCreateMerchantResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeMerchantServiceCreateMerchantResponse) ProtoMessage() {}

func (x *BackofficeMerchantServiceCreateMerchantResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_merchant_v1_backoffice_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeMerchantServiceCreateMerchantResponse.ProtoReflect.Descriptor instead.
func (*BackofficeMerchantServiceCreateMerchantResponse) Descriptor() ([]byte, []int) {
	return file_billing_merchant_v1_backoffice_proto_rawDescGZIP(), []int{1}
}

func (x *BackofficeMerchantServiceCreateMerchantResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeMerchantServiceFetchMerchantRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NameSearch    string                 `protobuf:"bytes,1,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	State         *v12.State             `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v12.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeMerchantServiceFetchMerchantRequest) Reset() {
	*x = BackofficeMerchantServiceFetchMerchantRequest{}
	mi := &file_billing_merchant_v1_backoffice_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeMerchantServiceFetchMerchantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeMerchantServiceFetchMerchantRequest) ProtoMessage() {}

func (x *BackofficeMerchantServiceFetchMerchantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_merchant_v1_backoffice_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeMerchantServiceFetchMerchantRequest.ProtoReflect.Descriptor instead.
func (*BackofficeMerchantServiceFetchMerchantRequest) Descriptor() ([]byte, []int) {
	return file_billing_merchant_v1_backoffice_proto_rawDescGZIP(), []int{2}
}

func (x *BackofficeMerchantServiceFetchMerchantRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *BackofficeMerchantServiceFetchMerchantRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeMerchantServiceFetchMerchantRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeMerchantServiceFetchMerchantResponse struct {
	state         protoimpl.MessageState              `protogen:"open.v1"`
	Error         *v11.ErrorMessage                   `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v12.PaginationResponse             `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Merchants     []*MerchantBackofficeMerchantEntity `protobuf:"bytes,3,rep,name=merchants,proto3" json:"merchants,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeMerchantServiceFetchMerchantResponse) Reset() {
	*x = BackofficeMerchantServiceFetchMerchantResponse{}
	mi := &file_billing_merchant_v1_backoffice_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeMerchantServiceFetchMerchantResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeMerchantServiceFetchMerchantResponse) ProtoMessage() {}

func (x *BackofficeMerchantServiceFetchMerchantResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_merchant_v1_backoffice_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeMerchantServiceFetchMerchantResponse.ProtoReflect.Descriptor instead.
func (*BackofficeMerchantServiceFetchMerchantResponse) Descriptor() ([]byte, []int) {
	return file_billing_merchant_v1_backoffice_proto_rawDescGZIP(), []int{3}
}

func (x *BackofficeMerchantServiceFetchMerchantResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeMerchantServiceFetchMerchantResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficeMerchantServiceFetchMerchantResponse) GetMerchants() []*MerchantBackofficeMerchantEntity {
	if x != nil {
		return x.Merchants
	}
	return nil
}

type BackofficeMerchantServiceUpdateMerchantRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant    string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	Currency      v1.Currency            `protobuf:"varint,2,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	State         *v12.State             `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeMerchantServiceUpdateMerchantRequest) Reset() {
	*x = BackofficeMerchantServiceUpdateMerchantRequest{}
	mi := &file_billing_merchant_v1_backoffice_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeMerchantServiceUpdateMerchantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeMerchantServiceUpdateMerchantRequest) ProtoMessage() {}

func (x *BackofficeMerchantServiceUpdateMerchantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_billing_merchant_v1_backoffice_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeMerchantServiceUpdateMerchantRequest.ProtoReflect.Descriptor instead.
func (*BackofficeMerchantServiceUpdateMerchantRequest) Descriptor() ([]byte, []int) {
	return file_billing_merchant_v1_backoffice_proto_rawDescGZIP(), []int{4}
}

func (x *BackofficeMerchantServiceUpdateMerchantRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficeMerchantServiceUpdateMerchantRequest) GetCurrency() v1.Currency {
	if x != nil {
		return x.Currency
	}
	return v1.Currency(0)
}

func (x *BackofficeMerchantServiceUpdateMerchantRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

type BackofficeMerchantServiceUpdateMerchantResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeMerchantServiceUpdateMerchantResponse) Reset() {
	*x = BackofficeMerchantServiceUpdateMerchantResponse{}
	mi := &file_billing_merchant_v1_backoffice_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeMerchantServiceUpdateMerchantResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeMerchantServiceUpdateMerchantResponse) ProtoMessage() {}

func (x *BackofficeMerchantServiceUpdateMerchantResponse) ProtoReflect() protoreflect.Message {
	mi := &file_billing_merchant_v1_backoffice_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeMerchantServiceUpdateMerchantResponse.ProtoReflect.Descriptor instead.
func (*BackofficeMerchantServiceUpdateMerchantResponse) Descriptor() ([]byte, []int) {
	return file_billing_merchant_v1_backoffice_proto_rawDescGZIP(), []int{5}
}

func (x *BackofficeMerchantServiceUpdateMerchantResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type MerchantBackofficeMerchantEntity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant    string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Domain        string                 `protobuf:"bytes,3,opt,name=domain,proto3" json:"domain,omitempty"`
	Currency      v1.Currency            `protobuf:"varint,4,opt,name=currency,proto3,enum=algoenum.v1.Currency" json:"currency,omitempty"`
	IsActive      bool                   `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	State         bool                   `protobuf:"varint,6,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantBackofficeMerchantEntity) Reset() {
	*x = MerchantBackofficeMerchantEntity{}
	mi := &file_billing_merchant_v1_backoffice_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantBackofficeMerchantEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantBackofficeMerchantEntity) ProtoMessage() {}

func (x *MerchantBackofficeMerchantEntity) ProtoReflect() protoreflect.Message {
	mi := &file_billing_merchant_v1_backoffice_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantBackofficeMerchantEntity.ProtoReflect.Descriptor instead.
func (*MerchantBackofficeMerchantEntity) Descriptor() ([]byte, []int) {
	return file_billing_merchant_v1_backoffice_proto_rawDescGZIP(), []int{6}
}

func (x *MerchantBackofficeMerchantEntity) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *MerchantBackofficeMerchantEntity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MerchantBackofficeMerchantEntity) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *MerchantBackofficeMerchantEntity) GetCurrency() v1.Currency {
	if x != nil {
		return x.Currency
	}
	return v1.Currency(0)
}

func (x *MerchantBackofficeMerchantEntity) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *MerchantBackofficeMerchantEntity) GetState() bool {
	if x != nil {
		return x.State
	}
	return false
}

var File_billing_merchant_v1_backoffice_proto protoreflect.FileDescriptor

const file_billing_merchant_v1_backoffice_proto_rawDesc = "" +
	"\n" +
	"$billing/merchant/v1/backoffice.proto\x12\x13billing.merchant.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x1aalgoenum/v1/currency.proto\x1a\x1dalgoenum/v1/app_country.proto\x1a\x14utils/v1/utils.proto\"\xc9\x01\n" +
	".BackofficeMerchantServiceCreateMerchantRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06domain\x18\x02 \x01(\tR\x06domain\x121\n" +
	"\bcurrency\x18\x03 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\x128\n" +
	"\vapp_country\x18\x04 \x01(\x0e2\x17.algoenum.v1.AppCountryR\n" +
	"appCountry\"`\n" +
	"/BackofficeMerchantServiceCreateMerchantResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xb4\x01\n" +
	"-BackofficeMerchantServiceFetchMerchantRequest\x12\x1f\n" +
	"\vname_search\x18\x01 \x01(\tR\n" +
	"nameSearch\x12%\n" +
	"\x05state\x18\x02 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xf2\x01\n" +
	".BackofficeMerchantServiceFetchMerchantResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12S\n" +
	"\tmerchants\x18\x03 \x03(\v25.billing.merchant.v1.MerchantBackofficeMerchantEntityR\tmerchants\"\xab\x01\n" +
	".BackofficeMerchantServiceUpdateMerchantRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x121\n" +
	"\bcurrency\x18\x02 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\x12%\n" +
	"\x05state\x18\x03 \x01(\v2\x0f.utils.v1.StateR\x05state\"`\n" +
	"/BackofficeMerchantServiceUpdateMerchantResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xd5\x01\n" +
	" MerchantBackofficeMerchantEntity\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06domain\x18\x03 \x01(\tR\x06domain\x121\n" +
	"\bcurrency\x18\x04 \x01(\x0e2\x15.algoenum.v1.CurrencyR\bcurrency\x12\x1b\n" +
	"\tis_active\x18\x05 \x01(\bR\bisActive\x12\x14\n" +
	"\x05state\x18\x06 \x01(\bR\x05state2\xf2\x03\n" +
	"\x19BackofficeMerchantService\x12\x9b\x01\n" +
	"\x0eCreateMerchant\x12C.billing.merchant.v1.BackofficeMerchantServiceCreateMerchantRequest\x1aD.billing.merchant.v1.BackofficeMerchantServiceCreateMerchantResponse\x12\x98\x01\n" +
	"\rFetchMerchant\x12B.billing.merchant.v1.BackofficeMerchantServiceFetchMerchantRequest\x1aC.billing.merchant.v1.BackofficeMerchantServiceFetchMerchantResponse\x12\x9b\x01\n" +
	"\x0eUpdateMerchant\x12C.billing.merchant.v1.BackofficeMerchantServiceUpdateMerchantRequest\x1aD.billing.merchant.v1.BackofficeMerchantServiceUpdateMerchantResponseBOZMgit.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/merchant/v1;merchantv1b\x06proto3"

var (
	file_billing_merchant_v1_backoffice_proto_rawDescOnce sync.Once
	file_billing_merchant_v1_backoffice_proto_rawDescData []byte
)

func file_billing_merchant_v1_backoffice_proto_rawDescGZIP() []byte {
	file_billing_merchant_v1_backoffice_proto_rawDescOnce.Do(func() {
		file_billing_merchant_v1_backoffice_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_billing_merchant_v1_backoffice_proto_rawDesc), len(file_billing_merchant_v1_backoffice_proto_rawDesc)))
	})
	return file_billing_merchant_v1_backoffice_proto_rawDescData
}

var file_billing_merchant_v1_backoffice_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_billing_merchant_v1_backoffice_proto_goTypes = []any{
	(*BackofficeMerchantServiceCreateMerchantRequest)(nil),  // 0: billing.merchant.v1.BackofficeMerchantServiceCreateMerchantRequest
	(*BackofficeMerchantServiceCreateMerchantResponse)(nil), // 1: billing.merchant.v1.BackofficeMerchantServiceCreateMerchantResponse
	(*BackofficeMerchantServiceFetchMerchantRequest)(nil),   // 2: billing.merchant.v1.BackofficeMerchantServiceFetchMerchantRequest
	(*BackofficeMerchantServiceFetchMerchantResponse)(nil),  // 3: billing.merchant.v1.BackofficeMerchantServiceFetchMerchantResponse
	(*BackofficeMerchantServiceUpdateMerchantRequest)(nil),  // 4: billing.merchant.v1.BackofficeMerchantServiceUpdateMerchantRequest
	(*BackofficeMerchantServiceUpdateMerchantResponse)(nil), // 5: billing.merchant.v1.BackofficeMerchantServiceUpdateMerchantResponse
	(*MerchantBackofficeMerchantEntity)(nil),                // 6: billing.merchant.v1.MerchantBackofficeMerchantEntity
	(v1.Currency)(0),                                        // 7: algoenum.v1.Currency
	(v1.AppCountry)(0),                                      // 8: algoenum.v1.AppCountry
	(*v11.ErrorMessage)(nil),                                // 9: errmsg.v1.ErrorMessage
	(*v12.State)(nil),                                       // 10: utils.v1.State
	(*v12.PaginationRequest)(nil),                           // 11: utils.v1.PaginationRequest
	(*v12.PaginationResponse)(nil),                          // 12: utils.v1.PaginationResponse
}
var file_billing_merchant_v1_backoffice_proto_depIdxs = []int32{
	7,  // 0: billing.merchant.v1.BackofficeMerchantServiceCreateMerchantRequest.currency:type_name -> algoenum.v1.Currency
	8,  // 1: billing.merchant.v1.BackofficeMerchantServiceCreateMerchantRequest.app_country:type_name -> algoenum.v1.AppCountry
	9,  // 2: billing.merchant.v1.BackofficeMerchantServiceCreateMerchantResponse.error:type_name -> errmsg.v1.ErrorMessage
	10, // 3: billing.merchant.v1.BackofficeMerchantServiceFetchMerchantRequest.state:type_name -> utils.v1.State
	11, // 4: billing.merchant.v1.BackofficeMerchantServiceFetchMerchantRequest.pagination:type_name -> utils.v1.PaginationRequest
	9,  // 5: billing.merchant.v1.BackofficeMerchantServiceFetchMerchantResponse.error:type_name -> errmsg.v1.ErrorMessage
	12, // 6: billing.merchant.v1.BackofficeMerchantServiceFetchMerchantResponse.pagination:type_name -> utils.v1.PaginationResponse
	6,  // 7: billing.merchant.v1.BackofficeMerchantServiceFetchMerchantResponse.merchants:type_name -> billing.merchant.v1.MerchantBackofficeMerchantEntity
	7,  // 8: billing.merchant.v1.BackofficeMerchantServiceUpdateMerchantRequest.currency:type_name -> algoenum.v1.Currency
	10, // 9: billing.merchant.v1.BackofficeMerchantServiceUpdateMerchantRequest.state:type_name -> utils.v1.State
	9,  // 10: billing.merchant.v1.BackofficeMerchantServiceUpdateMerchantResponse.error:type_name -> errmsg.v1.ErrorMessage
	7,  // 11: billing.merchant.v1.MerchantBackofficeMerchantEntity.currency:type_name -> algoenum.v1.Currency
	0,  // 12: billing.merchant.v1.BackofficeMerchantService.CreateMerchant:input_type -> billing.merchant.v1.BackofficeMerchantServiceCreateMerchantRequest
	2,  // 13: billing.merchant.v1.BackofficeMerchantService.FetchMerchant:input_type -> billing.merchant.v1.BackofficeMerchantServiceFetchMerchantRequest
	4,  // 14: billing.merchant.v1.BackofficeMerchantService.UpdateMerchant:input_type -> billing.merchant.v1.BackofficeMerchantServiceUpdateMerchantRequest
	1,  // 15: billing.merchant.v1.BackofficeMerchantService.CreateMerchant:output_type -> billing.merchant.v1.BackofficeMerchantServiceCreateMerchantResponse
	3,  // 16: billing.merchant.v1.BackofficeMerchantService.FetchMerchant:output_type -> billing.merchant.v1.BackofficeMerchantServiceFetchMerchantResponse
	5,  // 17: billing.merchant.v1.BackofficeMerchantService.UpdateMerchant:output_type -> billing.merchant.v1.BackofficeMerchantServiceUpdateMerchantResponse
	15, // [15:18] is the sub-list for method output_type
	12, // [12:15] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_billing_merchant_v1_backoffice_proto_init() }
func file_billing_merchant_v1_backoffice_proto_init() {
	if File_billing_merchant_v1_backoffice_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_billing_merchant_v1_backoffice_proto_rawDesc), len(file_billing_merchant_v1_backoffice_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_billing_merchant_v1_backoffice_proto_goTypes,
		DependencyIndexes: file_billing_merchant_v1_backoffice_proto_depIdxs,
		MessageInfos:      file_billing_merchant_v1_backoffice_proto_msgTypes,
	}.Build()
	File_billing_merchant_v1_backoffice_proto = out.File
	file_billing_merchant_v1_backoffice_proto_goTypes = nil
	file_billing_merchant_v1_backoffice_proto_depIdxs = nil
}
