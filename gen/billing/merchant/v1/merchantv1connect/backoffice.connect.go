// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: billing/merchant/v1/backoffice.proto

package merchantv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/merchant/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackofficeMerchantServiceName is the fully-qualified name of the BackofficeMerchantService
	// service.
	BackofficeMerchantServiceName = "billing.merchant.v1.BackofficeMerchantService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackofficeMerchantServiceCreateMerchantProcedure is the fully-qualified name of the
	// BackofficeMerchantService's CreateMerchant RPC.
	BackofficeMerchantServiceCreateMerchantProcedure = "/billing.merchant.v1.BackofficeMerchantService/CreateMerchant"
	// BackofficeMerchantServiceFetchMerchantProcedure is the fully-qualified name of the
	// BackofficeMerchantService's FetchMerchant RPC.
	BackofficeMerchantServiceFetchMerchantProcedure = "/billing.merchant.v1.BackofficeMerchantService/FetchMerchant"
	// BackofficeMerchantServiceUpdateMerchantProcedure is the fully-qualified name of the
	// BackofficeMerchantService's UpdateMerchant RPC.
	BackofficeMerchantServiceUpdateMerchantProcedure = "/billing.merchant.v1.BackofficeMerchantService/UpdateMerchant"
)

// BackofficeMerchantServiceClient is a client for the billing.merchant.v1.BackofficeMerchantService
// service.
type BackofficeMerchantServiceClient interface {
	CreateMerchant(context.Context, *connect.Request[v1.BackofficeMerchantServiceCreateMerchantRequest]) (*connect.Response[v1.BackofficeMerchantServiceCreateMerchantResponse], error)
	FetchMerchant(context.Context, *connect.Request[v1.BackofficeMerchantServiceFetchMerchantRequest]) (*connect.Response[v1.BackofficeMerchantServiceFetchMerchantResponse], error)
	UpdateMerchant(context.Context, *connect.Request[v1.BackofficeMerchantServiceUpdateMerchantRequest]) (*connect.Response[v1.BackofficeMerchantServiceUpdateMerchantResponse], error)
}

// NewBackofficeMerchantServiceClient constructs a client for the
// billing.merchant.v1.BackofficeMerchantService service. By default, it uses the Connect protocol
// with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed requests. To
// use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or connect.WithGRPCWeb()
// options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackofficeMerchantServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackofficeMerchantServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backofficeMerchantServiceMethods := v1.File_billing_merchant_v1_backoffice_proto.Services().ByName("BackofficeMerchantService").Methods()
	return &backofficeMerchantServiceClient{
		createMerchant: connect.NewClient[v1.BackofficeMerchantServiceCreateMerchantRequest, v1.BackofficeMerchantServiceCreateMerchantResponse](
			httpClient,
			baseURL+BackofficeMerchantServiceCreateMerchantProcedure,
			connect.WithSchema(backofficeMerchantServiceMethods.ByName("CreateMerchant")),
			connect.WithClientOptions(opts...),
		),
		fetchMerchant: connect.NewClient[v1.BackofficeMerchantServiceFetchMerchantRequest, v1.BackofficeMerchantServiceFetchMerchantResponse](
			httpClient,
			baseURL+BackofficeMerchantServiceFetchMerchantProcedure,
			connect.WithSchema(backofficeMerchantServiceMethods.ByName("FetchMerchant")),
			connect.WithClientOptions(opts...),
		),
		updateMerchant: connect.NewClient[v1.BackofficeMerchantServiceUpdateMerchantRequest, v1.BackofficeMerchantServiceUpdateMerchantResponse](
			httpClient,
			baseURL+BackofficeMerchantServiceUpdateMerchantProcedure,
			connect.WithSchema(backofficeMerchantServiceMethods.ByName("UpdateMerchant")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backofficeMerchantServiceClient implements BackofficeMerchantServiceClient.
type backofficeMerchantServiceClient struct {
	createMerchant *connect.Client[v1.BackofficeMerchantServiceCreateMerchantRequest, v1.BackofficeMerchantServiceCreateMerchantResponse]
	fetchMerchant  *connect.Client[v1.BackofficeMerchantServiceFetchMerchantRequest, v1.BackofficeMerchantServiceFetchMerchantResponse]
	updateMerchant *connect.Client[v1.BackofficeMerchantServiceUpdateMerchantRequest, v1.BackofficeMerchantServiceUpdateMerchantResponse]
}

// CreateMerchant calls billing.merchant.v1.BackofficeMerchantService.CreateMerchant.
func (c *backofficeMerchantServiceClient) CreateMerchant(ctx context.Context, req *connect.Request[v1.BackofficeMerchantServiceCreateMerchantRequest]) (*connect.Response[v1.BackofficeMerchantServiceCreateMerchantResponse], error) {
	return c.createMerchant.CallUnary(ctx, req)
}

// FetchMerchant calls billing.merchant.v1.BackofficeMerchantService.FetchMerchant.
func (c *backofficeMerchantServiceClient) FetchMerchant(ctx context.Context, req *connect.Request[v1.BackofficeMerchantServiceFetchMerchantRequest]) (*connect.Response[v1.BackofficeMerchantServiceFetchMerchantResponse], error) {
	return c.fetchMerchant.CallUnary(ctx, req)
}

// UpdateMerchant calls billing.merchant.v1.BackofficeMerchantService.UpdateMerchant.
func (c *backofficeMerchantServiceClient) UpdateMerchant(ctx context.Context, req *connect.Request[v1.BackofficeMerchantServiceUpdateMerchantRequest]) (*connect.Response[v1.BackofficeMerchantServiceUpdateMerchantResponse], error) {
	return c.updateMerchant.CallUnary(ctx, req)
}

// BackofficeMerchantServiceHandler is an implementation of the
// billing.merchant.v1.BackofficeMerchantService service.
type BackofficeMerchantServiceHandler interface {
	CreateMerchant(context.Context, *connect.Request[v1.BackofficeMerchantServiceCreateMerchantRequest]) (*connect.Response[v1.BackofficeMerchantServiceCreateMerchantResponse], error)
	FetchMerchant(context.Context, *connect.Request[v1.BackofficeMerchantServiceFetchMerchantRequest]) (*connect.Response[v1.BackofficeMerchantServiceFetchMerchantResponse], error)
	UpdateMerchant(context.Context, *connect.Request[v1.BackofficeMerchantServiceUpdateMerchantRequest]) (*connect.Response[v1.BackofficeMerchantServiceUpdateMerchantResponse], error)
}

// NewBackofficeMerchantServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackofficeMerchantServiceHandler(svc BackofficeMerchantServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backofficeMerchantServiceMethods := v1.File_billing_merchant_v1_backoffice_proto.Services().ByName("BackofficeMerchantService").Methods()
	backofficeMerchantServiceCreateMerchantHandler := connect.NewUnaryHandler(
		BackofficeMerchantServiceCreateMerchantProcedure,
		svc.CreateMerchant,
		connect.WithSchema(backofficeMerchantServiceMethods.ByName("CreateMerchant")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeMerchantServiceFetchMerchantHandler := connect.NewUnaryHandler(
		BackofficeMerchantServiceFetchMerchantProcedure,
		svc.FetchMerchant,
		connect.WithSchema(backofficeMerchantServiceMethods.ByName("FetchMerchant")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeMerchantServiceUpdateMerchantHandler := connect.NewUnaryHandler(
		BackofficeMerchantServiceUpdateMerchantProcedure,
		svc.UpdateMerchant,
		connect.WithSchema(backofficeMerchantServiceMethods.ByName("UpdateMerchant")),
		connect.WithHandlerOptions(opts...),
	)
	return "/billing.merchant.v1.BackofficeMerchantService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackofficeMerchantServiceCreateMerchantProcedure:
			backofficeMerchantServiceCreateMerchantHandler.ServeHTTP(w, r)
		case BackofficeMerchantServiceFetchMerchantProcedure:
			backofficeMerchantServiceFetchMerchantHandler.ServeHTTP(w, r)
		case BackofficeMerchantServiceUpdateMerchantProcedure:
			backofficeMerchantServiceUpdateMerchantHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackofficeMerchantServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackofficeMerchantServiceHandler struct{}

func (UnimplementedBackofficeMerchantServiceHandler) CreateMerchant(context.Context, *connect.Request[v1.BackofficeMerchantServiceCreateMerchantRequest]) (*connect.Response[v1.BackofficeMerchantServiceCreateMerchantResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.merchant.v1.BackofficeMerchantService.CreateMerchant is not implemented"))
}

func (UnimplementedBackofficeMerchantServiceHandler) FetchMerchant(context.Context, *connect.Request[v1.BackofficeMerchantServiceFetchMerchantRequest]) (*connect.Response[v1.BackofficeMerchantServiceFetchMerchantResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.merchant.v1.BackofficeMerchantService.FetchMerchant is not implemented"))
}

func (UnimplementedBackofficeMerchantServiceHandler) UpdateMerchant(context.Context, *connect.Request[v1.BackofficeMerchantServiceUpdateMerchantRequest]) (*connect.Response[v1.BackofficeMerchantServiceUpdateMerchantResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("billing.merchant.v1.BackofficeMerchantService.UpdateMerchant is not implemented"))
}
