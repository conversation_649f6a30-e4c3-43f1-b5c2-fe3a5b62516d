// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: utils/v1/utils.proto

package utilsv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type State struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsActive      bool                   `protobuf:"varint,1,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	IsDeactivate  bool                   `protobuf:"varint,2,opt,name=is_deactivate,json=isDeactivate,proto3" json:"is_deactivate,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *State) Reset() {
	*x = State{}
	mi := &file_utils_v1_utils_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *State) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*State) ProtoMessage() {}

func (x *State) ProtoReflect() protoreflect.Message {
	mi := &file_utils_v1_utils_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use State.ProtoReflect.Descriptor instead.
func (*State) Descriptor() ([]byte, []int) {
	return file_utils_v1_utils_proto_rawDescGZIP(), []int{0}
}

func (x *State) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *State) GetIsDeactivate() bool {
	if x != nil {
		return x.IsDeactivate
	}
	return false
}

type Bool struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsTrue        bool                   `protobuf:"varint,1,opt,name=is_true,json=isTrue,proto3" json:"is_true,omitempty"`
	IsFalse       bool                   `protobuf:"varint,2,opt,name=is_false,json=isFalse,proto3" json:"is_false,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Bool) Reset() {
	*x = Bool{}
	mi := &file_utils_v1_utils_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Bool) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bool) ProtoMessage() {}

func (x *Bool) ProtoReflect() protoreflect.Message {
	mi := &file_utils_v1_utils_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bool.ProtoReflect.Descriptor instead.
func (*Bool) Descriptor() ([]byte, []int) {
	return file_utils_v1_utils_proto_rawDescGZIP(), []int{1}
}

func (x *Bool) GetIsTrue() bool {
	if x != nil {
		return x.IsTrue
	}
	return false
}

func (x *Bool) GetIsFalse() bool {
	if x != nil {
		return x.IsFalse
	}
	return false
}

type PaginationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PageSize      int64                  `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageNumber    int64                  `protobuf:"varint,2,opt,name=page_number,json=pageNumber,proto3" json:"page_number,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaginationRequest) Reset() {
	*x = PaginationRequest{}
	mi := &file_utils_v1_utils_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaginationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginationRequest) ProtoMessage() {}

func (x *PaginationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_utils_v1_utils_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginationRequest.ProtoReflect.Descriptor instead.
func (*PaginationRequest) Descriptor() ([]byte, []int) {
	return file_utils_v1_utils_proto_rawDescGZIP(), []int{2}
}

func (x *PaginationRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PaginationRequest) GetPageNumber() int64 {
	if x != nil {
		return x.PageNumber
	}
	return 0
}

type PaginationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CurrentPage   int64                  `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page,omitempty"`
	PageSize      int64                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Total         int64                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	TotalPages    int64                  `protobuf:"varint,4,opt,name=total_pages,json=totalPages,proto3" json:"total_pages,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaginationResponse) Reset() {
	*x = PaginationResponse{}
	mi := &file_utils_v1_utils_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaginationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginationResponse) ProtoMessage() {}

func (x *PaginationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_utils_v1_utils_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginationResponse.ProtoReflect.Descriptor instead.
func (*PaginationResponse) Descriptor() ([]byte, []int) {
	return file_utils_v1_utils_proto_rawDescGZIP(), []int{3}
}

func (x *PaginationResponse) GetCurrentPage() int64 {
	if x != nil {
		return x.CurrentPage
	}
	return 0
}

func (x *PaginationResponse) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PaginationResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *PaginationResponse) GetTotalPages() int64 {
	if x != nil {
		return x.TotalPages
	}
	return 0
}

var File_utils_v1_utils_proto protoreflect.FileDescriptor

const file_utils_v1_utils_proto_rawDesc = "" +
	"\n" +
	"\x14utils/v1/utils.proto\x12\butils.v1\"I\n" +
	"\x05State\x12\x1b\n" +
	"\tis_active\x18\x01 \x01(\bR\bisActive\x12#\n" +
	"\ris_deactivate\x18\x02 \x01(\bR\fisDeactivate\":\n" +
	"\x04Bool\x12\x17\n" +
	"\ais_true\x18\x01 \x01(\bR\x06isTrue\x12\x19\n" +
	"\bis_false\x18\x02 \x01(\bR\aisFalse\"Q\n" +
	"\x11PaginationRequest\x12\x1b\n" +
	"\tpage_size\x18\x01 \x01(\x03R\bpageSize\x12\x1f\n" +
	"\vpage_number\x18\x02 \x01(\x03R\n" +
	"pageNumber\"\x8b\x01\n" +
	"\x12PaginationResponse\x12!\n" +
	"\fcurrent_page\x18\x01 \x01(\x03R\vcurrentPage\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x03R\bpageSize\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x03R\x05total\x12\x1f\n" +
	"\vtotal_pages\x18\x04 \x01(\x03R\n" +
	"totalPagesBAZ?git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1;utilsv1b\x06proto3"

var (
	file_utils_v1_utils_proto_rawDescOnce sync.Once
	file_utils_v1_utils_proto_rawDescData []byte
)

func file_utils_v1_utils_proto_rawDescGZIP() []byte {
	file_utils_v1_utils_proto_rawDescOnce.Do(func() {
		file_utils_v1_utils_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_utils_v1_utils_proto_rawDesc), len(file_utils_v1_utils_proto_rawDesc)))
	})
	return file_utils_v1_utils_proto_rawDescData
}

var file_utils_v1_utils_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_utils_v1_utils_proto_goTypes = []any{
	(*State)(nil),              // 0: utils.v1.State
	(*Bool)(nil),               // 1: utils.v1.Bool
	(*PaginationRequest)(nil),  // 2: utils.v1.PaginationRequest
	(*PaginationResponse)(nil), // 3: utils.v1.PaginationResponse
}
var file_utils_v1_utils_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_utils_v1_utils_proto_init() }
func file_utils_v1_utils_proto_init() {
	if File_utils_v1_utils_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_utils_v1_utils_proto_rawDesc), len(file_utils_v1_utils_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_utils_v1_utils_proto_goTypes,
		DependencyIndexes: file_utils_v1_utils_proto_depIdxs,
		MessageInfos:      file_utils_v1_utils_proto_msgTypes,
	}.Build()
	File_utils_v1_utils_proto = out.File
	file_utils_v1_utils_proto_goTypes = nil
	file_utils_v1_utils_proto_depIdxs = nil
}
