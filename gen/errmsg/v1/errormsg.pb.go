// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: errmsg/v1/errormsg.proto

package errmsgv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorCode int32

const (
	ErrorCode_ERROR_CODE_UNSPECIFIED                         ErrorCode = 0
	ErrorCode_ERROR_CODE_PUBLIC_URL                          ErrorCode = 9999
	ErrorCode_ERROR_CODE_CROSS_SERVICE_DATA_MISMATCH         ErrorCode = 10000
	ErrorCode_ERROR_CODE_CROSS_SERVICE_TELCO_ERROR           ErrorCode = 10001
	ErrorCode_ERROR_CODE_CROSS_SERVICE_LOCATION_ERROR        ErrorCode = 10002
	ErrorCode_ERROR_CODE_SUCCESS                             ErrorCode = 1
	ErrorCode_ERROR_CODE_INTERNAL_SERVER_ERROR               ErrorCode = 2
	ErrorCode_ERROR_CODE_INVALID_REQUEST                     ErrorCode = 3
	ErrorCode_ERROR_CODE_UNAUTHORIZED                        ErrorCode = 4
	ErrorCode_ERROR_CODE_ABORTED                             ErrorCode = 5
	ErrorCode_ERROR_CODE_USERNAME_OR_PASSWORD_DOES_NOT_MATCH ErrorCode = 6
	ErrorCode_ERROR_CODE_USER_HAS_BEEN_BANNED                ErrorCode = 7
	ErrorCode_ERROR_CODE_EMAIL_EXIST                         ErrorCode = 8
	ErrorCode_ERROR_CODE_EMAIL_NOT_EXIST                     ErrorCode = 9
	ErrorCode_ERROR_CODE_OLD_PASSWORD_INCORRECT              ErrorCode = 10
	// auth service
	ErrorCode_ERROR_CODE_AUTH_APP_EXIST                                                                 ErrorCode = 1000
	ErrorCode_ERROR_CODE_AUTH_APP_NOT_EXIST                                                             ErrorCode = 1001
	ErrorCode_ERROR_CODE_AUTH_USER_NOT_EXIST                                                            ErrorCode = 1002
	ErrorCode_ERROR_CODE_AUTH_USER_EXIST                                                                ErrorCode = 1003
	ErrorCode_ERROR_CODE_AUTH_ROLE_EXIST                                                                ErrorCode = 1004
	ErrorCode_ERROR_CODE_AUTH_ROLE_NOT_EXIST                                                            ErrorCode = 1005
	ErrorCode_ERROR_CODE_AUTH_SERVICE_EXIST                                                             ErrorCode = 1006
	ErrorCode_ERROR_CODE_AUTH_SERVICE_NOT_EXIST                                                         ErrorCode = 1007
	ErrorCode_ERROR_CODE_AUTH_PATH_EXIST                                                                ErrorCode = 1008
	ErrorCode_ERROR_CODE_AUTH_PATH_NOT_EXIST                                                            ErrorCode = 1009
	ErrorCode_ERROR_CODE_AUTH_POLICY_EXIST                                                              ErrorCode = 1010
	ErrorCode_ERROR_CODE_AUTH_POLICY_NOT_EXIST                                                          ErrorCode = 1011
	ErrorCode_ERROR_CODE_AUTH_PERMISSION_DENY                                                           ErrorCode = 1012
	ErrorCode_ERROR_CODE_AUTH_ROLE_PRIORITY_MUST_BE_HIGHER                                              ErrorCode = 1013
	ErrorCode_ERROR_CODE_AUTH_OLD_PASSWORD_INCORRECT                                                    ErrorCode = 1014
	ErrorCode_ERROR_CODE_AUTH_TOTP_VERIFY_INCORRECT                                                     ErrorCode = 1016
	ErrorCode_ERROR_CODE_AUTH_TOTP_VERIFY_CORRECT                                                       ErrorCode = 1017
	ErrorCode_ERROR_CODE_AUTH_CONFIG_NOT_EXIST                                                          ErrorCode = 1018
	ErrorCode_ERROR_CODE_AUTH_CONFIG_EXIST                                                              ErrorCode = 1019
	ErrorCode_ERROR_CODE_AUTH_REFRESH_TOKEN_EXPRIRY                                                     ErrorCode = 1020
	ErrorCode_ERROR_CODE_AUTH_OTP_REQUIRED                                                              ErrorCode = 1021
	ErrorCode_ERROR_CODE_AUTH_TOTP_REQUIRED                                                             ErrorCode = 1022
	ErrorCode_ERROR_CODE_AUTH_OTP_INCORRECT                                                             ErrorCode = 1023
	ErrorCode_ERROR_CODE_AUTH_REF_CODE_INCORRECT                                                        ErrorCode = 1024
	ErrorCode_ERROR_CODE_AUTH_REF_EXIST                                                                 ErrorCode = 1025
	ErrorCode_ERROR_CODE_AUTH_OTP_VERIFY_INCORRECT                                                      ErrorCode = 1226
	ErrorCode_ERROR_CODE_AUTH_TOKEN_VERIFY_INCORRECT                                                    ErrorCode = 1227
	ErrorCode_ERROR_CODE_AUTH_USER_INACTIVE                                                             ErrorCode = 1228
	ErrorCode_ERROR_CODE_DNS_DNS_EXIST                                                                  ErrorCode = 2000
	ErrorCode_ERROR_CODE_DNS_DNS_NOT_EXIST                                                              ErrorCode = 2001
	ErrorCode_ERROR_CODE_DNS_DNS1_AND_DNS2_NOT_SAME_IP_TYPE                                             ErrorCode = 2002
	ErrorCode_ERROR_CODE_TELCO_TELCO_EXIST                                                              ErrorCode = 3000
	ErrorCode_ERROR_CODE_TELCO_TELCO_NOT_EXIST                                                          ErrorCode = 3001
	ErrorCode_ERROR_CODE_LOCATION_LOCATION_EXIST                                                        ErrorCode = 4000
	ErrorCode_ERROR_CODE_LOCATION_LOCATION_NOT_EXIST                                                    ErrorCode = 4001
	ErrorCode_ERROR_CODE_RES_RES_ACCOUNT_EXIST                                                          ErrorCode = 5001
	ErrorCode_ERROR_CODE_RES_RES_ACCOUNT_NOT_EXIST                                                      ErrorCode = 5002
	ErrorCode_ERROR_CODE_RES_RES_ACCOUNT_HAS_BINDING_ANOTHER_PORT                                       ErrorCode = 5003
	ErrorCode_ERROR_CODE_RES_RES_NODE_EXIST                                                             ErrorCode = 5004
	ErrorCode_ERROR_CODE_RES_RES_NODE_NOT_EXIST                                                         ErrorCode = 5005
	ErrorCode_ERROR_CODE_RES_RES_NODE_AND_RES_ACCOUNT_NOT_SAME_LOCATION                                 ErrorCode = 5006
	ErrorCode_ERROR_CODE_RES_RES_PORT_EXIST                                                             ErrorCode = 5007
	ErrorCode_ERROR_CODE_RES_RES_PORT_NOT_EXIST                                                         ErrorCode = 5008
	ErrorCode_ERROR_CODE_RES_RES_PORT_TOTAL_RES_PORT_MUST_BE_GREATER_THAN_CURRENT                       ErrorCode = 5009
	ErrorCode_ERROR_CODE_RES_RES_PORT_MUST_HAVE_ACCOUNT_BEFORE_ACTIVE                                   ErrorCode = 5010
	ErrorCode_ERROR_CODE_RES_RES_DEVICE_NOT_EXIST                                                       ErrorCode = 5011
	ErrorCode_ERROR_CODE_MERCHANT_NOT_EXIST                                                             ErrorCode = 6000
	ErrorCode_ERROR_CODE_MERCHANT_USER_NOT_EXIST                                                        ErrorCode = 6001
	ErrorCode_ERROR_CODE_MERCHANT_PRODUCT_BASE_EXIST                                                    ErrorCode = 6002
	ErrorCode_ERROR_CODE_MERCHANT_PRODUCT_BASE_NOT_EXIST                                                ErrorCode = 6003
	ErrorCode_ERROR_CODE_MERCHANT_PRODUCT_EXIST                                                         ErrorCode = 6004
	ErrorCode_ERROR_CODE_MERCHANT_PRODUCT_NOT_EXIST                                                     ErrorCode = 6005
	ErrorCode_ERROR_CODE_MERCHANT_API_NOT_EXIST                                                         ErrorCode = 6006
	ErrorCode_ERROR_CODE_MERCHANT_INSUFFICIENT_USER_BALANCE                                             ErrorCode = 6007
	ErrorCode_ERROR_CODE_MERCHANT_PRODUCT_PRICE_INVALID                                                 ErrorCode = 6008
	ErrorCode_ERROR_CODE_MERCHANT_UPDATE_BALANCE_MISSING_NOTE                                           ErrorCode = 6009
	ErrorCode_ERROR_CODE_MERCHANT_UPDATE_BALANCE_TRANSACTION_TYPE_INVALID                               ErrorCode = 6010
	ErrorCode_ERROR_CODE_MERCHANT_UPDATE_COMPANY_NOT_EXIST                                              ErrorCode = 6011
	ErrorCode_ERROR_CODE_MERCHANT_UPDATE_COMPANY_MEST_EXIST                                             ErrorCode = 6012
	ErrorCode_ERROR_CODE_MERCHANT_UPDATE_CONFIG_MAIL_NOT_EXIST                                          ErrorCode = 6013
	ErrorCode_ERROR_CODE_BILLING_PLAN_MUST_HAVE_PRICE_FOR_ACTIVE_STATE                                  ErrorCode = 7002
	ErrorCode_ERROR_CODE_BILLING_PLAN_PRICE_FOR_BILLING_CYCLE_EXIST                                     ErrorCode = 7008
	ErrorCode_ERROR_CODE_BILLING_PLAN_PRICE_NOT_EXIST                                                   ErrorCode = 7009
	ErrorCode_ERROR_CODE_BILLING_ORDER_INSUFFICIENT_FUNDS                                               ErrorCode = 7010
	ErrorCode_ERROR_CODE_BILLING_PLAN_EXIST                                                             ErrorCode = 8000
	ErrorCode_ERROR_CODE_BILLING_PLAN_NOT_EXIST                                                         ErrorCode = 8001
	ErrorCode_ERROR_CODE_PROXY_MANAGER_PLAN_LOCATION_NOT_SAME_LEVEL                                     ErrorCode = 8002
	ErrorCode_ERROR_CODE_PROXY_MANAGER_LOCATION_IS_NOT_AVAILABLE_IN_THIS_PLAN                           ErrorCode = 8003
	ErrorCode_ERROR_CODE_PROXY_MANAGER_CANNOT_ACTIVE_PLAN_WHEN_DEFAULT_PROFILE_OF_PLAN_NOT_EXIST        ErrorCode = 8004
	ErrorCode_ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_SUBSCRIPTION_NOT_EXPIRED                            ErrorCode = 8005
	ErrorCode_ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_SUBSCRIPTION_EXPIRED                                ErrorCode = 8006
	ErrorCode_ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_SUBSCRIPTION_NOT_EXIST                              ErrorCode = 8007
	ErrorCode_ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_PROXY_TOKEN_USED                                    ErrorCode = 8008
	ErrorCode_ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_PROXY_TOKEN_NOT_EXIST                               ErrorCode = 8009
	ErrorCode_ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_PROXY_NOT_FOUND                                     ErrorCode = 8010
	ErrorCode_ERROR_CODE_PROXY_MANAGER_PLAN_MUST_HAVE_AT_LEAST_ONE_LOCATION                             ErrorCode = 8011
	ErrorCode_ERROR_CODE_PROXY_MANAGER_PLAN_MUST_HAVE_AT_LEAST_ONE_BACK_CONNECT                         ErrorCode = 8012
	ErrorCode_ERROR_CODE_BILLING_MERCHANT_EXIST                                                         ErrorCode = 7101
	ErrorCode_ERROR_CODE_BILLING_MERCHANT_NOT_EXIST                                                     ErrorCode = 7102
	ErrorCode_ERROR_CODE_BILLING_MERCHANT_CANNOT_UPDATE_CURRENCY                                        ErrorCode = 7103
	ErrorCode_ERROR_CODE_BILLING_PAYMENT_GATEWAY_TYPE_NOT_EXIST                                         ErrorCode = 7201
	ErrorCode_ERROR_CODE_BILLING_PAYMENT_GATEWAY_TYPE_EXIST                                             ErrorCode = 7202
	ErrorCode_ERROR_CODE_BILLING_PAYMENT_GATEWAY_EXIST                                                  ErrorCode = 7203
	ErrorCode_ERROR_CODE_BILLING_PAYMENT_GATEWAY_NOT_EXIST                                              ErrorCode = 7204
	ErrorCode_ERROR_CODE_BILLING_PAYMENT_GATEWAY_DO_NOT_SUPPORT_CURRENCY_OF_MERCHANT                    ErrorCode = 7205
	ErrorCode_ERROR_CODE_BILLING_PAYMENT_GATEWAY_AMOUNT_TOP_UP_EXIST                                    ErrorCode = 7206
	ErrorCode_ERROR_CODE_BILLING_PAYMENT_GATEWAY_AMOUNT_TOP_UP_NOT_EXIST                                ErrorCode = 7207
	ErrorCode_ERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_DO_NOT_ENABLED_ON_THIS_MERCHANT   ErrorCode = 7208
	ErrorCode_ERROR_CODE_BILLING_PAYMENT_GATEWAY_APPOTA_PAYMENT_GATEWAY_DO_NOT_ENABLED_ON_THIS_MERCHANT ErrorCode = 7209
	ErrorCode_ERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_CONFIG_INCORRECT                  ErrorCode = 7210
	ErrorCode_ERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_PRODUCT_ID_NOT_EXIST              ErrorCode = 7211
	ErrorCode_ERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_MUST_HAVE_ONE_PRODUCT             ErrorCode = 7212
	ErrorCode_ERROR_CODE_BILLING_PAYMENT_GATEWAY_DO_NOT_SUPPORT_COUNTRY                                 ErrorCode = 7213
	ErrorCode_ERROR_CODE_BILLING_PAYMENT_GATEWAY_SEPAY_PAYMENT_GATEWAY_API_KEY_EXIST                    ErrorCode = 7214
	ErrorCode_ERROR_CODE_BILLING_PAYMENT_GATEWAY_SEPAY_PAYMENT_GATEWAY_ACCOUNT_NUMBER_EXIST             ErrorCode = 7215
	ErrorCode_ERROR_CODE_BACK_CONNECT_BACK_CONNECT_MANAGER_NOT_EXIST                                    ErrorCode = 7301
	ErrorCode_ERROR_CODE_BACK_CONNECT_BACK_CONNECT_MANAGER_EXIST                                        ErrorCode = 7302
	ErrorCode_ERROR_CODE_BACK_CONNECT_BACK_CONNECT_PORT_NOT_EXIST                                       ErrorCode = 7303
	ErrorCode_ERROR_CODE_BACK_CONNECT_BACK_CONNECT_PORT_EXIST                                           ErrorCode = 7304
	ErrorCode_ERROR_CODE_BACK_CONNECT_NOT_AVAILABLE                                                     ErrorCode = 7305
	ErrorCode_ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_NOT_EXIST                                          ErrorCode = 7401
	ErrorCode_ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_IS_DEACTIVATE                                      ErrorCode = 7402
	ErrorCode_ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_DEACTIVATE_DEFAULT_PROFILE                  ErrorCode = 7403
	ErrorCode_ERROR_CODE_PROXY_PROFILE_PROXY_CANNOT_SET_PRIVATE_PROFILE_TO_ANOTHER_PROXY_TOKEN          ErrorCode = 7404
	ErrorCode_ERROR_CODE_PROXY_PROFILE_PROXY_DNS_NOT_SAME_IP_TYPE                                       ErrorCode = 7405
	ErrorCode_ERROR_CODE_PROXY_PROFILE_PROXY_NOT_HAVE_LOCATION                                          ErrorCode = 7406
	ErrorCode_ERROR_CODE_PROXY_PROFILE_PROXY_DEFAULT_PROFILE_MUST_HAVE_AT_LEAST_ONE_LOCATION            ErrorCode = 7407
	ErrorCode_ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_DELETE_DEFAULT_PROFILE_OF_PROXY_TOKEN       ErrorCode = 7408
	ErrorCode_ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_MODIFY_PROFILE_OF_ANOTHER_USER              ErrorCode = 7409
	ErrorCode_ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_RESET_DEFAULT_PROFILE                       ErrorCode = 7410
	ErrorCode_ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_LOCATION_NOT_SAME_LEVEL                            ErrorCode = 7411
	ErrorCode_ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_LOCATION_STATE_MUST_BE_SAME_COUNTRY                ErrorCode = 7412
	ErrorCode_ERROR_CODE_PROXY_TOKEN_MIN_USED_NOT_REACHED                                               ErrorCode = 7501
	ErrorCode_ERROR_CODE_PROXY_TOKEN_PROXY_OUT_OF_STOCK                                                 ErrorCode = 7502
	ErrorCode_ERROR_CODE_PROXY_POOL_NOT_EXIST                                                           ErrorCode = 7600
	ErrorCode_ERROR_CODE_MISC_PAYMENT_ADDRESS_ADDRESS_INVALID                                           ErrorCode = 9001
)

// Enum value maps for ErrorCode.
var (
	ErrorCode_name = map[int32]string{
		0:     "ERROR_CODE_UNSPECIFIED",
		9999:  "ERROR_CODE_PUBLIC_URL",
		10000: "ERROR_CODE_CROSS_SERVICE_DATA_MISMATCH",
		10001: "ERROR_CODE_CROSS_SERVICE_TELCO_ERROR",
		10002: "ERROR_CODE_CROSS_SERVICE_LOCATION_ERROR",
		1:     "ERROR_CODE_SUCCESS",
		2:     "ERROR_CODE_INTERNAL_SERVER_ERROR",
		3:     "ERROR_CODE_INVALID_REQUEST",
		4:     "ERROR_CODE_UNAUTHORIZED",
		5:     "ERROR_CODE_ABORTED",
		6:     "ERROR_CODE_USERNAME_OR_PASSWORD_DOES_NOT_MATCH",
		7:     "ERROR_CODE_USER_HAS_BEEN_BANNED",
		8:     "ERROR_CODE_EMAIL_EXIST",
		9:     "ERROR_CODE_EMAIL_NOT_EXIST",
		10:    "ERROR_CODE_OLD_PASSWORD_INCORRECT",
		1000:  "ERROR_CODE_AUTH_APP_EXIST",
		1001:  "ERROR_CODE_AUTH_APP_NOT_EXIST",
		1002:  "ERROR_CODE_AUTH_USER_NOT_EXIST",
		1003:  "ERROR_CODE_AUTH_USER_EXIST",
		1004:  "ERROR_CODE_AUTH_ROLE_EXIST",
		1005:  "ERROR_CODE_AUTH_ROLE_NOT_EXIST",
		1006:  "ERROR_CODE_AUTH_SERVICE_EXIST",
		1007:  "ERROR_CODE_AUTH_SERVICE_NOT_EXIST",
		1008:  "ERROR_CODE_AUTH_PATH_EXIST",
		1009:  "ERROR_CODE_AUTH_PATH_NOT_EXIST",
		1010:  "ERROR_CODE_AUTH_POLICY_EXIST",
		1011:  "ERROR_CODE_AUTH_POLICY_NOT_EXIST",
		1012:  "ERROR_CODE_AUTH_PERMISSION_DENY",
		1013:  "ERROR_CODE_AUTH_ROLE_PRIORITY_MUST_BE_HIGHER",
		1014:  "ERROR_CODE_AUTH_OLD_PASSWORD_INCORRECT",
		1016:  "ERROR_CODE_AUTH_TOTP_VERIFY_INCORRECT",
		1017:  "ERROR_CODE_AUTH_TOTP_VERIFY_CORRECT",
		1018:  "ERROR_CODE_AUTH_CONFIG_NOT_EXIST",
		1019:  "ERROR_CODE_AUTH_CONFIG_EXIST",
		1020:  "ERROR_CODE_AUTH_REFRESH_TOKEN_EXPRIRY",
		1021:  "ERROR_CODE_AUTH_OTP_REQUIRED",
		1022:  "ERROR_CODE_AUTH_TOTP_REQUIRED",
		1023:  "ERROR_CODE_AUTH_OTP_INCORRECT",
		1024:  "ERROR_CODE_AUTH_REF_CODE_INCORRECT",
		1025:  "ERROR_CODE_AUTH_REF_EXIST",
		1226:  "ERROR_CODE_AUTH_OTP_VERIFY_INCORRECT",
		1227:  "ERROR_CODE_AUTH_TOKEN_VERIFY_INCORRECT",
		1228:  "ERROR_CODE_AUTH_USER_INACTIVE",
		2000:  "ERROR_CODE_DNS_DNS_EXIST",
		2001:  "ERROR_CODE_DNS_DNS_NOT_EXIST",
		2002:  "ERROR_CODE_DNS_DNS1_AND_DNS2_NOT_SAME_IP_TYPE",
		3000:  "ERROR_CODE_TELCO_TELCO_EXIST",
		3001:  "ERROR_CODE_TELCO_TELCO_NOT_EXIST",
		4000:  "ERROR_CODE_LOCATION_LOCATION_EXIST",
		4001:  "ERROR_CODE_LOCATION_LOCATION_NOT_EXIST",
		5001:  "ERROR_CODE_RES_RES_ACCOUNT_EXIST",
		5002:  "ERROR_CODE_RES_RES_ACCOUNT_NOT_EXIST",
		5003:  "ERROR_CODE_RES_RES_ACCOUNT_HAS_BINDING_ANOTHER_PORT",
		5004:  "ERROR_CODE_RES_RES_NODE_EXIST",
		5005:  "ERROR_CODE_RES_RES_NODE_NOT_EXIST",
		5006:  "ERROR_CODE_RES_RES_NODE_AND_RES_ACCOUNT_NOT_SAME_LOCATION",
		5007:  "ERROR_CODE_RES_RES_PORT_EXIST",
		5008:  "ERROR_CODE_RES_RES_PORT_NOT_EXIST",
		5009:  "ERROR_CODE_RES_RES_PORT_TOTAL_RES_PORT_MUST_BE_GREATER_THAN_CURRENT",
		5010:  "ERROR_CODE_RES_RES_PORT_MUST_HAVE_ACCOUNT_BEFORE_ACTIVE",
		5011:  "ERROR_CODE_RES_RES_DEVICE_NOT_EXIST",
		6000:  "ERROR_CODE_MERCHANT_NOT_EXIST",
		6001:  "ERROR_CODE_MERCHANT_USER_NOT_EXIST",
		6002:  "ERROR_CODE_MERCHANT_PRODUCT_BASE_EXIST",
		6003:  "ERROR_CODE_MERCHANT_PRODUCT_BASE_NOT_EXIST",
		6004:  "ERROR_CODE_MERCHANT_PRODUCT_EXIST",
		6005:  "ERROR_CODE_MERCHANT_PRODUCT_NOT_EXIST",
		6006:  "ERROR_CODE_MERCHANT_API_NOT_EXIST",
		6007:  "ERROR_CODE_MERCHANT_INSUFFICIENT_USER_BALANCE",
		6008:  "ERROR_CODE_MERCHANT_PRODUCT_PRICE_INVALID",
		6009:  "ERROR_CODE_MERCHANT_UPDATE_BALANCE_MISSING_NOTE",
		6010:  "ERROR_CODE_MERCHANT_UPDATE_BALANCE_TRANSACTION_TYPE_INVALID",
		6011:  "ERROR_CODE_MERCHANT_UPDATE_COMPANY_NOT_EXIST",
		6012:  "ERROR_CODE_MERCHANT_UPDATE_COMPANY_MEST_EXIST",
		6013:  "ERROR_CODE_MERCHANT_UPDATE_CONFIG_MAIL_NOT_EXIST",
		7002:  "ERROR_CODE_BILLING_PLAN_MUST_HAVE_PRICE_FOR_ACTIVE_STATE",
		7008:  "ERROR_CODE_BILLING_PLAN_PRICE_FOR_BILLING_CYCLE_EXIST",
		7009:  "ERROR_CODE_BILLING_PLAN_PRICE_NOT_EXIST",
		7010:  "ERROR_CODE_BILLING_ORDER_INSUFFICIENT_FUNDS",
		8000:  "ERROR_CODE_BILLING_PLAN_EXIST",
		8001:  "ERROR_CODE_BILLING_PLAN_NOT_EXIST",
		8002:  "ERROR_CODE_PROXY_MANAGER_PLAN_LOCATION_NOT_SAME_LEVEL",
		8003:  "ERROR_CODE_PROXY_MANAGER_LOCATION_IS_NOT_AVAILABLE_IN_THIS_PLAN",
		8004:  "ERROR_CODE_PROXY_MANAGER_CANNOT_ACTIVE_PLAN_WHEN_DEFAULT_PROFILE_OF_PLAN_NOT_EXIST",
		8005:  "ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_SUBSCRIPTION_NOT_EXPIRED",
		8006:  "ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_SUBSCRIPTION_EXPIRED",
		8007:  "ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_SUBSCRIPTION_NOT_EXIST",
		8008:  "ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_PROXY_TOKEN_USED",
		8009:  "ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_PROXY_TOKEN_NOT_EXIST",
		8010:  "ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_PROXY_NOT_FOUND",
		8011:  "ERROR_CODE_PROXY_MANAGER_PLAN_MUST_HAVE_AT_LEAST_ONE_LOCATION",
		8012:  "ERROR_CODE_PROXY_MANAGER_PLAN_MUST_HAVE_AT_LEAST_ONE_BACK_CONNECT",
		7101:  "ERROR_CODE_BILLING_MERCHANT_EXIST",
		7102:  "ERROR_CODE_BILLING_MERCHANT_NOT_EXIST",
		7103:  "ERROR_CODE_BILLING_MERCHANT_CANNOT_UPDATE_CURRENCY",
		7201:  "ERROR_CODE_BILLING_PAYMENT_GATEWAY_TYPE_NOT_EXIST",
		7202:  "ERROR_CODE_BILLING_PAYMENT_GATEWAY_TYPE_EXIST",
		7203:  "ERROR_CODE_BILLING_PAYMENT_GATEWAY_EXIST",
		7204:  "ERROR_CODE_BILLING_PAYMENT_GATEWAY_NOT_EXIST",
		7205:  "ERROR_CODE_BILLING_PAYMENT_GATEWAY_DO_NOT_SUPPORT_CURRENCY_OF_MERCHANT",
		7206:  "ERROR_CODE_BILLING_PAYMENT_GATEWAY_AMOUNT_TOP_UP_EXIST",
		7207:  "ERROR_CODE_BILLING_PAYMENT_GATEWAY_AMOUNT_TOP_UP_NOT_EXIST",
		7208:  "ERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_DO_NOT_ENABLED_ON_THIS_MERCHANT",
		7209:  "ERROR_CODE_BILLING_PAYMENT_GATEWAY_APPOTA_PAYMENT_GATEWAY_DO_NOT_ENABLED_ON_THIS_MERCHANT",
		7210:  "ERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_CONFIG_INCORRECT",
		7211:  "ERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_PRODUCT_ID_NOT_EXIST",
		7212:  "ERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_MUST_HAVE_ONE_PRODUCT",
		7213:  "ERROR_CODE_BILLING_PAYMENT_GATEWAY_DO_NOT_SUPPORT_COUNTRY",
		7214:  "ERROR_CODE_BILLING_PAYMENT_GATEWAY_SEPAY_PAYMENT_GATEWAY_API_KEY_EXIST",
		7215:  "ERROR_CODE_BILLING_PAYMENT_GATEWAY_SEPAY_PAYMENT_GATEWAY_ACCOUNT_NUMBER_EXIST",
		7301:  "ERROR_CODE_BACK_CONNECT_BACK_CONNECT_MANAGER_NOT_EXIST",
		7302:  "ERROR_CODE_BACK_CONNECT_BACK_CONNECT_MANAGER_EXIST",
		7303:  "ERROR_CODE_BACK_CONNECT_BACK_CONNECT_PORT_NOT_EXIST",
		7304:  "ERROR_CODE_BACK_CONNECT_BACK_CONNECT_PORT_EXIST",
		7305:  "ERROR_CODE_BACK_CONNECT_NOT_AVAILABLE",
		7401:  "ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_NOT_EXIST",
		7402:  "ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_IS_DEACTIVATE",
		7403:  "ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_DEACTIVATE_DEFAULT_PROFILE",
		7404:  "ERROR_CODE_PROXY_PROFILE_PROXY_CANNOT_SET_PRIVATE_PROFILE_TO_ANOTHER_PROXY_TOKEN",
		7405:  "ERROR_CODE_PROXY_PROFILE_PROXY_DNS_NOT_SAME_IP_TYPE",
		7406:  "ERROR_CODE_PROXY_PROFILE_PROXY_NOT_HAVE_LOCATION",
		7407:  "ERROR_CODE_PROXY_PROFILE_PROXY_DEFAULT_PROFILE_MUST_HAVE_AT_LEAST_ONE_LOCATION",
		7408:  "ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_DELETE_DEFAULT_PROFILE_OF_PROXY_TOKEN",
		7409:  "ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_MODIFY_PROFILE_OF_ANOTHER_USER",
		7410:  "ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_RESET_DEFAULT_PROFILE",
		7411:  "ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_LOCATION_NOT_SAME_LEVEL",
		7412:  "ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_LOCATION_STATE_MUST_BE_SAME_COUNTRY",
		7501:  "ERROR_CODE_PROXY_TOKEN_MIN_USED_NOT_REACHED",
		7502:  "ERROR_CODE_PROXY_TOKEN_PROXY_OUT_OF_STOCK",
		7600:  "ERROR_CODE_PROXY_POOL_NOT_EXIST",
		9001:  "ERROR_CODE_MISC_PAYMENT_ADDRESS_ADDRESS_INVALID",
	}
	ErrorCode_value = map[string]int32{
		"ERROR_CODE_UNSPECIFIED":                                                                    0,
		"ERROR_CODE_PUBLIC_URL":                                                                     9999,
		"ERROR_CODE_CROSS_SERVICE_DATA_MISMATCH":                                                    10000,
		"ERROR_CODE_CROSS_SERVICE_TELCO_ERROR":                                                      10001,
		"ERROR_CODE_CROSS_SERVICE_LOCATION_ERROR":                                                   10002,
		"ERROR_CODE_SUCCESS":                                                                        1,
		"ERROR_CODE_INTERNAL_SERVER_ERROR":                                                          2,
		"ERROR_CODE_INVALID_REQUEST":                                                                3,
		"ERROR_CODE_UNAUTHORIZED":                                                                   4,
		"ERROR_CODE_ABORTED":                                                                        5,
		"ERROR_CODE_USERNAME_OR_PASSWORD_DOES_NOT_MATCH":                                            6,
		"ERROR_CODE_USER_HAS_BEEN_BANNED":                                                           7,
		"ERROR_CODE_EMAIL_EXIST":                                                                    8,
		"ERROR_CODE_EMAIL_NOT_EXIST":                                                                9,
		"ERROR_CODE_OLD_PASSWORD_INCORRECT":                                                         10,
		"ERROR_CODE_AUTH_APP_EXIST":                                                                 1000,
		"ERROR_CODE_AUTH_APP_NOT_EXIST":                                                             1001,
		"ERROR_CODE_AUTH_USER_NOT_EXIST":                                                            1002,
		"ERROR_CODE_AUTH_USER_EXIST":                                                                1003,
		"ERROR_CODE_AUTH_ROLE_EXIST":                                                                1004,
		"ERROR_CODE_AUTH_ROLE_NOT_EXIST":                                                            1005,
		"ERROR_CODE_AUTH_SERVICE_EXIST":                                                             1006,
		"ERROR_CODE_AUTH_SERVICE_NOT_EXIST":                                                         1007,
		"ERROR_CODE_AUTH_PATH_EXIST":                                                                1008,
		"ERROR_CODE_AUTH_PATH_NOT_EXIST":                                                            1009,
		"ERROR_CODE_AUTH_POLICY_EXIST":                                                              1010,
		"ERROR_CODE_AUTH_POLICY_NOT_EXIST":                                                          1011,
		"ERROR_CODE_AUTH_PERMISSION_DENY":                                                           1012,
		"ERROR_CODE_AUTH_ROLE_PRIORITY_MUST_BE_HIGHER":                                              1013,
		"ERROR_CODE_AUTH_OLD_PASSWORD_INCORRECT":                                                    1014,
		"ERROR_CODE_AUTH_TOTP_VERIFY_INCORRECT":                                                     1016,
		"ERROR_CODE_AUTH_TOTP_VERIFY_CORRECT":                                                       1017,
		"ERROR_CODE_AUTH_CONFIG_NOT_EXIST":                                                          1018,
		"ERROR_CODE_AUTH_CONFIG_EXIST":                                                              1019,
		"ERROR_CODE_AUTH_REFRESH_TOKEN_EXPRIRY":                                                     1020,
		"ERROR_CODE_AUTH_OTP_REQUIRED":                                                              1021,
		"ERROR_CODE_AUTH_TOTP_REQUIRED":                                                             1022,
		"ERROR_CODE_AUTH_OTP_INCORRECT":                                                             1023,
		"ERROR_CODE_AUTH_REF_CODE_INCORRECT":                                                        1024,
		"ERROR_CODE_AUTH_REF_EXIST":                                                                 1025,
		"ERROR_CODE_AUTH_OTP_VERIFY_INCORRECT":                                                      1226,
		"ERROR_CODE_AUTH_TOKEN_VERIFY_INCORRECT":                                                    1227,
		"ERROR_CODE_AUTH_USER_INACTIVE":                                                             1228,
		"ERROR_CODE_DNS_DNS_EXIST":                                                                  2000,
		"ERROR_CODE_DNS_DNS_NOT_EXIST":                                                              2001,
		"ERROR_CODE_DNS_DNS1_AND_DNS2_NOT_SAME_IP_TYPE":                                             2002,
		"ERROR_CODE_TELCO_TELCO_EXIST":                                                              3000,
		"ERROR_CODE_TELCO_TELCO_NOT_EXIST":                                                          3001,
		"ERROR_CODE_LOCATION_LOCATION_EXIST":                                                        4000,
		"ERROR_CODE_LOCATION_LOCATION_NOT_EXIST":                                                    4001,
		"ERROR_CODE_RES_RES_ACCOUNT_EXIST":                                                          5001,
		"ERROR_CODE_RES_RES_ACCOUNT_NOT_EXIST":                                                      5002,
		"ERROR_CODE_RES_RES_ACCOUNT_HAS_BINDING_ANOTHER_PORT":                                       5003,
		"ERROR_CODE_RES_RES_NODE_EXIST":                                                             5004,
		"ERROR_CODE_RES_RES_NODE_NOT_EXIST":                                                         5005,
		"ERROR_CODE_RES_RES_NODE_AND_RES_ACCOUNT_NOT_SAME_LOCATION":                                 5006,
		"ERROR_CODE_RES_RES_PORT_EXIST":                                                             5007,
		"ERROR_CODE_RES_RES_PORT_NOT_EXIST":                                                         5008,
		"ERROR_CODE_RES_RES_PORT_TOTAL_RES_PORT_MUST_BE_GREATER_THAN_CURRENT":                       5009,
		"ERROR_CODE_RES_RES_PORT_MUST_HAVE_ACCOUNT_BEFORE_ACTIVE":                                   5010,
		"ERROR_CODE_RES_RES_DEVICE_NOT_EXIST":                                                       5011,
		"ERROR_CODE_MERCHANT_NOT_EXIST":                                                             6000,
		"ERROR_CODE_MERCHANT_USER_NOT_EXIST":                                                        6001,
		"ERROR_CODE_MERCHANT_PRODUCT_BASE_EXIST":                                                    6002,
		"ERROR_CODE_MERCHANT_PRODUCT_BASE_NOT_EXIST":                                                6003,
		"ERROR_CODE_MERCHANT_PRODUCT_EXIST":                                                         6004,
		"ERROR_CODE_MERCHANT_PRODUCT_NOT_EXIST":                                                     6005,
		"ERROR_CODE_MERCHANT_API_NOT_EXIST":                                                         6006,
		"ERROR_CODE_MERCHANT_INSUFFICIENT_USER_BALANCE":                                             6007,
		"ERROR_CODE_MERCHANT_PRODUCT_PRICE_INVALID":                                                 6008,
		"ERROR_CODE_MERCHANT_UPDATE_BALANCE_MISSING_NOTE":                                           6009,
		"ERROR_CODE_MERCHANT_UPDATE_BALANCE_TRANSACTION_TYPE_INVALID":                               6010,
		"ERROR_CODE_MERCHANT_UPDATE_COMPANY_NOT_EXIST":                                              6011,
		"ERROR_CODE_MERCHANT_UPDATE_COMPANY_MEST_EXIST":                                             6012,
		"ERROR_CODE_MERCHANT_UPDATE_CONFIG_MAIL_NOT_EXIST":                                          6013,
		"ERROR_CODE_BILLING_PLAN_MUST_HAVE_PRICE_FOR_ACTIVE_STATE":                                  7002,
		"ERROR_CODE_BILLING_PLAN_PRICE_FOR_BILLING_CYCLE_EXIST":                                     7008,
		"ERROR_CODE_BILLING_PLAN_PRICE_NOT_EXIST":                                                   7009,
		"ERROR_CODE_BILLING_ORDER_INSUFFICIENT_FUNDS":                                               7010,
		"ERROR_CODE_BILLING_PLAN_EXIST":                                                             8000,
		"ERROR_CODE_BILLING_PLAN_NOT_EXIST":                                                         8001,
		"ERROR_CODE_PROXY_MANAGER_PLAN_LOCATION_NOT_SAME_LEVEL":                                     8002,
		"ERROR_CODE_PROXY_MANAGER_LOCATION_IS_NOT_AVAILABLE_IN_THIS_PLAN":                           8003,
		"ERROR_CODE_PROXY_MANAGER_CANNOT_ACTIVE_PLAN_WHEN_DEFAULT_PROFILE_OF_PLAN_NOT_EXIST":        8004,
		"ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_SUBSCRIPTION_NOT_EXPIRED":                            8005,
		"ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_SUBSCRIPTION_EXPIRED":                                8006,
		"ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_SUBSCRIPTION_NOT_EXIST":                              8007,
		"ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_PROXY_TOKEN_USED":                                    8008,
		"ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_PROXY_TOKEN_NOT_EXIST":                               8009,
		"ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_PROXY_NOT_FOUND":                                     8010,
		"ERROR_CODE_PROXY_MANAGER_PLAN_MUST_HAVE_AT_LEAST_ONE_LOCATION":                             8011,
		"ERROR_CODE_PROXY_MANAGER_PLAN_MUST_HAVE_AT_LEAST_ONE_BACK_CONNECT":                         8012,
		"ERROR_CODE_BILLING_MERCHANT_EXIST":                                                         7101,
		"ERROR_CODE_BILLING_MERCHANT_NOT_EXIST":                                                     7102,
		"ERROR_CODE_BILLING_MERCHANT_CANNOT_UPDATE_CURRENCY":                                        7103,
		"ERROR_CODE_BILLING_PAYMENT_GATEWAY_TYPE_NOT_EXIST":                                         7201,
		"ERROR_CODE_BILLING_PAYMENT_GATEWAY_TYPE_EXIST":                                             7202,
		"ERROR_CODE_BILLING_PAYMENT_GATEWAY_EXIST":                                                  7203,
		"ERROR_CODE_BILLING_PAYMENT_GATEWAY_NOT_EXIST":                                              7204,
		"ERROR_CODE_BILLING_PAYMENT_GATEWAY_DO_NOT_SUPPORT_CURRENCY_OF_MERCHANT":                    7205,
		"ERROR_CODE_BILLING_PAYMENT_GATEWAY_AMOUNT_TOP_UP_EXIST":                                    7206,
		"ERROR_CODE_BILLING_PAYMENT_GATEWAY_AMOUNT_TOP_UP_NOT_EXIST":                                7207,
		"ERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_DO_NOT_ENABLED_ON_THIS_MERCHANT":   7208,
		"ERROR_CODE_BILLING_PAYMENT_GATEWAY_APPOTA_PAYMENT_GATEWAY_DO_NOT_ENABLED_ON_THIS_MERCHANT": 7209,
		"ERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_CONFIG_INCORRECT":                  7210,
		"ERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_PRODUCT_ID_NOT_EXIST":              7211,
		"ERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_MUST_HAVE_ONE_PRODUCT":             7212,
		"ERROR_CODE_BILLING_PAYMENT_GATEWAY_DO_NOT_SUPPORT_COUNTRY":                                 7213,
		"ERROR_CODE_BILLING_PAYMENT_GATEWAY_SEPAY_PAYMENT_GATEWAY_API_KEY_EXIST":                    7214,
		"ERROR_CODE_BILLING_PAYMENT_GATEWAY_SEPAY_PAYMENT_GATEWAY_ACCOUNT_NUMBER_EXIST":             7215,
		"ERROR_CODE_BACK_CONNECT_BACK_CONNECT_MANAGER_NOT_EXIST":                                    7301,
		"ERROR_CODE_BACK_CONNECT_BACK_CONNECT_MANAGER_EXIST":                                        7302,
		"ERROR_CODE_BACK_CONNECT_BACK_CONNECT_PORT_NOT_EXIST":                                       7303,
		"ERROR_CODE_BACK_CONNECT_BACK_CONNECT_PORT_EXIST":                                           7304,
		"ERROR_CODE_BACK_CONNECT_NOT_AVAILABLE":                                                     7305,
		"ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_NOT_EXIST":                                          7401,
		"ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_IS_DEACTIVATE":                                      7402,
		"ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_DEACTIVATE_DEFAULT_PROFILE":                  7403,
		"ERROR_CODE_PROXY_PROFILE_PROXY_CANNOT_SET_PRIVATE_PROFILE_TO_ANOTHER_PROXY_TOKEN":          7404,
		"ERROR_CODE_PROXY_PROFILE_PROXY_DNS_NOT_SAME_IP_TYPE":                                       7405,
		"ERROR_CODE_PROXY_PROFILE_PROXY_NOT_HAVE_LOCATION":                                          7406,
		"ERROR_CODE_PROXY_PROFILE_PROXY_DEFAULT_PROFILE_MUST_HAVE_AT_LEAST_ONE_LOCATION":            7407,
		"ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_DELETE_DEFAULT_PROFILE_OF_PROXY_TOKEN":       7408,
		"ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_MODIFY_PROFILE_OF_ANOTHER_USER":              7409,
		"ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_RESET_DEFAULT_PROFILE":                       7410,
		"ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_LOCATION_NOT_SAME_LEVEL":                            7411,
		"ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_LOCATION_STATE_MUST_BE_SAME_COUNTRY":                7412,
		"ERROR_CODE_PROXY_TOKEN_MIN_USED_NOT_REACHED":                                               7501,
		"ERROR_CODE_PROXY_TOKEN_PROXY_OUT_OF_STOCK":                                                 7502,
		"ERROR_CODE_PROXY_POOL_NOT_EXIST":                                                           7600,
		"ERROR_CODE_MISC_PAYMENT_ADDRESS_ADDRESS_INVALID":                                           9001,
	}
)

func (x ErrorCode) Enum() *ErrorCode {
	p := new(ErrorCode)
	*p = x
	return p
}

func (x ErrorCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorCode) Descriptor() protoreflect.EnumDescriptor {
	return file_errmsg_v1_errormsg_proto_enumTypes[0].Descriptor()
}

func (ErrorCode) Type() protoreflect.EnumType {
	return &file_errmsg_v1_errormsg_proto_enumTypes[0]
}

func (x ErrorCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorCode.Descriptor instead.
func (ErrorCode) EnumDescriptor() ([]byte, []int) {
	return file_errmsg_v1_errormsg_proto_rawDescGZIP(), []int{0}
}

type ErrorMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          ErrorCode              `protobuf:"varint,1,opt,name=code,proto3,enum=errmsg.v1.ErrorCode" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Extras        map[string]*anypb.Any  `protobuf:"bytes,3,rep,name=extras,proto3" json:"extras,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ErrorMessage) Reset() {
	*x = ErrorMessage{}
	mi := &file_errmsg_v1_errormsg_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorMessage) ProtoMessage() {}

func (x *ErrorMessage) ProtoReflect() protoreflect.Message {
	mi := &file_errmsg_v1_errormsg_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorMessage.ProtoReflect.Descriptor instead.
func (*ErrorMessage) Descriptor() ([]byte, []int) {
	return file_errmsg_v1_errormsg_proto_rawDescGZIP(), []int{0}
}

func (x *ErrorMessage) GetCode() ErrorCode {
	if x != nil {
		return x.Code
	}
	return ErrorCode_ERROR_CODE_UNSPECIFIED
}

func (x *ErrorMessage) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ErrorMessage) GetExtras() map[string]*anypb.Any {
	if x != nil {
		return x.Extras
	}
	return nil
}

var File_errmsg_v1_errormsg_proto protoreflect.FileDescriptor

const file_errmsg_v1_errormsg_proto_rawDesc = "" +
	"\n" +
	"\x18errmsg/v1/errormsg.proto\x12\terrmsg.v1\x1a\x19google/protobuf/any.proto\"\xe0\x01\n" +
	"\fErrorMessage\x12(\n" +
	"\x04code\x18\x01 \x01(\x0e2\x14.errmsg.v1.ErrorCodeR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12;\n" +
	"\x06extras\x18\x03 \x03(\v2#.errmsg.v1.ErrorMessage.ExtrasEntryR\x06extras\x1aO\n" +
	"\vExtrasEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12*\n" +
	"\x05value\x18\x02 \x01(\v2\x14.google.protobuf.AnyR\x05value:\x028\x01*\x9b4\n" +
	"\tErrorCode\x12\x1a\n" +
	"\x16ERROR_CODE_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x15ERROR_CODE_PUBLIC_URL\x10\x8fN\x12+\n" +
	"&ERROR_CODE_CROSS_SERVICE_DATA_MISMATCH\x10\x90N\x12)\n" +
	"$ERROR_CODE_CROSS_SERVICE_TELCO_ERROR\x10\x91N\x12,\n" +
	"'ERROR_CODE_CROSS_SERVICE_LOCATION_ERROR\x10\x92N\x12\x16\n" +
	"\x12ERROR_CODE_SUCCESS\x10\x01\x12$\n" +
	" ERROR_CODE_INTERNAL_SERVER_ERROR\x10\x02\x12\x1e\n" +
	"\x1aERROR_CODE_INVALID_REQUEST\x10\x03\x12\x1b\n" +
	"\x17ERROR_CODE_UNAUTHORIZED\x10\x04\x12\x16\n" +
	"\x12ERROR_CODE_ABORTED\x10\x05\x122\n" +
	".ERROR_CODE_USERNAME_OR_PASSWORD_DOES_NOT_MATCH\x10\x06\x12#\n" +
	"\x1fERROR_CODE_USER_HAS_BEEN_BANNED\x10\a\x12\x1a\n" +
	"\x16ERROR_CODE_EMAIL_EXIST\x10\b\x12\x1e\n" +
	"\x1aERROR_CODE_EMAIL_NOT_EXIST\x10\t\x12%\n" +
	"!ERROR_CODE_OLD_PASSWORD_INCORRECT\x10\n" +
	"\x12\x1e\n" +
	"\x19ERROR_CODE_AUTH_APP_EXIST\x10\xe8\a\x12\"\n" +
	"\x1dERROR_CODE_AUTH_APP_NOT_EXIST\x10\xe9\a\x12#\n" +
	"\x1eERROR_CODE_AUTH_USER_NOT_EXIST\x10\xea\a\x12\x1f\n" +
	"\x1aERROR_CODE_AUTH_USER_EXIST\x10\xeb\a\x12\x1f\n" +
	"\x1aERROR_CODE_AUTH_ROLE_EXIST\x10\xec\a\x12#\n" +
	"\x1eERROR_CODE_AUTH_ROLE_NOT_EXIST\x10\xed\a\x12\"\n" +
	"\x1dERROR_CODE_AUTH_SERVICE_EXIST\x10\xee\a\x12&\n" +
	"!ERROR_CODE_AUTH_SERVICE_NOT_EXIST\x10\xef\a\x12\x1f\n" +
	"\x1aERROR_CODE_AUTH_PATH_EXIST\x10\xf0\a\x12#\n" +
	"\x1eERROR_CODE_AUTH_PATH_NOT_EXIST\x10\xf1\a\x12!\n" +
	"\x1cERROR_CODE_AUTH_POLICY_EXIST\x10\xf2\a\x12%\n" +
	" ERROR_CODE_AUTH_POLICY_NOT_EXIST\x10\xf3\a\x12$\n" +
	"\x1fERROR_CODE_AUTH_PERMISSION_DENY\x10\xf4\a\x121\n" +
	",ERROR_CODE_AUTH_ROLE_PRIORITY_MUST_BE_HIGHER\x10\xf5\a\x12+\n" +
	"&ERROR_CODE_AUTH_OLD_PASSWORD_INCORRECT\x10\xf6\a\x12*\n" +
	"%ERROR_CODE_AUTH_TOTP_VERIFY_INCORRECT\x10\xf8\a\x12(\n" +
	"#ERROR_CODE_AUTH_TOTP_VERIFY_CORRECT\x10\xf9\a\x12%\n" +
	" ERROR_CODE_AUTH_CONFIG_NOT_EXIST\x10\xfa\a\x12!\n" +
	"\x1cERROR_CODE_AUTH_CONFIG_EXIST\x10\xfb\a\x12*\n" +
	"%ERROR_CODE_AUTH_REFRESH_TOKEN_EXPRIRY\x10\xfc\a\x12!\n" +
	"\x1cERROR_CODE_AUTH_OTP_REQUIRED\x10\xfd\a\x12\"\n" +
	"\x1dERROR_CODE_AUTH_TOTP_REQUIRED\x10\xfe\a\x12\"\n" +
	"\x1dERROR_CODE_AUTH_OTP_INCORRECT\x10\xff\a\x12'\n" +
	"\"ERROR_CODE_AUTH_REF_CODE_INCORRECT\x10\x80\b\x12\x1e\n" +
	"\x19ERROR_CODE_AUTH_REF_EXIST\x10\x81\b\x12)\n" +
	"$ERROR_CODE_AUTH_OTP_VERIFY_INCORRECT\x10\xca\t\x12+\n" +
	"&ERROR_CODE_AUTH_TOKEN_VERIFY_INCORRECT\x10\xcb\t\x12\"\n" +
	"\x1dERROR_CODE_AUTH_USER_INACTIVE\x10\xcc\t\x12\x1d\n" +
	"\x18ERROR_CODE_DNS_DNS_EXIST\x10\xd0\x0f\x12!\n" +
	"\x1cERROR_CODE_DNS_DNS_NOT_EXIST\x10\xd1\x0f\x122\n" +
	"-ERROR_CODE_DNS_DNS1_AND_DNS2_NOT_SAME_IP_TYPE\x10\xd2\x0f\x12!\n" +
	"\x1cERROR_CODE_TELCO_TELCO_EXIST\x10\xb8\x17\x12%\n" +
	" ERROR_CODE_TELCO_TELCO_NOT_EXIST\x10\xb9\x17\x12'\n" +
	"\"ERROR_CODE_LOCATION_LOCATION_EXIST\x10\xa0\x1f\x12+\n" +
	"&ERROR_CODE_LOCATION_LOCATION_NOT_EXIST\x10\xa1\x1f\x12%\n" +
	" ERROR_CODE_RES_RES_ACCOUNT_EXIST\x10\x89'\x12)\n" +
	"$ERROR_CODE_RES_RES_ACCOUNT_NOT_EXIST\x10\x8a'\x128\n" +
	"3ERROR_CODE_RES_RES_ACCOUNT_HAS_BINDING_ANOTHER_PORT\x10\x8b'\x12\"\n" +
	"\x1dERROR_CODE_RES_RES_NODE_EXIST\x10\x8c'\x12&\n" +
	"!ERROR_CODE_RES_RES_NODE_NOT_EXIST\x10\x8d'\x12>\n" +
	"9ERROR_CODE_RES_RES_NODE_AND_RES_ACCOUNT_NOT_SAME_LOCATION\x10\x8e'\x12\"\n" +
	"\x1dERROR_CODE_RES_RES_PORT_EXIST\x10\x8f'\x12&\n" +
	"!ERROR_CODE_RES_RES_PORT_NOT_EXIST\x10\x90'\x12H\n" +
	"CERROR_CODE_RES_RES_PORT_TOTAL_RES_PORT_MUST_BE_GREATER_THAN_CURRENT\x10\x91'\x12<\n" +
	"7ERROR_CODE_RES_RES_PORT_MUST_HAVE_ACCOUNT_BEFORE_ACTIVE\x10\x92'\x12(\n" +
	"#ERROR_CODE_RES_RES_DEVICE_NOT_EXIST\x10\x93'\x12\"\n" +
	"\x1dERROR_CODE_MERCHANT_NOT_EXIST\x10\xf0.\x12'\n" +
	"\"ERROR_CODE_MERCHANT_USER_NOT_EXIST\x10\xf1.\x12+\n" +
	"&ERROR_CODE_MERCHANT_PRODUCT_BASE_EXIST\x10\xf2.\x12/\n" +
	"*ERROR_CODE_MERCHANT_PRODUCT_BASE_NOT_EXIST\x10\xf3.\x12&\n" +
	"!ERROR_CODE_MERCHANT_PRODUCT_EXIST\x10\xf4.\x12*\n" +
	"%ERROR_CODE_MERCHANT_PRODUCT_NOT_EXIST\x10\xf5.\x12&\n" +
	"!ERROR_CODE_MERCHANT_API_NOT_EXIST\x10\xf6.\x122\n" +
	"-ERROR_CODE_MERCHANT_INSUFFICIENT_USER_BALANCE\x10\xf7.\x12.\n" +
	")ERROR_CODE_MERCHANT_PRODUCT_PRICE_INVALID\x10\xf8.\x124\n" +
	"/ERROR_CODE_MERCHANT_UPDATE_BALANCE_MISSING_NOTE\x10\xf9.\x12@\n" +
	";ERROR_CODE_MERCHANT_UPDATE_BALANCE_TRANSACTION_TYPE_INVALID\x10\xfa.\x121\n" +
	",ERROR_CODE_MERCHANT_UPDATE_COMPANY_NOT_EXIST\x10\xfb.\x122\n" +
	"-ERROR_CODE_MERCHANT_UPDATE_COMPANY_MEST_EXIST\x10\xfc.\x125\n" +
	"0ERROR_CODE_MERCHANT_UPDATE_CONFIG_MAIL_NOT_EXIST\x10\xfd.\x12=\n" +
	"8ERROR_CODE_BILLING_PLAN_MUST_HAVE_PRICE_FOR_ACTIVE_STATE\x10\xda6\x12:\n" +
	"5ERROR_CODE_BILLING_PLAN_PRICE_FOR_BILLING_CYCLE_EXIST\x10\xe06\x12,\n" +
	"'ERROR_CODE_BILLING_PLAN_PRICE_NOT_EXIST\x10\xe16\x120\n" +
	"+ERROR_CODE_BILLING_ORDER_INSUFFICIENT_FUNDS\x10\xe26\x12\"\n" +
	"\x1dERROR_CODE_BILLING_PLAN_EXIST\x10\xc0>\x12&\n" +
	"!ERROR_CODE_BILLING_PLAN_NOT_EXIST\x10\xc1>\x12:\n" +
	"5ERROR_CODE_PROXY_MANAGER_PLAN_LOCATION_NOT_SAME_LEVEL\x10\xc2>\x12D\n" +
	"?ERROR_CODE_PROXY_MANAGER_LOCATION_IS_NOT_AVAILABLE_IN_THIS_PLAN\x10\xc3>\x12W\n" +
	"RERROR_CODE_PROXY_MANAGER_CANNOT_ACTIVE_PLAN_WHEN_DEFAULT_PROFILE_OF_PLAN_NOT_EXIST\x10\xc4>\x12C\n" +
	">ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_SUBSCRIPTION_NOT_EXPIRED\x10\xc5>\x12?\n" +
	":ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_SUBSCRIPTION_EXPIRED\x10\xc6>\x12A\n" +
	"<ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_SUBSCRIPTION_NOT_EXIST\x10\xc7>\x12;\n" +
	"6ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_PROXY_TOKEN_USED\x10\xc8>\x12@\n" +
	";ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_PROXY_TOKEN_NOT_EXIST\x10\xc9>\x12:\n" +
	"5ERROR_CODE_PROXY_MANAGER_SUBSCRIPTION_PROXY_NOT_FOUND\x10\xca>\x12B\n" +
	"=ERROR_CODE_PROXY_MANAGER_PLAN_MUST_HAVE_AT_LEAST_ONE_LOCATION\x10\xcb>\x12F\n" +
	"AERROR_CODE_PROXY_MANAGER_PLAN_MUST_HAVE_AT_LEAST_ONE_BACK_CONNECT\x10\xcc>\x12&\n" +
	"!ERROR_CODE_BILLING_MERCHANT_EXIST\x10\xbd7\x12*\n" +
	"%ERROR_CODE_BILLING_MERCHANT_NOT_EXIST\x10\xbe7\x127\n" +
	"2ERROR_CODE_BILLING_MERCHANT_CANNOT_UPDATE_CURRENCY\x10\xbf7\x126\n" +
	"1ERROR_CODE_BILLING_PAYMENT_GATEWAY_TYPE_NOT_EXIST\x10\xa18\x122\n" +
	"-ERROR_CODE_BILLING_PAYMENT_GATEWAY_TYPE_EXIST\x10\xa28\x12-\n" +
	"(ERROR_CODE_BILLING_PAYMENT_GATEWAY_EXIST\x10\xa38\x121\n" +
	",ERROR_CODE_BILLING_PAYMENT_GATEWAY_NOT_EXIST\x10\xa48\x12K\n" +
	"FERROR_CODE_BILLING_PAYMENT_GATEWAY_DO_NOT_SUPPORT_CURRENCY_OF_MERCHANT\x10\xa58\x12;\n" +
	"6ERROR_CODE_BILLING_PAYMENT_GATEWAY_AMOUNT_TOP_UP_EXIST\x10\xa68\x12?\n" +
	":ERROR_CODE_BILLING_PAYMENT_GATEWAY_AMOUNT_TOP_UP_NOT_EXIST\x10\xa78\x12\\\n" +
	"WERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_DO_NOT_ENABLED_ON_THIS_MERCHANT\x10\xa88\x12^\n" +
	"YERROR_CODE_BILLING_PAYMENT_GATEWAY_APPOTA_PAYMENT_GATEWAY_DO_NOT_ENABLED_ON_THIS_MERCHANT\x10\xa98\x12M\n" +
	"HERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_CONFIG_INCORRECT\x10\xaa8\x12Q\n" +
	"LERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_PRODUCT_ID_NOT_EXIST\x10\xab8\x12R\n" +
	"MERROR_CODE_BILLING_PAYMENT_GATEWAY_DODO_PAYMENT_GATEWAY_MUST_HAVE_ONE_PRODUCT\x10\xac8\x12>\n" +
	"9ERROR_CODE_BILLING_PAYMENT_GATEWAY_DO_NOT_SUPPORT_COUNTRY\x10\xad8\x12K\n" +
	"FERROR_CODE_BILLING_PAYMENT_GATEWAY_SEPAY_PAYMENT_GATEWAY_API_KEY_EXIST\x10\xae8\x12R\n" +
	"MERROR_CODE_BILLING_PAYMENT_GATEWAY_SEPAY_PAYMENT_GATEWAY_ACCOUNT_NUMBER_EXIST\x10\xaf8\x12;\n" +
	"6ERROR_CODE_BACK_CONNECT_BACK_CONNECT_MANAGER_NOT_EXIST\x10\x859\x127\n" +
	"2ERROR_CODE_BACK_CONNECT_BACK_CONNECT_MANAGER_EXIST\x10\x869\x128\n" +
	"3ERROR_CODE_BACK_CONNECT_BACK_CONNECT_PORT_NOT_EXIST\x10\x879\x124\n" +
	"/ERROR_CODE_BACK_CONNECT_BACK_CONNECT_PORT_EXIST\x10\x889\x12*\n" +
	"%ERROR_CODE_BACK_CONNECT_NOT_AVAILABLE\x10\x899\x125\n" +
	"0ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_NOT_EXIST\x10\xe99\x129\n" +
	"4ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_IS_DEACTIVATE\x10\xea9\x12M\n" +
	"HERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_DEACTIVATE_DEFAULT_PROFILE\x10\xeb9\x12U\n" +
	"PERROR_CODE_PROXY_PROFILE_PROXY_CANNOT_SET_PRIVATE_PROFILE_TO_ANOTHER_PROXY_TOKEN\x10\xec9\x128\n" +
	"3ERROR_CODE_PROXY_PROFILE_PROXY_DNS_NOT_SAME_IP_TYPE\x10\xed9\x125\n" +
	"0ERROR_CODE_PROXY_PROFILE_PROXY_NOT_HAVE_LOCATION\x10\xee9\x12S\n" +
	"NERROR_CODE_PROXY_PROFILE_PROXY_DEFAULT_PROFILE_MUST_HAVE_AT_LEAST_ONE_LOCATION\x10\xef9\x12X\n" +
	"SERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_DELETE_DEFAULT_PROFILE_OF_PROXY_TOKEN\x10\xf09\x12Q\n" +
	"LERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_MODIFY_PROFILE_OF_ANOTHER_USER\x10\xf19\x12H\n" +
	"CERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_CANNOT_RESET_DEFAULT_PROFILE\x10\xf29\x12C\n" +
	">ERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_LOCATION_NOT_SAME_LEVEL\x10\xf39\x12O\n" +
	"JERROR_CODE_PROXY_PROFILE_PROXY_PROFILE_LOCATION_STATE_MUST_BE_SAME_COUNTRY\x10\xf49\x120\n" +
	"+ERROR_CODE_PROXY_TOKEN_MIN_USED_NOT_REACHED\x10\xcd:\x12.\n" +
	")ERROR_CODE_PROXY_TOKEN_PROXY_OUT_OF_STOCK\x10\xce:\x12$\n" +
	"\x1fERROR_CODE_PROXY_POOL_NOT_EXIST\x10\xb0;\x124\n" +
	"/ERROR_CODE_MISC_PAYMENT_ADDRESS_ADDRESS_INVALID\x10\xa9FBCZAgit.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1;errmsgv1b\x06proto3"

var (
	file_errmsg_v1_errormsg_proto_rawDescOnce sync.Once
	file_errmsg_v1_errormsg_proto_rawDescData []byte
)

func file_errmsg_v1_errormsg_proto_rawDescGZIP() []byte {
	file_errmsg_v1_errormsg_proto_rawDescOnce.Do(func() {
		file_errmsg_v1_errormsg_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_errmsg_v1_errormsg_proto_rawDesc), len(file_errmsg_v1_errormsg_proto_rawDesc)))
	})
	return file_errmsg_v1_errormsg_proto_rawDescData
}

var file_errmsg_v1_errormsg_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_errmsg_v1_errormsg_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_errmsg_v1_errormsg_proto_goTypes = []any{
	(ErrorCode)(0),       // 0: errmsg.v1.ErrorCode
	(*ErrorMessage)(nil), // 1: errmsg.v1.ErrorMessage
	nil,                  // 2: errmsg.v1.ErrorMessage.ExtrasEntry
	(*anypb.Any)(nil),    // 3: google.protobuf.Any
}
var file_errmsg_v1_errormsg_proto_depIdxs = []int32{
	0, // 0: errmsg.v1.ErrorMessage.code:type_name -> errmsg.v1.ErrorCode
	2, // 1: errmsg.v1.ErrorMessage.extras:type_name -> errmsg.v1.ErrorMessage.ExtrasEntry
	3, // 2: errmsg.v1.ErrorMessage.ExtrasEntry.value:type_name -> google.protobuf.Any
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_errmsg_v1_errormsg_proto_init() }
func file_errmsg_v1_errormsg_proto_init() {
	if File_errmsg_v1_errormsg_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_errmsg_v1_errormsg_proto_rawDesc), len(file_errmsg_v1_errormsg_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_errmsg_v1_errormsg_proto_goTypes,
		DependencyIndexes: file_errmsg_v1_errormsg_proto_depIdxs,
		EnumInfos:         file_errmsg_v1_errormsg_proto_enumTypes,
		MessageInfos:      file_errmsg_v1_errormsg_proto_msgTypes,
	}.Build()
	File_errmsg_v1_errormsg_proto = out.File
	file_errmsg_v1_errormsg_proto_goTypes = nil
	file_errmsg_v1_errormsg_proto_depIdxs = nil
}
