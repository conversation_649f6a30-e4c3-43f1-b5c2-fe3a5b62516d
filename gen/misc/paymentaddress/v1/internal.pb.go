// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: misc/paymentaddress/v1/internal.proto

package paymentaddressv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InternalPaymentAddressServiceValidPaymentAddressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdCountry     int64                  `protobuf:"varint,1,opt,name=id_country,json=idCountry,proto3" json:"id_country,omitempty"`
	IdState       int64                  `protobuf:"varint,2,opt,name=id_state,json=idState,proto3" json:"id_state,omitempty"`
	IdCity        int64                  `protobuf:"varint,3,opt,name=id_city,json=idCity,proto3" json:"id_city,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalPaymentAddressServiceValidPaymentAddressRequest) Reset() {
	*x = InternalPaymentAddressServiceValidPaymentAddressRequest{}
	mi := &file_misc_paymentaddress_v1_internal_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalPaymentAddressServiceValidPaymentAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalPaymentAddressServiceValidPaymentAddressRequest) ProtoMessage() {}

func (x *InternalPaymentAddressServiceValidPaymentAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_misc_paymentaddress_v1_internal_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalPaymentAddressServiceValidPaymentAddressRequest.ProtoReflect.Descriptor instead.
func (*InternalPaymentAddressServiceValidPaymentAddressRequest) Descriptor() ([]byte, []int) {
	return file_misc_paymentaddress_v1_internal_proto_rawDescGZIP(), []int{0}
}

func (x *InternalPaymentAddressServiceValidPaymentAddressRequest) GetIdCountry() int64 {
	if x != nil {
		return x.IdCountry
	}
	return 0
}

func (x *InternalPaymentAddressServiceValidPaymentAddressRequest) GetIdState() int64 {
	if x != nil {
		return x.IdState
	}
	return 0
}

func (x *InternalPaymentAddressServiceValidPaymentAddressRequest) GetIdCity() int64 {
	if x != nil {
		return x.IdCity
	}
	return 0
}

type InternalPaymentAddressServiceValidPaymentAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Country       *Country               `protobuf:"bytes,2,opt,name=country,proto3" json:"country,omitempty"`
	State         *State                 `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	City          *City                  `protobuf:"bytes,4,opt,name=city,proto3" json:"city,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalPaymentAddressServiceValidPaymentAddressResponse) Reset() {
	*x = InternalPaymentAddressServiceValidPaymentAddressResponse{}
	mi := &file_misc_paymentaddress_v1_internal_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalPaymentAddressServiceValidPaymentAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalPaymentAddressServiceValidPaymentAddressResponse) ProtoMessage() {}

func (x *InternalPaymentAddressServiceValidPaymentAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_misc_paymentaddress_v1_internal_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalPaymentAddressServiceValidPaymentAddressResponse.ProtoReflect.Descriptor instead.
func (*InternalPaymentAddressServiceValidPaymentAddressResponse) Descriptor() ([]byte, []int) {
	return file_misc_paymentaddress_v1_internal_proto_rawDescGZIP(), []int{1}
}

func (x *InternalPaymentAddressServiceValidPaymentAddressResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *InternalPaymentAddressServiceValidPaymentAddressResponse) GetCountry() *Country {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *InternalPaymentAddressServiceValidPaymentAddressResponse) GetState() *State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *InternalPaymentAddressServiceValidPaymentAddressResponse) GetCity() *City {
	if x != nil {
		return x.City
	}
	return nil
}

var File_misc_paymentaddress_v1_internal_proto protoreflect.FileDescriptor

const file_misc_paymentaddress_v1_internal_proto_rawDesc = "" +
	"\n" +
	"%misc/paymentaddress/v1/internal.proto\x12\x16misc.paymentaddress.v1\x1a\x18errmsg/v1/errormsg.proto\x1a#misc/paymentaddress/v1/entity.proto\"\x8c\x01\n" +
	"7InternalPaymentAddressServiceValidPaymentAddressRequest\x12\x1d\n" +
	"\n" +
	"id_country\x18\x01 \x01(\x03R\tidCountry\x12\x19\n" +
	"\bid_state\x18\x02 \x01(\x03R\aidState\x12\x17\n" +
	"\aid_city\x18\x03 \x01(\x03R\x06idCity\"\x8b\x02\n" +
	"8InternalPaymentAddressServiceValidPaymentAddressResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x129\n" +
	"\acountry\x18\x02 \x01(\v2\x1f.misc.paymentaddress.v1.CountryR\acountry\x123\n" +
	"\x05state\x18\x03 \x01(\v2\x1d.misc.paymentaddress.v1.StateR\x05state\x120\n" +
	"\x04city\x18\x04 \x01(\v2\x1c.misc.paymentaddress.v1.CityR\x04city2\xda\x01\n" +
	"\x1dInternalPaymentAddressService\x12\xb8\x01\n" +
	"\x13ValidPaymentAddress\x12O.misc.paymentaddress.v1.InternalPaymentAddressServiceValidPaymentAddressRequest\x1aP.misc.paymentaddress.v1.InternalPaymentAddressServiceValidPaymentAddressResponseBXZVgit.tmproxy-infra.com/algo/algoproxy-proto/gen/misc/paymentaddress/v1;paymentaddressv1b\x06proto3"

var (
	file_misc_paymentaddress_v1_internal_proto_rawDescOnce sync.Once
	file_misc_paymentaddress_v1_internal_proto_rawDescData []byte
)

func file_misc_paymentaddress_v1_internal_proto_rawDescGZIP() []byte {
	file_misc_paymentaddress_v1_internal_proto_rawDescOnce.Do(func() {
		file_misc_paymentaddress_v1_internal_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_misc_paymentaddress_v1_internal_proto_rawDesc), len(file_misc_paymentaddress_v1_internal_proto_rawDesc)))
	})
	return file_misc_paymentaddress_v1_internal_proto_rawDescData
}

var file_misc_paymentaddress_v1_internal_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_misc_paymentaddress_v1_internal_proto_goTypes = []any{
	(*InternalPaymentAddressServiceValidPaymentAddressRequest)(nil),  // 0: misc.paymentaddress.v1.InternalPaymentAddressServiceValidPaymentAddressRequest
	(*InternalPaymentAddressServiceValidPaymentAddressResponse)(nil), // 1: misc.paymentaddress.v1.InternalPaymentAddressServiceValidPaymentAddressResponse
	(*v1.ErrorMessage)(nil), // 2: errmsg.v1.ErrorMessage
	(*Country)(nil),         // 3: misc.paymentaddress.v1.Country
	(*State)(nil),           // 4: misc.paymentaddress.v1.State
	(*City)(nil),            // 5: misc.paymentaddress.v1.City
}
var file_misc_paymentaddress_v1_internal_proto_depIdxs = []int32{
	2, // 0: misc.paymentaddress.v1.InternalPaymentAddressServiceValidPaymentAddressResponse.error:type_name -> errmsg.v1.ErrorMessage
	3, // 1: misc.paymentaddress.v1.InternalPaymentAddressServiceValidPaymentAddressResponse.country:type_name -> misc.paymentaddress.v1.Country
	4, // 2: misc.paymentaddress.v1.InternalPaymentAddressServiceValidPaymentAddressResponse.state:type_name -> misc.paymentaddress.v1.State
	5, // 3: misc.paymentaddress.v1.InternalPaymentAddressServiceValidPaymentAddressResponse.city:type_name -> misc.paymentaddress.v1.City
	0, // 4: misc.paymentaddress.v1.InternalPaymentAddressService.ValidPaymentAddress:input_type -> misc.paymentaddress.v1.InternalPaymentAddressServiceValidPaymentAddressRequest
	1, // 5: misc.paymentaddress.v1.InternalPaymentAddressService.ValidPaymentAddress:output_type -> misc.paymentaddress.v1.InternalPaymentAddressServiceValidPaymentAddressResponse
	5, // [5:6] is the sub-list for method output_type
	4, // [4:5] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_misc_paymentaddress_v1_internal_proto_init() }
func file_misc_paymentaddress_v1_internal_proto_init() {
	if File_misc_paymentaddress_v1_internal_proto != nil {
		return
	}
	file_misc_paymentaddress_v1_entity_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_misc_paymentaddress_v1_internal_proto_rawDesc), len(file_misc_paymentaddress_v1_internal_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_misc_paymentaddress_v1_internal_proto_goTypes,
		DependencyIndexes: file_misc_paymentaddress_v1_internal_proto_depIdxs,
		MessageInfos:      file_misc_paymentaddress_v1_internal_proto_msgTypes,
	}.Build()
	File_misc_paymentaddress_v1_internal_proto = out.File
	file_misc_paymentaddress_v1_internal_proto_goTypes = nil
	file_misc_paymentaddress_v1_internal_proto_depIdxs = nil
}
