// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: misc/paymentaddress/v1/customer.proto

package paymentaddressv1

import (
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CustomerPaymentAddressServiceFetchCountriesRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	CountryNameSearch string                 `protobuf:"bytes,1,opt,name=country_name_search,json=countryNameSearch,proto3" json:"country_name_search,omitempty"`
	Pagination        *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CustomerPaymentAddressServiceFetchCountriesRequest) Reset() {
	*x = CustomerPaymentAddressServiceFetchCountriesRequest{}
	mi := &file_misc_paymentaddress_v1_customer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentAddressServiceFetchCountriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentAddressServiceFetchCountriesRequest) ProtoMessage() {}

func (x *CustomerPaymentAddressServiceFetchCountriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_misc_paymentaddress_v1_customer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentAddressServiceFetchCountriesRequest.ProtoReflect.Descriptor instead.
func (*CustomerPaymentAddressServiceFetchCountriesRequest) Descriptor() ([]byte, []int) {
	return file_misc_paymentaddress_v1_customer_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerPaymentAddressServiceFetchCountriesRequest) GetCountryNameSearch() string {
	if x != nil {
		return x.CountryNameSearch
	}
	return ""
}

func (x *CustomerPaymentAddressServiceFetchCountriesRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerPaymentAddressServiceFetchCountriesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v1.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Countries     []*Country             `protobuf:"bytes,3,rep,name=countries,proto3" json:"countries,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPaymentAddressServiceFetchCountriesResponse) Reset() {
	*x = CustomerPaymentAddressServiceFetchCountriesResponse{}
	mi := &file_misc_paymentaddress_v1_customer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentAddressServiceFetchCountriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentAddressServiceFetchCountriesResponse) ProtoMessage() {}

func (x *CustomerPaymentAddressServiceFetchCountriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_misc_paymentaddress_v1_customer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentAddressServiceFetchCountriesResponse.ProtoReflect.Descriptor instead.
func (*CustomerPaymentAddressServiceFetchCountriesResponse) Descriptor() ([]byte, []int) {
	return file_misc_paymentaddress_v1_customer_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerPaymentAddressServiceFetchCountriesResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerPaymentAddressServiceFetchCountriesResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *CustomerPaymentAddressServiceFetchCountriesResponse) GetCountries() []*Country {
	if x != nil {
		return x.Countries
	}
	return nil
}

type CustomerPaymentAddressServiceFetchStatesRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	IdCountry       int64                  `protobuf:"varint,1,opt,name=id_country,json=idCountry,proto3" json:"id_country,omitempty"`
	StateNameSearch string                 `protobuf:"bytes,2,opt,name=state_name_search,json=stateNameSearch,proto3" json:"state_name_search,omitempty"`
	Pagination      *v1.PaginationRequest  `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CustomerPaymentAddressServiceFetchStatesRequest) Reset() {
	*x = CustomerPaymentAddressServiceFetchStatesRequest{}
	mi := &file_misc_paymentaddress_v1_customer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentAddressServiceFetchStatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentAddressServiceFetchStatesRequest) ProtoMessage() {}

func (x *CustomerPaymentAddressServiceFetchStatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_misc_paymentaddress_v1_customer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentAddressServiceFetchStatesRequest.ProtoReflect.Descriptor instead.
func (*CustomerPaymentAddressServiceFetchStatesRequest) Descriptor() ([]byte, []int) {
	return file_misc_paymentaddress_v1_customer_proto_rawDescGZIP(), []int{2}
}

func (x *CustomerPaymentAddressServiceFetchStatesRequest) GetIdCountry() int64 {
	if x != nil {
		return x.IdCountry
	}
	return 0
}

func (x *CustomerPaymentAddressServiceFetchStatesRequest) GetStateNameSearch() string {
	if x != nil {
		return x.StateNameSearch
	}
	return ""
}

func (x *CustomerPaymentAddressServiceFetchStatesRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerPaymentAddressServiceFetchStatesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v1.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	States        []*State               `protobuf:"bytes,3,rep,name=states,proto3" json:"states,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPaymentAddressServiceFetchStatesResponse) Reset() {
	*x = CustomerPaymentAddressServiceFetchStatesResponse{}
	mi := &file_misc_paymentaddress_v1_customer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentAddressServiceFetchStatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentAddressServiceFetchStatesResponse) ProtoMessage() {}

func (x *CustomerPaymentAddressServiceFetchStatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_misc_paymentaddress_v1_customer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentAddressServiceFetchStatesResponse.ProtoReflect.Descriptor instead.
func (*CustomerPaymentAddressServiceFetchStatesResponse) Descriptor() ([]byte, []int) {
	return file_misc_paymentaddress_v1_customer_proto_rawDescGZIP(), []int{3}
}

func (x *CustomerPaymentAddressServiceFetchStatesResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerPaymentAddressServiceFetchStatesResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *CustomerPaymentAddressServiceFetchStatesResponse) GetStates() []*State {
	if x != nil {
		return x.States
	}
	return nil
}

type CustomerPaymentAddressServiceFetchCitiesRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdState        int64                  `protobuf:"varint,1,opt,name=id_state,json=idState,proto3" json:"id_state,omitempty"`
	CityNameSearch string                 `protobuf:"bytes,2,opt,name=city_name_search,json=cityNameSearch,proto3" json:"city_name_search,omitempty"`
	Pagination     *v1.PaginationRequest  `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerPaymentAddressServiceFetchCitiesRequest) Reset() {
	*x = CustomerPaymentAddressServiceFetchCitiesRequest{}
	mi := &file_misc_paymentaddress_v1_customer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentAddressServiceFetchCitiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentAddressServiceFetchCitiesRequest) ProtoMessage() {}

func (x *CustomerPaymentAddressServiceFetchCitiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_misc_paymentaddress_v1_customer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentAddressServiceFetchCitiesRequest.ProtoReflect.Descriptor instead.
func (*CustomerPaymentAddressServiceFetchCitiesRequest) Descriptor() ([]byte, []int) {
	return file_misc_paymentaddress_v1_customer_proto_rawDescGZIP(), []int{4}
}

func (x *CustomerPaymentAddressServiceFetchCitiesRequest) GetIdState() int64 {
	if x != nil {
		return x.IdState
	}
	return 0
}

func (x *CustomerPaymentAddressServiceFetchCitiesRequest) GetCityNameSearch() string {
	if x != nil {
		return x.CityNameSearch
	}
	return ""
}

func (x *CustomerPaymentAddressServiceFetchCitiesRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerPaymentAddressServiceFetchCitiesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v1.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Cities        []*City                `protobuf:"bytes,3,rep,name=cities,proto3" json:"cities,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPaymentAddressServiceFetchCitiesResponse) Reset() {
	*x = CustomerPaymentAddressServiceFetchCitiesResponse{}
	mi := &file_misc_paymentaddress_v1_customer_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPaymentAddressServiceFetchCitiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPaymentAddressServiceFetchCitiesResponse) ProtoMessage() {}

func (x *CustomerPaymentAddressServiceFetchCitiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_misc_paymentaddress_v1_customer_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPaymentAddressServiceFetchCitiesResponse.ProtoReflect.Descriptor instead.
func (*CustomerPaymentAddressServiceFetchCitiesResponse) Descriptor() ([]byte, []int) {
	return file_misc_paymentaddress_v1_customer_proto_rawDescGZIP(), []int{5}
}

func (x *CustomerPaymentAddressServiceFetchCitiesResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerPaymentAddressServiceFetchCitiesResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *CustomerPaymentAddressServiceFetchCitiesResponse) GetCities() []*City {
	if x != nil {
		return x.Cities
	}
	return nil
}

var File_misc_paymentaddress_v1_customer_proto protoreflect.FileDescriptor

const file_misc_paymentaddress_v1_customer_proto_rawDesc = "" +
	"\n" +
	"%misc/paymentaddress/v1/customer.proto\x12\x16misc.paymentaddress.v1\x1a\x14utils/v1/utils.proto\x1a\x18errmsg/v1/errormsg.proto\x1a#misc/paymentaddress/v1/entity.proto\"\xa1\x01\n" +
	"2CustomerPaymentAddressServiceFetchCountriesRequest\x12.\n" +
	"\x13country_name_search\x18\x01 \x01(\tR\x11countryNameSearch\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xe1\x01\n" +
	"3CustomerPaymentAddressServiceFetchCountriesResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12=\n" +
	"\tcountries\x18\x03 \x03(\v2\x1f.misc.paymentaddress.v1.CountryR\tcountries\"\xb9\x01\n" +
	"/CustomerPaymentAddressServiceFetchStatesRequest\x12\x1d\n" +
	"\n" +
	"id_country\x18\x01 \x01(\x03R\tidCountry\x12*\n" +
	"\x11state_name_search\x18\x02 \x01(\tR\x0fstateNameSearch\x12;\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xd6\x01\n" +
	"0CustomerPaymentAddressServiceFetchStatesResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x125\n" +
	"\x06states\x18\x03 \x03(\v2\x1d.misc.paymentaddress.v1.StateR\x06states\"\xb3\x01\n" +
	"/CustomerPaymentAddressServiceFetchCitiesRequest\x12\x19\n" +
	"\bid_state\x18\x01 \x01(\x03R\aidState\x12(\n" +
	"\x10city_name_search\x18\x02 \x01(\tR\x0ecityNameSearch\x12;\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xd5\x01\n" +
	"0CustomerPaymentAddressServiceFetchCitiesResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x124\n" +
	"\x06cities\x18\x03 \x03(\v2\x1c.misc.paymentaddress.v1.CityR\x06cities2\x91\x04\n" +
	"\x1dCustomerPaymentAddressService\x12\xa9\x01\n" +
	"\x0eFetchCountries\x12J.misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCountriesRequest\x1aK.misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCountriesResponse\x12\xa0\x01\n" +
	"\vFetchStates\x12G.misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchStatesRequest\x1aH.misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchStatesResponse\x12\xa0\x01\n" +
	"\vFetchCities\x12G.misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCitiesRequest\x1aH.misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCitiesResponseBXZVgit.tmproxy-infra.com/algo/algoproxy-proto/gen/misc/paymentaddress/v1;paymentaddressv1b\x06proto3"

var (
	file_misc_paymentaddress_v1_customer_proto_rawDescOnce sync.Once
	file_misc_paymentaddress_v1_customer_proto_rawDescData []byte
)

func file_misc_paymentaddress_v1_customer_proto_rawDescGZIP() []byte {
	file_misc_paymentaddress_v1_customer_proto_rawDescOnce.Do(func() {
		file_misc_paymentaddress_v1_customer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_misc_paymentaddress_v1_customer_proto_rawDesc), len(file_misc_paymentaddress_v1_customer_proto_rawDesc)))
	})
	return file_misc_paymentaddress_v1_customer_proto_rawDescData
}

var file_misc_paymentaddress_v1_customer_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_misc_paymentaddress_v1_customer_proto_goTypes = []any{
	(*CustomerPaymentAddressServiceFetchCountriesRequest)(nil),  // 0: misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCountriesRequest
	(*CustomerPaymentAddressServiceFetchCountriesResponse)(nil), // 1: misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCountriesResponse
	(*CustomerPaymentAddressServiceFetchStatesRequest)(nil),     // 2: misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchStatesRequest
	(*CustomerPaymentAddressServiceFetchStatesResponse)(nil),    // 3: misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchStatesResponse
	(*CustomerPaymentAddressServiceFetchCitiesRequest)(nil),     // 4: misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCitiesRequest
	(*CustomerPaymentAddressServiceFetchCitiesResponse)(nil),    // 5: misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCitiesResponse
	(*v1.PaginationRequest)(nil),                                // 6: utils.v1.PaginationRequest
	(*v11.ErrorMessage)(nil),                                    // 7: errmsg.v1.ErrorMessage
	(*v1.PaginationResponse)(nil),                               // 8: utils.v1.PaginationResponse
	(*Country)(nil),                                             // 9: misc.paymentaddress.v1.Country
	(*State)(nil),                                               // 10: misc.paymentaddress.v1.State
	(*City)(nil),                                                // 11: misc.paymentaddress.v1.City
}
var file_misc_paymentaddress_v1_customer_proto_depIdxs = []int32{
	6,  // 0: misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCountriesRequest.pagination:type_name -> utils.v1.PaginationRequest
	7,  // 1: misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCountriesResponse.error:type_name -> errmsg.v1.ErrorMessage
	8,  // 2: misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCountriesResponse.pagination:type_name -> utils.v1.PaginationResponse
	9,  // 3: misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCountriesResponse.countries:type_name -> misc.paymentaddress.v1.Country
	6,  // 4: misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchStatesRequest.pagination:type_name -> utils.v1.PaginationRequest
	7,  // 5: misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchStatesResponse.error:type_name -> errmsg.v1.ErrorMessage
	8,  // 6: misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchStatesResponse.pagination:type_name -> utils.v1.PaginationResponse
	10, // 7: misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchStatesResponse.states:type_name -> misc.paymentaddress.v1.State
	6,  // 8: misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCitiesRequest.pagination:type_name -> utils.v1.PaginationRequest
	7,  // 9: misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCitiesResponse.error:type_name -> errmsg.v1.ErrorMessage
	8,  // 10: misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCitiesResponse.pagination:type_name -> utils.v1.PaginationResponse
	11, // 11: misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCitiesResponse.cities:type_name -> misc.paymentaddress.v1.City
	0,  // 12: misc.paymentaddress.v1.CustomerPaymentAddressService.FetchCountries:input_type -> misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCountriesRequest
	2,  // 13: misc.paymentaddress.v1.CustomerPaymentAddressService.FetchStates:input_type -> misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchStatesRequest
	4,  // 14: misc.paymentaddress.v1.CustomerPaymentAddressService.FetchCities:input_type -> misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCitiesRequest
	1,  // 15: misc.paymentaddress.v1.CustomerPaymentAddressService.FetchCountries:output_type -> misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCountriesResponse
	3,  // 16: misc.paymentaddress.v1.CustomerPaymentAddressService.FetchStates:output_type -> misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchStatesResponse
	5,  // 17: misc.paymentaddress.v1.CustomerPaymentAddressService.FetchCities:output_type -> misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCitiesResponse
	15, // [15:18] is the sub-list for method output_type
	12, // [12:15] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_misc_paymentaddress_v1_customer_proto_init() }
func file_misc_paymentaddress_v1_customer_proto_init() {
	if File_misc_paymentaddress_v1_customer_proto != nil {
		return
	}
	file_misc_paymentaddress_v1_entity_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_misc_paymentaddress_v1_customer_proto_rawDesc), len(file_misc_paymentaddress_v1_customer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_misc_paymentaddress_v1_customer_proto_goTypes,
		DependencyIndexes: file_misc_paymentaddress_v1_customer_proto_depIdxs,
		MessageInfos:      file_misc_paymentaddress_v1_customer_proto_msgTypes,
	}.Build()
	File_misc_paymentaddress_v1_customer_proto = out.File
	file_misc_paymentaddress_v1_customer_proto_goTypes = nil
	file_misc_paymentaddress_v1_customer_proto_depIdxs = nil
}
