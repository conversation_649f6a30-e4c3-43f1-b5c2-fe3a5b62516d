// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: misc/paymentaddress/v1/entity.proto

package paymentaddressv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type State struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdState       int64                  `protobuf:"varint,1,opt,name=id_state,json=idState,proto3" json:"id_state,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *State) Reset() {
	*x = State{}
	mi := &file_misc_paymentaddress_v1_entity_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *State) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*State) ProtoMessage() {}

func (x *State) ProtoReflect() protoreflect.Message {
	mi := &file_misc_paymentaddress_v1_entity_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use State.ProtoReflect.Descriptor instead.
func (*State) Descriptor() ([]byte, []int) {
	return file_misc_paymentaddress_v1_entity_proto_rawDescGZIP(), []int{0}
}

func (x *State) GetIdState() int64 {
	if x != nil {
		return x.IdState
	}
	return 0
}

func (x *State) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type City struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdCity        int64                  `protobuf:"varint,1,opt,name=id_city,json=idCity,proto3" json:"id_city,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *City) Reset() {
	*x = City{}
	mi := &file_misc_paymentaddress_v1_entity_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *City) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*City) ProtoMessage() {}

func (x *City) ProtoReflect() protoreflect.Message {
	mi := &file_misc_paymentaddress_v1_entity_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use City.ProtoReflect.Descriptor instead.
func (*City) Descriptor() ([]byte, []int) {
	return file_misc_paymentaddress_v1_entity_proto_rawDescGZIP(), []int{1}
}

func (x *City) GetIdCity() int64 {
	if x != nil {
		return x.IdCity
	}
	return 0
}

func (x *City) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type Country struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdCountry     int64                  `protobuf:"varint,1,opt,name=id_country,json=idCountry,proto3" json:"id_country,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Iso2          string                 `protobuf:"bytes,3,opt,name=iso2,proto3" json:"iso2,omitempty"`
	Iso3          string                 `protobuf:"bytes,4,opt,name=iso3,proto3" json:"iso3,omitempty"`
	PhoneCode     string                 `protobuf:"bytes,5,opt,name=phone_code,json=phoneCode,proto3" json:"phone_code,omitempty"`
	Emoji         string                 `protobuf:"bytes,6,opt,name=emoji,proto3" json:"emoji,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Country) Reset() {
	*x = Country{}
	mi := &file_misc_paymentaddress_v1_entity_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Country) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Country) ProtoMessage() {}

func (x *Country) ProtoReflect() protoreflect.Message {
	mi := &file_misc_paymentaddress_v1_entity_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Country.ProtoReflect.Descriptor instead.
func (*Country) Descriptor() ([]byte, []int) {
	return file_misc_paymentaddress_v1_entity_proto_rawDescGZIP(), []int{2}
}

func (x *Country) GetIdCountry() int64 {
	if x != nil {
		return x.IdCountry
	}
	return 0
}

func (x *Country) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Country) GetIso2() string {
	if x != nil {
		return x.Iso2
	}
	return ""
}

func (x *Country) GetIso3() string {
	if x != nil {
		return x.Iso3
	}
	return ""
}

func (x *Country) GetPhoneCode() string {
	if x != nil {
		return x.PhoneCode
	}
	return ""
}

func (x *Country) GetEmoji() string {
	if x != nil {
		return x.Emoji
	}
	return ""
}

var File_misc_paymentaddress_v1_entity_proto protoreflect.FileDescriptor

const file_misc_paymentaddress_v1_entity_proto_rawDesc = "" +
	"\n" +
	"#misc/paymentaddress/v1/entity.proto\x12\x16misc.paymentaddress.v1\"6\n" +
	"\x05State\x12\x19\n" +
	"\bid_state\x18\x01 \x01(\x03R\aidState\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"3\n" +
	"\x04City\x12\x17\n" +
	"\aid_city\x18\x01 \x01(\x03R\x06idCity\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"\x99\x01\n" +
	"\aCountry\x12\x1d\n" +
	"\n" +
	"id_country\x18\x01 \x01(\x03R\tidCountry\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04iso2\x18\x03 \x01(\tR\x04iso2\x12\x12\n" +
	"\x04iso3\x18\x04 \x01(\tR\x04iso3\x12\x1d\n" +
	"\n" +
	"phone_code\x18\x05 \x01(\tR\tphoneCode\x12\x14\n" +
	"\x05emoji\x18\x06 \x01(\tR\x05emojiBXZVgit.tmproxy-infra.com/algo/algoproxy-proto/gen/misc/paymentaddress/v1;paymentaddressv1b\x06proto3"

var (
	file_misc_paymentaddress_v1_entity_proto_rawDescOnce sync.Once
	file_misc_paymentaddress_v1_entity_proto_rawDescData []byte
)

func file_misc_paymentaddress_v1_entity_proto_rawDescGZIP() []byte {
	file_misc_paymentaddress_v1_entity_proto_rawDescOnce.Do(func() {
		file_misc_paymentaddress_v1_entity_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_misc_paymentaddress_v1_entity_proto_rawDesc), len(file_misc_paymentaddress_v1_entity_proto_rawDesc)))
	})
	return file_misc_paymentaddress_v1_entity_proto_rawDescData
}

var file_misc_paymentaddress_v1_entity_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_misc_paymentaddress_v1_entity_proto_goTypes = []any{
	(*State)(nil),   // 0: misc.paymentaddress.v1.State
	(*City)(nil),    // 1: misc.paymentaddress.v1.City
	(*Country)(nil), // 2: misc.paymentaddress.v1.Country
}
var file_misc_paymentaddress_v1_entity_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_misc_paymentaddress_v1_entity_proto_init() }
func file_misc_paymentaddress_v1_entity_proto_init() {
	if File_misc_paymentaddress_v1_entity_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_misc_paymentaddress_v1_entity_proto_rawDesc), len(file_misc_paymentaddress_v1_entity_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_misc_paymentaddress_v1_entity_proto_goTypes,
		DependencyIndexes: file_misc_paymentaddress_v1_entity_proto_depIdxs,
		MessageInfos:      file_misc_paymentaddress_v1_entity_proto_msgTypes,
	}.Build()
	File_misc_paymentaddress_v1_entity_proto = out.File
	file_misc_paymentaddress_v1_entity_proto_goTypes = nil
	file_misc_paymentaddress_v1_entity_proto_depIdxs = nil
}
