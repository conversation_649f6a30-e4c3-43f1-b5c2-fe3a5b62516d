// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: misc/paymentaddress/v1/customer.proto

package paymentaddressv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/misc/paymentaddress/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CustomerPaymentAddressServiceName is the fully-qualified name of the
	// CustomerPaymentAddressService service.
	CustomerPaymentAddressServiceName = "misc.paymentaddress.v1.CustomerPaymentAddressService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CustomerPaymentAddressServiceFetchCountriesProcedure is the fully-qualified name of the
	// CustomerPaymentAddressService's FetchCountries RPC.
	CustomerPaymentAddressServiceFetchCountriesProcedure = "/misc.paymentaddress.v1.CustomerPaymentAddressService/FetchCountries"
	// CustomerPaymentAddressServiceFetchStatesProcedure is the fully-qualified name of the
	// CustomerPaymentAddressService's FetchStates RPC.
	CustomerPaymentAddressServiceFetchStatesProcedure = "/misc.paymentaddress.v1.CustomerPaymentAddressService/FetchStates"
	// CustomerPaymentAddressServiceFetchCitiesProcedure is the fully-qualified name of the
	// CustomerPaymentAddressService's FetchCities RPC.
	CustomerPaymentAddressServiceFetchCitiesProcedure = "/misc.paymentaddress.v1.CustomerPaymentAddressService/FetchCities"
)

// CustomerPaymentAddressServiceClient is a client for the
// misc.paymentaddress.v1.CustomerPaymentAddressService service.
type CustomerPaymentAddressServiceClient interface {
	FetchCountries(context.Context, *connect.Request[v1.CustomerPaymentAddressServiceFetchCountriesRequest]) (*connect.Response[v1.CustomerPaymentAddressServiceFetchCountriesResponse], error)
	FetchStates(context.Context, *connect.Request[v1.CustomerPaymentAddressServiceFetchStatesRequest]) (*connect.Response[v1.CustomerPaymentAddressServiceFetchStatesResponse], error)
	FetchCities(context.Context, *connect.Request[v1.CustomerPaymentAddressServiceFetchCitiesRequest]) (*connect.Response[v1.CustomerPaymentAddressServiceFetchCitiesResponse], error)
}

// NewCustomerPaymentAddressServiceClient constructs a client for the
// misc.paymentaddress.v1.CustomerPaymentAddressService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCustomerPaymentAddressServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CustomerPaymentAddressServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	customerPaymentAddressServiceMethods := v1.File_misc_paymentaddress_v1_customer_proto.Services().ByName("CustomerPaymentAddressService").Methods()
	return &customerPaymentAddressServiceClient{
		fetchCountries: connect.NewClient[v1.CustomerPaymentAddressServiceFetchCountriesRequest, v1.CustomerPaymentAddressServiceFetchCountriesResponse](
			httpClient,
			baseURL+CustomerPaymentAddressServiceFetchCountriesProcedure,
			connect.WithSchema(customerPaymentAddressServiceMethods.ByName("FetchCountries")),
			connect.WithClientOptions(opts...),
		),
		fetchStates: connect.NewClient[v1.CustomerPaymentAddressServiceFetchStatesRequest, v1.CustomerPaymentAddressServiceFetchStatesResponse](
			httpClient,
			baseURL+CustomerPaymentAddressServiceFetchStatesProcedure,
			connect.WithSchema(customerPaymentAddressServiceMethods.ByName("FetchStates")),
			connect.WithClientOptions(opts...),
		),
		fetchCities: connect.NewClient[v1.CustomerPaymentAddressServiceFetchCitiesRequest, v1.CustomerPaymentAddressServiceFetchCitiesResponse](
			httpClient,
			baseURL+CustomerPaymentAddressServiceFetchCitiesProcedure,
			connect.WithSchema(customerPaymentAddressServiceMethods.ByName("FetchCities")),
			connect.WithClientOptions(opts...),
		),
	}
}

// customerPaymentAddressServiceClient implements CustomerPaymentAddressServiceClient.
type customerPaymentAddressServiceClient struct {
	fetchCountries *connect.Client[v1.CustomerPaymentAddressServiceFetchCountriesRequest, v1.CustomerPaymentAddressServiceFetchCountriesResponse]
	fetchStates    *connect.Client[v1.CustomerPaymentAddressServiceFetchStatesRequest, v1.CustomerPaymentAddressServiceFetchStatesResponse]
	fetchCities    *connect.Client[v1.CustomerPaymentAddressServiceFetchCitiesRequest, v1.CustomerPaymentAddressServiceFetchCitiesResponse]
}

// FetchCountries calls misc.paymentaddress.v1.CustomerPaymentAddressService.FetchCountries.
func (c *customerPaymentAddressServiceClient) FetchCountries(ctx context.Context, req *connect.Request[v1.CustomerPaymentAddressServiceFetchCountriesRequest]) (*connect.Response[v1.CustomerPaymentAddressServiceFetchCountriesResponse], error) {
	return c.fetchCountries.CallUnary(ctx, req)
}

// FetchStates calls misc.paymentaddress.v1.CustomerPaymentAddressService.FetchStates.
func (c *customerPaymentAddressServiceClient) FetchStates(ctx context.Context, req *connect.Request[v1.CustomerPaymentAddressServiceFetchStatesRequest]) (*connect.Response[v1.CustomerPaymentAddressServiceFetchStatesResponse], error) {
	return c.fetchStates.CallUnary(ctx, req)
}

// FetchCities calls misc.paymentaddress.v1.CustomerPaymentAddressService.FetchCities.
func (c *customerPaymentAddressServiceClient) FetchCities(ctx context.Context, req *connect.Request[v1.CustomerPaymentAddressServiceFetchCitiesRequest]) (*connect.Response[v1.CustomerPaymentAddressServiceFetchCitiesResponse], error) {
	return c.fetchCities.CallUnary(ctx, req)
}

// CustomerPaymentAddressServiceHandler is an implementation of the
// misc.paymentaddress.v1.CustomerPaymentAddressService service.
type CustomerPaymentAddressServiceHandler interface {
	FetchCountries(context.Context, *connect.Request[v1.CustomerPaymentAddressServiceFetchCountriesRequest]) (*connect.Response[v1.CustomerPaymentAddressServiceFetchCountriesResponse], error)
	FetchStates(context.Context, *connect.Request[v1.CustomerPaymentAddressServiceFetchStatesRequest]) (*connect.Response[v1.CustomerPaymentAddressServiceFetchStatesResponse], error)
	FetchCities(context.Context, *connect.Request[v1.CustomerPaymentAddressServiceFetchCitiesRequest]) (*connect.Response[v1.CustomerPaymentAddressServiceFetchCitiesResponse], error)
}

// NewCustomerPaymentAddressServiceHandler builds an HTTP handler from the service implementation.
// It returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCustomerPaymentAddressServiceHandler(svc CustomerPaymentAddressServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	customerPaymentAddressServiceMethods := v1.File_misc_paymentaddress_v1_customer_proto.Services().ByName("CustomerPaymentAddressService").Methods()
	customerPaymentAddressServiceFetchCountriesHandler := connect.NewUnaryHandler(
		CustomerPaymentAddressServiceFetchCountriesProcedure,
		svc.FetchCountries,
		connect.WithSchema(customerPaymentAddressServiceMethods.ByName("FetchCountries")),
		connect.WithHandlerOptions(opts...),
	)
	customerPaymentAddressServiceFetchStatesHandler := connect.NewUnaryHandler(
		CustomerPaymentAddressServiceFetchStatesProcedure,
		svc.FetchStates,
		connect.WithSchema(customerPaymentAddressServiceMethods.ByName("FetchStates")),
		connect.WithHandlerOptions(opts...),
	)
	customerPaymentAddressServiceFetchCitiesHandler := connect.NewUnaryHandler(
		CustomerPaymentAddressServiceFetchCitiesProcedure,
		svc.FetchCities,
		connect.WithSchema(customerPaymentAddressServiceMethods.ByName("FetchCities")),
		connect.WithHandlerOptions(opts...),
	)
	return "/misc.paymentaddress.v1.CustomerPaymentAddressService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CustomerPaymentAddressServiceFetchCountriesProcedure:
			customerPaymentAddressServiceFetchCountriesHandler.ServeHTTP(w, r)
		case CustomerPaymentAddressServiceFetchStatesProcedure:
			customerPaymentAddressServiceFetchStatesHandler.ServeHTTP(w, r)
		case CustomerPaymentAddressServiceFetchCitiesProcedure:
			customerPaymentAddressServiceFetchCitiesHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCustomerPaymentAddressServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCustomerPaymentAddressServiceHandler struct{}

func (UnimplementedCustomerPaymentAddressServiceHandler) FetchCountries(context.Context, *connect.Request[v1.CustomerPaymentAddressServiceFetchCountriesRequest]) (*connect.Response[v1.CustomerPaymentAddressServiceFetchCountriesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("misc.paymentaddress.v1.CustomerPaymentAddressService.FetchCountries is not implemented"))
}

func (UnimplementedCustomerPaymentAddressServiceHandler) FetchStates(context.Context, *connect.Request[v1.CustomerPaymentAddressServiceFetchStatesRequest]) (*connect.Response[v1.CustomerPaymentAddressServiceFetchStatesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("misc.paymentaddress.v1.CustomerPaymentAddressService.FetchStates is not implemented"))
}

func (UnimplementedCustomerPaymentAddressServiceHandler) FetchCities(context.Context, *connect.Request[v1.CustomerPaymentAddressServiceFetchCitiesRequest]) (*connect.Response[v1.CustomerPaymentAddressServiceFetchCitiesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("misc.paymentaddress.v1.CustomerPaymentAddressService.FetchCities is not implemented"))
}
