// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: misc/paymentaddress/v1/internal.proto

package paymentaddressv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/misc/paymentaddress/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// InternalPaymentAddressServiceName is the fully-qualified name of the
	// InternalPaymentAddressService service.
	InternalPaymentAddressServiceName = "misc.paymentaddress.v1.InternalPaymentAddressService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// InternalPaymentAddressServiceValidPaymentAddressProcedure is the fully-qualified name of the
	// InternalPaymentAddressService's ValidPaymentAddress RPC.
	InternalPaymentAddressServiceValidPaymentAddressProcedure = "/misc.paymentaddress.v1.InternalPaymentAddressService/ValidPaymentAddress"
)

// InternalPaymentAddressServiceClient is a client for the
// misc.paymentaddress.v1.InternalPaymentAddressService service.
type InternalPaymentAddressServiceClient interface {
	ValidPaymentAddress(context.Context, *connect.Request[v1.InternalPaymentAddressServiceValidPaymentAddressRequest]) (*connect.Response[v1.InternalPaymentAddressServiceValidPaymentAddressResponse], error)
}

// NewInternalPaymentAddressServiceClient constructs a client for the
// misc.paymentaddress.v1.InternalPaymentAddressService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewInternalPaymentAddressServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) InternalPaymentAddressServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	internalPaymentAddressServiceMethods := v1.File_misc_paymentaddress_v1_internal_proto.Services().ByName("InternalPaymentAddressService").Methods()
	return &internalPaymentAddressServiceClient{
		validPaymentAddress: connect.NewClient[v1.InternalPaymentAddressServiceValidPaymentAddressRequest, v1.InternalPaymentAddressServiceValidPaymentAddressResponse](
			httpClient,
			baseURL+InternalPaymentAddressServiceValidPaymentAddressProcedure,
			connect.WithSchema(internalPaymentAddressServiceMethods.ByName("ValidPaymentAddress")),
			connect.WithClientOptions(opts...),
		),
	}
}

// internalPaymentAddressServiceClient implements InternalPaymentAddressServiceClient.
type internalPaymentAddressServiceClient struct {
	validPaymentAddress *connect.Client[v1.InternalPaymentAddressServiceValidPaymentAddressRequest, v1.InternalPaymentAddressServiceValidPaymentAddressResponse]
}

// ValidPaymentAddress calls
// misc.paymentaddress.v1.InternalPaymentAddressService.ValidPaymentAddress.
func (c *internalPaymentAddressServiceClient) ValidPaymentAddress(ctx context.Context, req *connect.Request[v1.InternalPaymentAddressServiceValidPaymentAddressRequest]) (*connect.Response[v1.InternalPaymentAddressServiceValidPaymentAddressResponse], error) {
	return c.validPaymentAddress.CallUnary(ctx, req)
}

// InternalPaymentAddressServiceHandler is an implementation of the
// misc.paymentaddress.v1.InternalPaymentAddressService service.
type InternalPaymentAddressServiceHandler interface {
	ValidPaymentAddress(context.Context, *connect.Request[v1.InternalPaymentAddressServiceValidPaymentAddressRequest]) (*connect.Response[v1.InternalPaymentAddressServiceValidPaymentAddressResponse], error)
}

// NewInternalPaymentAddressServiceHandler builds an HTTP handler from the service implementation.
// It returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewInternalPaymentAddressServiceHandler(svc InternalPaymentAddressServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	internalPaymentAddressServiceMethods := v1.File_misc_paymentaddress_v1_internal_proto.Services().ByName("InternalPaymentAddressService").Methods()
	internalPaymentAddressServiceValidPaymentAddressHandler := connect.NewUnaryHandler(
		InternalPaymentAddressServiceValidPaymentAddressProcedure,
		svc.ValidPaymentAddress,
		connect.WithSchema(internalPaymentAddressServiceMethods.ByName("ValidPaymentAddress")),
		connect.WithHandlerOptions(opts...),
	)
	return "/misc.paymentaddress.v1.InternalPaymentAddressService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case InternalPaymentAddressServiceValidPaymentAddressProcedure:
			internalPaymentAddressServiceValidPaymentAddressHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedInternalPaymentAddressServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedInternalPaymentAddressServiceHandler struct{}

func (UnimplementedInternalPaymentAddressServiceHandler) ValidPaymentAddress(context.Context, *connect.Request[v1.InternalPaymentAddressServiceValidPaymentAddressRequest]) (*connect.Response[v1.InternalPaymentAddressServiceValidPaymentAddressResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("misc.paymentaddress.v1.InternalPaymentAddressService.ValidPaymentAddress is not implemented"))
}
