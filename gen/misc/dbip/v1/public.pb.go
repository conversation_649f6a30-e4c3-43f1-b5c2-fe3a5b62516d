// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: misc/dbip/v1/public.proto

package dbipv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/misc/paymentaddress/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PublicDBIPServiceIPInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IpAddr        string                 `protobuf:"bytes,1,opt,name=ip_addr,json=ipAddr,proto3" json:"ip_addr,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicDBIPServiceIPInfoRequest) Reset() {
	*x = PublicDBIPServiceIPInfoRequest{}
	mi := &file_misc_dbip_v1_public_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicDBIPServiceIPInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicDBIPServiceIPInfoRequest) ProtoMessage() {}

func (x *PublicDBIPServiceIPInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_misc_dbip_v1_public_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicDBIPServiceIPInfoRequest.ProtoReflect.Descriptor instead.
func (*PublicDBIPServiceIPInfoRequest) Descriptor() ([]byte, []int) {
	return file_misc_dbip_v1_public_proto_rawDescGZIP(), []int{0}
}

func (x *PublicDBIPServiceIPInfoRequest) GetIpAddr() string {
	if x != nil {
		return x.IpAddr
	}
	return ""
}

type PublicDBIPServiceIPInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	IpInfo        *IPInfo                `protobuf:"bytes,2,opt,name=ip_info,json=ipInfo,proto3" json:"ip_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicDBIPServiceIPInfoResponse) Reset() {
	*x = PublicDBIPServiceIPInfoResponse{}
	mi := &file_misc_dbip_v1_public_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicDBIPServiceIPInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicDBIPServiceIPInfoResponse) ProtoMessage() {}

func (x *PublicDBIPServiceIPInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_misc_dbip_v1_public_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicDBIPServiceIPInfoResponse.ProtoReflect.Descriptor instead.
func (*PublicDBIPServiceIPInfoResponse) Descriptor() ([]byte, []int) {
	return file_misc_dbip_v1_public_proto_rawDescGZIP(), []int{1}
}

func (x *PublicDBIPServiceIPInfoResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *PublicDBIPServiceIPInfoResponse) GetIpInfo() *IPInfo {
	if x != nil {
		return x.IpInfo
	}
	return nil
}

type IPInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Region         *NamesRecord           `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	Country        *NamesRecord           `protobuf:"bytes,2,opt,name=country,proto3" json:"country,omitempty"`
	State          *NamesRecord           `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	City           *NamesRecord           `protobuf:"bytes,4,opt,name=city,proto3" json:"city,omitempty"`
	GeoLocation    *GeoLocation           `protobuf:"bytes,5,opt,name=geo_location,json=geoLocation,proto3" json:"geo_location,omitempty"`
	ConnectionInfo *ConnectionInfo        `protobuf:"bytes,6,opt,name=connection_info,json=connectionInfo,proto3" json:"connection_info,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *IPInfo) Reset() {
	*x = IPInfo{}
	mi := &file_misc_dbip_v1_public_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IPInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPInfo) ProtoMessage() {}

func (x *IPInfo) ProtoReflect() protoreflect.Message {
	mi := &file_misc_dbip_v1_public_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPInfo.ProtoReflect.Descriptor instead.
func (*IPInfo) Descriptor() ([]byte, []int) {
	return file_misc_dbip_v1_public_proto_rawDescGZIP(), []int{2}
}

func (x *IPInfo) GetRegion() *NamesRecord {
	if x != nil {
		return x.Region
	}
	return nil
}

func (x *IPInfo) GetCountry() *NamesRecord {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *IPInfo) GetState() *NamesRecord {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *IPInfo) GetCity() *NamesRecord {
	if x != nil {
		return x.City
	}
	return nil
}

func (x *IPInfo) GetGeoLocation() *GeoLocation {
	if x != nil {
		return x.GeoLocation
	}
	return nil
}

func (x *IPInfo) GetConnectionInfo() *ConnectionInfo {
	if x != nil {
		return x.ConnectionInfo
	}
	return nil
}

type NamesRecord struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsoCode       string                 `protobuf:"bytes,1,opt,name=iso_code,json=isoCode,proto3" json:"iso_code,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	GeonameId     int64                  `protobuf:"varint,3,opt,name=geoname_id,json=geonameId,proto3" json:"geoname_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NamesRecord) Reset() {
	*x = NamesRecord{}
	mi := &file_misc_dbip_v1_public_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NamesRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamesRecord) ProtoMessage() {}

func (x *NamesRecord) ProtoReflect() protoreflect.Message {
	mi := &file_misc_dbip_v1_public_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamesRecord.ProtoReflect.Descriptor instead.
func (*NamesRecord) Descriptor() ([]byte, []int) {
	return file_misc_dbip_v1_public_proto_rawDescGZIP(), []int{3}
}

func (x *NamesRecord) GetIsoCode() string {
	if x != nil {
		return x.IsoCode
	}
	return ""
}

func (x *NamesRecord) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamesRecord) GetGeonameId() int64 {
	if x != nil {
		return x.GeonameId
	}
	return 0
}

type GeoLocation struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Lat            float64                `protobuf:"fixed64,1,opt,name=lat,proto3" json:"lat,omitempty"`
	Long           float64                `protobuf:"fixed64,2,opt,name=long,proto3" json:"long,omitempty"`
	TimeZone       string                 `protobuf:"bytes,3,opt,name=time_zone,json=timeZone,proto3" json:"time_zone,omitempty"`
	WeatherStation string                 `protobuf:"bytes,4,opt,name=weather_station,json=weatherStation,proto3" json:"weather_station,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GeoLocation) Reset() {
	*x = GeoLocation{}
	mi := &file_misc_dbip_v1_public_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GeoLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeoLocation) ProtoMessage() {}

func (x *GeoLocation) ProtoReflect() protoreflect.Message {
	mi := &file_misc_dbip_v1_public_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeoLocation.ProtoReflect.Descriptor instead.
func (*GeoLocation) Descriptor() ([]byte, []int) {
	return file_misc_dbip_v1_public_proto_rawDescGZIP(), []int{4}
}

func (x *GeoLocation) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *GeoLocation) GetLong() float64 {
	if x != nil {
		return x.Long
	}
	return 0
}

func (x *GeoLocation) GetTimeZone() string {
	if x != nil {
		return x.TimeZone
	}
	return ""
}

func (x *GeoLocation) GetWeatherStation() string {
	if x != nil {
		return x.WeatherStation
	}
	return ""
}

type ConnectionInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	AsNumber       int64                  `protobuf:"varint,1,opt,name=as_number,json=asNumber,proto3" json:"as_number,omitempty"`
	AsName         string                 `protobuf:"bytes,2,opt,name=as_name,json=asName,proto3" json:"as_name,omitempty"`
	Organization   string                 `protobuf:"bytes,3,opt,name=organization,proto3" json:"organization,omitempty"`
	ConnectionType string                 `protobuf:"bytes,4,opt,name=connection_type,json=connectionType,proto3" json:"connection_type,omitempty"`
	UserType       string                 `protobuf:"bytes,5,opt,name=user_type,json=userType,proto3" json:"user_type,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ConnectionInfo) Reset() {
	*x = ConnectionInfo{}
	mi := &file_misc_dbip_v1_public_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConnectionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionInfo) ProtoMessage() {}

func (x *ConnectionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_misc_dbip_v1_public_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionInfo.ProtoReflect.Descriptor instead.
func (*ConnectionInfo) Descriptor() ([]byte, []int) {
	return file_misc_dbip_v1_public_proto_rawDescGZIP(), []int{5}
}

func (x *ConnectionInfo) GetAsNumber() int64 {
	if x != nil {
		return x.AsNumber
	}
	return 0
}

func (x *ConnectionInfo) GetAsName() string {
	if x != nil {
		return x.AsName
	}
	return ""
}

func (x *ConnectionInfo) GetOrganization() string {
	if x != nil {
		return x.Organization
	}
	return ""
}

func (x *ConnectionInfo) GetConnectionType() string {
	if x != nil {
		return x.ConnectionType
	}
	return ""
}

func (x *ConnectionInfo) GetUserType() string {
	if x != nil {
		return x.UserType
	}
	return ""
}

// ------------------------------------------------------
type PublicDBIPServiceFetchCountriesRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	CountryNameSearch string                 `protobuf:"bytes,1,opt,name=country_name_search,json=countryNameSearch,proto3" json:"country_name_search,omitempty"`
	Pagination        *v11.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PublicDBIPServiceFetchCountriesRequest) Reset() {
	*x = PublicDBIPServiceFetchCountriesRequest{}
	mi := &file_misc_dbip_v1_public_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicDBIPServiceFetchCountriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicDBIPServiceFetchCountriesRequest) ProtoMessage() {}

func (x *PublicDBIPServiceFetchCountriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_misc_dbip_v1_public_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicDBIPServiceFetchCountriesRequest.ProtoReflect.Descriptor instead.
func (*PublicDBIPServiceFetchCountriesRequest) Descriptor() ([]byte, []int) {
	return file_misc_dbip_v1_public_proto_rawDescGZIP(), []int{6}
}

func (x *PublicDBIPServiceFetchCountriesRequest) GetCountryNameSearch() string {
	if x != nil {
		return x.CountryNameSearch
	}
	return ""
}

func (x *PublicDBIPServiceFetchCountriesRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type PublicDBIPServiceFetchCountriesResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Countries     []*v12.Country          `protobuf:"bytes,3,rep,name=countries,proto3" json:"countries,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicDBIPServiceFetchCountriesResponse) Reset() {
	*x = PublicDBIPServiceFetchCountriesResponse{}
	mi := &file_misc_dbip_v1_public_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicDBIPServiceFetchCountriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicDBIPServiceFetchCountriesResponse) ProtoMessage() {}

func (x *PublicDBIPServiceFetchCountriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_misc_dbip_v1_public_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicDBIPServiceFetchCountriesResponse.ProtoReflect.Descriptor instead.
func (*PublicDBIPServiceFetchCountriesResponse) Descriptor() ([]byte, []int) {
	return file_misc_dbip_v1_public_proto_rawDescGZIP(), []int{7}
}

func (x *PublicDBIPServiceFetchCountriesResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *PublicDBIPServiceFetchCountriesResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *PublicDBIPServiceFetchCountriesResponse) GetCountries() []*v12.Country {
	if x != nil {
		return x.Countries
	}
	return nil
}

type PublicDBIPServiceFetchStatesRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	IdCountry       int64                  `protobuf:"varint,1,opt,name=id_country,json=idCountry,proto3" json:"id_country,omitempty"`
	StateNameSearch string                 `protobuf:"bytes,2,opt,name=state_name_search,json=stateNameSearch,proto3" json:"state_name_search,omitempty"`
	Pagination      *v11.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *PublicDBIPServiceFetchStatesRequest) Reset() {
	*x = PublicDBIPServiceFetchStatesRequest{}
	mi := &file_misc_dbip_v1_public_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicDBIPServiceFetchStatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicDBIPServiceFetchStatesRequest) ProtoMessage() {}

func (x *PublicDBIPServiceFetchStatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_misc_dbip_v1_public_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicDBIPServiceFetchStatesRequest.ProtoReflect.Descriptor instead.
func (*PublicDBIPServiceFetchStatesRequest) Descriptor() ([]byte, []int) {
	return file_misc_dbip_v1_public_proto_rawDescGZIP(), []int{8}
}

func (x *PublicDBIPServiceFetchStatesRequest) GetIdCountry() int64 {
	if x != nil {
		return x.IdCountry
	}
	return 0
}

func (x *PublicDBIPServiceFetchStatesRequest) GetStateNameSearch() string {
	if x != nil {
		return x.StateNameSearch
	}
	return ""
}

func (x *PublicDBIPServiceFetchStatesRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type PublicDBIPServiceFetchStatesResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	States        []*v12.State            `protobuf:"bytes,3,rep,name=states,proto3" json:"states,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicDBIPServiceFetchStatesResponse) Reset() {
	*x = PublicDBIPServiceFetchStatesResponse{}
	mi := &file_misc_dbip_v1_public_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicDBIPServiceFetchStatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicDBIPServiceFetchStatesResponse) ProtoMessage() {}

func (x *PublicDBIPServiceFetchStatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_misc_dbip_v1_public_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicDBIPServiceFetchStatesResponse.ProtoReflect.Descriptor instead.
func (*PublicDBIPServiceFetchStatesResponse) Descriptor() ([]byte, []int) {
	return file_misc_dbip_v1_public_proto_rawDescGZIP(), []int{9}
}

func (x *PublicDBIPServiceFetchStatesResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *PublicDBIPServiceFetchStatesResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *PublicDBIPServiceFetchStatesResponse) GetStates() []*v12.State {
	if x != nil {
		return x.States
	}
	return nil
}

type PublicDBIPServiceFetchCitiesRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdState        int64                  `protobuf:"varint,1,opt,name=id_state,json=idState,proto3" json:"id_state,omitempty"`
	CityNameSearch string                 `protobuf:"bytes,2,opt,name=city_name_search,json=cityNameSearch,proto3" json:"city_name_search,omitempty"`
	Pagination     *v11.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PublicDBIPServiceFetchCitiesRequest) Reset() {
	*x = PublicDBIPServiceFetchCitiesRequest{}
	mi := &file_misc_dbip_v1_public_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicDBIPServiceFetchCitiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicDBIPServiceFetchCitiesRequest) ProtoMessage() {}

func (x *PublicDBIPServiceFetchCitiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_misc_dbip_v1_public_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicDBIPServiceFetchCitiesRequest.ProtoReflect.Descriptor instead.
func (*PublicDBIPServiceFetchCitiesRequest) Descriptor() ([]byte, []int) {
	return file_misc_dbip_v1_public_proto_rawDescGZIP(), []int{10}
}

func (x *PublicDBIPServiceFetchCitiesRequest) GetIdState() int64 {
	if x != nil {
		return x.IdState
	}
	return 0
}

func (x *PublicDBIPServiceFetchCitiesRequest) GetCityNameSearch() string {
	if x != nil {
		return x.CityNameSearch
	}
	return ""
}

func (x *PublicDBIPServiceFetchCitiesRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type PublicDBIPServiceFetchCitiesResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Cities        []*v12.City             `protobuf:"bytes,3,rep,name=cities,proto3" json:"cities,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PublicDBIPServiceFetchCitiesResponse) Reset() {
	*x = PublicDBIPServiceFetchCitiesResponse{}
	mi := &file_misc_dbip_v1_public_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PublicDBIPServiceFetchCitiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PublicDBIPServiceFetchCitiesResponse) ProtoMessage() {}

func (x *PublicDBIPServiceFetchCitiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_misc_dbip_v1_public_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PublicDBIPServiceFetchCitiesResponse.ProtoReflect.Descriptor instead.
func (*PublicDBIPServiceFetchCitiesResponse) Descriptor() ([]byte, []int) {
	return file_misc_dbip_v1_public_proto_rawDescGZIP(), []int{11}
}

func (x *PublicDBIPServiceFetchCitiesResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *PublicDBIPServiceFetchCitiesResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *PublicDBIPServiceFetchCitiesResponse) GetCities() []*v12.City {
	if x != nil {
		return x.Cities
	}
	return nil
}

var File_misc_dbip_v1_public_proto protoreflect.FileDescriptor

const file_misc_dbip_v1_public_proto_rawDesc = "" +
	"\n" +
	"\x19misc/dbip/v1/public.proto\x12\fmisc.dbip.v1\x1a\x14utils/v1/utils.proto\x1a\x18errmsg/v1/errormsg.proto\x1a#misc/paymentaddress/v1/entity.proto\"9\n" +
	"\x1ePublicDBIPServiceIPInfoRequest\x12\x17\n" +
	"\aip_addr\x18\x01 \x01(\tR\x06ipAddr\"\x7f\n" +
	"\x1fPublicDBIPServiceIPInfoResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12-\n" +
	"\aip_info\x18\x02 \x01(\v2\x14.misc.dbip.v1.IPInfoR\x06ipInfo\"\xd5\x02\n" +
	"\x06IPInfo\x121\n" +
	"\x06region\x18\x01 \x01(\v2\x19.misc.dbip.v1.NamesRecordR\x06region\x123\n" +
	"\acountry\x18\x02 \x01(\v2\x19.misc.dbip.v1.NamesRecordR\acountry\x12/\n" +
	"\x05state\x18\x03 \x01(\v2\x19.misc.dbip.v1.NamesRecordR\x05state\x12-\n" +
	"\x04city\x18\x04 \x01(\v2\x19.misc.dbip.v1.NamesRecordR\x04city\x12<\n" +
	"\fgeo_location\x18\x05 \x01(\v2\x19.misc.dbip.v1.GeoLocationR\vgeoLocation\x12E\n" +
	"\x0fconnection_info\x18\x06 \x01(\v2\x1c.misc.dbip.v1.ConnectionInfoR\x0econnectionInfo\"[\n" +
	"\vNamesRecord\x12\x19\n" +
	"\biso_code\x18\x01 \x01(\tR\aisoCode\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"geoname_id\x18\x03 \x01(\x03R\tgeonameId\"y\n" +
	"\vGeoLocation\x12\x10\n" +
	"\x03lat\x18\x01 \x01(\x01R\x03lat\x12\x12\n" +
	"\x04long\x18\x02 \x01(\x01R\x04long\x12\x1b\n" +
	"\ttime_zone\x18\x03 \x01(\tR\btimeZone\x12'\n" +
	"\x0fweather_station\x18\x04 \x01(\tR\x0eweatherStation\"\xb0\x01\n" +
	"\x0eConnectionInfo\x12\x1b\n" +
	"\tas_number\x18\x01 \x01(\x03R\basNumber\x12\x17\n" +
	"\aas_name\x18\x02 \x01(\tR\x06asName\x12\"\n" +
	"\forganization\x18\x03 \x01(\tR\forganization\x12'\n" +
	"\x0fconnection_type\x18\x04 \x01(\tR\x0econnectionType\x12\x1b\n" +
	"\tuser_type\x18\x05 \x01(\tR\buserType\"\x95\x01\n" +
	"&PublicDBIPServiceFetchCountriesRequest\x12.\n" +
	"\x13country_name_search\x18\x01 \x01(\tR\x11countryNameSearch\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xd5\x01\n" +
	"'PublicDBIPServiceFetchCountriesResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12=\n" +
	"\tcountries\x18\x03 \x03(\v2\x1f.misc.paymentaddress.v1.CountryR\tcountries\"\xad\x01\n" +
	"#PublicDBIPServiceFetchStatesRequest\x12\x1d\n" +
	"\n" +
	"id_country\x18\x01 \x01(\x03R\tidCountry\x12*\n" +
	"\x11state_name_search\x18\x02 \x01(\tR\x0fstateNameSearch\x12;\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xca\x01\n" +
	"$PublicDBIPServiceFetchStatesResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x125\n" +
	"\x06states\x18\x03 \x03(\v2\x1d.misc.paymentaddress.v1.StateR\x06states\"\xa7\x01\n" +
	"#PublicDBIPServiceFetchCitiesRequest\x12\x19\n" +
	"\bid_state\x18\x01 \x01(\x03R\aidState\x12(\n" +
	"\x10city_name_search\x18\x02 \x01(\tR\x0ecityNameSearch\x12;\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xc9\x01\n" +
	"$PublicDBIPServiceFetchCitiesResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x124\n" +
	"\x06cities\x18\x03 \x03(\v2\x1c.misc.paymentaddress.v1.CityR\x06cities2\xe5\x03\n" +
	"\x11PublicDBIPService\x12e\n" +
	"\x06IPInfo\x12,.misc.dbip.v1.PublicDBIPServiceIPInfoRequest\x1a-.misc.dbip.v1.PublicDBIPServiceIPInfoResponse\x12}\n" +
	"\x0eFetchCountries\x124.misc.dbip.v1.PublicDBIPServiceFetchCountriesRequest\x1a5.misc.dbip.v1.PublicDBIPServiceFetchCountriesResponse\x12t\n" +
	"\vFetchStates\x121.misc.dbip.v1.PublicDBIPServiceFetchStatesRequest\x1a2.misc.dbip.v1.PublicDBIPServiceFetchStatesResponse\x12t\n" +
	"\vFetchCities\x121.misc.dbip.v1.PublicDBIPServiceFetchCitiesRequest\x1a2.misc.dbip.v1.PublicDBIPServiceFetchCitiesResponseBDZBgit.tmproxy-infra.com/algo/algoproxy-proto/gen/misc/dbip/v1;dbipv1b\x06proto3"

var (
	file_misc_dbip_v1_public_proto_rawDescOnce sync.Once
	file_misc_dbip_v1_public_proto_rawDescData []byte
)

func file_misc_dbip_v1_public_proto_rawDescGZIP() []byte {
	file_misc_dbip_v1_public_proto_rawDescOnce.Do(func() {
		file_misc_dbip_v1_public_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_misc_dbip_v1_public_proto_rawDesc), len(file_misc_dbip_v1_public_proto_rawDesc)))
	})
	return file_misc_dbip_v1_public_proto_rawDescData
}

var file_misc_dbip_v1_public_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_misc_dbip_v1_public_proto_goTypes = []any{
	(*PublicDBIPServiceIPInfoRequest)(nil),          // 0: misc.dbip.v1.PublicDBIPServiceIPInfoRequest
	(*PublicDBIPServiceIPInfoResponse)(nil),         // 1: misc.dbip.v1.PublicDBIPServiceIPInfoResponse
	(*IPInfo)(nil),                                  // 2: misc.dbip.v1.IPInfo
	(*NamesRecord)(nil),                             // 3: misc.dbip.v1.NamesRecord
	(*GeoLocation)(nil),                             // 4: misc.dbip.v1.GeoLocation
	(*ConnectionInfo)(nil),                          // 5: misc.dbip.v1.ConnectionInfo
	(*PublicDBIPServiceFetchCountriesRequest)(nil),  // 6: misc.dbip.v1.PublicDBIPServiceFetchCountriesRequest
	(*PublicDBIPServiceFetchCountriesResponse)(nil), // 7: misc.dbip.v1.PublicDBIPServiceFetchCountriesResponse
	(*PublicDBIPServiceFetchStatesRequest)(nil),     // 8: misc.dbip.v1.PublicDBIPServiceFetchStatesRequest
	(*PublicDBIPServiceFetchStatesResponse)(nil),    // 9: misc.dbip.v1.PublicDBIPServiceFetchStatesResponse
	(*PublicDBIPServiceFetchCitiesRequest)(nil),     // 10: misc.dbip.v1.PublicDBIPServiceFetchCitiesRequest
	(*PublicDBIPServiceFetchCitiesResponse)(nil),    // 11: misc.dbip.v1.PublicDBIPServiceFetchCitiesResponse
	(*v1.ErrorMessage)(nil),                         // 12: errmsg.v1.ErrorMessage
	(*v11.PaginationRequest)(nil),                   // 13: utils.v1.PaginationRequest
	(*v11.PaginationResponse)(nil),                  // 14: utils.v1.PaginationResponse
	(*v12.Country)(nil),                             // 15: misc.paymentaddress.v1.Country
	(*v12.State)(nil),                               // 16: misc.paymentaddress.v1.State
	(*v12.City)(nil),                                // 17: misc.paymentaddress.v1.City
}
var file_misc_dbip_v1_public_proto_depIdxs = []int32{
	12, // 0: misc.dbip.v1.PublicDBIPServiceIPInfoResponse.error:type_name -> errmsg.v1.ErrorMessage
	2,  // 1: misc.dbip.v1.PublicDBIPServiceIPInfoResponse.ip_info:type_name -> misc.dbip.v1.IPInfo
	3,  // 2: misc.dbip.v1.IPInfo.region:type_name -> misc.dbip.v1.NamesRecord
	3,  // 3: misc.dbip.v1.IPInfo.country:type_name -> misc.dbip.v1.NamesRecord
	3,  // 4: misc.dbip.v1.IPInfo.state:type_name -> misc.dbip.v1.NamesRecord
	3,  // 5: misc.dbip.v1.IPInfo.city:type_name -> misc.dbip.v1.NamesRecord
	4,  // 6: misc.dbip.v1.IPInfo.geo_location:type_name -> misc.dbip.v1.GeoLocation
	5,  // 7: misc.dbip.v1.IPInfo.connection_info:type_name -> misc.dbip.v1.ConnectionInfo
	13, // 8: misc.dbip.v1.PublicDBIPServiceFetchCountriesRequest.pagination:type_name -> utils.v1.PaginationRequest
	12, // 9: misc.dbip.v1.PublicDBIPServiceFetchCountriesResponse.error:type_name -> errmsg.v1.ErrorMessage
	14, // 10: misc.dbip.v1.PublicDBIPServiceFetchCountriesResponse.pagination:type_name -> utils.v1.PaginationResponse
	15, // 11: misc.dbip.v1.PublicDBIPServiceFetchCountriesResponse.countries:type_name -> misc.paymentaddress.v1.Country
	13, // 12: misc.dbip.v1.PublicDBIPServiceFetchStatesRequest.pagination:type_name -> utils.v1.PaginationRequest
	12, // 13: misc.dbip.v1.PublicDBIPServiceFetchStatesResponse.error:type_name -> errmsg.v1.ErrorMessage
	14, // 14: misc.dbip.v1.PublicDBIPServiceFetchStatesResponse.pagination:type_name -> utils.v1.PaginationResponse
	16, // 15: misc.dbip.v1.PublicDBIPServiceFetchStatesResponse.states:type_name -> misc.paymentaddress.v1.State
	13, // 16: misc.dbip.v1.PublicDBIPServiceFetchCitiesRequest.pagination:type_name -> utils.v1.PaginationRequest
	12, // 17: misc.dbip.v1.PublicDBIPServiceFetchCitiesResponse.error:type_name -> errmsg.v1.ErrorMessage
	14, // 18: misc.dbip.v1.PublicDBIPServiceFetchCitiesResponse.pagination:type_name -> utils.v1.PaginationResponse
	17, // 19: misc.dbip.v1.PublicDBIPServiceFetchCitiesResponse.cities:type_name -> misc.paymentaddress.v1.City
	0,  // 20: misc.dbip.v1.PublicDBIPService.IPInfo:input_type -> misc.dbip.v1.PublicDBIPServiceIPInfoRequest
	6,  // 21: misc.dbip.v1.PublicDBIPService.FetchCountries:input_type -> misc.dbip.v1.PublicDBIPServiceFetchCountriesRequest
	8,  // 22: misc.dbip.v1.PublicDBIPService.FetchStates:input_type -> misc.dbip.v1.PublicDBIPServiceFetchStatesRequest
	10, // 23: misc.dbip.v1.PublicDBIPService.FetchCities:input_type -> misc.dbip.v1.PublicDBIPServiceFetchCitiesRequest
	1,  // 24: misc.dbip.v1.PublicDBIPService.IPInfo:output_type -> misc.dbip.v1.PublicDBIPServiceIPInfoResponse
	7,  // 25: misc.dbip.v1.PublicDBIPService.FetchCountries:output_type -> misc.dbip.v1.PublicDBIPServiceFetchCountriesResponse
	9,  // 26: misc.dbip.v1.PublicDBIPService.FetchStates:output_type -> misc.dbip.v1.PublicDBIPServiceFetchStatesResponse
	11, // 27: misc.dbip.v1.PublicDBIPService.FetchCities:output_type -> misc.dbip.v1.PublicDBIPServiceFetchCitiesResponse
	24, // [24:28] is the sub-list for method output_type
	20, // [20:24] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_misc_dbip_v1_public_proto_init() }
func file_misc_dbip_v1_public_proto_init() {
	if File_misc_dbip_v1_public_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_misc_dbip_v1_public_proto_rawDesc), len(file_misc_dbip_v1_public_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_misc_dbip_v1_public_proto_goTypes,
		DependencyIndexes: file_misc_dbip_v1_public_proto_depIdxs,
		MessageInfos:      file_misc_dbip_v1_public_proto_msgTypes,
	}.Build()
	File_misc_dbip_v1_public_proto = out.File
	file_misc_dbip_v1_public_proto_goTypes = nil
	file_misc_dbip_v1_public_proto_depIdxs = nil
}
