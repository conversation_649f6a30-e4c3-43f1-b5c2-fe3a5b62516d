// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: misc/dbip/v1/public.proto

package dbipv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/misc/dbip/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// PublicDBIPServiceName is the fully-qualified name of the PublicDBIPService service.
	PublicDBIPServiceName = "misc.dbip.v1.PublicDBIPService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// PublicDBIPServiceIPInfoProcedure is the fully-qualified name of the PublicDBIPService's IPInfo
	// RPC.
	PublicDBIPServiceIPInfoProcedure = "/misc.dbip.v1.PublicDBIPService/IPInfo"
	// PublicDBIPServiceFetchCountriesProcedure is the fully-qualified name of the PublicDBIPService's
	// FetchCountries RPC.
	PublicDBIPServiceFetchCountriesProcedure = "/misc.dbip.v1.PublicDBIPService/FetchCountries"
	// PublicDBIPServiceFetchStatesProcedure is the fully-qualified name of the PublicDBIPService's
	// FetchStates RPC.
	PublicDBIPServiceFetchStatesProcedure = "/misc.dbip.v1.PublicDBIPService/FetchStates"
	// PublicDBIPServiceFetchCitiesProcedure is the fully-qualified name of the PublicDBIPService's
	// FetchCities RPC.
	PublicDBIPServiceFetchCitiesProcedure = "/misc.dbip.v1.PublicDBIPService/FetchCities"
)

// PublicDBIPServiceClient is a client for the misc.dbip.v1.PublicDBIPService service.
type PublicDBIPServiceClient interface {
	IPInfo(context.Context, *connect.Request[v1.PublicDBIPServiceIPInfoRequest]) (*connect.Response[v1.PublicDBIPServiceIPInfoResponse], error)
	FetchCountries(context.Context, *connect.Request[v1.PublicDBIPServiceFetchCountriesRequest]) (*connect.Response[v1.PublicDBIPServiceFetchCountriesResponse], error)
	FetchStates(context.Context, *connect.Request[v1.PublicDBIPServiceFetchStatesRequest]) (*connect.Response[v1.PublicDBIPServiceFetchStatesResponse], error)
	FetchCities(context.Context, *connect.Request[v1.PublicDBIPServiceFetchCitiesRequest]) (*connect.Response[v1.PublicDBIPServiceFetchCitiesResponse], error)
}

// NewPublicDBIPServiceClient constructs a client for the misc.dbip.v1.PublicDBIPService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewPublicDBIPServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) PublicDBIPServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	publicDBIPServiceMethods := v1.File_misc_dbip_v1_public_proto.Services().ByName("PublicDBIPService").Methods()
	return &publicDBIPServiceClient{
		iPInfo: connect.NewClient[v1.PublicDBIPServiceIPInfoRequest, v1.PublicDBIPServiceIPInfoResponse](
			httpClient,
			baseURL+PublicDBIPServiceIPInfoProcedure,
			connect.WithSchema(publicDBIPServiceMethods.ByName("IPInfo")),
			connect.WithClientOptions(opts...),
		),
		fetchCountries: connect.NewClient[v1.PublicDBIPServiceFetchCountriesRequest, v1.PublicDBIPServiceFetchCountriesResponse](
			httpClient,
			baseURL+PublicDBIPServiceFetchCountriesProcedure,
			connect.WithSchema(publicDBIPServiceMethods.ByName("FetchCountries")),
			connect.WithClientOptions(opts...),
		),
		fetchStates: connect.NewClient[v1.PublicDBIPServiceFetchStatesRequest, v1.PublicDBIPServiceFetchStatesResponse](
			httpClient,
			baseURL+PublicDBIPServiceFetchStatesProcedure,
			connect.WithSchema(publicDBIPServiceMethods.ByName("FetchStates")),
			connect.WithClientOptions(opts...),
		),
		fetchCities: connect.NewClient[v1.PublicDBIPServiceFetchCitiesRequest, v1.PublicDBIPServiceFetchCitiesResponse](
			httpClient,
			baseURL+PublicDBIPServiceFetchCitiesProcedure,
			connect.WithSchema(publicDBIPServiceMethods.ByName("FetchCities")),
			connect.WithClientOptions(opts...),
		),
	}
}

// publicDBIPServiceClient implements PublicDBIPServiceClient.
type publicDBIPServiceClient struct {
	iPInfo         *connect.Client[v1.PublicDBIPServiceIPInfoRequest, v1.PublicDBIPServiceIPInfoResponse]
	fetchCountries *connect.Client[v1.PublicDBIPServiceFetchCountriesRequest, v1.PublicDBIPServiceFetchCountriesResponse]
	fetchStates    *connect.Client[v1.PublicDBIPServiceFetchStatesRequest, v1.PublicDBIPServiceFetchStatesResponse]
	fetchCities    *connect.Client[v1.PublicDBIPServiceFetchCitiesRequest, v1.PublicDBIPServiceFetchCitiesResponse]
}

// IPInfo calls misc.dbip.v1.PublicDBIPService.IPInfo.
func (c *publicDBIPServiceClient) IPInfo(ctx context.Context, req *connect.Request[v1.PublicDBIPServiceIPInfoRequest]) (*connect.Response[v1.PublicDBIPServiceIPInfoResponse], error) {
	return c.iPInfo.CallUnary(ctx, req)
}

// FetchCountries calls misc.dbip.v1.PublicDBIPService.FetchCountries.
func (c *publicDBIPServiceClient) FetchCountries(ctx context.Context, req *connect.Request[v1.PublicDBIPServiceFetchCountriesRequest]) (*connect.Response[v1.PublicDBIPServiceFetchCountriesResponse], error) {
	return c.fetchCountries.CallUnary(ctx, req)
}

// FetchStates calls misc.dbip.v1.PublicDBIPService.FetchStates.
func (c *publicDBIPServiceClient) FetchStates(ctx context.Context, req *connect.Request[v1.PublicDBIPServiceFetchStatesRequest]) (*connect.Response[v1.PublicDBIPServiceFetchStatesResponse], error) {
	return c.fetchStates.CallUnary(ctx, req)
}

// FetchCities calls misc.dbip.v1.PublicDBIPService.FetchCities.
func (c *publicDBIPServiceClient) FetchCities(ctx context.Context, req *connect.Request[v1.PublicDBIPServiceFetchCitiesRequest]) (*connect.Response[v1.PublicDBIPServiceFetchCitiesResponse], error) {
	return c.fetchCities.CallUnary(ctx, req)
}

// PublicDBIPServiceHandler is an implementation of the misc.dbip.v1.PublicDBIPService service.
type PublicDBIPServiceHandler interface {
	IPInfo(context.Context, *connect.Request[v1.PublicDBIPServiceIPInfoRequest]) (*connect.Response[v1.PublicDBIPServiceIPInfoResponse], error)
	FetchCountries(context.Context, *connect.Request[v1.PublicDBIPServiceFetchCountriesRequest]) (*connect.Response[v1.PublicDBIPServiceFetchCountriesResponse], error)
	FetchStates(context.Context, *connect.Request[v1.PublicDBIPServiceFetchStatesRequest]) (*connect.Response[v1.PublicDBIPServiceFetchStatesResponse], error)
	FetchCities(context.Context, *connect.Request[v1.PublicDBIPServiceFetchCitiesRequest]) (*connect.Response[v1.PublicDBIPServiceFetchCitiesResponse], error)
}

// NewPublicDBIPServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewPublicDBIPServiceHandler(svc PublicDBIPServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	publicDBIPServiceMethods := v1.File_misc_dbip_v1_public_proto.Services().ByName("PublicDBIPService").Methods()
	publicDBIPServiceIPInfoHandler := connect.NewUnaryHandler(
		PublicDBIPServiceIPInfoProcedure,
		svc.IPInfo,
		connect.WithSchema(publicDBIPServiceMethods.ByName("IPInfo")),
		connect.WithHandlerOptions(opts...),
	)
	publicDBIPServiceFetchCountriesHandler := connect.NewUnaryHandler(
		PublicDBIPServiceFetchCountriesProcedure,
		svc.FetchCountries,
		connect.WithSchema(publicDBIPServiceMethods.ByName("FetchCountries")),
		connect.WithHandlerOptions(opts...),
	)
	publicDBIPServiceFetchStatesHandler := connect.NewUnaryHandler(
		PublicDBIPServiceFetchStatesProcedure,
		svc.FetchStates,
		connect.WithSchema(publicDBIPServiceMethods.ByName("FetchStates")),
		connect.WithHandlerOptions(opts...),
	)
	publicDBIPServiceFetchCitiesHandler := connect.NewUnaryHandler(
		PublicDBIPServiceFetchCitiesProcedure,
		svc.FetchCities,
		connect.WithSchema(publicDBIPServiceMethods.ByName("FetchCities")),
		connect.WithHandlerOptions(opts...),
	)
	return "/misc.dbip.v1.PublicDBIPService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case PublicDBIPServiceIPInfoProcedure:
			publicDBIPServiceIPInfoHandler.ServeHTTP(w, r)
		case PublicDBIPServiceFetchCountriesProcedure:
			publicDBIPServiceFetchCountriesHandler.ServeHTTP(w, r)
		case PublicDBIPServiceFetchStatesProcedure:
			publicDBIPServiceFetchStatesHandler.ServeHTTP(w, r)
		case PublicDBIPServiceFetchCitiesProcedure:
			publicDBIPServiceFetchCitiesHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedPublicDBIPServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedPublicDBIPServiceHandler struct{}

func (UnimplementedPublicDBIPServiceHandler) IPInfo(context.Context, *connect.Request[v1.PublicDBIPServiceIPInfoRequest]) (*connect.Response[v1.PublicDBIPServiceIPInfoResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("misc.dbip.v1.PublicDBIPService.IPInfo is not implemented"))
}

func (UnimplementedPublicDBIPServiceHandler) FetchCountries(context.Context, *connect.Request[v1.PublicDBIPServiceFetchCountriesRequest]) (*connect.Response[v1.PublicDBIPServiceFetchCountriesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("misc.dbip.v1.PublicDBIPService.FetchCountries is not implemented"))
}

func (UnimplementedPublicDBIPServiceHandler) FetchStates(context.Context, *connect.Request[v1.PublicDBIPServiceFetchStatesRequest]) (*connect.Response[v1.PublicDBIPServiceFetchStatesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("misc.dbip.v1.PublicDBIPService.FetchStates is not implemented"))
}

func (UnimplementedPublicDBIPServiceHandler) FetchCities(context.Context, *connect.Request[v1.PublicDBIPServiceFetchCitiesRequest]) (*connect.Response[v1.PublicDBIPServiceFetchCitiesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("misc.dbip.v1.PublicDBIPService.FetchCities is not implemented"))
}
