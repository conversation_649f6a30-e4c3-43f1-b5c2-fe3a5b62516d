// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: hop/v1/hop.proto

package hopv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Hop struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	IdSession         string                 `protobuf:"bytes,1,opt,name=id_session,json=idSession,proto3" json:"id_session,omitempty"`
	LocalPort         uint64                 `protobuf:"varint,2,opt,name=local_port,json=localPort,proto3" json:"local_port,omitempty"`
	Username          string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Password          string                 `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	IpAllow           []string               `protobuf:"bytes,5,rep,name=ip_allow,json=ipAllow,proto3" json:"ip_allow,omitempty"`
	NextHopS5Ip       string                 `protobuf:"bytes,6,opt,name=next_hop_s5_ip,json=nextHopS5Ip,proto3" json:"next_hop_s5_ip,omitempty"`
	NextHopS5Port     uint64                 `protobuf:"varint,7,opt,name=next_hop_s5_port,json=nextHopS5Port,proto3" json:"next_hop_s5_port,omitempty"`
	NextHopS5Username string                 `protobuf:"bytes,8,opt,name=next_hop_s5_username,json=nextHopS5Username,proto3" json:"next_hop_s5_username,omitempty"`
	NextHopS5Password string                 `protobuf:"bytes,9,opt,name=next_hop_s5_password,json=nextHopS5Password,proto3" json:"next_hop_s5_password,omitempty"`
	PublicIp          string                 `protobuf:"bytes,10,opt,name=public_ip,json=publicIp,proto3" json:"public_ip,omitempty"`
	Dns1              string                 `protobuf:"bytes,11,opt,name=dns1,proto3" json:"dns1,omitempty"`
	Dns2              string                 `protobuf:"bytes,12,opt,name=dns2,proto3" json:"dns2,omitempty"`
	IpProxy           string                 `protobuf:"bytes,13,opt,name=ip_proxy,json=ipProxy,proto3" json:"ip_proxy,omitempty"`
	Bandwidth         uint64                 `protobuf:"varint,14,opt,name=bandwidth,proto3" json:"bandwidth,omitempty"`
	ExpiredAt         uint64                 `protobuf:"varint,15,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	IsActive          bool                   `protobuf:"varint,16,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Hop) Reset() {
	*x = Hop{}
	mi := &file_hop_v1_hop_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Hop) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Hop) ProtoMessage() {}

func (x *Hop) ProtoReflect() protoreflect.Message {
	mi := &file_hop_v1_hop_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Hop.ProtoReflect.Descriptor instead.
func (*Hop) Descriptor() ([]byte, []int) {
	return file_hop_v1_hop_proto_rawDescGZIP(), []int{0}
}

func (x *Hop) GetIdSession() string {
	if x != nil {
		return x.IdSession
	}
	return ""
}

func (x *Hop) GetLocalPort() uint64 {
	if x != nil {
		return x.LocalPort
	}
	return 0
}

func (x *Hop) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *Hop) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Hop) GetIpAllow() []string {
	if x != nil {
		return x.IpAllow
	}
	return nil
}

func (x *Hop) GetNextHopS5Ip() string {
	if x != nil {
		return x.NextHopS5Ip
	}
	return ""
}

func (x *Hop) GetNextHopS5Port() uint64 {
	if x != nil {
		return x.NextHopS5Port
	}
	return 0
}

func (x *Hop) GetNextHopS5Username() string {
	if x != nil {
		return x.NextHopS5Username
	}
	return ""
}

func (x *Hop) GetNextHopS5Password() string {
	if x != nil {
		return x.NextHopS5Password
	}
	return ""
}

func (x *Hop) GetPublicIp() string {
	if x != nil {
		return x.PublicIp
	}
	return ""
}

func (x *Hop) GetDns1() string {
	if x != nil {
		return x.Dns1
	}
	return ""
}

func (x *Hop) GetDns2() string {
	if x != nil {
		return x.Dns2
	}
	return ""
}

func (x *Hop) GetIpProxy() string {
	if x != nil {
		return x.IpProxy
	}
	return ""
}

func (x *Hop) GetBandwidth() uint64 {
	if x != nil {
		return x.Bandwidth
	}
	return 0
}

func (x *Hop) GetExpiredAt() uint64 {
	if x != nil {
		return x.ExpiredAt
	}
	return 0
}

func (x *Hop) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

var File_hop_v1_hop_proto protoreflect.FileDescriptor

const file_hop_v1_hop_proto_rawDesc = "" +
	"\n" +
	"\x10hop/v1/hop.proto\x12\x06hop.v1\"\x80\x04\n" +
	"\x03Hop\x12\x1d\n" +
	"\n" +
	"id_session\x18\x01 \x01(\tR\tidSession\x12\x1d\n" +
	"\n" +
	"local_port\x18\x02 \x01(\x04R\tlocalPort\x12\x1a\n" +
	"\busername\x18\x03 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x04 \x01(\tR\bpassword\x12\x19\n" +
	"\bip_allow\x18\x05 \x03(\tR\aipAllow\x12#\n" +
	"\x0enext_hop_s5_ip\x18\x06 \x01(\tR\vnextHopS5Ip\x12'\n" +
	"\x10next_hop_s5_port\x18\a \x01(\x04R\rnextHopS5Port\x12/\n" +
	"\x14next_hop_s5_username\x18\b \x01(\tR\x11nextHopS5Username\x12/\n" +
	"\x14next_hop_s5_password\x18\t \x01(\tR\x11nextHopS5Password\x12\x1b\n" +
	"\tpublic_ip\x18\n" +
	" \x01(\tR\bpublicIp\x12\x12\n" +
	"\x04dns1\x18\v \x01(\tR\x04dns1\x12\x12\n" +
	"\x04dns2\x18\f \x01(\tR\x04dns2\x12\x19\n" +
	"\bip_proxy\x18\r \x01(\tR\aipProxy\x12\x1c\n" +
	"\tbandwidth\x18\x0e \x01(\x04R\tbandwidth\x12\x1d\n" +
	"\n" +
	"expired_at\x18\x0f \x01(\x04R\texpiredAt\x12\x1b\n" +
	"\tis_active\x18\x10 \x01(\bR\bisActiveB=Z;git.tmproxy-infra.com/algo/algoproxy-proto/gen/hop/v1;hopv1b\x06proto3"

var (
	file_hop_v1_hop_proto_rawDescOnce sync.Once
	file_hop_v1_hop_proto_rawDescData []byte
)

func file_hop_v1_hop_proto_rawDescGZIP() []byte {
	file_hop_v1_hop_proto_rawDescOnce.Do(func() {
		file_hop_v1_hop_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_hop_v1_hop_proto_rawDesc), len(file_hop_v1_hop_proto_rawDesc)))
	})
	return file_hop_v1_hop_proto_rawDescData
}

var file_hop_v1_hop_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_hop_v1_hop_proto_goTypes = []any{
	(*Hop)(nil), // 0: hop.v1.Hop
}
var file_hop_v1_hop_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_hop_v1_hop_proto_init() }
func file_hop_v1_hop_proto_init() {
	if File_hop_v1_hop_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_hop_v1_hop_proto_rawDesc), len(file_hop_v1_hop_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_hop_v1_hop_proto_goTypes,
		DependencyIndexes: file_hop_v1_hop_proto_depIdxs,
		MessageInfos:      file_hop_v1_hop_proto_msgTypes,
	}.Build()
	File_hop_v1_hop_proto = out.File
	file_hop_v1_hop_proto_goTypes = nil
	file_hop_v1_hop_proto_depIdxs = nil
}
