// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: algoenum/v1/app_country.proto

package algoenumv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AppCountry int32

const (
	AppCountry_APP_COUNTRY_UNSPECIFIED AppCountry = 0
	AppCountry_APP_COUNTRY_VN          AppCountry = 1
	AppCountry_APP_COUNTRY_WW          AppCountry = 2
)

// Enum value maps for AppCountry.
var (
	AppCountry_name = map[int32]string{
		0: "APP_COUNTRY_UNSPECIFIED",
		1: "APP_COUNTRY_VN",
		2: "APP_COUNTRY_WW",
	}
	AppCountry_value = map[string]int32{
		"APP_COUNTRY_UNSPECIFIED": 0,
		"APP_COUNTRY_VN":          1,
		"APP_COUNTRY_WW":          2,
	}
)

func (x AppCountry) Enum() *AppCountry {
	p := new(AppCountry)
	*p = x
	return p
}

func (x AppCountry) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AppCountry) Descriptor() protoreflect.EnumDescriptor {
	return file_algoenum_v1_app_country_proto_enumTypes[0].Descriptor()
}

func (AppCountry) Type() protoreflect.EnumType {
	return &file_algoenum_v1_app_country_proto_enumTypes[0]
}

func (x AppCountry) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AppCountry.Descriptor instead.
func (AppCountry) EnumDescriptor() ([]byte, []int) {
	return file_algoenum_v1_app_country_proto_rawDescGZIP(), []int{0}
}

var File_algoenum_v1_app_country_proto protoreflect.FileDescriptor

const file_algoenum_v1_app_country_proto_rawDesc = "" +
	"\n" +
	"\x1dalgoenum/v1/app_country.proto\x12\valgoenum.v1*Q\n" +
	"\n" +
	"AppCountry\x12\x1b\n" +
	"\x17APP_COUNTRY_UNSPECIFIED\x10\x00\x12\x12\n" +
	"\x0eAPP_COUNTRY_VN\x10\x01\x12\x12\n" +
	"\x0eAPP_COUNTRY_WW\x10\x02BGZEgit.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1;algoenumv1b\x06proto3"

var (
	file_algoenum_v1_app_country_proto_rawDescOnce sync.Once
	file_algoenum_v1_app_country_proto_rawDescData []byte
)

func file_algoenum_v1_app_country_proto_rawDescGZIP() []byte {
	file_algoenum_v1_app_country_proto_rawDescOnce.Do(func() {
		file_algoenum_v1_app_country_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_algoenum_v1_app_country_proto_rawDesc), len(file_algoenum_v1_app_country_proto_rawDesc)))
	})
	return file_algoenum_v1_app_country_proto_rawDescData
}

var file_algoenum_v1_app_country_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_algoenum_v1_app_country_proto_goTypes = []any{
	(AppCountry)(0), // 0: algoenum.v1.AppCountry
}
var file_algoenum_v1_app_country_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_algoenum_v1_app_country_proto_init() }
func file_algoenum_v1_app_country_proto_init() {
	if File_algoenum_v1_app_country_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_algoenum_v1_app_country_proto_rawDesc), len(file_algoenum_v1_app_country_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_algoenum_v1_app_country_proto_goTypes,
		DependencyIndexes: file_algoenum_v1_app_country_proto_depIdxs,
		EnumInfos:         file_algoenum_v1_app_country_proto_enumTypes,
	}.Build()
	File_algoenum_v1_app_country_proto = out.File
	file_algoenum_v1_app_country_proto_goTypes = nil
	file_algoenum_v1_app_country_proto_depIdxs = nil
}
