// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: algoenum/v1/payment_gateway_type.proto

package algoenumv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PaymentGatewayType int32

const (
	PaymentGatewayType_PAYMENT_GATEWAY_TYPE_UNSPECIFIED PaymentGatewayType = 0
	// PAYMENT_GATEWAY_TYPE_VCB_VND = 1;
	// PAYMENT_GATEWAY_TYPE_ACB_VND = 2;
	PaymentGatewayType_PAYMENT_GATEWAY_TYPE_SEPAY_VND      PaymentGatewayType = 3
	PaymentGatewayType_PAYMENT_GATEWAY_TYPE_APPOTA_PAY_VND PaymentGatewayType = 4
	PaymentGatewayType_PAYMENT_GATEWAY_TYPE_ADMIN_VND      PaymentGatewayType = 5
	PaymentGatewayType_PAYMENT_GATEWAY_TYPE_ADMIN_USD      PaymentGatewayType = 6
	PaymentGatewayType_PAYMENT_GATEWAY_TYPE_DODO_PAY_USD   PaymentGatewayType = 7
)

// Enum value maps for PaymentGatewayType.
var (
	PaymentGatewayType_name = map[int32]string{
		0: "PAYMENT_GATEWAY_TYPE_UNSPECIFIED",
		3: "PAYMENT_GATEWAY_TYPE_SEPAY_VND",
		4: "PAYMENT_GATEWAY_TYPE_APPOTA_PAY_VND",
		5: "PAYMENT_GATEWAY_TYPE_ADMIN_VND",
		6: "PAYMENT_GATEWAY_TYPE_ADMIN_USD",
		7: "PAYMENT_GATEWAY_TYPE_DODO_PAY_USD",
	}
	PaymentGatewayType_value = map[string]int32{
		"PAYMENT_GATEWAY_TYPE_UNSPECIFIED":    0,
		"PAYMENT_GATEWAY_TYPE_SEPAY_VND":      3,
		"PAYMENT_GATEWAY_TYPE_APPOTA_PAY_VND": 4,
		"PAYMENT_GATEWAY_TYPE_ADMIN_VND":      5,
		"PAYMENT_GATEWAY_TYPE_ADMIN_USD":      6,
		"PAYMENT_GATEWAY_TYPE_DODO_PAY_USD":   7,
	}
)

func (x PaymentGatewayType) Enum() *PaymentGatewayType {
	p := new(PaymentGatewayType)
	*p = x
	return p
}

func (x PaymentGatewayType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentGatewayType) Descriptor() protoreflect.EnumDescriptor {
	return file_algoenum_v1_payment_gateway_type_proto_enumTypes[0].Descriptor()
}

func (PaymentGatewayType) Type() protoreflect.EnumType {
	return &file_algoenum_v1_payment_gateway_type_proto_enumTypes[0]
}

func (x PaymentGatewayType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentGatewayType.Descriptor instead.
func (PaymentGatewayType) EnumDescriptor() ([]byte, []int) {
	return file_algoenum_v1_payment_gateway_type_proto_rawDescGZIP(), []int{0}
}

var File_algoenum_v1_payment_gateway_type_proto protoreflect.FileDescriptor

const file_algoenum_v1_payment_gateway_type_proto_rawDesc = "" +
	"\n" +
	"&algoenum/v1/payment_gateway_type.proto\x12\valgoenum.v1*\xf6\x01\n" +
	"\x12PaymentGatewayType\x12$\n" +
	" PAYMENT_GATEWAY_TYPE_UNSPECIFIED\x10\x00\x12\"\n" +
	"\x1ePAYMENT_GATEWAY_TYPE_SEPAY_VND\x10\x03\x12'\n" +
	"#PAYMENT_GATEWAY_TYPE_APPOTA_PAY_VND\x10\x04\x12\"\n" +
	"\x1ePAYMENT_GATEWAY_TYPE_ADMIN_VND\x10\x05\x12\"\n" +
	"\x1ePAYMENT_GATEWAY_TYPE_ADMIN_USD\x10\x06\x12%\n" +
	"!PAYMENT_GATEWAY_TYPE_DODO_PAY_USD\x10\aBGZEgit.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1;algoenumv1b\x06proto3"

var (
	file_algoenum_v1_payment_gateway_type_proto_rawDescOnce sync.Once
	file_algoenum_v1_payment_gateway_type_proto_rawDescData []byte
)

func file_algoenum_v1_payment_gateway_type_proto_rawDescGZIP() []byte {
	file_algoenum_v1_payment_gateway_type_proto_rawDescOnce.Do(func() {
		file_algoenum_v1_payment_gateway_type_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_algoenum_v1_payment_gateway_type_proto_rawDesc), len(file_algoenum_v1_payment_gateway_type_proto_rawDesc)))
	})
	return file_algoenum_v1_payment_gateway_type_proto_rawDescData
}

var file_algoenum_v1_payment_gateway_type_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_algoenum_v1_payment_gateway_type_proto_goTypes = []any{
	(PaymentGatewayType)(0), // 0: algoenum.v1.PaymentGatewayType
}
var file_algoenum_v1_payment_gateway_type_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_algoenum_v1_payment_gateway_type_proto_init() }
func file_algoenum_v1_payment_gateway_type_proto_init() {
	if File_algoenum_v1_payment_gateway_type_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_algoenum_v1_payment_gateway_type_proto_rawDesc), len(file_algoenum_v1_payment_gateway_type_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_algoenum_v1_payment_gateway_type_proto_goTypes,
		DependencyIndexes: file_algoenum_v1_payment_gateway_type_proto_depIdxs,
		EnumInfos:         file_algoenum_v1_payment_gateway_type_proto_enumTypes,
	}.Build()
	File_algoenum_v1_payment_gateway_type_proto = out.File
	file_algoenum_v1_payment_gateway_type_proto_goTypes = nil
	file_algoenum_v1_payment_gateway_type_proto_depIdxs = nil
}
