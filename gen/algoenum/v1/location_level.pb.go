// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: algoenum/v1/location_level.proto

package algoenumv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LocationLevel int32

const (
	LocationLevel_LOCATION_LEVEL_UNSPECIFIED LocationLevel = 0
	LocationLevel_LOCATION_LEVEL_WORLD       LocationLevel = 1
	LocationLevel_LOCATION_LEVEL_REGION      LocationLevel = 2
	LocationLevel_LOCATION_LEVEL_COUNTRY     LocationLevel = 3
	LocationLevel_LOCATION_LEVEL_STATE       LocationLevel = 4
	LocationLevel_LOCATION_LEVEL_CITY        LocationLevel = 5
)

// Enum value maps for LocationLevel.
var (
	LocationLevel_name = map[int32]string{
		0: "LOCATION_LEVEL_UNSPECIFIED",
		1: "LOCATION_LEVEL_WORLD",
		2: "LOCATION_LEVEL_REGION",
		3: "LOCATION_LEVEL_COUNTRY",
		4: "LOCATION_LEVEL_STATE",
		5: "LOCATION_LEVEL_CITY",
	}
	LocationLevel_value = map[string]int32{
		"LOCATION_LEVEL_UNSPECIFIED": 0,
		"LOCATION_LEVEL_WORLD":       1,
		"LOCATION_LEVEL_REGION":      2,
		"LOCATION_LEVEL_COUNTRY":     3,
		"LOCATION_LEVEL_STATE":       4,
		"LOCATION_LEVEL_CITY":        5,
	}
)

func (x LocationLevel) Enum() *LocationLevel {
	p := new(LocationLevel)
	*p = x
	return p
}

func (x LocationLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LocationLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_algoenum_v1_location_level_proto_enumTypes[0].Descriptor()
}

func (LocationLevel) Type() protoreflect.EnumType {
	return &file_algoenum_v1_location_level_proto_enumTypes[0]
}

func (x LocationLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LocationLevel.Descriptor instead.
func (LocationLevel) EnumDescriptor() ([]byte, []int) {
	return file_algoenum_v1_location_level_proto_rawDescGZIP(), []int{0}
}

var File_algoenum_v1_location_level_proto protoreflect.FileDescriptor

const file_algoenum_v1_location_level_proto_rawDesc = "" +
	"\n" +
	" algoenum/v1/location_level.proto\x12\valgoenum.v1*\xb3\x01\n" +
	"\rLocationLevel\x12\x1e\n" +
	"\x1aLOCATION_LEVEL_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14LOCATION_LEVEL_WORLD\x10\x01\x12\x19\n" +
	"\x15LOCATION_LEVEL_REGION\x10\x02\x12\x1a\n" +
	"\x16LOCATION_LEVEL_COUNTRY\x10\x03\x12\x18\n" +
	"\x14LOCATION_LEVEL_STATE\x10\x04\x12\x17\n" +
	"\x13LOCATION_LEVEL_CITY\x10\x05BGZEgit.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1;algoenumv1b\x06proto3"

var (
	file_algoenum_v1_location_level_proto_rawDescOnce sync.Once
	file_algoenum_v1_location_level_proto_rawDescData []byte
)

func file_algoenum_v1_location_level_proto_rawDescGZIP() []byte {
	file_algoenum_v1_location_level_proto_rawDescOnce.Do(func() {
		file_algoenum_v1_location_level_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_algoenum_v1_location_level_proto_rawDesc), len(file_algoenum_v1_location_level_proto_rawDesc)))
	})
	return file_algoenum_v1_location_level_proto_rawDescData
}

var file_algoenum_v1_location_level_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_algoenum_v1_location_level_proto_goTypes = []any{
	(LocationLevel)(0), // 0: algoenum.v1.LocationLevel
}
var file_algoenum_v1_location_level_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_algoenum_v1_location_level_proto_init() }
func file_algoenum_v1_location_level_proto_init() {
	if File_algoenum_v1_location_level_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_algoenum_v1_location_level_proto_rawDesc), len(file_algoenum_v1_location_level_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_algoenum_v1_location_level_proto_goTypes,
		DependencyIndexes: file_algoenum_v1_location_level_proto_depIdxs,
		EnumInfos:         file_algoenum_v1_location_level_proto_enumTypes,
	}.Build()
	File_algoenum_v1_location_level_proto = out.File
	file_algoenum_v1_location_level_proto_goTypes = nil
	file_algoenum_v1_location_level_proto_depIdxs = nil
}
