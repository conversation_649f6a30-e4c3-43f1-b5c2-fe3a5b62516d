// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: algoenum/v1/currency.proto

package algoenumv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Currency int32

const (
	Currency_CURRENCY_UNSPECIFIED Currency = 0
	Currency_CURRENCY_USD         Currency = 1
	Currency_CURRENCY_VND         Currency = 2
	Currency_CURRENCY_THB         Currency = 3
	Currency_CURRENCY_IDR         Currency = 4
)

// Enum value maps for Currency.
var (
	Currency_name = map[int32]string{
		0: "CURRENCY_UNSPECIFIED",
		1: "CURRENCY_USD",
		2: "CURRENCY_VND",
		3: "CURRENCY_THB",
		4: "CURRENCY_IDR",
	}
	Currency_value = map[string]int32{
		"CURRENCY_UNSPECIFIED": 0,
		"CURRENCY_USD":         1,
		"CURRENCY_VND":         2,
		"CURRENCY_THB":         3,
		"CURRENCY_IDR":         4,
	}
)

func (x Currency) Enum() *Currency {
	p := new(Currency)
	*p = x
	return p
}

func (x Currency) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Currency) Descriptor() protoreflect.EnumDescriptor {
	return file_algoenum_v1_currency_proto_enumTypes[0].Descriptor()
}

func (Currency) Type() protoreflect.EnumType {
	return &file_algoenum_v1_currency_proto_enumTypes[0]
}

func (x Currency) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Currency.Descriptor instead.
func (Currency) EnumDescriptor() ([]byte, []int) {
	return file_algoenum_v1_currency_proto_rawDescGZIP(), []int{0}
}

var File_algoenum_v1_currency_proto protoreflect.FileDescriptor

const file_algoenum_v1_currency_proto_rawDesc = "" +
	"\n" +
	"\x1aalgoenum/v1/currency.proto\x12\valgoenum.v1*l\n" +
	"\bCurrency\x12\x18\n" +
	"\x14CURRENCY_UNSPECIFIED\x10\x00\x12\x10\n" +
	"\fCURRENCY_USD\x10\x01\x12\x10\n" +
	"\fCURRENCY_VND\x10\x02\x12\x10\n" +
	"\fCURRENCY_THB\x10\x03\x12\x10\n" +
	"\fCURRENCY_IDR\x10\x04BGZEgit.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1;algoenumv1b\x06proto3"

var (
	file_algoenum_v1_currency_proto_rawDescOnce sync.Once
	file_algoenum_v1_currency_proto_rawDescData []byte
)

func file_algoenum_v1_currency_proto_rawDescGZIP() []byte {
	file_algoenum_v1_currency_proto_rawDescOnce.Do(func() {
		file_algoenum_v1_currency_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_algoenum_v1_currency_proto_rawDesc), len(file_algoenum_v1_currency_proto_rawDesc)))
	})
	return file_algoenum_v1_currency_proto_rawDescData
}

var file_algoenum_v1_currency_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_algoenum_v1_currency_proto_goTypes = []any{
	(Currency)(0), // 0: algoenum.v1.Currency
}
var file_algoenum_v1_currency_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_algoenum_v1_currency_proto_init() }
func file_algoenum_v1_currency_proto_init() {
	if File_algoenum_v1_currency_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_algoenum_v1_currency_proto_rawDesc), len(file_algoenum_v1_currency_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_algoenum_v1_currency_proto_goTypes,
		DependencyIndexes: file_algoenum_v1_currency_proto_depIdxs,
		EnumInfos:         file_algoenum_v1_currency_proto_enumTypes,
	}.Build()
	File_algoenum_v1_currency_proto = out.File
	file_algoenum_v1_currency_proto_goTypes = nil
	file_algoenum_v1_currency_proto_depIdxs = nil
}
