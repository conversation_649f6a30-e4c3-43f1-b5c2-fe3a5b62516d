// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: algoenum/v1/vn_bank.proto

package algoenumv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VNBankType int32

const (
	VNBankType_VN_BANK_TYPE_UNSPECIFIED VNBankType = 0
	VNBankType_VN_BANK_TYPE_TP_BANK     VNBankType = 1
	VNBankType_VN_BANK_TYPE_ACB         VNBankType = 2
	VNBankType_VN_BANK_TYPE_BIDV        VNBankType = 3
	VNBankType_VN_BANK_TYPE_VCB         VNBankType = 4
)

// Enum value maps for VNBankType.
var (
	VNBankType_name = map[int32]string{
		0: "VN_BANK_TYPE_UNSPECIFIED",
		1: "VN_BANK_TYPE_TP_BANK",
		2: "VN_BANK_TYPE_ACB",
		3: "VN_BANK_TYPE_BIDV",
		4: "VN_BANK_TYPE_VCB",
	}
	VNBankType_value = map[string]int32{
		"VN_BANK_TYPE_UNSPECIFIED": 0,
		"VN_BANK_TYPE_TP_BANK":     1,
		"VN_BANK_TYPE_ACB":         2,
		"VN_BANK_TYPE_BIDV":        3,
		"VN_BANK_TYPE_VCB":         4,
	}
)

func (x VNBankType) Enum() *VNBankType {
	p := new(VNBankType)
	*p = x
	return p
}

func (x VNBankType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VNBankType) Descriptor() protoreflect.EnumDescriptor {
	return file_algoenum_v1_vn_bank_proto_enumTypes[0].Descriptor()
}

func (VNBankType) Type() protoreflect.EnumType {
	return &file_algoenum_v1_vn_bank_proto_enumTypes[0]
}

func (x VNBankType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VNBankType.Descriptor instead.
func (VNBankType) EnumDescriptor() ([]byte, []int) {
	return file_algoenum_v1_vn_bank_proto_rawDescGZIP(), []int{0}
}

var File_algoenum_v1_vn_bank_proto protoreflect.FileDescriptor

const file_algoenum_v1_vn_bank_proto_rawDesc = "" +
	"\n" +
	"\x19algoenum/v1/vn_bank.proto\x12\valgoenum.v1*\x87\x01\n" +
	"\n" +
	"VNBankType\x12\x1c\n" +
	"\x18VN_BANK_TYPE_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14VN_BANK_TYPE_TP_BANK\x10\x01\x12\x14\n" +
	"\x10VN_BANK_TYPE_ACB\x10\x02\x12\x15\n" +
	"\x11VN_BANK_TYPE_BIDV\x10\x03\x12\x14\n" +
	"\x10VN_BANK_TYPE_VCB\x10\x04BGZEgit.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1;algoenumv1b\x06proto3"

var (
	file_algoenum_v1_vn_bank_proto_rawDescOnce sync.Once
	file_algoenum_v1_vn_bank_proto_rawDescData []byte
)

func file_algoenum_v1_vn_bank_proto_rawDescGZIP() []byte {
	file_algoenum_v1_vn_bank_proto_rawDescOnce.Do(func() {
		file_algoenum_v1_vn_bank_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_algoenum_v1_vn_bank_proto_rawDesc), len(file_algoenum_v1_vn_bank_proto_rawDesc)))
	})
	return file_algoenum_v1_vn_bank_proto_rawDescData
}

var file_algoenum_v1_vn_bank_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_algoenum_v1_vn_bank_proto_goTypes = []any{
	(VNBankType)(0), // 0: algoenum.v1.VNBankType
}
var file_algoenum_v1_vn_bank_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_algoenum_v1_vn_bank_proto_init() }
func file_algoenum_v1_vn_bank_proto_init() {
	if File_algoenum_v1_vn_bank_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_algoenum_v1_vn_bank_proto_rawDesc), len(file_algoenum_v1_vn_bank_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_algoenum_v1_vn_bank_proto_goTypes,
		DependencyIndexes: file_algoenum_v1_vn_bank_proto_depIdxs,
		EnumInfos:         file_algoenum_v1_vn_bank_proto_enumTypes,
	}.Build()
	File_algoenum_v1_vn_bank_proto = out.File
	file_algoenum_v1_vn_bank_proto_goTypes = nil
	file_algoenum_v1_vn_bank_proto_depIdxs = nil
}
