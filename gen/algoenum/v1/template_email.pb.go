// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: algoenum/v1/template_email.proto

package algoenumv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TemplateEmailType int32

const (
	TemplateEmailType_TEMPLATE_EMAIL_TYPE_UNSPECIFIED         TemplateEmailType = 0
	TemplateEmailType_TEMPLATE_EMAIL_TYPE_OTP                 TemplateEmailType = 1
	TemplateEmailType_TEMPLATE_EMAIL_TYPE_OTP_FORGOT_PASSWORD TemplateEmailType = 2
	TemplateEmailType_TEMPLATE_EMAIL_TYPE_VERIFY_SIGNUP       TemplateEmailType = 3
)

// Enum value maps for TemplateEmailType.
var (
	TemplateEmailType_name = map[int32]string{
		0: "TEMPLATE_EMAIL_TYPE_UNSPECIFIED",
		1: "TEMPLATE_EMAIL_TYPE_OTP",
		2: "TEMPLATE_EMAIL_TYPE_OTP_FORGOT_PASSWORD",
		3: "TEMPLATE_EMAIL_TYPE_VERIFY_SIGNUP",
	}
	TemplateEmailType_value = map[string]int32{
		"TEMPLATE_EMAIL_TYPE_UNSPECIFIED":         0,
		"TEMPLATE_EMAIL_TYPE_OTP":                 1,
		"TEMPLATE_EMAIL_TYPE_OTP_FORGOT_PASSWORD": 2,
		"TEMPLATE_EMAIL_TYPE_VERIFY_SIGNUP":       3,
	}
)

func (x TemplateEmailType) Enum() *TemplateEmailType {
	p := new(TemplateEmailType)
	*p = x
	return p
}

func (x TemplateEmailType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TemplateEmailType) Descriptor() protoreflect.EnumDescriptor {
	return file_algoenum_v1_template_email_proto_enumTypes[0].Descriptor()
}

func (TemplateEmailType) Type() protoreflect.EnumType {
	return &file_algoenum_v1_template_email_proto_enumTypes[0]
}

func (x TemplateEmailType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TemplateEmailType.Descriptor instead.
func (TemplateEmailType) EnumDescriptor() ([]byte, []int) {
	return file_algoenum_v1_template_email_proto_rawDescGZIP(), []int{0}
}

var File_algoenum_v1_template_email_proto protoreflect.FileDescriptor

const file_algoenum_v1_template_email_proto_rawDesc = "" +
	"\n" +
	" algoenum/v1/template_email.proto\x12\valgoenum.v1*\xa9\x01\n" +
	"\x11TemplateEmailType\x12#\n" +
	"\x1fTEMPLATE_EMAIL_TYPE_UNSPECIFIED\x10\x00\x12\x1b\n" +
	"\x17TEMPLATE_EMAIL_TYPE_OTP\x10\x01\x12+\n" +
	"'TEMPLATE_EMAIL_TYPE_OTP_FORGOT_PASSWORD\x10\x02\x12%\n" +
	"!TEMPLATE_EMAIL_TYPE_VERIFY_SIGNUP\x10\x03BGZEgit.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1;algoenumv1b\x06proto3"

var (
	file_algoenum_v1_template_email_proto_rawDescOnce sync.Once
	file_algoenum_v1_template_email_proto_rawDescData []byte
)

func file_algoenum_v1_template_email_proto_rawDescGZIP() []byte {
	file_algoenum_v1_template_email_proto_rawDescOnce.Do(func() {
		file_algoenum_v1_template_email_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_algoenum_v1_template_email_proto_rawDesc), len(file_algoenum_v1_template_email_proto_rawDesc)))
	})
	return file_algoenum_v1_template_email_proto_rawDescData
}

var file_algoenum_v1_template_email_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_algoenum_v1_template_email_proto_goTypes = []any{
	(TemplateEmailType)(0), // 0: algoenum.v1.TemplateEmailType
}
var file_algoenum_v1_template_email_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_algoenum_v1_template_email_proto_init() }
func file_algoenum_v1_template_email_proto_init() {
	if File_algoenum_v1_template_email_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_algoenum_v1_template_email_proto_rawDesc), len(file_algoenum_v1_template_email_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_algoenum_v1_template_email_proto_goTypes,
		DependencyIndexes: file_algoenum_v1_template_email_proto_depIdxs,
		EnumInfos:         file_algoenum_v1_template_email_proto_enumTypes,
	}.Build()
	File_algoenum_v1_template_email_proto = out.File
	file_algoenum_v1_template_email_proto_goTypes = nil
	file_algoenum_v1_template_email_proto_depIdxs = nil
}
