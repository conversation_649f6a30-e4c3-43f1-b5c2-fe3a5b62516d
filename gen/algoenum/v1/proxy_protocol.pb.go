// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: algoenum/v1/proxy_protocol.proto

package algoenumv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProxyProtocol int32

const (
	ProxyProtocol_PROXY_PROTOCOL_UNSPECIFIED ProxyProtocol = 0
	ProxyProtocol_PROXY_PROTOCOL_HTTP        ProxyProtocol = 1
	ProxyProtocol_PROXY_PROTOCOL_SOCKS5      ProxyProtocol = 2
)

// Enum value maps for ProxyProtocol.
var (
	ProxyProtocol_name = map[int32]string{
		0: "PROXY_PROTOCOL_UNSPECIFIED",
		1: "PROXY_PROTOCOL_HTTP",
		2: "PROXY_PROTOCOL_SOCKS5",
	}
	ProxyProtocol_value = map[string]int32{
		"PROXY_PROTOCOL_UNSPECIFIED": 0,
		"PROXY_PROTOCOL_HTTP":        1,
		"PROXY_PROTOCOL_SOCKS5":      2,
	}
)

func (x ProxyProtocol) Enum() *ProxyProtocol {
	p := new(ProxyProtocol)
	*p = x
	return p
}

func (x ProxyProtocol) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProxyProtocol) Descriptor() protoreflect.EnumDescriptor {
	return file_algoenum_v1_proxy_protocol_proto_enumTypes[0].Descriptor()
}

func (ProxyProtocol) Type() protoreflect.EnumType {
	return &file_algoenum_v1_proxy_protocol_proto_enumTypes[0]
}

func (x ProxyProtocol) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProxyProtocol.Descriptor instead.
func (ProxyProtocol) EnumDescriptor() ([]byte, []int) {
	return file_algoenum_v1_proxy_protocol_proto_rawDescGZIP(), []int{0}
}

var File_algoenum_v1_proxy_protocol_proto protoreflect.FileDescriptor

const file_algoenum_v1_proxy_protocol_proto_rawDesc = "" +
	"\n" +
	" algoenum/v1/proxy_protocol.proto\x12\valgoenum.v1*c\n" +
	"\rProxyProtocol\x12\x1e\n" +
	"\x1aPROXY_PROTOCOL_UNSPECIFIED\x10\x00\x12\x17\n" +
	"\x13PROXY_PROTOCOL_HTTP\x10\x01\x12\x19\n" +
	"\x15PROXY_PROTOCOL_SOCKS5\x10\x02BGZEgit.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1;algoenumv1b\x06proto3"

var (
	file_algoenum_v1_proxy_protocol_proto_rawDescOnce sync.Once
	file_algoenum_v1_proxy_protocol_proto_rawDescData []byte
)

func file_algoenum_v1_proxy_protocol_proto_rawDescGZIP() []byte {
	file_algoenum_v1_proxy_protocol_proto_rawDescOnce.Do(func() {
		file_algoenum_v1_proxy_protocol_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_algoenum_v1_proxy_protocol_proto_rawDesc), len(file_algoenum_v1_proxy_protocol_proto_rawDesc)))
	})
	return file_algoenum_v1_proxy_protocol_proto_rawDescData
}

var file_algoenum_v1_proxy_protocol_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_algoenum_v1_proxy_protocol_proto_goTypes = []any{
	(ProxyProtocol)(0), // 0: algoenum.v1.ProxyProtocol
}
var file_algoenum_v1_proxy_protocol_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_algoenum_v1_proxy_protocol_proto_init() }
func file_algoenum_v1_proxy_protocol_proto_init() {
	if File_algoenum_v1_proxy_protocol_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_algoenum_v1_proxy_protocol_proto_rawDesc), len(file_algoenum_v1_proxy_protocol_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_algoenum_v1_proxy_protocol_proto_goTypes,
		DependencyIndexes: file_algoenum_v1_proxy_protocol_proto_depIdxs,
		EnumInfos:         file_algoenum_v1_proxy_protocol_proto_enumTypes,
	}.Build()
	File_algoenum_v1_proxy_protocol_proto = out.File
	file_algoenum_v1_proxy_protocol_proto_goTypes = nil
	file_algoenum_v1_proxy_protocol_proto_depIdxs = nil
}
