// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: algoenum/v1/service.proto

package algoenumv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ServiceType int32

const (
	ServiceType_SERVICE_TYPE_UNSPECIFIED ServiceType = 0
	ServiceType_SERVICE_TYPE_ALGO_PROXY  ServiceType = 1
	ServiceType_SERVICE_TYPE_U2DPN       ServiceType = 2
)

// Enum value maps for ServiceType.
var (
	ServiceType_name = map[int32]string{
		0: "SERVICE_TYPE_UNSPECIFIED",
		1: "SERVICE_TYPE_ALGO_PROXY",
		2: "SERVICE_TYPE_U2DPN",
	}
	ServiceType_value = map[string]int32{
		"SERVICE_TYPE_UNSPECIFIED": 0,
		"SERVICE_TYPE_ALGO_PROXY":  1,
		"SERVICE_TYPE_U2DPN":       2,
	}
)

func (x ServiceType) Enum() *ServiceType {
	p := new(ServiceType)
	*p = x
	return p
}

func (x ServiceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceType) Descriptor() protoreflect.EnumDescriptor {
	return file_algoenum_v1_service_proto_enumTypes[0].Descriptor()
}

func (ServiceType) Type() protoreflect.EnumType {
	return &file_algoenum_v1_service_proto_enumTypes[0]
}

func (x ServiceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceType.Descriptor instead.
func (ServiceType) EnumDescriptor() ([]byte, []int) {
	return file_algoenum_v1_service_proto_rawDescGZIP(), []int{0}
}

var File_algoenum_v1_service_proto protoreflect.FileDescriptor

const file_algoenum_v1_service_proto_rawDesc = "" +
	"\n" +
	"\x19algoenum/v1/service.proto\x12\valgoenum.v1*`\n" +
	"\vServiceType\x12\x1c\n" +
	"\x18SERVICE_TYPE_UNSPECIFIED\x10\x00\x12\x1b\n" +
	"\x17SERVICE_TYPE_ALGO_PROXY\x10\x01\x12\x16\n" +
	"\x12SERVICE_TYPE_U2DPN\x10\x02BGZEgit.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1;algoenumv1b\x06proto3"

var (
	file_algoenum_v1_service_proto_rawDescOnce sync.Once
	file_algoenum_v1_service_proto_rawDescData []byte
)

func file_algoenum_v1_service_proto_rawDescGZIP() []byte {
	file_algoenum_v1_service_proto_rawDescOnce.Do(func() {
		file_algoenum_v1_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_algoenum_v1_service_proto_rawDesc), len(file_algoenum_v1_service_proto_rawDesc)))
	})
	return file_algoenum_v1_service_proto_rawDescData
}

var file_algoenum_v1_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_algoenum_v1_service_proto_goTypes = []any{
	(ServiceType)(0), // 0: algoenum.v1.ServiceType
}
var file_algoenum_v1_service_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_algoenum_v1_service_proto_init() }
func file_algoenum_v1_service_proto_init() {
	if File_algoenum_v1_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_algoenum_v1_service_proto_rawDesc), len(file_algoenum_v1_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_algoenum_v1_service_proto_goTypes,
		DependencyIndexes: file_algoenum_v1_service_proto_depIdxs,
		EnumInfos:         file_algoenum_v1_service_proto_enumTypes,
	}.Build()
	File_algoenum_v1_service_proto = out.File
	file_algoenum_v1_service_proto_goTypes = nil
	file_algoenum_v1_service_proto_depIdxs = nil
}
