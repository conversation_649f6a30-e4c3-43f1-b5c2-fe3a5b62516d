// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: algoenum/v1/packet_type.proto

package algoenumv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PacketType int32

const (
	PacketType_PACKET_TYPE_UNSPECIFIED PacketType = 0
	PacketType_PACKET_TYPE_TCP         PacketType = 1
	PacketType_PACKET_TYPE_UDP         PacketType = 2
)

// Enum value maps for PacketType.
var (
	PacketType_name = map[int32]string{
		0: "PACKET_TYPE_UNSPECIFIED",
		1: "PACKET_TYPE_TCP",
		2: "PACKET_TYPE_UDP",
	}
	PacketType_value = map[string]int32{
		"PACKET_TYPE_UNSPECIFIED": 0,
		"PACKET_TYPE_TCP":         1,
		"PACKET_TYPE_UDP":         2,
	}
)

func (x PacketType) Enum() *PacketType {
	p := new(PacketType)
	*p = x
	return p
}

func (x PacketType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PacketType) Descriptor() protoreflect.EnumDescriptor {
	return file_algoenum_v1_packet_type_proto_enumTypes[0].Descriptor()
}

func (PacketType) Type() protoreflect.EnumType {
	return &file_algoenum_v1_packet_type_proto_enumTypes[0]
}

func (x PacketType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PacketType.Descriptor instead.
func (PacketType) EnumDescriptor() ([]byte, []int) {
	return file_algoenum_v1_packet_type_proto_rawDescGZIP(), []int{0}
}

var File_algoenum_v1_packet_type_proto protoreflect.FileDescriptor

const file_algoenum_v1_packet_type_proto_rawDesc = "" +
	"\n" +
	"\x1dalgoenum/v1/packet_type.proto\x12\valgoenum.v1*S\n" +
	"\n" +
	"PacketType\x12\x1b\n" +
	"\x17PACKET_TYPE_UNSPECIFIED\x10\x00\x12\x13\n" +
	"\x0fPACKET_TYPE_TCP\x10\x01\x12\x13\n" +
	"\x0fPACKET_TYPE_UDP\x10\x02BGZEgit.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1;algoenumv1b\x06proto3"

var (
	file_algoenum_v1_packet_type_proto_rawDescOnce sync.Once
	file_algoenum_v1_packet_type_proto_rawDescData []byte
)

func file_algoenum_v1_packet_type_proto_rawDescGZIP() []byte {
	file_algoenum_v1_packet_type_proto_rawDescOnce.Do(func() {
		file_algoenum_v1_packet_type_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_algoenum_v1_packet_type_proto_rawDesc), len(file_algoenum_v1_packet_type_proto_rawDesc)))
	})
	return file_algoenum_v1_packet_type_proto_rawDescData
}

var file_algoenum_v1_packet_type_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_algoenum_v1_packet_type_proto_goTypes = []any{
	(PacketType)(0), // 0: algoenum.v1.PacketType
}
var file_algoenum_v1_packet_type_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_algoenum_v1_packet_type_proto_init() }
func file_algoenum_v1_packet_type_proto_init() {
	if File_algoenum_v1_packet_type_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_algoenum_v1_packet_type_proto_rawDesc), len(file_algoenum_v1_packet_type_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_algoenum_v1_packet_type_proto_goTypes,
		DependencyIndexes: file_algoenum_v1_packet_type_proto_depIdxs,
		EnumInfos:         file_algoenum_v1_packet_type_proto_enumTypes,
	}.Build()
	File_algoenum_v1_packet_type_proto = out.File
	file_algoenum_v1_packet_type_proto_goTypes = nil
	file_algoenum_v1_packet_type_proto_depIdxs = nil
}
