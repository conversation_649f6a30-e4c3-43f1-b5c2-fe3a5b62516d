// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: algoenum/v1/proxy_type.proto

package algoenumv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProxyType int32

const (
	ProxyType_PROXY_TYPE_UNSPECIFIED ProxyType = 0
	ProxyType_PROXY_TYPE_DATACENTER  ProxyType = 1
	ProxyType_PROXY_TYPE_RESIDENTIAL ProxyType = 2
	ProxyType_PROXY_TYPE_MOBILE      ProxyType = 3
)

// Enum value maps for ProxyType.
var (
	ProxyType_name = map[int32]string{
		0: "PROXY_TYPE_UNSPECIFIED",
		1: "PROXY_TYPE_DATACENTER",
		2: "PROXY_TYPE_RESIDENTIAL",
		3: "PROXY_TYPE_MOBILE",
	}
	ProxyType_value = map[string]int32{
		"PROXY_TYPE_UNSPECIFIED": 0,
		"PROXY_TYPE_DATACENTER":  1,
		"PROXY_TYPE_RESIDENTIAL": 2,
		"PROXY_TYPE_MOBILE":      3,
	}
)

func (x ProxyType) Enum() *ProxyType {
	p := new(ProxyType)
	*p = x
	return p
}

func (x ProxyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProxyType) Descriptor() protoreflect.EnumDescriptor {
	return file_algoenum_v1_proxy_type_proto_enumTypes[0].Descriptor()
}

func (ProxyType) Type() protoreflect.EnumType {
	return &file_algoenum_v1_proxy_type_proto_enumTypes[0]
}

func (x ProxyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProxyType.Descriptor instead.
func (ProxyType) EnumDescriptor() ([]byte, []int) {
	return file_algoenum_v1_proxy_type_proto_rawDescGZIP(), []int{0}
}

var File_algoenum_v1_proxy_type_proto protoreflect.FileDescriptor

const file_algoenum_v1_proxy_type_proto_rawDesc = "" +
	"\n" +
	"\x1calgoenum/v1/proxy_type.proto\x12\valgoenum.v1*u\n" +
	"\tProxyType\x12\x1a\n" +
	"\x16PROXY_TYPE_UNSPECIFIED\x10\x00\x12\x19\n" +
	"\x15PROXY_TYPE_DATACENTER\x10\x01\x12\x1a\n" +
	"\x16PROXY_TYPE_RESIDENTIAL\x10\x02\x12\x15\n" +
	"\x11PROXY_TYPE_MOBILE\x10\x03BGZEgit.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1;algoenumv1b\x06proto3"

var (
	file_algoenum_v1_proxy_type_proto_rawDescOnce sync.Once
	file_algoenum_v1_proxy_type_proto_rawDescData []byte
)

func file_algoenum_v1_proxy_type_proto_rawDescGZIP() []byte {
	file_algoenum_v1_proxy_type_proto_rawDescOnce.Do(func() {
		file_algoenum_v1_proxy_type_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_algoenum_v1_proxy_type_proto_rawDesc), len(file_algoenum_v1_proxy_type_proto_rawDesc)))
	})
	return file_algoenum_v1_proxy_type_proto_rawDescData
}

var file_algoenum_v1_proxy_type_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_algoenum_v1_proxy_type_proto_goTypes = []any{
	(ProxyType)(0), // 0: algoenum.v1.ProxyType
}
var file_algoenum_v1_proxy_type_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_algoenum_v1_proxy_type_proto_init() }
func file_algoenum_v1_proxy_type_proto_init() {
	if File_algoenum_v1_proxy_type_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_algoenum_v1_proxy_type_proto_rawDesc), len(file_algoenum_v1_proxy_type_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_algoenum_v1_proxy_type_proto_goTypes,
		DependencyIndexes: file_algoenum_v1_proxy_type_proto_depIdxs,
		EnumInfos:         file_algoenum_v1_proxy_type_proto_enumTypes,
	}.Build()
	File_algoenum_v1_proxy_type_proto = out.File
	file_algoenum_v1_proxy_type_proto_goTypes = nil
	file_algoenum_v1_proxy_type_proto_depIdxs = nil
}
