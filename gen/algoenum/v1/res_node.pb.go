// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: algoenum/v1/res_node.proto

package algoenumv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ResNodeStatus int32

const (
	ResNodeStatus_RES_NODE_STATUS_UNSPECIFIED ResNodeStatus = 0
	ResNodeStatus_RES_NODE_STATUS_OFFLINE     ResNodeStatus = 1
	ResNodeStatus_RES_NODE_STATUS_ONLINE      ResNodeStatus = 2
)

// Enum value maps for ResNodeStatus.
var (
	ResNodeStatus_name = map[int32]string{
		0: "RES_NODE_STATUS_UNSPECIFIED",
		1: "RES_NODE_STATUS_OFFLINE",
		2: "RES_NODE_STATUS_ONLINE",
	}
	ResNodeStatus_value = map[string]int32{
		"RES_NODE_STATUS_UNSPECIFIED": 0,
		"RES_NODE_STATUS_OFFLINE":     1,
		"RES_NODE_STATUS_ONLINE":      2,
	}
)

func (x ResNodeStatus) Enum() *ResNodeStatus {
	p := new(ResNodeStatus)
	*p = x
	return p
}

func (x ResNodeStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResNodeStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_algoenum_v1_res_node_proto_enumTypes[0].Descriptor()
}

func (ResNodeStatus) Type() protoreflect.EnumType {
	return &file_algoenum_v1_res_node_proto_enumTypes[0]
}

func (x ResNodeStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResNodeStatus.Descriptor instead.
func (ResNodeStatus) EnumDescriptor() ([]byte, []int) {
	return file_algoenum_v1_res_node_proto_rawDescGZIP(), []int{0}
}

type ResNodePowerState int32

const (
	ResNodePowerState_RES_NODE_POWER_STATE_UNSPECIFIED        ResNodePowerState = 0
	ResNodePowerState_RES_NODE_POWER_STATE_OFF                ResNodePowerState = 1
	ResNodePowerState_RES_NODE_POWER_STATE_ON                 ResNodePowerState = 2
	ResNodePowerState_RES_NODE_POWER_STATE_POWER_ID_INCORRECT ResNodePowerState = 3
)

// Enum value maps for ResNodePowerState.
var (
	ResNodePowerState_name = map[int32]string{
		0: "RES_NODE_POWER_STATE_UNSPECIFIED",
		1: "RES_NODE_POWER_STATE_OFF",
		2: "RES_NODE_POWER_STATE_ON",
		3: "RES_NODE_POWER_STATE_POWER_ID_INCORRECT",
	}
	ResNodePowerState_value = map[string]int32{
		"RES_NODE_POWER_STATE_UNSPECIFIED":        0,
		"RES_NODE_POWER_STATE_OFF":                1,
		"RES_NODE_POWER_STATE_ON":                 2,
		"RES_NODE_POWER_STATE_POWER_ID_INCORRECT": 3,
	}
)

func (x ResNodePowerState) Enum() *ResNodePowerState {
	p := new(ResNodePowerState)
	*p = x
	return p
}

func (x ResNodePowerState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResNodePowerState) Descriptor() protoreflect.EnumDescriptor {
	return file_algoenum_v1_res_node_proto_enumTypes[1].Descriptor()
}

func (ResNodePowerState) Type() protoreflect.EnumType {
	return &file_algoenum_v1_res_node_proto_enumTypes[1]
}

func (x ResNodePowerState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResNodePowerState.Descriptor instead.
func (ResNodePowerState) EnumDescriptor() ([]byte, []int) {
	return file_algoenum_v1_res_node_proto_rawDescGZIP(), []int{1}
}

type ResDeviceStatus int32

const (
	ResDeviceStatus_RES_DEVICE_STATUS_UNSPECIFIED ResDeviceStatus = 0
	ResDeviceStatus_RES_DEVICE_STATUS_OFFLINE     ResDeviceStatus = 1
	ResDeviceStatus_RES_DEVICE_STATUS_ONLINE      ResDeviceStatus = 2
	ResDeviceStatus_RES_DEVICE_STATUS_ON_DIAL     ResDeviceStatus = 3
)

// Enum value maps for ResDeviceStatus.
var (
	ResDeviceStatus_name = map[int32]string{
		0: "RES_DEVICE_STATUS_UNSPECIFIED",
		1: "RES_DEVICE_STATUS_OFFLINE",
		2: "RES_DEVICE_STATUS_ONLINE",
		3: "RES_DEVICE_STATUS_ON_DIAL",
	}
	ResDeviceStatus_value = map[string]int32{
		"RES_DEVICE_STATUS_UNSPECIFIED": 0,
		"RES_DEVICE_STATUS_OFFLINE":     1,
		"RES_DEVICE_STATUS_ONLINE":      2,
		"RES_DEVICE_STATUS_ON_DIAL":     3,
	}
)

func (x ResDeviceStatus) Enum() *ResDeviceStatus {
	p := new(ResDeviceStatus)
	*p = x
	return p
}

func (x ResDeviceStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResDeviceStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_algoenum_v1_res_node_proto_enumTypes[2].Descriptor()
}

func (ResDeviceStatus) Type() protoreflect.EnumType {
	return &file_algoenum_v1_res_node_proto_enumTypes[2]
}

func (x ResDeviceStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResDeviceStatus.Descriptor instead.
func (ResDeviceStatus) EnumDescriptor() ([]byte, []int) {
	return file_algoenum_v1_res_node_proto_rawDescGZIP(), []int{2}
}

var File_algoenum_v1_res_node_proto protoreflect.FileDescriptor

const file_algoenum_v1_res_node_proto_rawDesc = "" +
	"\n" +
	"\x1aalgoenum/v1/res_node.proto\x12\valgoenum.v1*i\n" +
	"\rResNodeStatus\x12\x1f\n" +
	"\x1bRES_NODE_STATUS_UNSPECIFIED\x10\x00\x12\x1b\n" +
	"\x17RES_NODE_STATUS_OFFLINE\x10\x01\x12\x1a\n" +
	"\x16RES_NODE_STATUS_ONLINE\x10\x02*\xa1\x01\n" +
	"\x11ResNodePowerState\x12$\n" +
	" RES_NODE_POWER_STATE_UNSPECIFIED\x10\x00\x12\x1c\n" +
	"\x18RES_NODE_POWER_STATE_OFF\x10\x01\x12\x1b\n" +
	"\x17RES_NODE_POWER_STATE_ON\x10\x02\x12+\n" +
	"'RES_NODE_POWER_STATE_POWER_ID_INCORRECT\x10\x03*\x90\x01\n" +
	"\x0fResDeviceStatus\x12!\n" +
	"\x1dRES_DEVICE_STATUS_UNSPECIFIED\x10\x00\x12\x1d\n" +
	"\x19RES_DEVICE_STATUS_OFFLINE\x10\x01\x12\x1c\n" +
	"\x18RES_DEVICE_STATUS_ONLINE\x10\x02\x12\x1d\n" +
	"\x19RES_DEVICE_STATUS_ON_DIAL\x10\x03BGZEgit.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1;algoenumv1b\x06proto3"

var (
	file_algoenum_v1_res_node_proto_rawDescOnce sync.Once
	file_algoenum_v1_res_node_proto_rawDescData []byte
)

func file_algoenum_v1_res_node_proto_rawDescGZIP() []byte {
	file_algoenum_v1_res_node_proto_rawDescOnce.Do(func() {
		file_algoenum_v1_res_node_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_algoenum_v1_res_node_proto_rawDesc), len(file_algoenum_v1_res_node_proto_rawDesc)))
	})
	return file_algoenum_v1_res_node_proto_rawDescData
}

var file_algoenum_v1_res_node_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_algoenum_v1_res_node_proto_goTypes = []any{
	(ResNodeStatus)(0),     // 0: algoenum.v1.ResNodeStatus
	(ResNodePowerState)(0), // 1: algoenum.v1.ResNodePowerState
	(ResDeviceStatus)(0),   // 2: algoenum.v1.ResDeviceStatus
}
var file_algoenum_v1_res_node_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_algoenum_v1_res_node_proto_init() }
func file_algoenum_v1_res_node_proto_init() {
	if File_algoenum_v1_res_node_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_algoenum_v1_res_node_proto_rawDesc), len(file_algoenum_v1_res_node_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_algoenum_v1_res_node_proto_goTypes,
		DependencyIndexes: file_algoenum_v1_res_node_proto_depIdxs,
		EnumInfos:         file_algoenum_v1_res_node_proto_enumTypes,
	}.Build()
	File_algoenum_v1_res_node_proto = out.File
	file_algoenum_v1_res_node_proto_goTypes = nil
	file_algoenum_v1_res_node_proto_depIdxs = nil
}
