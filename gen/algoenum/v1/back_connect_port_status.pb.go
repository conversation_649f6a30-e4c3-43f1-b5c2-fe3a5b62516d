// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: algoenum/v1/back_connect_port_status.proto

package algoenumv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackConnectPortStatus int32

const (
	BackConnectPortStatus_BACK_CONNECT_PORT_STATUS_UNSPECIFIED BackConnectPortStatus = 0
	BackConnectPortStatus_BACK_CONNECT_PORT_STATUS_ONLINE      BackConnectPortStatus = 1
	BackConnectPortStatus_BACK_CONNECT_PORT_STATUS_OFFLINE     BackConnectPortStatus = 2
)

// Enum value maps for BackConnectPortStatus.
var (
	BackConnectPortStatus_name = map[int32]string{
		0: "BACK_CONNECT_PORT_STATUS_UNSPECIFIED",
		1: "BACK_CONNECT_PORT_STATUS_ONLINE",
		2: "BACK_CONNECT_PORT_STATUS_OFFLINE",
	}
	BackConnectPortStatus_value = map[string]int32{
		"BACK_CONNECT_PORT_STATUS_UNSPECIFIED": 0,
		"BACK_CONNECT_PORT_STATUS_ONLINE":      1,
		"BACK_CONNECT_PORT_STATUS_OFFLINE":     2,
	}
)

func (x BackConnectPortStatus) Enum() *BackConnectPortStatus {
	p := new(BackConnectPortStatus)
	*p = x
	return p
}

func (x BackConnectPortStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BackConnectPortStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_algoenum_v1_back_connect_port_status_proto_enumTypes[0].Descriptor()
}

func (BackConnectPortStatus) Type() protoreflect.EnumType {
	return &file_algoenum_v1_back_connect_port_status_proto_enumTypes[0]
}

func (x BackConnectPortStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BackConnectPortStatus.Descriptor instead.
func (BackConnectPortStatus) EnumDescriptor() ([]byte, []int) {
	return file_algoenum_v1_back_connect_port_status_proto_rawDescGZIP(), []int{0}
}

var File_algoenum_v1_back_connect_port_status_proto protoreflect.FileDescriptor

const file_algoenum_v1_back_connect_port_status_proto_rawDesc = "" +
	"\n" +
	"*algoenum/v1/back_connect_port_status.proto\x12\valgoenum.v1*\x8c\x01\n" +
	"\x15BackConnectPortStatus\x12(\n" +
	"$BACK_CONNECT_PORT_STATUS_UNSPECIFIED\x10\x00\x12#\n" +
	"\x1fBACK_CONNECT_PORT_STATUS_ONLINE\x10\x01\x12$\n" +
	" BACK_CONNECT_PORT_STATUS_OFFLINE\x10\x02BGZEgit.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1;algoenumv1b\x06proto3"

var (
	file_algoenum_v1_back_connect_port_status_proto_rawDescOnce sync.Once
	file_algoenum_v1_back_connect_port_status_proto_rawDescData []byte
)

func file_algoenum_v1_back_connect_port_status_proto_rawDescGZIP() []byte {
	file_algoenum_v1_back_connect_port_status_proto_rawDescOnce.Do(func() {
		file_algoenum_v1_back_connect_port_status_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_algoenum_v1_back_connect_port_status_proto_rawDesc), len(file_algoenum_v1_back_connect_port_status_proto_rawDesc)))
	})
	return file_algoenum_v1_back_connect_port_status_proto_rawDescData
}

var file_algoenum_v1_back_connect_port_status_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_algoenum_v1_back_connect_port_status_proto_goTypes = []any{
	(BackConnectPortStatus)(0), // 0: algoenum.v1.BackConnectPortStatus
}
var file_algoenum_v1_back_connect_port_status_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_algoenum_v1_back_connect_port_status_proto_init() }
func file_algoenum_v1_back_connect_port_status_proto_init() {
	if File_algoenum_v1_back_connect_port_status_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_algoenum_v1_back_connect_port_status_proto_rawDesc), len(file_algoenum_v1_back_connect_port_status_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_algoenum_v1_back_connect_port_status_proto_goTypes,
		DependencyIndexes: file_algoenum_v1_back_connect_port_status_proto_depIdxs,
		EnumInfos:         file_algoenum_v1_back_connect_port_status_proto_enumTypes,
	}.Build()
	File_algoenum_v1_back_connect_port_status_proto = out.File
	file_algoenum_v1_back_connect_port_status_proto_goTypes = nil
	file_algoenum_v1_back_connect_port_status_proto_depIdxs = nil
}
