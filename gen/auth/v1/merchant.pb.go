// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: auth/v1/merchant.proto

package authv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MerchantAuthServiceChangePasswordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OldPassword   string                 `protobuf:"bytes,1,opt,name=old_password,json=oldPassword,proto3" json:"old_password,omitempty"`
	NewPassword   string                 `protobuf:"bytes,2,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantAuthServiceChangePasswordRequest) Reset() {
	*x = MerchantAuthServiceChangePasswordRequest{}
	mi := &file_auth_v1_merchant_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceChangePasswordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceChangePasswordRequest) ProtoMessage() {}

func (x *MerchantAuthServiceChangePasswordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceChangePasswordRequest.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceChangePasswordRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{0}
}

func (x *MerchantAuthServiceChangePasswordRequest) GetOldPassword() string {
	if x != nil {
		return x.OldPassword
	}
	return ""
}

func (x *MerchantAuthServiceChangePasswordRequest) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

type MerchantAuthServiceChangePasswordResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantAuthServiceChangePasswordResponse) Reset() {
	*x = MerchantAuthServiceChangePasswordResponse{}
	mi := &file_auth_v1_merchant_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceChangePasswordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceChangePasswordResponse) ProtoMessage() {}

func (x *MerchantAuthServiceChangePasswordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceChangePasswordResponse.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceChangePasswordResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{1}
}

func (x *MerchantAuthServiceChangePasswordResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type MerchantAuthServiceFetchRoleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdRole        string                 `protobuf:"bytes,1,opt,name=id_role,json=idRole,proto3" json:"id_role,omitempty"`
	NameSearch    string                 `protobuf:"bytes,2,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	Pagination    *v11.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantAuthServiceFetchRoleRequest) Reset() {
	*x = MerchantAuthServiceFetchRoleRequest{}
	mi := &file_auth_v1_merchant_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceFetchRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceFetchRoleRequest) ProtoMessage() {}

func (x *MerchantAuthServiceFetchRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceFetchRoleRequest.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceFetchRoleRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{2}
}

func (x *MerchantAuthServiceFetchRoleRequest) GetIdRole() string {
	if x != nil {
		return x.IdRole
	}
	return ""
}

func (x *MerchantAuthServiceFetchRoleRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *MerchantAuthServiceFetchRoleRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantAuthServiceFetchRoleResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Items         []*MerchantRoleModel    `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantAuthServiceFetchRoleResponse) Reset() {
	*x = MerchantAuthServiceFetchRoleResponse{}
	mi := &file_auth_v1_merchant_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceFetchRoleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceFetchRoleResponse) ProtoMessage() {}

func (x *MerchantAuthServiceFetchRoleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceFetchRoleResponse.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceFetchRoleResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{3}
}

func (x *MerchantAuthServiceFetchRoleResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantAuthServiceFetchRoleResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *MerchantAuthServiceFetchRoleResponse) GetItems() []*MerchantRoleModel {
	if x != nil {
		return x.Items
	}
	return nil
}

type MerchantRoleModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdRole        string                 `protobuf:"bytes,1,opt,name=id_role,json=idRole,proto3" json:"id_role,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantRoleModel) Reset() {
	*x = MerchantRoleModel{}
	mi := &file_auth_v1_merchant_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantRoleModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantRoleModel) ProtoMessage() {}

func (x *MerchantRoleModel) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantRoleModel.ProtoReflect.Descriptor instead.
func (*MerchantRoleModel) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{4}
}

func (x *MerchantRoleModel) GetIdRole() string {
	if x != nil {
		return x.IdRole
	}
	return ""
}

func (x *MerchantRoleModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type MerchantAuthServiceUpdateUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdUser        string                 `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	State         *v11.State             `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantAuthServiceUpdateUserRequest) Reset() {
	*x = MerchantAuthServiceUpdateUserRequest{}
	mi := &file_auth_v1_merchant_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceUpdateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceUpdateUserRequest) ProtoMessage() {}

func (x *MerchantAuthServiceUpdateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceUpdateUserRequest.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceUpdateUserRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{5}
}

func (x *MerchantAuthServiceUpdateUserRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *MerchantAuthServiceUpdateUserRequest) GetState() *v11.State {
	if x != nil {
		return x.State
	}
	return nil
}

type MerchantAuthServiceUpdateUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantAuthServiceUpdateUserResponse) Reset() {
	*x = MerchantAuthServiceUpdateUserResponse{}
	mi := &file_auth_v1_merchant_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceUpdateUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceUpdateUserResponse) ProtoMessage() {}

func (x *MerchantAuthServiceUpdateUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceUpdateUserResponse.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceUpdateUserResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{6}
}

func (x *MerchantAuthServiceUpdateUserResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type MerchantAuthServiceFetchUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdUser        string                 `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	IdRole        string                 `protobuf:"bytes,2,opt,name=id_role,json=idRole,proto3" json:"id_role,omitempty"`
	EmailSearch   string                 `protobuf:"bytes,3,opt,name=email_search,json=emailSearch,proto3" json:"email_search,omitempty"`
	State         *v11.State             `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v11.PaginationRequest `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantAuthServiceFetchUserRequest) Reset() {
	*x = MerchantAuthServiceFetchUserRequest{}
	mi := &file_auth_v1_merchant_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceFetchUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceFetchUserRequest) ProtoMessage() {}

func (x *MerchantAuthServiceFetchUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceFetchUserRequest.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceFetchUserRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{7}
}

func (x *MerchantAuthServiceFetchUserRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *MerchantAuthServiceFetchUserRequest) GetIdRole() string {
	if x != nil {
		return x.IdRole
	}
	return ""
}

func (x *MerchantAuthServiceFetchUserRequest) GetEmailSearch() string {
	if x != nil {
		return x.EmailSearch
	}
	return ""
}

func (x *MerchantAuthServiceFetchUserRequest) GetState() *v11.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *MerchantAuthServiceFetchUserRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantAuthServiceFetchUserResponse struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Error         *v1.ErrorMessage            `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Users         []*MerchantServiceUserModel `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
	Pagination    *v11.PaginationResponse     `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantAuthServiceFetchUserResponse) Reset() {
	*x = MerchantAuthServiceFetchUserResponse{}
	mi := &file_auth_v1_merchant_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceFetchUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceFetchUserResponse) ProtoMessage() {}

func (x *MerchantAuthServiceFetchUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceFetchUserResponse.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceFetchUserResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{8}
}

func (x *MerchantAuthServiceFetchUserResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantAuthServiceFetchUserResponse) GetUsers() []*MerchantServiceUserModel {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *MerchantAuthServiceFetchUserResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantServiceUserModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdUser        string                 `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	Role          *MerchantUserRoleModel `protobuf:"bytes,2,opt,name=role,proto3" json:"role,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	IsEnableTotp  bool                   `protobuf:"varint,4,opt,name=is_enable_totp,json=isEnableTotp,proto3" json:"is_enable_totp,omitempty"`
	IsActive      bool                   `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantServiceUserModel) Reset() {
	*x = MerchantServiceUserModel{}
	mi := &file_auth_v1_merchant_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantServiceUserModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantServiceUserModel) ProtoMessage() {}

func (x *MerchantServiceUserModel) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantServiceUserModel.ProtoReflect.Descriptor instead.
func (*MerchantServiceUserModel) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{9}
}

func (x *MerchantServiceUserModel) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *MerchantServiceUserModel) GetRole() *MerchantUserRoleModel {
	if x != nil {
		return x.Role
	}
	return nil
}

func (x *MerchantServiceUserModel) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *MerchantServiceUserModel) GetIsEnableTotp() bool {
	if x != nil {
		return x.IsEnableTotp
	}
	return false
}

func (x *MerchantServiceUserModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type MerchantUserRoleModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdRole        string                 `protobuf:"bytes,1,opt,name=id_role,json=idRole,proto3" json:"id_role,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IsActive      bool                   `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantUserRoleModel) Reset() {
	*x = MerchantUserRoleModel{}
	mi := &file_auth_v1_merchant_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantUserRoleModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantUserRoleModel) ProtoMessage() {}

func (x *MerchantUserRoleModel) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantUserRoleModel.ProtoReflect.Descriptor instead.
func (*MerchantUserRoleModel) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{10}
}

func (x *MerchantUserRoleModel) GetIdRole() string {
	if x != nil {
		return x.IdRole
	}
	return ""
}

func (x *MerchantUserRoleModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MerchantUserRoleModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type MerchantAuthServiceSignUpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantAuthServiceSignUpRequest) Reset() {
	*x = MerchantAuthServiceSignUpRequest{}
	mi := &file_auth_v1_merchant_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceSignUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceSignUpRequest) ProtoMessage() {}

func (x *MerchantAuthServiceSignUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceSignUpRequest.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceSignUpRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{11}
}

func (x *MerchantAuthServiceSignUpRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *MerchantAuthServiceSignUpRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type MerchantAuthServiceSignUpResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantAuthServiceSignUpResponse) Reset() {
	*x = MerchantAuthServiceSignUpResponse{}
	mi := &file_auth_v1_merchant_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceSignUpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceSignUpResponse) ProtoMessage() {}

func (x *MerchantAuthServiceSignUpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceSignUpResponse.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceSignUpResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{12}
}

func (x *MerchantAuthServiceSignUpResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type MerchantAuthServiceLoginRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Domain        string                 `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	Email         string                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	Password      string                 `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	Otp           string                 `protobuf:"bytes,4,opt,name=otp,proto3" json:"otp,omitempty"`
	DeviceId      string                 `protobuf:"bytes,5,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantAuthServiceLoginRequest) Reset() {
	*x = MerchantAuthServiceLoginRequest{}
	mi := &file_auth_v1_merchant_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceLoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceLoginRequest) ProtoMessage() {}

func (x *MerchantAuthServiceLoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceLoginRequest.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceLoginRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{13}
}

func (x *MerchantAuthServiceLoginRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *MerchantAuthServiceLoginRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *MerchantAuthServiceLoginRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *MerchantAuthServiceLoginRequest) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *MerchantAuthServiceLoginRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type MerchantAuthServiceLoginResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,2,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantAuthServiceLoginResponse) Reset() {
	*x = MerchantAuthServiceLoginResponse{}
	mi := &file_auth_v1_merchant_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceLoginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceLoginResponse) ProtoMessage() {}

func (x *MerchantAuthServiceLoginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceLoginResponse.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceLoginResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{14}
}

func (x *MerchantAuthServiceLoginResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *MerchantAuthServiceLoginResponse) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *MerchantAuthServiceLoginResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type MerchantAuthServiceMeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantAuthServiceMeRequest) Reset() {
	*x = MerchantAuthServiceMeRequest{}
	mi := &file_auth_v1_merchant_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceMeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceMeRequest) ProtoMessage() {}

func (x *MerchantAuthServiceMeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceMeRequest.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceMeRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{15}
}

type MerchantAuthServiceMeResponse struct {
	state          protoimpl.MessageState     `protogen:"open.v1"`
	Paths          []string                   `protobuf:"bytes,1,rep,name=paths,proto3" json:"paths,omitempty"`
	Error          *v1.ErrorMessage           `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	UserDetail     *MerchantMeUserDetails     `protobuf:"bytes,3,opt,name=user_detail,json=userDetail,proto3" json:"user_detail,omitempty"`
	MerchantDetail *MerchantAuthServiceDetail `protobuf:"bytes,4,opt,name=merchant_detail,json=merchantDetail,proto3" json:"merchant_detail,omitempty"`
	Company        *CompanyModel              `protobuf:"bytes,5,opt,name=company,proto3" json:"company,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *MerchantAuthServiceMeResponse) Reset() {
	*x = MerchantAuthServiceMeResponse{}
	mi := &file_auth_v1_merchant_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceMeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceMeResponse) ProtoMessage() {}

func (x *MerchantAuthServiceMeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceMeResponse.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceMeResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{16}
}

func (x *MerchantAuthServiceMeResponse) GetPaths() []string {
	if x != nil {
		return x.Paths
	}
	return nil
}

func (x *MerchantAuthServiceMeResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantAuthServiceMeResponse) GetUserDetail() *MerchantMeUserDetails {
	if x != nil {
		return x.UserDetail
	}
	return nil
}

func (x *MerchantAuthServiceMeResponse) GetMerchantDetail() *MerchantAuthServiceDetail {
	if x != nil {
		return x.MerchantDetail
	}
	return nil
}

func (x *MerchantAuthServiceMeResponse) GetCompany() *CompanyModel {
	if x != nil {
		return x.Company
	}
	return nil
}

type MerchantMeUserDetails struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FirstName     string                 `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName      string                 `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	PhoneNumber   string                 `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Street        string                 `protobuf:"bytes,5,opt,name=street,proto3" json:"street,omitempty"`
	IdState       int64                  `protobuf:"varint,6,opt,name=id_state,json=idState,proto3" json:"id_state,omitempty"`
	IdCity        int64                  `protobuf:"varint,7,opt,name=id_city,json=idCity,proto3" json:"id_city,omitempty"`
	IdCountry     int64                  `protobuf:"varint,8,opt,name=id_country,json=idCountry,proto3" json:"id_country,omitempty"`
	IdCompany     string                 `protobuf:"bytes,9,opt,name=id_company,json=idCompany,proto3" json:"id_company,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantMeUserDetails) Reset() {
	*x = MerchantMeUserDetails{}
	mi := &file_auth_v1_merchant_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantMeUserDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantMeUserDetails) ProtoMessage() {}

func (x *MerchantMeUserDetails) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantMeUserDetails.ProtoReflect.Descriptor instead.
func (*MerchantMeUserDetails) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{17}
}

func (x *MerchantMeUserDetails) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *MerchantMeUserDetails) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *MerchantMeUserDetails) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *MerchantMeUserDetails) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *MerchantMeUserDetails) GetStreet() string {
	if x != nil {
		return x.Street
	}
	return ""
}

func (x *MerchantMeUserDetails) GetIdState() int64 {
	if x != nil {
		return x.IdState
	}
	return 0
}

func (x *MerchantMeUserDetails) GetIdCity() int64 {
	if x != nil {
		return x.IdCity
	}
	return 0
}

func (x *MerchantMeUserDetails) GetIdCountry() int64 {
	if x != nil {
		return x.IdCountry
	}
	return 0
}

func (x *MerchantMeUserDetails) GetIdCompany() string {
	if x != nil {
		return x.IdCompany
	}
	return ""
}

type MerchantAuthServiceDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Domain        string                 `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantAuthServiceDetail) Reset() {
	*x = MerchantAuthServiceDetail{}
	mi := &file_auth_v1_merchant_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceDetail) ProtoMessage() {}

func (x *MerchantAuthServiceDetail) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceDetail.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceDetail) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{18}
}

func (x *MerchantAuthServiceDetail) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *MerchantAuthServiceDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type MerchantAuthServiceRefreshTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RefreshToken  string                 `protobuf:"bytes,1,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	DeviceId      string                 `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantAuthServiceRefreshTokenRequest) Reset() {
	*x = MerchantAuthServiceRefreshTokenRequest{}
	mi := &file_auth_v1_merchant_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceRefreshTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceRefreshTokenRequest) ProtoMessage() {}

func (x *MerchantAuthServiceRefreshTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceRefreshTokenRequest.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceRefreshTokenRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{19}
}

func (x *MerchantAuthServiceRefreshTokenRequest) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *MerchantAuthServiceRefreshTokenRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type MerchantAuthServiceRefreshTokenResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,3,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantAuthServiceRefreshTokenResponse) Reset() {
	*x = MerchantAuthServiceRefreshTokenResponse{}
	mi := &file_auth_v1_merchant_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceRefreshTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceRefreshTokenResponse) ProtoMessage() {}

func (x *MerchantAuthServiceRefreshTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceRefreshTokenResponse.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceRefreshTokenResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{20}
}

func (x *MerchantAuthServiceRefreshTokenResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantAuthServiceRefreshTokenResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *MerchantAuthServiceRefreshTokenResponse) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

// -------------------------------------
type MerchantAuthServiceForgotPasswordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	Domain        string                 `protobuf:"bytes,2,opt,name=domain,proto3" json:"domain,omitempty"`
	Otp           string                 `protobuf:"bytes,3,opt,name=otp,proto3" json:"otp,omitempty"`
	NewPassword   string                 `protobuf:"bytes,4,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantAuthServiceForgotPasswordRequest) Reset() {
	*x = MerchantAuthServiceForgotPasswordRequest{}
	mi := &file_auth_v1_merchant_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceForgotPasswordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceForgotPasswordRequest) ProtoMessage() {}

func (x *MerchantAuthServiceForgotPasswordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceForgotPasswordRequest.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceForgotPasswordRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{21}
}

func (x *MerchantAuthServiceForgotPasswordRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *MerchantAuthServiceForgotPasswordRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *MerchantAuthServiceForgotPasswordRequest) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *MerchantAuthServiceForgotPasswordRequest) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

type MerchantAuthServiceForgotPasswordResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantAuthServiceForgotPasswordResponse) Reset() {
	*x = MerchantAuthServiceForgotPasswordResponse{}
	mi := &file_auth_v1_merchant_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantAuthServiceForgotPasswordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantAuthServiceForgotPasswordResponse) ProtoMessage() {}

func (x *MerchantAuthServiceForgotPasswordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_merchant_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantAuthServiceForgotPasswordResponse.ProtoReflect.Descriptor instead.
func (*MerchantAuthServiceForgotPasswordResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_merchant_proto_rawDescGZIP(), []int{22}
}

func (x *MerchantAuthServiceForgotPasswordResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_auth_v1_merchant_proto protoreflect.FileDescriptor

const file_auth_v1_merchant_proto_rawDesc = "" +
	"\n" +
	"\x16auth/v1/merchant.proto\x12\aauth.v1\x1a\x1dauth/v1/backoffice_auth.proto\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\"p\n" +
	"(MerchantAuthServiceChangePasswordRequest\x12!\n" +
	"\fold_password\x18\x01 \x01(\tR\voldPassword\x12!\n" +
	"\fnew_password\x18\x02 \x01(\tR\vnewPassword\"Z\n" +
	")MerchantAuthServiceChangePasswordResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x9c\x01\n" +
	"#MerchantAuthServiceFetchRoleRequest\x12\x17\n" +
	"\aid_role\x18\x01 \x01(\tR\x06idRole\x12\x1f\n" +
	"\vname_search\x18\x02 \x01(\tR\n" +
	"nameSearch\x12;\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xc5\x01\n" +
	"$MerchantAuthServiceFetchRoleResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x120\n" +
	"\x05items\x18\x03 \x03(\v2\x1a.auth.v1.MerchantRoleModelR\x05items\"@\n" +
	"\x11MerchantRoleModel\x12\x17\n" +
	"\aid_role\x18\x01 \x01(\tR\x06idRole\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"f\n" +
	"$MerchantAuthServiceUpdateUserRequest\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x12%\n" +
	"\x05state\x18\x02 \x01(\v2\x0f.utils.v1.StateR\x05state\"V\n" +
	"%MerchantAuthServiceUpdateUserResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xde\x01\n" +
	"#MerchantAuthServiceFetchUserRequest\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x12\x17\n" +
	"\aid_role\x18\x02 \x01(\tR\x06idRole\x12!\n" +
	"\femail_search\x18\x03 \x01(\tR\vemailSearch\x12%\n" +
	"\x05state\x18\x04 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xcc\x01\n" +
	"$MerchantAuthServiceFetchUserResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x127\n" +
	"\x05users\x18\x02 \x03(\v2!.auth.v1.MerchantServiceUserModelR\x05users\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\xc0\x01\n" +
	"\x18MerchantServiceUserModel\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x122\n" +
	"\x04role\x18\x02 \x01(\v2\x1e.auth.v1.MerchantUserRoleModelR\x04role\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12$\n" +
	"\x0eis_enable_totp\x18\x04 \x01(\bR\fisEnableTotp\x12\x1b\n" +
	"\tis_active\x18\x05 \x01(\bR\bisActive\"a\n" +
	"\x15MerchantUserRoleModel\x12\x17\n" +
	"\aid_role\x18\x01 \x01(\tR\x06idRole\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1b\n" +
	"\tis_active\x18\x03 \x01(\bR\bisActive\"T\n" +
	" MerchantAuthServiceSignUpRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\"R\n" +
	"!MerchantAuthServiceSignUpResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x9a\x01\n" +
	"\x1fMerchantAuthServiceLoginRequest\x12\x16\n" +
	"\x06domain\x18\x01 \x01(\tR\x06domain\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\x12\x1a\n" +
	"\bpassword\x18\x03 \x01(\tR\bpassword\x12\x10\n" +
	"\x03otp\x18\x04 \x01(\tR\x03otp\x12\x1b\n" +
	"\tdevice_id\x18\x05 \x01(\tR\bdeviceId\"\x8c\x01\n" +
	" MerchantAuthServiceLoginResponse\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12#\n" +
	"\rrefresh_token\x18\x02 \x01(\tR\frefreshToken\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x1e\n" +
	"\x1cMerchantAuthServiceMeRequest\"\xa3\x02\n" +
	"\x1dMerchantAuthServiceMeResponse\x12\x14\n" +
	"\x05paths\x18\x01 \x03(\tR\x05paths\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12?\n" +
	"\vuser_detail\x18\x03 \x01(\v2\x1e.auth.v1.MerchantMeUserDetailsR\n" +
	"userDetail\x12K\n" +
	"\x0fmerchant_detail\x18\x04 \x01(\v2\".auth.v1.MerchantAuthServiceDetailR\x0emerchantDetail\x12/\n" +
	"\acompany\x18\x05 \x01(\v2\x15.auth.v1.CompanyModelR\acompany\"\x96\x02\n" +
	"\x15MerchantMeUserDetails\x12\x1d\n" +
	"\n" +
	"first_name\x18\x01 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x02 \x01(\tR\blastName\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12!\n" +
	"\fphone_number\x18\x04 \x01(\tR\vphoneNumber\x12\x16\n" +
	"\x06street\x18\x05 \x01(\tR\x06street\x12\x19\n" +
	"\bid_state\x18\x06 \x01(\x03R\aidState\x12\x17\n" +
	"\aid_city\x18\a \x01(\x03R\x06idCity\x12\x1d\n" +
	"\n" +
	"id_country\x18\b \x01(\x03R\tidCountry\x12\x1d\n" +
	"\n" +
	"id_company\x18\t \x01(\tR\tidCompany\"G\n" +
	"\x19MerchantAuthServiceDetail\x12\x16\n" +
	"\x06domain\x18\x01 \x01(\tR\x06domain\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"j\n" +
	"&MerchantAuthServiceRefreshTokenRequest\x12#\n" +
	"\rrefresh_token\x18\x01 \x01(\tR\frefreshToken\x12\x1b\n" +
	"\tdevice_id\x18\x02 \x01(\tR\bdeviceId\"\x93\x01\n" +
	"'MerchantAuthServiceRefreshTokenResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\x12#\n" +
	"\rrefresh_token\x18\x03 \x01(\tR\frefreshToken\"\x8d\x01\n" +
	"(MerchantAuthServiceForgotPasswordRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12\x16\n" +
	"\x06domain\x18\x02 \x01(\tR\x06domain\x12\x10\n" +
	"\x03otp\x18\x03 \x01(\tR\x03otp\x12!\n" +
	"\fnew_password\x18\x04 \x01(\tR\vnewPassword\"Z\n" +
	")MerchantAuthServiceForgotPasswordResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error2\xee\x06\n" +
	"\x13MerchantAuthService\x12\\\n" +
	"\x05Login\x12(.auth.v1.MerchantAuthServiceLoginRequest\x1a).auth.v1.MerchantAuthServiceLoginResponse\x12S\n" +
	"\x02Me\x12%.auth.v1.MerchantAuthServiceMeRequest\x1a&.auth.v1.MerchantAuthServiceMeResponse\x12w\n" +
	"\x0eChangePassword\x121.auth.v1.MerchantAuthServiceChangePasswordRequest\x1a2.auth.v1.MerchantAuthServiceChangePasswordResponse\x12q\n" +
	"\fRefreshToken\x12/.auth.v1.MerchantAuthServiceRefreshTokenRequest\x1a0.auth.v1.MerchantAuthServiceRefreshTokenResponse\x12w\n" +
	"\x0eForgotPassword\x121.auth.v1.MerchantAuthServiceForgotPasswordRequest\x1a2.auth.v1.MerchantAuthServiceForgotPasswordResponse\x12h\n" +
	"\tFetchUser\x12,.auth.v1.MerchantAuthServiceFetchUserRequest\x1a-.auth.v1.MerchantAuthServiceFetchUserResponse\x12k\n" +
	"\n" +
	"UpdateUser\x12-.auth.v1.MerchantAuthServiceUpdateUserRequest\x1a..auth.v1.MerchantAuthServiceUpdateUserResponse\x12h\n" +
	"\tFetchRole\x12,.auth.v1.MerchantAuthServiceFetchRoleRequest\x1a-.auth.v1.MerchantAuthServiceFetchRoleResponseB?Z=git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1;authv1b\x06proto3"

var (
	file_auth_v1_merchant_proto_rawDescOnce sync.Once
	file_auth_v1_merchant_proto_rawDescData []byte
)

func file_auth_v1_merchant_proto_rawDescGZIP() []byte {
	file_auth_v1_merchant_proto_rawDescOnce.Do(func() {
		file_auth_v1_merchant_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_auth_v1_merchant_proto_rawDesc), len(file_auth_v1_merchant_proto_rawDesc)))
	})
	return file_auth_v1_merchant_proto_rawDescData
}

var file_auth_v1_merchant_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_auth_v1_merchant_proto_goTypes = []any{
	(*MerchantAuthServiceChangePasswordRequest)(nil),  // 0: auth.v1.MerchantAuthServiceChangePasswordRequest
	(*MerchantAuthServiceChangePasswordResponse)(nil), // 1: auth.v1.MerchantAuthServiceChangePasswordResponse
	(*MerchantAuthServiceFetchRoleRequest)(nil),       // 2: auth.v1.MerchantAuthServiceFetchRoleRequest
	(*MerchantAuthServiceFetchRoleResponse)(nil),      // 3: auth.v1.MerchantAuthServiceFetchRoleResponse
	(*MerchantRoleModel)(nil),                         // 4: auth.v1.MerchantRoleModel
	(*MerchantAuthServiceUpdateUserRequest)(nil),      // 5: auth.v1.MerchantAuthServiceUpdateUserRequest
	(*MerchantAuthServiceUpdateUserResponse)(nil),     // 6: auth.v1.MerchantAuthServiceUpdateUserResponse
	(*MerchantAuthServiceFetchUserRequest)(nil),       // 7: auth.v1.MerchantAuthServiceFetchUserRequest
	(*MerchantAuthServiceFetchUserResponse)(nil),      // 8: auth.v1.MerchantAuthServiceFetchUserResponse
	(*MerchantServiceUserModel)(nil),                  // 9: auth.v1.MerchantServiceUserModel
	(*MerchantUserRoleModel)(nil),                     // 10: auth.v1.MerchantUserRoleModel
	(*MerchantAuthServiceSignUpRequest)(nil),          // 11: auth.v1.MerchantAuthServiceSignUpRequest
	(*MerchantAuthServiceSignUpResponse)(nil),         // 12: auth.v1.MerchantAuthServiceSignUpResponse
	(*MerchantAuthServiceLoginRequest)(nil),           // 13: auth.v1.MerchantAuthServiceLoginRequest
	(*MerchantAuthServiceLoginResponse)(nil),          // 14: auth.v1.MerchantAuthServiceLoginResponse
	(*MerchantAuthServiceMeRequest)(nil),              // 15: auth.v1.MerchantAuthServiceMeRequest
	(*MerchantAuthServiceMeResponse)(nil),             // 16: auth.v1.MerchantAuthServiceMeResponse
	(*MerchantMeUserDetails)(nil),                     // 17: auth.v1.MerchantMeUserDetails
	(*MerchantAuthServiceDetail)(nil),                 // 18: auth.v1.MerchantAuthServiceDetail
	(*MerchantAuthServiceRefreshTokenRequest)(nil),    // 19: auth.v1.MerchantAuthServiceRefreshTokenRequest
	(*MerchantAuthServiceRefreshTokenResponse)(nil),   // 20: auth.v1.MerchantAuthServiceRefreshTokenResponse
	(*MerchantAuthServiceForgotPasswordRequest)(nil),  // 21: auth.v1.MerchantAuthServiceForgotPasswordRequest
	(*MerchantAuthServiceForgotPasswordResponse)(nil), // 22: auth.v1.MerchantAuthServiceForgotPasswordResponse
	(*v1.ErrorMessage)(nil),                           // 23: errmsg.v1.ErrorMessage
	(*v11.PaginationRequest)(nil),                     // 24: utils.v1.PaginationRequest
	(*v11.PaginationResponse)(nil),                    // 25: utils.v1.PaginationResponse
	(*v11.State)(nil),                                 // 26: utils.v1.State
	(*CompanyModel)(nil),                              // 27: auth.v1.CompanyModel
}
var file_auth_v1_merchant_proto_depIdxs = []int32{
	23, // 0: auth.v1.MerchantAuthServiceChangePasswordResponse.error:type_name -> errmsg.v1.ErrorMessage
	24, // 1: auth.v1.MerchantAuthServiceFetchRoleRequest.pagination:type_name -> utils.v1.PaginationRequest
	23, // 2: auth.v1.MerchantAuthServiceFetchRoleResponse.error:type_name -> errmsg.v1.ErrorMessage
	25, // 3: auth.v1.MerchantAuthServiceFetchRoleResponse.pagination:type_name -> utils.v1.PaginationResponse
	4,  // 4: auth.v1.MerchantAuthServiceFetchRoleResponse.items:type_name -> auth.v1.MerchantRoleModel
	26, // 5: auth.v1.MerchantAuthServiceUpdateUserRequest.state:type_name -> utils.v1.State
	23, // 6: auth.v1.MerchantAuthServiceUpdateUserResponse.error:type_name -> errmsg.v1.ErrorMessage
	26, // 7: auth.v1.MerchantAuthServiceFetchUserRequest.state:type_name -> utils.v1.State
	24, // 8: auth.v1.MerchantAuthServiceFetchUserRequest.pagination:type_name -> utils.v1.PaginationRequest
	23, // 9: auth.v1.MerchantAuthServiceFetchUserResponse.error:type_name -> errmsg.v1.ErrorMessage
	9,  // 10: auth.v1.MerchantAuthServiceFetchUserResponse.users:type_name -> auth.v1.MerchantServiceUserModel
	25, // 11: auth.v1.MerchantAuthServiceFetchUserResponse.pagination:type_name -> utils.v1.PaginationResponse
	10, // 12: auth.v1.MerchantServiceUserModel.role:type_name -> auth.v1.MerchantUserRoleModel
	23, // 13: auth.v1.MerchantAuthServiceSignUpResponse.error:type_name -> errmsg.v1.ErrorMessage
	23, // 14: auth.v1.MerchantAuthServiceLoginResponse.error:type_name -> errmsg.v1.ErrorMessage
	23, // 15: auth.v1.MerchantAuthServiceMeResponse.error:type_name -> errmsg.v1.ErrorMessage
	17, // 16: auth.v1.MerchantAuthServiceMeResponse.user_detail:type_name -> auth.v1.MerchantMeUserDetails
	18, // 17: auth.v1.MerchantAuthServiceMeResponse.merchant_detail:type_name -> auth.v1.MerchantAuthServiceDetail
	27, // 18: auth.v1.MerchantAuthServiceMeResponse.company:type_name -> auth.v1.CompanyModel
	23, // 19: auth.v1.MerchantAuthServiceRefreshTokenResponse.error:type_name -> errmsg.v1.ErrorMessage
	23, // 20: auth.v1.MerchantAuthServiceForgotPasswordResponse.error:type_name -> errmsg.v1.ErrorMessage
	13, // 21: auth.v1.MerchantAuthService.Login:input_type -> auth.v1.MerchantAuthServiceLoginRequest
	15, // 22: auth.v1.MerchantAuthService.Me:input_type -> auth.v1.MerchantAuthServiceMeRequest
	0,  // 23: auth.v1.MerchantAuthService.ChangePassword:input_type -> auth.v1.MerchantAuthServiceChangePasswordRequest
	19, // 24: auth.v1.MerchantAuthService.RefreshToken:input_type -> auth.v1.MerchantAuthServiceRefreshTokenRequest
	21, // 25: auth.v1.MerchantAuthService.ForgotPassword:input_type -> auth.v1.MerchantAuthServiceForgotPasswordRequest
	7,  // 26: auth.v1.MerchantAuthService.FetchUser:input_type -> auth.v1.MerchantAuthServiceFetchUserRequest
	5,  // 27: auth.v1.MerchantAuthService.UpdateUser:input_type -> auth.v1.MerchantAuthServiceUpdateUserRequest
	2,  // 28: auth.v1.MerchantAuthService.FetchRole:input_type -> auth.v1.MerchantAuthServiceFetchRoleRequest
	14, // 29: auth.v1.MerchantAuthService.Login:output_type -> auth.v1.MerchantAuthServiceLoginResponse
	16, // 30: auth.v1.MerchantAuthService.Me:output_type -> auth.v1.MerchantAuthServiceMeResponse
	1,  // 31: auth.v1.MerchantAuthService.ChangePassword:output_type -> auth.v1.MerchantAuthServiceChangePasswordResponse
	20, // 32: auth.v1.MerchantAuthService.RefreshToken:output_type -> auth.v1.MerchantAuthServiceRefreshTokenResponse
	22, // 33: auth.v1.MerchantAuthService.ForgotPassword:output_type -> auth.v1.MerchantAuthServiceForgotPasswordResponse
	8,  // 34: auth.v1.MerchantAuthService.FetchUser:output_type -> auth.v1.MerchantAuthServiceFetchUserResponse
	6,  // 35: auth.v1.MerchantAuthService.UpdateUser:output_type -> auth.v1.MerchantAuthServiceUpdateUserResponse
	3,  // 36: auth.v1.MerchantAuthService.FetchRole:output_type -> auth.v1.MerchantAuthServiceFetchRoleResponse
	29, // [29:37] is the sub-list for method output_type
	21, // [21:29] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_auth_v1_merchant_proto_init() }
func file_auth_v1_merchant_proto_init() {
	if File_auth_v1_merchant_proto != nil {
		return
	}
	file_auth_v1_backoffice_auth_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_auth_v1_merchant_proto_rawDesc), len(file_auth_v1_merchant_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_auth_v1_merchant_proto_goTypes,
		DependencyIndexes: file_auth_v1_merchant_proto_depIdxs,
		MessageInfos:      file_auth_v1_merchant_proto_msgTypes,
	}.Build()
	File_auth_v1_merchant_proto = out.File
	file_auth_v1_merchant_proto_goTypes = nil
	file_auth_v1_merchant_proto_depIdxs = nil
}
