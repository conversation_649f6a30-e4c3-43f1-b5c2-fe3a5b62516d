// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: auth/v1/customer.proto

package authv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CustomerAuthServiceChangePasswordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OldPassword   string                 `protobuf:"bytes,1,opt,name=old_password,json=oldPassword,proto3" json:"old_password,omitempty"`
	NewPassword   string                 `protobuf:"bytes,2,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAuthServiceChangePasswordRequest) Reset() {
	*x = CustomerAuthServiceChangePasswordRequest{}
	mi := &file_auth_v1_customer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAuthServiceChangePasswordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAuthServiceChangePasswordRequest) ProtoMessage() {}

func (x *CustomerAuthServiceChangePasswordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAuthServiceChangePasswordRequest.ProtoReflect.Descriptor instead.
func (*CustomerAuthServiceChangePasswordRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerAuthServiceChangePasswordRequest) GetOldPassword() string {
	if x != nil {
		return x.OldPassword
	}
	return ""
}

func (x *CustomerAuthServiceChangePasswordRequest) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

type CustomerAuthServiceChangePasswordResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAuthServiceChangePasswordResponse) Reset() {
	*x = CustomerAuthServiceChangePasswordResponse{}
	mi := &file_auth_v1_customer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAuthServiceChangePasswordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAuthServiceChangePasswordResponse) ProtoMessage() {}

func (x *CustomerAuthServiceChangePasswordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAuthServiceChangePasswordResponse.ProtoReflect.Descriptor instead.
func (*CustomerAuthServiceChangePasswordResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerAuthServiceChangePasswordResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerAuthServiceSignUpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	FirstName     string                 `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName      string                 `protobuf:"bytes,4,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	PhoneNumber   string                 `protobuf:"bytes,5,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	RefCode       string                 `protobuf:"bytes,6,opt,name=ref_code,json=refCode,proto3" json:"ref_code,omitempty"`
	Street        string                 `protobuf:"bytes,7,opt,name=street,proto3" json:"street,omitempty"`
	IdState       int64                  `protobuf:"varint,8,opt,name=id_state,json=idState,proto3" json:"id_state,omitempty"`
	IdCity        int64                  `protobuf:"varint,9,opt,name=id_city,json=idCity,proto3" json:"id_city,omitempty"`
	IdCountry     int64                  `protobuf:"varint,10,opt,name=id_country,json=idCountry,proto3" json:"id_country,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAuthServiceSignUpRequest) Reset() {
	*x = CustomerAuthServiceSignUpRequest{}
	mi := &file_auth_v1_customer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAuthServiceSignUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAuthServiceSignUpRequest) ProtoMessage() {}

func (x *CustomerAuthServiceSignUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAuthServiceSignUpRequest.ProtoReflect.Descriptor instead.
func (*CustomerAuthServiceSignUpRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{2}
}

func (x *CustomerAuthServiceSignUpRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CustomerAuthServiceSignUpRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *CustomerAuthServiceSignUpRequest) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *CustomerAuthServiceSignUpRequest) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *CustomerAuthServiceSignUpRequest) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *CustomerAuthServiceSignUpRequest) GetRefCode() string {
	if x != nil {
		return x.RefCode
	}
	return ""
}

func (x *CustomerAuthServiceSignUpRequest) GetStreet() string {
	if x != nil {
		return x.Street
	}
	return ""
}

func (x *CustomerAuthServiceSignUpRequest) GetIdState() int64 {
	if x != nil {
		return x.IdState
	}
	return 0
}

func (x *CustomerAuthServiceSignUpRequest) GetIdCity() int64 {
	if x != nil {
		return x.IdCity
	}
	return 0
}

func (x *CustomerAuthServiceSignUpRequest) GetIdCountry() int64 {
	if x != nil {
		return x.IdCountry
	}
	return 0
}

type CustomerAuthServiceSignUpResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TokenTtl      int64                  `protobuf:"varint,1,opt,name=token_ttl,json=tokenTtl,proto3" json:"token_ttl,omitempty"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAuthServiceSignUpResponse) Reset() {
	*x = CustomerAuthServiceSignUpResponse{}
	mi := &file_auth_v1_customer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAuthServiceSignUpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAuthServiceSignUpResponse) ProtoMessage() {}

func (x *CustomerAuthServiceSignUpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAuthServiceSignUpResponse.ProtoReflect.Descriptor instead.
func (*CustomerAuthServiceSignUpResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{3}
}

func (x *CustomerAuthServiceSignUpResponse) GetTokenTtl() int64 {
	if x != nil {
		return x.TokenTtl
	}
	return 0
}

func (x *CustomerAuthServiceSignUpResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerAuthServiceVerifySignUpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAuthServiceVerifySignUpRequest) Reset() {
	*x = CustomerAuthServiceVerifySignUpRequest{}
	mi := &file_auth_v1_customer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAuthServiceVerifySignUpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAuthServiceVerifySignUpRequest) ProtoMessage() {}

func (x *CustomerAuthServiceVerifySignUpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAuthServiceVerifySignUpRequest.ProtoReflect.Descriptor instead.
func (*CustomerAuthServiceVerifySignUpRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{4}
}

func (x *CustomerAuthServiceVerifySignUpRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type CustomerAuthServiceVerifySignUpResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAuthServiceVerifySignUpResponse) Reset() {
	*x = CustomerAuthServiceVerifySignUpResponse{}
	mi := &file_auth_v1_customer_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAuthServiceVerifySignUpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAuthServiceVerifySignUpResponse) ProtoMessage() {}

func (x *CustomerAuthServiceVerifySignUpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAuthServiceVerifySignUpResponse.ProtoReflect.Descriptor instead.
func (*CustomerAuthServiceVerifySignUpResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{5}
}

func (x *CustomerAuthServiceVerifySignUpResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerAuthServiceLoginRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	Otp           string                 `protobuf:"bytes,3,opt,name=otp,proto3" json:"otp,omitempty"`
	DeviceId      string                 `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAuthServiceLoginRequest) Reset() {
	*x = CustomerAuthServiceLoginRequest{}
	mi := &file_auth_v1_customer_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAuthServiceLoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAuthServiceLoginRequest) ProtoMessage() {}

func (x *CustomerAuthServiceLoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAuthServiceLoginRequest.ProtoReflect.Descriptor instead.
func (*CustomerAuthServiceLoginRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{6}
}

func (x *CustomerAuthServiceLoginRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CustomerAuthServiceLoginRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *CustomerAuthServiceLoginRequest) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *CustomerAuthServiceLoginRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type CustomerAuthServiceLoginResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,2,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAuthServiceLoginResponse) Reset() {
	*x = CustomerAuthServiceLoginResponse{}
	mi := &file_auth_v1_customer_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAuthServiceLoginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAuthServiceLoginResponse) ProtoMessage() {}

func (x *CustomerAuthServiceLoginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAuthServiceLoginResponse.ProtoReflect.Descriptor instead.
func (*CustomerAuthServiceLoginResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{7}
}

func (x *CustomerAuthServiceLoginResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *CustomerAuthServiceLoginResponse) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *CustomerAuthServiceLoginResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerAuthServiceMeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAuthServiceMeRequest) Reset() {
	*x = CustomerAuthServiceMeRequest{}
	mi := &file_auth_v1_customer_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAuthServiceMeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAuthServiceMeRequest) ProtoMessage() {}

func (x *CustomerAuthServiceMeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAuthServiceMeRequest.ProtoReflect.Descriptor instead.
func (*CustomerAuthServiceMeRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{8}
}

type CustomerAuthServiceMeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserDetail    *CustomerMeUserDetails `protobuf:"bytes,1,opt,name=user_detail,json=userDetail,proto3" json:"user_detail,omitempty"`
	Paths         []string               `protobuf:"bytes,2,rep,name=paths,proto3" json:"paths,omitempty"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAuthServiceMeResponse) Reset() {
	*x = CustomerAuthServiceMeResponse{}
	mi := &file_auth_v1_customer_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAuthServiceMeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAuthServiceMeResponse) ProtoMessage() {}

func (x *CustomerAuthServiceMeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAuthServiceMeResponse.ProtoReflect.Descriptor instead.
func (*CustomerAuthServiceMeResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{9}
}

func (x *CustomerAuthServiceMeResponse) GetUserDetail() *CustomerMeUserDetails {
	if x != nil {
		return x.UserDetail
	}
	return nil
}

func (x *CustomerAuthServiceMeResponse) GetPaths() []string {
	if x != nil {
		return x.Paths
	}
	return nil
}

func (x *CustomerAuthServiceMeResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerMeUserDetails struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FirstName     string                 `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName      string                 `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	PhoneNumber   string                 `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	RefCode       string                 `protobuf:"bytes,5,opt,name=ref_code,json=refCode,proto3" json:"ref_code,omitempty"`
	UserRefId     string                 `protobuf:"bytes,6,opt,name=user_ref_id,json=userRefId,proto3" json:"user_ref_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerMeUserDetails) Reset() {
	*x = CustomerMeUserDetails{}
	mi := &file_auth_v1_customer_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerMeUserDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerMeUserDetails) ProtoMessage() {}

func (x *CustomerMeUserDetails) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerMeUserDetails.ProtoReflect.Descriptor instead.
func (*CustomerMeUserDetails) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{10}
}

func (x *CustomerMeUserDetails) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *CustomerMeUserDetails) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *CustomerMeUserDetails) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CustomerMeUserDetails) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *CustomerMeUserDetails) GetRefCode() string {
	if x != nil {
		return x.RefCode
	}
	return ""
}

func (x *CustomerMeUserDetails) GetUserRefId() string {
	if x != nil {
		return x.UserRefId
	}
	return ""
}

type CustomerAuthServiceRefreshTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RefreshToken  string                 `protobuf:"bytes,1,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	DeviceId      string                 `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAuthServiceRefreshTokenRequest) Reset() {
	*x = CustomerAuthServiceRefreshTokenRequest{}
	mi := &file_auth_v1_customer_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAuthServiceRefreshTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAuthServiceRefreshTokenRequest) ProtoMessage() {}

func (x *CustomerAuthServiceRefreshTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAuthServiceRefreshTokenRequest.ProtoReflect.Descriptor instead.
func (*CustomerAuthServiceRefreshTokenRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{11}
}

func (x *CustomerAuthServiceRefreshTokenRequest) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *CustomerAuthServiceRefreshTokenRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type CustomerAuthServiceRefreshTokenResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,3,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAuthServiceRefreshTokenResponse) Reset() {
	*x = CustomerAuthServiceRefreshTokenResponse{}
	mi := &file_auth_v1_customer_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAuthServiceRefreshTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAuthServiceRefreshTokenResponse) ProtoMessage() {}

func (x *CustomerAuthServiceRefreshTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAuthServiceRefreshTokenResponse.ProtoReflect.Descriptor instead.
func (*CustomerAuthServiceRefreshTokenResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{12}
}

func (x *CustomerAuthServiceRefreshTokenResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerAuthServiceRefreshTokenResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *CustomerAuthServiceRefreshTokenResponse) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

// -------------------------------------
type CustomerAuthServiceForgotPasswordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	Domain        string                 `protobuf:"bytes,2,opt,name=domain,proto3" json:"domain,omitempty"`
	Otp           string                 `protobuf:"bytes,3,opt,name=otp,proto3" json:"otp,omitempty"`
	NewPassword   string                 `protobuf:"bytes,4,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAuthServiceForgotPasswordRequest) Reset() {
	*x = CustomerAuthServiceForgotPasswordRequest{}
	mi := &file_auth_v1_customer_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAuthServiceForgotPasswordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAuthServiceForgotPasswordRequest) ProtoMessage() {}

func (x *CustomerAuthServiceForgotPasswordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAuthServiceForgotPasswordRequest.ProtoReflect.Descriptor instead.
func (*CustomerAuthServiceForgotPasswordRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{13}
}

func (x *CustomerAuthServiceForgotPasswordRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CustomerAuthServiceForgotPasswordRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *CustomerAuthServiceForgotPasswordRequest) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *CustomerAuthServiceForgotPasswordRequest) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

type CustomerAuthServiceForgotPasswordResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAuthServiceForgotPasswordResponse) Reset() {
	*x = CustomerAuthServiceForgotPasswordResponse{}
	mi := &file_auth_v1_customer_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAuthServiceForgotPasswordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAuthServiceForgotPasswordResponse) ProtoMessage() {}

func (x *CustomerAuthServiceForgotPasswordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAuthServiceForgotPasswordResponse.ProtoReflect.Descriptor instead.
func (*CustomerAuthServiceForgotPasswordResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{14}
}

func (x *CustomerAuthServiceForgotPasswordResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

// -------------------------------------
type CustomerAuthServiceLoginOAuthRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OauthToken    string                 `protobuf:"bytes,1,opt,name=oauth_token,json=oauthToken,proto3" json:"oauth_token,omitempty"`
	DeviceId      string                 `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAuthServiceLoginOAuthRequest) Reset() {
	*x = CustomerAuthServiceLoginOAuthRequest{}
	mi := &file_auth_v1_customer_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAuthServiceLoginOAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAuthServiceLoginOAuthRequest) ProtoMessage() {}

func (x *CustomerAuthServiceLoginOAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAuthServiceLoginOAuthRequest.ProtoReflect.Descriptor instead.
func (*CustomerAuthServiceLoginOAuthRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{15}
}

func (x *CustomerAuthServiceLoginOAuthRequest) GetOauthToken() string {
	if x != nil {
		return x.OauthToken
	}
	return ""
}

func (x *CustomerAuthServiceLoginOAuthRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type CustomerAuthServiceLoginOAuthResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,3,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAuthServiceLoginOAuthResponse) Reset() {
	*x = CustomerAuthServiceLoginOAuthResponse{}
	mi := &file_auth_v1_customer_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAuthServiceLoginOAuthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAuthServiceLoginOAuthResponse) ProtoMessage() {}

func (x *CustomerAuthServiceLoginOAuthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAuthServiceLoginOAuthResponse.ProtoReflect.Descriptor instead.
func (*CustomerAuthServiceLoginOAuthResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{16}
}

func (x *CustomerAuthServiceLoginOAuthResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerAuthServiceLoginOAuthResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *CustomerAuthServiceLoginOAuthResponse) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

// -------------------------------------
type CustomerAuthServiceFetchOAuthAppRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAuthServiceFetchOAuthAppRequest) Reset() {
	*x = CustomerAuthServiceFetchOAuthAppRequest{}
	mi := &file_auth_v1_customer_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAuthServiceFetchOAuthAppRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAuthServiceFetchOAuthAppRequest) ProtoMessage() {}

func (x *CustomerAuthServiceFetchOAuthAppRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAuthServiceFetchOAuthAppRequest.ProtoReflect.Descriptor instead.
func (*CustomerAuthServiceFetchOAuthAppRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{17}
}

type CustomerAuthServiceFetchOAuthAppResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	ClientId      string                 `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerAuthServiceFetchOAuthAppResponse) Reset() {
	*x = CustomerAuthServiceFetchOAuthAppResponse{}
	mi := &file_auth_v1_customer_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerAuthServiceFetchOAuthAppResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAuthServiceFetchOAuthAppResponse) ProtoMessage() {}

func (x *CustomerAuthServiceFetchOAuthAppResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_customer_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAuthServiceFetchOAuthAppResponse.ProtoReflect.Descriptor instead.
func (*CustomerAuthServiceFetchOAuthAppResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_customer_proto_rawDescGZIP(), []int{18}
}

func (x *CustomerAuthServiceFetchOAuthAppResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerAuthServiceFetchOAuthAppResponse) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

var File_auth_v1_customer_proto protoreflect.FileDescriptor

const file_auth_v1_customer_proto_rawDesc = "" +
	"\n" +
	"\x16auth/v1/customer.proto\x12\aauth.v1\x1a\x18errmsg/v1/errormsg.proto\"p\n" +
	"(CustomerAuthServiceChangePasswordRequest\x12!\n" +
	"\fold_password\x18\x01 \x01(\tR\voldPassword\x12!\n" +
	"\fnew_password\x18\x02 \x01(\tR\vnewPassword\"Z\n" +
	")CustomerAuthServiceChangePasswordResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xb9\x02\n" +
	" CustomerAuthServiceSignUpRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x12\x1d\n" +
	"\n" +
	"first_name\x18\x03 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x04 \x01(\tR\blastName\x12!\n" +
	"\fphone_number\x18\x05 \x01(\tR\vphoneNumber\x12\x19\n" +
	"\bref_code\x18\x06 \x01(\tR\arefCode\x12\x16\n" +
	"\x06street\x18\a \x01(\tR\x06street\x12\x19\n" +
	"\bid_state\x18\b \x01(\x03R\aidState\x12\x17\n" +
	"\aid_city\x18\t \x01(\x03R\x06idCity\x12\x1d\n" +
	"\n" +
	"id_country\x18\n" +
	" \x01(\x03R\tidCountry\"o\n" +
	"!CustomerAuthServiceSignUpResponse\x12\x1b\n" +
	"\ttoken_ttl\x18\x01 \x01(\x03R\btokenTtl\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\">\n" +
	"&CustomerAuthServiceVerifySignUpRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\"X\n" +
	"'CustomerAuthServiceVerifySignUpResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x82\x01\n" +
	"\x1fCustomerAuthServiceLoginRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x12\x10\n" +
	"\x03otp\x18\x03 \x01(\tR\x03otp\x12\x1b\n" +
	"\tdevice_id\x18\x04 \x01(\tR\bdeviceId\"\x8c\x01\n" +
	" CustomerAuthServiceLoginResponse\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12#\n" +
	"\rrefresh_token\x18\x02 \x01(\tR\frefreshToken\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x1e\n" +
	"\x1cCustomerAuthServiceMeRequest\"\xa5\x01\n" +
	"\x1dCustomerAuthServiceMeResponse\x12?\n" +
	"\vuser_detail\x18\x01 \x01(\v2\x1e.auth.v1.CustomerMeUserDetailsR\n" +
	"userDetail\x12\x14\n" +
	"\x05paths\x18\x02 \x03(\tR\x05paths\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xc7\x01\n" +
	"\x15CustomerMeUserDetails\x12\x1d\n" +
	"\n" +
	"first_name\x18\x01 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x02 \x01(\tR\blastName\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12!\n" +
	"\fphone_number\x18\x04 \x01(\tR\vphoneNumber\x12\x19\n" +
	"\bref_code\x18\x05 \x01(\tR\arefCode\x12\x1e\n" +
	"\vuser_ref_id\x18\x06 \x01(\tR\tuserRefId\"j\n" +
	"&CustomerAuthServiceRefreshTokenRequest\x12#\n" +
	"\rrefresh_token\x18\x01 \x01(\tR\frefreshToken\x12\x1b\n" +
	"\tdevice_id\x18\x02 \x01(\tR\bdeviceId\"\x93\x01\n" +
	"'CustomerAuthServiceRefreshTokenResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\x12#\n" +
	"\rrefresh_token\x18\x03 \x01(\tR\frefreshToken\"\x8d\x01\n" +
	"(CustomerAuthServiceForgotPasswordRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12\x16\n" +
	"\x06domain\x18\x02 \x01(\tR\x06domain\x12\x10\n" +
	"\x03otp\x18\x03 \x01(\tR\x03otp\x12!\n" +
	"\fnew_password\x18\x04 \x01(\tR\vnewPassword\"Z\n" +
	")CustomerAuthServiceForgotPasswordResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"d\n" +
	"$CustomerAuthServiceLoginOAuthRequest\x12\x1f\n" +
	"\voauth_token\x18\x01 \x01(\tR\n" +
	"oauthToken\x12\x1b\n" +
	"\tdevice_id\x18\x02 \x01(\tR\bdeviceId\"\x91\x01\n" +
	"%CustomerAuthServiceLoginOAuthResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\x12#\n" +
	"\rrefresh_token\x18\x03 \x01(\tR\frefreshToken\")\n" +
	"'CustomerAuthServiceFetchOAuthAppRequest\"v\n" +
	"(CustomerAuthServiceFetchOAuthAppResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12\x1b\n" +
	"\tclient_id\x18\x02 \x01(\tR\bclientId2\xe4\a\n" +
	"\x13CustomerAuthService\x12_\n" +
	"\x06SignUp\x12).auth.v1.CustomerAuthServiceSignUpRequest\x1a*.auth.v1.CustomerAuthServiceSignUpResponse\x12q\n" +
	"\fVerifySignUp\x12/.auth.v1.CustomerAuthServiceVerifySignUpRequest\x1a0.auth.v1.CustomerAuthServiceVerifySignUpResponse\x12\\\n" +
	"\x05Login\x12(.auth.v1.CustomerAuthServiceLoginRequest\x1a).auth.v1.CustomerAuthServiceLoginResponse\x12S\n" +
	"\x02Me\x12%.auth.v1.CustomerAuthServiceMeRequest\x1a&.auth.v1.CustomerAuthServiceMeResponse\x12w\n" +
	"\x0eChangePassword\x121.auth.v1.CustomerAuthServiceChangePasswordRequest\x1a2.auth.v1.CustomerAuthServiceChangePasswordResponse\x12q\n" +
	"\fRefreshToken\x12/.auth.v1.CustomerAuthServiceRefreshTokenRequest\x1a0.auth.v1.CustomerAuthServiceRefreshTokenResponse\x12w\n" +
	"\x0eForgotPassword\x121.auth.v1.CustomerAuthServiceForgotPasswordRequest\x1a2.auth.v1.CustomerAuthServiceForgotPasswordResponse\x12k\n" +
	"\n" +
	"LoginOAuth\x12-.auth.v1.CustomerAuthServiceLoginOAuthRequest\x1a..auth.v1.CustomerAuthServiceLoginOAuthResponse\x12t\n" +
	"\rFetchOAuthApp\x120.auth.v1.CustomerAuthServiceFetchOAuthAppRequest\x1a1.auth.v1.CustomerAuthServiceFetchOAuthAppResponseB?Z=git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1;authv1b\x06proto3"

var (
	file_auth_v1_customer_proto_rawDescOnce sync.Once
	file_auth_v1_customer_proto_rawDescData []byte
)

func file_auth_v1_customer_proto_rawDescGZIP() []byte {
	file_auth_v1_customer_proto_rawDescOnce.Do(func() {
		file_auth_v1_customer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_auth_v1_customer_proto_rawDesc), len(file_auth_v1_customer_proto_rawDesc)))
	})
	return file_auth_v1_customer_proto_rawDescData
}

var file_auth_v1_customer_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_auth_v1_customer_proto_goTypes = []any{
	(*CustomerAuthServiceChangePasswordRequest)(nil),  // 0: auth.v1.CustomerAuthServiceChangePasswordRequest
	(*CustomerAuthServiceChangePasswordResponse)(nil), // 1: auth.v1.CustomerAuthServiceChangePasswordResponse
	(*CustomerAuthServiceSignUpRequest)(nil),          // 2: auth.v1.CustomerAuthServiceSignUpRequest
	(*CustomerAuthServiceSignUpResponse)(nil),         // 3: auth.v1.CustomerAuthServiceSignUpResponse
	(*CustomerAuthServiceVerifySignUpRequest)(nil),    // 4: auth.v1.CustomerAuthServiceVerifySignUpRequest
	(*CustomerAuthServiceVerifySignUpResponse)(nil),   // 5: auth.v1.CustomerAuthServiceVerifySignUpResponse
	(*CustomerAuthServiceLoginRequest)(nil),           // 6: auth.v1.CustomerAuthServiceLoginRequest
	(*CustomerAuthServiceLoginResponse)(nil),          // 7: auth.v1.CustomerAuthServiceLoginResponse
	(*CustomerAuthServiceMeRequest)(nil),              // 8: auth.v1.CustomerAuthServiceMeRequest
	(*CustomerAuthServiceMeResponse)(nil),             // 9: auth.v1.CustomerAuthServiceMeResponse
	(*CustomerMeUserDetails)(nil),                     // 10: auth.v1.CustomerMeUserDetails
	(*CustomerAuthServiceRefreshTokenRequest)(nil),    // 11: auth.v1.CustomerAuthServiceRefreshTokenRequest
	(*CustomerAuthServiceRefreshTokenResponse)(nil),   // 12: auth.v1.CustomerAuthServiceRefreshTokenResponse
	(*CustomerAuthServiceForgotPasswordRequest)(nil),  // 13: auth.v1.CustomerAuthServiceForgotPasswordRequest
	(*CustomerAuthServiceForgotPasswordResponse)(nil), // 14: auth.v1.CustomerAuthServiceForgotPasswordResponse
	(*CustomerAuthServiceLoginOAuthRequest)(nil),      // 15: auth.v1.CustomerAuthServiceLoginOAuthRequest
	(*CustomerAuthServiceLoginOAuthResponse)(nil),     // 16: auth.v1.CustomerAuthServiceLoginOAuthResponse
	(*CustomerAuthServiceFetchOAuthAppRequest)(nil),   // 17: auth.v1.CustomerAuthServiceFetchOAuthAppRequest
	(*CustomerAuthServiceFetchOAuthAppResponse)(nil),  // 18: auth.v1.CustomerAuthServiceFetchOAuthAppResponse
	(*v1.ErrorMessage)(nil),                           // 19: errmsg.v1.ErrorMessage
}
var file_auth_v1_customer_proto_depIdxs = []int32{
	19, // 0: auth.v1.CustomerAuthServiceChangePasswordResponse.error:type_name -> errmsg.v1.ErrorMessage
	19, // 1: auth.v1.CustomerAuthServiceSignUpResponse.error:type_name -> errmsg.v1.ErrorMessage
	19, // 2: auth.v1.CustomerAuthServiceVerifySignUpResponse.error:type_name -> errmsg.v1.ErrorMessage
	19, // 3: auth.v1.CustomerAuthServiceLoginResponse.error:type_name -> errmsg.v1.ErrorMessage
	10, // 4: auth.v1.CustomerAuthServiceMeResponse.user_detail:type_name -> auth.v1.CustomerMeUserDetails
	19, // 5: auth.v1.CustomerAuthServiceMeResponse.error:type_name -> errmsg.v1.ErrorMessage
	19, // 6: auth.v1.CustomerAuthServiceRefreshTokenResponse.error:type_name -> errmsg.v1.ErrorMessage
	19, // 7: auth.v1.CustomerAuthServiceForgotPasswordResponse.error:type_name -> errmsg.v1.ErrorMessage
	19, // 8: auth.v1.CustomerAuthServiceLoginOAuthResponse.error:type_name -> errmsg.v1.ErrorMessage
	19, // 9: auth.v1.CustomerAuthServiceFetchOAuthAppResponse.error:type_name -> errmsg.v1.ErrorMessage
	2,  // 10: auth.v1.CustomerAuthService.SignUp:input_type -> auth.v1.CustomerAuthServiceSignUpRequest
	4,  // 11: auth.v1.CustomerAuthService.VerifySignUp:input_type -> auth.v1.CustomerAuthServiceVerifySignUpRequest
	6,  // 12: auth.v1.CustomerAuthService.Login:input_type -> auth.v1.CustomerAuthServiceLoginRequest
	8,  // 13: auth.v1.CustomerAuthService.Me:input_type -> auth.v1.CustomerAuthServiceMeRequest
	0,  // 14: auth.v1.CustomerAuthService.ChangePassword:input_type -> auth.v1.CustomerAuthServiceChangePasswordRequest
	11, // 15: auth.v1.CustomerAuthService.RefreshToken:input_type -> auth.v1.CustomerAuthServiceRefreshTokenRequest
	13, // 16: auth.v1.CustomerAuthService.ForgotPassword:input_type -> auth.v1.CustomerAuthServiceForgotPasswordRequest
	15, // 17: auth.v1.CustomerAuthService.LoginOAuth:input_type -> auth.v1.CustomerAuthServiceLoginOAuthRequest
	17, // 18: auth.v1.CustomerAuthService.FetchOAuthApp:input_type -> auth.v1.CustomerAuthServiceFetchOAuthAppRequest
	3,  // 19: auth.v1.CustomerAuthService.SignUp:output_type -> auth.v1.CustomerAuthServiceSignUpResponse
	5,  // 20: auth.v1.CustomerAuthService.VerifySignUp:output_type -> auth.v1.CustomerAuthServiceVerifySignUpResponse
	7,  // 21: auth.v1.CustomerAuthService.Login:output_type -> auth.v1.CustomerAuthServiceLoginResponse
	9,  // 22: auth.v1.CustomerAuthService.Me:output_type -> auth.v1.CustomerAuthServiceMeResponse
	1,  // 23: auth.v1.CustomerAuthService.ChangePassword:output_type -> auth.v1.CustomerAuthServiceChangePasswordResponse
	12, // 24: auth.v1.CustomerAuthService.RefreshToken:output_type -> auth.v1.CustomerAuthServiceRefreshTokenResponse
	14, // 25: auth.v1.CustomerAuthService.ForgotPassword:output_type -> auth.v1.CustomerAuthServiceForgotPasswordResponse
	16, // 26: auth.v1.CustomerAuthService.LoginOAuth:output_type -> auth.v1.CustomerAuthServiceLoginOAuthResponse
	18, // 27: auth.v1.CustomerAuthService.FetchOAuthApp:output_type -> auth.v1.CustomerAuthServiceFetchOAuthAppResponse
	19, // [19:28] is the sub-list for method output_type
	10, // [10:19] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_auth_v1_customer_proto_init() }
func file_auth_v1_customer_proto_init() {
	if File_auth_v1_customer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_auth_v1_customer_proto_rawDesc), len(file_auth_v1_customer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_auth_v1_customer_proto_goTypes,
		DependencyIndexes: file_auth_v1_customer_proto_depIdxs,
		MessageInfos:      file_auth_v1_customer_proto_msgTypes,
	}.Build()
	File_auth_v1_customer_proto = out.File
	file_auth_v1_customer_proto_goTypes = nil
	file_auth_v1_customer_proto_depIdxs = nil
}
