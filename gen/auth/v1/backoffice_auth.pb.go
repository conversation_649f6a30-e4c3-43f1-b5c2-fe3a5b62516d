// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: auth/v1/backoffice_auth.proto

package authv1

import (
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackofficeAuthServiceChangePasswordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OldPassword   string                 `protobuf:"bytes,1,opt,name=old_password,json=oldPassword,proto3" json:"old_password,omitempty"`
	NewPassword   string                 `protobuf:"bytes,2,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceChangePasswordRequest) Reset() {
	*x = BackofficeAuthServiceChangePasswordRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceChangePasswordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceChangePasswordRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceChangePasswordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceChangePasswordRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceChangePasswordRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{0}
}

func (x *BackofficeAuthServiceChangePasswordRequest) GetOldPassword() string {
	if x != nil {
		return x.OldPassword
	}
	return ""
}

func (x *BackofficeAuthServiceChangePasswordRequest) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

type BackofficeAuthServiceChangePasswordResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceChangePasswordResponse) Reset() {
	*x = BackofficeAuthServiceChangePasswordResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceChangePasswordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceChangePasswordResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceChangePasswordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceChangePasswordResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceChangePasswordResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{1}
}

func (x *BackofficeAuthServiceChangePasswordResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeAuthServiceMeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceMeRequest) Reset() {
	*x = BackofficeAuthServiceMeRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceMeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceMeRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceMeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceMeRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceMeRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{2}
}

type BackofficeAuthServiceMeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Paths         []string               `protobuf:"bytes,2,rep,name=paths,proto3" json:"paths,omitempty"`
	UserDetail    *BackofficeUserDetail  `protobuf:"bytes,3,opt,name=user_detail,json=userDetail,proto3" json:"user_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceMeResponse) Reset() {
	*x = BackofficeAuthServiceMeResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceMeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceMeResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceMeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceMeResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceMeResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{3}
}

func (x *BackofficeAuthServiceMeResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeAuthServiceMeResponse) GetPaths() []string {
	if x != nil {
		return x.Paths
	}
	return nil
}

func (x *BackofficeAuthServiceMeResponse) GetUserDetail() *BackofficeUserDetail {
	if x != nil {
		return x.UserDetail
	}
	return nil
}

type BackofficeUserDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FirstName     string                 `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName      string                 `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	PhoneNumber   string                 `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	RefCode       string                 `protobuf:"bytes,5,opt,name=ref_code,json=refCode,proto3" json:"ref_code,omitempty"`
	UserRefId     string                 `protobuf:"bytes,6,opt,name=user_ref_id,json=userRefId,proto3" json:"user_ref_id,omitempty"`
	Street        string                 `protobuf:"bytes,7,opt,name=street,proto3" json:"street,omitempty"`
	IdState       int64                  `protobuf:"varint,8,opt,name=id_state,json=idState,proto3" json:"id_state,omitempty"`
	IdCity        int64                  `protobuf:"varint,9,opt,name=id_city,json=idCity,proto3" json:"id_city,omitempty"`
	IdCountry     int64                  `protobuf:"varint,10,opt,name=id_country,json=idCountry,proto3" json:"id_country,omitempty"`
	IdCompany     string                 `protobuf:"bytes,11,opt,name=id_company,json=idCompany,proto3" json:"id_company,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeUserDetail) Reset() {
	*x = BackofficeUserDetail{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeUserDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeUserDetail) ProtoMessage() {}

func (x *BackofficeUserDetail) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeUserDetail.ProtoReflect.Descriptor instead.
func (*BackofficeUserDetail) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{4}
}

func (x *BackofficeUserDetail) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *BackofficeUserDetail) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *BackofficeUserDetail) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *BackofficeUserDetail) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *BackofficeUserDetail) GetRefCode() string {
	if x != nil {
		return x.RefCode
	}
	return ""
}

func (x *BackofficeUserDetail) GetUserRefId() string {
	if x != nil {
		return x.UserRefId
	}
	return ""
}

func (x *BackofficeUserDetail) GetStreet() string {
	if x != nil {
		return x.Street
	}
	return ""
}

func (x *BackofficeUserDetail) GetIdState() int64 {
	if x != nil {
		return x.IdState
	}
	return 0
}

func (x *BackofficeUserDetail) GetIdCity() int64 {
	if x != nil {
		return x.IdCity
	}
	return 0
}

func (x *BackofficeUserDetail) GetIdCountry() int64 {
	if x != nil {
		return x.IdCountry
	}
	return 0
}

func (x *BackofficeUserDetail) GetIdCompany() string {
	if x != nil {
		return x.IdCompany
	}
	return ""
}

type BackofficeAuthServiceReloadEnforcerRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceReloadEnforcerRequest) Reset() {
	*x = BackofficeAuthServiceReloadEnforcerRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceReloadEnforcerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceReloadEnforcerRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceReloadEnforcerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceReloadEnforcerRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceReloadEnforcerRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{5}
}

type BackofficeAuthServiceReloadEnforcerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceReloadEnforcerResponse) Reset() {
	*x = BackofficeAuthServiceReloadEnforcerResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceReloadEnforcerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceReloadEnforcerResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceReloadEnforcerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceReloadEnforcerResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceReloadEnforcerResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{6}
}

func (x *BackofficeAuthServiceReloadEnforcerResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeAuthServiceLoginRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	Otp           string                 `protobuf:"bytes,3,opt,name=otp,proto3" json:"otp,omitempty"`
	DeviceId      string                 `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceLoginRequest) Reset() {
	*x = BackofficeAuthServiceLoginRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceLoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceLoginRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceLoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceLoginRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceLoginRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{7}
}

func (x *BackofficeAuthServiceLoginRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *BackofficeAuthServiceLoginRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *BackofficeAuthServiceLoginRequest) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *BackofficeAuthServiceLoginRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type BackofficeAuthServiceLoginResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,3,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceLoginResponse) Reset() {
	*x = BackofficeAuthServiceLoginResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceLoginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceLoginResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceLoginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceLoginResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceLoginResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{8}
}

func (x *BackofficeAuthServiceLoginResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeAuthServiceLoginResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *BackofficeAuthServiceLoginResponse) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

type BackofficeAuthServiceCreateAppRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	AppType       v11.AppType            `protobuf:"varint,2,opt,name=app_type,json=appType,proto3,enum=algoenum.v1.AppType" json:"app_type,omitempty"`
	Domain        string                 `protobuf:"bytes,3,opt,name=domain,proto3" json:"domain,omitempty"`
	TokenTtl      uint64                 `protobuf:"varint,4,opt,name=token_ttl,json=tokenTtl,proto3" json:"token_ttl,omitempty"`
	AppCountry    v11.AppCountry         `protobuf:"varint,5,opt,name=app_country,json=appCountry,proto3,enum=algoenum.v1.AppCountry" json:"app_country,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreateAppRequest) Reset() {
	*x = BackofficeAuthServiceCreateAppRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreateAppRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreateAppRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceCreateAppRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreateAppRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreateAppRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{9}
}

func (x *BackofficeAuthServiceCreateAppRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeAuthServiceCreateAppRequest) GetAppType() v11.AppType {
	if x != nil {
		return x.AppType
	}
	return v11.AppType(0)
}

func (x *BackofficeAuthServiceCreateAppRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *BackofficeAuthServiceCreateAppRequest) GetTokenTtl() uint64 {
	if x != nil {
		return x.TokenTtl
	}
	return 0
}

func (x *BackofficeAuthServiceCreateAppRequest) GetAppCountry() v11.AppCountry {
	if x != nil {
		return x.AppCountry
	}
	return v11.AppCountry(0)
}

type BackofficeAuthServiceCreateAppResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreateAppResponse) Reset() {
	*x = BackofficeAuthServiceCreateAppResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreateAppResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreateAppResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceCreateAppResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreateAppResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreateAppResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{10}
}

func (x *BackofficeAuthServiceCreateAppResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeAuthServiceFetchAppRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdApp         string                 `protobuf:"bytes,1,opt,name=id_app,json=idApp,proto3" json:"id_app,omitempty"`
	NameSearch    string                 `protobuf:"bytes,2,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	AppType       v11.AppType            `protobuf:"varint,3,opt,name=app_type,json=appType,proto3,enum=algoenum.v1.AppType" json:"app_type,omitempty"`
	AppCountry    v11.AppCountry         `protobuf:"varint,4,opt,name=app_country,json=appCountry,proto3,enum=algoenum.v1.AppCountry" json:"app_country,omitempty"`
	State         *v12.State             `protobuf:"bytes,5,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v12.PaginationRequest `protobuf:"bytes,6,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchAppRequest) Reset() {
	*x = BackofficeAuthServiceFetchAppRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchAppRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchAppRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchAppRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchAppRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchAppRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{11}
}

func (x *BackofficeAuthServiceFetchAppRequest) GetIdApp() string {
	if x != nil {
		return x.IdApp
	}
	return ""
}

func (x *BackofficeAuthServiceFetchAppRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *BackofficeAuthServiceFetchAppRequest) GetAppType() v11.AppType {
	if x != nil {
		return x.AppType
	}
	return v11.AppType(0)
}

func (x *BackofficeAuthServiceFetchAppRequest) GetAppCountry() v11.AppCountry {
	if x != nil {
		return x.AppCountry
	}
	return v11.AppCountry(0)
}

func (x *BackofficeAuthServiceFetchAppRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeAuthServiceFetchAppRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeAuthServiceFetchAppResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v12.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Items         []*BackofficeAppModel   `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchAppResponse) Reset() {
	*x = BackofficeAuthServiceFetchAppResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchAppResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchAppResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchAppResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchAppResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchAppResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{12}
}

func (x *BackofficeAuthServiceFetchAppResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeAuthServiceFetchAppResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficeAuthServiceFetchAppResponse) GetItems() []*BackofficeAppModel {
	if x != nil {
		return x.Items
	}
	return nil
}

type BackofficeAuthServiceUpdateAppRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	IdApp             string                 `protobuf:"bytes,1,opt,name=id_app,json=idApp,proto3" json:"id_app,omitempty"`
	Name              string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	AppType           v11.AppType            `protobuf:"varint,3,opt,name=app_type,json=appType,proto3,enum=algoenum.v1.AppType" json:"app_type,omitempty"`
	TokenTtl          uint64                 `protobuf:"varint,4,opt,name=token_ttl,json=tokenTtl,proto3" json:"token_ttl,omitempty"`
	IsChangePasetoKey bool                   `protobuf:"varint,5,opt,name=is_change_paseto_key,json=isChangePasetoKey,proto3" json:"is_change_paseto_key,omitempty"`
	State             *v12.State             `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	AppCountry        v11.AppCountry         `protobuf:"varint,7,opt,name=app_country,json=appCountry,proto3,enum=algoenum.v1.AppCountry" json:"app_country,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateAppRequest) Reset() {
	*x = BackofficeAuthServiceUpdateAppRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateAppRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateAppRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateAppRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateAppRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateAppRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{13}
}

func (x *BackofficeAuthServiceUpdateAppRequest) GetIdApp() string {
	if x != nil {
		return x.IdApp
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateAppRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateAppRequest) GetAppType() v11.AppType {
	if x != nil {
		return x.AppType
	}
	return v11.AppType(0)
}

func (x *BackofficeAuthServiceUpdateAppRequest) GetTokenTtl() uint64 {
	if x != nil {
		return x.TokenTtl
	}
	return 0
}

func (x *BackofficeAuthServiceUpdateAppRequest) GetIsChangePasetoKey() bool {
	if x != nil {
		return x.IsChangePasetoKey
	}
	return false
}

func (x *BackofficeAuthServiceUpdateAppRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeAuthServiceUpdateAppRequest) GetAppCountry() v11.AppCountry {
	if x != nil {
		return x.AppCountry
	}
	return v11.AppCountry(0)
}

type BackofficeAuthServiceUpdateAppResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateAppResponse) Reset() {
	*x = BackofficeAuthServiceUpdateAppResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateAppResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateAppResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateAppResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateAppResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateAppResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{14}
}

func (x *BackofficeAuthServiceUpdateAppResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

// ----------------------------------
type BackofficeAuthServiceFetchUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdApp         string                 `protobuf:"bytes,1,opt,name=id_app,json=idApp,proto3" json:"id_app,omitempty"`
	IdUser        string                 `protobuf:"bytes,2,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	IdRole        string                 `protobuf:"bytes,3,opt,name=id_role,json=idRole,proto3" json:"id_role,omitempty"`
	EmailSearch   string                 `protobuf:"bytes,4,opt,name=email_search,json=emailSearch,proto3" json:"email_search,omitempty"`
	State         *v12.State             `protobuf:"bytes,5,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v12.PaginationRequest `protobuf:"bytes,6,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchUserRequest) Reset() {
	*x = BackofficeAuthServiceFetchUserRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchUserRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchUserRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchUserRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{15}
}

func (x *BackofficeAuthServiceFetchUserRequest) GetIdApp() string {
	if x != nil {
		return x.IdApp
	}
	return ""
}

func (x *BackofficeAuthServiceFetchUserRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *BackofficeAuthServiceFetchUserRequest) GetIdRole() string {
	if x != nil {
		return x.IdRole
	}
	return ""
}

func (x *BackofficeAuthServiceFetchUserRequest) GetEmailSearch() string {
	if x != nil {
		return x.EmailSearch
	}
	return ""
}

func (x *BackofficeAuthServiceFetchUserRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeAuthServiceFetchUserRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeAuthServiceFetchUserResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v12.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Items         []*BackofficeUserModel  `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchUserResponse) Reset() {
	*x = BackofficeAuthServiceFetchUserResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchUserResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchUserResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchUserResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{16}
}

func (x *BackofficeAuthServiceFetchUserResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeAuthServiceFetchUserResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficeAuthServiceFetchUserResponse) GetItems() []*BackofficeUserModel {
	if x != nil {
		return x.Items
	}
	return nil
}

type BackofficeUserModel struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	IdUser        string                   `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	App           *AuthUserApp             `protobuf:"bytes,2,opt,name=app,proto3" json:"app,omitempty"`
	Role          *BackofficeUserRoleModel `protobuf:"bytes,3,opt,name=role,proto3" json:"role,omitempty"`
	UserDetails   *BackofficeUserDetail    `protobuf:"bytes,4,opt,name=user_details,json=userDetails,proto3" json:"user_details,omitempty"`
	Email         string                   `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	IsEnableTotp  bool                     `protobuf:"varint,6,opt,name=is_enable_totp,json=isEnableTotp,proto3" json:"is_enable_totp,omitempty"`
	IsActive      bool                     `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeUserModel) Reset() {
	*x = BackofficeUserModel{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeUserModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeUserModel) ProtoMessage() {}

func (x *BackofficeUserModel) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeUserModel.ProtoReflect.Descriptor instead.
func (*BackofficeUserModel) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{17}
}

func (x *BackofficeUserModel) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *BackofficeUserModel) GetApp() *AuthUserApp {
	if x != nil {
		return x.App
	}
	return nil
}

func (x *BackofficeUserModel) GetRole() *BackofficeUserRoleModel {
	if x != nil {
		return x.Role
	}
	return nil
}

func (x *BackofficeUserModel) GetUserDetails() *BackofficeUserDetail {
	if x != nil {
		return x.UserDetails
	}
	return nil
}

func (x *BackofficeUserModel) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *BackofficeUserModel) GetIsEnableTotp() bool {
	if x != nil {
		return x.IsEnableTotp
	}
	return false
}

func (x *BackofficeUserModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficeUserRoleModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdRole        string                 `protobuf:"bytes,1,opt,name=id_role,json=idRole,proto3" json:"id_role,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Priority      int64                  `protobuf:"varint,3,opt,name=priority,proto3" json:"priority,omitempty"`
	IsActive      bool                   `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeUserRoleModel) Reset() {
	*x = BackofficeUserRoleModel{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeUserRoleModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeUserRoleModel) ProtoMessage() {}

func (x *BackofficeUserRoleModel) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeUserRoleModel.ProtoReflect.Descriptor instead.
func (*BackofficeUserRoleModel) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{18}
}

func (x *BackofficeUserRoleModel) GetIdRole() string {
	if x != nil {
		return x.IdRole
	}
	return ""
}

func (x *BackofficeUserRoleModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeUserRoleModel) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *BackofficeUserRoleModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type AuthUserApp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdApp         string                 `protobuf:"bytes,1,opt,name=id_app,json=idApp,proto3" json:"id_app,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IsActive      bool                   `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AuthUserApp) Reset() {
	*x = AuthUserApp{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuthUserApp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthUserApp) ProtoMessage() {}

func (x *AuthUserApp) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthUserApp.ProtoReflect.Descriptor instead.
func (*AuthUserApp) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{19}
}

func (x *AuthUserApp) GetIdApp() string {
	if x != nil {
		return x.IdApp
	}
	return ""
}

func (x *AuthUserApp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AuthUserApp) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficeAuthServiceCreateUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdApp         string                 `protobuf:"bytes,1,opt,name=id_app,json=idApp,proto3" json:"id_app,omitempty"`
	IdRole        string                 `protobuf:"bytes,2,opt,name=id_role,json=idRole,proto3" json:"id_role,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	Password      string                 `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	FirstName     string                 `protobuf:"bytes,5,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName      string                 `protobuf:"bytes,6,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	PhoneNumber   string                 `protobuf:"bytes,7,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreateUserRequest) Reset() {
	*x = BackofficeAuthServiceCreateUserRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreateUserRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceCreateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreateUserRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreateUserRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{20}
}

func (x *BackofficeAuthServiceCreateUserRequest) GetIdApp() string {
	if x != nil {
		return x.IdApp
	}
	return ""
}

func (x *BackofficeAuthServiceCreateUserRequest) GetIdRole() string {
	if x != nil {
		return x.IdRole
	}
	return ""
}

func (x *BackofficeAuthServiceCreateUserRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *BackofficeAuthServiceCreateUserRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *BackofficeAuthServiceCreateUserRequest) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *BackofficeAuthServiceCreateUserRequest) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *BackofficeAuthServiceCreateUserRequest) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

type BackofficeAuthServiceCreateUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdUser        string                 `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreateUserResponse) Reset() {
	*x = BackofficeAuthServiceCreateUserResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreateUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreateUserResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceCreateUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreateUserResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreateUserResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{21}
}

func (x *BackofficeAuthServiceCreateUserResponse) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *BackofficeAuthServiceCreateUserResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeAuthServiceUpdateUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdUser        string                 `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	IdRole        string                 `protobuf:"bytes,2,opt,name=id_role,json=idRole,proto3" json:"id_role,omitempty"`
	Password      string                 `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	State         *v12.State             `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateUserRequest) Reset() {
	*x = BackofficeAuthServiceUpdateUserRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateUserRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateUserRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateUserRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{22}
}

func (x *BackofficeAuthServiceUpdateUserRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateUserRequest) GetIdRole() string {
	if x != nil {
		return x.IdRole
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateUserRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateUserRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

type BackofficeAuthServiceUpdateUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateUserResponse) Reset() {
	*x = BackofficeAuthServiceUpdateUserResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateUserResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateUserResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateUserResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{23}
}

func (x *BackofficeAuthServiceUpdateUserResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeAuthServiceFetchRoleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdRole        string                 `protobuf:"bytes,1,opt,name=id_role,json=idRole,proto3" json:"id_role,omitempty"`
	NameSearch    string                 `protobuf:"bytes,2,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	Pagination    *v12.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	State         *v12.State             `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchRoleRequest) Reset() {
	*x = BackofficeAuthServiceFetchRoleRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchRoleRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchRoleRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchRoleRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{24}
}

func (x *BackofficeAuthServiceFetchRoleRequest) GetIdRole() string {
	if x != nil {
		return x.IdRole
	}
	return ""
}

func (x *BackofficeAuthServiceFetchRoleRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *BackofficeAuthServiceFetchRoleRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficeAuthServiceFetchRoleRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

type BackofficeAuthServiceFetchRoleResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v12.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Items         []*RoleModel            `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchRoleResponse) Reset() {
	*x = BackofficeAuthServiceFetchRoleResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchRoleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchRoleResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchRoleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchRoleResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchRoleResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{25}
}

func (x *BackofficeAuthServiceFetchRoleResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeAuthServiceFetchRoleResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficeAuthServiceFetchRoleResponse) GetItems() []*RoleModel {
	if x != nil {
		return x.Items
	}
	return nil
}

type RoleModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdRole        string                 `protobuf:"bytes,1,opt,name=id_role,json=idRole,proto3" json:"id_role,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Priority      int64                  `protobuf:"varint,3,opt,name=priority,proto3" json:"priority,omitempty"`
	IsActive      bool                   `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoleModel) Reset() {
	*x = RoleModel{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoleModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoleModel) ProtoMessage() {}

func (x *RoleModel) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoleModel.ProtoReflect.Descriptor instead.
func (*RoleModel) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{26}
}

func (x *RoleModel) GetIdRole() string {
	if x != nil {
		return x.IdRole
	}
	return ""
}

func (x *RoleModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RoleModel) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *RoleModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficeAuthServiceCreateRoleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Priority      int64                  `protobuf:"varint,2,opt,name=priority,proto3" json:"priority,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreateRoleRequest) Reset() {
	*x = BackofficeAuthServiceCreateRoleRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreateRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreateRoleRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceCreateRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreateRoleRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreateRoleRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{27}
}

func (x *BackofficeAuthServiceCreateRoleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeAuthServiceCreateRoleRequest) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

type BackofficeAuthServiceCreateRoleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreateRoleResponse) Reset() {
	*x = BackofficeAuthServiceCreateRoleResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreateRoleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreateRoleResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceCreateRoleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreateRoleResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreateRoleResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{28}
}

func (x *BackofficeAuthServiceCreateRoleResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeAuthServiceUpdateRoleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdRole        string                 `protobuf:"bytes,1,opt,name=id_role,json=idRole,proto3" json:"id_role,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	State         *v12.State             `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateRoleRequest) Reset() {
	*x = BackofficeAuthServiceUpdateRoleRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateRoleRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateRoleRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateRoleRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{29}
}

func (x *BackofficeAuthServiceUpdateRoleRequest) GetIdRole() string {
	if x != nil {
		return x.IdRole
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateRoleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateRoleRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

type BackofficeAuthServiceUpdateRoleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateRoleResponse) Reset() {
	*x = BackofficeAuthServiceUpdateRoleResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateRoleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateRoleResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateRoleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateRoleResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateRoleResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{30}
}

func (x *BackofficeAuthServiceUpdateRoleResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

// ------------------
type BackofficeAuthServiceFetchServiceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdService     string                 `protobuf:"bytes,1,opt,name=id_service,json=idService,proto3" json:"id_service,omitempty"`
	NameSearch    string                 `protobuf:"bytes,2,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	State         *v12.State             `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v12.PaginationRequest `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchServiceRequest) Reset() {
	*x = BackofficeAuthServiceFetchServiceRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchServiceRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchServiceRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchServiceRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{31}
}

func (x *BackofficeAuthServiceFetchServiceRequest) GetIdService() string {
	if x != nil {
		return x.IdService
	}
	return ""
}

func (x *BackofficeAuthServiceFetchServiceRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *BackofficeAuthServiceFetchServiceRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeAuthServiceFetchServiceRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeAuthServiceFetchServiceResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v12.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Items         []*ServiceModel         `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchServiceResponse) Reset() {
	*x = BackofficeAuthServiceFetchServiceResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchServiceResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchServiceResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchServiceResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{32}
}

func (x *BackofficeAuthServiceFetchServiceResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeAuthServiceFetchServiceResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficeAuthServiceFetchServiceResponse) GetItems() []*ServiceModel {
	if x != nil {
		return x.Items
	}
	return nil
}

type ServiceModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdService     string                 `protobuf:"bytes,1,opt,name=id_service,json=idService,proto3" json:"id_service,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IsActive      bool                   `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceModel) Reset() {
	*x = ServiceModel{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceModel) ProtoMessage() {}

func (x *ServiceModel) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceModel.ProtoReflect.Descriptor instead.
func (*ServiceModel) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{33}
}

func (x *ServiceModel) GetIdService() string {
	if x != nil {
		return x.IdService
	}
	return ""
}

func (x *ServiceModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficeAuthServiceCreateServiceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreateServiceRequest) Reset() {
	*x = BackofficeAuthServiceCreateServiceRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreateServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreateServiceRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceCreateServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreateServiceRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreateServiceRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{34}
}

func (x *BackofficeAuthServiceCreateServiceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type BackofficeAuthServiceCreateServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreateServiceResponse) Reset() {
	*x = BackofficeAuthServiceCreateServiceResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreateServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreateServiceResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceCreateServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreateServiceResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreateServiceResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{35}
}

func (x *BackofficeAuthServiceCreateServiceResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeAuthServiceUpdateServiceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdService     string                 `protobuf:"bytes,1,opt,name=id_service,json=idService,proto3" json:"id_service,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	State         *v12.State             `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateServiceRequest) Reset() {
	*x = BackofficeAuthServiceUpdateServiceRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateServiceRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateServiceRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateServiceRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{36}
}

func (x *BackofficeAuthServiceUpdateServiceRequest) GetIdService() string {
	if x != nil {
		return x.IdService
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateServiceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateServiceRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

type BackofficeAuthServiceUpdateServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateServiceResponse) Reset() {
	*x = BackofficeAuthServiceUpdateServiceResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateServiceResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateServiceResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateServiceResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{37}
}

func (x *BackofficeAuthServiceUpdateServiceResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

// ----------------------------
type BackofficeAuthServiceFetchPathRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	IdPath             string                 `protobuf:"bytes,1,opt,name=id_path,json=idPath,proto3" json:"id_path,omitempty"`
	IdService          string                 `protobuf:"bytes,2,opt,name=id_service,json=idService,proto3" json:"id_service,omitempty"`
	AbsolutePathSearch string                 `protobuf:"bytes,3,opt,name=absolute_path_search,json=absolutePathSearch,proto3" json:"absolute_path_search,omitempty"`
	State              *v12.State             `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	Pagination         *v12.PaginationRequest `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchPathRequest) Reset() {
	*x = BackofficeAuthServiceFetchPathRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchPathRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchPathRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchPathRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchPathRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchPathRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{38}
}

func (x *BackofficeAuthServiceFetchPathRequest) GetIdPath() string {
	if x != nil {
		return x.IdPath
	}
	return ""
}

func (x *BackofficeAuthServiceFetchPathRequest) GetIdService() string {
	if x != nil {
		return x.IdService
	}
	return ""
}

func (x *BackofficeAuthServiceFetchPathRequest) GetAbsolutePathSearch() string {
	if x != nil {
		return x.AbsolutePathSearch
	}
	return ""
}

func (x *BackofficeAuthServiceFetchPathRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeAuthServiceFetchPathRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeAuthServiceFetchPathResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v12.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Items         []*PathModel            `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchPathResponse) Reset() {
	*x = BackofficeAuthServiceFetchPathResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchPathResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchPathResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchPathResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchPathResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchPathResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{39}
}

func (x *BackofficeAuthServiceFetchPathResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeAuthServiceFetchPathResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficeAuthServiceFetchPathResponse) GetItems() []*PathModel {
	if x != nil {
		return x.Items
	}
	return nil
}

type PathModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Service       *ServiceModel          `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	IdPath        string                 `protobuf:"bytes,2,opt,name=id_path,json=idPath,proto3" json:"id_path,omitempty"`
	AbsolutePath  string                 `protobuf:"bytes,3,opt,name=absolute_path,json=absolutePath,proto3" json:"absolute_path,omitempty"`
	IsActive      bool                   `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PathModel) Reset() {
	*x = PathModel{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PathModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PathModel) ProtoMessage() {}

func (x *PathModel) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PathModel.ProtoReflect.Descriptor instead.
func (*PathModel) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{40}
}

func (x *PathModel) GetService() *ServiceModel {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *PathModel) GetIdPath() string {
	if x != nil {
		return x.IdPath
	}
	return ""
}

func (x *PathModel) GetAbsolutePath() string {
	if x != nil {
		return x.AbsolutePath
	}
	return ""
}

func (x *PathModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficeAuthServiceCreatePathRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdService     string                 `protobuf:"bytes,1,opt,name=id_service,json=idService,proto3" json:"id_service,omitempty"`
	AbsolutePath  string                 `protobuf:"bytes,2,opt,name=absolute_path,json=absolutePath,proto3" json:"absolute_path,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreatePathRequest) Reset() {
	*x = BackofficeAuthServiceCreatePathRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreatePathRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreatePathRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceCreatePathRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreatePathRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreatePathRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{41}
}

func (x *BackofficeAuthServiceCreatePathRequest) GetIdService() string {
	if x != nil {
		return x.IdService
	}
	return ""
}

func (x *BackofficeAuthServiceCreatePathRequest) GetAbsolutePath() string {
	if x != nil {
		return x.AbsolutePath
	}
	return ""
}

type BackofficeAuthServiceCreatePathResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreatePathResponse) Reset() {
	*x = BackofficeAuthServiceCreatePathResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreatePathResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreatePathResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceCreatePathResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreatePathResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreatePathResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{42}
}

func (x *BackofficeAuthServiceCreatePathResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeAuthServiceUpdatePathRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdPath        string                 `protobuf:"bytes,1,opt,name=id_path,json=idPath,proto3" json:"id_path,omitempty"`
	AbsolutePath  string                 `protobuf:"bytes,2,opt,name=absolute_path,json=absolutePath,proto3" json:"absolute_path,omitempty"`
	State         *v12.State             `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdatePathRequest) Reset() {
	*x = BackofficeAuthServiceUpdatePathRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdatePathRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdatePathRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdatePathRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdatePathRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdatePathRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{43}
}

func (x *BackofficeAuthServiceUpdatePathRequest) GetIdPath() string {
	if x != nil {
		return x.IdPath
	}
	return ""
}

func (x *BackofficeAuthServiceUpdatePathRequest) GetAbsolutePath() string {
	if x != nil {
		return x.AbsolutePath
	}
	return ""
}

func (x *BackofficeAuthServiceUpdatePathRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

type BackofficeAuthServiceUpdatePathResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdatePathResponse) Reset() {
	*x = BackofficeAuthServiceUpdatePathResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdatePathResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdatePathResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdatePathResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdatePathResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdatePathResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{44}
}

func (x *BackofficeAuthServiceUpdatePathResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

// ------------------------------
type BackofficeAuthServiceFetchPolicyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdPolicy      string                 `protobuf:"bytes,1,opt,name=id_policy,json=idPolicy,proto3" json:"id_policy,omitempty"`
	IdPath        string                 `protobuf:"bytes,2,opt,name=id_path,json=idPath,proto3" json:"id_path,omitempty"`
	IdRole        string                 `protobuf:"bytes,3,opt,name=id_role,json=idRole,proto3" json:"id_role,omitempty"`
	State         *v12.State             `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v12.PaginationRequest `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchPolicyRequest) Reset() {
	*x = BackofficeAuthServiceFetchPolicyRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchPolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchPolicyRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchPolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchPolicyRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchPolicyRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{45}
}

func (x *BackofficeAuthServiceFetchPolicyRequest) GetIdPolicy() string {
	if x != nil {
		return x.IdPolicy
	}
	return ""
}

func (x *BackofficeAuthServiceFetchPolicyRequest) GetIdPath() string {
	if x != nil {
		return x.IdPath
	}
	return ""
}

func (x *BackofficeAuthServiceFetchPolicyRequest) GetIdRole() string {
	if x != nil {
		return x.IdRole
	}
	return ""
}

func (x *BackofficeAuthServiceFetchPolicyRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeAuthServiceFetchPolicyRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeAuthServiceFetchPolicyResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v12.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Items         []*PolicyModel          `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchPolicyResponse) Reset() {
	*x = BackofficeAuthServiceFetchPolicyResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchPolicyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchPolicyResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchPolicyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchPolicyResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchPolicyResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{46}
}

func (x *BackofficeAuthServiceFetchPolicyResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeAuthServiceFetchPolicyResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficeAuthServiceFetchPolicyResponse) GetItems() []*PolicyModel {
	if x != nil {
		return x.Items
	}
	return nil
}

type PolicyModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdPolicy      string                 `protobuf:"bytes,1,opt,name=id_policy,json=idPolicy,proto3" json:"id_policy,omitempty"`
	Service       *ServiceModel          `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
	Path          *PathModel             `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
	Role          *RoleModel             `protobuf:"bytes,4,opt,name=role,proto3" json:"role,omitempty"`
	IsActive      bool                   `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	State         bool                   `protobuf:"varint,6,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PolicyModel) Reset() {
	*x = PolicyModel{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PolicyModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolicyModel) ProtoMessage() {}

func (x *PolicyModel) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolicyModel.ProtoReflect.Descriptor instead.
func (*PolicyModel) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{47}
}

func (x *PolicyModel) GetIdPolicy() string {
	if x != nil {
		return x.IdPolicy
	}
	return ""
}

func (x *PolicyModel) GetService() *ServiceModel {
	if x != nil {
		return x.Service
	}
	return nil
}

func (x *PolicyModel) GetPath() *PathModel {
	if x != nil {
		return x.Path
	}
	return nil
}

func (x *PolicyModel) GetRole() *RoleModel {
	if x != nil {
		return x.Role
	}
	return nil
}

func (x *PolicyModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *PolicyModel) GetState() bool {
	if x != nil {
		return x.State
	}
	return false
}

type BackofficeAuthServiceCreatePolicyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdPath        string                 `protobuf:"bytes,1,opt,name=id_path,json=idPath,proto3" json:"id_path,omitempty"`
	IdRole        string                 `protobuf:"bytes,2,opt,name=id_role,json=idRole,proto3" json:"id_role,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreatePolicyRequest) Reset() {
	*x = BackofficeAuthServiceCreatePolicyRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreatePolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreatePolicyRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceCreatePolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreatePolicyRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreatePolicyRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{48}
}

func (x *BackofficeAuthServiceCreatePolicyRequest) GetIdPath() string {
	if x != nil {
		return x.IdPath
	}
	return ""
}

func (x *BackofficeAuthServiceCreatePolicyRequest) GetIdRole() string {
	if x != nil {
		return x.IdRole
	}
	return ""
}

type BackofficeAuthServiceCreatePolicyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreatePolicyResponse) Reset() {
	*x = BackofficeAuthServiceCreatePolicyResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreatePolicyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreatePolicyResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceCreatePolicyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreatePolicyResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreatePolicyResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{49}
}

func (x *BackofficeAuthServiceCreatePolicyResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeAuthServiceUpdatePolicyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdPolicy      string                 `protobuf:"bytes,1,opt,name=id_policy,json=idPolicy,proto3" json:"id_policy,omitempty"`
	State         *v12.State             `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdatePolicyRequest) Reset() {
	*x = BackofficeAuthServiceUpdatePolicyRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdatePolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdatePolicyRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdatePolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdatePolicyRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdatePolicyRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{50}
}

func (x *BackofficeAuthServiceUpdatePolicyRequest) GetIdPolicy() string {
	if x != nil {
		return x.IdPolicy
	}
	return ""
}

func (x *BackofficeAuthServiceUpdatePolicyRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

type BackofficeAuthServiceUpdatePolicyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdatePolicyResponse) Reset() {
	*x = BackofficeAuthServiceUpdatePolicyResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdatePolicyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdatePolicyResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdatePolicyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdatePolicyResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdatePolicyResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{51}
}

func (x *BackofficeAuthServiceUpdatePolicyResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeAppModel struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdApp          string                 `protobuf:"bytes,1,opt,name=id_app,json=idApp,proto3" json:"id_app,omitempty"`
	Name           string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Domain         string                 `protobuf:"bytes,3,opt,name=domain,proto3" json:"domain,omitempty"`
	AppType        v11.AppType            `protobuf:"varint,4,opt,name=app_type,json=appType,proto3,enum=algoenum.v1.AppType" json:"app_type,omitempty"`
	ShortPublicKey string                 `protobuf:"bytes,5,opt,name=short_public_key,json=shortPublicKey,proto3" json:"short_public_key,omitempty"`
	TokenTtl       uint64                 `protobuf:"varint,6,opt,name=token_ttl,json=tokenTtl,proto3" json:"token_ttl,omitempty"`
	IsActive       bool                   `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	AppCountry     v11.AppCountry         `protobuf:"varint,8,opt,name=app_country,json=appCountry,proto3,enum=algoenum.v1.AppCountry" json:"app_country,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BackofficeAppModel) Reset() {
	*x = BackofficeAppModel{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAppModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAppModel) ProtoMessage() {}

func (x *BackofficeAppModel) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAppModel.ProtoReflect.Descriptor instead.
func (*BackofficeAppModel) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{52}
}

func (x *BackofficeAppModel) GetIdApp() string {
	if x != nil {
		return x.IdApp
	}
	return ""
}

func (x *BackofficeAppModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeAppModel) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *BackofficeAppModel) GetAppType() v11.AppType {
	if x != nil {
		return x.AppType
	}
	return v11.AppType(0)
}

func (x *BackofficeAppModel) GetShortPublicKey() string {
	if x != nil {
		return x.ShortPublicKey
	}
	return ""
}

func (x *BackofficeAppModel) GetTokenTtl() uint64 {
	if x != nil {
		return x.TokenTtl
	}
	return 0
}

func (x *BackofficeAppModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *BackofficeAppModel) GetAppCountry() v11.AppCountry {
	if x != nil {
		return x.AppCountry
	}
	return v11.AppCountry(0)
}

// ------------------------------
type BackofficeAuthServiceInitTotpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceInitTotpRequest) Reset() {
	*x = BackofficeAuthServiceInitTotpRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceInitTotpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceInitTotpRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceInitTotpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceInitTotpRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceInitTotpRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{53}
}

type BackofficeAuthServiceInitTotpResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	QrCode        string                 `protobuf:"bytes,1,opt,name=qr_code,json=qrCode,proto3" json:"qr_code,omitempty"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceInitTotpResponse) Reset() {
	*x = BackofficeAuthServiceInitTotpResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceInitTotpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceInitTotpResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceInitTotpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceInitTotpResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceInitTotpResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{54}
}

func (x *BackofficeAuthServiceInitTotpResponse) GetQrCode() string {
	if x != nil {
		return x.QrCode
	}
	return ""
}

func (x *BackofficeAuthServiceInitTotpResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeAuthServiceVerifyTotpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Totp          string                 `protobuf:"bytes,1,opt,name=totp,proto3" json:"totp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceVerifyTotpRequest) Reset() {
	*x = BackofficeAuthServiceVerifyTotpRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceVerifyTotpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceVerifyTotpRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceVerifyTotpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceVerifyTotpRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceVerifyTotpRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{55}
}

func (x *BackofficeAuthServiceVerifyTotpRequest) GetTotp() string {
	if x != nil {
		return x.Totp
	}
	return ""
}

type BackofficeAuthServiceVerifyTotpResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceVerifyTotpResponse) Reset() {
	*x = BackofficeAuthServiceVerifyTotpResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceVerifyTotpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceVerifyTotpResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceVerifyTotpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceVerifyTotpResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceVerifyTotpResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{56}
}

func (x *BackofficeAuthServiceVerifyTotpResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeAuthServiceRemoveTotpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceRemoveTotpRequest) Reset() {
	*x = BackofficeAuthServiceRemoveTotpRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceRemoveTotpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceRemoveTotpRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceRemoveTotpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceRemoveTotpRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceRemoveTotpRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{57}
}

type BackofficeAuthServiceRemoveTotpResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceRemoveTotpResponse) Reset() {
	*x = BackofficeAuthServiceRemoveTotpResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceRemoveTotpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceRemoveTotpResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceRemoveTotpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceRemoveTotpResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceRemoveTotpResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{58}
}

func (x *BackofficeAuthServiceRemoveTotpResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeAuthServiceFetchConfigMailRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	SmtpServerAddress string                 `protobuf:"bytes,1,opt,name=smtp_server_address,json=smtpServerAddress,proto3" json:"smtp_server_address,omitempty"`
	AuthUsername      string                 `protobuf:"bytes,2,opt,name=auth_username,json=authUsername,proto3" json:"auth_username,omitempty"`
	SenderEmail       string                 `protobuf:"bytes,3,opt,name=sender_email,json=senderEmail,proto3" json:"sender_email,omitempty"`
	State             *v12.State             `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	Pagination        *v12.PaginationRequest `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchConfigMailRequest) Reset() {
	*x = BackofficeAuthServiceFetchConfigMailRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchConfigMailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchConfigMailRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchConfigMailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchConfigMailRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchConfigMailRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{59}
}

func (x *BackofficeAuthServiceFetchConfigMailRequest) GetSmtpServerAddress() string {
	if x != nil {
		return x.SmtpServerAddress
	}
	return ""
}

func (x *BackofficeAuthServiceFetchConfigMailRequest) GetAuthUsername() string {
	if x != nil {
		return x.AuthUsername
	}
	return ""
}

func (x *BackofficeAuthServiceFetchConfigMailRequest) GetSenderEmail() string {
	if x != nil {
		return x.SenderEmail
	}
	return ""
}

func (x *BackofficeAuthServiceFetchConfigMailRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeAuthServiceFetchConfigMailRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeAuthServiceFetchConfigMailResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Items         []*ConfigMailModel      `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	Pagination    *v12.PaginationResponse `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchConfigMailResponse) Reset() {
	*x = BackofficeAuthServiceFetchConfigMailResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchConfigMailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchConfigMailResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchConfigMailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchConfigMailResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchConfigMailResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{60}
}

func (x *BackofficeAuthServiceFetchConfigMailResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeAuthServiceFetchConfigMailResponse) GetItems() []*ConfigMailModel {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *BackofficeAuthServiceFetchConfigMailResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeAuthServiceCreateConfigMailRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	IdAuthApp         string                 `protobuf:"bytes,1,opt,name=id_auth_app,json=idAuthApp,proto3" json:"id_auth_app,omitempty"`
	SmtpServerAddress string                 `protobuf:"bytes,2,opt,name=smtp_server_address,json=smtpServerAddress,proto3" json:"smtp_server_address,omitempty"`
	SmtpServerPort    uint64                 `protobuf:"varint,3,opt,name=smtp_server_port,json=smtpServerPort,proto3" json:"smtp_server_port,omitempty"`
	AuthUsername      string                 `protobuf:"bytes,4,opt,name=auth_username,json=authUsername,proto3" json:"auth_username,omitempty"`
	AuthPassword      string                 `protobuf:"bytes,5,opt,name=auth_password,json=authPassword,proto3" json:"auth_password,omitempty"`
	SenderEmail       string                 `protobuf:"bytes,6,opt,name=sender_email,json=senderEmail,proto3" json:"sender_email,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreateConfigMailRequest) Reset() {
	*x = BackofficeAuthServiceCreateConfigMailRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreateConfigMailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreateConfigMailRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceCreateConfigMailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreateConfigMailRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreateConfigMailRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{61}
}

func (x *BackofficeAuthServiceCreateConfigMailRequest) GetIdAuthApp() string {
	if x != nil {
		return x.IdAuthApp
	}
	return ""
}

func (x *BackofficeAuthServiceCreateConfigMailRequest) GetSmtpServerAddress() string {
	if x != nil {
		return x.SmtpServerAddress
	}
	return ""
}

func (x *BackofficeAuthServiceCreateConfigMailRequest) GetSmtpServerPort() uint64 {
	if x != nil {
		return x.SmtpServerPort
	}
	return 0
}

func (x *BackofficeAuthServiceCreateConfigMailRequest) GetAuthUsername() string {
	if x != nil {
		return x.AuthUsername
	}
	return ""
}

func (x *BackofficeAuthServiceCreateConfigMailRequest) GetAuthPassword() string {
	if x != nil {
		return x.AuthPassword
	}
	return ""
}

func (x *BackofficeAuthServiceCreateConfigMailRequest) GetSenderEmail() string {
	if x != nil {
		return x.SenderEmail
	}
	return ""
}

type BackofficeAuthServiceCreateConfigMailResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreateConfigMailResponse) Reset() {
	*x = BackofficeAuthServiceCreateConfigMailResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreateConfigMailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreateConfigMailResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceCreateConfigMailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreateConfigMailResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreateConfigMailResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{62}
}

func (x *BackofficeAuthServiceCreateConfigMailResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeAuthServiceUpdateConfigMailRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	IdAuthApp         string                 `protobuf:"bytes,1,opt,name=id_auth_app,json=idAuthApp,proto3" json:"id_auth_app,omitempty"`
	SmtpServerAddress string                 `protobuf:"bytes,2,opt,name=smtp_server_address,json=smtpServerAddress,proto3" json:"smtp_server_address,omitempty"`
	SmtpServerPort    uint64                 `protobuf:"varint,3,opt,name=smtp_server_port,json=smtpServerPort,proto3" json:"smtp_server_port,omitempty"`
	AuthUsername      string                 `protobuf:"bytes,4,opt,name=auth_username,json=authUsername,proto3" json:"auth_username,omitempty"`
	AuthPassword      string                 `protobuf:"bytes,5,opt,name=auth_password,json=authPassword,proto3" json:"auth_password,omitempty"`
	SenderEmail       string                 `protobuf:"bytes,6,opt,name=sender_email,json=senderEmail,proto3" json:"sender_email,omitempty"`
	State             *v12.State             `protobuf:"bytes,7,opt,name=state,proto3" json:"state,omitempty"`
	IdConfigMail      string                 `protobuf:"bytes,8,opt,name=id_config_mail,json=idConfigMail,proto3" json:"id_config_mail,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateConfigMailRequest) Reset() {
	*x = BackofficeAuthServiceUpdateConfigMailRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateConfigMailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateConfigMailRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateConfigMailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateConfigMailRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateConfigMailRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{63}
}

func (x *BackofficeAuthServiceUpdateConfigMailRequest) GetIdAuthApp() string {
	if x != nil {
		return x.IdAuthApp
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateConfigMailRequest) GetSmtpServerAddress() string {
	if x != nil {
		return x.SmtpServerAddress
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateConfigMailRequest) GetSmtpServerPort() uint64 {
	if x != nil {
		return x.SmtpServerPort
	}
	return 0
}

func (x *BackofficeAuthServiceUpdateConfigMailRequest) GetAuthUsername() string {
	if x != nil {
		return x.AuthUsername
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateConfigMailRequest) GetAuthPassword() string {
	if x != nil {
		return x.AuthPassword
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateConfigMailRequest) GetSenderEmail() string {
	if x != nil {
		return x.SenderEmail
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateConfigMailRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeAuthServiceUpdateConfigMailRequest) GetIdConfigMail() string {
	if x != nil {
		return x.IdConfigMail
	}
	return ""
}

type BackofficeAuthServiceUpdateConfigMailResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateConfigMailResponse) Reset() {
	*x = BackofficeAuthServiceUpdateConfigMailResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateConfigMailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateConfigMailResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateConfigMailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateConfigMailResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateConfigMailResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{64}
}

func (x *BackofficeAuthServiceUpdateConfigMailResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type ConfigMailModel struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	IdConfigMail      string                 `protobuf:"bytes,1,opt,name=id_config_mail,json=idConfigMail,proto3" json:"id_config_mail,omitempty"`
	IdAuthApp         string                 `protobuf:"bytes,2,opt,name=id_auth_app,json=idAuthApp,proto3" json:"id_auth_app,omitempty"`
	SmtpServerAddress string                 `protobuf:"bytes,3,opt,name=smtp_server_address,json=smtpServerAddress,proto3" json:"smtp_server_address,omitempty"`
	SmtpServerPort    uint64                 `protobuf:"varint,4,opt,name=smtp_server_port,json=smtpServerPort,proto3" json:"smtp_server_port,omitempty"`
	AuthUsername      string                 `protobuf:"bytes,5,opt,name=auth_username,json=authUsername,proto3" json:"auth_username,omitempty"`
	AuthPassword      string                 `protobuf:"bytes,6,opt,name=auth_password,json=authPassword,proto3" json:"auth_password,omitempty"`
	SenderEmail       string                 `protobuf:"bytes,7,opt,name=sender_email,json=senderEmail,proto3" json:"sender_email,omitempty"`
	IsActive          bool                   `protobuf:"varint,8,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ConfigMailModel) Reset() {
	*x = ConfigMailModel{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConfigMailModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigMailModel) ProtoMessage() {}

func (x *ConfigMailModel) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigMailModel.ProtoReflect.Descriptor instead.
func (*ConfigMailModel) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{65}
}

func (x *ConfigMailModel) GetIdConfigMail() string {
	if x != nil {
		return x.IdConfigMail
	}
	return ""
}

func (x *ConfigMailModel) GetIdAuthApp() string {
	if x != nil {
		return x.IdAuthApp
	}
	return ""
}

func (x *ConfigMailModel) GetSmtpServerAddress() string {
	if x != nil {
		return x.SmtpServerAddress
	}
	return ""
}

func (x *ConfigMailModel) GetSmtpServerPort() uint64 {
	if x != nil {
		return x.SmtpServerPort
	}
	return 0
}

func (x *ConfigMailModel) GetAuthUsername() string {
	if x != nil {
		return x.AuthUsername
	}
	return ""
}

func (x *ConfigMailModel) GetAuthPassword() string {
	if x != nil {
		return x.AuthPassword
	}
	return ""
}

func (x *ConfigMailModel) GetSenderEmail() string {
	if x != nil {
		return x.SenderEmail
	}
	return ""
}

func (x *ConfigMailModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficeAuthServiceFetchConfigTemplateEmailRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          v11.TemplateEmailType  `protobuf:"varint,1,opt,name=name,proto3,enum=algoenum.v1.TemplateEmailType" json:"name,omitempty"`
	IdAuthApp     string                 `protobuf:"bytes,2,opt,name=id_auth_app,json=idAuthApp,proto3" json:"id_auth_app,omitempty"`
	State         *v12.State             `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v12.PaginationRequest `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchConfigTemplateEmailRequest) Reset() {
	*x = BackofficeAuthServiceFetchConfigTemplateEmailRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchConfigTemplateEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchConfigTemplateEmailRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchConfigTemplateEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchConfigTemplateEmailRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchConfigTemplateEmailRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{66}
}

func (x *BackofficeAuthServiceFetchConfigTemplateEmailRequest) GetName() v11.TemplateEmailType {
	if x != nil {
		return x.Name
	}
	return v11.TemplateEmailType(0)
}

func (x *BackofficeAuthServiceFetchConfigTemplateEmailRequest) GetIdAuthApp() string {
	if x != nil {
		return x.IdAuthApp
	}
	return ""
}

func (x *BackofficeAuthServiceFetchConfigTemplateEmailRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeAuthServiceFetchConfigTemplateEmailRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeAuthServiceFetchConfigTemplateEmailResponse struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Error         *v1.ErrorMessage            `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Items         []*ConfigTemplateEmailModel `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	Pagination    *v12.PaginationResponse     `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchConfigTemplateEmailResponse) Reset() {
	*x = BackofficeAuthServiceFetchConfigTemplateEmailResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchConfigTemplateEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchConfigTemplateEmailResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchConfigTemplateEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchConfigTemplateEmailResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchConfigTemplateEmailResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{67}
}

func (x *BackofficeAuthServiceFetchConfigTemplateEmailResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeAuthServiceFetchConfigTemplateEmailResponse) GetItems() []*ConfigTemplateEmailModel {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *BackofficeAuthServiceFetchConfigTemplateEmailResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeAuthServiceCreateConfigTemplateEmailRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdAuthApp     string                 `protobuf:"bytes,1,opt,name=id_auth_app,json=idAuthApp,proto3" json:"id_auth_app,omitempty"`
	Name          v11.TemplateEmailType  `protobuf:"varint,2,opt,name=name,proto3,enum=algoenum.v1.TemplateEmailType" json:"name,omitempty"`
	Content       string                 `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreateConfigTemplateEmailRequest) Reset() {
	*x = BackofficeAuthServiceCreateConfigTemplateEmailRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreateConfigTemplateEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreateConfigTemplateEmailRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceCreateConfigTemplateEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreateConfigTemplateEmailRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreateConfigTemplateEmailRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{68}
}

func (x *BackofficeAuthServiceCreateConfigTemplateEmailRequest) GetIdAuthApp() string {
	if x != nil {
		return x.IdAuthApp
	}
	return ""
}

func (x *BackofficeAuthServiceCreateConfigTemplateEmailRequest) GetName() v11.TemplateEmailType {
	if x != nil {
		return x.Name
	}
	return v11.TemplateEmailType(0)
}

func (x *BackofficeAuthServiceCreateConfigTemplateEmailRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type BackofficeAuthServiceCreateConfigTemplateEmailResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreateConfigTemplateEmailResponse) Reset() {
	*x = BackofficeAuthServiceCreateConfigTemplateEmailResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreateConfigTemplateEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreateConfigTemplateEmailResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceCreateConfigTemplateEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreateConfigTemplateEmailResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreateConfigTemplateEmailResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{69}
}

func (x *BackofficeAuthServiceCreateConfigTemplateEmailResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeAuthServiceUpdateConfigTemplateEmailRequest struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Content               string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	State                 *v12.State             `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	IdConfigTemplateEmail string                 `protobuf:"bytes,3,opt,name=id_config_template_email,json=idConfigTemplateEmail,proto3" json:"id_config_template_email,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateConfigTemplateEmailRequest) Reset() {
	*x = BackofficeAuthServiceUpdateConfigTemplateEmailRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateConfigTemplateEmailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateConfigTemplateEmailRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateConfigTemplateEmailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateConfigTemplateEmailRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateConfigTemplateEmailRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{70}
}

func (x *BackofficeAuthServiceUpdateConfigTemplateEmailRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateConfigTemplateEmailRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeAuthServiceUpdateConfigTemplateEmailRequest) GetIdConfigTemplateEmail() string {
	if x != nil {
		return x.IdConfigTemplateEmail
	}
	return ""
}

type BackofficeAuthServiceUpdateConfigTemplateEmailResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateConfigTemplateEmailResponse) Reset() {
	*x = BackofficeAuthServiceUpdateConfigTemplateEmailResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateConfigTemplateEmailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateConfigTemplateEmailResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateConfigTemplateEmailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateConfigTemplateEmailResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateConfigTemplateEmailResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{71}
}

func (x *BackofficeAuthServiceUpdateConfigTemplateEmailResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type ConfigTemplateEmailModel struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	IdConfigTemplateEmail string                 `protobuf:"bytes,1,opt,name=id_config_template_email,json=idConfigTemplateEmail,proto3" json:"id_config_template_email,omitempty"`
	IdAuthApp             string                 `protobuf:"bytes,2,opt,name=id_auth_app,json=idAuthApp,proto3" json:"id_auth_app,omitempty"`
	Name                  v11.TemplateEmailType  `protobuf:"varint,3,opt,name=name,proto3,enum=algoenum.v1.TemplateEmailType" json:"name,omitempty"`
	Content               string                 `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty"`
	IsActive              bool                   `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *ConfigTemplateEmailModel) Reset() {
	*x = ConfigTemplateEmailModel{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConfigTemplateEmailModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigTemplateEmailModel) ProtoMessage() {}

func (x *ConfigTemplateEmailModel) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigTemplateEmailModel.ProtoReflect.Descriptor instead.
func (*ConfigTemplateEmailModel) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{72}
}

func (x *ConfigTemplateEmailModel) GetIdConfigTemplateEmail() string {
	if x != nil {
		return x.IdConfigTemplateEmail
	}
	return ""
}

func (x *ConfigTemplateEmailModel) GetIdAuthApp() string {
	if x != nil {
		return x.IdAuthApp
	}
	return ""
}

func (x *ConfigTemplateEmailModel) GetName() v11.TemplateEmailType {
	if x != nil {
		return x.Name
	}
	return v11.TemplateEmailType(0)
}

func (x *ConfigTemplateEmailModel) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ConfigTemplateEmailModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficeAuthServiceRefreshTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RefreshToken  string                 `protobuf:"bytes,1,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	DeviceId      string                 `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceRefreshTokenRequest) Reset() {
	*x = BackofficeAuthServiceRefreshTokenRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceRefreshTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceRefreshTokenRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceRefreshTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceRefreshTokenRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceRefreshTokenRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{73}
}

func (x *BackofficeAuthServiceRefreshTokenRequest) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *BackofficeAuthServiceRefreshTokenRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type BackofficeAuthServiceRefreshTokenResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,3,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceRefreshTokenResponse) Reset() {
	*x = BackofficeAuthServiceRefreshTokenResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceRefreshTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceRefreshTokenResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceRefreshTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceRefreshTokenResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceRefreshTokenResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{74}
}

func (x *BackofficeAuthServiceRefreshTokenResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeAuthServiceRefreshTokenResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *BackofficeAuthServiceRefreshTokenResponse) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

// -------------------------------
type BackofficeAuthServiceUpdateRefCodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	RefCode       string                 `protobuf:"bytes,2,opt,name=ref_code,json=refCode,proto3" json:"ref_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateRefCodeRequest) Reset() {
	*x = BackofficeAuthServiceUpdateRefCodeRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[75]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateRefCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateRefCodeRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateRefCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[75]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateRefCodeRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateRefCodeRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{75}
}

func (x *BackofficeAuthServiceUpdateRefCodeRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateRefCodeRequest) GetRefCode() string {
	if x != nil {
		return x.RefCode
	}
	return ""
}

type BackofficeAuthServiceUpdateRefCodeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateRefCodeResponse) Reset() {
	*x = BackofficeAuthServiceUpdateRefCodeResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[76]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateRefCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateRefCodeResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateRefCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[76]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateRefCodeResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateRefCodeResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{76}
}

func (x *BackofficeAuthServiceUpdateRefCodeResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

// -------------------------------------
type BackofficeAuthServiceForgotPasswordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	Otp           string                 `protobuf:"bytes,2,opt,name=otp,proto3" json:"otp,omitempty"`
	NewPassword   string                 `protobuf:"bytes,3,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceForgotPasswordRequest) Reset() {
	*x = BackofficeAuthServiceForgotPasswordRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[77]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceForgotPasswordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceForgotPasswordRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceForgotPasswordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[77]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceForgotPasswordRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceForgotPasswordRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{77}
}

func (x *BackofficeAuthServiceForgotPasswordRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *BackofficeAuthServiceForgotPasswordRequest) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *BackofficeAuthServiceForgotPasswordRequest) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

type BackofficeAuthServiceForgotPasswordResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceForgotPasswordResponse) Reset() {
	*x = BackofficeAuthServiceForgotPasswordResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[78]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceForgotPasswordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceForgotPasswordResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceForgotPasswordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[78]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceForgotPasswordResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceForgotPasswordResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{78}
}

func (x *BackofficeAuthServiceForgotPasswordResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeAuthServiceFetchOAuthConfigRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdAuthApp     string                 `protobuf:"bytes,1,opt,name=id_auth_app,json=idAuthApp,proto3" json:"id_auth_app,omitempty"`
	State         *v12.State             `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v12.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchOAuthConfigRequest) Reset() {
	*x = BackofficeAuthServiceFetchOAuthConfigRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[79]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchOAuthConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchOAuthConfigRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchOAuthConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[79]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchOAuthConfigRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchOAuthConfigRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{79}
}

func (x *BackofficeAuthServiceFetchOAuthConfigRequest) GetIdAuthApp() string {
	if x != nil {
		return x.IdAuthApp
	}
	return ""
}

func (x *BackofficeAuthServiceFetchOAuthConfigRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeAuthServiceFetchOAuthConfigRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeAuthServiceFetchOAuthConfigResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Items         []*OAuthConfigModel     `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	Pagination    *v12.PaginationResponse `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchOAuthConfigResponse) Reset() {
	*x = BackofficeAuthServiceFetchOAuthConfigResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[80]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchOAuthConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchOAuthConfigResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchOAuthConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[80]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchOAuthConfigResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchOAuthConfigResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{80}
}

func (x *BackofficeAuthServiceFetchOAuthConfigResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeAuthServiceFetchOAuthConfigResponse) GetItems() []*OAuthConfigModel {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *BackofficeAuthServiceFetchOAuthConfigResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeAuthServiceCreateOAuthConfigRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdAuthApp     string                 `protobuf:"bytes,1,opt,name=id_auth_app,json=idAuthApp,proto3" json:"id_auth_app,omitempty"`
	ClientId      string                 `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientSecret  string                 `protobuf:"bytes,3,opt,name=client_secret,json=clientSecret,proto3" json:"client_secret,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreateOAuthConfigRequest) Reset() {
	*x = BackofficeAuthServiceCreateOAuthConfigRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[81]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreateOAuthConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreateOAuthConfigRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceCreateOAuthConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[81]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreateOAuthConfigRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreateOAuthConfigRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{81}
}

func (x *BackofficeAuthServiceCreateOAuthConfigRequest) GetIdAuthApp() string {
	if x != nil {
		return x.IdAuthApp
	}
	return ""
}

func (x *BackofficeAuthServiceCreateOAuthConfigRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *BackofficeAuthServiceCreateOAuthConfigRequest) GetClientSecret() string {
	if x != nil {
		return x.ClientSecret
	}
	return ""
}

type BackofficeAuthServiceCreateOAuthConfigResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreateOAuthConfigResponse) Reset() {
	*x = BackofficeAuthServiceCreateOAuthConfigResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[82]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreateOAuthConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreateOAuthConfigResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceCreateOAuthConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[82]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreateOAuthConfigResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreateOAuthConfigResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{82}
}

func (x *BackofficeAuthServiceCreateOAuthConfigResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeAuthServiceUpdateOAuthConfigRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdAuthApp     string                 `protobuf:"bytes,1,opt,name=id_auth_app,json=idAuthApp,proto3" json:"id_auth_app,omitempty"`
	ClientId      string                 `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientSecret  string                 `protobuf:"bytes,3,opt,name=client_secret,json=clientSecret,proto3" json:"client_secret,omitempty"`
	State         *v12.State             `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	IdOAuthConfig string                 `protobuf:"bytes,5,opt,name=id_o_auth_config,json=idOAuthConfig,proto3" json:"id_o_auth_config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateOAuthConfigRequest) Reset() {
	*x = BackofficeAuthServiceUpdateOAuthConfigRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[83]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateOAuthConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateOAuthConfigRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateOAuthConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[83]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateOAuthConfigRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateOAuthConfigRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{83}
}

func (x *BackofficeAuthServiceUpdateOAuthConfigRequest) GetIdAuthApp() string {
	if x != nil {
		return x.IdAuthApp
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateOAuthConfigRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateOAuthConfigRequest) GetClientSecret() string {
	if x != nil {
		return x.ClientSecret
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateOAuthConfigRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeAuthServiceUpdateOAuthConfigRequest) GetIdOAuthConfig() string {
	if x != nil {
		return x.IdOAuthConfig
	}
	return ""
}

type BackofficeAuthServiceUpdateOAuthConfigResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateOAuthConfigResponse) Reset() {
	*x = BackofficeAuthServiceUpdateOAuthConfigResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[84]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateOAuthConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateOAuthConfigResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateOAuthConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[84]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateOAuthConfigResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateOAuthConfigResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{84}
}

func (x *BackofficeAuthServiceUpdateOAuthConfigResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type OAuthConfigModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdOAuthConfig string                 `protobuf:"bytes,1,opt,name=id_o_auth_config,json=idOAuthConfig,proto3" json:"id_o_auth_config,omitempty"`
	IdAuthApp     string                 `protobuf:"bytes,2,opt,name=id_auth_app,json=idAuthApp,proto3" json:"id_auth_app,omitempty"`
	ClientId      string                 `protobuf:"bytes,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientSecret  string                 `protobuf:"bytes,4,opt,name=client_secret,json=clientSecret,proto3" json:"client_secret,omitempty"`
	IsActive      bool                   `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OAuthConfigModel) Reset() {
	*x = OAuthConfigModel{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[85]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OAuthConfigModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OAuthConfigModel) ProtoMessage() {}

func (x *OAuthConfigModel) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[85]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OAuthConfigModel.ProtoReflect.Descriptor instead.
func (*OAuthConfigModel) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{85}
}

func (x *OAuthConfigModel) GetIdOAuthConfig() string {
	if x != nil {
		return x.IdOAuthConfig
	}
	return ""
}

func (x *OAuthConfigModel) GetIdAuthApp() string {
	if x != nil {
		return x.IdAuthApp
	}
	return ""
}

func (x *OAuthConfigModel) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *OAuthConfigModel) GetClientSecret() string {
	if x != nil {
		return x.ClientSecret
	}
	return ""
}

func (x *OAuthConfigModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

// ---------------------------------
type BackofficeAuthServiceFetchCompanyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	IdAuthApp     string                 `protobuf:"bytes,2,opt,name=id_auth_app,json=idAuthApp,proto3" json:"id_auth_app,omitempty"`
	Street        string                 `protobuf:"bytes,3,opt,name=street,proto3" json:"street,omitempty"`
	IdState       int64                  `protobuf:"varint,4,opt,name=id_state,json=idState,proto3" json:"id_state,omitempty"`
	IdCity        int64                  `protobuf:"varint,5,opt,name=id_city,json=idCity,proto3" json:"id_city,omitempty"`
	IdCountry     int64                  `protobuf:"varint,6,opt,name=id_country,json=idCountry,proto3" json:"id_country,omitempty"`
	Mst           string                 `protobuf:"bytes,7,opt,name=mst,proto3" json:"mst,omitempty"`
	Verify        *v12.Bool              `protobuf:"bytes,8,opt,name=verify,proto3" json:"verify,omitempty"`
	State         *v12.State             `protobuf:"bytes,9,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v12.PaginationRequest `protobuf:"bytes,10,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchCompanyRequest) Reset() {
	*x = BackofficeAuthServiceFetchCompanyRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[86]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchCompanyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchCompanyRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchCompanyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[86]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchCompanyRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchCompanyRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{86}
}

func (x *BackofficeAuthServiceFetchCompanyRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeAuthServiceFetchCompanyRequest) GetIdAuthApp() string {
	if x != nil {
		return x.IdAuthApp
	}
	return ""
}

func (x *BackofficeAuthServiceFetchCompanyRequest) GetStreet() string {
	if x != nil {
		return x.Street
	}
	return ""
}

func (x *BackofficeAuthServiceFetchCompanyRequest) GetIdState() int64 {
	if x != nil {
		return x.IdState
	}
	return 0
}

func (x *BackofficeAuthServiceFetchCompanyRequest) GetIdCity() int64 {
	if x != nil {
		return x.IdCity
	}
	return 0
}

func (x *BackofficeAuthServiceFetchCompanyRequest) GetIdCountry() int64 {
	if x != nil {
		return x.IdCountry
	}
	return 0
}

func (x *BackofficeAuthServiceFetchCompanyRequest) GetMst() string {
	if x != nil {
		return x.Mst
	}
	return ""
}

func (x *BackofficeAuthServiceFetchCompanyRequest) GetVerify() *v12.Bool {
	if x != nil {
		return x.Verify
	}
	return nil
}

func (x *BackofficeAuthServiceFetchCompanyRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeAuthServiceFetchCompanyRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeAuthServiceFetchCompanyResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Items         []*CompanyModel         `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	Pagination    *v12.PaginationResponse `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceFetchCompanyResponse) Reset() {
	*x = BackofficeAuthServiceFetchCompanyResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[87]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceFetchCompanyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceFetchCompanyResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceFetchCompanyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[87]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceFetchCompanyResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceFetchCompanyResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{87}
}

func (x *BackofficeAuthServiceFetchCompanyResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeAuthServiceFetchCompanyResponse) GetItems() []*CompanyModel {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *BackofficeAuthServiceFetchCompanyResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeAuthServiceCreateCompanyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Street        string                 `protobuf:"bytes,2,opt,name=street,proto3" json:"street,omitempty"`
	IdState       int64                  `protobuf:"varint,3,opt,name=id_state,json=idState,proto3" json:"id_state,omitempty"`
	IdCity        int64                  `protobuf:"varint,4,opt,name=id_city,json=idCity,proto3" json:"id_city,omitempty"`
	IdCountry     int64                  `protobuf:"varint,5,opt,name=id_country,json=idCountry,proto3" json:"id_country,omitempty"`
	Logo          string                 `protobuf:"bytes,6,opt,name=logo,proto3" json:"logo,omitempty"`
	Mst           string                 `protobuf:"bytes,7,opt,name=mst,proto3" json:"mst,omitempty"`
	IdAuthApp     string                 `protobuf:"bytes,8,opt,name=id_auth_app,json=idAuthApp,proto3" json:"id_auth_app,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreateCompanyRequest) Reset() {
	*x = BackofficeAuthServiceCreateCompanyRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[88]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreateCompanyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreateCompanyRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceCreateCompanyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[88]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreateCompanyRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreateCompanyRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{88}
}

func (x *BackofficeAuthServiceCreateCompanyRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeAuthServiceCreateCompanyRequest) GetStreet() string {
	if x != nil {
		return x.Street
	}
	return ""
}

func (x *BackofficeAuthServiceCreateCompanyRequest) GetIdState() int64 {
	if x != nil {
		return x.IdState
	}
	return 0
}

func (x *BackofficeAuthServiceCreateCompanyRequest) GetIdCity() int64 {
	if x != nil {
		return x.IdCity
	}
	return 0
}

func (x *BackofficeAuthServiceCreateCompanyRequest) GetIdCountry() int64 {
	if x != nil {
		return x.IdCountry
	}
	return 0
}

func (x *BackofficeAuthServiceCreateCompanyRequest) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *BackofficeAuthServiceCreateCompanyRequest) GetMst() string {
	if x != nil {
		return x.Mst
	}
	return ""
}

func (x *BackofficeAuthServiceCreateCompanyRequest) GetIdAuthApp() string {
	if x != nil {
		return x.IdAuthApp
	}
	return ""
}

type BackofficeAuthServiceCreateCompanyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceCreateCompanyResponse) Reset() {
	*x = BackofficeAuthServiceCreateCompanyResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[89]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceCreateCompanyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceCreateCompanyResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceCreateCompanyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[89]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceCreateCompanyResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceCreateCompanyResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{89}
}

func (x *BackofficeAuthServiceCreateCompanyResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeAuthServiceUpdateCompanyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Street        string                 `protobuf:"bytes,2,opt,name=street,proto3" json:"street,omitempty"`
	IdState       int64                  `protobuf:"varint,3,opt,name=id_state,json=idState,proto3" json:"id_state,omitempty"`
	IdCity        int64                  `protobuf:"varint,4,opt,name=id_city,json=idCity,proto3" json:"id_city,omitempty"`
	IdCountry     int64                  `protobuf:"varint,6,opt,name=id_country,json=idCountry,proto3" json:"id_country,omitempty"`
	Logo          string                 `protobuf:"bytes,7,opt,name=logo,proto3" json:"logo,omitempty"`
	Mst           string                 `protobuf:"bytes,8,opt,name=mst,proto3" json:"mst,omitempty"`
	Verify        bool                   `protobuf:"varint,9,opt,name=verify,proto3" json:"verify,omitempty"`
	State         *v12.State             `protobuf:"bytes,10,opt,name=state,proto3" json:"state,omitempty"`
	IdCompany     string                 `protobuf:"bytes,11,opt,name=id_company,json=idCompany,proto3" json:"id_company,omitempty"`
	IdAuthApp     string                 `protobuf:"bytes,12,opt,name=id_auth_app,json=idAuthApp,proto3" json:"id_auth_app,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateCompanyRequest) Reset() {
	*x = BackofficeAuthServiceUpdateCompanyRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[90]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateCompanyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateCompanyRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateCompanyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[90]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateCompanyRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateCompanyRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{90}
}

func (x *BackofficeAuthServiceUpdateCompanyRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateCompanyRequest) GetStreet() string {
	if x != nil {
		return x.Street
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateCompanyRequest) GetIdState() int64 {
	if x != nil {
		return x.IdState
	}
	return 0
}

func (x *BackofficeAuthServiceUpdateCompanyRequest) GetIdCity() int64 {
	if x != nil {
		return x.IdCity
	}
	return 0
}

func (x *BackofficeAuthServiceUpdateCompanyRequest) GetIdCountry() int64 {
	if x != nil {
		return x.IdCountry
	}
	return 0
}

func (x *BackofficeAuthServiceUpdateCompanyRequest) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateCompanyRequest) GetMst() string {
	if x != nil {
		return x.Mst
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateCompanyRequest) GetVerify() bool {
	if x != nil {
		return x.Verify
	}
	return false
}

func (x *BackofficeAuthServiceUpdateCompanyRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeAuthServiceUpdateCompanyRequest) GetIdCompany() string {
	if x != nil {
		return x.IdCompany
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateCompanyRequest) GetIdAuthApp() string {
	if x != nil {
		return x.IdAuthApp
	}
	return ""
}

type BackofficeAuthServiceUpdateCompanyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateCompanyResponse) Reset() {
	*x = BackofficeAuthServiceUpdateCompanyResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[91]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateCompanyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateCompanyResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateCompanyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[91]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateCompanyResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateCompanyResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{91}
}

func (x *BackofficeAuthServiceUpdateCompanyResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CompanyModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdCompany     string                 `protobuf:"bytes,1,opt,name=id_company,json=idCompany,proto3" json:"id_company,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Street        string                 `protobuf:"bytes,3,opt,name=street,proto3" json:"street,omitempty"`
	IdState       int64                  `protobuf:"varint,4,opt,name=id_state,json=idState,proto3" json:"id_state,omitempty"`
	IdCity        int64                  `protobuf:"varint,5,opt,name=id_city,json=idCity,proto3" json:"id_city,omitempty"`
	IdCountry     int64                  `protobuf:"varint,6,opt,name=id_country,json=idCountry,proto3" json:"id_country,omitempty"`
	Logo          string                 `protobuf:"bytes,7,opt,name=logo,proto3" json:"logo,omitempty"`
	Mst           string                 `protobuf:"bytes,8,opt,name=mst,proto3" json:"mst,omitempty"`
	Verify        bool                   `protobuf:"varint,9,opt,name=verify,proto3" json:"verify,omitempty"`
	IsActive      bool                   `protobuf:"varint,10,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	IdAuthApp     string                 `protobuf:"bytes,11,opt,name=id_auth_app,json=idAuthApp,proto3" json:"id_auth_app,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CompanyModel) Reset() {
	*x = CompanyModel{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[92]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CompanyModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompanyModel) ProtoMessage() {}

func (x *CompanyModel) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[92]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompanyModel.ProtoReflect.Descriptor instead.
func (*CompanyModel) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{92}
}

func (x *CompanyModel) GetIdCompany() string {
	if x != nil {
		return x.IdCompany
	}
	return ""
}

func (x *CompanyModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CompanyModel) GetStreet() string {
	if x != nil {
		return x.Street
	}
	return ""
}

func (x *CompanyModel) GetIdState() int64 {
	if x != nil {
		return x.IdState
	}
	return 0
}

func (x *CompanyModel) GetIdCity() int64 {
	if x != nil {
		return x.IdCity
	}
	return 0
}

func (x *CompanyModel) GetIdCountry() int64 {
	if x != nil {
		return x.IdCountry
	}
	return 0
}

func (x *CompanyModel) GetLogo() string {
	if x != nil {
		return x.Logo
	}
	return ""
}

func (x *CompanyModel) GetMst() string {
	if x != nil {
		return x.Mst
	}
	return ""
}

func (x *CompanyModel) GetVerify() bool {
	if x != nil {
		return x.Verify
	}
	return false
}

func (x *CompanyModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *CompanyModel) GetIdAuthApp() string {
	if x != nil {
		return x.IdAuthApp
	}
	return ""
}

type BackofficeAuthServiceUpdateProfileRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FirstName     string                 `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName      string                 `protobuf:"bytes,2,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	Street        string                 `protobuf:"bytes,3,opt,name=street,proto3" json:"street,omitempty"`
	IdState       int64                  `protobuf:"varint,4,opt,name=id_state,json=idState,proto3" json:"id_state,omitempty"`
	IdCity        int64                  `protobuf:"varint,5,opt,name=id_city,json=idCity,proto3" json:"id_city,omitempty"`
	IdCountry     int64                  `protobuf:"varint,6,opt,name=id_country,json=idCountry,proto3" json:"id_country,omitempty"`
	IdCompany     string                 `protobuf:"bytes,7,opt,name=id_company,json=idCompany,proto3" json:"id_company,omitempty"`
	IdUser        string                 `protobuf:"bytes,8,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	IdAuthApp     string                 `protobuf:"bytes,9,opt,name=id_auth_app,json=idAuthApp,proto3" json:"id_auth_app,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateProfileRequest) Reset() {
	*x = BackofficeAuthServiceUpdateProfileRequest{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[93]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateProfileRequest) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[93]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateProfileRequest.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateProfileRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{93}
}

func (x *BackofficeAuthServiceUpdateProfileRequest) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateProfileRequest) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateProfileRequest) GetStreet() string {
	if x != nil {
		return x.Street
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateProfileRequest) GetIdState() int64 {
	if x != nil {
		return x.IdState
	}
	return 0
}

func (x *BackofficeAuthServiceUpdateProfileRequest) GetIdCity() int64 {
	if x != nil {
		return x.IdCity
	}
	return 0
}

func (x *BackofficeAuthServiceUpdateProfileRequest) GetIdCountry() int64 {
	if x != nil {
		return x.IdCountry
	}
	return 0
}

func (x *BackofficeAuthServiceUpdateProfileRequest) GetIdCompany() string {
	if x != nil {
		return x.IdCompany
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateProfileRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *BackofficeAuthServiceUpdateProfileRequest) GetIdAuthApp() string {
	if x != nil {
		return x.IdAuthApp
	}
	return ""
}

type BackofficeAuthServiceUpdateProfileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeAuthServiceUpdateProfileResponse) Reset() {
	*x = BackofficeAuthServiceUpdateProfileResponse{}
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[94]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeAuthServiceUpdateProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeAuthServiceUpdateProfileResponse) ProtoMessage() {}

func (x *BackofficeAuthServiceUpdateProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_backoffice_auth_proto_msgTypes[94]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeAuthServiceUpdateProfileResponse.ProtoReflect.Descriptor instead.
func (*BackofficeAuthServiceUpdateProfileResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_backoffice_auth_proto_rawDescGZIP(), []int{94}
}

func (x *BackofficeAuthServiceUpdateProfileResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_auth_v1_backoffice_auth_proto protoreflect.FileDescriptor

const file_auth_v1_backoffice_auth_proto_rawDesc = "" +
	"\n" +
	"\x1dauth/v1/backoffice_auth.proto\x12\aauth.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\x1a\x1aalgoenum/v1/app_type.proto\x1a algoenum/v1/template_email.proto\x1a\x1dalgoenum/v1/app_country.proto\"r\n" +
	"*BackofficeAuthServiceChangePasswordRequest\x12!\n" +
	"\fold_password\x18\x01 \x01(\tR\voldPassword\x12!\n" +
	"\fnew_password\x18\x02 \x01(\tR\vnewPassword\"\\\n" +
	"+BackofficeAuthServiceChangePasswordResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\" \n" +
	"\x1eBackofficeAuthServiceMeRequest\"\xa6\x01\n" +
	"\x1fBackofficeAuthServiceMeResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12\x14\n" +
	"\x05paths\x18\x02 \x03(\tR\x05paths\x12>\n" +
	"\vuser_detail\x18\x03 \x01(\v2\x1d.auth.v1.BackofficeUserDetailR\n" +
	"userDetail\"\xd0\x02\n" +
	"\x14BackofficeUserDetail\x12\x1d\n" +
	"\n" +
	"first_name\x18\x01 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x02 \x01(\tR\blastName\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12!\n" +
	"\fphone_number\x18\x04 \x01(\tR\vphoneNumber\x12\x19\n" +
	"\bref_code\x18\x05 \x01(\tR\arefCode\x12\x1e\n" +
	"\vuser_ref_id\x18\x06 \x01(\tR\tuserRefId\x12\x16\n" +
	"\x06street\x18\a \x01(\tR\x06street\x12\x19\n" +
	"\bid_state\x18\b \x01(\x03R\aidState\x12\x17\n" +
	"\aid_city\x18\t \x01(\x03R\x06idCity\x12\x1d\n" +
	"\n" +
	"id_country\x18\n" +
	" \x01(\x03R\tidCountry\x12\x1d\n" +
	"\n" +
	"id_company\x18\v \x01(\tR\tidCompany\",\n" +
	"*BackofficeAuthServiceReloadEnforcerRequest\"\\\n" +
	"+BackofficeAuthServiceReloadEnforcerResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x84\x01\n" +
	"!BackofficeAuthServiceLoginRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x12\x10\n" +
	"\x03otp\x18\x03 \x01(\tR\x03otp\x12\x1b\n" +
	"\tdevice_id\x18\x04 \x01(\tR\bdeviceId\"\x8e\x01\n" +
	"\"BackofficeAuthServiceLoginResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\x12#\n" +
	"\rrefresh_token\x18\x03 \x01(\tR\frefreshToken\"\xdb\x01\n" +
	"%BackofficeAuthServiceCreateAppRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12/\n" +
	"\bapp_type\x18\x02 \x01(\x0e2\x14.algoenum.v1.AppTypeR\aappType\x12\x16\n" +
	"\x06domain\x18\x03 \x01(\tR\x06domain\x12\x1b\n" +
	"\ttoken_ttl\x18\x04 \x01(\x04R\btokenTtl\x128\n" +
	"\vapp_country\x18\x05 \x01(\x0e2\x17.algoenum.v1.AppCountryR\n" +
	"appCountry\"W\n" +
	"&BackofficeAuthServiceCreateAppResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xad\x02\n" +
	"$BackofficeAuthServiceFetchAppRequest\x12\x15\n" +
	"\x06id_app\x18\x01 \x01(\tR\x05idApp\x12\x1f\n" +
	"\vname_search\x18\x02 \x01(\tR\n" +
	"nameSearch\x12/\n" +
	"\bapp_type\x18\x03 \x01(\x0e2\x14.algoenum.v1.AppTypeR\aappType\x128\n" +
	"\vapp_country\x18\x04 \x01(\x0e2\x17.algoenum.v1.AppCountryR\n" +
	"appCountry\x12%\n" +
	"\x05state\x18\x05 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x06 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xc7\x01\n" +
	"%BackofficeAuthServiceFetchAppResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x121\n" +
	"\x05items\x18\x03 \x03(\v2\x1b.auth.v1.BackofficeAppModelR\x05items\"\xb2\x02\n" +
	"%BackofficeAuthServiceUpdateAppRequest\x12\x15\n" +
	"\x06id_app\x18\x01 \x01(\tR\x05idApp\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12/\n" +
	"\bapp_type\x18\x03 \x01(\x0e2\x14.algoenum.v1.AppTypeR\aappType\x12\x1b\n" +
	"\ttoken_ttl\x18\x04 \x01(\x04R\btokenTtl\x12/\n" +
	"\x14is_change_paseto_key\x18\x05 \x01(\bR\x11isChangePasetoKey\x12%\n" +
	"\x05state\x18\x06 \x01(\v2\x0f.utils.v1.StateR\x05state\x128\n" +
	"\vapp_country\x18\a \x01(\x0e2\x17.algoenum.v1.AppCountryR\n" +
	"appCountry\"W\n" +
	"&BackofficeAuthServiceUpdateAppResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xf7\x01\n" +
	"%BackofficeAuthServiceFetchUserRequest\x12\x15\n" +
	"\x06id_app\x18\x01 \x01(\tR\x05idApp\x12\x17\n" +
	"\aid_user\x18\x02 \x01(\tR\x06idUser\x12\x17\n" +
	"\aid_role\x18\x03 \x01(\tR\x06idRole\x12!\n" +
	"\femail_search\x18\x04 \x01(\tR\vemailSearch\x12%\n" +
	"\x05state\x18\x05 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x06 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xc9\x01\n" +
	"&BackofficeAuthServiceFetchUserResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x122\n" +
	"\x05items\x18\x03 \x03(\v2\x1c.auth.v1.BackofficeUserModelR\x05items\"\xa7\x02\n" +
	"\x13BackofficeUserModel\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x12&\n" +
	"\x03app\x18\x02 \x01(\v2\x14.auth.v1.AuthUserAppR\x03app\x124\n" +
	"\x04role\x18\x03 \x01(\v2 .auth.v1.BackofficeUserRoleModelR\x04role\x12@\n" +
	"\fuser_details\x18\x04 \x01(\v2\x1d.auth.v1.BackofficeUserDetailR\vuserDetails\x12\x14\n" +
	"\x05email\x18\x05 \x01(\tR\x05email\x12$\n" +
	"\x0eis_enable_totp\x18\x06 \x01(\bR\fisEnableTotp\x12\x1b\n" +
	"\tis_active\x18\a \x01(\bR\bisActive\"\x7f\n" +
	"\x17BackofficeUserRoleModel\x12\x17\n" +
	"\aid_role\x18\x01 \x01(\tR\x06idRole\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1a\n" +
	"\bpriority\x18\x03 \x01(\x03R\bpriority\x12\x1b\n" +
	"\tis_active\x18\x04 \x01(\bR\bisActive\"U\n" +
	"\vAuthUserApp\x12\x15\n" +
	"\x06id_app\x18\x01 \x01(\tR\x05idApp\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1b\n" +
	"\tis_active\x18\x03 \x01(\bR\bisActive\"\xe9\x01\n" +
	"&BackofficeAuthServiceCreateUserRequest\x12\x15\n" +
	"\x06id_app\x18\x01 \x01(\tR\x05idApp\x12\x17\n" +
	"\aid_role\x18\x02 \x01(\tR\x06idRole\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12\x1a\n" +
	"\bpassword\x18\x04 \x01(\tR\bpassword\x12\x1d\n" +
	"\n" +
	"first_name\x18\x05 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x06 \x01(\tR\blastName\x12!\n" +
	"\fphone_number\x18\a \x01(\tR\vphoneNumber\"q\n" +
	"'BackofficeAuthServiceCreateUserResponse\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x9d\x01\n" +
	"&BackofficeAuthServiceUpdateUserRequest\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x12\x17\n" +
	"\aid_role\x18\x02 \x01(\tR\x06idRole\x12\x1a\n" +
	"\bpassword\x18\x03 \x01(\tR\bpassword\x12%\n" +
	"\x05state\x18\x04 \x01(\v2\x0f.utils.v1.StateR\x05state\"X\n" +
	"'BackofficeAuthServiceUpdateUserResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xc5\x01\n" +
	"%BackofficeAuthServiceFetchRoleRequest\x12\x17\n" +
	"\aid_role\x18\x01 \x01(\tR\x06idRole\x12\x1f\n" +
	"\vname_search\x18\x02 \x01(\tR\n" +
	"nameSearch\x12;\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\x12%\n" +
	"\x05state\x18\x04 \x01(\v2\x0f.utils.v1.StateR\x05state\"\xbf\x01\n" +
	"&BackofficeAuthServiceFetchRoleResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12(\n" +
	"\x05items\x18\x03 \x03(\v2\x12.auth.v1.RoleModelR\x05items\"q\n" +
	"\tRoleModel\x12\x17\n" +
	"\aid_role\x18\x01 \x01(\tR\x06idRole\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1a\n" +
	"\bpriority\x18\x03 \x01(\x03R\bpriority\x12\x1b\n" +
	"\tis_active\x18\x04 \x01(\bR\bisActive\"X\n" +
	"&BackofficeAuthServiceCreateRoleRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x1a\n" +
	"\bpriority\x18\x02 \x01(\x03R\bpriority\"X\n" +
	"'BackofficeAuthServiceCreateRoleResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"|\n" +
	"&BackofficeAuthServiceUpdateRoleRequest\x12\x17\n" +
	"\aid_role\x18\x01 \x01(\tR\x06idRole\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12%\n" +
	"\x05state\x18\x03 \x01(\v2\x0f.utils.v1.StateR\x05state\"X\n" +
	"'BackofficeAuthServiceUpdateRoleResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xce\x01\n" +
	"(BackofficeAuthServiceFetchServiceRequest\x12\x1d\n" +
	"\n" +
	"id_service\x18\x01 \x01(\tR\tidService\x12\x1f\n" +
	"\vname_search\x18\x02 \x01(\tR\n" +
	"nameSearch\x12%\n" +
	"\x05state\x18\x03 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x04 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xc5\x01\n" +
	")BackofficeAuthServiceFetchServiceResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12+\n" +
	"\x05items\x18\x03 \x03(\v2\x15.auth.v1.ServiceModelR\x05items\"^\n" +
	"\fServiceModel\x12\x1d\n" +
	"\n" +
	"id_service\x18\x01 \x01(\tR\tidService\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1b\n" +
	"\tis_active\x18\x03 \x01(\bR\bisActive\"?\n" +
	")BackofficeAuthServiceCreateServiceRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"[\n" +
	"*BackofficeAuthServiceCreateServiceResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x85\x01\n" +
	")BackofficeAuthServiceUpdateServiceRequest\x12\x1d\n" +
	"\n" +
	"id_service\x18\x01 \x01(\tR\tidService\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12%\n" +
	"\x05state\x18\x03 \x01(\v2\x0f.utils.v1.StateR\x05state\"[\n" +
	"*BackofficeAuthServiceUpdateServiceResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xf5\x01\n" +
	"%BackofficeAuthServiceFetchPathRequest\x12\x17\n" +
	"\aid_path\x18\x01 \x01(\tR\x06idPath\x12\x1d\n" +
	"\n" +
	"id_service\x18\x02 \x01(\tR\tidService\x120\n" +
	"\x14absolute_path_search\x18\x03 \x01(\tR\x12absolutePathSearch\x12%\n" +
	"\x05state\x18\x04 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xbf\x01\n" +
	"&BackofficeAuthServiceFetchPathResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12(\n" +
	"\x05items\x18\x03 \x03(\v2\x12.auth.v1.PathModelR\x05items\"\x97\x01\n" +
	"\tPathModel\x12/\n" +
	"\aservice\x18\x01 \x01(\v2\x15.auth.v1.ServiceModelR\aservice\x12\x17\n" +
	"\aid_path\x18\x02 \x01(\tR\x06idPath\x12#\n" +
	"\rabsolute_path\x18\x03 \x01(\tR\fabsolutePath\x12\x1b\n" +
	"\tis_active\x18\x04 \x01(\bR\bisActive\"l\n" +
	"&BackofficeAuthServiceCreatePathRequest\x12\x1d\n" +
	"\n" +
	"id_service\x18\x01 \x01(\tR\tidService\x12#\n" +
	"\rabsolute_path\x18\x02 \x01(\tR\fabsolutePath\"X\n" +
	"'BackofficeAuthServiceCreatePathResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x8d\x01\n" +
	"&BackofficeAuthServiceUpdatePathRequest\x12\x17\n" +
	"\aid_path\x18\x01 \x01(\tR\x06idPath\x12#\n" +
	"\rabsolute_path\x18\x02 \x01(\tR\fabsolutePath\x12%\n" +
	"\x05state\x18\x03 \x01(\v2\x0f.utils.v1.StateR\x05state\"X\n" +
	"'BackofficeAuthServiceUpdatePathResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xdc\x01\n" +
	"'BackofficeAuthServiceFetchPolicyRequest\x12\x1b\n" +
	"\tid_policy\x18\x01 \x01(\tR\bidPolicy\x12\x17\n" +
	"\aid_path\x18\x02 \x01(\tR\x06idPath\x12\x17\n" +
	"\aid_role\x18\x03 \x01(\tR\x06idRole\x12%\n" +
	"\x05state\x18\x04 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xc3\x01\n" +
	"(BackofficeAuthServiceFetchPolicyResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12*\n" +
	"\x05items\x18\x03 \x03(\v2\x14.auth.v1.PolicyModelR\x05items\"\xde\x01\n" +
	"\vPolicyModel\x12\x1b\n" +
	"\tid_policy\x18\x01 \x01(\tR\bidPolicy\x12/\n" +
	"\aservice\x18\x02 \x01(\v2\x15.auth.v1.ServiceModelR\aservice\x12&\n" +
	"\x04path\x18\x03 \x01(\v2\x12.auth.v1.PathModelR\x04path\x12&\n" +
	"\x04role\x18\x04 \x01(\v2\x12.auth.v1.RoleModelR\x04role\x12\x1b\n" +
	"\tis_active\x18\x05 \x01(\bR\bisActive\x12\x14\n" +
	"\x05state\x18\x06 \x01(\bR\x05state\"\\\n" +
	"(BackofficeAuthServiceCreatePolicyRequest\x12\x17\n" +
	"\aid_path\x18\x01 \x01(\tR\x06idPath\x12\x17\n" +
	"\aid_role\x18\x02 \x01(\tR\x06idRole\"Z\n" +
	")BackofficeAuthServiceCreatePolicyResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"n\n" +
	"(BackofficeAuthServiceUpdatePolicyRequest\x12\x1b\n" +
	"\tid_policy\x18\x01 \x01(\tR\bidPolicy\x12%\n" +
	"\x05state\x18\x02 \x01(\v2\x0f.utils.v1.StateR\x05state\"Z\n" +
	")BackofficeAuthServiceUpdatePolicyResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xa6\x02\n" +
	"\x12BackofficeAppModel\x12\x15\n" +
	"\x06id_app\x18\x01 \x01(\tR\x05idApp\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06domain\x18\x03 \x01(\tR\x06domain\x12/\n" +
	"\bapp_type\x18\x04 \x01(\x0e2\x14.algoenum.v1.AppTypeR\aappType\x12(\n" +
	"\x10short_public_key\x18\x05 \x01(\tR\x0eshortPublicKey\x12\x1b\n" +
	"\ttoken_ttl\x18\x06 \x01(\x04R\btokenTtl\x12\x1b\n" +
	"\tis_active\x18\a \x01(\bR\bisActive\x128\n" +
	"\vapp_country\x18\b \x01(\x0e2\x17.algoenum.v1.AppCountryR\n" +
	"appCountry\"&\n" +
	"$BackofficeAuthServiceInitTotpRequest\"o\n" +
	"%BackofficeAuthServiceInitTotpResponse\x12\x17\n" +
	"\aqr_code\x18\x01 \x01(\tR\x06qrCode\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"<\n" +
	"&BackofficeAuthServiceVerifyTotpRequest\x12\x12\n" +
	"\x04totp\x18\x01 \x01(\tR\x04totp\"X\n" +
	"'BackofficeAuthServiceVerifyTotpResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"(\n" +
	"&BackofficeAuthServiceRemoveTotpRequest\"X\n" +
	"'BackofficeAuthServiceRemoveTotpResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x89\x02\n" +
	"+BackofficeAuthServiceFetchConfigMailRequest\x12.\n" +
	"\x13smtp_server_address\x18\x01 \x01(\tR\x11smtpServerAddress\x12#\n" +
	"\rauth_username\x18\x02 \x01(\tR\fauthUsername\x12!\n" +
	"\fsender_email\x18\x03 \x01(\tR\vsenderEmail\x12%\n" +
	"\x05state\x18\x04 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xcb\x01\n" +
	",BackofficeAuthServiceFetchConfigMailResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12.\n" +
	"\x05items\x18\x02 \x03(\v2\x18.auth.v1.ConfigMailModelR\x05items\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\x95\x02\n" +
	",BackofficeAuthServiceCreateConfigMailRequest\x12\x1e\n" +
	"\vid_auth_app\x18\x01 \x01(\tR\tidAuthApp\x12.\n" +
	"\x13smtp_server_address\x18\x02 \x01(\tR\x11smtpServerAddress\x12(\n" +
	"\x10smtp_server_port\x18\x03 \x01(\x04R\x0esmtpServerPort\x12#\n" +
	"\rauth_username\x18\x04 \x01(\tR\fauthUsername\x12#\n" +
	"\rauth_password\x18\x05 \x01(\tR\fauthPassword\x12!\n" +
	"\fsender_email\x18\x06 \x01(\tR\vsenderEmail\"^\n" +
	"-BackofficeAuthServiceCreateConfigMailResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xe2\x02\n" +
	",BackofficeAuthServiceUpdateConfigMailRequest\x12\x1e\n" +
	"\vid_auth_app\x18\x01 \x01(\tR\tidAuthApp\x12.\n" +
	"\x13smtp_server_address\x18\x02 \x01(\tR\x11smtpServerAddress\x12(\n" +
	"\x10smtp_server_port\x18\x03 \x01(\x04R\x0esmtpServerPort\x12#\n" +
	"\rauth_username\x18\x04 \x01(\tR\fauthUsername\x12#\n" +
	"\rauth_password\x18\x05 \x01(\tR\fauthPassword\x12!\n" +
	"\fsender_email\x18\x06 \x01(\tR\vsenderEmail\x12%\n" +
	"\x05state\x18\a \x01(\v2\x0f.utils.v1.StateR\x05state\x12$\n" +
	"\x0eid_config_mail\x18\b \x01(\tR\fidConfigMail\"^\n" +
	"-BackofficeAuthServiceUpdateConfigMailResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xbb\x02\n" +
	"\x0fConfigMailModel\x12$\n" +
	"\x0eid_config_mail\x18\x01 \x01(\tR\fidConfigMail\x12\x1e\n" +
	"\vid_auth_app\x18\x02 \x01(\tR\tidAuthApp\x12.\n" +
	"\x13smtp_server_address\x18\x03 \x01(\tR\x11smtpServerAddress\x12(\n" +
	"\x10smtp_server_port\x18\x04 \x01(\x04R\x0esmtpServerPort\x12#\n" +
	"\rauth_username\x18\x05 \x01(\tR\fauthUsername\x12#\n" +
	"\rauth_password\x18\x06 \x01(\tR\fauthPassword\x12!\n" +
	"\fsender_email\x18\a \x01(\tR\vsenderEmail\x12\x1b\n" +
	"\tis_active\x18\b \x01(\bR\bisActive\"\xee\x01\n" +
	"4BackofficeAuthServiceFetchConfigTemplateEmailRequest\x122\n" +
	"\x04name\x18\x01 \x01(\x0e2\x1e.algoenum.v1.TemplateEmailTypeR\x04name\x12\x1e\n" +
	"\vid_auth_app\x18\x02 \x01(\tR\tidAuthApp\x12%\n" +
	"\x05state\x18\x03 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x04 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xdd\x01\n" +
	"5BackofficeAuthServiceFetchConfigTemplateEmailResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x127\n" +
	"\x05items\x18\x02 \x03(\v2!.auth.v1.ConfigTemplateEmailModelR\x05items\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\xa5\x01\n" +
	"5BackofficeAuthServiceCreateConfigTemplateEmailRequest\x12\x1e\n" +
	"\vid_auth_app\x18\x01 \x01(\tR\tidAuthApp\x122\n" +
	"\x04name\x18\x02 \x01(\x0e2\x1e.algoenum.v1.TemplateEmailTypeR\x04name\x12\x18\n" +
	"\acontent\x18\x03 \x01(\tR\acontent\"g\n" +
	"6BackofficeAuthServiceCreateConfigTemplateEmailResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xb1\x01\n" +
	"5BackofficeAuthServiceUpdateConfigTemplateEmailRequest\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\x12%\n" +
	"\x05state\x18\x02 \x01(\v2\x0f.utils.v1.StateR\x05state\x127\n" +
	"\x18id_config_template_email\x18\x03 \x01(\tR\x15idConfigTemplateEmail\"g\n" +
	"6BackofficeAuthServiceUpdateConfigTemplateEmailResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xde\x01\n" +
	"\x18ConfigTemplateEmailModel\x127\n" +
	"\x18id_config_template_email\x18\x01 \x01(\tR\x15idConfigTemplateEmail\x12\x1e\n" +
	"\vid_auth_app\x18\x02 \x01(\tR\tidAuthApp\x122\n" +
	"\x04name\x18\x03 \x01(\x0e2\x1e.algoenum.v1.TemplateEmailTypeR\x04name\x12\x18\n" +
	"\acontent\x18\x04 \x01(\tR\acontent\x12\x1b\n" +
	"\tis_active\x18\x05 \x01(\bR\bisActive\"l\n" +
	"(BackofficeAuthServiceRefreshTokenRequest\x12#\n" +
	"\rrefresh_token\x18\x01 \x01(\tR\frefreshToken\x12\x1b\n" +
	"\tdevice_id\x18\x02 \x01(\tR\bdeviceId\"\x95\x01\n" +
	")BackofficeAuthServiceRefreshTokenResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\x12#\n" +
	"\rrefresh_token\x18\x03 \x01(\tR\frefreshToken\"_\n" +
	")BackofficeAuthServiceUpdateRefCodeRequest\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\tR\x06userId\x12\x19\n" +
	"\bref_code\x18\x02 \x01(\tR\arefCode\"[\n" +
	"*BackofficeAuthServiceUpdateRefCodeResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"w\n" +
	"*BackofficeAuthServiceForgotPasswordRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12\x10\n" +
	"\x03otp\x18\x02 \x01(\tR\x03otp\x12!\n" +
	"\fnew_password\x18\x03 \x01(\tR\vnewPassword\"\\\n" +
	"+BackofficeAuthServiceForgotPasswordResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xb2\x01\n" +
	",BackofficeAuthServiceFetchOAuthConfigRequest\x12\x1e\n" +
	"\vid_auth_app\x18\x01 \x01(\tR\tidAuthApp\x12%\n" +
	"\x05state\x18\x02 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xcd\x01\n" +
	"-BackofficeAuthServiceFetchOAuthConfigResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12/\n" +
	"\x05items\x18\x02 \x03(\v2\x19.auth.v1.OAuthConfigModelR\x05items\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\x91\x01\n" +
	"-BackofficeAuthServiceCreateOAuthConfigRequest\x12\x1e\n" +
	"\vid_auth_app\x18\x01 \x01(\tR\tidAuthApp\x12\x1b\n" +
	"\tclient_id\x18\x02 \x01(\tR\bclientId\x12#\n" +
	"\rclient_secret\x18\x03 \x01(\tR\fclientSecret\"_\n" +
	".BackofficeAuthServiceCreateOAuthConfigResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xe1\x01\n" +
	"-BackofficeAuthServiceUpdateOAuthConfigRequest\x12\x1e\n" +
	"\vid_auth_app\x18\x01 \x01(\tR\tidAuthApp\x12\x1b\n" +
	"\tclient_id\x18\x02 \x01(\tR\bclientId\x12#\n" +
	"\rclient_secret\x18\x03 \x01(\tR\fclientSecret\x12%\n" +
	"\x05state\x18\x04 \x01(\v2\x0f.utils.v1.StateR\x05state\x12'\n" +
	"\x10id_o_auth_config\x18\x05 \x01(\tR\ridOAuthConfig\"_\n" +
	".BackofficeAuthServiceUpdateOAuthConfigResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xba\x01\n" +
	"\x10OAuthConfigModel\x12'\n" +
	"\x10id_o_auth_config\x18\x01 \x01(\tR\ridOAuthConfig\x12\x1e\n" +
	"\vid_auth_app\x18\x02 \x01(\tR\tidAuthApp\x12\x1b\n" +
	"\tclient_id\x18\x03 \x01(\tR\bclientId\x12#\n" +
	"\rclient_secret\x18\x04 \x01(\tR\fclientSecret\x12\x1b\n" +
	"\tis_active\x18\x05 \x01(\bR\bisActive\"\xe7\x02\n" +
	"(BackofficeAuthServiceFetchCompanyRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x1e\n" +
	"\vid_auth_app\x18\x02 \x01(\tR\tidAuthApp\x12\x16\n" +
	"\x06street\x18\x03 \x01(\tR\x06street\x12\x19\n" +
	"\bid_state\x18\x04 \x01(\x03R\aidState\x12\x17\n" +
	"\aid_city\x18\x05 \x01(\x03R\x06idCity\x12\x1d\n" +
	"\n" +
	"id_country\x18\x06 \x01(\x03R\tidCountry\x12\x10\n" +
	"\x03mst\x18\a \x01(\tR\x03mst\x12&\n" +
	"\x06verify\x18\b \x01(\v2\x0e.utils.v1.BoolR\x06verify\x12%\n" +
	"\x05state\x18\t \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\n" +
	" \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xc5\x01\n" +
	")BackofficeAuthServiceFetchCompanyResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12+\n" +
	"\x05items\x18\x02 \x03(\v2\x15.auth.v1.CompanyModelR\x05items\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\xf0\x01\n" +
	")BackofficeAuthServiceCreateCompanyRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06street\x18\x02 \x01(\tR\x06street\x12\x19\n" +
	"\bid_state\x18\x03 \x01(\x03R\aidState\x12\x17\n" +
	"\aid_city\x18\x04 \x01(\x03R\x06idCity\x12\x1d\n" +
	"\n" +
	"id_country\x18\x05 \x01(\x03R\tidCountry\x12\x12\n" +
	"\x04logo\x18\x06 \x01(\tR\x04logo\x12\x10\n" +
	"\x03mst\x18\a \x01(\tR\x03mst\x12\x1e\n" +
	"\vid_auth_app\x18\b \x01(\tR\tidAuthApp\"[\n" +
	"*BackofficeAuthServiceCreateCompanyResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xce\x02\n" +
	")BackofficeAuthServiceUpdateCompanyRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x16\n" +
	"\x06street\x18\x02 \x01(\tR\x06street\x12\x19\n" +
	"\bid_state\x18\x03 \x01(\x03R\aidState\x12\x17\n" +
	"\aid_city\x18\x04 \x01(\x03R\x06idCity\x12\x1d\n" +
	"\n" +
	"id_country\x18\x06 \x01(\x03R\tidCountry\x12\x12\n" +
	"\x04logo\x18\a \x01(\tR\x04logo\x12\x10\n" +
	"\x03mst\x18\b \x01(\tR\x03mst\x12\x16\n" +
	"\x06verify\x18\t \x01(\bR\x06verify\x12%\n" +
	"\x05state\x18\n" +
	" \x01(\v2\x0f.utils.v1.StateR\x05state\x12\x1d\n" +
	"\n" +
	"id_company\x18\v \x01(\tR\tidCompany\x12\x1e\n" +
	"\vid_auth_app\x18\f \x01(\tR\tidAuthApp\"[\n" +
	"*BackofficeAuthServiceUpdateCompanyResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xa7\x02\n" +
	"\fCompanyModel\x12\x1d\n" +
	"\n" +
	"id_company\x18\x01 \x01(\tR\tidCompany\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06street\x18\x03 \x01(\tR\x06street\x12\x19\n" +
	"\bid_state\x18\x04 \x01(\x03R\aidState\x12\x17\n" +
	"\aid_city\x18\x05 \x01(\x03R\x06idCity\x12\x1d\n" +
	"\n" +
	"id_country\x18\x06 \x01(\x03R\tidCountry\x12\x12\n" +
	"\x04logo\x18\a \x01(\tR\x04logo\x12\x10\n" +
	"\x03mst\x18\b \x01(\tR\x03mst\x12\x16\n" +
	"\x06verify\x18\t \x01(\bR\x06verify\x12\x1b\n" +
	"\tis_active\x18\n" +
	" \x01(\bR\bisActive\x12\x1e\n" +
	"\vid_auth_app\x18\v \x01(\tR\tidAuthApp\"\xaa\x02\n" +
	")BackofficeAuthServiceUpdateProfileRequest\x12\x1d\n" +
	"\n" +
	"first_name\x18\x01 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x02 \x01(\tR\blastName\x12\x16\n" +
	"\x06street\x18\x03 \x01(\tR\x06street\x12\x19\n" +
	"\bid_state\x18\x04 \x01(\x03R\aidState\x12\x17\n" +
	"\aid_city\x18\x05 \x01(\x03R\x06idCity\x12\x1d\n" +
	"\n" +
	"id_country\x18\x06 \x01(\x03R\tidCountry\x12\x1d\n" +
	"\n" +
	"id_company\x18\a \x01(\tR\tidCompany\x12\x17\n" +
	"\aid_user\x18\b \x01(\tR\x06idUser\x12\x1e\n" +
	"\vid_auth_app\x18\t \x01(\tR\tidAuthApp\"[\n" +
	"*BackofficeAuthServiceUpdateProfileResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error2\xe4&\n" +
	"\x15BackofficeAuthService\x12`\n" +
	"\x05Login\x12*.auth.v1.BackofficeAuthServiceLoginRequest\x1a+.auth.v1.BackofficeAuthServiceLoginResponse\x12W\n" +
	"\x02Me\x12'.auth.v1.BackofficeAuthServiceMeRequest\x1a(.auth.v1.BackofficeAuthServiceMeResponse\x12{\n" +
	"\x0eChangePassword\x123.auth.v1.BackofficeAuthServiceChangePasswordRequest\x1a4.auth.v1.BackofficeAuthServiceChangePasswordResponse\x12u\n" +
	"\fRefreshToken\x121.auth.v1.BackofficeAuthServiceRefreshTokenRequest\x1a2.auth.v1.BackofficeAuthServiceRefreshTokenResponse\x12{\n" +
	"\x0eForgotPassword\x123.auth.v1.BackofficeAuthServiceForgotPasswordRequest\x1a4.auth.v1.BackofficeAuthServiceForgotPasswordResponse\x12{\n" +
	"\x0eReloadEnforcer\x123.auth.v1.BackofficeAuthServiceReloadEnforcerRequest\x1a4.auth.v1.BackofficeAuthServiceReloadEnforcerResponse\x12l\n" +
	"\tCreateApp\x12..auth.v1.BackofficeAuthServiceCreateAppRequest\x1a/.auth.v1.BackofficeAuthServiceCreateAppResponse\x12i\n" +
	"\bFetchApp\x12-.auth.v1.BackofficeAuthServiceFetchAppRequest\x1a..auth.v1.BackofficeAuthServiceFetchAppResponse\x12l\n" +
	"\tUpdateApp\x12..auth.v1.BackofficeAuthServiceUpdateAppRequest\x1a/.auth.v1.BackofficeAuthServiceUpdateAppResponse\x12l\n" +
	"\tFetchUser\x12..auth.v1.BackofficeAuthServiceFetchUserRequest\x1a/.auth.v1.BackofficeAuthServiceFetchUserResponse\x12o\n" +
	"\n" +
	"CreateUser\x12/.auth.v1.BackofficeAuthServiceCreateUserRequest\x1a0.auth.v1.BackofficeAuthServiceCreateUserResponse\x12o\n" +
	"\n" +
	"UpdateUser\x12/.auth.v1.BackofficeAuthServiceUpdateUserRequest\x1a0.auth.v1.BackofficeAuthServiceUpdateUserResponse\x12l\n" +
	"\tFetchRole\x12..auth.v1.BackofficeAuthServiceFetchRoleRequest\x1a/.auth.v1.BackofficeAuthServiceFetchRoleResponse\x12o\n" +
	"\n" +
	"CreateRole\x12/.auth.v1.BackofficeAuthServiceCreateRoleRequest\x1a0.auth.v1.BackofficeAuthServiceCreateRoleResponse\x12o\n" +
	"\n" +
	"UpdateRole\x12/.auth.v1.BackofficeAuthServiceUpdateRoleRequest\x1a0.auth.v1.BackofficeAuthServiceUpdateRoleResponse\x12u\n" +
	"\fFetchService\x121.auth.v1.BackofficeAuthServiceFetchServiceRequest\x1a2.auth.v1.BackofficeAuthServiceFetchServiceResponse\x12x\n" +
	"\rCreateService\x122.auth.v1.BackofficeAuthServiceCreateServiceRequest\x1a3.auth.v1.BackofficeAuthServiceCreateServiceResponse\x12x\n" +
	"\rUpdateService\x122.auth.v1.BackofficeAuthServiceUpdateServiceRequest\x1a3.auth.v1.BackofficeAuthServiceUpdateServiceResponse\x12l\n" +
	"\tFetchPath\x12..auth.v1.BackofficeAuthServiceFetchPathRequest\x1a/.auth.v1.BackofficeAuthServiceFetchPathResponse\x12o\n" +
	"\n" +
	"CreatePath\x12/.auth.v1.BackofficeAuthServiceCreatePathRequest\x1a0.auth.v1.BackofficeAuthServiceCreatePathResponse\x12o\n" +
	"\n" +
	"UpdatePath\x12/.auth.v1.BackofficeAuthServiceUpdatePathRequest\x1a0.auth.v1.BackofficeAuthServiceUpdatePathResponse\x12r\n" +
	"\vFetchPolicy\x120.auth.v1.BackofficeAuthServiceFetchPolicyRequest\x1a1.auth.v1.BackofficeAuthServiceFetchPolicyResponse\x12u\n" +
	"\fCreatePolicy\x121.auth.v1.BackofficeAuthServiceCreatePolicyRequest\x1a2.auth.v1.BackofficeAuthServiceCreatePolicyResponse\x12u\n" +
	"\fUpdatePolicy\x121.auth.v1.BackofficeAuthServiceUpdatePolicyRequest\x1a2.auth.v1.BackofficeAuthServiceUpdatePolicyResponse\x12i\n" +
	"\bInitTotp\x12-.auth.v1.BackofficeAuthServiceInitTotpRequest\x1a..auth.v1.BackofficeAuthServiceInitTotpResponse\x12o\n" +
	"\n" +
	"VerifyTotp\x12/.auth.v1.BackofficeAuthServiceVerifyTotpRequest\x1a0.auth.v1.BackofficeAuthServiceVerifyTotpResponse\x12o\n" +
	"\n" +
	"RemoveTotp\x12/.auth.v1.BackofficeAuthServiceRemoveTotpRequest\x1a0.auth.v1.BackofficeAuthServiceRemoveTotpResponse\x12~\n" +
	"\x0fFetchConfigMail\x124.auth.v1.BackofficeAuthServiceFetchConfigMailRequest\x1a5.auth.v1.BackofficeAuthServiceFetchConfigMailResponse\x12\x81\x01\n" +
	"\x10CreateConfigMail\x125.auth.v1.BackofficeAuthServiceCreateConfigMailRequest\x1a6.auth.v1.BackofficeAuthServiceCreateConfigMailResponse\x12\x81\x01\n" +
	"\x10UpdateConfigMail\x125.auth.v1.BackofficeAuthServiceUpdateConfigMailRequest\x1a6.auth.v1.BackofficeAuthServiceUpdateConfigMailResponse\x12\x99\x01\n" +
	"\x18FetchConfigTemplateEmail\x12=.auth.v1.BackofficeAuthServiceFetchConfigTemplateEmailRequest\x1a>.auth.v1.BackofficeAuthServiceFetchConfigTemplateEmailResponse\x12\x9c\x01\n" +
	"\x19CreateConfigTemplateEmail\x12>.auth.v1.BackofficeAuthServiceCreateConfigTemplateEmailRequest\x1a?.auth.v1.BackofficeAuthServiceCreateConfigTemplateEmailResponse\x12\x9c\x01\n" +
	"\x19UpdateConfigTemplateEmail\x12>.auth.v1.BackofficeAuthServiceUpdateConfigTemplateEmailRequest\x1a?.auth.v1.BackofficeAuthServiceUpdateConfigTemplateEmailResponse\x12x\n" +
	"\rUpdateRefCode\x122.auth.v1.BackofficeAuthServiceUpdateRefCodeRequest\x1a3.auth.v1.BackofficeAuthServiceUpdateRefCodeResponse\x12\x81\x01\n" +
	"\x10FetchOAuthConfig\x125.auth.v1.BackofficeAuthServiceFetchOAuthConfigRequest\x1a6.auth.v1.BackofficeAuthServiceFetchOAuthConfigResponse\x12\x84\x01\n" +
	"\x11CreateOAuthConfig\x126.auth.v1.BackofficeAuthServiceCreateOAuthConfigRequest\x1a7.auth.v1.BackofficeAuthServiceCreateOAuthConfigResponse\x12\x84\x01\n" +
	"\x11UpdateOAuthConfig\x126.auth.v1.BackofficeAuthServiceUpdateOAuthConfigRequest\x1a7.auth.v1.BackofficeAuthServiceUpdateOAuthConfigResponse\x12u\n" +
	"\fFetchCompany\x121.auth.v1.BackofficeAuthServiceFetchCompanyRequest\x1a2.auth.v1.BackofficeAuthServiceFetchCompanyResponse\x12x\n" +
	"\rCreateCompany\x122.auth.v1.BackofficeAuthServiceCreateCompanyRequest\x1a3.auth.v1.BackofficeAuthServiceCreateCompanyResponse\x12x\n" +
	"\rUpdateCompany\x122.auth.v1.BackofficeAuthServiceUpdateCompanyRequest\x1a3.auth.v1.BackofficeAuthServiceUpdateCompanyResponse\x12x\n" +
	"\rUpdateProfile\x122.auth.v1.BackofficeAuthServiceUpdateProfileRequest\x1a3.auth.v1.BackofficeAuthServiceUpdateProfileResponseB?Z=git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1;authv1b\x06proto3"

var (
	file_auth_v1_backoffice_auth_proto_rawDescOnce sync.Once
	file_auth_v1_backoffice_auth_proto_rawDescData []byte
)

func file_auth_v1_backoffice_auth_proto_rawDescGZIP() []byte {
	file_auth_v1_backoffice_auth_proto_rawDescOnce.Do(func() {
		file_auth_v1_backoffice_auth_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_auth_v1_backoffice_auth_proto_rawDesc), len(file_auth_v1_backoffice_auth_proto_rawDesc)))
	})
	return file_auth_v1_backoffice_auth_proto_rawDescData
}

var file_auth_v1_backoffice_auth_proto_msgTypes = make([]protoimpl.MessageInfo, 95)
var file_auth_v1_backoffice_auth_proto_goTypes = []any{
	(*BackofficeAuthServiceChangePasswordRequest)(nil),             // 0: auth.v1.BackofficeAuthServiceChangePasswordRequest
	(*BackofficeAuthServiceChangePasswordResponse)(nil),            // 1: auth.v1.BackofficeAuthServiceChangePasswordResponse
	(*BackofficeAuthServiceMeRequest)(nil),                         // 2: auth.v1.BackofficeAuthServiceMeRequest
	(*BackofficeAuthServiceMeResponse)(nil),                        // 3: auth.v1.BackofficeAuthServiceMeResponse
	(*BackofficeUserDetail)(nil),                                   // 4: auth.v1.BackofficeUserDetail
	(*BackofficeAuthServiceReloadEnforcerRequest)(nil),             // 5: auth.v1.BackofficeAuthServiceReloadEnforcerRequest
	(*BackofficeAuthServiceReloadEnforcerResponse)(nil),            // 6: auth.v1.BackofficeAuthServiceReloadEnforcerResponse
	(*BackofficeAuthServiceLoginRequest)(nil),                      // 7: auth.v1.BackofficeAuthServiceLoginRequest
	(*BackofficeAuthServiceLoginResponse)(nil),                     // 8: auth.v1.BackofficeAuthServiceLoginResponse
	(*BackofficeAuthServiceCreateAppRequest)(nil),                  // 9: auth.v1.BackofficeAuthServiceCreateAppRequest
	(*BackofficeAuthServiceCreateAppResponse)(nil),                 // 10: auth.v1.BackofficeAuthServiceCreateAppResponse
	(*BackofficeAuthServiceFetchAppRequest)(nil),                   // 11: auth.v1.BackofficeAuthServiceFetchAppRequest
	(*BackofficeAuthServiceFetchAppResponse)(nil),                  // 12: auth.v1.BackofficeAuthServiceFetchAppResponse
	(*BackofficeAuthServiceUpdateAppRequest)(nil),                  // 13: auth.v1.BackofficeAuthServiceUpdateAppRequest
	(*BackofficeAuthServiceUpdateAppResponse)(nil),                 // 14: auth.v1.BackofficeAuthServiceUpdateAppResponse
	(*BackofficeAuthServiceFetchUserRequest)(nil),                  // 15: auth.v1.BackofficeAuthServiceFetchUserRequest
	(*BackofficeAuthServiceFetchUserResponse)(nil),                 // 16: auth.v1.BackofficeAuthServiceFetchUserResponse
	(*BackofficeUserModel)(nil),                                    // 17: auth.v1.BackofficeUserModel
	(*BackofficeUserRoleModel)(nil),                                // 18: auth.v1.BackofficeUserRoleModel
	(*AuthUserApp)(nil),                                            // 19: auth.v1.AuthUserApp
	(*BackofficeAuthServiceCreateUserRequest)(nil),                 // 20: auth.v1.BackofficeAuthServiceCreateUserRequest
	(*BackofficeAuthServiceCreateUserResponse)(nil),                // 21: auth.v1.BackofficeAuthServiceCreateUserResponse
	(*BackofficeAuthServiceUpdateUserRequest)(nil),                 // 22: auth.v1.BackofficeAuthServiceUpdateUserRequest
	(*BackofficeAuthServiceUpdateUserResponse)(nil),                // 23: auth.v1.BackofficeAuthServiceUpdateUserResponse
	(*BackofficeAuthServiceFetchRoleRequest)(nil),                  // 24: auth.v1.BackofficeAuthServiceFetchRoleRequest
	(*BackofficeAuthServiceFetchRoleResponse)(nil),                 // 25: auth.v1.BackofficeAuthServiceFetchRoleResponse
	(*RoleModel)(nil),                                              // 26: auth.v1.RoleModel
	(*BackofficeAuthServiceCreateRoleRequest)(nil),                 // 27: auth.v1.BackofficeAuthServiceCreateRoleRequest
	(*BackofficeAuthServiceCreateRoleResponse)(nil),                // 28: auth.v1.BackofficeAuthServiceCreateRoleResponse
	(*BackofficeAuthServiceUpdateRoleRequest)(nil),                 // 29: auth.v1.BackofficeAuthServiceUpdateRoleRequest
	(*BackofficeAuthServiceUpdateRoleResponse)(nil),                // 30: auth.v1.BackofficeAuthServiceUpdateRoleResponse
	(*BackofficeAuthServiceFetchServiceRequest)(nil),               // 31: auth.v1.BackofficeAuthServiceFetchServiceRequest
	(*BackofficeAuthServiceFetchServiceResponse)(nil),              // 32: auth.v1.BackofficeAuthServiceFetchServiceResponse
	(*ServiceModel)(nil),                                           // 33: auth.v1.ServiceModel
	(*BackofficeAuthServiceCreateServiceRequest)(nil),              // 34: auth.v1.BackofficeAuthServiceCreateServiceRequest
	(*BackofficeAuthServiceCreateServiceResponse)(nil),             // 35: auth.v1.BackofficeAuthServiceCreateServiceResponse
	(*BackofficeAuthServiceUpdateServiceRequest)(nil),              // 36: auth.v1.BackofficeAuthServiceUpdateServiceRequest
	(*BackofficeAuthServiceUpdateServiceResponse)(nil),             // 37: auth.v1.BackofficeAuthServiceUpdateServiceResponse
	(*BackofficeAuthServiceFetchPathRequest)(nil),                  // 38: auth.v1.BackofficeAuthServiceFetchPathRequest
	(*BackofficeAuthServiceFetchPathResponse)(nil),                 // 39: auth.v1.BackofficeAuthServiceFetchPathResponse
	(*PathModel)(nil),                                              // 40: auth.v1.PathModel
	(*BackofficeAuthServiceCreatePathRequest)(nil),                 // 41: auth.v1.BackofficeAuthServiceCreatePathRequest
	(*BackofficeAuthServiceCreatePathResponse)(nil),                // 42: auth.v1.BackofficeAuthServiceCreatePathResponse
	(*BackofficeAuthServiceUpdatePathRequest)(nil),                 // 43: auth.v1.BackofficeAuthServiceUpdatePathRequest
	(*BackofficeAuthServiceUpdatePathResponse)(nil),                // 44: auth.v1.BackofficeAuthServiceUpdatePathResponse
	(*BackofficeAuthServiceFetchPolicyRequest)(nil),                // 45: auth.v1.BackofficeAuthServiceFetchPolicyRequest
	(*BackofficeAuthServiceFetchPolicyResponse)(nil),               // 46: auth.v1.BackofficeAuthServiceFetchPolicyResponse
	(*PolicyModel)(nil),                                            // 47: auth.v1.PolicyModel
	(*BackofficeAuthServiceCreatePolicyRequest)(nil),               // 48: auth.v1.BackofficeAuthServiceCreatePolicyRequest
	(*BackofficeAuthServiceCreatePolicyResponse)(nil),              // 49: auth.v1.BackofficeAuthServiceCreatePolicyResponse
	(*BackofficeAuthServiceUpdatePolicyRequest)(nil),               // 50: auth.v1.BackofficeAuthServiceUpdatePolicyRequest
	(*BackofficeAuthServiceUpdatePolicyResponse)(nil),              // 51: auth.v1.BackofficeAuthServiceUpdatePolicyResponse
	(*BackofficeAppModel)(nil),                                     // 52: auth.v1.BackofficeAppModel
	(*BackofficeAuthServiceInitTotpRequest)(nil),                   // 53: auth.v1.BackofficeAuthServiceInitTotpRequest
	(*BackofficeAuthServiceInitTotpResponse)(nil),                  // 54: auth.v1.BackofficeAuthServiceInitTotpResponse
	(*BackofficeAuthServiceVerifyTotpRequest)(nil),                 // 55: auth.v1.BackofficeAuthServiceVerifyTotpRequest
	(*BackofficeAuthServiceVerifyTotpResponse)(nil),                // 56: auth.v1.BackofficeAuthServiceVerifyTotpResponse
	(*BackofficeAuthServiceRemoveTotpRequest)(nil),                 // 57: auth.v1.BackofficeAuthServiceRemoveTotpRequest
	(*BackofficeAuthServiceRemoveTotpResponse)(nil),                // 58: auth.v1.BackofficeAuthServiceRemoveTotpResponse
	(*BackofficeAuthServiceFetchConfigMailRequest)(nil),            // 59: auth.v1.BackofficeAuthServiceFetchConfigMailRequest
	(*BackofficeAuthServiceFetchConfigMailResponse)(nil),           // 60: auth.v1.BackofficeAuthServiceFetchConfigMailResponse
	(*BackofficeAuthServiceCreateConfigMailRequest)(nil),           // 61: auth.v1.BackofficeAuthServiceCreateConfigMailRequest
	(*BackofficeAuthServiceCreateConfigMailResponse)(nil),          // 62: auth.v1.BackofficeAuthServiceCreateConfigMailResponse
	(*BackofficeAuthServiceUpdateConfigMailRequest)(nil),           // 63: auth.v1.BackofficeAuthServiceUpdateConfigMailRequest
	(*BackofficeAuthServiceUpdateConfigMailResponse)(nil),          // 64: auth.v1.BackofficeAuthServiceUpdateConfigMailResponse
	(*ConfigMailModel)(nil),                                        // 65: auth.v1.ConfigMailModel
	(*BackofficeAuthServiceFetchConfigTemplateEmailRequest)(nil),   // 66: auth.v1.BackofficeAuthServiceFetchConfigTemplateEmailRequest
	(*BackofficeAuthServiceFetchConfigTemplateEmailResponse)(nil),  // 67: auth.v1.BackofficeAuthServiceFetchConfigTemplateEmailResponse
	(*BackofficeAuthServiceCreateConfigTemplateEmailRequest)(nil),  // 68: auth.v1.BackofficeAuthServiceCreateConfigTemplateEmailRequest
	(*BackofficeAuthServiceCreateConfigTemplateEmailResponse)(nil), // 69: auth.v1.BackofficeAuthServiceCreateConfigTemplateEmailResponse
	(*BackofficeAuthServiceUpdateConfigTemplateEmailRequest)(nil),  // 70: auth.v1.BackofficeAuthServiceUpdateConfigTemplateEmailRequest
	(*BackofficeAuthServiceUpdateConfigTemplateEmailResponse)(nil), // 71: auth.v1.BackofficeAuthServiceUpdateConfigTemplateEmailResponse
	(*ConfigTemplateEmailModel)(nil),                               // 72: auth.v1.ConfigTemplateEmailModel
	(*BackofficeAuthServiceRefreshTokenRequest)(nil),               // 73: auth.v1.BackofficeAuthServiceRefreshTokenRequest
	(*BackofficeAuthServiceRefreshTokenResponse)(nil),              // 74: auth.v1.BackofficeAuthServiceRefreshTokenResponse
	(*BackofficeAuthServiceUpdateRefCodeRequest)(nil),              // 75: auth.v1.BackofficeAuthServiceUpdateRefCodeRequest
	(*BackofficeAuthServiceUpdateRefCodeResponse)(nil),             // 76: auth.v1.BackofficeAuthServiceUpdateRefCodeResponse
	(*BackofficeAuthServiceForgotPasswordRequest)(nil),             // 77: auth.v1.BackofficeAuthServiceForgotPasswordRequest
	(*BackofficeAuthServiceForgotPasswordResponse)(nil),            // 78: auth.v1.BackofficeAuthServiceForgotPasswordResponse
	(*BackofficeAuthServiceFetchOAuthConfigRequest)(nil),           // 79: auth.v1.BackofficeAuthServiceFetchOAuthConfigRequest
	(*BackofficeAuthServiceFetchOAuthConfigResponse)(nil),          // 80: auth.v1.BackofficeAuthServiceFetchOAuthConfigResponse
	(*BackofficeAuthServiceCreateOAuthConfigRequest)(nil),          // 81: auth.v1.BackofficeAuthServiceCreateOAuthConfigRequest
	(*BackofficeAuthServiceCreateOAuthConfigResponse)(nil),         // 82: auth.v1.BackofficeAuthServiceCreateOAuthConfigResponse
	(*BackofficeAuthServiceUpdateOAuthConfigRequest)(nil),          // 83: auth.v1.BackofficeAuthServiceUpdateOAuthConfigRequest
	(*BackofficeAuthServiceUpdateOAuthConfigResponse)(nil),         // 84: auth.v1.BackofficeAuthServiceUpdateOAuthConfigResponse
	(*OAuthConfigModel)(nil),                                       // 85: auth.v1.OAuthConfigModel
	(*BackofficeAuthServiceFetchCompanyRequest)(nil),               // 86: auth.v1.BackofficeAuthServiceFetchCompanyRequest
	(*BackofficeAuthServiceFetchCompanyResponse)(nil),              // 87: auth.v1.BackofficeAuthServiceFetchCompanyResponse
	(*BackofficeAuthServiceCreateCompanyRequest)(nil),              // 88: auth.v1.BackofficeAuthServiceCreateCompanyRequest
	(*BackofficeAuthServiceCreateCompanyResponse)(nil),             // 89: auth.v1.BackofficeAuthServiceCreateCompanyResponse
	(*BackofficeAuthServiceUpdateCompanyRequest)(nil),              // 90: auth.v1.BackofficeAuthServiceUpdateCompanyRequest
	(*BackofficeAuthServiceUpdateCompanyResponse)(nil),             // 91: auth.v1.BackofficeAuthServiceUpdateCompanyResponse
	(*CompanyModel)(nil),                                           // 92: auth.v1.CompanyModel
	(*BackofficeAuthServiceUpdateProfileRequest)(nil),              // 93: auth.v1.BackofficeAuthServiceUpdateProfileRequest
	(*BackofficeAuthServiceUpdateProfileResponse)(nil),             // 94: auth.v1.BackofficeAuthServiceUpdateProfileResponse
	(*v1.ErrorMessage)(nil),                                        // 95: errmsg.v1.ErrorMessage
	(v11.AppType)(0),                                               // 96: algoenum.v1.AppType
	(v11.AppCountry)(0),                                            // 97: algoenum.v1.AppCountry
	(*v12.State)(nil),                                              // 98: utils.v1.State
	(*v12.PaginationRequest)(nil),                                  // 99: utils.v1.PaginationRequest
	(*v12.PaginationResponse)(nil),                                 // 100: utils.v1.PaginationResponse
	(v11.TemplateEmailType)(0),                                     // 101: algoenum.v1.TemplateEmailType
	(*v12.Bool)(nil),                                               // 102: utils.v1.Bool
}
var file_auth_v1_backoffice_auth_proto_depIdxs = []int32{
	95,  // 0: auth.v1.BackofficeAuthServiceChangePasswordResponse.error:type_name -> errmsg.v1.ErrorMessage
	95,  // 1: auth.v1.BackofficeAuthServiceMeResponse.error:type_name -> errmsg.v1.ErrorMessage
	4,   // 2: auth.v1.BackofficeAuthServiceMeResponse.user_detail:type_name -> auth.v1.BackofficeUserDetail
	95,  // 3: auth.v1.BackofficeAuthServiceReloadEnforcerResponse.error:type_name -> errmsg.v1.ErrorMessage
	95,  // 4: auth.v1.BackofficeAuthServiceLoginResponse.error:type_name -> errmsg.v1.ErrorMessage
	96,  // 5: auth.v1.BackofficeAuthServiceCreateAppRequest.app_type:type_name -> algoenum.v1.AppType
	97,  // 6: auth.v1.BackofficeAuthServiceCreateAppRequest.app_country:type_name -> algoenum.v1.AppCountry
	95,  // 7: auth.v1.BackofficeAuthServiceCreateAppResponse.error:type_name -> errmsg.v1.ErrorMessage
	96,  // 8: auth.v1.BackofficeAuthServiceFetchAppRequest.app_type:type_name -> algoenum.v1.AppType
	97,  // 9: auth.v1.BackofficeAuthServiceFetchAppRequest.app_country:type_name -> algoenum.v1.AppCountry
	98,  // 10: auth.v1.BackofficeAuthServiceFetchAppRequest.state:type_name -> utils.v1.State
	99,  // 11: auth.v1.BackofficeAuthServiceFetchAppRequest.pagination:type_name -> utils.v1.PaginationRequest
	95,  // 12: auth.v1.BackofficeAuthServiceFetchAppResponse.error:type_name -> errmsg.v1.ErrorMessage
	100, // 13: auth.v1.BackofficeAuthServiceFetchAppResponse.pagination:type_name -> utils.v1.PaginationResponse
	52,  // 14: auth.v1.BackofficeAuthServiceFetchAppResponse.items:type_name -> auth.v1.BackofficeAppModel
	96,  // 15: auth.v1.BackofficeAuthServiceUpdateAppRequest.app_type:type_name -> algoenum.v1.AppType
	98,  // 16: auth.v1.BackofficeAuthServiceUpdateAppRequest.state:type_name -> utils.v1.State
	97,  // 17: auth.v1.BackofficeAuthServiceUpdateAppRequest.app_country:type_name -> algoenum.v1.AppCountry
	95,  // 18: auth.v1.BackofficeAuthServiceUpdateAppResponse.error:type_name -> errmsg.v1.ErrorMessage
	98,  // 19: auth.v1.BackofficeAuthServiceFetchUserRequest.state:type_name -> utils.v1.State
	99,  // 20: auth.v1.BackofficeAuthServiceFetchUserRequest.pagination:type_name -> utils.v1.PaginationRequest
	95,  // 21: auth.v1.BackofficeAuthServiceFetchUserResponse.error:type_name -> errmsg.v1.ErrorMessage
	100, // 22: auth.v1.BackofficeAuthServiceFetchUserResponse.pagination:type_name -> utils.v1.PaginationResponse
	17,  // 23: auth.v1.BackofficeAuthServiceFetchUserResponse.items:type_name -> auth.v1.BackofficeUserModel
	19,  // 24: auth.v1.BackofficeUserModel.app:type_name -> auth.v1.AuthUserApp
	18,  // 25: auth.v1.BackofficeUserModel.role:type_name -> auth.v1.BackofficeUserRoleModel
	4,   // 26: auth.v1.BackofficeUserModel.user_details:type_name -> auth.v1.BackofficeUserDetail
	95,  // 27: auth.v1.BackofficeAuthServiceCreateUserResponse.error:type_name -> errmsg.v1.ErrorMessage
	98,  // 28: auth.v1.BackofficeAuthServiceUpdateUserRequest.state:type_name -> utils.v1.State
	95,  // 29: auth.v1.BackofficeAuthServiceUpdateUserResponse.error:type_name -> errmsg.v1.ErrorMessage
	99,  // 30: auth.v1.BackofficeAuthServiceFetchRoleRequest.pagination:type_name -> utils.v1.PaginationRequest
	98,  // 31: auth.v1.BackofficeAuthServiceFetchRoleRequest.state:type_name -> utils.v1.State
	95,  // 32: auth.v1.BackofficeAuthServiceFetchRoleResponse.error:type_name -> errmsg.v1.ErrorMessage
	100, // 33: auth.v1.BackofficeAuthServiceFetchRoleResponse.pagination:type_name -> utils.v1.PaginationResponse
	26,  // 34: auth.v1.BackofficeAuthServiceFetchRoleResponse.items:type_name -> auth.v1.RoleModel
	95,  // 35: auth.v1.BackofficeAuthServiceCreateRoleResponse.error:type_name -> errmsg.v1.ErrorMessage
	98,  // 36: auth.v1.BackofficeAuthServiceUpdateRoleRequest.state:type_name -> utils.v1.State
	95,  // 37: auth.v1.BackofficeAuthServiceUpdateRoleResponse.error:type_name -> errmsg.v1.ErrorMessage
	98,  // 38: auth.v1.BackofficeAuthServiceFetchServiceRequest.state:type_name -> utils.v1.State
	99,  // 39: auth.v1.BackofficeAuthServiceFetchServiceRequest.pagination:type_name -> utils.v1.PaginationRequest
	95,  // 40: auth.v1.BackofficeAuthServiceFetchServiceResponse.error:type_name -> errmsg.v1.ErrorMessage
	100, // 41: auth.v1.BackofficeAuthServiceFetchServiceResponse.pagination:type_name -> utils.v1.PaginationResponse
	33,  // 42: auth.v1.BackofficeAuthServiceFetchServiceResponse.items:type_name -> auth.v1.ServiceModel
	95,  // 43: auth.v1.BackofficeAuthServiceCreateServiceResponse.error:type_name -> errmsg.v1.ErrorMessage
	98,  // 44: auth.v1.BackofficeAuthServiceUpdateServiceRequest.state:type_name -> utils.v1.State
	95,  // 45: auth.v1.BackofficeAuthServiceUpdateServiceResponse.error:type_name -> errmsg.v1.ErrorMessage
	98,  // 46: auth.v1.BackofficeAuthServiceFetchPathRequest.state:type_name -> utils.v1.State
	99,  // 47: auth.v1.BackofficeAuthServiceFetchPathRequest.pagination:type_name -> utils.v1.PaginationRequest
	95,  // 48: auth.v1.BackofficeAuthServiceFetchPathResponse.error:type_name -> errmsg.v1.ErrorMessage
	100, // 49: auth.v1.BackofficeAuthServiceFetchPathResponse.pagination:type_name -> utils.v1.PaginationResponse
	40,  // 50: auth.v1.BackofficeAuthServiceFetchPathResponse.items:type_name -> auth.v1.PathModel
	33,  // 51: auth.v1.PathModel.service:type_name -> auth.v1.ServiceModel
	95,  // 52: auth.v1.BackofficeAuthServiceCreatePathResponse.error:type_name -> errmsg.v1.ErrorMessage
	98,  // 53: auth.v1.BackofficeAuthServiceUpdatePathRequest.state:type_name -> utils.v1.State
	95,  // 54: auth.v1.BackofficeAuthServiceUpdatePathResponse.error:type_name -> errmsg.v1.ErrorMessage
	98,  // 55: auth.v1.BackofficeAuthServiceFetchPolicyRequest.state:type_name -> utils.v1.State
	99,  // 56: auth.v1.BackofficeAuthServiceFetchPolicyRequest.pagination:type_name -> utils.v1.PaginationRequest
	95,  // 57: auth.v1.BackofficeAuthServiceFetchPolicyResponse.error:type_name -> errmsg.v1.ErrorMessage
	100, // 58: auth.v1.BackofficeAuthServiceFetchPolicyResponse.pagination:type_name -> utils.v1.PaginationResponse
	47,  // 59: auth.v1.BackofficeAuthServiceFetchPolicyResponse.items:type_name -> auth.v1.PolicyModel
	33,  // 60: auth.v1.PolicyModel.service:type_name -> auth.v1.ServiceModel
	40,  // 61: auth.v1.PolicyModel.path:type_name -> auth.v1.PathModel
	26,  // 62: auth.v1.PolicyModel.role:type_name -> auth.v1.RoleModel
	95,  // 63: auth.v1.BackofficeAuthServiceCreatePolicyResponse.error:type_name -> errmsg.v1.ErrorMessage
	98,  // 64: auth.v1.BackofficeAuthServiceUpdatePolicyRequest.state:type_name -> utils.v1.State
	95,  // 65: auth.v1.BackofficeAuthServiceUpdatePolicyResponse.error:type_name -> errmsg.v1.ErrorMessage
	96,  // 66: auth.v1.BackofficeAppModel.app_type:type_name -> algoenum.v1.AppType
	97,  // 67: auth.v1.BackofficeAppModel.app_country:type_name -> algoenum.v1.AppCountry
	95,  // 68: auth.v1.BackofficeAuthServiceInitTotpResponse.error:type_name -> errmsg.v1.ErrorMessage
	95,  // 69: auth.v1.BackofficeAuthServiceVerifyTotpResponse.error:type_name -> errmsg.v1.ErrorMessage
	95,  // 70: auth.v1.BackofficeAuthServiceRemoveTotpResponse.error:type_name -> errmsg.v1.ErrorMessage
	98,  // 71: auth.v1.BackofficeAuthServiceFetchConfigMailRequest.state:type_name -> utils.v1.State
	99,  // 72: auth.v1.BackofficeAuthServiceFetchConfigMailRequest.pagination:type_name -> utils.v1.PaginationRequest
	95,  // 73: auth.v1.BackofficeAuthServiceFetchConfigMailResponse.error:type_name -> errmsg.v1.ErrorMessage
	65,  // 74: auth.v1.BackofficeAuthServiceFetchConfigMailResponse.items:type_name -> auth.v1.ConfigMailModel
	100, // 75: auth.v1.BackofficeAuthServiceFetchConfigMailResponse.pagination:type_name -> utils.v1.PaginationResponse
	95,  // 76: auth.v1.BackofficeAuthServiceCreateConfigMailResponse.error:type_name -> errmsg.v1.ErrorMessage
	98,  // 77: auth.v1.BackofficeAuthServiceUpdateConfigMailRequest.state:type_name -> utils.v1.State
	95,  // 78: auth.v1.BackofficeAuthServiceUpdateConfigMailResponse.error:type_name -> errmsg.v1.ErrorMessage
	101, // 79: auth.v1.BackofficeAuthServiceFetchConfigTemplateEmailRequest.name:type_name -> algoenum.v1.TemplateEmailType
	98,  // 80: auth.v1.BackofficeAuthServiceFetchConfigTemplateEmailRequest.state:type_name -> utils.v1.State
	99,  // 81: auth.v1.BackofficeAuthServiceFetchConfigTemplateEmailRequest.pagination:type_name -> utils.v1.PaginationRequest
	95,  // 82: auth.v1.BackofficeAuthServiceFetchConfigTemplateEmailResponse.error:type_name -> errmsg.v1.ErrorMessage
	72,  // 83: auth.v1.BackofficeAuthServiceFetchConfigTemplateEmailResponse.items:type_name -> auth.v1.ConfigTemplateEmailModel
	100, // 84: auth.v1.BackofficeAuthServiceFetchConfigTemplateEmailResponse.pagination:type_name -> utils.v1.PaginationResponse
	101, // 85: auth.v1.BackofficeAuthServiceCreateConfigTemplateEmailRequest.name:type_name -> algoenum.v1.TemplateEmailType
	95,  // 86: auth.v1.BackofficeAuthServiceCreateConfigTemplateEmailResponse.error:type_name -> errmsg.v1.ErrorMessage
	98,  // 87: auth.v1.BackofficeAuthServiceUpdateConfigTemplateEmailRequest.state:type_name -> utils.v1.State
	95,  // 88: auth.v1.BackofficeAuthServiceUpdateConfigTemplateEmailResponse.error:type_name -> errmsg.v1.ErrorMessage
	101, // 89: auth.v1.ConfigTemplateEmailModel.name:type_name -> algoenum.v1.TemplateEmailType
	95,  // 90: auth.v1.BackofficeAuthServiceRefreshTokenResponse.error:type_name -> errmsg.v1.ErrorMessage
	95,  // 91: auth.v1.BackofficeAuthServiceUpdateRefCodeResponse.error:type_name -> errmsg.v1.ErrorMessage
	95,  // 92: auth.v1.BackofficeAuthServiceForgotPasswordResponse.error:type_name -> errmsg.v1.ErrorMessage
	98,  // 93: auth.v1.BackofficeAuthServiceFetchOAuthConfigRequest.state:type_name -> utils.v1.State
	99,  // 94: auth.v1.BackofficeAuthServiceFetchOAuthConfigRequest.pagination:type_name -> utils.v1.PaginationRequest
	95,  // 95: auth.v1.BackofficeAuthServiceFetchOAuthConfigResponse.error:type_name -> errmsg.v1.ErrorMessage
	85,  // 96: auth.v1.BackofficeAuthServiceFetchOAuthConfigResponse.items:type_name -> auth.v1.OAuthConfigModel
	100, // 97: auth.v1.BackofficeAuthServiceFetchOAuthConfigResponse.pagination:type_name -> utils.v1.PaginationResponse
	95,  // 98: auth.v1.BackofficeAuthServiceCreateOAuthConfigResponse.error:type_name -> errmsg.v1.ErrorMessage
	98,  // 99: auth.v1.BackofficeAuthServiceUpdateOAuthConfigRequest.state:type_name -> utils.v1.State
	95,  // 100: auth.v1.BackofficeAuthServiceUpdateOAuthConfigResponse.error:type_name -> errmsg.v1.ErrorMessage
	102, // 101: auth.v1.BackofficeAuthServiceFetchCompanyRequest.verify:type_name -> utils.v1.Bool
	98,  // 102: auth.v1.BackofficeAuthServiceFetchCompanyRequest.state:type_name -> utils.v1.State
	99,  // 103: auth.v1.BackofficeAuthServiceFetchCompanyRequest.pagination:type_name -> utils.v1.PaginationRequest
	95,  // 104: auth.v1.BackofficeAuthServiceFetchCompanyResponse.error:type_name -> errmsg.v1.ErrorMessage
	92,  // 105: auth.v1.BackofficeAuthServiceFetchCompanyResponse.items:type_name -> auth.v1.CompanyModel
	100, // 106: auth.v1.BackofficeAuthServiceFetchCompanyResponse.pagination:type_name -> utils.v1.PaginationResponse
	95,  // 107: auth.v1.BackofficeAuthServiceCreateCompanyResponse.error:type_name -> errmsg.v1.ErrorMessage
	98,  // 108: auth.v1.BackofficeAuthServiceUpdateCompanyRequest.state:type_name -> utils.v1.State
	95,  // 109: auth.v1.BackofficeAuthServiceUpdateCompanyResponse.error:type_name -> errmsg.v1.ErrorMessage
	95,  // 110: auth.v1.BackofficeAuthServiceUpdateProfileResponse.error:type_name -> errmsg.v1.ErrorMessage
	7,   // 111: auth.v1.BackofficeAuthService.Login:input_type -> auth.v1.BackofficeAuthServiceLoginRequest
	2,   // 112: auth.v1.BackofficeAuthService.Me:input_type -> auth.v1.BackofficeAuthServiceMeRequest
	0,   // 113: auth.v1.BackofficeAuthService.ChangePassword:input_type -> auth.v1.BackofficeAuthServiceChangePasswordRequest
	73,  // 114: auth.v1.BackofficeAuthService.RefreshToken:input_type -> auth.v1.BackofficeAuthServiceRefreshTokenRequest
	77,  // 115: auth.v1.BackofficeAuthService.ForgotPassword:input_type -> auth.v1.BackofficeAuthServiceForgotPasswordRequest
	5,   // 116: auth.v1.BackofficeAuthService.ReloadEnforcer:input_type -> auth.v1.BackofficeAuthServiceReloadEnforcerRequest
	9,   // 117: auth.v1.BackofficeAuthService.CreateApp:input_type -> auth.v1.BackofficeAuthServiceCreateAppRequest
	11,  // 118: auth.v1.BackofficeAuthService.FetchApp:input_type -> auth.v1.BackofficeAuthServiceFetchAppRequest
	13,  // 119: auth.v1.BackofficeAuthService.UpdateApp:input_type -> auth.v1.BackofficeAuthServiceUpdateAppRequest
	15,  // 120: auth.v1.BackofficeAuthService.FetchUser:input_type -> auth.v1.BackofficeAuthServiceFetchUserRequest
	20,  // 121: auth.v1.BackofficeAuthService.CreateUser:input_type -> auth.v1.BackofficeAuthServiceCreateUserRequest
	22,  // 122: auth.v1.BackofficeAuthService.UpdateUser:input_type -> auth.v1.BackofficeAuthServiceUpdateUserRequest
	24,  // 123: auth.v1.BackofficeAuthService.FetchRole:input_type -> auth.v1.BackofficeAuthServiceFetchRoleRequest
	27,  // 124: auth.v1.BackofficeAuthService.CreateRole:input_type -> auth.v1.BackofficeAuthServiceCreateRoleRequest
	29,  // 125: auth.v1.BackofficeAuthService.UpdateRole:input_type -> auth.v1.BackofficeAuthServiceUpdateRoleRequest
	31,  // 126: auth.v1.BackofficeAuthService.FetchService:input_type -> auth.v1.BackofficeAuthServiceFetchServiceRequest
	34,  // 127: auth.v1.BackofficeAuthService.CreateService:input_type -> auth.v1.BackofficeAuthServiceCreateServiceRequest
	36,  // 128: auth.v1.BackofficeAuthService.UpdateService:input_type -> auth.v1.BackofficeAuthServiceUpdateServiceRequest
	38,  // 129: auth.v1.BackofficeAuthService.FetchPath:input_type -> auth.v1.BackofficeAuthServiceFetchPathRequest
	41,  // 130: auth.v1.BackofficeAuthService.CreatePath:input_type -> auth.v1.BackofficeAuthServiceCreatePathRequest
	43,  // 131: auth.v1.BackofficeAuthService.UpdatePath:input_type -> auth.v1.BackofficeAuthServiceUpdatePathRequest
	45,  // 132: auth.v1.BackofficeAuthService.FetchPolicy:input_type -> auth.v1.BackofficeAuthServiceFetchPolicyRequest
	48,  // 133: auth.v1.BackofficeAuthService.CreatePolicy:input_type -> auth.v1.BackofficeAuthServiceCreatePolicyRequest
	50,  // 134: auth.v1.BackofficeAuthService.UpdatePolicy:input_type -> auth.v1.BackofficeAuthServiceUpdatePolicyRequest
	53,  // 135: auth.v1.BackofficeAuthService.InitTotp:input_type -> auth.v1.BackofficeAuthServiceInitTotpRequest
	55,  // 136: auth.v1.BackofficeAuthService.VerifyTotp:input_type -> auth.v1.BackofficeAuthServiceVerifyTotpRequest
	57,  // 137: auth.v1.BackofficeAuthService.RemoveTotp:input_type -> auth.v1.BackofficeAuthServiceRemoveTotpRequest
	59,  // 138: auth.v1.BackofficeAuthService.FetchConfigMail:input_type -> auth.v1.BackofficeAuthServiceFetchConfigMailRequest
	61,  // 139: auth.v1.BackofficeAuthService.CreateConfigMail:input_type -> auth.v1.BackofficeAuthServiceCreateConfigMailRequest
	63,  // 140: auth.v1.BackofficeAuthService.UpdateConfigMail:input_type -> auth.v1.BackofficeAuthServiceUpdateConfigMailRequest
	66,  // 141: auth.v1.BackofficeAuthService.FetchConfigTemplateEmail:input_type -> auth.v1.BackofficeAuthServiceFetchConfigTemplateEmailRequest
	68,  // 142: auth.v1.BackofficeAuthService.CreateConfigTemplateEmail:input_type -> auth.v1.BackofficeAuthServiceCreateConfigTemplateEmailRequest
	70,  // 143: auth.v1.BackofficeAuthService.UpdateConfigTemplateEmail:input_type -> auth.v1.BackofficeAuthServiceUpdateConfigTemplateEmailRequest
	75,  // 144: auth.v1.BackofficeAuthService.UpdateRefCode:input_type -> auth.v1.BackofficeAuthServiceUpdateRefCodeRequest
	79,  // 145: auth.v1.BackofficeAuthService.FetchOAuthConfig:input_type -> auth.v1.BackofficeAuthServiceFetchOAuthConfigRequest
	81,  // 146: auth.v1.BackofficeAuthService.CreateOAuthConfig:input_type -> auth.v1.BackofficeAuthServiceCreateOAuthConfigRequest
	83,  // 147: auth.v1.BackofficeAuthService.UpdateOAuthConfig:input_type -> auth.v1.BackofficeAuthServiceUpdateOAuthConfigRequest
	86,  // 148: auth.v1.BackofficeAuthService.FetchCompany:input_type -> auth.v1.BackofficeAuthServiceFetchCompanyRequest
	88,  // 149: auth.v1.BackofficeAuthService.CreateCompany:input_type -> auth.v1.BackofficeAuthServiceCreateCompanyRequest
	90,  // 150: auth.v1.BackofficeAuthService.UpdateCompany:input_type -> auth.v1.BackofficeAuthServiceUpdateCompanyRequest
	93,  // 151: auth.v1.BackofficeAuthService.UpdateProfile:input_type -> auth.v1.BackofficeAuthServiceUpdateProfileRequest
	8,   // 152: auth.v1.BackofficeAuthService.Login:output_type -> auth.v1.BackofficeAuthServiceLoginResponse
	3,   // 153: auth.v1.BackofficeAuthService.Me:output_type -> auth.v1.BackofficeAuthServiceMeResponse
	1,   // 154: auth.v1.BackofficeAuthService.ChangePassword:output_type -> auth.v1.BackofficeAuthServiceChangePasswordResponse
	74,  // 155: auth.v1.BackofficeAuthService.RefreshToken:output_type -> auth.v1.BackofficeAuthServiceRefreshTokenResponse
	78,  // 156: auth.v1.BackofficeAuthService.ForgotPassword:output_type -> auth.v1.BackofficeAuthServiceForgotPasswordResponse
	6,   // 157: auth.v1.BackofficeAuthService.ReloadEnforcer:output_type -> auth.v1.BackofficeAuthServiceReloadEnforcerResponse
	10,  // 158: auth.v1.BackofficeAuthService.CreateApp:output_type -> auth.v1.BackofficeAuthServiceCreateAppResponse
	12,  // 159: auth.v1.BackofficeAuthService.FetchApp:output_type -> auth.v1.BackofficeAuthServiceFetchAppResponse
	14,  // 160: auth.v1.BackofficeAuthService.UpdateApp:output_type -> auth.v1.BackofficeAuthServiceUpdateAppResponse
	16,  // 161: auth.v1.BackofficeAuthService.FetchUser:output_type -> auth.v1.BackofficeAuthServiceFetchUserResponse
	21,  // 162: auth.v1.BackofficeAuthService.CreateUser:output_type -> auth.v1.BackofficeAuthServiceCreateUserResponse
	23,  // 163: auth.v1.BackofficeAuthService.UpdateUser:output_type -> auth.v1.BackofficeAuthServiceUpdateUserResponse
	25,  // 164: auth.v1.BackofficeAuthService.FetchRole:output_type -> auth.v1.BackofficeAuthServiceFetchRoleResponse
	28,  // 165: auth.v1.BackofficeAuthService.CreateRole:output_type -> auth.v1.BackofficeAuthServiceCreateRoleResponse
	30,  // 166: auth.v1.BackofficeAuthService.UpdateRole:output_type -> auth.v1.BackofficeAuthServiceUpdateRoleResponse
	32,  // 167: auth.v1.BackofficeAuthService.FetchService:output_type -> auth.v1.BackofficeAuthServiceFetchServiceResponse
	35,  // 168: auth.v1.BackofficeAuthService.CreateService:output_type -> auth.v1.BackofficeAuthServiceCreateServiceResponse
	37,  // 169: auth.v1.BackofficeAuthService.UpdateService:output_type -> auth.v1.BackofficeAuthServiceUpdateServiceResponse
	39,  // 170: auth.v1.BackofficeAuthService.FetchPath:output_type -> auth.v1.BackofficeAuthServiceFetchPathResponse
	42,  // 171: auth.v1.BackofficeAuthService.CreatePath:output_type -> auth.v1.BackofficeAuthServiceCreatePathResponse
	44,  // 172: auth.v1.BackofficeAuthService.UpdatePath:output_type -> auth.v1.BackofficeAuthServiceUpdatePathResponse
	46,  // 173: auth.v1.BackofficeAuthService.FetchPolicy:output_type -> auth.v1.BackofficeAuthServiceFetchPolicyResponse
	49,  // 174: auth.v1.BackofficeAuthService.CreatePolicy:output_type -> auth.v1.BackofficeAuthServiceCreatePolicyResponse
	51,  // 175: auth.v1.BackofficeAuthService.UpdatePolicy:output_type -> auth.v1.BackofficeAuthServiceUpdatePolicyResponse
	54,  // 176: auth.v1.BackofficeAuthService.InitTotp:output_type -> auth.v1.BackofficeAuthServiceInitTotpResponse
	56,  // 177: auth.v1.BackofficeAuthService.VerifyTotp:output_type -> auth.v1.BackofficeAuthServiceVerifyTotpResponse
	58,  // 178: auth.v1.BackofficeAuthService.RemoveTotp:output_type -> auth.v1.BackofficeAuthServiceRemoveTotpResponse
	60,  // 179: auth.v1.BackofficeAuthService.FetchConfigMail:output_type -> auth.v1.BackofficeAuthServiceFetchConfigMailResponse
	62,  // 180: auth.v1.BackofficeAuthService.CreateConfigMail:output_type -> auth.v1.BackofficeAuthServiceCreateConfigMailResponse
	64,  // 181: auth.v1.BackofficeAuthService.UpdateConfigMail:output_type -> auth.v1.BackofficeAuthServiceUpdateConfigMailResponse
	67,  // 182: auth.v1.BackofficeAuthService.FetchConfigTemplateEmail:output_type -> auth.v1.BackofficeAuthServiceFetchConfigTemplateEmailResponse
	69,  // 183: auth.v1.BackofficeAuthService.CreateConfigTemplateEmail:output_type -> auth.v1.BackofficeAuthServiceCreateConfigTemplateEmailResponse
	71,  // 184: auth.v1.BackofficeAuthService.UpdateConfigTemplateEmail:output_type -> auth.v1.BackofficeAuthServiceUpdateConfigTemplateEmailResponse
	76,  // 185: auth.v1.BackofficeAuthService.UpdateRefCode:output_type -> auth.v1.BackofficeAuthServiceUpdateRefCodeResponse
	80,  // 186: auth.v1.BackofficeAuthService.FetchOAuthConfig:output_type -> auth.v1.BackofficeAuthServiceFetchOAuthConfigResponse
	82,  // 187: auth.v1.BackofficeAuthService.CreateOAuthConfig:output_type -> auth.v1.BackofficeAuthServiceCreateOAuthConfigResponse
	84,  // 188: auth.v1.BackofficeAuthService.UpdateOAuthConfig:output_type -> auth.v1.BackofficeAuthServiceUpdateOAuthConfigResponse
	87,  // 189: auth.v1.BackofficeAuthService.FetchCompany:output_type -> auth.v1.BackofficeAuthServiceFetchCompanyResponse
	89,  // 190: auth.v1.BackofficeAuthService.CreateCompany:output_type -> auth.v1.BackofficeAuthServiceCreateCompanyResponse
	91,  // 191: auth.v1.BackofficeAuthService.UpdateCompany:output_type -> auth.v1.BackofficeAuthServiceUpdateCompanyResponse
	94,  // 192: auth.v1.BackofficeAuthService.UpdateProfile:output_type -> auth.v1.BackofficeAuthServiceUpdateProfileResponse
	152, // [152:193] is the sub-list for method output_type
	111, // [111:152] is the sub-list for method input_type
	111, // [111:111] is the sub-list for extension type_name
	111, // [111:111] is the sub-list for extension extendee
	0,   // [0:111] is the sub-list for field type_name
}

func init() { file_auth_v1_backoffice_auth_proto_init() }
func file_auth_v1_backoffice_auth_proto_init() {
	if File_auth_v1_backoffice_auth_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_auth_v1_backoffice_auth_proto_rawDesc), len(file_auth_v1_backoffice_auth_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   95,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_auth_v1_backoffice_auth_proto_goTypes,
		DependencyIndexes: file_auth_v1_backoffice_auth_proto_depIdxs,
		MessageInfos:      file_auth_v1_backoffice_auth_proto_msgTypes,
	}.Build()
	File_auth_v1_backoffice_auth_proto = out.File
	file_auth_v1_backoffice_auth_proto_goTypes = nil
	file_auth_v1_backoffice_auth_proto_depIdxs = nil
}
