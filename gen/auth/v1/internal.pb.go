// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: auth/v1/internal.proto

package authv1

import (
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InternalAuthServiceFetchUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ListIdUser    []string               `protobuf:"bytes,1,rep,name=list_id_user,json=listIdUser,proto3" json:"list_id_user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceFetchUserRequest) Reset() {
	*x = InternalAuthServiceFetchUserRequest{}
	mi := &file_auth_v1_internal_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceFetchUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceFetchUserRequest) ProtoMessage() {}

func (x *InternalAuthServiceFetchUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceFetchUserRequest.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceFetchUserRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{0}
}

func (x *InternalAuthServiceFetchUserRequest) GetListIdUser() []string {
	if x != nil {
		return x.ListIdUser
	}
	return nil
}

type InternalAuthServiceFetchUserResponse struct {
	state         protoimpl.MessageState                 `protogen:"open.v1"`
	Users         *InternalAuthServiceFetchUserUserModel `protobuf:"bytes,1,opt,name=users,proto3" json:"users,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceFetchUserResponse) Reset() {
	*x = InternalAuthServiceFetchUserResponse{}
	mi := &file_auth_v1_internal_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceFetchUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceFetchUserResponse) ProtoMessage() {}

func (x *InternalAuthServiceFetchUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceFetchUserResponse.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceFetchUserResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{1}
}

func (x *InternalAuthServiceFetchUserResponse) GetUsers() *InternalAuthServiceFetchUserUserModel {
	if x != nil {
		return x.Users
	}
	return nil
}

type InternalAuthServiceFetchUserUserModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdUser        string                 `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	Email         string                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceFetchUserUserModel) Reset() {
	*x = InternalAuthServiceFetchUserUserModel{}
	mi := &file_auth_v1_internal_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceFetchUserUserModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceFetchUserUserModel) ProtoMessage() {}

func (x *InternalAuthServiceFetchUserUserModel) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceFetchUserUserModel.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceFetchUserUserModel) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{2}
}

func (x *InternalAuthServiceFetchUserUserModel) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *InternalAuthServiceFetchUserUserModel) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type InternalAuthServiceCustomerLoginRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceCustomerLoginRequest) Reset() {
	*x = InternalAuthServiceCustomerLoginRequest{}
	mi := &file_auth_v1_internal_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceCustomerLoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceCustomerLoginRequest) ProtoMessage() {}

func (x *InternalAuthServiceCustomerLoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceCustomerLoginRequest.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceCustomerLoginRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{3}
}

func (x *InternalAuthServiceCustomerLoginRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *InternalAuthServiceCustomerLoginRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type InternalAuthServiceCustomerLoginResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceCustomerLoginResponse) Reset() {
	*x = InternalAuthServiceCustomerLoginResponse{}
	mi := &file_auth_v1_internal_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceCustomerLoginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceCustomerLoginResponse) ProtoMessage() {}

func (x *InternalAuthServiceCustomerLoginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceCustomerLoginResponse.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceCustomerLoginResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{4}
}

func (x *InternalAuthServiceCustomerLoginResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *InternalAuthServiceCustomerLoginResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type InternalAuthServiceFetchUserInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ListIdUser    []string               `protobuf:"bytes,1,rep,name=list_id_user,json=listIdUser,proto3" json:"list_id_user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceFetchUserInfoRequest) Reset() {
	*x = InternalAuthServiceFetchUserInfoRequest{}
	mi := &file_auth_v1_internal_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceFetchUserInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceFetchUserInfoRequest) ProtoMessage() {}

func (x *InternalAuthServiceFetchUserInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceFetchUserInfoRequest.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceFetchUserInfoRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{5}
}

func (x *InternalAuthServiceFetchUserInfoRequest) GetListIdUser() []string {
	if x != nil {
		return x.ListIdUser
	}
	return nil
}

type InternalAuthServiceFetchUserInfoResponse struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Users         []*InternalUserInfoModel `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	Error         *v1.ErrorMessage         `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceFetchUserInfoResponse) Reset() {
	*x = InternalAuthServiceFetchUserInfoResponse{}
	mi := &file_auth_v1_internal_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceFetchUserInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceFetchUserInfoResponse) ProtoMessage() {}

func (x *InternalAuthServiceFetchUserInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceFetchUserInfoResponse.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceFetchUserInfoResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{6}
}

func (x *InternalAuthServiceFetchUserInfoResponse) GetUsers() []*InternalUserInfoModel {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *InternalAuthServiceFetchUserInfoResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type InternalUserInfoModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdUser        string                 `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	Email         string                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	FirstName     string                 `protobuf:"bytes,3,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	LastName      string                 `protobuf:"bytes,4,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	IsActive      bool                   `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalUserInfoModel) Reset() {
	*x = InternalUserInfoModel{}
	mi := &file_auth_v1_internal_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalUserInfoModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalUserInfoModel) ProtoMessage() {}

func (x *InternalUserInfoModel) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalUserInfoModel.ProtoReflect.Descriptor instead.
func (*InternalUserInfoModel) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{7}
}

func (x *InternalUserInfoModel) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *InternalUserInfoModel) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *InternalUserInfoModel) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *InternalUserInfoModel) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *InternalUserInfoModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type InternalAuthServiceCreateAppMerchantRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdApp         string                 `protobuf:"bytes,1,opt,name=id_app,json=idApp,proto3" json:"id_app,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Domain        string                 `protobuf:"bytes,3,opt,name=domain,proto3" json:"domain,omitempty"`
	AppCountry    v11.AppCountry         `protobuf:"varint,4,opt,name=app_country,json=appCountry,proto3,enum=algoenum.v1.AppCountry" json:"app_country,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceCreateAppMerchantRequest) Reset() {
	*x = InternalAuthServiceCreateAppMerchantRequest{}
	mi := &file_auth_v1_internal_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceCreateAppMerchantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceCreateAppMerchantRequest) ProtoMessage() {}

func (x *InternalAuthServiceCreateAppMerchantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceCreateAppMerchantRequest.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceCreateAppMerchantRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{8}
}

func (x *InternalAuthServiceCreateAppMerchantRequest) GetIdApp() string {
	if x != nil {
		return x.IdApp
	}
	return ""
}

func (x *InternalAuthServiceCreateAppMerchantRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InternalAuthServiceCreateAppMerchantRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *InternalAuthServiceCreateAppMerchantRequest) GetAppCountry() v11.AppCountry {
	if x != nil {
		return x.AppCountry
	}
	return v11.AppCountry(0)
}

type InternalAuthServiceCreateAppMerchantResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceCreateAppMerchantResponse) Reset() {
	*x = InternalAuthServiceCreateAppMerchantResponse{}
	mi := &file_auth_v1_internal_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceCreateAppMerchantResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceCreateAppMerchantResponse) ProtoMessage() {}

func (x *InternalAuthServiceCreateAppMerchantResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceCreateAppMerchantResponse.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceCreateAppMerchantResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{9}
}

func (x *InternalAuthServiceCreateAppMerchantResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type AuthInternalServiceCreateUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdApp         string                 `protobuf:"bytes,1,opt,name=id_app,json=idApp,proto3" json:"id_app,omitempty"`
	NameSearch    string                 `protobuf:"bytes,2,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	AppType       v11.AppType            `protobuf:"varint,3,opt,name=app_type,json=appType,proto3,enum=algoenum.v1.AppType" json:"app_type,omitempty"`
	State         *v12.State             `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v12.PaginationRequest `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AuthInternalServiceCreateUserRequest) Reset() {
	*x = AuthInternalServiceCreateUserRequest{}
	mi := &file_auth_v1_internal_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuthInternalServiceCreateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthInternalServiceCreateUserRequest) ProtoMessage() {}

func (x *AuthInternalServiceCreateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthInternalServiceCreateUserRequest.ProtoReflect.Descriptor instead.
func (*AuthInternalServiceCreateUserRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{10}
}

func (x *AuthInternalServiceCreateUserRequest) GetIdApp() string {
	if x != nil {
		return x.IdApp
	}
	return ""
}

func (x *AuthInternalServiceCreateUserRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *AuthInternalServiceCreateUserRequest) GetAppType() v11.AppType {
	if x != nil {
		return x.AppType
	}
	return v11.AppType(0)
}

func (x *AuthInternalServiceCreateUserRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *AuthInternalServiceCreateUserRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type AuthInternalServiceCreateUserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdUser        string                 `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AuthInternalServiceCreateUserResponse) Reset() {
	*x = AuthInternalServiceCreateUserResponse{}
	mi := &file_auth_v1_internal_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuthInternalServiceCreateUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthInternalServiceCreateUserResponse) ProtoMessage() {}

func (x *AuthInternalServiceCreateUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthInternalServiceCreateUserResponse.ProtoReflect.Descriptor instead.
func (*AuthInternalServiceCreateUserResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{11}
}

func (x *AuthInternalServiceCreateUserResponse) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *AuthInternalServiceCreateUserResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type InternalAuthServiceFetchAppRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ListIdApp     []string               `protobuf:"bytes,1,rep,name=list_id_app,json=listIdApp,proto3" json:"list_id_app,omitempty"`
	NameSearch    string                 `protobuf:"bytes,2,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	DomainSearch  string                 `protobuf:"bytes,3,opt,name=domain_search,json=domainSearch,proto3" json:"domain_search,omitempty"`
	AppType       v11.AppType            `protobuf:"varint,4,opt,name=app_type,json=appType,proto3,enum=algoenum.v1.AppType" json:"app_type,omitempty"`
	AppCountry    v11.AppCountry         `protobuf:"varint,5,opt,name=app_country,json=appCountry,proto3,enum=algoenum.v1.AppCountry" json:"app_country,omitempty"`
	State         *v12.State             `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v12.PaginationRequest `protobuf:"bytes,7,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceFetchAppRequest) Reset() {
	*x = InternalAuthServiceFetchAppRequest{}
	mi := &file_auth_v1_internal_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceFetchAppRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceFetchAppRequest) ProtoMessage() {}

func (x *InternalAuthServiceFetchAppRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceFetchAppRequest.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceFetchAppRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{12}
}

func (x *InternalAuthServiceFetchAppRequest) GetListIdApp() []string {
	if x != nil {
		return x.ListIdApp
	}
	return nil
}

func (x *InternalAuthServiceFetchAppRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *InternalAuthServiceFetchAppRequest) GetDomainSearch() string {
	if x != nil {
		return x.DomainSearch
	}
	return ""
}

func (x *InternalAuthServiceFetchAppRequest) GetAppType() v11.AppType {
	if x != nil {
		return x.AppType
	}
	return v11.AppType(0)
}

func (x *InternalAuthServiceFetchAppRequest) GetAppCountry() v11.AppCountry {
	if x != nil {
		return x.AppCountry
	}
	return v11.AppCountry(0)
}

func (x *InternalAuthServiceFetchAppRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *InternalAuthServiceFetchAppRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type InternalAuthServiceFetchAppResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v12.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Items         []*InternalAppModel     `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceFetchAppResponse) Reset() {
	*x = InternalAuthServiceFetchAppResponse{}
	mi := &file_auth_v1_internal_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceFetchAppResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceFetchAppResponse) ProtoMessage() {}

func (x *InternalAuthServiceFetchAppResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceFetchAppResponse.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceFetchAppResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{13}
}

func (x *InternalAuthServiceFetchAppResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *InternalAuthServiceFetchAppResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *InternalAuthServiceFetchAppResponse) GetItems() []*InternalAppModel {
	if x != nil {
		return x.Items
	}
	return nil
}

type InternalAuthServiceMerchantLoginRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	IpAddress     string                 `protobuf:"bytes,3,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	UserAgent     string                 `protobuf:"bytes,4,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
	Otp           string                 `protobuf:"bytes,5,opt,name=otp,proto3" json:"otp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceMerchantLoginRequest) Reset() {
	*x = InternalAuthServiceMerchantLoginRequest{}
	mi := &file_auth_v1_internal_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceMerchantLoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceMerchantLoginRequest) ProtoMessage() {}

func (x *InternalAuthServiceMerchantLoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceMerchantLoginRequest.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceMerchantLoginRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{14}
}

func (x *InternalAuthServiceMerchantLoginRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *InternalAuthServiceMerchantLoginRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *InternalAuthServiceMerchantLoginRequest) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *InternalAuthServiceMerchantLoginRequest) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *InternalAuthServiceMerchantLoginRequest) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

type InternalAuthServiceMerchantLoginResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *InternalUserModel     `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceMerchantLoginResponse) Reset() {
	*x = InternalAuthServiceMerchantLoginResponse{}
	mi := &file_auth_v1_internal_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceMerchantLoginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceMerchantLoginResponse) ProtoMessage() {}

func (x *InternalAuthServiceMerchantLoginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceMerchantLoginResponse.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceMerchantLoginResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{15}
}

func (x *InternalAuthServiceMerchantLoginResponse) GetUser() *InternalUserModel {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *InternalAuthServiceMerchantLoginResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *InternalAuthServiceMerchantLoginResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type InternalAuthServiceLoginRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Domain        string                 `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	Email         string                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	Password      string                 `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	IpAddress     string                 `protobuf:"bytes,4,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	UserAgent     string                 `protobuf:"bytes,5,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
	Otp           string                 `protobuf:"bytes,6,opt,name=otp,proto3" json:"otp,omitempty"`
	DeviceId      string                 `protobuf:"bytes,7,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceLoginRequest) Reset() {
	*x = InternalAuthServiceLoginRequest{}
	mi := &file_auth_v1_internal_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceLoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceLoginRequest) ProtoMessage() {}

func (x *InternalAuthServiceLoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceLoginRequest.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceLoginRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{16}
}

func (x *InternalAuthServiceLoginRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *InternalAuthServiceLoginRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *InternalAuthServiceLoginRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *InternalAuthServiceLoginRequest) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *InternalAuthServiceLoginRequest) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *InternalAuthServiceLoginRequest) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *InternalAuthServiceLoginRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type InternalAuthServiceLoginResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *InternalUserModel     `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,3,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,4,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceLoginResponse) Reset() {
	*x = InternalAuthServiceLoginResponse{}
	mi := &file_auth_v1_internal_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceLoginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceLoginResponse) ProtoMessage() {}

func (x *InternalAuthServiceLoginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceLoginResponse.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceLoginResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{17}
}

func (x *InternalAuthServiceLoginResponse) GetUser() *InternalUserModel {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *InternalAuthServiceLoginResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *InternalAuthServiceLoginResponse) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *InternalAuthServiceLoginResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type InternalAuthServiceLogoutRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Domain        string                 `protobuf:"bytes,1,opt,name=domain,proto3" json:"domain,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceLogoutRequest) Reset() {
	*x = InternalAuthServiceLogoutRequest{}
	mi := &file_auth_v1_internal_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceLogoutRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceLogoutRequest) ProtoMessage() {}

func (x *InternalAuthServiceLogoutRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceLogoutRequest.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceLogoutRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{18}
}

func (x *InternalAuthServiceLogoutRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *InternalAuthServiceLogoutRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type InternalAuthServiceLogoutResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceLogoutResponse) Reset() {
	*x = InternalAuthServiceLogoutResponse{}
	mi := &file_auth_v1_internal_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceLogoutResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceLogoutResponse) ProtoMessage() {}

func (x *InternalAuthServiceLogoutResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceLogoutResponse.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceLogoutResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{19}
}

func (x *InternalAuthServiceLogoutResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *InternalAuthServiceLogoutResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type InternalAuthServiceRegisterRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdApp         string                 `protobuf:"bytes,1,opt,name=id_app,json=idApp,proto3" json:"id_app,omitempty"`
	IdUser        string                 `protobuf:"bytes,2,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	IdRole        string                 `protobuf:"bytes,3,opt,name=id_role,json=idRole,proto3" json:"id_role,omitempty"`
	Email         string                 `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	Password      string                 `protobuf:"bytes,5,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceRegisterRequest) Reset() {
	*x = InternalAuthServiceRegisterRequest{}
	mi := &file_auth_v1_internal_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceRegisterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceRegisterRequest) ProtoMessage() {}

func (x *InternalAuthServiceRegisterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceRegisterRequest.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceRegisterRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{20}
}

func (x *InternalAuthServiceRegisterRequest) GetIdApp() string {
	if x != nil {
		return x.IdApp
	}
	return ""
}

func (x *InternalAuthServiceRegisterRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *InternalAuthServiceRegisterRequest) GetIdRole() string {
	if x != nil {
		return x.IdRole
	}
	return ""
}

func (x *InternalAuthServiceRegisterRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *InternalAuthServiceRegisterRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type InternalAuthServiceRegisterResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceRegisterResponse) Reset() {
	*x = InternalAuthServiceRegisterResponse{}
	mi := &file_auth_v1_internal_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceRegisterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceRegisterResponse) ProtoMessage() {}

func (x *InternalAuthServiceRegisterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceRegisterResponse.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceRegisterResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{21}
}

func (x *InternalAuthServiceRegisterResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type InternalAuthServiceChangePasswordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *InternalUserModel     `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	OldPassword   string                 `protobuf:"bytes,2,opt,name=old_password,json=oldPassword,proto3" json:"old_password,omitempty"`
	NewPassword   string                 `protobuf:"bytes,3,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceChangePasswordRequest) Reset() {
	*x = InternalAuthServiceChangePasswordRequest{}
	mi := &file_auth_v1_internal_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceChangePasswordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceChangePasswordRequest) ProtoMessage() {}

func (x *InternalAuthServiceChangePasswordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceChangePasswordRequest.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceChangePasswordRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{22}
}

func (x *InternalAuthServiceChangePasswordRequest) GetUser() *InternalUserModel {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *InternalAuthServiceChangePasswordRequest) GetOldPassword() string {
	if x != nil {
		return x.OldPassword
	}
	return ""
}

func (x *InternalAuthServiceChangePasswordRequest) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

type InternalAuthServiceChangePasswordResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceChangePasswordResponse) Reset() {
	*x = InternalAuthServiceChangePasswordResponse{}
	mi := &file_auth_v1_internal_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceChangePasswordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceChangePasswordResponse) ProtoMessage() {}

func (x *InternalAuthServiceChangePasswordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceChangePasswordResponse.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceChangePasswordResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{23}
}

func (x *InternalAuthServiceChangePasswordResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type InternalAuthServiceVerifyRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// string domain = 1;
	Token         string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	AbsolutePath  string `protobuf:"bytes,2,opt,name=absolute_path,json=absolutePath,proto3" json:"absolute_path,omitempty"`
	UserIp        string `protobuf:"bytes,3,opt,name=user_ip,json=userIp,proto3" json:"user_ip,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceVerifyRequest) Reset() {
	*x = InternalAuthServiceVerifyRequest{}
	mi := &file_auth_v1_internal_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceVerifyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceVerifyRequest) ProtoMessage() {}

func (x *InternalAuthServiceVerifyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceVerifyRequest.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceVerifyRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{24}
}

func (x *InternalAuthServiceVerifyRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *InternalAuthServiceVerifyRequest) GetAbsolutePath() string {
	if x != nil {
		return x.AbsolutePath
	}
	return ""
}

func (x *InternalAuthServiceVerifyRequest) GetUserIp() string {
	if x != nil {
		return x.UserIp
	}
	return ""
}

type InternalAuthServiceVerifyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *InternalUserModel     `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceVerifyResponse) Reset() {
	*x = InternalAuthServiceVerifyResponse{}
	mi := &file_auth_v1_internal_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceVerifyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceVerifyResponse) ProtoMessage() {}

func (x *InternalAuthServiceVerifyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceVerifyResponse.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceVerifyResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{25}
}

func (x *InternalAuthServiceVerifyResponse) GetUser() *InternalUserModel {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *InternalAuthServiceVerifyResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type InternalUserModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdApp         string                 `protobuf:"bytes,1,opt,name=id_app,json=idApp,proto3" json:"id_app,omitempty"`
	IdUser        string                 `protobuf:"bytes,2,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	IdRole        string                 `protobuf:"bytes,3,opt,name=id_role,json=idRole,proto3" json:"id_role,omitempty"`
	UserIp        string                 `protobuf:"bytes,4,opt,name=user_ip,json=userIp,proto3" json:"user_ip,omitempty"`
	Lat           float64                `protobuf:"fixed64,5,opt,name=lat,proto3" json:"lat,omitempty"`
	Lng           float64                `protobuf:"fixed64,6,opt,name=lng,proto3" json:"lng,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalUserModel) Reset() {
	*x = InternalUserModel{}
	mi := &file_auth_v1_internal_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalUserModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalUserModel) ProtoMessage() {}

func (x *InternalUserModel) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalUserModel.ProtoReflect.Descriptor instead.
func (*InternalUserModel) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{26}
}

func (x *InternalUserModel) GetIdApp() string {
	if x != nil {
		return x.IdApp
	}
	return ""
}

func (x *InternalUserModel) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *InternalUserModel) GetIdRole() string {
	if x != nil {
		return x.IdRole
	}
	return ""
}

func (x *InternalUserModel) GetUserIp() string {
	if x != nil {
		return x.UserIp
	}
	return ""
}

func (x *InternalUserModel) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *InternalUserModel) GetLng() float64 {
	if x != nil {
		return x.Lng
	}
	return 0
}

type InternalAppModel struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdApp          string                 `protobuf:"bytes,1,opt,name=id_app,json=idApp,proto3" json:"id_app,omitempty"`
	Name           string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Domain         string                 `protobuf:"bytes,3,opt,name=domain,proto3" json:"domain,omitempty"`
	AppType        v11.AppType            `protobuf:"varint,4,opt,name=app_type,json=appType,proto3,enum=algoenum.v1.AppType" json:"app_type,omitempty"`
	ShortPublicKey string                 `protobuf:"bytes,5,opt,name=short_public_key,json=shortPublicKey,proto3" json:"short_public_key,omitempty"`
	TokenTtl       uint64                 `protobuf:"varint,6,opt,name=token_ttl,json=tokenTtl,proto3" json:"token_ttl,omitempty"`
	IsActive       bool                   `protobuf:"varint,7,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	AppCountry     v11.AppCountry         `protobuf:"varint,8,opt,name=app_country,json=appCountry,proto3,enum=algoenum.v1.AppCountry" json:"app_country,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *InternalAppModel) Reset() {
	*x = InternalAppModel{}
	mi := &file_auth_v1_internal_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAppModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAppModel) ProtoMessage() {}

func (x *InternalAppModel) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAppModel.ProtoReflect.Descriptor instead.
func (*InternalAppModel) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{27}
}

func (x *InternalAppModel) GetIdApp() string {
	if x != nil {
		return x.IdApp
	}
	return ""
}

func (x *InternalAppModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InternalAppModel) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *InternalAppModel) GetAppType() v11.AppType {
	if x != nil {
		return x.AppType
	}
	return v11.AppType(0)
}

func (x *InternalAppModel) GetShortPublicKey() string {
	if x != nil {
		return x.ShortPublicKey
	}
	return ""
}

func (x *InternalAppModel) GetTokenTtl() uint64 {
	if x != nil {
		return x.TokenTtl
	}
	return 0
}

func (x *InternalAppModel) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *InternalAppModel) GetAppCountry() v11.AppCountry {
	if x != nil {
		return x.AppCountry
	}
	return v11.AppCountry(0)
}

// -------------------------------------
type InternalAuthServiceSendMailHtmlRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	To            []string               `protobuf:"bytes,1,rep,name=to,proto3" json:"to,omitempty"`
	Cc            []string               `protobuf:"bytes,2,rep,name=cc,proto3" json:"cc,omitempty"`
	Subject       string                 `protobuf:"bytes,3,opt,name=subject,proto3" json:"subject,omitempty"`
	Body          string                 `protobuf:"bytes,4,opt,name=body,proto3" json:"body,omitempty"`
	IdApp         string                 `protobuf:"bytes,5,opt,name=id_app,json=idApp,proto3" json:"id_app,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceSendMailHtmlRequest) Reset() {
	*x = InternalAuthServiceSendMailHtmlRequest{}
	mi := &file_auth_v1_internal_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceSendMailHtmlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceSendMailHtmlRequest) ProtoMessage() {}

func (x *InternalAuthServiceSendMailHtmlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceSendMailHtmlRequest.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceSendMailHtmlRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{28}
}

func (x *InternalAuthServiceSendMailHtmlRequest) GetTo() []string {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *InternalAuthServiceSendMailHtmlRequest) GetCc() []string {
	if x != nil {
		return x.Cc
	}
	return nil
}

func (x *InternalAuthServiceSendMailHtmlRequest) GetSubject() string {
	if x != nil {
		return x.Subject
	}
	return ""
}

func (x *InternalAuthServiceSendMailHtmlRequest) GetBody() string {
	if x != nil {
		return x.Body
	}
	return ""
}

func (x *InternalAuthServiceSendMailHtmlRequest) GetIdApp() string {
	if x != nil {
		return x.IdApp
	}
	return ""
}

type InternalAuthServiceSendMailHtmlResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceSendMailHtmlResponse) Reset() {
	*x = InternalAuthServiceSendMailHtmlResponse{}
	mi := &file_auth_v1_internal_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceSendMailHtmlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceSendMailHtmlResponse) ProtoMessage() {}

func (x *InternalAuthServiceSendMailHtmlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceSendMailHtmlResponse.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceSendMailHtmlResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{29}
}

func (x *InternalAuthServiceSendMailHtmlResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

// -------------------------------------
type InternalAuthServiceRefreshTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdApp         string                 `protobuf:"bytes,1,opt,name=id_app,json=idApp,proto3" json:"id_app,omitempty"`
	DeviceId      string                 `protobuf:"bytes,2,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,3,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceRefreshTokenRequest) Reset() {
	*x = InternalAuthServiceRefreshTokenRequest{}
	mi := &file_auth_v1_internal_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceRefreshTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceRefreshTokenRequest) ProtoMessage() {}

func (x *InternalAuthServiceRefreshTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceRefreshTokenRequest.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceRefreshTokenRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{30}
}

func (x *InternalAuthServiceRefreshTokenRequest) GetIdApp() string {
	if x != nil {
		return x.IdApp
	}
	return ""
}

func (x *InternalAuthServiceRefreshTokenRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *InternalAuthServiceRefreshTokenRequest) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

type InternalAuthServiceRefreshTokenResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,4,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceRefreshTokenResponse) Reset() {
	*x = InternalAuthServiceRefreshTokenResponse{}
	mi := &file_auth_v1_internal_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceRefreshTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceRefreshTokenResponse) ProtoMessage() {}

func (x *InternalAuthServiceRefreshTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceRefreshTokenResponse.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceRefreshTokenResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{31}
}

func (x *InternalAuthServiceRefreshTokenResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *InternalAuthServiceRefreshTokenResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *InternalAuthServiceRefreshTokenResponse) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

// -------------------------------------
type InternalAuthServiceForgotPasswordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Email         string                 `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	Domain        string                 `protobuf:"bytes,2,opt,name=domain,proto3" json:"domain,omitempty"`
	Otp           string                 `protobuf:"bytes,3,opt,name=otp,proto3" json:"otp,omitempty"`
	NewPassword   string                 `protobuf:"bytes,4,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty"`
	IpAddress     string                 `protobuf:"bytes,5,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceForgotPasswordRequest) Reset() {
	*x = InternalAuthServiceForgotPasswordRequest{}
	mi := &file_auth_v1_internal_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceForgotPasswordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceForgotPasswordRequest) ProtoMessage() {}

func (x *InternalAuthServiceForgotPasswordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceForgotPasswordRequest.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceForgotPasswordRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{32}
}

func (x *InternalAuthServiceForgotPasswordRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *InternalAuthServiceForgotPasswordRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *InternalAuthServiceForgotPasswordRequest) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *InternalAuthServiceForgotPasswordRequest) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

func (x *InternalAuthServiceForgotPasswordRequest) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

type InternalAuthServiceForgotPasswordResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalAuthServiceForgotPasswordResponse) Reset() {
	*x = InternalAuthServiceForgotPasswordResponse{}
	mi := &file_auth_v1_internal_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalAuthServiceForgotPasswordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalAuthServiceForgotPasswordResponse) ProtoMessage() {}

func (x *InternalAuthServiceForgotPasswordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_internal_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalAuthServiceForgotPasswordResponse.ProtoReflect.Descriptor instead.
func (*InternalAuthServiceForgotPasswordResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_internal_proto_rawDescGZIP(), []int{33}
}

func (x *InternalAuthServiceForgotPasswordResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_auth_v1_internal_proto protoreflect.FileDescriptor

const file_auth_v1_internal_proto_rawDesc = "" +
	"\n" +
	"\x16auth/v1/internal.proto\x12\aauth.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\x1a\x1aalgoenum/v1/app_type.proto\x1a\x1dalgoenum/v1/app_country.proto\"G\n" +
	"#InternalAuthServiceFetchUserRequest\x12 \n" +
	"\flist_id_user\x18\x01 \x03(\tR\n" +
	"listIdUser\"l\n" +
	"$InternalAuthServiceFetchUserResponse\x12D\n" +
	"\x05users\x18\x01 \x01(\v2..auth.v1.InternalAuthServiceFetchUserUserModelR\x05users\"V\n" +
	"%InternalAuthServiceFetchUserUserModel\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\"[\n" +
	"'InternalAuthServiceCustomerLoginRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\"o\n" +
	"(InternalAuthServiceCustomerLoginResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\"K\n" +
	"'InternalAuthServiceFetchUserInfoRequest\x12 \n" +
	"\flist_id_user\x18\x01 \x03(\tR\n" +
	"listIdUser\"\x8f\x01\n" +
	"(InternalAuthServiceFetchUserInfoResponse\x124\n" +
	"\x05users\x18\x01 \x03(\v2\x1e.auth.v1.InternalUserInfoModelR\x05users\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x9f\x01\n" +
	"\x15InternalUserInfoModel\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\x12\x1d\n" +
	"\n" +
	"first_name\x18\x03 \x01(\tR\tfirstName\x12\x1b\n" +
	"\tlast_name\x18\x04 \x01(\tR\blastName\x12\x1b\n" +
	"\tis_active\x18\x05 \x01(\bR\bisActive\"\xaa\x01\n" +
	"+InternalAuthServiceCreateAppMerchantRequest\x12\x15\n" +
	"\x06id_app\x18\x01 \x01(\tR\x05idApp\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06domain\x18\x03 \x01(\tR\x06domain\x128\n" +
	"\vapp_country\x18\x04 \x01(\x0e2\x17.algoenum.v1.AppCountryR\n" +
	"appCountry\"]\n" +
	",InternalAuthServiceCreateAppMerchantResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xf3\x01\n" +
	"$AuthInternalServiceCreateUserRequest\x12\x15\n" +
	"\x06id_app\x18\x01 \x01(\tR\x05idApp\x12\x1f\n" +
	"\vname_search\x18\x02 \x01(\tR\n" +
	"nameSearch\x12/\n" +
	"\bapp_type\x18\x03 \x01(\x0e2\x14.algoenum.v1.AppTypeR\aappType\x12%\n" +
	"\x05state\x18\x04 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"o\n" +
	"%AuthInternalServiceCreateUserResponse\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xd9\x02\n" +
	"\"InternalAuthServiceFetchAppRequest\x12\x1e\n" +
	"\vlist_id_app\x18\x01 \x03(\tR\tlistIdApp\x12\x1f\n" +
	"\vname_search\x18\x02 \x01(\tR\n" +
	"nameSearch\x12#\n" +
	"\rdomain_search\x18\x03 \x01(\tR\fdomainSearch\x12/\n" +
	"\bapp_type\x18\x04 \x01(\x0e2\x14.algoenum.v1.AppTypeR\aappType\x128\n" +
	"\vapp_country\x18\x05 \x01(\x0e2\x17.algoenum.v1.AppCountryR\n" +
	"appCountry\x12%\n" +
	"\x05state\x18\x06 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\a \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xc3\x01\n" +
	"#InternalAuthServiceFetchAppResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12/\n" +
	"\x05items\x18\x03 \x03(\v2\x19.auth.v1.InternalAppModelR\x05items\"\xab\x01\n" +
	"'InternalAuthServiceMerchantLoginRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x12\x1d\n" +
	"\n" +
	"ip_address\x18\x03 \x01(\tR\tipAddress\x12\x1d\n" +
	"\n" +
	"user_agent\x18\x04 \x01(\tR\tuserAgent\x12\x10\n" +
	"\x03otp\x18\x05 \x01(\tR\x03otp\"\x9f\x01\n" +
	"(InternalAuthServiceMerchantLoginResponse\x12.\n" +
	"\x04user\x18\x01 \x01(\v2\x1a.auth.v1.InternalUserModelR\x04user\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xd8\x01\n" +
	"\x1fInternalAuthServiceLoginRequest\x12\x16\n" +
	"\x06domain\x18\x01 \x01(\tR\x06domain\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\x12\x1a\n" +
	"\bpassword\x18\x03 \x01(\tR\bpassword\x12\x1d\n" +
	"\n" +
	"ip_address\x18\x04 \x01(\tR\tipAddress\x12\x1d\n" +
	"\n" +
	"user_agent\x18\x05 \x01(\tR\tuserAgent\x12\x10\n" +
	"\x03otp\x18\x06 \x01(\tR\x03otp\x12\x1b\n" +
	"\tdevice_id\x18\a \x01(\tR\bdeviceId\"\xbc\x01\n" +
	" InternalAuthServiceLoginResponse\x12.\n" +
	"\x04user\x18\x01 \x01(\v2\x1a.auth.v1.InternalUserModelR\x04user\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\x12#\n" +
	"\rrefresh_token\x18\x03 \x01(\tR\frefreshToken\x12-\n" +
	"\x05error\x18\x04 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"P\n" +
	" InternalAuthServiceLogoutRequest\x12\x16\n" +
	"\x06domain\x18\x01 \x01(\tR\x06domain\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\"l\n" +
	"!InternalAuthServiceLogoutResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x9f\x01\n" +
	"\"InternalAuthServiceRegisterRequest\x12\x15\n" +
	"\x06id_app\x18\x01 \x01(\tR\x05idApp\x12\x17\n" +
	"\aid_user\x18\x02 \x01(\tR\x06idUser\x12\x17\n" +
	"\aid_role\x18\x03 \x01(\tR\x06idRole\x12\x14\n" +
	"\x05email\x18\x04 \x01(\tR\x05email\x12\x1a\n" +
	"\bpassword\x18\x05 \x01(\tR\bpassword\"T\n" +
	"#InternalAuthServiceRegisterResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xa0\x01\n" +
	"(InternalAuthServiceChangePasswordRequest\x12.\n" +
	"\x04user\x18\x01 \x01(\v2\x1a.auth.v1.InternalUserModelR\x04user\x12!\n" +
	"\fold_password\x18\x02 \x01(\tR\voldPassword\x12!\n" +
	"\fnew_password\x18\x03 \x01(\tR\vnewPassword\"Z\n" +
	")InternalAuthServiceChangePasswordResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"v\n" +
	" InternalAuthServiceVerifyRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12#\n" +
	"\rabsolute_path\x18\x02 \x01(\tR\fabsolutePath\x12\x17\n" +
	"\auser_ip\x18\x03 \x01(\tR\x06userIp\"\x82\x01\n" +
	"!InternalAuthServiceVerifyResponse\x12.\n" +
	"\x04user\x18\x01 \x01(\v2\x1a.auth.v1.InternalUserModelR\x04user\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x99\x01\n" +
	"\x11InternalUserModel\x12\x15\n" +
	"\x06id_app\x18\x01 \x01(\tR\x05idApp\x12\x17\n" +
	"\aid_user\x18\x02 \x01(\tR\x06idUser\x12\x17\n" +
	"\aid_role\x18\x03 \x01(\tR\x06idRole\x12\x17\n" +
	"\auser_ip\x18\x04 \x01(\tR\x06userIp\x12\x10\n" +
	"\x03lat\x18\x05 \x01(\x01R\x03lat\x12\x10\n" +
	"\x03lng\x18\x06 \x01(\x01R\x03lng\"\xa4\x02\n" +
	"\x10InternalAppModel\x12\x15\n" +
	"\x06id_app\x18\x01 \x01(\tR\x05idApp\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06domain\x18\x03 \x01(\tR\x06domain\x12/\n" +
	"\bapp_type\x18\x04 \x01(\x0e2\x14.algoenum.v1.AppTypeR\aappType\x12(\n" +
	"\x10short_public_key\x18\x05 \x01(\tR\x0eshortPublicKey\x12\x1b\n" +
	"\ttoken_ttl\x18\x06 \x01(\x04R\btokenTtl\x12\x1b\n" +
	"\tis_active\x18\a \x01(\bR\bisActive\x128\n" +
	"\vapp_country\x18\b \x01(\x0e2\x17.algoenum.v1.AppCountryR\n" +
	"appCountry\"\x8d\x01\n" +
	"&InternalAuthServiceSendMailHtmlRequest\x12\x0e\n" +
	"\x02to\x18\x01 \x03(\tR\x02to\x12\x0e\n" +
	"\x02cc\x18\x02 \x03(\tR\x02cc\x12\x18\n" +
	"\asubject\x18\x03 \x01(\tR\asubject\x12\x12\n" +
	"\x04body\x18\x04 \x01(\tR\x04body\x12\x15\n" +
	"\x06id_app\x18\x05 \x01(\tR\x05idApp\"X\n" +
	"'InternalAuthServiceSendMailHtmlResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x81\x01\n" +
	"&InternalAuthServiceRefreshTokenRequest\x12\x15\n" +
	"\x06id_app\x18\x01 \x01(\tR\x05idApp\x12\x1b\n" +
	"\tdevice_id\x18\x02 \x01(\tR\bdeviceId\x12#\n" +
	"\rrefresh_token\x18\x03 \x01(\tR\frefreshToken\"\x93\x01\n" +
	"'InternalAuthServiceRefreshTokenResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\x12#\n" +
	"\rrefresh_token\x18\x04 \x01(\tR\frefreshToken\"\xac\x01\n" +
	"(InternalAuthServiceForgotPasswordRequest\x12\x14\n" +
	"\x05email\x18\x01 \x01(\tR\x05email\x12\x16\n" +
	"\x06domain\x18\x02 \x01(\tR\x06domain\x12\x10\n" +
	"\x03otp\x18\x03 \x01(\tR\x03otp\x12!\n" +
	"\fnew_password\x18\x04 \x01(\tR\vnewPassword\x12\x1d\n" +
	"\n" +
	"ip_address\x18\x05 \x01(\tR\tipAddress\"Z\n" +
	")InternalAuthServiceForgotPasswordResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error2\xfd\a\n" +
	"\x13InternalAuthService\x12\\\n" +
	"\x05Login\x12(.auth.v1.InternalAuthServiceLoginRequest\x1a).auth.v1.InternalAuthServiceLoginResponse\x12_\n" +
	"\x06Verify\x12).auth.v1.InternalAuthServiceVerifyRequest\x1a*.auth.v1.InternalAuthServiceVerifyResponse\x12e\n" +
	"\bFetchApp\x12+.auth.v1.InternalAuthServiceFetchAppRequest\x1a,.auth.v1.InternalAuthServiceFetchAppResponse\x12h\n" +
	"\tFetchUser\x12,.auth.v1.InternalAuthServiceFetchUserRequest\x1a-.auth.v1.InternalAuthServiceFetchUserResponse\x12\x80\x01\n" +
	"\x11CreateAppMerchant\x124.auth.v1.InternalAuthServiceCreateAppMerchantRequest\x1a5.auth.v1.InternalAuthServiceCreateAppMerchantResponse\x12t\n" +
	"\rFetchUserInfo\x120.auth.v1.InternalAuthServiceFetchUserInfoRequest\x1a1.auth.v1.InternalAuthServiceFetchUserInfoResponse\x12q\n" +
	"\fSendMailHtml\x12/.auth.v1.InternalAuthServiceSendMailHtmlRequest\x1a0.auth.v1.InternalAuthServiceSendMailHtmlResponse\x12q\n" +
	"\fRefreshToken\x12/.auth.v1.InternalAuthServiceRefreshTokenRequest\x1a0.auth.v1.InternalAuthServiceRefreshTokenResponse\x12w\n" +
	"\x0eForgotPassword\x121.auth.v1.InternalAuthServiceForgotPasswordRequest\x1a2.auth.v1.InternalAuthServiceForgotPasswordResponseB?Z=git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1;authv1b\x06proto3"

var (
	file_auth_v1_internal_proto_rawDescOnce sync.Once
	file_auth_v1_internal_proto_rawDescData []byte
)

func file_auth_v1_internal_proto_rawDescGZIP() []byte {
	file_auth_v1_internal_proto_rawDescOnce.Do(func() {
		file_auth_v1_internal_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_auth_v1_internal_proto_rawDesc), len(file_auth_v1_internal_proto_rawDesc)))
	})
	return file_auth_v1_internal_proto_rawDescData
}

var file_auth_v1_internal_proto_msgTypes = make([]protoimpl.MessageInfo, 34)
var file_auth_v1_internal_proto_goTypes = []any{
	(*InternalAuthServiceFetchUserRequest)(nil),          // 0: auth.v1.InternalAuthServiceFetchUserRequest
	(*InternalAuthServiceFetchUserResponse)(nil),         // 1: auth.v1.InternalAuthServiceFetchUserResponse
	(*InternalAuthServiceFetchUserUserModel)(nil),        // 2: auth.v1.InternalAuthServiceFetchUserUserModel
	(*InternalAuthServiceCustomerLoginRequest)(nil),      // 3: auth.v1.InternalAuthServiceCustomerLoginRequest
	(*InternalAuthServiceCustomerLoginResponse)(nil),     // 4: auth.v1.InternalAuthServiceCustomerLoginResponse
	(*InternalAuthServiceFetchUserInfoRequest)(nil),      // 5: auth.v1.InternalAuthServiceFetchUserInfoRequest
	(*InternalAuthServiceFetchUserInfoResponse)(nil),     // 6: auth.v1.InternalAuthServiceFetchUserInfoResponse
	(*InternalUserInfoModel)(nil),                        // 7: auth.v1.InternalUserInfoModel
	(*InternalAuthServiceCreateAppMerchantRequest)(nil),  // 8: auth.v1.InternalAuthServiceCreateAppMerchantRequest
	(*InternalAuthServiceCreateAppMerchantResponse)(nil), // 9: auth.v1.InternalAuthServiceCreateAppMerchantResponse
	(*AuthInternalServiceCreateUserRequest)(nil),         // 10: auth.v1.AuthInternalServiceCreateUserRequest
	(*AuthInternalServiceCreateUserResponse)(nil),        // 11: auth.v1.AuthInternalServiceCreateUserResponse
	(*InternalAuthServiceFetchAppRequest)(nil),           // 12: auth.v1.InternalAuthServiceFetchAppRequest
	(*InternalAuthServiceFetchAppResponse)(nil),          // 13: auth.v1.InternalAuthServiceFetchAppResponse
	(*InternalAuthServiceMerchantLoginRequest)(nil),      // 14: auth.v1.InternalAuthServiceMerchantLoginRequest
	(*InternalAuthServiceMerchantLoginResponse)(nil),     // 15: auth.v1.InternalAuthServiceMerchantLoginResponse
	(*InternalAuthServiceLoginRequest)(nil),              // 16: auth.v1.InternalAuthServiceLoginRequest
	(*InternalAuthServiceLoginResponse)(nil),             // 17: auth.v1.InternalAuthServiceLoginResponse
	(*InternalAuthServiceLogoutRequest)(nil),             // 18: auth.v1.InternalAuthServiceLogoutRequest
	(*InternalAuthServiceLogoutResponse)(nil),            // 19: auth.v1.InternalAuthServiceLogoutResponse
	(*InternalAuthServiceRegisterRequest)(nil),           // 20: auth.v1.InternalAuthServiceRegisterRequest
	(*InternalAuthServiceRegisterResponse)(nil),          // 21: auth.v1.InternalAuthServiceRegisterResponse
	(*InternalAuthServiceChangePasswordRequest)(nil),     // 22: auth.v1.InternalAuthServiceChangePasswordRequest
	(*InternalAuthServiceChangePasswordResponse)(nil),    // 23: auth.v1.InternalAuthServiceChangePasswordResponse
	(*InternalAuthServiceVerifyRequest)(nil),             // 24: auth.v1.InternalAuthServiceVerifyRequest
	(*InternalAuthServiceVerifyResponse)(nil),            // 25: auth.v1.InternalAuthServiceVerifyResponse
	(*InternalUserModel)(nil),                            // 26: auth.v1.InternalUserModel
	(*InternalAppModel)(nil),                             // 27: auth.v1.InternalAppModel
	(*InternalAuthServiceSendMailHtmlRequest)(nil),       // 28: auth.v1.InternalAuthServiceSendMailHtmlRequest
	(*InternalAuthServiceSendMailHtmlResponse)(nil),      // 29: auth.v1.InternalAuthServiceSendMailHtmlResponse
	(*InternalAuthServiceRefreshTokenRequest)(nil),       // 30: auth.v1.InternalAuthServiceRefreshTokenRequest
	(*InternalAuthServiceRefreshTokenResponse)(nil),      // 31: auth.v1.InternalAuthServiceRefreshTokenResponse
	(*InternalAuthServiceForgotPasswordRequest)(nil),     // 32: auth.v1.InternalAuthServiceForgotPasswordRequest
	(*InternalAuthServiceForgotPasswordResponse)(nil),    // 33: auth.v1.InternalAuthServiceForgotPasswordResponse
	(*v1.ErrorMessage)(nil),                              // 34: errmsg.v1.ErrorMessage
	(v11.AppCountry)(0),                                  // 35: algoenum.v1.AppCountry
	(v11.AppType)(0),                                     // 36: algoenum.v1.AppType
	(*v12.State)(nil),                                    // 37: utils.v1.State
	(*v12.PaginationRequest)(nil),                        // 38: utils.v1.PaginationRequest
	(*v12.PaginationResponse)(nil),                       // 39: utils.v1.PaginationResponse
}
var file_auth_v1_internal_proto_depIdxs = []int32{
	2,  // 0: auth.v1.InternalAuthServiceFetchUserResponse.users:type_name -> auth.v1.InternalAuthServiceFetchUserUserModel
	34, // 1: auth.v1.InternalAuthServiceCustomerLoginResponse.error:type_name -> errmsg.v1.ErrorMessage
	7,  // 2: auth.v1.InternalAuthServiceFetchUserInfoResponse.users:type_name -> auth.v1.InternalUserInfoModel
	34, // 3: auth.v1.InternalAuthServiceFetchUserInfoResponse.error:type_name -> errmsg.v1.ErrorMessage
	35, // 4: auth.v1.InternalAuthServiceCreateAppMerchantRequest.app_country:type_name -> algoenum.v1.AppCountry
	34, // 5: auth.v1.InternalAuthServiceCreateAppMerchantResponse.error:type_name -> errmsg.v1.ErrorMessage
	36, // 6: auth.v1.AuthInternalServiceCreateUserRequest.app_type:type_name -> algoenum.v1.AppType
	37, // 7: auth.v1.AuthInternalServiceCreateUserRequest.state:type_name -> utils.v1.State
	38, // 8: auth.v1.AuthInternalServiceCreateUserRequest.pagination:type_name -> utils.v1.PaginationRequest
	34, // 9: auth.v1.AuthInternalServiceCreateUserResponse.error:type_name -> errmsg.v1.ErrorMessage
	36, // 10: auth.v1.InternalAuthServiceFetchAppRequest.app_type:type_name -> algoenum.v1.AppType
	35, // 11: auth.v1.InternalAuthServiceFetchAppRequest.app_country:type_name -> algoenum.v1.AppCountry
	37, // 12: auth.v1.InternalAuthServiceFetchAppRequest.state:type_name -> utils.v1.State
	38, // 13: auth.v1.InternalAuthServiceFetchAppRequest.pagination:type_name -> utils.v1.PaginationRequest
	34, // 14: auth.v1.InternalAuthServiceFetchAppResponse.error:type_name -> errmsg.v1.ErrorMessage
	39, // 15: auth.v1.InternalAuthServiceFetchAppResponse.pagination:type_name -> utils.v1.PaginationResponse
	27, // 16: auth.v1.InternalAuthServiceFetchAppResponse.items:type_name -> auth.v1.InternalAppModel
	26, // 17: auth.v1.InternalAuthServiceMerchantLoginResponse.user:type_name -> auth.v1.InternalUserModel
	34, // 18: auth.v1.InternalAuthServiceMerchantLoginResponse.error:type_name -> errmsg.v1.ErrorMessage
	26, // 19: auth.v1.InternalAuthServiceLoginResponse.user:type_name -> auth.v1.InternalUserModel
	34, // 20: auth.v1.InternalAuthServiceLoginResponse.error:type_name -> errmsg.v1.ErrorMessage
	34, // 21: auth.v1.InternalAuthServiceLogoutResponse.error:type_name -> errmsg.v1.ErrorMessage
	34, // 22: auth.v1.InternalAuthServiceRegisterResponse.error:type_name -> errmsg.v1.ErrorMessage
	26, // 23: auth.v1.InternalAuthServiceChangePasswordRequest.user:type_name -> auth.v1.InternalUserModel
	34, // 24: auth.v1.InternalAuthServiceChangePasswordResponse.error:type_name -> errmsg.v1.ErrorMessage
	26, // 25: auth.v1.InternalAuthServiceVerifyResponse.user:type_name -> auth.v1.InternalUserModel
	34, // 26: auth.v1.InternalAuthServiceVerifyResponse.error:type_name -> errmsg.v1.ErrorMessage
	36, // 27: auth.v1.InternalAppModel.app_type:type_name -> algoenum.v1.AppType
	35, // 28: auth.v1.InternalAppModel.app_country:type_name -> algoenum.v1.AppCountry
	34, // 29: auth.v1.InternalAuthServiceSendMailHtmlResponse.error:type_name -> errmsg.v1.ErrorMessage
	34, // 30: auth.v1.InternalAuthServiceRefreshTokenResponse.error:type_name -> errmsg.v1.ErrorMessage
	34, // 31: auth.v1.InternalAuthServiceForgotPasswordResponse.error:type_name -> errmsg.v1.ErrorMessage
	16, // 32: auth.v1.InternalAuthService.Login:input_type -> auth.v1.InternalAuthServiceLoginRequest
	24, // 33: auth.v1.InternalAuthService.Verify:input_type -> auth.v1.InternalAuthServiceVerifyRequest
	12, // 34: auth.v1.InternalAuthService.FetchApp:input_type -> auth.v1.InternalAuthServiceFetchAppRequest
	0,  // 35: auth.v1.InternalAuthService.FetchUser:input_type -> auth.v1.InternalAuthServiceFetchUserRequest
	8,  // 36: auth.v1.InternalAuthService.CreateAppMerchant:input_type -> auth.v1.InternalAuthServiceCreateAppMerchantRequest
	5,  // 37: auth.v1.InternalAuthService.FetchUserInfo:input_type -> auth.v1.InternalAuthServiceFetchUserInfoRequest
	28, // 38: auth.v1.InternalAuthService.SendMailHtml:input_type -> auth.v1.InternalAuthServiceSendMailHtmlRequest
	30, // 39: auth.v1.InternalAuthService.RefreshToken:input_type -> auth.v1.InternalAuthServiceRefreshTokenRequest
	32, // 40: auth.v1.InternalAuthService.ForgotPassword:input_type -> auth.v1.InternalAuthServiceForgotPasswordRequest
	17, // 41: auth.v1.InternalAuthService.Login:output_type -> auth.v1.InternalAuthServiceLoginResponse
	25, // 42: auth.v1.InternalAuthService.Verify:output_type -> auth.v1.InternalAuthServiceVerifyResponse
	13, // 43: auth.v1.InternalAuthService.FetchApp:output_type -> auth.v1.InternalAuthServiceFetchAppResponse
	1,  // 44: auth.v1.InternalAuthService.FetchUser:output_type -> auth.v1.InternalAuthServiceFetchUserResponse
	9,  // 45: auth.v1.InternalAuthService.CreateAppMerchant:output_type -> auth.v1.InternalAuthServiceCreateAppMerchantResponse
	6,  // 46: auth.v1.InternalAuthService.FetchUserInfo:output_type -> auth.v1.InternalAuthServiceFetchUserInfoResponse
	29, // 47: auth.v1.InternalAuthService.SendMailHtml:output_type -> auth.v1.InternalAuthServiceSendMailHtmlResponse
	31, // 48: auth.v1.InternalAuthService.RefreshToken:output_type -> auth.v1.InternalAuthServiceRefreshTokenResponse
	33, // 49: auth.v1.InternalAuthService.ForgotPassword:output_type -> auth.v1.InternalAuthServiceForgotPasswordResponse
	41, // [41:50] is the sub-list for method output_type
	32, // [32:41] is the sub-list for method input_type
	32, // [32:32] is the sub-list for extension type_name
	32, // [32:32] is the sub-list for extension extendee
	0,  // [0:32] is the sub-list for field type_name
}

func init() { file_auth_v1_internal_proto_init() }
func file_auth_v1_internal_proto_init() {
	if File_auth_v1_internal_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_auth_v1_internal_proto_rawDesc), len(file_auth_v1_internal_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   34,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_auth_v1_internal_proto_goTypes,
		DependencyIndexes: file_auth_v1_internal_proto_depIdxs,
		MessageInfos:      file_auth_v1_internal_proto_msgTypes,
	}.Build()
	File_auth_v1_internal_proto = out.File
	file_auth_v1_internal_proto_goTypes = nil
	file_auth_v1_internal_proto_depIdxs = nil
}
