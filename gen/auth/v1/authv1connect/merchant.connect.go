// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: auth/v1/merchant.proto

package authv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// MerchantAuthServiceName is the fully-qualified name of the MerchantAuthService service.
	MerchantAuthServiceName = "auth.v1.MerchantAuthService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// MerchantAuthServiceLoginProcedure is the fully-qualified name of the MerchantAuthService's Login
	// RPC.
	MerchantAuthServiceLoginProcedure = "/auth.v1.MerchantAuthService/Login"
	// MerchantAuthServiceMeProcedure is the fully-qualified name of the MerchantAuthService's Me RPC.
	MerchantAuthServiceMeProcedure = "/auth.v1.MerchantAuthService/Me"
	// MerchantAuthServiceChangePasswordProcedure is the fully-qualified name of the
	// MerchantAuthService's ChangePassword RPC.
	MerchantAuthServiceChangePasswordProcedure = "/auth.v1.MerchantAuthService/ChangePassword"
	// MerchantAuthServiceRefreshTokenProcedure is the fully-qualified name of the MerchantAuthService's
	// RefreshToken RPC.
	MerchantAuthServiceRefreshTokenProcedure = "/auth.v1.MerchantAuthService/RefreshToken"
	// MerchantAuthServiceForgotPasswordProcedure is the fully-qualified name of the
	// MerchantAuthService's ForgotPassword RPC.
	MerchantAuthServiceForgotPasswordProcedure = "/auth.v1.MerchantAuthService/ForgotPassword"
	// MerchantAuthServiceFetchUserProcedure is the fully-qualified name of the MerchantAuthService's
	// FetchUser RPC.
	MerchantAuthServiceFetchUserProcedure = "/auth.v1.MerchantAuthService/FetchUser"
	// MerchantAuthServiceUpdateUserProcedure is the fully-qualified name of the MerchantAuthService's
	// UpdateUser RPC.
	MerchantAuthServiceUpdateUserProcedure = "/auth.v1.MerchantAuthService/UpdateUser"
	// MerchantAuthServiceFetchRoleProcedure is the fully-qualified name of the MerchantAuthService's
	// FetchRole RPC.
	MerchantAuthServiceFetchRoleProcedure = "/auth.v1.MerchantAuthService/FetchRole"
)

// MerchantAuthServiceClient is a client for the auth.v1.MerchantAuthService service.
type MerchantAuthServiceClient interface {
	Login(context.Context, *connect.Request[v1.MerchantAuthServiceLoginRequest]) (*connect.Response[v1.MerchantAuthServiceLoginResponse], error)
	Me(context.Context, *connect.Request[v1.MerchantAuthServiceMeRequest]) (*connect.Response[v1.MerchantAuthServiceMeResponse], error)
	ChangePassword(context.Context, *connect.Request[v1.MerchantAuthServiceChangePasswordRequest]) (*connect.Response[v1.MerchantAuthServiceChangePasswordResponse], error)
	RefreshToken(context.Context, *connect.Request[v1.MerchantAuthServiceRefreshTokenRequest]) (*connect.Response[v1.MerchantAuthServiceRefreshTokenResponse], error)
	ForgotPassword(context.Context, *connect.Request[v1.MerchantAuthServiceForgotPasswordRequest]) (*connect.Response[v1.MerchantAuthServiceForgotPasswordResponse], error)
	FetchUser(context.Context, *connect.Request[v1.MerchantAuthServiceFetchUserRequest]) (*connect.Response[v1.MerchantAuthServiceFetchUserResponse], error)
	UpdateUser(context.Context, *connect.Request[v1.MerchantAuthServiceUpdateUserRequest]) (*connect.Response[v1.MerchantAuthServiceUpdateUserResponse], error)
	FetchRole(context.Context, *connect.Request[v1.MerchantAuthServiceFetchRoleRequest]) (*connect.Response[v1.MerchantAuthServiceFetchRoleResponse], error)
}

// NewMerchantAuthServiceClient constructs a client for the auth.v1.MerchantAuthService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewMerchantAuthServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) MerchantAuthServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	merchantAuthServiceMethods := v1.File_auth_v1_merchant_proto.Services().ByName("MerchantAuthService").Methods()
	return &merchantAuthServiceClient{
		login: connect.NewClient[v1.MerchantAuthServiceLoginRequest, v1.MerchantAuthServiceLoginResponse](
			httpClient,
			baseURL+MerchantAuthServiceLoginProcedure,
			connect.WithSchema(merchantAuthServiceMethods.ByName("Login")),
			connect.WithClientOptions(opts...),
		),
		me: connect.NewClient[v1.MerchantAuthServiceMeRequest, v1.MerchantAuthServiceMeResponse](
			httpClient,
			baseURL+MerchantAuthServiceMeProcedure,
			connect.WithSchema(merchantAuthServiceMethods.ByName("Me")),
			connect.WithClientOptions(opts...),
		),
		changePassword: connect.NewClient[v1.MerchantAuthServiceChangePasswordRequest, v1.MerchantAuthServiceChangePasswordResponse](
			httpClient,
			baseURL+MerchantAuthServiceChangePasswordProcedure,
			connect.WithSchema(merchantAuthServiceMethods.ByName("ChangePassword")),
			connect.WithClientOptions(opts...),
		),
		refreshToken: connect.NewClient[v1.MerchantAuthServiceRefreshTokenRequest, v1.MerchantAuthServiceRefreshTokenResponse](
			httpClient,
			baseURL+MerchantAuthServiceRefreshTokenProcedure,
			connect.WithSchema(merchantAuthServiceMethods.ByName("RefreshToken")),
			connect.WithClientOptions(opts...),
		),
		forgotPassword: connect.NewClient[v1.MerchantAuthServiceForgotPasswordRequest, v1.MerchantAuthServiceForgotPasswordResponse](
			httpClient,
			baseURL+MerchantAuthServiceForgotPasswordProcedure,
			connect.WithSchema(merchantAuthServiceMethods.ByName("ForgotPassword")),
			connect.WithClientOptions(opts...),
		),
		fetchUser: connect.NewClient[v1.MerchantAuthServiceFetchUserRequest, v1.MerchantAuthServiceFetchUserResponse](
			httpClient,
			baseURL+MerchantAuthServiceFetchUserProcedure,
			connect.WithSchema(merchantAuthServiceMethods.ByName("FetchUser")),
			connect.WithClientOptions(opts...),
		),
		updateUser: connect.NewClient[v1.MerchantAuthServiceUpdateUserRequest, v1.MerchantAuthServiceUpdateUserResponse](
			httpClient,
			baseURL+MerchantAuthServiceUpdateUserProcedure,
			connect.WithSchema(merchantAuthServiceMethods.ByName("UpdateUser")),
			connect.WithClientOptions(opts...),
		),
		fetchRole: connect.NewClient[v1.MerchantAuthServiceFetchRoleRequest, v1.MerchantAuthServiceFetchRoleResponse](
			httpClient,
			baseURL+MerchantAuthServiceFetchRoleProcedure,
			connect.WithSchema(merchantAuthServiceMethods.ByName("FetchRole")),
			connect.WithClientOptions(opts...),
		),
	}
}

// merchantAuthServiceClient implements MerchantAuthServiceClient.
type merchantAuthServiceClient struct {
	login          *connect.Client[v1.MerchantAuthServiceLoginRequest, v1.MerchantAuthServiceLoginResponse]
	me             *connect.Client[v1.MerchantAuthServiceMeRequest, v1.MerchantAuthServiceMeResponse]
	changePassword *connect.Client[v1.MerchantAuthServiceChangePasswordRequest, v1.MerchantAuthServiceChangePasswordResponse]
	refreshToken   *connect.Client[v1.MerchantAuthServiceRefreshTokenRequest, v1.MerchantAuthServiceRefreshTokenResponse]
	forgotPassword *connect.Client[v1.MerchantAuthServiceForgotPasswordRequest, v1.MerchantAuthServiceForgotPasswordResponse]
	fetchUser      *connect.Client[v1.MerchantAuthServiceFetchUserRequest, v1.MerchantAuthServiceFetchUserResponse]
	updateUser     *connect.Client[v1.MerchantAuthServiceUpdateUserRequest, v1.MerchantAuthServiceUpdateUserResponse]
	fetchRole      *connect.Client[v1.MerchantAuthServiceFetchRoleRequest, v1.MerchantAuthServiceFetchRoleResponse]
}

// Login calls auth.v1.MerchantAuthService.Login.
func (c *merchantAuthServiceClient) Login(ctx context.Context, req *connect.Request[v1.MerchantAuthServiceLoginRequest]) (*connect.Response[v1.MerchantAuthServiceLoginResponse], error) {
	return c.login.CallUnary(ctx, req)
}

// Me calls auth.v1.MerchantAuthService.Me.
func (c *merchantAuthServiceClient) Me(ctx context.Context, req *connect.Request[v1.MerchantAuthServiceMeRequest]) (*connect.Response[v1.MerchantAuthServiceMeResponse], error) {
	return c.me.CallUnary(ctx, req)
}

// ChangePassword calls auth.v1.MerchantAuthService.ChangePassword.
func (c *merchantAuthServiceClient) ChangePassword(ctx context.Context, req *connect.Request[v1.MerchantAuthServiceChangePasswordRequest]) (*connect.Response[v1.MerchantAuthServiceChangePasswordResponse], error) {
	return c.changePassword.CallUnary(ctx, req)
}

// RefreshToken calls auth.v1.MerchantAuthService.RefreshToken.
func (c *merchantAuthServiceClient) RefreshToken(ctx context.Context, req *connect.Request[v1.MerchantAuthServiceRefreshTokenRequest]) (*connect.Response[v1.MerchantAuthServiceRefreshTokenResponse], error) {
	return c.refreshToken.CallUnary(ctx, req)
}

// ForgotPassword calls auth.v1.MerchantAuthService.ForgotPassword.
func (c *merchantAuthServiceClient) ForgotPassword(ctx context.Context, req *connect.Request[v1.MerchantAuthServiceForgotPasswordRequest]) (*connect.Response[v1.MerchantAuthServiceForgotPasswordResponse], error) {
	return c.forgotPassword.CallUnary(ctx, req)
}

// FetchUser calls auth.v1.MerchantAuthService.FetchUser.
func (c *merchantAuthServiceClient) FetchUser(ctx context.Context, req *connect.Request[v1.MerchantAuthServiceFetchUserRequest]) (*connect.Response[v1.MerchantAuthServiceFetchUserResponse], error) {
	return c.fetchUser.CallUnary(ctx, req)
}

// UpdateUser calls auth.v1.MerchantAuthService.UpdateUser.
func (c *merchantAuthServiceClient) UpdateUser(ctx context.Context, req *connect.Request[v1.MerchantAuthServiceUpdateUserRequest]) (*connect.Response[v1.MerchantAuthServiceUpdateUserResponse], error) {
	return c.updateUser.CallUnary(ctx, req)
}

// FetchRole calls auth.v1.MerchantAuthService.FetchRole.
func (c *merchantAuthServiceClient) FetchRole(ctx context.Context, req *connect.Request[v1.MerchantAuthServiceFetchRoleRequest]) (*connect.Response[v1.MerchantAuthServiceFetchRoleResponse], error) {
	return c.fetchRole.CallUnary(ctx, req)
}

// MerchantAuthServiceHandler is an implementation of the auth.v1.MerchantAuthService service.
type MerchantAuthServiceHandler interface {
	Login(context.Context, *connect.Request[v1.MerchantAuthServiceLoginRequest]) (*connect.Response[v1.MerchantAuthServiceLoginResponse], error)
	Me(context.Context, *connect.Request[v1.MerchantAuthServiceMeRequest]) (*connect.Response[v1.MerchantAuthServiceMeResponse], error)
	ChangePassword(context.Context, *connect.Request[v1.MerchantAuthServiceChangePasswordRequest]) (*connect.Response[v1.MerchantAuthServiceChangePasswordResponse], error)
	RefreshToken(context.Context, *connect.Request[v1.MerchantAuthServiceRefreshTokenRequest]) (*connect.Response[v1.MerchantAuthServiceRefreshTokenResponse], error)
	ForgotPassword(context.Context, *connect.Request[v1.MerchantAuthServiceForgotPasswordRequest]) (*connect.Response[v1.MerchantAuthServiceForgotPasswordResponse], error)
	FetchUser(context.Context, *connect.Request[v1.MerchantAuthServiceFetchUserRequest]) (*connect.Response[v1.MerchantAuthServiceFetchUserResponse], error)
	UpdateUser(context.Context, *connect.Request[v1.MerchantAuthServiceUpdateUserRequest]) (*connect.Response[v1.MerchantAuthServiceUpdateUserResponse], error)
	FetchRole(context.Context, *connect.Request[v1.MerchantAuthServiceFetchRoleRequest]) (*connect.Response[v1.MerchantAuthServiceFetchRoleResponse], error)
}

// NewMerchantAuthServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewMerchantAuthServiceHandler(svc MerchantAuthServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	merchantAuthServiceMethods := v1.File_auth_v1_merchant_proto.Services().ByName("MerchantAuthService").Methods()
	merchantAuthServiceLoginHandler := connect.NewUnaryHandler(
		MerchantAuthServiceLoginProcedure,
		svc.Login,
		connect.WithSchema(merchantAuthServiceMethods.ByName("Login")),
		connect.WithHandlerOptions(opts...),
	)
	merchantAuthServiceMeHandler := connect.NewUnaryHandler(
		MerchantAuthServiceMeProcedure,
		svc.Me,
		connect.WithSchema(merchantAuthServiceMethods.ByName("Me")),
		connect.WithHandlerOptions(opts...),
	)
	merchantAuthServiceChangePasswordHandler := connect.NewUnaryHandler(
		MerchantAuthServiceChangePasswordProcedure,
		svc.ChangePassword,
		connect.WithSchema(merchantAuthServiceMethods.ByName("ChangePassword")),
		connect.WithHandlerOptions(opts...),
	)
	merchantAuthServiceRefreshTokenHandler := connect.NewUnaryHandler(
		MerchantAuthServiceRefreshTokenProcedure,
		svc.RefreshToken,
		connect.WithSchema(merchantAuthServiceMethods.ByName("RefreshToken")),
		connect.WithHandlerOptions(opts...),
	)
	merchantAuthServiceForgotPasswordHandler := connect.NewUnaryHandler(
		MerchantAuthServiceForgotPasswordProcedure,
		svc.ForgotPassword,
		connect.WithSchema(merchantAuthServiceMethods.ByName("ForgotPassword")),
		connect.WithHandlerOptions(opts...),
	)
	merchantAuthServiceFetchUserHandler := connect.NewUnaryHandler(
		MerchantAuthServiceFetchUserProcedure,
		svc.FetchUser,
		connect.WithSchema(merchantAuthServiceMethods.ByName("FetchUser")),
		connect.WithHandlerOptions(opts...),
	)
	merchantAuthServiceUpdateUserHandler := connect.NewUnaryHandler(
		MerchantAuthServiceUpdateUserProcedure,
		svc.UpdateUser,
		connect.WithSchema(merchantAuthServiceMethods.ByName("UpdateUser")),
		connect.WithHandlerOptions(opts...),
	)
	merchantAuthServiceFetchRoleHandler := connect.NewUnaryHandler(
		MerchantAuthServiceFetchRoleProcedure,
		svc.FetchRole,
		connect.WithSchema(merchantAuthServiceMethods.ByName("FetchRole")),
		connect.WithHandlerOptions(opts...),
	)
	return "/auth.v1.MerchantAuthService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case MerchantAuthServiceLoginProcedure:
			merchantAuthServiceLoginHandler.ServeHTTP(w, r)
		case MerchantAuthServiceMeProcedure:
			merchantAuthServiceMeHandler.ServeHTTP(w, r)
		case MerchantAuthServiceChangePasswordProcedure:
			merchantAuthServiceChangePasswordHandler.ServeHTTP(w, r)
		case MerchantAuthServiceRefreshTokenProcedure:
			merchantAuthServiceRefreshTokenHandler.ServeHTTP(w, r)
		case MerchantAuthServiceForgotPasswordProcedure:
			merchantAuthServiceForgotPasswordHandler.ServeHTTP(w, r)
		case MerchantAuthServiceFetchUserProcedure:
			merchantAuthServiceFetchUserHandler.ServeHTTP(w, r)
		case MerchantAuthServiceUpdateUserProcedure:
			merchantAuthServiceUpdateUserHandler.ServeHTTP(w, r)
		case MerchantAuthServiceFetchRoleProcedure:
			merchantAuthServiceFetchRoleHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedMerchantAuthServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedMerchantAuthServiceHandler struct{}

func (UnimplementedMerchantAuthServiceHandler) Login(context.Context, *connect.Request[v1.MerchantAuthServiceLoginRequest]) (*connect.Response[v1.MerchantAuthServiceLoginResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.MerchantAuthService.Login is not implemented"))
}

func (UnimplementedMerchantAuthServiceHandler) Me(context.Context, *connect.Request[v1.MerchantAuthServiceMeRequest]) (*connect.Response[v1.MerchantAuthServiceMeResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.MerchantAuthService.Me is not implemented"))
}

func (UnimplementedMerchantAuthServiceHandler) ChangePassword(context.Context, *connect.Request[v1.MerchantAuthServiceChangePasswordRequest]) (*connect.Response[v1.MerchantAuthServiceChangePasswordResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.MerchantAuthService.ChangePassword is not implemented"))
}

func (UnimplementedMerchantAuthServiceHandler) RefreshToken(context.Context, *connect.Request[v1.MerchantAuthServiceRefreshTokenRequest]) (*connect.Response[v1.MerchantAuthServiceRefreshTokenResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.MerchantAuthService.RefreshToken is not implemented"))
}

func (UnimplementedMerchantAuthServiceHandler) ForgotPassword(context.Context, *connect.Request[v1.MerchantAuthServiceForgotPasswordRequest]) (*connect.Response[v1.MerchantAuthServiceForgotPasswordResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.MerchantAuthService.ForgotPassword is not implemented"))
}

func (UnimplementedMerchantAuthServiceHandler) FetchUser(context.Context, *connect.Request[v1.MerchantAuthServiceFetchUserRequest]) (*connect.Response[v1.MerchantAuthServiceFetchUserResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.MerchantAuthService.FetchUser is not implemented"))
}

func (UnimplementedMerchantAuthServiceHandler) UpdateUser(context.Context, *connect.Request[v1.MerchantAuthServiceUpdateUserRequest]) (*connect.Response[v1.MerchantAuthServiceUpdateUserResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.MerchantAuthService.UpdateUser is not implemented"))
}

func (UnimplementedMerchantAuthServiceHandler) FetchRole(context.Context, *connect.Request[v1.MerchantAuthServiceFetchRoleRequest]) (*connect.Response[v1.MerchantAuthServiceFetchRoleResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.MerchantAuthService.FetchRole is not implemented"))
}
