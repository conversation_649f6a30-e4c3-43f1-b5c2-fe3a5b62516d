// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: auth/v1/backoffice_auth.proto

package authv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackofficeAuthServiceName is the fully-qualified name of the BackofficeAuthService service.
	BackofficeAuthServiceName = "auth.v1.BackofficeAuthService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackofficeAuthServiceLoginProcedure is the fully-qualified name of the BackofficeAuthService's
	// Login RPC.
	BackofficeAuthServiceLoginProcedure = "/auth.v1.BackofficeAuthService/Login"
	// BackofficeAuthServiceMeProcedure is the fully-qualified name of the BackofficeAuthService's Me
	// RPC.
	BackofficeAuthServiceMeProcedure = "/auth.v1.BackofficeAuthService/Me"
	// BackofficeAuthServiceChangePasswordProcedure is the fully-qualified name of the
	// BackofficeAuthService's ChangePassword RPC.
	BackofficeAuthServiceChangePasswordProcedure = "/auth.v1.BackofficeAuthService/ChangePassword"
	// BackofficeAuthServiceRefreshTokenProcedure is the fully-qualified name of the
	// BackofficeAuthService's RefreshToken RPC.
	BackofficeAuthServiceRefreshTokenProcedure = "/auth.v1.BackofficeAuthService/RefreshToken"
	// BackofficeAuthServiceForgotPasswordProcedure is the fully-qualified name of the
	// BackofficeAuthService's ForgotPassword RPC.
	BackofficeAuthServiceForgotPasswordProcedure = "/auth.v1.BackofficeAuthService/ForgotPassword"
	// BackofficeAuthServiceReloadEnforcerProcedure is the fully-qualified name of the
	// BackofficeAuthService's ReloadEnforcer RPC.
	BackofficeAuthServiceReloadEnforcerProcedure = "/auth.v1.BackofficeAuthService/ReloadEnforcer"
	// BackofficeAuthServiceCreateAppProcedure is the fully-qualified name of the
	// BackofficeAuthService's CreateApp RPC.
	BackofficeAuthServiceCreateAppProcedure = "/auth.v1.BackofficeAuthService/CreateApp"
	// BackofficeAuthServiceFetchAppProcedure is the fully-qualified name of the BackofficeAuthService's
	// FetchApp RPC.
	BackofficeAuthServiceFetchAppProcedure = "/auth.v1.BackofficeAuthService/FetchApp"
	// BackofficeAuthServiceUpdateAppProcedure is the fully-qualified name of the
	// BackofficeAuthService's UpdateApp RPC.
	BackofficeAuthServiceUpdateAppProcedure = "/auth.v1.BackofficeAuthService/UpdateApp"
	// BackofficeAuthServiceFetchUserProcedure is the fully-qualified name of the
	// BackofficeAuthService's FetchUser RPC.
	BackofficeAuthServiceFetchUserProcedure = "/auth.v1.BackofficeAuthService/FetchUser"
	// BackofficeAuthServiceCreateUserProcedure is the fully-qualified name of the
	// BackofficeAuthService's CreateUser RPC.
	BackofficeAuthServiceCreateUserProcedure = "/auth.v1.BackofficeAuthService/CreateUser"
	// BackofficeAuthServiceUpdateUserProcedure is the fully-qualified name of the
	// BackofficeAuthService's UpdateUser RPC.
	BackofficeAuthServiceUpdateUserProcedure = "/auth.v1.BackofficeAuthService/UpdateUser"
	// BackofficeAuthServiceFetchRoleProcedure is the fully-qualified name of the
	// BackofficeAuthService's FetchRole RPC.
	BackofficeAuthServiceFetchRoleProcedure = "/auth.v1.BackofficeAuthService/FetchRole"
	// BackofficeAuthServiceCreateRoleProcedure is the fully-qualified name of the
	// BackofficeAuthService's CreateRole RPC.
	BackofficeAuthServiceCreateRoleProcedure = "/auth.v1.BackofficeAuthService/CreateRole"
	// BackofficeAuthServiceUpdateRoleProcedure is the fully-qualified name of the
	// BackofficeAuthService's UpdateRole RPC.
	BackofficeAuthServiceUpdateRoleProcedure = "/auth.v1.BackofficeAuthService/UpdateRole"
	// BackofficeAuthServiceFetchServiceProcedure is the fully-qualified name of the
	// BackofficeAuthService's FetchService RPC.
	BackofficeAuthServiceFetchServiceProcedure = "/auth.v1.BackofficeAuthService/FetchService"
	// BackofficeAuthServiceCreateServiceProcedure is the fully-qualified name of the
	// BackofficeAuthService's CreateService RPC.
	BackofficeAuthServiceCreateServiceProcedure = "/auth.v1.BackofficeAuthService/CreateService"
	// BackofficeAuthServiceUpdateServiceProcedure is the fully-qualified name of the
	// BackofficeAuthService's UpdateService RPC.
	BackofficeAuthServiceUpdateServiceProcedure = "/auth.v1.BackofficeAuthService/UpdateService"
	// BackofficeAuthServiceFetchPathProcedure is the fully-qualified name of the
	// BackofficeAuthService's FetchPath RPC.
	BackofficeAuthServiceFetchPathProcedure = "/auth.v1.BackofficeAuthService/FetchPath"
	// BackofficeAuthServiceCreatePathProcedure is the fully-qualified name of the
	// BackofficeAuthService's CreatePath RPC.
	BackofficeAuthServiceCreatePathProcedure = "/auth.v1.BackofficeAuthService/CreatePath"
	// BackofficeAuthServiceUpdatePathProcedure is the fully-qualified name of the
	// BackofficeAuthService's UpdatePath RPC.
	BackofficeAuthServiceUpdatePathProcedure = "/auth.v1.BackofficeAuthService/UpdatePath"
	// BackofficeAuthServiceFetchPolicyProcedure is the fully-qualified name of the
	// BackofficeAuthService's FetchPolicy RPC.
	BackofficeAuthServiceFetchPolicyProcedure = "/auth.v1.BackofficeAuthService/FetchPolicy"
	// BackofficeAuthServiceCreatePolicyProcedure is the fully-qualified name of the
	// BackofficeAuthService's CreatePolicy RPC.
	BackofficeAuthServiceCreatePolicyProcedure = "/auth.v1.BackofficeAuthService/CreatePolicy"
	// BackofficeAuthServiceUpdatePolicyProcedure is the fully-qualified name of the
	// BackofficeAuthService's UpdatePolicy RPC.
	BackofficeAuthServiceUpdatePolicyProcedure = "/auth.v1.BackofficeAuthService/UpdatePolicy"
	// BackofficeAuthServiceInitTotpProcedure is the fully-qualified name of the BackofficeAuthService's
	// InitTotp RPC.
	BackofficeAuthServiceInitTotpProcedure = "/auth.v1.BackofficeAuthService/InitTotp"
	// BackofficeAuthServiceVerifyTotpProcedure is the fully-qualified name of the
	// BackofficeAuthService's VerifyTotp RPC.
	BackofficeAuthServiceVerifyTotpProcedure = "/auth.v1.BackofficeAuthService/VerifyTotp"
	// BackofficeAuthServiceRemoveTotpProcedure is the fully-qualified name of the
	// BackofficeAuthService's RemoveTotp RPC.
	BackofficeAuthServiceRemoveTotpProcedure = "/auth.v1.BackofficeAuthService/RemoveTotp"
	// BackofficeAuthServiceFetchConfigMailProcedure is the fully-qualified name of the
	// BackofficeAuthService's FetchConfigMail RPC.
	BackofficeAuthServiceFetchConfigMailProcedure = "/auth.v1.BackofficeAuthService/FetchConfigMail"
	// BackofficeAuthServiceCreateConfigMailProcedure is the fully-qualified name of the
	// BackofficeAuthService's CreateConfigMail RPC.
	BackofficeAuthServiceCreateConfigMailProcedure = "/auth.v1.BackofficeAuthService/CreateConfigMail"
	// BackofficeAuthServiceUpdateConfigMailProcedure is the fully-qualified name of the
	// BackofficeAuthService's UpdateConfigMail RPC.
	BackofficeAuthServiceUpdateConfigMailProcedure = "/auth.v1.BackofficeAuthService/UpdateConfigMail"
	// BackofficeAuthServiceFetchConfigTemplateEmailProcedure is the fully-qualified name of the
	// BackofficeAuthService's FetchConfigTemplateEmail RPC.
	BackofficeAuthServiceFetchConfigTemplateEmailProcedure = "/auth.v1.BackofficeAuthService/FetchConfigTemplateEmail"
	// BackofficeAuthServiceCreateConfigTemplateEmailProcedure is the fully-qualified name of the
	// BackofficeAuthService's CreateConfigTemplateEmail RPC.
	BackofficeAuthServiceCreateConfigTemplateEmailProcedure = "/auth.v1.BackofficeAuthService/CreateConfigTemplateEmail"
	// BackofficeAuthServiceUpdateConfigTemplateEmailProcedure is the fully-qualified name of the
	// BackofficeAuthService's UpdateConfigTemplateEmail RPC.
	BackofficeAuthServiceUpdateConfigTemplateEmailProcedure = "/auth.v1.BackofficeAuthService/UpdateConfigTemplateEmail"
	// BackofficeAuthServiceUpdateRefCodeProcedure is the fully-qualified name of the
	// BackofficeAuthService's UpdateRefCode RPC.
	BackofficeAuthServiceUpdateRefCodeProcedure = "/auth.v1.BackofficeAuthService/UpdateRefCode"
	// BackofficeAuthServiceFetchOAuthConfigProcedure is the fully-qualified name of the
	// BackofficeAuthService's FetchOAuthConfig RPC.
	BackofficeAuthServiceFetchOAuthConfigProcedure = "/auth.v1.BackofficeAuthService/FetchOAuthConfig"
	// BackofficeAuthServiceCreateOAuthConfigProcedure is the fully-qualified name of the
	// BackofficeAuthService's CreateOAuthConfig RPC.
	BackofficeAuthServiceCreateOAuthConfigProcedure = "/auth.v1.BackofficeAuthService/CreateOAuthConfig"
	// BackofficeAuthServiceUpdateOAuthConfigProcedure is the fully-qualified name of the
	// BackofficeAuthService's UpdateOAuthConfig RPC.
	BackofficeAuthServiceUpdateOAuthConfigProcedure = "/auth.v1.BackofficeAuthService/UpdateOAuthConfig"
	// BackofficeAuthServiceFetchCompanyProcedure is the fully-qualified name of the
	// BackofficeAuthService's FetchCompany RPC.
	BackofficeAuthServiceFetchCompanyProcedure = "/auth.v1.BackofficeAuthService/FetchCompany"
	// BackofficeAuthServiceCreateCompanyProcedure is the fully-qualified name of the
	// BackofficeAuthService's CreateCompany RPC.
	BackofficeAuthServiceCreateCompanyProcedure = "/auth.v1.BackofficeAuthService/CreateCompany"
	// BackofficeAuthServiceUpdateCompanyProcedure is the fully-qualified name of the
	// BackofficeAuthService's UpdateCompany RPC.
	BackofficeAuthServiceUpdateCompanyProcedure = "/auth.v1.BackofficeAuthService/UpdateCompany"
	// BackofficeAuthServiceUpdateProfileProcedure is the fully-qualified name of the
	// BackofficeAuthService's UpdateProfile RPC.
	BackofficeAuthServiceUpdateProfileProcedure = "/auth.v1.BackofficeAuthService/UpdateProfile"
)

// BackofficeAuthServiceClient is a client for the auth.v1.BackofficeAuthService service.
type BackofficeAuthServiceClient interface {
	Login(context.Context, *connect.Request[v1.BackofficeAuthServiceLoginRequest]) (*connect.Response[v1.BackofficeAuthServiceLoginResponse], error)
	Me(context.Context, *connect.Request[v1.BackofficeAuthServiceMeRequest]) (*connect.Response[v1.BackofficeAuthServiceMeResponse], error)
	ChangePassword(context.Context, *connect.Request[v1.BackofficeAuthServiceChangePasswordRequest]) (*connect.Response[v1.BackofficeAuthServiceChangePasswordResponse], error)
	RefreshToken(context.Context, *connect.Request[v1.BackofficeAuthServiceRefreshTokenRequest]) (*connect.Response[v1.BackofficeAuthServiceRefreshTokenResponse], error)
	ForgotPassword(context.Context, *connect.Request[v1.BackofficeAuthServiceForgotPasswordRequest]) (*connect.Response[v1.BackofficeAuthServiceForgotPasswordResponse], error)
	ReloadEnforcer(context.Context, *connect.Request[v1.BackofficeAuthServiceReloadEnforcerRequest]) (*connect.Response[v1.BackofficeAuthServiceReloadEnforcerResponse], error)
	CreateApp(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateAppRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateAppResponse], error)
	FetchApp(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchAppRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchAppResponse], error)
	UpdateApp(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateAppRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateAppResponse], error)
	FetchUser(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchUserRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchUserResponse], error)
	CreateUser(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateUserRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateUserResponse], error)
	UpdateUser(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateUserRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateUserResponse], error)
	FetchRole(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchRoleRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchRoleResponse], error)
	CreateRole(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateRoleRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateRoleResponse], error)
	UpdateRole(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateRoleRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateRoleResponse], error)
	FetchService(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchServiceRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchServiceResponse], error)
	CreateService(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateServiceRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateServiceResponse], error)
	UpdateService(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateServiceRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateServiceResponse], error)
	FetchPath(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchPathRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchPathResponse], error)
	CreatePath(context.Context, *connect.Request[v1.BackofficeAuthServiceCreatePathRequest]) (*connect.Response[v1.BackofficeAuthServiceCreatePathResponse], error)
	UpdatePath(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdatePathRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdatePathResponse], error)
	FetchPolicy(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchPolicyRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchPolicyResponse], error)
	CreatePolicy(context.Context, *connect.Request[v1.BackofficeAuthServiceCreatePolicyRequest]) (*connect.Response[v1.BackofficeAuthServiceCreatePolicyResponse], error)
	UpdatePolicy(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdatePolicyRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdatePolicyResponse], error)
	InitTotp(context.Context, *connect.Request[v1.BackofficeAuthServiceInitTotpRequest]) (*connect.Response[v1.BackofficeAuthServiceInitTotpResponse], error)
	VerifyTotp(context.Context, *connect.Request[v1.BackofficeAuthServiceVerifyTotpRequest]) (*connect.Response[v1.BackofficeAuthServiceVerifyTotpResponse], error)
	RemoveTotp(context.Context, *connect.Request[v1.BackofficeAuthServiceRemoveTotpRequest]) (*connect.Response[v1.BackofficeAuthServiceRemoveTotpResponse], error)
	FetchConfigMail(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchConfigMailRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchConfigMailResponse], error)
	CreateConfigMail(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateConfigMailRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateConfigMailResponse], error)
	UpdateConfigMail(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateConfigMailRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateConfigMailResponse], error)
	FetchConfigTemplateEmail(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchConfigTemplateEmailRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchConfigTemplateEmailResponse], error)
	CreateConfigTemplateEmail(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateConfigTemplateEmailRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateConfigTemplateEmailResponse], error)
	UpdateConfigTemplateEmail(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateConfigTemplateEmailRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateConfigTemplateEmailResponse], error)
	UpdateRefCode(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateRefCodeRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateRefCodeResponse], error)
	FetchOAuthConfig(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchOAuthConfigRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchOAuthConfigResponse], error)
	CreateOAuthConfig(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateOAuthConfigRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateOAuthConfigResponse], error)
	UpdateOAuthConfig(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateOAuthConfigRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateOAuthConfigResponse], error)
	FetchCompany(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchCompanyRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchCompanyResponse], error)
	CreateCompany(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateCompanyRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateCompanyResponse], error)
	UpdateCompany(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateCompanyRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateCompanyResponse], error)
	UpdateProfile(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateProfileRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateProfileResponse], error)
}

// NewBackofficeAuthServiceClient constructs a client for the auth.v1.BackofficeAuthService service.
// By default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped
// responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackofficeAuthServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackofficeAuthServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backofficeAuthServiceMethods := v1.File_auth_v1_backoffice_auth_proto.Services().ByName("BackofficeAuthService").Methods()
	return &backofficeAuthServiceClient{
		login: connect.NewClient[v1.BackofficeAuthServiceLoginRequest, v1.BackofficeAuthServiceLoginResponse](
			httpClient,
			baseURL+BackofficeAuthServiceLoginProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("Login")),
			connect.WithClientOptions(opts...),
		),
		me: connect.NewClient[v1.BackofficeAuthServiceMeRequest, v1.BackofficeAuthServiceMeResponse](
			httpClient,
			baseURL+BackofficeAuthServiceMeProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("Me")),
			connect.WithClientOptions(opts...),
		),
		changePassword: connect.NewClient[v1.BackofficeAuthServiceChangePasswordRequest, v1.BackofficeAuthServiceChangePasswordResponse](
			httpClient,
			baseURL+BackofficeAuthServiceChangePasswordProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("ChangePassword")),
			connect.WithClientOptions(opts...),
		),
		refreshToken: connect.NewClient[v1.BackofficeAuthServiceRefreshTokenRequest, v1.BackofficeAuthServiceRefreshTokenResponse](
			httpClient,
			baseURL+BackofficeAuthServiceRefreshTokenProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("RefreshToken")),
			connect.WithClientOptions(opts...),
		),
		forgotPassword: connect.NewClient[v1.BackofficeAuthServiceForgotPasswordRequest, v1.BackofficeAuthServiceForgotPasswordResponse](
			httpClient,
			baseURL+BackofficeAuthServiceForgotPasswordProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("ForgotPassword")),
			connect.WithClientOptions(opts...),
		),
		reloadEnforcer: connect.NewClient[v1.BackofficeAuthServiceReloadEnforcerRequest, v1.BackofficeAuthServiceReloadEnforcerResponse](
			httpClient,
			baseURL+BackofficeAuthServiceReloadEnforcerProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("ReloadEnforcer")),
			connect.WithClientOptions(opts...),
		),
		createApp: connect.NewClient[v1.BackofficeAuthServiceCreateAppRequest, v1.BackofficeAuthServiceCreateAppResponse](
			httpClient,
			baseURL+BackofficeAuthServiceCreateAppProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("CreateApp")),
			connect.WithClientOptions(opts...),
		),
		fetchApp: connect.NewClient[v1.BackofficeAuthServiceFetchAppRequest, v1.BackofficeAuthServiceFetchAppResponse](
			httpClient,
			baseURL+BackofficeAuthServiceFetchAppProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchApp")),
			connect.WithClientOptions(opts...),
		),
		updateApp: connect.NewClient[v1.BackofficeAuthServiceUpdateAppRequest, v1.BackofficeAuthServiceUpdateAppResponse](
			httpClient,
			baseURL+BackofficeAuthServiceUpdateAppProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateApp")),
			connect.WithClientOptions(opts...),
		),
		fetchUser: connect.NewClient[v1.BackofficeAuthServiceFetchUserRequest, v1.BackofficeAuthServiceFetchUserResponse](
			httpClient,
			baseURL+BackofficeAuthServiceFetchUserProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchUser")),
			connect.WithClientOptions(opts...),
		),
		createUser: connect.NewClient[v1.BackofficeAuthServiceCreateUserRequest, v1.BackofficeAuthServiceCreateUserResponse](
			httpClient,
			baseURL+BackofficeAuthServiceCreateUserProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("CreateUser")),
			connect.WithClientOptions(opts...),
		),
		updateUser: connect.NewClient[v1.BackofficeAuthServiceUpdateUserRequest, v1.BackofficeAuthServiceUpdateUserResponse](
			httpClient,
			baseURL+BackofficeAuthServiceUpdateUserProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateUser")),
			connect.WithClientOptions(opts...),
		),
		fetchRole: connect.NewClient[v1.BackofficeAuthServiceFetchRoleRequest, v1.BackofficeAuthServiceFetchRoleResponse](
			httpClient,
			baseURL+BackofficeAuthServiceFetchRoleProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchRole")),
			connect.WithClientOptions(opts...),
		),
		createRole: connect.NewClient[v1.BackofficeAuthServiceCreateRoleRequest, v1.BackofficeAuthServiceCreateRoleResponse](
			httpClient,
			baseURL+BackofficeAuthServiceCreateRoleProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("CreateRole")),
			connect.WithClientOptions(opts...),
		),
		updateRole: connect.NewClient[v1.BackofficeAuthServiceUpdateRoleRequest, v1.BackofficeAuthServiceUpdateRoleResponse](
			httpClient,
			baseURL+BackofficeAuthServiceUpdateRoleProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateRole")),
			connect.WithClientOptions(opts...),
		),
		fetchService: connect.NewClient[v1.BackofficeAuthServiceFetchServiceRequest, v1.BackofficeAuthServiceFetchServiceResponse](
			httpClient,
			baseURL+BackofficeAuthServiceFetchServiceProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchService")),
			connect.WithClientOptions(opts...),
		),
		createService: connect.NewClient[v1.BackofficeAuthServiceCreateServiceRequest, v1.BackofficeAuthServiceCreateServiceResponse](
			httpClient,
			baseURL+BackofficeAuthServiceCreateServiceProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("CreateService")),
			connect.WithClientOptions(opts...),
		),
		updateService: connect.NewClient[v1.BackofficeAuthServiceUpdateServiceRequest, v1.BackofficeAuthServiceUpdateServiceResponse](
			httpClient,
			baseURL+BackofficeAuthServiceUpdateServiceProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateService")),
			connect.WithClientOptions(opts...),
		),
		fetchPath: connect.NewClient[v1.BackofficeAuthServiceFetchPathRequest, v1.BackofficeAuthServiceFetchPathResponse](
			httpClient,
			baseURL+BackofficeAuthServiceFetchPathProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchPath")),
			connect.WithClientOptions(opts...),
		),
		createPath: connect.NewClient[v1.BackofficeAuthServiceCreatePathRequest, v1.BackofficeAuthServiceCreatePathResponse](
			httpClient,
			baseURL+BackofficeAuthServiceCreatePathProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("CreatePath")),
			connect.WithClientOptions(opts...),
		),
		updatePath: connect.NewClient[v1.BackofficeAuthServiceUpdatePathRequest, v1.BackofficeAuthServiceUpdatePathResponse](
			httpClient,
			baseURL+BackofficeAuthServiceUpdatePathProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdatePath")),
			connect.WithClientOptions(opts...),
		),
		fetchPolicy: connect.NewClient[v1.BackofficeAuthServiceFetchPolicyRequest, v1.BackofficeAuthServiceFetchPolicyResponse](
			httpClient,
			baseURL+BackofficeAuthServiceFetchPolicyProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchPolicy")),
			connect.WithClientOptions(opts...),
		),
		createPolicy: connect.NewClient[v1.BackofficeAuthServiceCreatePolicyRequest, v1.BackofficeAuthServiceCreatePolicyResponse](
			httpClient,
			baseURL+BackofficeAuthServiceCreatePolicyProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("CreatePolicy")),
			connect.WithClientOptions(opts...),
		),
		updatePolicy: connect.NewClient[v1.BackofficeAuthServiceUpdatePolicyRequest, v1.BackofficeAuthServiceUpdatePolicyResponse](
			httpClient,
			baseURL+BackofficeAuthServiceUpdatePolicyProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdatePolicy")),
			connect.WithClientOptions(opts...),
		),
		initTotp: connect.NewClient[v1.BackofficeAuthServiceInitTotpRequest, v1.BackofficeAuthServiceInitTotpResponse](
			httpClient,
			baseURL+BackofficeAuthServiceInitTotpProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("InitTotp")),
			connect.WithClientOptions(opts...),
		),
		verifyTotp: connect.NewClient[v1.BackofficeAuthServiceVerifyTotpRequest, v1.BackofficeAuthServiceVerifyTotpResponse](
			httpClient,
			baseURL+BackofficeAuthServiceVerifyTotpProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("VerifyTotp")),
			connect.WithClientOptions(opts...),
		),
		removeTotp: connect.NewClient[v1.BackofficeAuthServiceRemoveTotpRequest, v1.BackofficeAuthServiceRemoveTotpResponse](
			httpClient,
			baseURL+BackofficeAuthServiceRemoveTotpProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("RemoveTotp")),
			connect.WithClientOptions(opts...),
		),
		fetchConfigMail: connect.NewClient[v1.BackofficeAuthServiceFetchConfigMailRequest, v1.BackofficeAuthServiceFetchConfigMailResponse](
			httpClient,
			baseURL+BackofficeAuthServiceFetchConfigMailProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchConfigMail")),
			connect.WithClientOptions(opts...),
		),
		createConfigMail: connect.NewClient[v1.BackofficeAuthServiceCreateConfigMailRequest, v1.BackofficeAuthServiceCreateConfigMailResponse](
			httpClient,
			baseURL+BackofficeAuthServiceCreateConfigMailProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("CreateConfigMail")),
			connect.WithClientOptions(opts...),
		),
		updateConfigMail: connect.NewClient[v1.BackofficeAuthServiceUpdateConfigMailRequest, v1.BackofficeAuthServiceUpdateConfigMailResponse](
			httpClient,
			baseURL+BackofficeAuthServiceUpdateConfigMailProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateConfigMail")),
			connect.WithClientOptions(opts...),
		),
		fetchConfigTemplateEmail: connect.NewClient[v1.BackofficeAuthServiceFetchConfigTemplateEmailRequest, v1.BackofficeAuthServiceFetchConfigTemplateEmailResponse](
			httpClient,
			baseURL+BackofficeAuthServiceFetchConfigTemplateEmailProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchConfigTemplateEmail")),
			connect.WithClientOptions(opts...),
		),
		createConfigTemplateEmail: connect.NewClient[v1.BackofficeAuthServiceCreateConfigTemplateEmailRequest, v1.BackofficeAuthServiceCreateConfigTemplateEmailResponse](
			httpClient,
			baseURL+BackofficeAuthServiceCreateConfigTemplateEmailProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("CreateConfigTemplateEmail")),
			connect.WithClientOptions(opts...),
		),
		updateConfigTemplateEmail: connect.NewClient[v1.BackofficeAuthServiceUpdateConfigTemplateEmailRequest, v1.BackofficeAuthServiceUpdateConfigTemplateEmailResponse](
			httpClient,
			baseURL+BackofficeAuthServiceUpdateConfigTemplateEmailProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateConfigTemplateEmail")),
			connect.WithClientOptions(opts...),
		),
		updateRefCode: connect.NewClient[v1.BackofficeAuthServiceUpdateRefCodeRequest, v1.BackofficeAuthServiceUpdateRefCodeResponse](
			httpClient,
			baseURL+BackofficeAuthServiceUpdateRefCodeProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateRefCode")),
			connect.WithClientOptions(opts...),
		),
		fetchOAuthConfig: connect.NewClient[v1.BackofficeAuthServiceFetchOAuthConfigRequest, v1.BackofficeAuthServiceFetchOAuthConfigResponse](
			httpClient,
			baseURL+BackofficeAuthServiceFetchOAuthConfigProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchOAuthConfig")),
			connect.WithClientOptions(opts...),
		),
		createOAuthConfig: connect.NewClient[v1.BackofficeAuthServiceCreateOAuthConfigRequest, v1.BackofficeAuthServiceCreateOAuthConfigResponse](
			httpClient,
			baseURL+BackofficeAuthServiceCreateOAuthConfigProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("CreateOAuthConfig")),
			connect.WithClientOptions(opts...),
		),
		updateOAuthConfig: connect.NewClient[v1.BackofficeAuthServiceUpdateOAuthConfigRequest, v1.BackofficeAuthServiceUpdateOAuthConfigResponse](
			httpClient,
			baseURL+BackofficeAuthServiceUpdateOAuthConfigProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateOAuthConfig")),
			connect.WithClientOptions(opts...),
		),
		fetchCompany: connect.NewClient[v1.BackofficeAuthServiceFetchCompanyRequest, v1.BackofficeAuthServiceFetchCompanyResponse](
			httpClient,
			baseURL+BackofficeAuthServiceFetchCompanyProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchCompany")),
			connect.WithClientOptions(opts...),
		),
		createCompany: connect.NewClient[v1.BackofficeAuthServiceCreateCompanyRequest, v1.BackofficeAuthServiceCreateCompanyResponse](
			httpClient,
			baseURL+BackofficeAuthServiceCreateCompanyProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("CreateCompany")),
			connect.WithClientOptions(opts...),
		),
		updateCompany: connect.NewClient[v1.BackofficeAuthServiceUpdateCompanyRequest, v1.BackofficeAuthServiceUpdateCompanyResponse](
			httpClient,
			baseURL+BackofficeAuthServiceUpdateCompanyProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateCompany")),
			connect.WithClientOptions(opts...),
		),
		updateProfile: connect.NewClient[v1.BackofficeAuthServiceUpdateProfileRequest, v1.BackofficeAuthServiceUpdateProfileResponse](
			httpClient,
			baseURL+BackofficeAuthServiceUpdateProfileProcedure,
			connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateProfile")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backofficeAuthServiceClient implements BackofficeAuthServiceClient.
type backofficeAuthServiceClient struct {
	login                     *connect.Client[v1.BackofficeAuthServiceLoginRequest, v1.BackofficeAuthServiceLoginResponse]
	me                        *connect.Client[v1.BackofficeAuthServiceMeRequest, v1.BackofficeAuthServiceMeResponse]
	changePassword            *connect.Client[v1.BackofficeAuthServiceChangePasswordRequest, v1.BackofficeAuthServiceChangePasswordResponse]
	refreshToken              *connect.Client[v1.BackofficeAuthServiceRefreshTokenRequest, v1.BackofficeAuthServiceRefreshTokenResponse]
	forgotPassword            *connect.Client[v1.BackofficeAuthServiceForgotPasswordRequest, v1.BackofficeAuthServiceForgotPasswordResponse]
	reloadEnforcer            *connect.Client[v1.BackofficeAuthServiceReloadEnforcerRequest, v1.BackofficeAuthServiceReloadEnforcerResponse]
	createApp                 *connect.Client[v1.BackofficeAuthServiceCreateAppRequest, v1.BackofficeAuthServiceCreateAppResponse]
	fetchApp                  *connect.Client[v1.BackofficeAuthServiceFetchAppRequest, v1.BackofficeAuthServiceFetchAppResponse]
	updateApp                 *connect.Client[v1.BackofficeAuthServiceUpdateAppRequest, v1.BackofficeAuthServiceUpdateAppResponse]
	fetchUser                 *connect.Client[v1.BackofficeAuthServiceFetchUserRequest, v1.BackofficeAuthServiceFetchUserResponse]
	createUser                *connect.Client[v1.BackofficeAuthServiceCreateUserRequest, v1.BackofficeAuthServiceCreateUserResponse]
	updateUser                *connect.Client[v1.BackofficeAuthServiceUpdateUserRequest, v1.BackofficeAuthServiceUpdateUserResponse]
	fetchRole                 *connect.Client[v1.BackofficeAuthServiceFetchRoleRequest, v1.BackofficeAuthServiceFetchRoleResponse]
	createRole                *connect.Client[v1.BackofficeAuthServiceCreateRoleRequest, v1.BackofficeAuthServiceCreateRoleResponse]
	updateRole                *connect.Client[v1.BackofficeAuthServiceUpdateRoleRequest, v1.BackofficeAuthServiceUpdateRoleResponse]
	fetchService              *connect.Client[v1.BackofficeAuthServiceFetchServiceRequest, v1.BackofficeAuthServiceFetchServiceResponse]
	createService             *connect.Client[v1.BackofficeAuthServiceCreateServiceRequest, v1.BackofficeAuthServiceCreateServiceResponse]
	updateService             *connect.Client[v1.BackofficeAuthServiceUpdateServiceRequest, v1.BackofficeAuthServiceUpdateServiceResponse]
	fetchPath                 *connect.Client[v1.BackofficeAuthServiceFetchPathRequest, v1.BackofficeAuthServiceFetchPathResponse]
	createPath                *connect.Client[v1.BackofficeAuthServiceCreatePathRequest, v1.BackofficeAuthServiceCreatePathResponse]
	updatePath                *connect.Client[v1.BackofficeAuthServiceUpdatePathRequest, v1.BackofficeAuthServiceUpdatePathResponse]
	fetchPolicy               *connect.Client[v1.BackofficeAuthServiceFetchPolicyRequest, v1.BackofficeAuthServiceFetchPolicyResponse]
	createPolicy              *connect.Client[v1.BackofficeAuthServiceCreatePolicyRequest, v1.BackofficeAuthServiceCreatePolicyResponse]
	updatePolicy              *connect.Client[v1.BackofficeAuthServiceUpdatePolicyRequest, v1.BackofficeAuthServiceUpdatePolicyResponse]
	initTotp                  *connect.Client[v1.BackofficeAuthServiceInitTotpRequest, v1.BackofficeAuthServiceInitTotpResponse]
	verifyTotp                *connect.Client[v1.BackofficeAuthServiceVerifyTotpRequest, v1.BackofficeAuthServiceVerifyTotpResponse]
	removeTotp                *connect.Client[v1.BackofficeAuthServiceRemoveTotpRequest, v1.BackofficeAuthServiceRemoveTotpResponse]
	fetchConfigMail           *connect.Client[v1.BackofficeAuthServiceFetchConfigMailRequest, v1.BackofficeAuthServiceFetchConfigMailResponse]
	createConfigMail          *connect.Client[v1.BackofficeAuthServiceCreateConfigMailRequest, v1.BackofficeAuthServiceCreateConfigMailResponse]
	updateConfigMail          *connect.Client[v1.BackofficeAuthServiceUpdateConfigMailRequest, v1.BackofficeAuthServiceUpdateConfigMailResponse]
	fetchConfigTemplateEmail  *connect.Client[v1.BackofficeAuthServiceFetchConfigTemplateEmailRequest, v1.BackofficeAuthServiceFetchConfigTemplateEmailResponse]
	createConfigTemplateEmail *connect.Client[v1.BackofficeAuthServiceCreateConfigTemplateEmailRequest, v1.BackofficeAuthServiceCreateConfigTemplateEmailResponse]
	updateConfigTemplateEmail *connect.Client[v1.BackofficeAuthServiceUpdateConfigTemplateEmailRequest, v1.BackofficeAuthServiceUpdateConfigTemplateEmailResponse]
	updateRefCode             *connect.Client[v1.BackofficeAuthServiceUpdateRefCodeRequest, v1.BackofficeAuthServiceUpdateRefCodeResponse]
	fetchOAuthConfig          *connect.Client[v1.BackofficeAuthServiceFetchOAuthConfigRequest, v1.BackofficeAuthServiceFetchOAuthConfigResponse]
	createOAuthConfig         *connect.Client[v1.BackofficeAuthServiceCreateOAuthConfigRequest, v1.BackofficeAuthServiceCreateOAuthConfigResponse]
	updateOAuthConfig         *connect.Client[v1.BackofficeAuthServiceUpdateOAuthConfigRequest, v1.BackofficeAuthServiceUpdateOAuthConfigResponse]
	fetchCompany              *connect.Client[v1.BackofficeAuthServiceFetchCompanyRequest, v1.BackofficeAuthServiceFetchCompanyResponse]
	createCompany             *connect.Client[v1.BackofficeAuthServiceCreateCompanyRequest, v1.BackofficeAuthServiceCreateCompanyResponse]
	updateCompany             *connect.Client[v1.BackofficeAuthServiceUpdateCompanyRequest, v1.BackofficeAuthServiceUpdateCompanyResponse]
	updateProfile             *connect.Client[v1.BackofficeAuthServiceUpdateProfileRequest, v1.BackofficeAuthServiceUpdateProfileResponse]
}

// Login calls auth.v1.BackofficeAuthService.Login.
func (c *backofficeAuthServiceClient) Login(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceLoginRequest]) (*connect.Response[v1.BackofficeAuthServiceLoginResponse], error) {
	return c.login.CallUnary(ctx, req)
}

// Me calls auth.v1.BackofficeAuthService.Me.
func (c *backofficeAuthServiceClient) Me(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceMeRequest]) (*connect.Response[v1.BackofficeAuthServiceMeResponse], error) {
	return c.me.CallUnary(ctx, req)
}

// ChangePassword calls auth.v1.BackofficeAuthService.ChangePassword.
func (c *backofficeAuthServiceClient) ChangePassword(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceChangePasswordRequest]) (*connect.Response[v1.BackofficeAuthServiceChangePasswordResponse], error) {
	return c.changePassword.CallUnary(ctx, req)
}

// RefreshToken calls auth.v1.BackofficeAuthService.RefreshToken.
func (c *backofficeAuthServiceClient) RefreshToken(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceRefreshTokenRequest]) (*connect.Response[v1.BackofficeAuthServiceRefreshTokenResponse], error) {
	return c.refreshToken.CallUnary(ctx, req)
}

// ForgotPassword calls auth.v1.BackofficeAuthService.ForgotPassword.
func (c *backofficeAuthServiceClient) ForgotPassword(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceForgotPasswordRequest]) (*connect.Response[v1.BackofficeAuthServiceForgotPasswordResponse], error) {
	return c.forgotPassword.CallUnary(ctx, req)
}

// ReloadEnforcer calls auth.v1.BackofficeAuthService.ReloadEnforcer.
func (c *backofficeAuthServiceClient) ReloadEnforcer(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceReloadEnforcerRequest]) (*connect.Response[v1.BackofficeAuthServiceReloadEnforcerResponse], error) {
	return c.reloadEnforcer.CallUnary(ctx, req)
}

// CreateApp calls auth.v1.BackofficeAuthService.CreateApp.
func (c *backofficeAuthServiceClient) CreateApp(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceCreateAppRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateAppResponse], error) {
	return c.createApp.CallUnary(ctx, req)
}

// FetchApp calls auth.v1.BackofficeAuthService.FetchApp.
func (c *backofficeAuthServiceClient) FetchApp(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceFetchAppRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchAppResponse], error) {
	return c.fetchApp.CallUnary(ctx, req)
}

// UpdateApp calls auth.v1.BackofficeAuthService.UpdateApp.
func (c *backofficeAuthServiceClient) UpdateApp(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceUpdateAppRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateAppResponse], error) {
	return c.updateApp.CallUnary(ctx, req)
}

// FetchUser calls auth.v1.BackofficeAuthService.FetchUser.
func (c *backofficeAuthServiceClient) FetchUser(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceFetchUserRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchUserResponse], error) {
	return c.fetchUser.CallUnary(ctx, req)
}

// CreateUser calls auth.v1.BackofficeAuthService.CreateUser.
func (c *backofficeAuthServiceClient) CreateUser(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceCreateUserRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateUserResponse], error) {
	return c.createUser.CallUnary(ctx, req)
}

// UpdateUser calls auth.v1.BackofficeAuthService.UpdateUser.
func (c *backofficeAuthServiceClient) UpdateUser(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceUpdateUserRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateUserResponse], error) {
	return c.updateUser.CallUnary(ctx, req)
}

// FetchRole calls auth.v1.BackofficeAuthService.FetchRole.
func (c *backofficeAuthServiceClient) FetchRole(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceFetchRoleRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchRoleResponse], error) {
	return c.fetchRole.CallUnary(ctx, req)
}

// CreateRole calls auth.v1.BackofficeAuthService.CreateRole.
func (c *backofficeAuthServiceClient) CreateRole(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceCreateRoleRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateRoleResponse], error) {
	return c.createRole.CallUnary(ctx, req)
}

// UpdateRole calls auth.v1.BackofficeAuthService.UpdateRole.
func (c *backofficeAuthServiceClient) UpdateRole(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceUpdateRoleRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateRoleResponse], error) {
	return c.updateRole.CallUnary(ctx, req)
}

// FetchService calls auth.v1.BackofficeAuthService.FetchService.
func (c *backofficeAuthServiceClient) FetchService(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceFetchServiceRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchServiceResponse], error) {
	return c.fetchService.CallUnary(ctx, req)
}

// CreateService calls auth.v1.BackofficeAuthService.CreateService.
func (c *backofficeAuthServiceClient) CreateService(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceCreateServiceRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateServiceResponse], error) {
	return c.createService.CallUnary(ctx, req)
}

// UpdateService calls auth.v1.BackofficeAuthService.UpdateService.
func (c *backofficeAuthServiceClient) UpdateService(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceUpdateServiceRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateServiceResponse], error) {
	return c.updateService.CallUnary(ctx, req)
}

// FetchPath calls auth.v1.BackofficeAuthService.FetchPath.
func (c *backofficeAuthServiceClient) FetchPath(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceFetchPathRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchPathResponse], error) {
	return c.fetchPath.CallUnary(ctx, req)
}

// CreatePath calls auth.v1.BackofficeAuthService.CreatePath.
func (c *backofficeAuthServiceClient) CreatePath(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceCreatePathRequest]) (*connect.Response[v1.BackofficeAuthServiceCreatePathResponse], error) {
	return c.createPath.CallUnary(ctx, req)
}

// UpdatePath calls auth.v1.BackofficeAuthService.UpdatePath.
func (c *backofficeAuthServiceClient) UpdatePath(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceUpdatePathRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdatePathResponse], error) {
	return c.updatePath.CallUnary(ctx, req)
}

// FetchPolicy calls auth.v1.BackofficeAuthService.FetchPolicy.
func (c *backofficeAuthServiceClient) FetchPolicy(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceFetchPolicyRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchPolicyResponse], error) {
	return c.fetchPolicy.CallUnary(ctx, req)
}

// CreatePolicy calls auth.v1.BackofficeAuthService.CreatePolicy.
func (c *backofficeAuthServiceClient) CreatePolicy(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceCreatePolicyRequest]) (*connect.Response[v1.BackofficeAuthServiceCreatePolicyResponse], error) {
	return c.createPolicy.CallUnary(ctx, req)
}

// UpdatePolicy calls auth.v1.BackofficeAuthService.UpdatePolicy.
func (c *backofficeAuthServiceClient) UpdatePolicy(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceUpdatePolicyRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdatePolicyResponse], error) {
	return c.updatePolicy.CallUnary(ctx, req)
}

// InitTotp calls auth.v1.BackofficeAuthService.InitTotp.
func (c *backofficeAuthServiceClient) InitTotp(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceInitTotpRequest]) (*connect.Response[v1.BackofficeAuthServiceInitTotpResponse], error) {
	return c.initTotp.CallUnary(ctx, req)
}

// VerifyTotp calls auth.v1.BackofficeAuthService.VerifyTotp.
func (c *backofficeAuthServiceClient) VerifyTotp(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceVerifyTotpRequest]) (*connect.Response[v1.BackofficeAuthServiceVerifyTotpResponse], error) {
	return c.verifyTotp.CallUnary(ctx, req)
}

// RemoveTotp calls auth.v1.BackofficeAuthService.RemoveTotp.
func (c *backofficeAuthServiceClient) RemoveTotp(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceRemoveTotpRequest]) (*connect.Response[v1.BackofficeAuthServiceRemoveTotpResponse], error) {
	return c.removeTotp.CallUnary(ctx, req)
}

// FetchConfigMail calls auth.v1.BackofficeAuthService.FetchConfigMail.
func (c *backofficeAuthServiceClient) FetchConfigMail(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceFetchConfigMailRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchConfigMailResponse], error) {
	return c.fetchConfigMail.CallUnary(ctx, req)
}

// CreateConfigMail calls auth.v1.BackofficeAuthService.CreateConfigMail.
func (c *backofficeAuthServiceClient) CreateConfigMail(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceCreateConfigMailRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateConfigMailResponse], error) {
	return c.createConfigMail.CallUnary(ctx, req)
}

// UpdateConfigMail calls auth.v1.BackofficeAuthService.UpdateConfigMail.
func (c *backofficeAuthServiceClient) UpdateConfigMail(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceUpdateConfigMailRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateConfigMailResponse], error) {
	return c.updateConfigMail.CallUnary(ctx, req)
}

// FetchConfigTemplateEmail calls auth.v1.BackofficeAuthService.FetchConfigTemplateEmail.
func (c *backofficeAuthServiceClient) FetchConfigTemplateEmail(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceFetchConfigTemplateEmailRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchConfigTemplateEmailResponse], error) {
	return c.fetchConfigTemplateEmail.CallUnary(ctx, req)
}

// CreateConfigTemplateEmail calls auth.v1.BackofficeAuthService.CreateConfigTemplateEmail.
func (c *backofficeAuthServiceClient) CreateConfigTemplateEmail(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceCreateConfigTemplateEmailRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateConfigTemplateEmailResponse], error) {
	return c.createConfigTemplateEmail.CallUnary(ctx, req)
}

// UpdateConfigTemplateEmail calls auth.v1.BackofficeAuthService.UpdateConfigTemplateEmail.
func (c *backofficeAuthServiceClient) UpdateConfigTemplateEmail(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceUpdateConfigTemplateEmailRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateConfigTemplateEmailResponse], error) {
	return c.updateConfigTemplateEmail.CallUnary(ctx, req)
}

// UpdateRefCode calls auth.v1.BackofficeAuthService.UpdateRefCode.
func (c *backofficeAuthServiceClient) UpdateRefCode(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceUpdateRefCodeRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateRefCodeResponse], error) {
	return c.updateRefCode.CallUnary(ctx, req)
}

// FetchOAuthConfig calls auth.v1.BackofficeAuthService.FetchOAuthConfig.
func (c *backofficeAuthServiceClient) FetchOAuthConfig(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceFetchOAuthConfigRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchOAuthConfigResponse], error) {
	return c.fetchOAuthConfig.CallUnary(ctx, req)
}

// CreateOAuthConfig calls auth.v1.BackofficeAuthService.CreateOAuthConfig.
func (c *backofficeAuthServiceClient) CreateOAuthConfig(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceCreateOAuthConfigRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateOAuthConfigResponse], error) {
	return c.createOAuthConfig.CallUnary(ctx, req)
}

// UpdateOAuthConfig calls auth.v1.BackofficeAuthService.UpdateOAuthConfig.
func (c *backofficeAuthServiceClient) UpdateOAuthConfig(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceUpdateOAuthConfigRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateOAuthConfigResponse], error) {
	return c.updateOAuthConfig.CallUnary(ctx, req)
}

// FetchCompany calls auth.v1.BackofficeAuthService.FetchCompany.
func (c *backofficeAuthServiceClient) FetchCompany(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceFetchCompanyRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchCompanyResponse], error) {
	return c.fetchCompany.CallUnary(ctx, req)
}

// CreateCompany calls auth.v1.BackofficeAuthService.CreateCompany.
func (c *backofficeAuthServiceClient) CreateCompany(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceCreateCompanyRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateCompanyResponse], error) {
	return c.createCompany.CallUnary(ctx, req)
}

// UpdateCompany calls auth.v1.BackofficeAuthService.UpdateCompany.
func (c *backofficeAuthServiceClient) UpdateCompany(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceUpdateCompanyRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateCompanyResponse], error) {
	return c.updateCompany.CallUnary(ctx, req)
}

// UpdateProfile calls auth.v1.BackofficeAuthService.UpdateProfile.
func (c *backofficeAuthServiceClient) UpdateProfile(ctx context.Context, req *connect.Request[v1.BackofficeAuthServiceUpdateProfileRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateProfileResponse], error) {
	return c.updateProfile.CallUnary(ctx, req)
}

// BackofficeAuthServiceHandler is an implementation of the auth.v1.BackofficeAuthService service.
type BackofficeAuthServiceHandler interface {
	Login(context.Context, *connect.Request[v1.BackofficeAuthServiceLoginRequest]) (*connect.Response[v1.BackofficeAuthServiceLoginResponse], error)
	Me(context.Context, *connect.Request[v1.BackofficeAuthServiceMeRequest]) (*connect.Response[v1.BackofficeAuthServiceMeResponse], error)
	ChangePassword(context.Context, *connect.Request[v1.BackofficeAuthServiceChangePasswordRequest]) (*connect.Response[v1.BackofficeAuthServiceChangePasswordResponse], error)
	RefreshToken(context.Context, *connect.Request[v1.BackofficeAuthServiceRefreshTokenRequest]) (*connect.Response[v1.BackofficeAuthServiceRefreshTokenResponse], error)
	ForgotPassword(context.Context, *connect.Request[v1.BackofficeAuthServiceForgotPasswordRequest]) (*connect.Response[v1.BackofficeAuthServiceForgotPasswordResponse], error)
	ReloadEnforcer(context.Context, *connect.Request[v1.BackofficeAuthServiceReloadEnforcerRequest]) (*connect.Response[v1.BackofficeAuthServiceReloadEnforcerResponse], error)
	CreateApp(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateAppRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateAppResponse], error)
	FetchApp(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchAppRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchAppResponse], error)
	UpdateApp(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateAppRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateAppResponse], error)
	FetchUser(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchUserRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchUserResponse], error)
	CreateUser(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateUserRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateUserResponse], error)
	UpdateUser(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateUserRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateUserResponse], error)
	FetchRole(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchRoleRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchRoleResponse], error)
	CreateRole(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateRoleRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateRoleResponse], error)
	UpdateRole(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateRoleRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateRoleResponse], error)
	FetchService(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchServiceRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchServiceResponse], error)
	CreateService(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateServiceRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateServiceResponse], error)
	UpdateService(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateServiceRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateServiceResponse], error)
	FetchPath(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchPathRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchPathResponse], error)
	CreatePath(context.Context, *connect.Request[v1.BackofficeAuthServiceCreatePathRequest]) (*connect.Response[v1.BackofficeAuthServiceCreatePathResponse], error)
	UpdatePath(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdatePathRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdatePathResponse], error)
	FetchPolicy(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchPolicyRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchPolicyResponse], error)
	CreatePolicy(context.Context, *connect.Request[v1.BackofficeAuthServiceCreatePolicyRequest]) (*connect.Response[v1.BackofficeAuthServiceCreatePolicyResponse], error)
	UpdatePolicy(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdatePolicyRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdatePolicyResponse], error)
	InitTotp(context.Context, *connect.Request[v1.BackofficeAuthServiceInitTotpRequest]) (*connect.Response[v1.BackofficeAuthServiceInitTotpResponse], error)
	VerifyTotp(context.Context, *connect.Request[v1.BackofficeAuthServiceVerifyTotpRequest]) (*connect.Response[v1.BackofficeAuthServiceVerifyTotpResponse], error)
	RemoveTotp(context.Context, *connect.Request[v1.BackofficeAuthServiceRemoveTotpRequest]) (*connect.Response[v1.BackofficeAuthServiceRemoveTotpResponse], error)
	FetchConfigMail(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchConfigMailRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchConfigMailResponse], error)
	CreateConfigMail(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateConfigMailRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateConfigMailResponse], error)
	UpdateConfigMail(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateConfigMailRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateConfigMailResponse], error)
	FetchConfigTemplateEmail(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchConfigTemplateEmailRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchConfigTemplateEmailResponse], error)
	CreateConfigTemplateEmail(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateConfigTemplateEmailRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateConfigTemplateEmailResponse], error)
	UpdateConfigTemplateEmail(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateConfigTemplateEmailRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateConfigTemplateEmailResponse], error)
	UpdateRefCode(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateRefCodeRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateRefCodeResponse], error)
	FetchOAuthConfig(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchOAuthConfigRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchOAuthConfigResponse], error)
	CreateOAuthConfig(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateOAuthConfigRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateOAuthConfigResponse], error)
	UpdateOAuthConfig(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateOAuthConfigRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateOAuthConfigResponse], error)
	FetchCompany(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchCompanyRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchCompanyResponse], error)
	CreateCompany(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateCompanyRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateCompanyResponse], error)
	UpdateCompany(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateCompanyRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateCompanyResponse], error)
	UpdateProfile(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateProfileRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateProfileResponse], error)
}

// NewBackofficeAuthServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackofficeAuthServiceHandler(svc BackofficeAuthServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backofficeAuthServiceMethods := v1.File_auth_v1_backoffice_auth_proto.Services().ByName("BackofficeAuthService").Methods()
	backofficeAuthServiceLoginHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceLoginProcedure,
		svc.Login,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("Login")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceMeHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceMeProcedure,
		svc.Me,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("Me")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceChangePasswordHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceChangePasswordProcedure,
		svc.ChangePassword,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("ChangePassword")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceRefreshTokenHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceRefreshTokenProcedure,
		svc.RefreshToken,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("RefreshToken")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceForgotPasswordHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceForgotPasswordProcedure,
		svc.ForgotPassword,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("ForgotPassword")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceReloadEnforcerHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceReloadEnforcerProcedure,
		svc.ReloadEnforcer,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("ReloadEnforcer")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceCreateAppHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceCreateAppProcedure,
		svc.CreateApp,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("CreateApp")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceFetchAppHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceFetchAppProcedure,
		svc.FetchApp,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchApp")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceUpdateAppHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceUpdateAppProcedure,
		svc.UpdateApp,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateApp")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceFetchUserHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceFetchUserProcedure,
		svc.FetchUser,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchUser")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceCreateUserHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceCreateUserProcedure,
		svc.CreateUser,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("CreateUser")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceUpdateUserHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceUpdateUserProcedure,
		svc.UpdateUser,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateUser")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceFetchRoleHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceFetchRoleProcedure,
		svc.FetchRole,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchRole")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceCreateRoleHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceCreateRoleProcedure,
		svc.CreateRole,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("CreateRole")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceUpdateRoleHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceUpdateRoleProcedure,
		svc.UpdateRole,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateRole")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceFetchServiceHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceFetchServiceProcedure,
		svc.FetchService,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchService")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceCreateServiceHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceCreateServiceProcedure,
		svc.CreateService,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("CreateService")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceUpdateServiceHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceUpdateServiceProcedure,
		svc.UpdateService,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateService")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceFetchPathHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceFetchPathProcedure,
		svc.FetchPath,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchPath")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceCreatePathHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceCreatePathProcedure,
		svc.CreatePath,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("CreatePath")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceUpdatePathHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceUpdatePathProcedure,
		svc.UpdatePath,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdatePath")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceFetchPolicyHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceFetchPolicyProcedure,
		svc.FetchPolicy,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchPolicy")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceCreatePolicyHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceCreatePolicyProcedure,
		svc.CreatePolicy,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("CreatePolicy")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceUpdatePolicyHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceUpdatePolicyProcedure,
		svc.UpdatePolicy,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdatePolicy")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceInitTotpHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceInitTotpProcedure,
		svc.InitTotp,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("InitTotp")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceVerifyTotpHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceVerifyTotpProcedure,
		svc.VerifyTotp,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("VerifyTotp")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceRemoveTotpHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceRemoveTotpProcedure,
		svc.RemoveTotp,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("RemoveTotp")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceFetchConfigMailHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceFetchConfigMailProcedure,
		svc.FetchConfigMail,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchConfigMail")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceCreateConfigMailHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceCreateConfigMailProcedure,
		svc.CreateConfigMail,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("CreateConfigMail")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceUpdateConfigMailHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceUpdateConfigMailProcedure,
		svc.UpdateConfigMail,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateConfigMail")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceFetchConfigTemplateEmailHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceFetchConfigTemplateEmailProcedure,
		svc.FetchConfigTemplateEmail,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchConfigTemplateEmail")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceCreateConfigTemplateEmailHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceCreateConfigTemplateEmailProcedure,
		svc.CreateConfigTemplateEmail,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("CreateConfigTemplateEmail")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceUpdateConfigTemplateEmailHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceUpdateConfigTemplateEmailProcedure,
		svc.UpdateConfigTemplateEmail,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateConfigTemplateEmail")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceUpdateRefCodeHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceUpdateRefCodeProcedure,
		svc.UpdateRefCode,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateRefCode")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceFetchOAuthConfigHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceFetchOAuthConfigProcedure,
		svc.FetchOAuthConfig,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchOAuthConfig")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceCreateOAuthConfigHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceCreateOAuthConfigProcedure,
		svc.CreateOAuthConfig,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("CreateOAuthConfig")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceUpdateOAuthConfigHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceUpdateOAuthConfigProcedure,
		svc.UpdateOAuthConfig,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateOAuthConfig")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceFetchCompanyHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceFetchCompanyProcedure,
		svc.FetchCompany,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("FetchCompany")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceCreateCompanyHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceCreateCompanyProcedure,
		svc.CreateCompany,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("CreateCompany")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceUpdateCompanyHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceUpdateCompanyProcedure,
		svc.UpdateCompany,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateCompany")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeAuthServiceUpdateProfileHandler := connect.NewUnaryHandler(
		BackofficeAuthServiceUpdateProfileProcedure,
		svc.UpdateProfile,
		connect.WithSchema(backofficeAuthServiceMethods.ByName("UpdateProfile")),
		connect.WithHandlerOptions(opts...),
	)
	return "/auth.v1.BackofficeAuthService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackofficeAuthServiceLoginProcedure:
			backofficeAuthServiceLoginHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceMeProcedure:
			backofficeAuthServiceMeHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceChangePasswordProcedure:
			backofficeAuthServiceChangePasswordHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceRefreshTokenProcedure:
			backofficeAuthServiceRefreshTokenHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceForgotPasswordProcedure:
			backofficeAuthServiceForgotPasswordHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceReloadEnforcerProcedure:
			backofficeAuthServiceReloadEnforcerHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceCreateAppProcedure:
			backofficeAuthServiceCreateAppHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceFetchAppProcedure:
			backofficeAuthServiceFetchAppHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceUpdateAppProcedure:
			backofficeAuthServiceUpdateAppHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceFetchUserProcedure:
			backofficeAuthServiceFetchUserHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceCreateUserProcedure:
			backofficeAuthServiceCreateUserHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceUpdateUserProcedure:
			backofficeAuthServiceUpdateUserHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceFetchRoleProcedure:
			backofficeAuthServiceFetchRoleHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceCreateRoleProcedure:
			backofficeAuthServiceCreateRoleHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceUpdateRoleProcedure:
			backofficeAuthServiceUpdateRoleHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceFetchServiceProcedure:
			backofficeAuthServiceFetchServiceHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceCreateServiceProcedure:
			backofficeAuthServiceCreateServiceHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceUpdateServiceProcedure:
			backofficeAuthServiceUpdateServiceHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceFetchPathProcedure:
			backofficeAuthServiceFetchPathHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceCreatePathProcedure:
			backofficeAuthServiceCreatePathHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceUpdatePathProcedure:
			backofficeAuthServiceUpdatePathHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceFetchPolicyProcedure:
			backofficeAuthServiceFetchPolicyHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceCreatePolicyProcedure:
			backofficeAuthServiceCreatePolicyHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceUpdatePolicyProcedure:
			backofficeAuthServiceUpdatePolicyHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceInitTotpProcedure:
			backofficeAuthServiceInitTotpHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceVerifyTotpProcedure:
			backofficeAuthServiceVerifyTotpHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceRemoveTotpProcedure:
			backofficeAuthServiceRemoveTotpHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceFetchConfigMailProcedure:
			backofficeAuthServiceFetchConfigMailHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceCreateConfigMailProcedure:
			backofficeAuthServiceCreateConfigMailHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceUpdateConfigMailProcedure:
			backofficeAuthServiceUpdateConfigMailHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceFetchConfigTemplateEmailProcedure:
			backofficeAuthServiceFetchConfigTemplateEmailHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceCreateConfigTemplateEmailProcedure:
			backofficeAuthServiceCreateConfigTemplateEmailHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceUpdateConfigTemplateEmailProcedure:
			backofficeAuthServiceUpdateConfigTemplateEmailHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceUpdateRefCodeProcedure:
			backofficeAuthServiceUpdateRefCodeHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceFetchOAuthConfigProcedure:
			backofficeAuthServiceFetchOAuthConfigHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceCreateOAuthConfigProcedure:
			backofficeAuthServiceCreateOAuthConfigHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceUpdateOAuthConfigProcedure:
			backofficeAuthServiceUpdateOAuthConfigHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceFetchCompanyProcedure:
			backofficeAuthServiceFetchCompanyHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceCreateCompanyProcedure:
			backofficeAuthServiceCreateCompanyHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceUpdateCompanyProcedure:
			backofficeAuthServiceUpdateCompanyHandler.ServeHTTP(w, r)
		case BackofficeAuthServiceUpdateProfileProcedure:
			backofficeAuthServiceUpdateProfileHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackofficeAuthServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackofficeAuthServiceHandler struct{}

func (UnimplementedBackofficeAuthServiceHandler) Login(context.Context, *connect.Request[v1.BackofficeAuthServiceLoginRequest]) (*connect.Response[v1.BackofficeAuthServiceLoginResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.Login is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) Me(context.Context, *connect.Request[v1.BackofficeAuthServiceMeRequest]) (*connect.Response[v1.BackofficeAuthServiceMeResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.Me is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) ChangePassword(context.Context, *connect.Request[v1.BackofficeAuthServiceChangePasswordRequest]) (*connect.Response[v1.BackofficeAuthServiceChangePasswordResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.ChangePassword is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) RefreshToken(context.Context, *connect.Request[v1.BackofficeAuthServiceRefreshTokenRequest]) (*connect.Response[v1.BackofficeAuthServiceRefreshTokenResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.RefreshToken is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) ForgotPassword(context.Context, *connect.Request[v1.BackofficeAuthServiceForgotPasswordRequest]) (*connect.Response[v1.BackofficeAuthServiceForgotPasswordResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.ForgotPassword is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) ReloadEnforcer(context.Context, *connect.Request[v1.BackofficeAuthServiceReloadEnforcerRequest]) (*connect.Response[v1.BackofficeAuthServiceReloadEnforcerResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.ReloadEnforcer is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) CreateApp(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateAppRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateAppResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.CreateApp is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) FetchApp(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchAppRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchAppResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.FetchApp is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) UpdateApp(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateAppRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateAppResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.UpdateApp is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) FetchUser(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchUserRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchUserResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.FetchUser is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) CreateUser(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateUserRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateUserResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.CreateUser is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) UpdateUser(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateUserRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateUserResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.UpdateUser is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) FetchRole(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchRoleRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchRoleResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.FetchRole is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) CreateRole(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateRoleRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateRoleResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.CreateRole is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) UpdateRole(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateRoleRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateRoleResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.UpdateRole is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) FetchService(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchServiceRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchServiceResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.FetchService is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) CreateService(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateServiceRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateServiceResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.CreateService is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) UpdateService(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateServiceRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateServiceResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.UpdateService is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) FetchPath(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchPathRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchPathResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.FetchPath is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) CreatePath(context.Context, *connect.Request[v1.BackofficeAuthServiceCreatePathRequest]) (*connect.Response[v1.BackofficeAuthServiceCreatePathResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.CreatePath is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) UpdatePath(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdatePathRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdatePathResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.UpdatePath is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) FetchPolicy(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchPolicyRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchPolicyResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.FetchPolicy is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) CreatePolicy(context.Context, *connect.Request[v1.BackofficeAuthServiceCreatePolicyRequest]) (*connect.Response[v1.BackofficeAuthServiceCreatePolicyResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.CreatePolicy is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) UpdatePolicy(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdatePolicyRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdatePolicyResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.UpdatePolicy is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) InitTotp(context.Context, *connect.Request[v1.BackofficeAuthServiceInitTotpRequest]) (*connect.Response[v1.BackofficeAuthServiceInitTotpResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.InitTotp is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) VerifyTotp(context.Context, *connect.Request[v1.BackofficeAuthServiceVerifyTotpRequest]) (*connect.Response[v1.BackofficeAuthServiceVerifyTotpResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.VerifyTotp is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) RemoveTotp(context.Context, *connect.Request[v1.BackofficeAuthServiceRemoveTotpRequest]) (*connect.Response[v1.BackofficeAuthServiceRemoveTotpResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.RemoveTotp is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) FetchConfigMail(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchConfigMailRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchConfigMailResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.FetchConfigMail is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) CreateConfigMail(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateConfigMailRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateConfigMailResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.CreateConfigMail is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) UpdateConfigMail(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateConfigMailRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateConfigMailResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.UpdateConfigMail is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) FetchConfigTemplateEmail(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchConfigTemplateEmailRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchConfigTemplateEmailResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.FetchConfigTemplateEmail is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) CreateConfigTemplateEmail(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateConfigTemplateEmailRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateConfigTemplateEmailResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.CreateConfigTemplateEmail is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) UpdateConfigTemplateEmail(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateConfigTemplateEmailRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateConfigTemplateEmailResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.UpdateConfigTemplateEmail is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) UpdateRefCode(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateRefCodeRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateRefCodeResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.UpdateRefCode is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) FetchOAuthConfig(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchOAuthConfigRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchOAuthConfigResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.FetchOAuthConfig is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) CreateOAuthConfig(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateOAuthConfigRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateOAuthConfigResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.CreateOAuthConfig is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) UpdateOAuthConfig(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateOAuthConfigRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateOAuthConfigResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.UpdateOAuthConfig is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) FetchCompany(context.Context, *connect.Request[v1.BackofficeAuthServiceFetchCompanyRequest]) (*connect.Response[v1.BackofficeAuthServiceFetchCompanyResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.FetchCompany is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) CreateCompany(context.Context, *connect.Request[v1.BackofficeAuthServiceCreateCompanyRequest]) (*connect.Response[v1.BackofficeAuthServiceCreateCompanyResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.CreateCompany is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) UpdateCompany(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateCompanyRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateCompanyResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.UpdateCompany is not implemented"))
}

func (UnimplementedBackofficeAuthServiceHandler) UpdateProfile(context.Context, *connect.Request[v1.BackofficeAuthServiceUpdateProfileRequest]) (*connect.Response[v1.BackofficeAuthServiceUpdateProfileResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.BackofficeAuthService.UpdateProfile is not implemented"))
}
