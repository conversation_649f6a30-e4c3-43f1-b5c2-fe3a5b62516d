// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: auth/v1/internal.proto

package authv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// InternalAuthServiceName is the fully-qualified name of the InternalAuthService service.
	InternalAuthServiceName = "auth.v1.InternalAuthService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// InternalAuthServiceLoginProcedure is the fully-qualified name of the InternalAuthService's Login
	// RPC.
	InternalAuthServiceLoginProcedure = "/auth.v1.InternalAuthService/Login"
	// InternalAuthServiceVerifyProcedure is the fully-qualified name of the InternalAuthService's
	// Verify RPC.
	InternalAuthServiceVerifyProcedure = "/auth.v1.InternalAuthService/Verify"
	// InternalAuthServiceFetchAppProcedure is the fully-qualified name of the InternalAuthService's
	// FetchApp RPC.
	InternalAuthServiceFetchAppProcedure = "/auth.v1.InternalAuthService/FetchApp"
	// InternalAuthServiceFetchUserProcedure is the fully-qualified name of the InternalAuthService's
	// FetchUser RPC.
	InternalAuthServiceFetchUserProcedure = "/auth.v1.InternalAuthService/FetchUser"
	// InternalAuthServiceCreateAppMerchantProcedure is the fully-qualified name of the
	// InternalAuthService's CreateAppMerchant RPC.
	InternalAuthServiceCreateAppMerchantProcedure = "/auth.v1.InternalAuthService/CreateAppMerchant"
	// InternalAuthServiceFetchUserInfoProcedure is the fully-qualified name of the
	// InternalAuthService's FetchUserInfo RPC.
	InternalAuthServiceFetchUserInfoProcedure = "/auth.v1.InternalAuthService/FetchUserInfo"
	// InternalAuthServiceSendMailHtmlProcedure is the fully-qualified name of the InternalAuthService's
	// SendMailHtml RPC.
	InternalAuthServiceSendMailHtmlProcedure = "/auth.v1.InternalAuthService/SendMailHtml"
	// InternalAuthServiceRefreshTokenProcedure is the fully-qualified name of the InternalAuthService's
	// RefreshToken RPC.
	InternalAuthServiceRefreshTokenProcedure = "/auth.v1.InternalAuthService/RefreshToken"
	// InternalAuthServiceForgotPasswordProcedure is the fully-qualified name of the
	// InternalAuthService's ForgotPassword RPC.
	InternalAuthServiceForgotPasswordProcedure = "/auth.v1.InternalAuthService/ForgotPassword"
)

// InternalAuthServiceClient is a client for the auth.v1.InternalAuthService service.
type InternalAuthServiceClient interface {
	Login(context.Context, *connect.Request[v1.InternalAuthServiceLoginRequest]) (*connect.Response[v1.InternalAuthServiceLoginResponse], error)
	Verify(context.Context, *connect.Request[v1.InternalAuthServiceVerifyRequest]) (*connect.Response[v1.InternalAuthServiceVerifyResponse], error)
	FetchApp(context.Context, *connect.Request[v1.InternalAuthServiceFetchAppRequest]) (*connect.Response[v1.InternalAuthServiceFetchAppResponse], error)
	FetchUser(context.Context, *connect.Request[v1.InternalAuthServiceFetchUserRequest]) (*connect.Response[v1.InternalAuthServiceFetchUserResponse], error)
	CreateAppMerchant(context.Context, *connect.Request[v1.InternalAuthServiceCreateAppMerchantRequest]) (*connect.Response[v1.InternalAuthServiceCreateAppMerchantResponse], error)
	FetchUserInfo(context.Context, *connect.Request[v1.InternalAuthServiceFetchUserInfoRequest]) (*connect.Response[v1.InternalAuthServiceFetchUserInfoResponse], error)
	SendMailHtml(context.Context, *connect.Request[v1.InternalAuthServiceSendMailHtmlRequest]) (*connect.Response[v1.InternalAuthServiceSendMailHtmlResponse], error)
	RefreshToken(context.Context, *connect.Request[v1.InternalAuthServiceRefreshTokenRequest]) (*connect.Response[v1.InternalAuthServiceRefreshTokenResponse], error)
	ForgotPassword(context.Context, *connect.Request[v1.InternalAuthServiceForgotPasswordRequest]) (*connect.Response[v1.InternalAuthServiceForgotPasswordResponse], error)
}

// NewInternalAuthServiceClient constructs a client for the auth.v1.InternalAuthService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewInternalAuthServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) InternalAuthServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	internalAuthServiceMethods := v1.File_auth_v1_internal_proto.Services().ByName("InternalAuthService").Methods()
	return &internalAuthServiceClient{
		login: connect.NewClient[v1.InternalAuthServiceLoginRequest, v1.InternalAuthServiceLoginResponse](
			httpClient,
			baseURL+InternalAuthServiceLoginProcedure,
			connect.WithSchema(internalAuthServiceMethods.ByName("Login")),
			connect.WithClientOptions(opts...),
		),
		verify: connect.NewClient[v1.InternalAuthServiceVerifyRequest, v1.InternalAuthServiceVerifyResponse](
			httpClient,
			baseURL+InternalAuthServiceVerifyProcedure,
			connect.WithSchema(internalAuthServiceMethods.ByName("Verify")),
			connect.WithClientOptions(opts...),
		),
		fetchApp: connect.NewClient[v1.InternalAuthServiceFetchAppRequest, v1.InternalAuthServiceFetchAppResponse](
			httpClient,
			baseURL+InternalAuthServiceFetchAppProcedure,
			connect.WithSchema(internalAuthServiceMethods.ByName("FetchApp")),
			connect.WithClientOptions(opts...),
		),
		fetchUser: connect.NewClient[v1.InternalAuthServiceFetchUserRequest, v1.InternalAuthServiceFetchUserResponse](
			httpClient,
			baseURL+InternalAuthServiceFetchUserProcedure,
			connect.WithSchema(internalAuthServiceMethods.ByName("FetchUser")),
			connect.WithClientOptions(opts...),
		),
		createAppMerchant: connect.NewClient[v1.InternalAuthServiceCreateAppMerchantRequest, v1.InternalAuthServiceCreateAppMerchantResponse](
			httpClient,
			baseURL+InternalAuthServiceCreateAppMerchantProcedure,
			connect.WithSchema(internalAuthServiceMethods.ByName("CreateAppMerchant")),
			connect.WithClientOptions(opts...),
		),
		fetchUserInfo: connect.NewClient[v1.InternalAuthServiceFetchUserInfoRequest, v1.InternalAuthServiceFetchUserInfoResponse](
			httpClient,
			baseURL+InternalAuthServiceFetchUserInfoProcedure,
			connect.WithSchema(internalAuthServiceMethods.ByName("FetchUserInfo")),
			connect.WithClientOptions(opts...),
		),
		sendMailHtml: connect.NewClient[v1.InternalAuthServiceSendMailHtmlRequest, v1.InternalAuthServiceSendMailHtmlResponse](
			httpClient,
			baseURL+InternalAuthServiceSendMailHtmlProcedure,
			connect.WithSchema(internalAuthServiceMethods.ByName("SendMailHtml")),
			connect.WithClientOptions(opts...),
		),
		refreshToken: connect.NewClient[v1.InternalAuthServiceRefreshTokenRequest, v1.InternalAuthServiceRefreshTokenResponse](
			httpClient,
			baseURL+InternalAuthServiceRefreshTokenProcedure,
			connect.WithSchema(internalAuthServiceMethods.ByName("RefreshToken")),
			connect.WithClientOptions(opts...),
		),
		forgotPassword: connect.NewClient[v1.InternalAuthServiceForgotPasswordRequest, v1.InternalAuthServiceForgotPasswordResponse](
			httpClient,
			baseURL+InternalAuthServiceForgotPasswordProcedure,
			connect.WithSchema(internalAuthServiceMethods.ByName("ForgotPassword")),
			connect.WithClientOptions(opts...),
		),
	}
}

// internalAuthServiceClient implements InternalAuthServiceClient.
type internalAuthServiceClient struct {
	login             *connect.Client[v1.InternalAuthServiceLoginRequest, v1.InternalAuthServiceLoginResponse]
	verify            *connect.Client[v1.InternalAuthServiceVerifyRequest, v1.InternalAuthServiceVerifyResponse]
	fetchApp          *connect.Client[v1.InternalAuthServiceFetchAppRequest, v1.InternalAuthServiceFetchAppResponse]
	fetchUser         *connect.Client[v1.InternalAuthServiceFetchUserRequest, v1.InternalAuthServiceFetchUserResponse]
	createAppMerchant *connect.Client[v1.InternalAuthServiceCreateAppMerchantRequest, v1.InternalAuthServiceCreateAppMerchantResponse]
	fetchUserInfo     *connect.Client[v1.InternalAuthServiceFetchUserInfoRequest, v1.InternalAuthServiceFetchUserInfoResponse]
	sendMailHtml      *connect.Client[v1.InternalAuthServiceSendMailHtmlRequest, v1.InternalAuthServiceSendMailHtmlResponse]
	refreshToken      *connect.Client[v1.InternalAuthServiceRefreshTokenRequest, v1.InternalAuthServiceRefreshTokenResponse]
	forgotPassword    *connect.Client[v1.InternalAuthServiceForgotPasswordRequest, v1.InternalAuthServiceForgotPasswordResponse]
}

// Login calls auth.v1.InternalAuthService.Login.
func (c *internalAuthServiceClient) Login(ctx context.Context, req *connect.Request[v1.InternalAuthServiceLoginRequest]) (*connect.Response[v1.InternalAuthServiceLoginResponse], error) {
	return c.login.CallUnary(ctx, req)
}

// Verify calls auth.v1.InternalAuthService.Verify.
func (c *internalAuthServiceClient) Verify(ctx context.Context, req *connect.Request[v1.InternalAuthServiceVerifyRequest]) (*connect.Response[v1.InternalAuthServiceVerifyResponse], error) {
	return c.verify.CallUnary(ctx, req)
}

// FetchApp calls auth.v1.InternalAuthService.FetchApp.
func (c *internalAuthServiceClient) FetchApp(ctx context.Context, req *connect.Request[v1.InternalAuthServiceFetchAppRequest]) (*connect.Response[v1.InternalAuthServiceFetchAppResponse], error) {
	return c.fetchApp.CallUnary(ctx, req)
}

// FetchUser calls auth.v1.InternalAuthService.FetchUser.
func (c *internalAuthServiceClient) FetchUser(ctx context.Context, req *connect.Request[v1.InternalAuthServiceFetchUserRequest]) (*connect.Response[v1.InternalAuthServiceFetchUserResponse], error) {
	return c.fetchUser.CallUnary(ctx, req)
}

// CreateAppMerchant calls auth.v1.InternalAuthService.CreateAppMerchant.
func (c *internalAuthServiceClient) CreateAppMerchant(ctx context.Context, req *connect.Request[v1.InternalAuthServiceCreateAppMerchantRequest]) (*connect.Response[v1.InternalAuthServiceCreateAppMerchantResponse], error) {
	return c.createAppMerchant.CallUnary(ctx, req)
}

// FetchUserInfo calls auth.v1.InternalAuthService.FetchUserInfo.
func (c *internalAuthServiceClient) FetchUserInfo(ctx context.Context, req *connect.Request[v1.InternalAuthServiceFetchUserInfoRequest]) (*connect.Response[v1.InternalAuthServiceFetchUserInfoResponse], error) {
	return c.fetchUserInfo.CallUnary(ctx, req)
}

// SendMailHtml calls auth.v1.InternalAuthService.SendMailHtml.
func (c *internalAuthServiceClient) SendMailHtml(ctx context.Context, req *connect.Request[v1.InternalAuthServiceSendMailHtmlRequest]) (*connect.Response[v1.InternalAuthServiceSendMailHtmlResponse], error) {
	return c.sendMailHtml.CallUnary(ctx, req)
}

// RefreshToken calls auth.v1.InternalAuthService.RefreshToken.
func (c *internalAuthServiceClient) RefreshToken(ctx context.Context, req *connect.Request[v1.InternalAuthServiceRefreshTokenRequest]) (*connect.Response[v1.InternalAuthServiceRefreshTokenResponse], error) {
	return c.refreshToken.CallUnary(ctx, req)
}

// ForgotPassword calls auth.v1.InternalAuthService.ForgotPassword.
func (c *internalAuthServiceClient) ForgotPassword(ctx context.Context, req *connect.Request[v1.InternalAuthServiceForgotPasswordRequest]) (*connect.Response[v1.InternalAuthServiceForgotPasswordResponse], error) {
	return c.forgotPassword.CallUnary(ctx, req)
}

// InternalAuthServiceHandler is an implementation of the auth.v1.InternalAuthService service.
type InternalAuthServiceHandler interface {
	Login(context.Context, *connect.Request[v1.InternalAuthServiceLoginRequest]) (*connect.Response[v1.InternalAuthServiceLoginResponse], error)
	Verify(context.Context, *connect.Request[v1.InternalAuthServiceVerifyRequest]) (*connect.Response[v1.InternalAuthServiceVerifyResponse], error)
	FetchApp(context.Context, *connect.Request[v1.InternalAuthServiceFetchAppRequest]) (*connect.Response[v1.InternalAuthServiceFetchAppResponse], error)
	FetchUser(context.Context, *connect.Request[v1.InternalAuthServiceFetchUserRequest]) (*connect.Response[v1.InternalAuthServiceFetchUserResponse], error)
	CreateAppMerchant(context.Context, *connect.Request[v1.InternalAuthServiceCreateAppMerchantRequest]) (*connect.Response[v1.InternalAuthServiceCreateAppMerchantResponse], error)
	FetchUserInfo(context.Context, *connect.Request[v1.InternalAuthServiceFetchUserInfoRequest]) (*connect.Response[v1.InternalAuthServiceFetchUserInfoResponse], error)
	SendMailHtml(context.Context, *connect.Request[v1.InternalAuthServiceSendMailHtmlRequest]) (*connect.Response[v1.InternalAuthServiceSendMailHtmlResponse], error)
	RefreshToken(context.Context, *connect.Request[v1.InternalAuthServiceRefreshTokenRequest]) (*connect.Response[v1.InternalAuthServiceRefreshTokenResponse], error)
	ForgotPassword(context.Context, *connect.Request[v1.InternalAuthServiceForgotPasswordRequest]) (*connect.Response[v1.InternalAuthServiceForgotPasswordResponse], error)
}

// NewInternalAuthServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewInternalAuthServiceHandler(svc InternalAuthServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	internalAuthServiceMethods := v1.File_auth_v1_internal_proto.Services().ByName("InternalAuthService").Methods()
	internalAuthServiceLoginHandler := connect.NewUnaryHandler(
		InternalAuthServiceLoginProcedure,
		svc.Login,
		connect.WithSchema(internalAuthServiceMethods.ByName("Login")),
		connect.WithHandlerOptions(opts...),
	)
	internalAuthServiceVerifyHandler := connect.NewUnaryHandler(
		InternalAuthServiceVerifyProcedure,
		svc.Verify,
		connect.WithSchema(internalAuthServiceMethods.ByName("Verify")),
		connect.WithHandlerOptions(opts...),
	)
	internalAuthServiceFetchAppHandler := connect.NewUnaryHandler(
		InternalAuthServiceFetchAppProcedure,
		svc.FetchApp,
		connect.WithSchema(internalAuthServiceMethods.ByName("FetchApp")),
		connect.WithHandlerOptions(opts...),
	)
	internalAuthServiceFetchUserHandler := connect.NewUnaryHandler(
		InternalAuthServiceFetchUserProcedure,
		svc.FetchUser,
		connect.WithSchema(internalAuthServiceMethods.ByName("FetchUser")),
		connect.WithHandlerOptions(opts...),
	)
	internalAuthServiceCreateAppMerchantHandler := connect.NewUnaryHandler(
		InternalAuthServiceCreateAppMerchantProcedure,
		svc.CreateAppMerchant,
		connect.WithSchema(internalAuthServiceMethods.ByName("CreateAppMerchant")),
		connect.WithHandlerOptions(opts...),
	)
	internalAuthServiceFetchUserInfoHandler := connect.NewUnaryHandler(
		InternalAuthServiceFetchUserInfoProcedure,
		svc.FetchUserInfo,
		connect.WithSchema(internalAuthServiceMethods.ByName("FetchUserInfo")),
		connect.WithHandlerOptions(opts...),
	)
	internalAuthServiceSendMailHtmlHandler := connect.NewUnaryHandler(
		InternalAuthServiceSendMailHtmlProcedure,
		svc.SendMailHtml,
		connect.WithSchema(internalAuthServiceMethods.ByName("SendMailHtml")),
		connect.WithHandlerOptions(opts...),
	)
	internalAuthServiceRefreshTokenHandler := connect.NewUnaryHandler(
		InternalAuthServiceRefreshTokenProcedure,
		svc.RefreshToken,
		connect.WithSchema(internalAuthServiceMethods.ByName("RefreshToken")),
		connect.WithHandlerOptions(opts...),
	)
	internalAuthServiceForgotPasswordHandler := connect.NewUnaryHandler(
		InternalAuthServiceForgotPasswordProcedure,
		svc.ForgotPassword,
		connect.WithSchema(internalAuthServiceMethods.ByName("ForgotPassword")),
		connect.WithHandlerOptions(opts...),
	)
	return "/auth.v1.InternalAuthService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case InternalAuthServiceLoginProcedure:
			internalAuthServiceLoginHandler.ServeHTTP(w, r)
		case InternalAuthServiceVerifyProcedure:
			internalAuthServiceVerifyHandler.ServeHTTP(w, r)
		case InternalAuthServiceFetchAppProcedure:
			internalAuthServiceFetchAppHandler.ServeHTTP(w, r)
		case InternalAuthServiceFetchUserProcedure:
			internalAuthServiceFetchUserHandler.ServeHTTP(w, r)
		case InternalAuthServiceCreateAppMerchantProcedure:
			internalAuthServiceCreateAppMerchantHandler.ServeHTTP(w, r)
		case InternalAuthServiceFetchUserInfoProcedure:
			internalAuthServiceFetchUserInfoHandler.ServeHTTP(w, r)
		case InternalAuthServiceSendMailHtmlProcedure:
			internalAuthServiceSendMailHtmlHandler.ServeHTTP(w, r)
		case InternalAuthServiceRefreshTokenProcedure:
			internalAuthServiceRefreshTokenHandler.ServeHTTP(w, r)
		case InternalAuthServiceForgotPasswordProcedure:
			internalAuthServiceForgotPasswordHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedInternalAuthServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedInternalAuthServiceHandler struct{}

func (UnimplementedInternalAuthServiceHandler) Login(context.Context, *connect.Request[v1.InternalAuthServiceLoginRequest]) (*connect.Response[v1.InternalAuthServiceLoginResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.InternalAuthService.Login is not implemented"))
}

func (UnimplementedInternalAuthServiceHandler) Verify(context.Context, *connect.Request[v1.InternalAuthServiceVerifyRequest]) (*connect.Response[v1.InternalAuthServiceVerifyResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.InternalAuthService.Verify is not implemented"))
}

func (UnimplementedInternalAuthServiceHandler) FetchApp(context.Context, *connect.Request[v1.InternalAuthServiceFetchAppRequest]) (*connect.Response[v1.InternalAuthServiceFetchAppResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.InternalAuthService.FetchApp is not implemented"))
}

func (UnimplementedInternalAuthServiceHandler) FetchUser(context.Context, *connect.Request[v1.InternalAuthServiceFetchUserRequest]) (*connect.Response[v1.InternalAuthServiceFetchUserResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.InternalAuthService.FetchUser is not implemented"))
}

func (UnimplementedInternalAuthServiceHandler) CreateAppMerchant(context.Context, *connect.Request[v1.InternalAuthServiceCreateAppMerchantRequest]) (*connect.Response[v1.InternalAuthServiceCreateAppMerchantResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.InternalAuthService.CreateAppMerchant is not implemented"))
}

func (UnimplementedInternalAuthServiceHandler) FetchUserInfo(context.Context, *connect.Request[v1.InternalAuthServiceFetchUserInfoRequest]) (*connect.Response[v1.InternalAuthServiceFetchUserInfoResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.InternalAuthService.FetchUserInfo is not implemented"))
}

func (UnimplementedInternalAuthServiceHandler) SendMailHtml(context.Context, *connect.Request[v1.InternalAuthServiceSendMailHtmlRequest]) (*connect.Response[v1.InternalAuthServiceSendMailHtmlResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.InternalAuthService.SendMailHtml is not implemented"))
}

func (UnimplementedInternalAuthServiceHandler) RefreshToken(context.Context, *connect.Request[v1.InternalAuthServiceRefreshTokenRequest]) (*connect.Response[v1.InternalAuthServiceRefreshTokenResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.InternalAuthService.RefreshToken is not implemented"))
}

func (UnimplementedInternalAuthServiceHandler) ForgotPassword(context.Context, *connect.Request[v1.InternalAuthServiceForgotPasswordRequest]) (*connect.Response[v1.InternalAuthServiceForgotPasswordResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.InternalAuthService.ForgotPassword is not implemented"))
}
