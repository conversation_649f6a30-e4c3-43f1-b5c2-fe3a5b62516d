// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: auth/v1/customer.proto

package authv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/auth/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CustomerAuthServiceName is the fully-qualified name of the CustomerAuthService service.
	CustomerAuthServiceName = "auth.v1.CustomerAuthService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CustomerAuthServiceSignUpProcedure is the fully-qualified name of the CustomerAuthService's
	// SignUp RPC.
	CustomerAuthServiceSignUpProcedure = "/auth.v1.CustomerAuthService/SignUp"
	// CustomerAuthServiceVerifySignUpProcedure is the fully-qualified name of the CustomerAuthService's
	// VerifySignUp RPC.
	CustomerAuthServiceVerifySignUpProcedure = "/auth.v1.CustomerAuthService/VerifySignUp"
	// CustomerAuthServiceLoginProcedure is the fully-qualified name of the CustomerAuthService's Login
	// RPC.
	CustomerAuthServiceLoginProcedure = "/auth.v1.CustomerAuthService/Login"
	// CustomerAuthServiceMeProcedure is the fully-qualified name of the CustomerAuthService's Me RPC.
	CustomerAuthServiceMeProcedure = "/auth.v1.CustomerAuthService/Me"
	// CustomerAuthServiceChangePasswordProcedure is the fully-qualified name of the
	// CustomerAuthService's ChangePassword RPC.
	CustomerAuthServiceChangePasswordProcedure = "/auth.v1.CustomerAuthService/ChangePassword"
	// CustomerAuthServiceRefreshTokenProcedure is the fully-qualified name of the CustomerAuthService's
	// RefreshToken RPC.
	CustomerAuthServiceRefreshTokenProcedure = "/auth.v1.CustomerAuthService/RefreshToken"
	// CustomerAuthServiceForgotPasswordProcedure is the fully-qualified name of the
	// CustomerAuthService's ForgotPassword RPC.
	CustomerAuthServiceForgotPasswordProcedure = "/auth.v1.CustomerAuthService/ForgotPassword"
	// CustomerAuthServiceLoginOAuthProcedure is the fully-qualified name of the CustomerAuthService's
	// LoginOAuth RPC.
	CustomerAuthServiceLoginOAuthProcedure = "/auth.v1.CustomerAuthService/LoginOAuth"
	// CustomerAuthServiceFetchOAuthAppProcedure is the fully-qualified name of the
	// CustomerAuthService's FetchOAuthApp RPC.
	CustomerAuthServiceFetchOAuthAppProcedure = "/auth.v1.CustomerAuthService/FetchOAuthApp"
)

// CustomerAuthServiceClient is a client for the auth.v1.CustomerAuthService service.
type CustomerAuthServiceClient interface {
	SignUp(context.Context, *connect.Request[v1.CustomerAuthServiceSignUpRequest]) (*connect.Response[v1.CustomerAuthServiceSignUpResponse], error)
	VerifySignUp(context.Context, *connect.Request[v1.CustomerAuthServiceVerifySignUpRequest]) (*connect.Response[v1.CustomerAuthServiceVerifySignUpResponse], error)
	Login(context.Context, *connect.Request[v1.CustomerAuthServiceLoginRequest]) (*connect.Response[v1.CustomerAuthServiceLoginResponse], error)
	Me(context.Context, *connect.Request[v1.CustomerAuthServiceMeRequest]) (*connect.Response[v1.CustomerAuthServiceMeResponse], error)
	ChangePassword(context.Context, *connect.Request[v1.CustomerAuthServiceChangePasswordRequest]) (*connect.Response[v1.CustomerAuthServiceChangePasswordResponse], error)
	RefreshToken(context.Context, *connect.Request[v1.CustomerAuthServiceRefreshTokenRequest]) (*connect.Response[v1.CustomerAuthServiceRefreshTokenResponse], error)
	ForgotPassword(context.Context, *connect.Request[v1.CustomerAuthServiceForgotPasswordRequest]) (*connect.Response[v1.CustomerAuthServiceForgotPasswordResponse], error)
	LoginOAuth(context.Context, *connect.Request[v1.CustomerAuthServiceLoginOAuthRequest]) (*connect.Response[v1.CustomerAuthServiceLoginOAuthResponse], error)
	FetchOAuthApp(context.Context, *connect.Request[v1.CustomerAuthServiceFetchOAuthAppRequest]) (*connect.Response[v1.CustomerAuthServiceFetchOAuthAppResponse], error)
}

// NewCustomerAuthServiceClient constructs a client for the auth.v1.CustomerAuthService service. By
// default, it uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses,
// and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the
// connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCustomerAuthServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CustomerAuthServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	customerAuthServiceMethods := v1.File_auth_v1_customer_proto.Services().ByName("CustomerAuthService").Methods()
	return &customerAuthServiceClient{
		signUp: connect.NewClient[v1.CustomerAuthServiceSignUpRequest, v1.CustomerAuthServiceSignUpResponse](
			httpClient,
			baseURL+CustomerAuthServiceSignUpProcedure,
			connect.WithSchema(customerAuthServiceMethods.ByName("SignUp")),
			connect.WithClientOptions(opts...),
		),
		verifySignUp: connect.NewClient[v1.CustomerAuthServiceVerifySignUpRequest, v1.CustomerAuthServiceVerifySignUpResponse](
			httpClient,
			baseURL+CustomerAuthServiceVerifySignUpProcedure,
			connect.WithSchema(customerAuthServiceMethods.ByName("VerifySignUp")),
			connect.WithClientOptions(opts...),
		),
		login: connect.NewClient[v1.CustomerAuthServiceLoginRequest, v1.CustomerAuthServiceLoginResponse](
			httpClient,
			baseURL+CustomerAuthServiceLoginProcedure,
			connect.WithSchema(customerAuthServiceMethods.ByName("Login")),
			connect.WithClientOptions(opts...),
		),
		me: connect.NewClient[v1.CustomerAuthServiceMeRequest, v1.CustomerAuthServiceMeResponse](
			httpClient,
			baseURL+CustomerAuthServiceMeProcedure,
			connect.WithSchema(customerAuthServiceMethods.ByName("Me")),
			connect.WithClientOptions(opts...),
		),
		changePassword: connect.NewClient[v1.CustomerAuthServiceChangePasswordRequest, v1.CustomerAuthServiceChangePasswordResponse](
			httpClient,
			baseURL+CustomerAuthServiceChangePasswordProcedure,
			connect.WithSchema(customerAuthServiceMethods.ByName("ChangePassword")),
			connect.WithClientOptions(opts...),
		),
		refreshToken: connect.NewClient[v1.CustomerAuthServiceRefreshTokenRequest, v1.CustomerAuthServiceRefreshTokenResponse](
			httpClient,
			baseURL+CustomerAuthServiceRefreshTokenProcedure,
			connect.WithSchema(customerAuthServiceMethods.ByName("RefreshToken")),
			connect.WithClientOptions(opts...),
		),
		forgotPassword: connect.NewClient[v1.CustomerAuthServiceForgotPasswordRequest, v1.CustomerAuthServiceForgotPasswordResponse](
			httpClient,
			baseURL+CustomerAuthServiceForgotPasswordProcedure,
			connect.WithSchema(customerAuthServiceMethods.ByName("ForgotPassword")),
			connect.WithClientOptions(opts...),
		),
		loginOAuth: connect.NewClient[v1.CustomerAuthServiceLoginOAuthRequest, v1.CustomerAuthServiceLoginOAuthResponse](
			httpClient,
			baseURL+CustomerAuthServiceLoginOAuthProcedure,
			connect.WithSchema(customerAuthServiceMethods.ByName("LoginOAuth")),
			connect.WithClientOptions(opts...),
		),
		fetchOAuthApp: connect.NewClient[v1.CustomerAuthServiceFetchOAuthAppRequest, v1.CustomerAuthServiceFetchOAuthAppResponse](
			httpClient,
			baseURL+CustomerAuthServiceFetchOAuthAppProcedure,
			connect.WithSchema(customerAuthServiceMethods.ByName("FetchOAuthApp")),
			connect.WithClientOptions(opts...),
		),
	}
}

// customerAuthServiceClient implements CustomerAuthServiceClient.
type customerAuthServiceClient struct {
	signUp         *connect.Client[v1.CustomerAuthServiceSignUpRequest, v1.CustomerAuthServiceSignUpResponse]
	verifySignUp   *connect.Client[v1.CustomerAuthServiceVerifySignUpRequest, v1.CustomerAuthServiceVerifySignUpResponse]
	login          *connect.Client[v1.CustomerAuthServiceLoginRequest, v1.CustomerAuthServiceLoginResponse]
	me             *connect.Client[v1.CustomerAuthServiceMeRequest, v1.CustomerAuthServiceMeResponse]
	changePassword *connect.Client[v1.CustomerAuthServiceChangePasswordRequest, v1.CustomerAuthServiceChangePasswordResponse]
	refreshToken   *connect.Client[v1.CustomerAuthServiceRefreshTokenRequest, v1.CustomerAuthServiceRefreshTokenResponse]
	forgotPassword *connect.Client[v1.CustomerAuthServiceForgotPasswordRequest, v1.CustomerAuthServiceForgotPasswordResponse]
	loginOAuth     *connect.Client[v1.CustomerAuthServiceLoginOAuthRequest, v1.CustomerAuthServiceLoginOAuthResponse]
	fetchOAuthApp  *connect.Client[v1.CustomerAuthServiceFetchOAuthAppRequest, v1.CustomerAuthServiceFetchOAuthAppResponse]
}

// SignUp calls auth.v1.CustomerAuthService.SignUp.
func (c *customerAuthServiceClient) SignUp(ctx context.Context, req *connect.Request[v1.CustomerAuthServiceSignUpRequest]) (*connect.Response[v1.CustomerAuthServiceSignUpResponse], error) {
	return c.signUp.CallUnary(ctx, req)
}

// VerifySignUp calls auth.v1.CustomerAuthService.VerifySignUp.
func (c *customerAuthServiceClient) VerifySignUp(ctx context.Context, req *connect.Request[v1.CustomerAuthServiceVerifySignUpRequest]) (*connect.Response[v1.CustomerAuthServiceVerifySignUpResponse], error) {
	return c.verifySignUp.CallUnary(ctx, req)
}

// Login calls auth.v1.CustomerAuthService.Login.
func (c *customerAuthServiceClient) Login(ctx context.Context, req *connect.Request[v1.CustomerAuthServiceLoginRequest]) (*connect.Response[v1.CustomerAuthServiceLoginResponse], error) {
	return c.login.CallUnary(ctx, req)
}

// Me calls auth.v1.CustomerAuthService.Me.
func (c *customerAuthServiceClient) Me(ctx context.Context, req *connect.Request[v1.CustomerAuthServiceMeRequest]) (*connect.Response[v1.CustomerAuthServiceMeResponse], error) {
	return c.me.CallUnary(ctx, req)
}

// ChangePassword calls auth.v1.CustomerAuthService.ChangePassword.
func (c *customerAuthServiceClient) ChangePassword(ctx context.Context, req *connect.Request[v1.CustomerAuthServiceChangePasswordRequest]) (*connect.Response[v1.CustomerAuthServiceChangePasswordResponse], error) {
	return c.changePassword.CallUnary(ctx, req)
}

// RefreshToken calls auth.v1.CustomerAuthService.RefreshToken.
func (c *customerAuthServiceClient) RefreshToken(ctx context.Context, req *connect.Request[v1.CustomerAuthServiceRefreshTokenRequest]) (*connect.Response[v1.CustomerAuthServiceRefreshTokenResponse], error) {
	return c.refreshToken.CallUnary(ctx, req)
}

// ForgotPassword calls auth.v1.CustomerAuthService.ForgotPassword.
func (c *customerAuthServiceClient) ForgotPassword(ctx context.Context, req *connect.Request[v1.CustomerAuthServiceForgotPasswordRequest]) (*connect.Response[v1.CustomerAuthServiceForgotPasswordResponse], error) {
	return c.forgotPassword.CallUnary(ctx, req)
}

// LoginOAuth calls auth.v1.CustomerAuthService.LoginOAuth.
func (c *customerAuthServiceClient) LoginOAuth(ctx context.Context, req *connect.Request[v1.CustomerAuthServiceLoginOAuthRequest]) (*connect.Response[v1.CustomerAuthServiceLoginOAuthResponse], error) {
	return c.loginOAuth.CallUnary(ctx, req)
}

// FetchOAuthApp calls auth.v1.CustomerAuthService.FetchOAuthApp.
func (c *customerAuthServiceClient) FetchOAuthApp(ctx context.Context, req *connect.Request[v1.CustomerAuthServiceFetchOAuthAppRequest]) (*connect.Response[v1.CustomerAuthServiceFetchOAuthAppResponse], error) {
	return c.fetchOAuthApp.CallUnary(ctx, req)
}

// CustomerAuthServiceHandler is an implementation of the auth.v1.CustomerAuthService service.
type CustomerAuthServiceHandler interface {
	SignUp(context.Context, *connect.Request[v1.CustomerAuthServiceSignUpRequest]) (*connect.Response[v1.CustomerAuthServiceSignUpResponse], error)
	VerifySignUp(context.Context, *connect.Request[v1.CustomerAuthServiceVerifySignUpRequest]) (*connect.Response[v1.CustomerAuthServiceVerifySignUpResponse], error)
	Login(context.Context, *connect.Request[v1.CustomerAuthServiceLoginRequest]) (*connect.Response[v1.CustomerAuthServiceLoginResponse], error)
	Me(context.Context, *connect.Request[v1.CustomerAuthServiceMeRequest]) (*connect.Response[v1.CustomerAuthServiceMeResponse], error)
	ChangePassword(context.Context, *connect.Request[v1.CustomerAuthServiceChangePasswordRequest]) (*connect.Response[v1.CustomerAuthServiceChangePasswordResponse], error)
	RefreshToken(context.Context, *connect.Request[v1.CustomerAuthServiceRefreshTokenRequest]) (*connect.Response[v1.CustomerAuthServiceRefreshTokenResponse], error)
	ForgotPassword(context.Context, *connect.Request[v1.CustomerAuthServiceForgotPasswordRequest]) (*connect.Response[v1.CustomerAuthServiceForgotPasswordResponse], error)
	LoginOAuth(context.Context, *connect.Request[v1.CustomerAuthServiceLoginOAuthRequest]) (*connect.Response[v1.CustomerAuthServiceLoginOAuthResponse], error)
	FetchOAuthApp(context.Context, *connect.Request[v1.CustomerAuthServiceFetchOAuthAppRequest]) (*connect.Response[v1.CustomerAuthServiceFetchOAuthAppResponse], error)
}

// NewCustomerAuthServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCustomerAuthServiceHandler(svc CustomerAuthServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	customerAuthServiceMethods := v1.File_auth_v1_customer_proto.Services().ByName("CustomerAuthService").Methods()
	customerAuthServiceSignUpHandler := connect.NewUnaryHandler(
		CustomerAuthServiceSignUpProcedure,
		svc.SignUp,
		connect.WithSchema(customerAuthServiceMethods.ByName("SignUp")),
		connect.WithHandlerOptions(opts...),
	)
	customerAuthServiceVerifySignUpHandler := connect.NewUnaryHandler(
		CustomerAuthServiceVerifySignUpProcedure,
		svc.VerifySignUp,
		connect.WithSchema(customerAuthServiceMethods.ByName("VerifySignUp")),
		connect.WithHandlerOptions(opts...),
	)
	customerAuthServiceLoginHandler := connect.NewUnaryHandler(
		CustomerAuthServiceLoginProcedure,
		svc.Login,
		connect.WithSchema(customerAuthServiceMethods.ByName("Login")),
		connect.WithHandlerOptions(opts...),
	)
	customerAuthServiceMeHandler := connect.NewUnaryHandler(
		CustomerAuthServiceMeProcedure,
		svc.Me,
		connect.WithSchema(customerAuthServiceMethods.ByName("Me")),
		connect.WithHandlerOptions(opts...),
	)
	customerAuthServiceChangePasswordHandler := connect.NewUnaryHandler(
		CustomerAuthServiceChangePasswordProcedure,
		svc.ChangePassword,
		connect.WithSchema(customerAuthServiceMethods.ByName("ChangePassword")),
		connect.WithHandlerOptions(opts...),
	)
	customerAuthServiceRefreshTokenHandler := connect.NewUnaryHandler(
		CustomerAuthServiceRefreshTokenProcedure,
		svc.RefreshToken,
		connect.WithSchema(customerAuthServiceMethods.ByName("RefreshToken")),
		connect.WithHandlerOptions(opts...),
	)
	customerAuthServiceForgotPasswordHandler := connect.NewUnaryHandler(
		CustomerAuthServiceForgotPasswordProcedure,
		svc.ForgotPassword,
		connect.WithSchema(customerAuthServiceMethods.ByName("ForgotPassword")),
		connect.WithHandlerOptions(opts...),
	)
	customerAuthServiceLoginOAuthHandler := connect.NewUnaryHandler(
		CustomerAuthServiceLoginOAuthProcedure,
		svc.LoginOAuth,
		connect.WithSchema(customerAuthServiceMethods.ByName("LoginOAuth")),
		connect.WithHandlerOptions(opts...),
	)
	customerAuthServiceFetchOAuthAppHandler := connect.NewUnaryHandler(
		CustomerAuthServiceFetchOAuthAppProcedure,
		svc.FetchOAuthApp,
		connect.WithSchema(customerAuthServiceMethods.ByName("FetchOAuthApp")),
		connect.WithHandlerOptions(opts...),
	)
	return "/auth.v1.CustomerAuthService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CustomerAuthServiceSignUpProcedure:
			customerAuthServiceSignUpHandler.ServeHTTP(w, r)
		case CustomerAuthServiceVerifySignUpProcedure:
			customerAuthServiceVerifySignUpHandler.ServeHTTP(w, r)
		case CustomerAuthServiceLoginProcedure:
			customerAuthServiceLoginHandler.ServeHTTP(w, r)
		case CustomerAuthServiceMeProcedure:
			customerAuthServiceMeHandler.ServeHTTP(w, r)
		case CustomerAuthServiceChangePasswordProcedure:
			customerAuthServiceChangePasswordHandler.ServeHTTP(w, r)
		case CustomerAuthServiceRefreshTokenProcedure:
			customerAuthServiceRefreshTokenHandler.ServeHTTP(w, r)
		case CustomerAuthServiceForgotPasswordProcedure:
			customerAuthServiceForgotPasswordHandler.ServeHTTP(w, r)
		case CustomerAuthServiceLoginOAuthProcedure:
			customerAuthServiceLoginOAuthHandler.ServeHTTP(w, r)
		case CustomerAuthServiceFetchOAuthAppProcedure:
			customerAuthServiceFetchOAuthAppHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCustomerAuthServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCustomerAuthServiceHandler struct{}

func (UnimplementedCustomerAuthServiceHandler) SignUp(context.Context, *connect.Request[v1.CustomerAuthServiceSignUpRequest]) (*connect.Response[v1.CustomerAuthServiceSignUpResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.CustomerAuthService.SignUp is not implemented"))
}

func (UnimplementedCustomerAuthServiceHandler) VerifySignUp(context.Context, *connect.Request[v1.CustomerAuthServiceVerifySignUpRequest]) (*connect.Response[v1.CustomerAuthServiceVerifySignUpResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.CustomerAuthService.VerifySignUp is not implemented"))
}

func (UnimplementedCustomerAuthServiceHandler) Login(context.Context, *connect.Request[v1.CustomerAuthServiceLoginRequest]) (*connect.Response[v1.CustomerAuthServiceLoginResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.CustomerAuthService.Login is not implemented"))
}

func (UnimplementedCustomerAuthServiceHandler) Me(context.Context, *connect.Request[v1.CustomerAuthServiceMeRequest]) (*connect.Response[v1.CustomerAuthServiceMeResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.CustomerAuthService.Me is not implemented"))
}

func (UnimplementedCustomerAuthServiceHandler) ChangePassword(context.Context, *connect.Request[v1.CustomerAuthServiceChangePasswordRequest]) (*connect.Response[v1.CustomerAuthServiceChangePasswordResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.CustomerAuthService.ChangePassword is not implemented"))
}

func (UnimplementedCustomerAuthServiceHandler) RefreshToken(context.Context, *connect.Request[v1.CustomerAuthServiceRefreshTokenRequest]) (*connect.Response[v1.CustomerAuthServiceRefreshTokenResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.CustomerAuthService.RefreshToken is not implemented"))
}

func (UnimplementedCustomerAuthServiceHandler) ForgotPassword(context.Context, *connect.Request[v1.CustomerAuthServiceForgotPasswordRequest]) (*connect.Response[v1.CustomerAuthServiceForgotPasswordResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.CustomerAuthService.ForgotPassword is not implemented"))
}

func (UnimplementedCustomerAuthServiceHandler) LoginOAuth(context.Context, *connect.Request[v1.CustomerAuthServiceLoginOAuthRequest]) (*connect.Response[v1.CustomerAuthServiceLoginOAuthResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.CustomerAuthService.LoginOAuth is not implemented"))
}

func (UnimplementedCustomerAuthServiceHandler) FetchOAuthApp(context.Context, *connect.Request[v1.CustomerAuthServiceFetchOAuthAppRequest]) (*connect.Response[v1.CustomerAuthServiceFetchOAuthAppResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("auth.v1.CustomerAuthService.FetchOAuthApp is not implemented"))
}
