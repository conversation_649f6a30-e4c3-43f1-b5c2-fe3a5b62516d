// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/u2dpn/v1/controlplane.proto

package u2dpnv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ControlPlaneU2DpnServiceInstantProxyNotificationRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdU2DpnSession string                 `protobuf:"bytes,1,opt,name=id_u2dpn_session,json=idU2dpnSession,proto3" json:"id_u2dpn_session,omitempty"`
	IpAddr         string                 `protobuf:"bytes,2,opt,name=ip_addr,json=ipAddr,proto3" json:"ip_addr,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ControlPlaneU2DpnServiceInstantProxyNotificationRequest) Reset() {
	*x = ControlPlaneU2DpnServiceInstantProxyNotificationRequest{}
	mi := &file_proxymanager_u2dpn_v1_controlplane_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ControlPlaneU2DpnServiceInstantProxyNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlPlaneU2DpnServiceInstantProxyNotificationRequest) ProtoMessage() {}

func (x *ControlPlaneU2DpnServiceInstantProxyNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_u2dpn_v1_controlplane_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlPlaneU2DpnServiceInstantProxyNotificationRequest.ProtoReflect.Descriptor instead.
func (*ControlPlaneU2DpnServiceInstantProxyNotificationRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_u2dpn_v1_controlplane_proto_rawDescGZIP(), []int{0}
}

func (x *ControlPlaneU2DpnServiceInstantProxyNotificationRequest) GetIdU2DpnSession() string {
	if x != nil {
		return x.IdU2DpnSession
	}
	return ""
}

func (x *ControlPlaneU2DpnServiceInstantProxyNotificationRequest) GetIpAddr() string {
	if x != nil {
		return x.IpAddr
	}
	return ""
}

type ControlPlaneU2DpnServiceInstantProxyNotificationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ControlPlaneU2DpnServiceInstantProxyNotificationResponse) Reset() {
	*x = ControlPlaneU2DpnServiceInstantProxyNotificationResponse{}
	mi := &file_proxymanager_u2dpn_v1_controlplane_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ControlPlaneU2DpnServiceInstantProxyNotificationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlPlaneU2DpnServiceInstantProxyNotificationResponse) ProtoMessage() {}

func (x *ControlPlaneU2DpnServiceInstantProxyNotificationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_u2dpn_v1_controlplane_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlPlaneU2DpnServiceInstantProxyNotificationResponse.ProtoReflect.Descriptor instead.
func (*ControlPlaneU2DpnServiceInstantProxyNotificationResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_u2dpn_v1_controlplane_proto_rawDescGZIP(), []int{1}
}

func (x *ControlPlaneU2DpnServiceInstantProxyNotificationResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_proxymanager_u2dpn_v1_controlplane_proto protoreflect.FileDescriptor

const file_proxymanager_u2dpn_v1_controlplane_proto_rawDesc = "" +
	"\n" +
	"(proxymanager/u2dpn/v1/controlplane.proto\x12\x15proxymanager.u2dpn.v1\x1a\x18errmsg/v1/errormsg.proto\"|\n" +
	"7ControlPlaneU2dpnServiceInstantProxyNotificationRequest\x12(\n" +
	"\x10id_u2dpn_session\x18\x01 \x01(\tR\x0eidU2dpnSession\x12\x17\n" +
	"\aip_addr\x18\x02 \x01(\tR\x06ipAddr\"i\n" +
	"8ControlPlaneU2dpnServiceInstantProxyNotificationResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error2\xd8\x01\n" +
	"\x18ControlPlaneU2dpnService\x12\xbb\x01\n" +
	"\x18InstantProxyNotification\x12N.proxymanager.u2dpn.v1.ControlPlaneU2dpnServiceInstantProxyNotificationRequest\x1aO.proxymanager.u2dpn.v1.ControlPlaneU2dpnServiceInstantProxyNotificationResponseBNZLgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/u2dpn/v1;u2dpnv1b\x06proto3"

var (
	file_proxymanager_u2dpn_v1_controlplane_proto_rawDescOnce sync.Once
	file_proxymanager_u2dpn_v1_controlplane_proto_rawDescData []byte
)

func file_proxymanager_u2dpn_v1_controlplane_proto_rawDescGZIP() []byte {
	file_proxymanager_u2dpn_v1_controlplane_proto_rawDescOnce.Do(func() {
		file_proxymanager_u2dpn_v1_controlplane_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_u2dpn_v1_controlplane_proto_rawDesc), len(file_proxymanager_u2dpn_v1_controlplane_proto_rawDesc)))
	})
	return file_proxymanager_u2dpn_v1_controlplane_proto_rawDescData
}

var file_proxymanager_u2dpn_v1_controlplane_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_proxymanager_u2dpn_v1_controlplane_proto_goTypes = []any{
	(*ControlPlaneU2DpnServiceInstantProxyNotificationRequest)(nil),  // 0: proxymanager.u2dpn.v1.ControlPlaneU2dpnServiceInstantProxyNotificationRequest
	(*ControlPlaneU2DpnServiceInstantProxyNotificationResponse)(nil), // 1: proxymanager.u2dpn.v1.ControlPlaneU2dpnServiceInstantProxyNotificationResponse
	(*v1.ErrorMessage)(nil), // 2: errmsg.v1.ErrorMessage
}
var file_proxymanager_u2dpn_v1_controlplane_proto_depIdxs = []int32{
	2, // 0: proxymanager.u2dpn.v1.ControlPlaneU2dpnServiceInstantProxyNotificationResponse.error:type_name -> errmsg.v1.ErrorMessage
	0, // 1: proxymanager.u2dpn.v1.ControlPlaneU2dpnService.InstantProxyNotification:input_type -> proxymanager.u2dpn.v1.ControlPlaneU2dpnServiceInstantProxyNotificationRequest
	1, // 2: proxymanager.u2dpn.v1.ControlPlaneU2dpnService.InstantProxyNotification:output_type -> proxymanager.u2dpn.v1.ControlPlaneU2dpnServiceInstantProxyNotificationResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_proxymanager_u2dpn_v1_controlplane_proto_init() }
func file_proxymanager_u2dpn_v1_controlplane_proto_init() {
	if File_proxymanager_u2dpn_v1_controlplane_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_u2dpn_v1_controlplane_proto_rawDesc), len(file_proxymanager_u2dpn_v1_controlplane_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_u2dpn_v1_controlplane_proto_goTypes,
		DependencyIndexes: file_proxymanager_u2dpn_v1_controlplane_proto_depIdxs,
		MessageInfos:      file_proxymanager_u2dpn_v1_controlplane_proto_msgTypes,
	}.Build()
	File_proxymanager_u2dpn_v1_controlplane_proto = out.File
	file_proxymanager_u2dpn_v1_controlplane_proto_goTypes = nil
	file_proxymanager_u2dpn_v1_controlplane_proto_depIdxs = nil
}
