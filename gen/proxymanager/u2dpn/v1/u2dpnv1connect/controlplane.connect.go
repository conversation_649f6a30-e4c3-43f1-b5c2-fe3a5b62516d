// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/u2dpn/v1/controlplane.proto

package u2dpnv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/u2dpn/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// ControlPlaneU2dpnServiceName is the fully-qualified name of the ControlPlaneU2dpnService service.
	ControlPlaneU2dpnServiceName = "proxymanager.u2dpn.v1.ControlPlaneU2dpnService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// ControlPlaneU2DpnServiceInstantProxyNotificationProcedure is the fully-qualified name of the
	// ControlPlaneU2dpnService's InstantProxyNotification RPC.
	ControlPlaneU2DpnServiceInstantProxyNotificationProcedure = "/proxymanager.u2dpn.v1.ControlPlaneU2dpnService/InstantProxyNotification"
)

// ControlPlaneU2DpnServiceClient is a client for the proxymanager.u2dpn.v1.ControlPlaneU2dpnService
// service.
type ControlPlaneU2DpnServiceClient interface {
	InstantProxyNotification(context.Context, *connect.Request[v1.ControlPlaneU2DpnServiceInstantProxyNotificationRequest]) (*connect.Response[v1.ControlPlaneU2DpnServiceInstantProxyNotificationResponse], error)
}

// NewControlPlaneU2DpnServiceClient constructs a client for the
// proxymanager.u2dpn.v1.ControlPlaneU2dpnService service. By default, it uses the Connect protocol
// with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed requests. To
// use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or connect.WithGRPCWeb()
// options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewControlPlaneU2DpnServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) ControlPlaneU2DpnServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	controlPlaneU2DpnServiceMethods := v1.File_proxymanager_u2dpn_v1_controlplane_proto.Services().ByName("ControlPlaneU2dpnService").Methods()
	return &controlPlaneU2DpnServiceClient{
		instantProxyNotification: connect.NewClient[v1.ControlPlaneU2DpnServiceInstantProxyNotificationRequest, v1.ControlPlaneU2DpnServiceInstantProxyNotificationResponse](
			httpClient,
			baseURL+ControlPlaneU2DpnServiceInstantProxyNotificationProcedure,
			connect.WithSchema(controlPlaneU2DpnServiceMethods.ByName("InstantProxyNotification")),
			connect.WithClientOptions(opts...),
		),
	}
}

// controlPlaneU2DpnServiceClient implements ControlPlaneU2DpnServiceClient.
type controlPlaneU2DpnServiceClient struct {
	instantProxyNotification *connect.Client[v1.ControlPlaneU2DpnServiceInstantProxyNotificationRequest, v1.ControlPlaneU2DpnServiceInstantProxyNotificationResponse]
}

// InstantProxyNotification calls
// proxymanager.u2dpn.v1.ControlPlaneU2dpnService.InstantProxyNotification.
func (c *controlPlaneU2DpnServiceClient) InstantProxyNotification(ctx context.Context, req *connect.Request[v1.ControlPlaneU2DpnServiceInstantProxyNotificationRequest]) (*connect.Response[v1.ControlPlaneU2DpnServiceInstantProxyNotificationResponse], error) {
	return c.instantProxyNotification.CallUnary(ctx, req)
}

// ControlPlaneU2DpnServiceHandler is an implementation of the
// proxymanager.u2dpn.v1.ControlPlaneU2dpnService service.
type ControlPlaneU2DpnServiceHandler interface {
	InstantProxyNotification(context.Context, *connect.Request[v1.ControlPlaneU2DpnServiceInstantProxyNotificationRequest]) (*connect.Response[v1.ControlPlaneU2DpnServiceInstantProxyNotificationResponse], error)
}

// NewControlPlaneU2DpnServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewControlPlaneU2DpnServiceHandler(svc ControlPlaneU2DpnServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	controlPlaneU2DpnServiceMethods := v1.File_proxymanager_u2dpn_v1_controlplane_proto.Services().ByName("ControlPlaneU2dpnService").Methods()
	controlPlaneU2DpnServiceInstantProxyNotificationHandler := connect.NewUnaryHandler(
		ControlPlaneU2DpnServiceInstantProxyNotificationProcedure,
		svc.InstantProxyNotification,
		connect.WithSchema(controlPlaneU2DpnServiceMethods.ByName("InstantProxyNotification")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.u2dpn.v1.ControlPlaneU2dpnService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case ControlPlaneU2DpnServiceInstantProxyNotificationProcedure:
			controlPlaneU2DpnServiceInstantProxyNotificationHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedControlPlaneU2DpnServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedControlPlaneU2DpnServiceHandler struct{}

func (UnimplementedControlPlaneU2DpnServiceHandler) InstantProxyNotification(context.Context, *connect.Request[v1.ControlPlaneU2DpnServiceInstantProxyNotificationRequest]) (*connect.Response[v1.ControlPlaneU2DpnServiceInstantProxyNotificationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.u2dpn.v1.ControlPlaneU2dpnService.InstantProxyNotification is not implemented"))
}
