// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/telco/v1/backoffice.proto

package telcov1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackofficeTelcoServiceReloadCacheRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTelcoServiceReloadCacheRequest) Reset() {
	*x = BackofficeTelcoServiceReloadCacheRequest{}
	mi := &file_proxymanager_telco_v1_backoffice_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTelcoServiceReloadCacheRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTelcoServiceReloadCacheRequest) ProtoMessage() {}

func (x *BackofficeTelcoServiceReloadCacheRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_telco_v1_backoffice_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTelcoServiceReloadCacheRequest.ProtoReflect.Descriptor instead.
func (*BackofficeTelcoServiceReloadCacheRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_telco_v1_backoffice_proto_rawDescGZIP(), []int{0}
}

type BackofficeTelcoServiceReloadCacheResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTelcoServiceReloadCacheResponse) Reset() {
	*x = BackofficeTelcoServiceReloadCacheResponse{}
	mi := &file_proxymanager_telco_v1_backoffice_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTelcoServiceReloadCacheResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTelcoServiceReloadCacheResponse) ProtoMessage() {}

func (x *BackofficeTelcoServiceReloadCacheResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_telco_v1_backoffice_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTelcoServiceReloadCacheResponse.ProtoReflect.Descriptor instead.
func (*BackofficeTelcoServiceReloadCacheResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_telco_v1_backoffice_proto_rawDescGZIP(), []int{1}
}

func (x *BackofficeTelcoServiceReloadCacheResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeTelcoServiceFetchTelcoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdTelco       string                 `protobuf:"bytes,1,opt,name=id_telco,json=idTelco,proto3" json:"id_telco,omitempty"`
	NameSearch    string                 `protobuf:"bytes,2,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	Pagination    *v11.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTelcoServiceFetchTelcoRequest) Reset() {
	*x = BackofficeTelcoServiceFetchTelcoRequest{}
	mi := &file_proxymanager_telco_v1_backoffice_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTelcoServiceFetchTelcoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTelcoServiceFetchTelcoRequest) ProtoMessage() {}

func (x *BackofficeTelcoServiceFetchTelcoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_telco_v1_backoffice_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTelcoServiceFetchTelcoRequest.ProtoReflect.Descriptor instead.
func (*BackofficeTelcoServiceFetchTelcoRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_telco_v1_backoffice_proto_rawDescGZIP(), []int{2}
}

func (x *BackofficeTelcoServiceFetchTelcoRequest) GetIdTelco() string {
	if x != nil {
		return x.IdTelco
	}
	return ""
}

func (x *BackofficeTelcoServiceFetchTelcoRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *BackofficeTelcoServiceFetchTelcoRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeTelcoServiceFetchTelcoResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Items         []*BackofficeTelcoModel `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTelcoServiceFetchTelcoResponse) Reset() {
	*x = BackofficeTelcoServiceFetchTelcoResponse{}
	mi := &file_proxymanager_telco_v1_backoffice_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTelcoServiceFetchTelcoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTelcoServiceFetchTelcoResponse) ProtoMessage() {}

func (x *BackofficeTelcoServiceFetchTelcoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_telco_v1_backoffice_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTelcoServiceFetchTelcoResponse.ProtoReflect.Descriptor instead.
func (*BackofficeTelcoServiceFetchTelcoResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_telco_v1_backoffice_proto_rawDescGZIP(), []int{3}
}

func (x *BackofficeTelcoServiceFetchTelcoResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeTelcoServiceFetchTelcoResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficeTelcoServiceFetchTelcoResponse) GetItems() []*BackofficeTelcoModel {
	if x != nil {
		return x.Items
	}
	return nil
}

type BackofficeTelcoServiceCreateTelcoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTelcoServiceCreateTelcoRequest) Reset() {
	*x = BackofficeTelcoServiceCreateTelcoRequest{}
	mi := &file_proxymanager_telco_v1_backoffice_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTelcoServiceCreateTelcoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTelcoServiceCreateTelcoRequest) ProtoMessage() {}

func (x *BackofficeTelcoServiceCreateTelcoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_telco_v1_backoffice_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTelcoServiceCreateTelcoRequest.ProtoReflect.Descriptor instead.
func (*BackofficeTelcoServiceCreateTelcoRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_telco_v1_backoffice_proto_rawDescGZIP(), []int{4}
}

func (x *BackofficeTelcoServiceCreateTelcoRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type BackofficeTelcoServiceCreateTelcoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTelcoServiceCreateTelcoResponse) Reset() {
	*x = BackofficeTelcoServiceCreateTelcoResponse{}
	mi := &file_proxymanager_telco_v1_backoffice_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTelcoServiceCreateTelcoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTelcoServiceCreateTelcoResponse) ProtoMessage() {}

func (x *BackofficeTelcoServiceCreateTelcoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_telco_v1_backoffice_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTelcoServiceCreateTelcoResponse.ProtoReflect.Descriptor instead.
func (*BackofficeTelcoServiceCreateTelcoResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_telco_v1_backoffice_proto_rawDescGZIP(), []int{5}
}

func (x *BackofficeTelcoServiceCreateTelcoResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeTelcoServiceUpdateTelcoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdTelco       string                 `protobuf:"bytes,1,opt,name=id_telco,json=idTelco,proto3" json:"id_telco,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IdDefaultDns  string                 `protobuf:"bytes,3,opt,name=id_default_dns,json=idDefaultDns,proto3" json:"id_default_dns,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTelcoServiceUpdateTelcoRequest) Reset() {
	*x = BackofficeTelcoServiceUpdateTelcoRequest{}
	mi := &file_proxymanager_telco_v1_backoffice_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTelcoServiceUpdateTelcoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTelcoServiceUpdateTelcoRequest) ProtoMessage() {}

func (x *BackofficeTelcoServiceUpdateTelcoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_telco_v1_backoffice_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTelcoServiceUpdateTelcoRequest.ProtoReflect.Descriptor instead.
func (*BackofficeTelcoServiceUpdateTelcoRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_telco_v1_backoffice_proto_rawDescGZIP(), []int{6}
}

func (x *BackofficeTelcoServiceUpdateTelcoRequest) GetIdTelco() string {
	if x != nil {
		return x.IdTelco
	}
	return ""
}

func (x *BackofficeTelcoServiceUpdateTelcoRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeTelcoServiceUpdateTelcoRequest) GetIdDefaultDns() string {
	if x != nil {
		return x.IdDefaultDns
	}
	return ""
}

type BackofficeTelcoServiceUpdateTelcoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTelcoServiceUpdateTelcoResponse) Reset() {
	*x = BackofficeTelcoServiceUpdateTelcoResponse{}
	mi := &file_proxymanager_telco_v1_backoffice_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTelcoServiceUpdateTelcoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTelcoServiceUpdateTelcoResponse) ProtoMessage() {}

func (x *BackofficeTelcoServiceUpdateTelcoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_telco_v1_backoffice_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTelcoServiceUpdateTelcoResponse.ProtoReflect.Descriptor instead.
func (*BackofficeTelcoServiceUpdateTelcoResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_telco_v1_backoffice_proto_rawDescGZIP(), []int{7}
}

func (x *BackofficeTelcoServiceUpdateTelcoResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeTelcoModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdTelco       string                 `protobuf:"bytes,1,opt,name=id_telco,json=idTelco,proto3" json:"id_telco,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IdDefaultDns  string                 `protobuf:"bytes,3,opt,name=id_default_dns,json=idDefaultDns,proto3" json:"id_default_dns,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeTelcoModel) Reset() {
	*x = BackofficeTelcoModel{}
	mi := &file_proxymanager_telco_v1_backoffice_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeTelcoModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeTelcoModel) ProtoMessage() {}

func (x *BackofficeTelcoModel) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_telco_v1_backoffice_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeTelcoModel.ProtoReflect.Descriptor instead.
func (*BackofficeTelcoModel) Descriptor() ([]byte, []int) {
	return file_proxymanager_telco_v1_backoffice_proto_rawDescGZIP(), []int{8}
}

func (x *BackofficeTelcoModel) GetIdTelco() string {
	if x != nil {
		return x.IdTelco
	}
	return ""
}

func (x *BackofficeTelcoModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeTelcoModel) GetIdDefaultDns() string {
	if x != nil {
		return x.IdDefaultDns
	}
	return ""
}

var File_proxymanager_telco_v1_backoffice_proto protoreflect.FileDescriptor

const file_proxymanager_telco_v1_backoffice_proto_rawDesc = "" +
	"\n" +
	"&proxymanager/telco/v1/backoffice.proto\x12\x15proxymanager.telco.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\"*\n" +
	"(BackofficeTelcoServiceReloadCacheRequest\"Z\n" +
	")BackofficeTelcoServiceReloadCacheResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xa2\x01\n" +
	"'BackofficeTelcoServiceFetchTelcoRequest\x12\x19\n" +
	"\bid_telco\x18\x01 \x01(\tR\aidTelco\x12\x1f\n" +
	"\vname_search\x18\x02 \x01(\tR\n" +
	"nameSearch\x12;\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xda\x01\n" +
	"(BackofficeTelcoServiceFetchTelcoResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12A\n" +
	"\x05items\x18\x03 \x03(\v2+.proxymanager.telco.v1.BackofficeTelcoModelR\x05items\">\n" +
	"(BackofficeTelcoServiceCreateTelcoRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"Z\n" +
	")BackofficeTelcoServiceCreateTelcoResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x7f\n" +
	"(BackofficeTelcoServiceUpdateTelcoRequest\x12\x19\n" +
	"\bid_telco\x18\x01 \x01(\tR\aidTelco\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12$\n" +
	"\x0eid_default_dns\x18\x03 \x01(\tR\fidDefaultDns\"Z\n" +
	")BackofficeTelcoServiceUpdateTelcoResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"k\n" +
	"\x14BackofficeTelcoModel\x12\x19\n" +
	"\bid_telco\x18\x01 \x01(\tR\aidTelco\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12$\n" +
	"\x0eid_default_dns\x18\x03 \x01(\tR\fidDefaultDns2\xe1\x04\n" +
	"\x16BackofficeTelcoService\x12\x90\x01\n" +
	"\vReloadCache\x12?.proxymanager.telco.v1.BackofficeTelcoServiceReloadCacheRequest\<EMAIL>\x12\x8d\x01\n" +
	"\n" +
	"FetchTelco\x12>.proxymanager.telco.v1.BackofficeTelcoServiceFetchTelcoRequest\x1a?.proxymanager.telco.v1.BackofficeTelcoServiceFetchTelcoResponse\x12\x90\x01\n" +
	"\vCreateTelco\x12?.proxymanager.telco.v1.BackofficeTelcoServiceCreateTelcoRequest\<EMAIL>\x12\x90\x01\n" +
	"\vUpdateTelco\x12?.proxymanager.telco.v1.BackofficeTelcoServiceUpdateTelcoRequest\<EMAIL>/algo/algoproxy-proto/gen/proxymanager/telco/v1;telcov1b\x06proto3"

var (
	file_proxymanager_telco_v1_backoffice_proto_rawDescOnce sync.Once
	file_proxymanager_telco_v1_backoffice_proto_rawDescData []byte
)

func file_proxymanager_telco_v1_backoffice_proto_rawDescGZIP() []byte {
	file_proxymanager_telco_v1_backoffice_proto_rawDescOnce.Do(func() {
		file_proxymanager_telco_v1_backoffice_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_telco_v1_backoffice_proto_rawDesc), len(file_proxymanager_telco_v1_backoffice_proto_rawDesc)))
	})
	return file_proxymanager_telco_v1_backoffice_proto_rawDescData
}

var file_proxymanager_telco_v1_backoffice_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_proxymanager_telco_v1_backoffice_proto_goTypes = []any{
	(*BackofficeTelcoServiceReloadCacheRequest)(nil),  // 0: proxymanager.telco.v1.BackofficeTelcoServiceReloadCacheRequest
	(*BackofficeTelcoServiceReloadCacheResponse)(nil), // 1: proxymanager.telco.v1.BackofficeTelcoServiceReloadCacheResponse
	(*BackofficeTelcoServiceFetchTelcoRequest)(nil),   // 2: proxymanager.telco.v1.BackofficeTelcoServiceFetchTelcoRequest
	(*BackofficeTelcoServiceFetchTelcoResponse)(nil),  // 3: proxymanager.telco.v1.BackofficeTelcoServiceFetchTelcoResponse
	(*BackofficeTelcoServiceCreateTelcoRequest)(nil),  // 4: proxymanager.telco.v1.BackofficeTelcoServiceCreateTelcoRequest
	(*BackofficeTelcoServiceCreateTelcoResponse)(nil), // 5: proxymanager.telco.v1.BackofficeTelcoServiceCreateTelcoResponse
	(*BackofficeTelcoServiceUpdateTelcoRequest)(nil),  // 6: proxymanager.telco.v1.BackofficeTelcoServiceUpdateTelcoRequest
	(*BackofficeTelcoServiceUpdateTelcoResponse)(nil), // 7: proxymanager.telco.v1.BackofficeTelcoServiceUpdateTelcoResponse
	(*BackofficeTelcoModel)(nil),                      // 8: proxymanager.telco.v1.BackofficeTelcoModel
	(*v1.ErrorMessage)(nil),                           // 9: errmsg.v1.ErrorMessage
	(*v11.PaginationRequest)(nil),                     // 10: utils.v1.PaginationRequest
	(*v11.PaginationResponse)(nil),                    // 11: utils.v1.PaginationResponse
}
var file_proxymanager_telco_v1_backoffice_proto_depIdxs = []int32{
	9,  // 0: proxymanager.telco.v1.BackofficeTelcoServiceReloadCacheResponse.error:type_name -> errmsg.v1.ErrorMessage
	10, // 1: proxymanager.telco.v1.BackofficeTelcoServiceFetchTelcoRequest.pagination:type_name -> utils.v1.PaginationRequest
	9,  // 2: proxymanager.telco.v1.BackofficeTelcoServiceFetchTelcoResponse.error:type_name -> errmsg.v1.ErrorMessage
	11, // 3: proxymanager.telco.v1.BackofficeTelcoServiceFetchTelcoResponse.pagination:type_name -> utils.v1.PaginationResponse
	8,  // 4: proxymanager.telco.v1.BackofficeTelcoServiceFetchTelcoResponse.items:type_name -> proxymanager.telco.v1.BackofficeTelcoModel
	9,  // 5: proxymanager.telco.v1.BackofficeTelcoServiceCreateTelcoResponse.error:type_name -> errmsg.v1.ErrorMessage
	9,  // 6: proxymanager.telco.v1.BackofficeTelcoServiceUpdateTelcoResponse.error:type_name -> errmsg.v1.ErrorMessage
	0,  // 7: proxymanager.telco.v1.BackofficeTelcoService.ReloadCache:input_type -> proxymanager.telco.v1.BackofficeTelcoServiceReloadCacheRequest
	2,  // 8: proxymanager.telco.v1.BackofficeTelcoService.FetchTelco:input_type -> proxymanager.telco.v1.BackofficeTelcoServiceFetchTelcoRequest
	4,  // 9: proxymanager.telco.v1.BackofficeTelcoService.CreateTelco:input_type -> proxymanager.telco.v1.BackofficeTelcoServiceCreateTelcoRequest
	6,  // 10: proxymanager.telco.v1.BackofficeTelcoService.UpdateTelco:input_type -> proxymanager.telco.v1.BackofficeTelcoServiceUpdateTelcoRequest
	1,  // 11: proxymanager.telco.v1.BackofficeTelcoService.ReloadCache:output_type -> proxymanager.telco.v1.BackofficeTelcoServiceReloadCacheResponse
	3,  // 12: proxymanager.telco.v1.BackofficeTelcoService.FetchTelco:output_type -> proxymanager.telco.v1.BackofficeTelcoServiceFetchTelcoResponse
	5,  // 13: proxymanager.telco.v1.BackofficeTelcoService.CreateTelco:output_type -> proxymanager.telco.v1.BackofficeTelcoServiceCreateTelcoResponse
	7,  // 14: proxymanager.telco.v1.BackofficeTelcoService.UpdateTelco:output_type -> proxymanager.telco.v1.BackofficeTelcoServiceUpdateTelcoResponse
	11, // [11:15] is the sub-list for method output_type
	7,  // [7:11] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_proxymanager_telco_v1_backoffice_proto_init() }
func file_proxymanager_telco_v1_backoffice_proto_init() {
	if File_proxymanager_telco_v1_backoffice_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_telco_v1_backoffice_proto_rawDesc), len(file_proxymanager_telco_v1_backoffice_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_telco_v1_backoffice_proto_goTypes,
		DependencyIndexes: file_proxymanager_telco_v1_backoffice_proto_depIdxs,
		MessageInfos:      file_proxymanager_telco_v1_backoffice_proto_msgTypes,
	}.Build()
	File_proxymanager_telco_v1_backoffice_proto = out.File
	file_proxymanager_telco_v1_backoffice_proto_goTypes = nil
	file_proxymanager_telco_v1_backoffice_proto_depIdxs = nil
}
