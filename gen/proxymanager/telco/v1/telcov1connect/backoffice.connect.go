// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/telco/v1/backoffice.proto

package telcov1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/telco/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackofficeTelcoServiceName is the fully-qualified name of the BackofficeTelcoService service.
	BackofficeTelcoServiceName = "proxymanager.telco.v1.BackofficeTelcoService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackofficeTelcoServiceReloadCacheProcedure is the fully-qualified name of the
	// BackofficeTelcoService's ReloadCache RPC.
	BackofficeTelcoServiceReloadCacheProcedure = "/proxymanager.telco.v1.BackofficeTelcoService/ReloadCache"
	// BackofficeTelcoServiceFetchTelcoProcedure is the fully-qualified name of the
	// BackofficeTelcoService's FetchTelco RPC.
	BackofficeTelcoServiceFetchTelcoProcedure = "/proxymanager.telco.v1.BackofficeTelcoService/FetchTelco"
	// BackofficeTelcoServiceCreateTelcoProcedure is the fully-qualified name of the
	// BackofficeTelcoService's CreateTelco RPC.
	BackofficeTelcoServiceCreateTelcoProcedure = "/proxymanager.telco.v1.BackofficeTelcoService/CreateTelco"
	// BackofficeTelcoServiceUpdateTelcoProcedure is the fully-qualified name of the
	// BackofficeTelcoService's UpdateTelco RPC.
	BackofficeTelcoServiceUpdateTelcoProcedure = "/proxymanager.telco.v1.BackofficeTelcoService/UpdateTelco"
)

// BackofficeTelcoServiceClient is a client for the proxymanager.telco.v1.BackofficeTelcoService
// service.
type BackofficeTelcoServiceClient interface {
	ReloadCache(context.Context, *connect.Request[v1.BackofficeTelcoServiceReloadCacheRequest]) (*connect.Response[v1.BackofficeTelcoServiceReloadCacheResponse], error)
	FetchTelco(context.Context, *connect.Request[v1.BackofficeTelcoServiceFetchTelcoRequest]) (*connect.Response[v1.BackofficeTelcoServiceFetchTelcoResponse], error)
	CreateTelco(context.Context, *connect.Request[v1.BackofficeTelcoServiceCreateTelcoRequest]) (*connect.Response[v1.BackofficeTelcoServiceCreateTelcoResponse], error)
	UpdateTelco(context.Context, *connect.Request[v1.BackofficeTelcoServiceUpdateTelcoRequest]) (*connect.Response[v1.BackofficeTelcoServiceUpdateTelcoResponse], error)
}

// NewBackofficeTelcoServiceClient constructs a client for the
// proxymanager.telco.v1.BackofficeTelcoService service. By default, it uses the Connect protocol
// with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed requests. To
// use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or connect.WithGRPCWeb()
// options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackofficeTelcoServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackofficeTelcoServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backofficeTelcoServiceMethods := v1.File_proxymanager_telco_v1_backoffice_proto.Services().ByName("BackofficeTelcoService").Methods()
	return &backofficeTelcoServiceClient{
		reloadCache: connect.NewClient[v1.BackofficeTelcoServiceReloadCacheRequest, v1.BackofficeTelcoServiceReloadCacheResponse](
			httpClient,
			baseURL+BackofficeTelcoServiceReloadCacheProcedure,
			connect.WithSchema(backofficeTelcoServiceMethods.ByName("ReloadCache")),
			connect.WithClientOptions(opts...),
		),
		fetchTelco: connect.NewClient[v1.BackofficeTelcoServiceFetchTelcoRequest, v1.BackofficeTelcoServiceFetchTelcoResponse](
			httpClient,
			baseURL+BackofficeTelcoServiceFetchTelcoProcedure,
			connect.WithSchema(backofficeTelcoServiceMethods.ByName("FetchTelco")),
			connect.WithClientOptions(opts...),
		),
		createTelco: connect.NewClient[v1.BackofficeTelcoServiceCreateTelcoRequest, v1.BackofficeTelcoServiceCreateTelcoResponse](
			httpClient,
			baseURL+BackofficeTelcoServiceCreateTelcoProcedure,
			connect.WithSchema(backofficeTelcoServiceMethods.ByName("CreateTelco")),
			connect.WithClientOptions(opts...),
		),
		updateTelco: connect.NewClient[v1.BackofficeTelcoServiceUpdateTelcoRequest, v1.BackofficeTelcoServiceUpdateTelcoResponse](
			httpClient,
			baseURL+BackofficeTelcoServiceUpdateTelcoProcedure,
			connect.WithSchema(backofficeTelcoServiceMethods.ByName("UpdateTelco")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backofficeTelcoServiceClient implements BackofficeTelcoServiceClient.
type backofficeTelcoServiceClient struct {
	reloadCache *connect.Client[v1.BackofficeTelcoServiceReloadCacheRequest, v1.BackofficeTelcoServiceReloadCacheResponse]
	fetchTelco  *connect.Client[v1.BackofficeTelcoServiceFetchTelcoRequest, v1.BackofficeTelcoServiceFetchTelcoResponse]
	createTelco *connect.Client[v1.BackofficeTelcoServiceCreateTelcoRequest, v1.BackofficeTelcoServiceCreateTelcoResponse]
	updateTelco *connect.Client[v1.BackofficeTelcoServiceUpdateTelcoRequest, v1.BackofficeTelcoServiceUpdateTelcoResponse]
}

// ReloadCache calls proxymanager.telco.v1.BackofficeTelcoService.ReloadCache.
func (c *backofficeTelcoServiceClient) ReloadCache(ctx context.Context, req *connect.Request[v1.BackofficeTelcoServiceReloadCacheRequest]) (*connect.Response[v1.BackofficeTelcoServiceReloadCacheResponse], error) {
	return c.reloadCache.CallUnary(ctx, req)
}

// FetchTelco calls proxymanager.telco.v1.BackofficeTelcoService.FetchTelco.
func (c *backofficeTelcoServiceClient) FetchTelco(ctx context.Context, req *connect.Request[v1.BackofficeTelcoServiceFetchTelcoRequest]) (*connect.Response[v1.BackofficeTelcoServiceFetchTelcoResponse], error) {
	return c.fetchTelco.CallUnary(ctx, req)
}

// CreateTelco calls proxymanager.telco.v1.BackofficeTelcoService.CreateTelco.
func (c *backofficeTelcoServiceClient) CreateTelco(ctx context.Context, req *connect.Request[v1.BackofficeTelcoServiceCreateTelcoRequest]) (*connect.Response[v1.BackofficeTelcoServiceCreateTelcoResponse], error) {
	return c.createTelco.CallUnary(ctx, req)
}

// UpdateTelco calls proxymanager.telco.v1.BackofficeTelcoService.UpdateTelco.
func (c *backofficeTelcoServiceClient) UpdateTelco(ctx context.Context, req *connect.Request[v1.BackofficeTelcoServiceUpdateTelcoRequest]) (*connect.Response[v1.BackofficeTelcoServiceUpdateTelcoResponse], error) {
	return c.updateTelco.CallUnary(ctx, req)
}

// BackofficeTelcoServiceHandler is an implementation of the
// proxymanager.telco.v1.BackofficeTelcoService service.
type BackofficeTelcoServiceHandler interface {
	ReloadCache(context.Context, *connect.Request[v1.BackofficeTelcoServiceReloadCacheRequest]) (*connect.Response[v1.BackofficeTelcoServiceReloadCacheResponse], error)
	FetchTelco(context.Context, *connect.Request[v1.BackofficeTelcoServiceFetchTelcoRequest]) (*connect.Response[v1.BackofficeTelcoServiceFetchTelcoResponse], error)
	CreateTelco(context.Context, *connect.Request[v1.BackofficeTelcoServiceCreateTelcoRequest]) (*connect.Response[v1.BackofficeTelcoServiceCreateTelcoResponse], error)
	UpdateTelco(context.Context, *connect.Request[v1.BackofficeTelcoServiceUpdateTelcoRequest]) (*connect.Response[v1.BackofficeTelcoServiceUpdateTelcoResponse], error)
}

// NewBackofficeTelcoServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackofficeTelcoServiceHandler(svc BackofficeTelcoServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backofficeTelcoServiceMethods := v1.File_proxymanager_telco_v1_backoffice_proto.Services().ByName("BackofficeTelcoService").Methods()
	backofficeTelcoServiceReloadCacheHandler := connect.NewUnaryHandler(
		BackofficeTelcoServiceReloadCacheProcedure,
		svc.ReloadCache,
		connect.WithSchema(backofficeTelcoServiceMethods.ByName("ReloadCache")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeTelcoServiceFetchTelcoHandler := connect.NewUnaryHandler(
		BackofficeTelcoServiceFetchTelcoProcedure,
		svc.FetchTelco,
		connect.WithSchema(backofficeTelcoServiceMethods.ByName("FetchTelco")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeTelcoServiceCreateTelcoHandler := connect.NewUnaryHandler(
		BackofficeTelcoServiceCreateTelcoProcedure,
		svc.CreateTelco,
		connect.WithSchema(backofficeTelcoServiceMethods.ByName("CreateTelco")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeTelcoServiceUpdateTelcoHandler := connect.NewUnaryHandler(
		BackofficeTelcoServiceUpdateTelcoProcedure,
		svc.UpdateTelco,
		connect.WithSchema(backofficeTelcoServiceMethods.ByName("UpdateTelco")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.telco.v1.BackofficeTelcoService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackofficeTelcoServiceReloadCacheProcedure:
			backofficeTelcoServiceReloadCacheHandler.ServeHTTP(w, r)
		case BackofficeTelcoServiceFetchTelcoProcedure:
			backofficeTelcoServiceFetchTelcoHandler.ServeHTTP(w, r)
		case BackofficeTelcoServiceCreateTelcoProcedure:
			backofficeTelcoServiceCreateTelcoHandler.ServeHTTP(w, r)
		case BackofficeTelcoServiceUpdateTelcoProcedure:
			backofficeTelcoServiceUpdateTelcoHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackofficeTelcoServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackofficeTelcoServiceHandler struct{}

func (UnimplementedBackofficeTelcoServiceHandler) ReloadCache(context.Context, *connect.Request[v1.BackofficeTelcoServiceReloadCacheRequest]) (*connect.Response[v1.BackofficeTelcoServiceReloadCacheResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.telco.v1.BackofficeTelcoService.ReloadCache is not implemented"))
}

func (UnimplementedBackofficeTelcoServiceHandler) FetchTelco(context.Context, *connect.Request[v1.BackofficeTelcoServiceFetchTelcoRequest]) (*connect.Response[v1.BackofficeTelcoServiceFetchTelcoResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.telco.v1.BackofficeTelcoService.FetchTelco is not implemented"))
}

func (UnimplementedBackofficeTelcoServiceHandler) CreateTelco(context.Context, *connect.Request[v1.BackofficeTelcoServiceCreateTelcoRequest]) (*connect.Response[v1.BackofficeTelcoServiceCreateTelcoResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.telco.v1.BackofficeTelcoService.CreateTelco is not implemented"))
}

func (UnimplementedBackofficeTelcoServiceHandler) UpdateTelco(context.Context, *connect.Request[v1.BackofficeTelcoServiceUpdateTelcoRequest]) (*connect.Response[v1.BackofficeTelcoServiceUpdateTelcoResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.telco.v1.BackofficeTelcoService.UpdateTelco is not implemented"))
}
