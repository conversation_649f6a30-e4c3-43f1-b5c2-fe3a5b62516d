// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/dns/v1/backoffice.proto

package dnsv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackofficeDNSServiceFetchDNSRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdDns         string                 `protobuf:"bytes,1,opt,name=id_dns,json=idDns,proto3" json:"id_dns,omitempty"`
	NameSearch    string                 `protobuf:"bytes,2,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	IpSearch      string                 `protobuf:"bytes,3,opt,name=ip_search,json=ipSearch,proto3" json:"ip_search,omitempty"`
	IpType        v1.IPType              `protobuf:"varint,4,opt,name=ip_type,json=ipType,proto3,enum=algoenum.v1.IPType" json:"ip_type,omitempty"`
	State         *v11.State             `protobuf:"bytes,5,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v11.PaginationRequest `protobuf:"bytes,6,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeDNSServiceFetchDNSRequest) Reset() {
	*x = BackofficeDNSServiceFetchDNSRequest{}
	mi := &file_proxymanager_dns_v1_backoffice_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeDNSServiceFetchDNSRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeDNSServiceFetchDNSRequest) ProtoMessage() {}

func (x *BackofficeDNSServiceFetchDNSRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_dns_v1_backoffice_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeDNSServiceFetchDNSRequest.ProtoReflect.Descriptor instead.
func (*BackofficeDNSServiceFetchDNSRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_dns_v1_backoffice_proto_rawDescGZIP(), []int{0}
}

func (x *BackofficeDNSServiceFetchDNSRequest) GetIdDns() string {
	if x != nil {
		return x.IdDns
	}
	return ""
}

func (x *BackofficeDNSServiceFetchDNSRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *BackofficeDNSServiceFetchDNSRequest) GetIpSearch() string {
	if x != nil {
		return x.IpSearch
	}
	return ""
}

func (x *BackofficeDNSServiceFetchDNSRequest) GetIpType() v1.IPType {
	if x != nil {
		return x.IpType
	}
	return v1.IPType(0)
}

func (x *BackofficeDNSServiceFetchDNSRequest) GetState() *v11.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeDNSServiceFetchDNSRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeDNSServiceFetchDNSResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v12.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ListDns       []*BackofficeDNS        `protobuf:"bytes,3,rep,name=list_dns,json=listDns,proto3" json:"list_dns,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeDNSServiceFetchDNSResponse) Reset() {
	*x = BackofficeDNSServiceFetchDNSResponse{}
	mi := &file_proxymanager_dns_v1_backoffice_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeDNSServiceFetchDNSResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeDNSServiceFetchDNSResponse) ProtoMessage() {}

func (x *BackofficeDNSServiceFetchDNSResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_dns_v1_backoffice_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeDNSServiceFetchDNSResponse.ProtoReflect.Descriptor instead.
func (*BackofficeDNSServiceFetchDNSResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_dns_v1_backoffice_proto_rawDescGZIP(), []int{1}
}

func (x *BackofficeDNSServiceFetchDNSResponse) GetError() *v12.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeDNSServiceFetchDNSResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficeDNSServiceFetchDNSResponse) GetListDns() []*BackofficeDNS {
	if x != nil {
		return x.ListDns
	}
	return nil
}

type BackofficeDNS struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdDns         string                 `protobuf:"bytes,1,opt,name=id_dns,json=idDns,proto3" json:"id_dns,omitempty"`
	IpType        v1.IPType              `protobuf:"varint,2,opt,name=ip_type,json=ipType,proto3,enum=algoenum.v1.IPType" json:"ip_type,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Dns1          string                 `protobuf:"bytes,4,opt,name=dns1,proto3" json:"dns1,omitempty"`
	Dns2          string                 `protobuf:"bytes,5,opt,name=dns2,proto3" json:"dns2,omitempty"`
	IsActive      bool                   `protobuf:"varint,6,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeDNS) Reset() {
	*x = BackofficeDNS{}
	mi := &file_proxymanager_dns_v1_backoffice_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeDNS) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeDNS) ProtoMessage() {}

func (x *BackofficeDNS) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_dns_v1_backoffice_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeDNS.ProtoReflect.Descriptor instead.
func (*BackofficeDNS) Descriptor() ([]byte, []int) {
	return file_proxymanager_dns_v1_backoffice_proto_rawDescGZIP(), []int{2}
}

func (x *BackofficeDNS) GetIdDns() string {
	if x != nil {
		return x.IdDns
	}
	return ""
}

func (x *BackofficeDNS) GetIpType() v1.IPType {
	if x != nil {
		return x.IpType
	}
	return v1.IPType(0)
}

func (x *BackofficeDNS) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeDNS) GetDns1() string {
	if x != nil {
		return x.Dns1
	}
	return ""
}

func (x *BackofficeDNS) GetDns2() string {
	if x != nil {
		return x.Dns2
	}
	return ""
}

func (x *BackofficeDNS) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficeDNSServiceCreateDNSRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Dns1          string                 `protobuf:"bytes,2,opt,name=dns1,proto3" json:"dns1,omitempty"`
	Dns2          string                 `protobuf:"bytes,3,opt,name=dns2,proto3" json:"dns2,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeDNSServiceCreateDNSRequest) Reset() {
	*x = BackofficeDNSServiceCreateDNSRequest{}
	mi := &file_proxymanager_dns_v1_backoffice_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeDNSServiceCreateDNSRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeDNSServiceCreateDNSRequest) ProtoMessage() {}

func (x *BackofficeDNSServiceCreateDNSRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_dns_v1_backoffice_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeDNSServiceCreateDNSRequest.ProtoReflect.Descriptor instead.
func (*BackofficeDNSServiceCreateDNSRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_dns_v1_backoffice_proto_rawDescGZIP(), []int{3}
}

func (x *BackofficeDNSServiceCreateDNSRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeDNSServiceCreateDNSRequest) GetDns1() string {
	if x != nil {
		return x.Dns1
	}
	return ""
}

func (x *BackofficeDNSServiceCreateDNSRequest) GetDns2() string {
	if x != nil {
		return x.Dns2
	}
	return ""
}

type BackofficeDNSServiceCreateDNSResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v12.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeDNSServiceCreateDNSResponse) Reset() {
	*x = BackofficeDNSServiceCreateDNSResponse{}
	mi := &file_proxymanager_dns_v1_backoffice_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeDNSServiceCreateDNSResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeDNSServiceCreateDNSResponse) ProtoMessage() {}

func (x *BackofficeDNSServiceCreateDNSResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_dns_v1_backoffice_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeDNSServiceCreateDNSResponse.ProtoReflect.Descriptor instead.
func (*BackofficeDNSServiceCreateDNSResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_dns_v1_backoffice_proto_rawDescGZIP(), []int{4}
}

func (x *BackofficeDNSServiceCreateDNSResponse) GetError() *v12.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeDNSServiceUpdateDNSRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdDns         string                 `protobuf:"bytes,1,opt,name=id_dns,json=idDns,proto3" json:"id_dns,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Dns1          string                 `protobuf:"bytes,3,opt,name=dns1,proto3" json:"dns1,omitempty"`
	Dns2          string                 `protobuf:"bytes,4,opt,name=dns2,proto3" json:"dns2,omitempty"`
	State         *v11.State             `protobuf:"bytes,5,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeDNSServiceUpdateDNSRequest) Reset() {
	*x = BackofficeDNSServiceUpdateDNSRequest{}
	mi := &file_proxymanager_dns_v1_backoffice_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeDNSServiceUpdateDNSRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeDNSServiceUpdateDNSRequest) ProtoMessage() {}

func (x *BackofficeDNSServiceUpdateDNSRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_dns_v1_backoffice_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeDNSServiceUpdateDNSRequest.ProtoReflect.Descriptor instead.
func (*BackofficeDNSServiceUpdateDNSRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_dns_v1_backoffice_proto_rawDescGZIP(), []int{5}
}

func (x *BackofficeDNSServiceUpdateDNSRequest) GetIdDns() string {
	if x != nil {
		return x.IdDns
	}
	return ""
}

func (x *BackofficeDNSServiceUpdateDNSRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeDNSServiceUpdateDNSRequest) GetDns1() string {
	if x != nil {
		return x.Dns1
	}
	return ""
}

func (x *BackofficeDNSServiceUpdateDNSRequest) GetDns2() string {
	if x != nil {
		return x.Dns2
	}
	return ""
}

func (x *BackofficeDNSServiceUpdateDNSRequest) GetState() *v11.State {
	if x != nil {
		return x.State
	}
	return nil
}

type BackofficeDNSServiceUpdateDNSResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v12.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeDNSServiceUpdateDNSResponse) Reset() {
	*x = BackofficeDNSServiceUpdateDNSResponse{}
	mi := &file_proxymanager_dns_v1_backoffice_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeDNSServiceUpdateDNSResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeDNSServiceUpdateDNSResponse) ProtoMessage() {}

func (x *BackofficeDNSServiceUpdateDNSResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_dns_v1_backoffice_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeDNSServiceUpdateDNSResponse.ProtoReflect.Descriptor instead.
func (*BackofficeDNSServiceUpdateDNSResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_dns_v1_backoffice_proto_rawDescGZIP(), []int{6}
}

func (x *BackofficeDNSServiceUpdateDNSResponse) GetError() *v12.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_proxymanager_dns_v1_backoffice_proto protoreflect.FileDescriptor

const file_proxymanager_dns_v1_backoffice_proto_rawDesc = "" +
	"\n" +
	"$proxymanager/dns/v1/backoffice.proto\x12\x13proxymanager.dns.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\x1a\x19algoenum/v1/ip_type.proto\"\x8c\x02\n" +
	"#BackofficeDNSServiceFetchDNSRequest\x12\x15\n" +
	"\x06id_dns\x18\x01 \x01(\tR\x05idDns\x12\x1f\n" +
	"\vname_search\x18\x02 \x01(\tR\n" +
	"nameSearch\x12\x1b\n" +
	"\tip_search\x18\x03 \x01(\tR\bipSearch\x12,\n" +
	"\aip_type\x18\x04 \x01(\x0e2\x13.algoenum.v1.IPTypeR\x06ipType\x12%\n" +
	"\x05state\x18\x05 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x06 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xd2\x01\n" +
	"$BackofficeDNSServiceFetchDNSResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12=\n" +
	"\blist_dns\x18\x03 \x03(\v2\".proxymanager.dns.v1.BackofficeDNSR\alistDns\"\xad\x01\n" +
	"\rBackofficeDNS\x12\x15\n" +
	"\x06id_dns\x18\x01 \x01(\tR\x05idDns\x12,\n" +
	"\aip_type\x18\x02 \x01(\x0e2\x13.algoenum.v1.IPTypeR\x06ipType\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x12\n" +
	"\x04dns1\x18\x04 \x01(\tR\x04dns1\x12\x12\n" +
	"\x04dns2\x18\x05 \x01(\tR\x04dns2\x12\x1b\n" +
	"\tis_active\x18\x06 \x01(\bR\bisActive\"b\n" +
	"$BackofficeDNSServiceCreateDNSRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04dns1\x18\x02 \x01(\tR\x04dns1\x12\x12\n" +
	"\x04dns2\x18\x03 \x01(\tR\x04dns2\"V\n" +
	"%BackofficeDNSServiceCreateDNSResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xa0\x01\n" +
	"$BackofficeDNSServiceUpdateDNSRequest\x12\x15\n" +
	"\x06id_dns\x18\x01 \x01(\tR\x05idDns\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04dns1\x18\x03 \x01(\tR\x04dns1\x12\x12\n" +
	"\x04dns2\x18\x04 \x01(\tR\x04dns2\x12%\n" +
	"\x05state\x18\x05 \x01(\v2\x0f.utils.v1.StateR\x05state\"V\n" +
	"%BackofficeDNSServiceUpdateDNSResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error2\xa1\x03\n" +
	"\x14BackofficeDNSService\x12\x7f\n" +
	"\bFetchDNS\x128.proxymanager.dns.v1.BackofficeDNSServiceFetchDNSRequest\x1a9.proxymanager.dns.v1.BackofficeDNSServiceFetchDNSResponse\x12\x82\x01\n" +
	"\tCreateDNS\x129.proxymanager.dns.v1.BackofficeDNSServiceCreateDNSRequest\x1a:.proxymanager.dns.v1.BackofficeDNSServiceCreateDNSResponse\x12\x82\x01\n" +
	"\tUpdateDNS\x129.proxymanager.dns.v1.BackofficeDNSServiceUpdateDNSRequest\x1a:.proxymanager.dns.v1.BackofficeDNSServiceUpdateDNSResponseBJZHgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/dns/v1;dnsv1b\x06proto3"

var (
	file_proxymanager_dns_v1_backoffice_proto_rawDescOnce sync.Once
	file_proxymanager_dns_v1_backoffice_proto_rawDescData []byte
)

func file_proxymanager_dns_v1_backoffice_proto_rawDescGZIP() []byte {
	file_proxymanager_dns_v1_backoffice_proto_rawDescOnce.Do(func() {
		file_proxymanager_dns_v1_backoffice_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_dns_v1_backoffice_proto_rawDesc), len(file_proxymanager_dns_v1_backoffice_proto_rawDesc)))
	})
	return file_proxymanager_dns_v1_backoffice_proto_rawDescData
}

var file_proxymanager_dns_v1_backoffice_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_proxymanager_dns_v1_backoffice_proto_goTypes = []any{
	(*BackofficeDNSServiceFetchDNSRequest)(nil),   // 0: proxymanager.dns.v1.BackofficeDNSServiceFetchDNSRequest
	(*BackofficeDNSServiceFetchDNSResponse)(nil),  // 1: proxymanager.dns.v1.BackofficeDNSServiceFetchDNSResponse
	(*BackofficeDNS)(nil),                         // 2: proxymanager.dns.v1.BackofficeDNS
	(*BackofficeDNSServiceCreateDNSRequest)(nil),  // 3: proxymanager.dns.v1.BackofficeDNSServiceCreateDNSRequest
	(*BackofficeDNSServiceCreateDNSResponse)(nil), // 4: proxymanager.dns.v1.BackofficeDNSServiceCreateDNSResponse
	(*BackofficeDNSServiceUpdateDNSRequest)(nil),  // 5: proxymanager.dns.v1.BackofficeDNSServiceUpdateDNSRequest
	(*BackofficeDNSServiceUpdateDNSResponse)(nil), // 6: proxymanager.dns.v1.BackofficeDNSServiceUpdateDNSResponse
	(v1.IPType)(0),                 // 7: algoenum.v1.IPType
	(*v11.State)(nil),              // 8: utils.v1.State
	(*v11.PaginationRequest)(nil),  // 9: utils.v1.PaginationRequest
	(*v12.ErrorMessage)(nil),       // 10: errmsg.v1.ErrorMessage
	(*v11.PaginationResponse)(nil), // 11: utils.v1.PaginationResponse
}
var file_proxymanager_dns_v1_backoffice_proto_depIdxs = []int32{
	7,  // 0: proxymanager.dns.v1.BackofficeDNSServiceFetchDNSRequest.ip_type:type_name -> algoenum.v1.IPType
	8,  // 1: proxymanager.dns.v1.BackofficeDNSServiceFetchDNSRequest.state:type_name -> utils.v1.State
	9,  // 2: proxymanager.dns.v1.BackofficeDNSServiceFetchDNSRequest.pagination:type_name -> utils.v1.PaginationRequest
	10, // 3: proxymanager.dns.v1.BackofficeDNSServiceFetchDNSResponse.error:type_name -> errmsg.v1.ErrorMessage
	11, // 4: proxymanager.dns.v1.BackofficeDNSServiceFetchDNSResponse.pagination:type_name -> utils.v1.PaginationResponse
	2,  // 5: proxymanager.dns.v1.BackofficeDNSServiceFetchDNSResponse.list_dns:type_name -> proxymanager.dns.v1.BackofficeDNS
	7,  // 6: proxymanager.dns.v1.BackofficeDNS.ip_type:type_name -> algoenum.v1.IPType
	10, // 7: proxymanager.dns.v1.BackofficeDNSServiceCreateDNSResponse.error:type_name -> errmsg.v1.ErrorMessage
	8,  // 8: proxymanager.dns.v1.BackofficeDNSServiceUpdateDNSRequest.state:type_name -> utils.v1.State
	10, // 9: proxymanager.dns.v1.BackofficeDNSServiceUpdateDNSResponse.error:type_name -> errmsg.v1.ErrorMessage
	0,  // 10: proxymanager.dns.v1.BackofficeDNSService.FetchDNS:input_type -> proxymanager.dns.v1.BackofficeDNSServiceFetchDNSRequest
	3,  // 11: proxymanager.dns.v1.BackofficeDNSService.CreateDNS:input_type -> proxymanager.dns.v1.BackofficeDNSServiceCreateDNSRequest
	5,  // 12: proxymanager.dns.v1.BackofficeDNSService.UpdateDNS:input_type -> proxymanager.dns.v1.BackofficeDNSServiceUpdateDNSRequest
	1,  // 13: proxymanager.dns.v1.BackofficeDNSService.FetchDNS:output_type -> proxymanager.dns.v1.BackofficeDNSServiceFetchDNSResponse
	4,  // 14: proxymanager.dns.v1.BackofficeDNSService.CreateDNS:output_type -> proxymanager.dns.v1.BackofficeDNSServiceCreateDNSResponse
	6,  // 15: proxymanager.dns.v1.BackofficeDNSService.UpdateDNS:output_type -> proxymanager.dns.v1.BackofficeDNSServiceUpdateDNSResponse
	13, // [13:16] is the sub-list for method output_type
	10, // [10:13] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_proxymanager_dns_v1_backoffice_proto_init() }
func file_proxymanager_dns_v1_backoffice_proto_init() {
	if File_proxymanager_dns_v1_backoffice_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_dns_v1_backoffice_proto_rawDesc), len(file_proxymanager_dns_v1_backoffice_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_dns_v1_backoffice_proto_goTypes,
		DependencyIndexes: file_proxymanager_dns_v1_backoffice_proto_depIdxs,
		MessageInfos:      file_proxymanager_dns_v1_backoffice_proto_msgTypes,
	}.Build()
	File_proxymanager_dns_v1_backoffice_proto = out.File
	file_proxymanager_dns_v1_backoffice_proto_goTypes = nil
	file_proxymanager_dns_v1_backoffice_proto_depIdxs = nil
}
