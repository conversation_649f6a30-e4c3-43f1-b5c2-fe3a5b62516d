// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/dns/v1/merchant.proto

package dnsv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MerchantDNSServiceFetchDNSRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdDns         string                 `protobuf:"bytes,1,opt,name=id_dns,json=idDns,proto3" json:"id_dns,omitempty"`
	NameSearch    string                 `protobuf:"bytes,2,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	IpSearch      string                 `protobuf:"bytes,3,opt,name=ip_search,json=ipSearch,proto3" json:"ip_search,omitempty"`
	IpType        v1.IPType              `protobuf:"varint,4,opt,name=ip_type,json=ipType,proto3,enum=algoenum.v1.IPType" json:"ip_type,omitempty"`
	State         *v11.State             `protobuf:"bytes,5,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v11.PaginationRequest `protobuf:"bytes,6,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantDNSServiceFetchDNSRequest) Reset() {
	*x = MerchantDNSServiceFetchDNSRequest{}
	mi := &file_proxymanager_dns_v1_merchant_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantDNSServiceFetchDNSRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantDNSServiceFetchDNSRequest) ProtoMessage() {}

func (x *MerchantDNSServiceFetchDNSRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_dns_v1_merchant_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantDNSServiceFetchDNSRequest.ProtoReflect.Descriptor instead.
func (*MerchantDNSServiceFetchDNSRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_dns_v1_merchant_proto_rawDescGZIP(), []int{0}
}

func (x *MerchantDNSServiceFetchDNSRequest) GetIdDns() string {
	if x != nil {
		return x.IdDns
	}
	return ""
}

func (x *MerchantDNSServiceFetchDNSRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *MerchantDNSServiceFetchDNSRequest) GetIpSearch() string {
	if x != nil {
		return x.IpSearch
	}
	return ""
}

func (x *MerchantDNSServiceFetchDNSRequest) GetIpType() v1.IPType {
	if x != nil {
		return x.IpType
	}
	return v1.IPType(0)
}

func (x *MerchantDNSServiceFetchDNSRequest) GetState() *v11.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *MerchantDNSServiceFetchDNSRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantDNSServiceFetchDNSResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v12.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ListDns       []*MerchantDNS          `protobuf:"bytes,3,rep,name=list_dns,json=listDns,proto3" json:"list_dns,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantDNSServiceFetchDNSResponse) Reset() {
	*x = MerchantDNSServiceFetchDNSResponse{}
	mi := &file_proxymanager_dns_v1_merchant_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantDNSServiceFetchDNSResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantDNSServiceFetchDNSResponse) ProtoMessage() {}

func (x *MerchantDNSServiceFetchDNSResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_dns_v1_merchant_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantDNSServiceFetchDNSResponse.ProtoReflect.Descriptor instead.
func (*MerchantDNSServiceFetchDNSResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_dns_v1_merchant_proto_rawDescGZIP(), []int{1}
}

func (x *MerchantDNSServiceFetchDNSResponse) GetError() *v12.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantDNSServiceFetchDNSResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *MerchantDNSServiceFetchDNSResponse) GetListDns() []*MerchantDNS {
	if x != nil {
		return x.ListDns
	}
	return nil
}

type MerchantDNS struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdDns         string                 `protobuf:"bytes,1,opt,name=id_dns,json=idDns,proto3" json:"id_dns,omitempty"`
	IpType        v1.IPType              `protobuf:"varint,2,opt,name=ip_type,json=ipType,proto3,enum=algoenum.v1.IPType" json:"ip_type,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Dns1          string                 `protobuf:"bytes,4,opt,name=dns1,proto3" json:"dns1,omitempty"`
	Dns2          string                 `protobuf:"bytes,5,opt,name=dns2,proto3" json:"dns2,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantDNS) Reset() {
	*x = MerchantDNS{}
	mi := &file_proxymanager_dns_v1_merchant_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantDNS) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantDNS) ProtoMessage() {}

func (x *MerchantDNS) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_dns_v1_merchant_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantDNS.ProtoReflect.Descriptor instead.
func (*MerchantDNS) Descriptor() ([]byte, []int) {
	return file_proxymanager_dns_v1_merchant_proto_rawDescGZIP(), []int{2}
}

func (x *MerchantDNS) GetIdDns() string {
	if x != nil {
		return x.IdDns
	}
	return ""
}

func (x *MerchantDNS) GetIpType() v1.IPType {
	if x != nil {
		return x.IpType
	}
	return v1.IPType(0)
}

func (x *MerchantDNS) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MerchantDNS) GetDns1() string {
	if x != nil {
		return x.Dns1
	}
	return ""
}

func (x *MerchantDNS) GetDns2() string {
	if x != nil {
		return x.Dns2
	}
	return ""
}

var File_proxymanager_dns_v1_merchant_proto protoreflect.FileDescriptor

const file_proxymanager_dns_v1_merchant_proto_rawDesc = "" +
	"\n" +
	"\"proxymanager/dns/v1/merchant.proto\x12\x13proxymanager.dns.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\x1a\x19algoenum/v1/ip_type.proto\"\x8a\x02\n" +
	"!MerchantDNSServiceFetchDNSRequest\x12\x15\n" +
	"\x06id_dns\x18\x01 \x01(\tR\x05idDns\x12\x1f\n" +
	"\vname_search\x18\x02 \x01(\tR\n" +
	"nameSearch\x12\x1b\n" +
	"\tip_search\x18\x03 \x01(\tR\bipSearch\x12,\n" +
	"\aip_type\x18\x04 \x01(\x0e2\x13.algoenum.v1.IPTypeR\x06ipType\x12%\n" +
	"\x05state\x18\x05 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x06 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xce\x01\n" +
	"\"MerchantDNSServiceFetchDNSResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12;\n" +
	"\blist_dns\x18\x03 \x03(\v2 .proxymanager.dns.v1.MerchantDNSR\alistDns\"\x8e\x01\n" +
	"\vMerchantDNS\x12\x15\n" +
	"\x06id_dns\x18\x01 \x01(\tR\x05idDns\x12,\n" +
	"\aip_type\x18\x02 \x01(\x0e2\x13.algoenum.v1.IPTypeR\x06ipType\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x12\n" +
	"\x04dns1\x18\x04 \x01(\tR\x04dns1\x12\x12\n" +
	"\x04dns2\x18\x05 \x01(\tR\x04dns22\x91\x01\n" +
	"\x12MerchantDNSService\x12{\n" +
	"\bFetchDNS\x126.proxymanager.dns.v1.MerchantDNSServiceFetchDNSRequest\x1a7.proxymanager.dns.v1.MerchantDNSServiceFetchDNSResponseBJZHgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/dns/v1;dnsv1b\x06proto3"

var (
	file_proxymanager_dns_v1_merchant_proto_rawDescOnce sync.Once
	file_proxymanager_dns_v1_merchant_proto_rawDescData []byte
)

func file_proxymanager_dns_v1_merchant_proto_rawDescGZIP() []byte {
	file_proxymanager_dns_v1_merchant_proto_rawDescOnce.Do(func() {
		file_proxymanager_dns_v1_merchant_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_dns_v1_merchant_proto_rawDesc), len(file_proxymanager_dns_v1_merchant_proto_rawDesc)))
	})
	return file_proxymanager_dns_v1_merchant_proto_rawDescData
}

var file_proxymanager_dns_v1_merchant_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_proxymanager_dns_v1_merchant_proto_goTypes = []any{
	(*MerchantDNSServiceFetchDNSRequest)(nil),  // 0: proxymanager.dns.v1.MerchantDNSServiceFetchDNSRequest
	(*MerchantDNSServiceFetchDNSResponse)(nil), // 1: proxymanager.dns.v1.MerchantDNSServiceFetchDNSResponse
	(*MerchantDNS)(nil),                        // 2: proxymanager.dns.v1.MerchantDNS
	(v1.IPType)(0),                             // 3: algoenum.v1.IPType
	(*v11.State)(nil),                          // 4: utils.v1.State
	(*v11.PaginationRequest)(nil),              // 5: utils.v1.PaginationRequest
	(*v12.ErrorMessage)(nil),                   // 6: errmsg.v1.ErrorMessage
	(*v11.PaginationResponse)(nil),             // 7: utils.v1.PaginationResponse
}
var file_proxymanager_dns_v1_merchant_proto_depIdxs = []int32{
	3, // 0: proxymanager.dns.v1.MerchantDNSServiceFetchDNSRequest.ip_type:type_name -> algoenum.v1.IPType
	4, // 1: proxymanager.dns.v1.MerchantDNSServiceFetchDNSRequest.state:type_name -> utils.v1.State
	5, // 2: proxymanager.dns.v1.MerchantDNSServiceFetchDNSRequest.pagination:type_name -> utils.v1.PaginationRequest
	6, // 3: proxymanager.dns.v1.MerchantDNSServiceFetchDNSResponse.error:type_name -> errmsg.v1.ErrorMessage
	7, // 4: proxymanager.dns.v1.MerchantDNSServiceFetchDNSResponse.pagination:type_name -> utils.v1.PaginationResponse
	2, // 5: proxymanager.dns.v1.MerchantDNSServiceFetchDNSResponse.list_dns:type_name -> proxymanager.dns.v1.MerchantDNS
	3, // 6: proxymanager.dns.v1.MerchantDNS.ip_type:type_name -> algoenum.v1.IPType
	0, // 7: proxymanager.dns.v1.MerchantDNSService.FetchDNS:input_type -> proxymanager.dns.v1.MerchantDNSServiceFetchDNSRequest
	1, // 8: proxymanager.dns.v1.MerchantDNSService.FetchDNS:output_type -> proxymanager.dns.v1.MerchantDNSServiceFetchDNSResponse
	8, // [8:9] is the sub-list for method output_type
	7, // [7:8] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_proxymanager_dns_v1_merchant_proto_init() }
func file_proxymanager_dns_v1_merchant_proto_init() {
	if File_proxymanager_dns_v1_merchant_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_dns_v1_merchant_proto_rawDesc), len(file_proxymanager_dns_v1_merchant_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_dns_v1_merchant_proto_goTypes,
		DependencyIndexes: file_proxymanager_dns_v1_merchant_proto_depIdxs,
		MessageInfos:      file_proxymanager_dns_v1_merchant_proto_msgTypes,
	}.Build()
	File_proxymanager_dns_v1_merchant_proto = out.File
	file_proxymanager_dns_v1_merchant_proto_goTypes = nil
	file_proxymanager_dns_v1_merchant_proto_depIdxs = nil
}
