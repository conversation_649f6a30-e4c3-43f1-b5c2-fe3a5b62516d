// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/dns/v1/customer.proto

package dnsv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CustomerDNSServiceFetchDNSRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdDns         string                 `protobuf:"bytes,1,opt,name=id_dns,json=idDns,proto3" json:"id_dns,omitempty"`
	NameSearch    string                 `protobuf:"bytes,2,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	IpSearch      string                 `protobuf:"bytes,3,opt,name=ip_search,json=ipSearch,proto3" json:"ip_search,omitempty"`
	IpType        v1.IPType              `protobuf:"varint,4,opt,name=ip_type,json=ipType,proto3,enum=algoenum.v1.IPType" json:"ip_type,omitempty"`
	State         *v11.State             `protobuf:"bytes,5,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v11.PaginationRequest `protobuf:"bytes,6,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerDNSServiceFetchDNSRequest) Reset() {
	*x = CustomerDNSServiceFetchDNSRequest{}
	mi := &file_proxymanager_dns_v1_customer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerDNSServiceFetchDNSRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerDNSServiceFetchDNSRequest) ProtoMessage() {}

func (x *CustomerDNSServiceFetchDNSRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_dns_v1_customer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerDNSServiceFetchDNSRequest.ProtoReflect.Descriptor instead.
func (*CustomerDNSServiceFetchDNSRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_dns_v1_customer_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerDNSServiceFetchDNSRequest) GetIdDns() string {
	if x != nil {
		return x.IdDns
	}
	return ""
}

func (x *CustomerDNSServiceFetchDNSRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *CustomerDNSServiceFetchDNSRequest) GetIpSearch() string {
	if x != nil {
		return x.IpSearch
	}
	return ""
}

func (x *CustomerDNSServiceFetchDNSRequest) GetIpType() v1.IPType {
	if x != nil {
		return x.IpType
	}
	return v1.IPType(0)
}

func (x *CustomerDNSServiceFetchDNSRequest) GetState() *v11.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *CustomerDNSServiceFetchDNSRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerDNSServiceFetchDNSResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v12.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ListDns       []*CustomerDNS          `protobuf:"bytes,3,rep,name=list_dns,json=listDns,proto3" json:"list_dns,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerDNSServiceFetchDNSResponse) Reset() {
	*x = CustomerDNSServiceFetchDNSResponse{}
	mi := &file_proxymanager_dns_v1_customer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerDNSServiceFetchDNSResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerDNSServiceFetchDNSResponse) ProtoMessage() {}

func (x *CustomerDNSServiceFetchDNSResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_dns_v1_customer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerDNSServiceFetchDNSResponse.ProtoReflect.Descriptor instead.
func (*CustomerDNSServiceFetchDNSResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_dns_v1_customer_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerDNSServiceFetchDNSResponse) GetError() *v12.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerDNSServiceFetchDNSResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *CustomerDNSServiceFetchDNSResponse) GetListDns() []*CustomerDNS {
	if x != nil {
		return x.ListDns
	}
	return nil
}

type CustomerDNS struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdDns         string                 `protobuf:"bytes,1,opt,name=id_dns,json=idDns,proto3" json:"id_dns,omitempty"`
	IpType        v1.IPType              `protobuf:"varint,2,opt,name=ip_type,json=ipType,proto3,enum=algoenum.v1.IPType" json:"ip_type,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerDNS) Reset() {
	*x = CustomerDNS{}
	mi := &file_proxymanager_dns_v1_customer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerDNS) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerDNS) ProtoMessage() {}

func (x *CustomerDNS) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_dns_v1_customer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerDNS.ProtoReflect.Descriptor instead.
func (*CustomerDNS) Descriptor() ([]byte, []int) {
	return file_proxymanager_dns_v1_customer_proto_rawDescGZIP(), []int{2}
}

func (x *CustomerDNS) GetIdDns() string {
	if x != nil {
		return x.IdDns
	}
	return ""
}

func (x *CustomerDNS) GetIpType() v1.IPType {
	if x != nil {
		return x.IpType
	}
	return v1.IPType(0)
}

func (x *CustomerDNS) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_proxymanager_dns_v1_customer_proto protoreflect.FileDescriptor

const file_proxymanager_dns_v1_customer_proto_rawDesc = "" +
	"\n" +
	"\"proxymanager/dns/v1/customer.proto\x12\x13proxymanager.dns.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\x1a\x19algoenum/v1/ip_type.proto\"\x8a\x02\n" +
	"!CustomerDNSServiceFetchDNSRequest\x12\x15\n" +
	"\x06id_dns\x18\x01 \x01(\tR\x05idDns\x12\x1f\n" +
	"\vname_search\x18\x02 \x01(\tR\n" +
	"nameSearch\x12\x1b\n" +
	"\tip_search\x18\x03 \x01(\tR\bipSearch\x12,\n" +
	"\aip_type\x18\x04 \x01(\x0e2\x13.algoenum.v1.IPTypeR\x06ipType\x12%\n" +
	"\x05state\x18\x05 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x06 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xce\x01\n" +
	"\"CustomerDNSServiceFetchDNSResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12;\n" +
	"\blist_dns\x18\x03 \x03(\v2 .proxymanager.dns.v1.CustomerDNSR\alistDns\"f\n" +
	"\vCustomerDNS\x12\x15\n" +
	"\x06id_dns\x18\x01 \x01(\tR\x05idDns\x12,\n" +
	"\aip_type\x18\x02 \x01(\x0e2\x13.algoenum.v1.IPTypeR\x06ipType\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name2\x91\x01\n" +
	"\x12CustomerDNSService\x12{\n" +
	"\bFetchDNS\x126.proxymanager.dns.v1.CustomerDNSServiceFetchDNSRequest\x1a7.proxymanager.dns.v1.CustomerDNSServiceFetchDNSResponseBJZHgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/dns/v1;dnsv1b\x06proto3"

var (
	file_proxymanager_dns_v1_customer_proto_rawDescOnce sync.Once
	file_proxymanager_dns_v1_customer_proto_rawDescData []byte
)

func file_proxymanager_dns_v1_customer_proto_rawDescGZIP() []byte {
	file_proxymanager_dns_v1_customer_proto_rawDescOnce.Do(func() {
		file_proxymanager_dns_v1_customer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_dns_v1_customer_proto_rawDesc), len(file_proxymanager_dns_v1_customer_proto_rawDesc)))
	})
	return file_proxymanager_dns_v1_customer_proto_rawDescData
}

var file_proxymanager_dns_v1_customer_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_proxymanager_dns_v1_customer_proto_goTypes = []any{
	(*CustomerDNSServiceFetchDNSRequest)(nil),  // 0: proxymanager.dns.v1.CustomerDNSServiceFetchDNSRequest
	(*CustomerDNSServiceFetchDNSResponse)(nil), // 1: proxymanager.dns.v1.CustomerDNSServiceFetchDNSResponse
	(*CustomerDNS)(nil),                        // 2: proxymanager.dns.v1.CustomerDNS
	(v1.IPType)(0),                             // 3: algoenum.v1.IPType
	(*v11.State)(nil),                          // 4: utils.v1.State
	(*v11.PaginationRequest)(nil),              // 5: utils.v1.PaginationRequest
	(*v12.ErrorMessage)(nil),                   // 6: errmsg.v1.ErrorMessage
	(*v11.PaginationResponse)(nil),             // 7: utils.v1.PaginationResponse
}
var file_proxymanager_dns_v1_customer_proto_depIdxs = []int32{
	3, // 0: proxymanager.dns.v1.CustomerDNSServiceFetchDNSRequest.ip_type:type_name -> algoenum.v1.IPType
	4, // 1: proxymanager.dns.v1.CustomerDNSServiceFetchDNSRequest.state:type_name -> utils.v1.State
	5, // 2: proxymanager.dns.v1.CustomerDNSServiceFetchDNSRequest.pagination:type_name -> utils.v1.PaginationRequest
	6, // 3: proxymanager.dns.v1.CustomerDNSServiceFetchDNSResponse.error:type_name -> errmsg.v1.ErrorMessage
	7, // 4: proxymanager.dns.v1.CustomerDNSServiceFetchDNSResponse.pagination:type_name -> utils.v1.PaginationResponse
	2, // 5: proxymanager.dns.v1.CustomerDNSServiceFetchDNSResponse.list_dns:type_name -> proxymanager.dns.v1.CustomerDNS
	3, // 6: proxymanager.dns.v1.CustomerDNS.ip_type:type_name -> algoenum.v1.IPType
	0, // 7: proxymanager.dns.v1.CustomerDNSService.FetchDNS:input_type -> proxymanager.dns.v1.CustomerDNSServiceFetchDNSRequest
	1, // 8: proxymanager.dns.v1.CustomerDNSService.FetchDNS:output_type -> proxymanager.dns.v1.CustomerDNSServiceFetchDNSResponse
	8, // [8:9] is the sub-list for method output_type
	7, // [7:8] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_proxymanager_dns_v1_customer_proto_init() }
func file_proxymanager_dns_v1_customer_proto_init() {
	if File_proxymanager_dns_v1_customer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_dns_v1_customer_proto_rawDesc), len(file_proxymanager_dns_v1_customer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_dns_v1_customer_proto_goTypes,
		DependencyIndexes: file_proxymanager_dns_v1_customer_proto_depIdxs,
		MessageInfos:      file_proxymanager_dns_v1_customer_proto_msgTypes,
	}.Build()
	File_proxymanager_dns_v1_customer_proto = out.File
	file_proxymanager_dns_v1_customer_proto_goTypes = nil
	file_proxymanager_dns_v1_customer_proto_depIdxs = nil
}
