// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/dns/v1/customer.proto

package dnsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/dns/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CustomerDNSServiceName is the fully-qualified name of the CustomerDNSService service.
	CustomerDNSServiceName = "proxymanager.dns.v1.CustomerDNSService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CustomerDNSServiceFetchDNSProcedure is the fully-qualified name of the CustomerDNSService's
	// FetchDNS RPC.
	CustomerDNSServiceFetchDNSProcedure = "/proxymanager.dns.v1.CustomerDNSService/FetchDNS"
)

// CustomerDNSServiceClient is a client for the proxymanager.dns.v1.CustomerDNSService service.
type CustomerDNSServiceClient interface {
	FetchDNS(context.Context, *connect.Request[v1.CustomerDNSServiceFetchDNSRequest]) (*connect.Response[v1.CustomerDNSServiceFetchDNSResponse], error)
}

// NewCustomerDNSServiceClient constructs a client for the proxymanager.dns.v1.CustomerDNSService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCustomerDNSServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CustomerDNSServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	customerDNSServiceMethods := v1.File_proxymanager_dns_v1_customer_proto.Services().ByName("CustomerDNSService").Methods()
	return &customerDNSServiceClient{
		fetchDNS: connect.NewClient[v1.CustomerDNSServiceFetchDNSRequest, v1.CustomerDNSServiceFetchDNSResponse](
			httpClient,
			baseURL+CustomerDNSServiceFetchDNSProcedure,
			connect.WithSchema(customerDNSServiceMethods.ByName("FetchDNS")),
			connect.WithClientOptions(opts...),
		),
	}
}

// customerDNSServiceClient implements CustomerDNSServiceClient.
type customerDNSServiceClient struct {
	fetchDNS *connect.Client[v1.CustomerDNSServiceFetchDNSRequest, v1.CustomerDNSServiceFetchDNSResponse]
}

// FetchDNS calls proxymanager.dns.v1.CustomerDNSService.FetchDNS.
func (c *customerDNSServiceClient) FetchDNS(ctx context.Context, req *connect.Request[v1.CustomerDNSServiceFetchDNSRequest]) (*connect.Response[v1.CustomerDNSServiceFetchDNSResponse], error) {
	return c.fetchDNS.CallUnary(ctx, req)
}

// CustomerDNSServiceHandler is an implementation of the proxymanager.dns.v1.CustomerDNSService
// service.
type CustomerDNSServiceHandler interface {
	FetchDNS(context.Context, *connect.Request[v1.CustomerDNSServiceFetchDNSRequest]) (*connect.Response[v1.CustomerDNSServiceFetchDNSResponse], error)
}

// NewCustomerDNSServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCustomerDNSServiceHandler(svc CustomerDNSServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	customerDNSServiceMethods := v1.File_proxymanager_dns_v1_customer_proto.Services().ByName("CustomerDNSService").Methods()
	customerDNSServiceFetchDNSHandler := connect.NewUnaryHandler(
		CustomerDNSServiceFetchDNSProcedure,
		svc.FetchDNS,
		connect.WithSchema(customerDNSServiceMethods.ByName("FetchDNS")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.dns.v1.CustomerDNSService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CustomerDNSServiceFetchDNSProcedure:
			customerDNSServiceFetchDNSHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCustomerDNSServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCustomerDNSServiceHandler struct{}

func (UnimplementedCustomerDNSServiceHandler) FetchDNS(context.Context, *connect.Request[v1.CustomerDNSServiceFetchDNSRequest]) (*connect.Response[v1.CustomerDNSServiceFetchDNSResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.dns.v1.CustomerDNSService.FetchDNS is not implemented"))
}
