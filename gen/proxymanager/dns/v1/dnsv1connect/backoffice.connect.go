// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/dns/v1/backoffice.proto

package dnsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/dns/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackofficeDNSServiceName is the fully-qualified name of the BackofficeDNSService service.
	BackofficeDNSServiceName = "proxymanager.dns.v1.BackofficeDNSService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackofficeDNSServiceFetchDNSProcedure is the fully-qualified name of the BackofficeDNSService's
	// FetchDNS RPC.
	BackofficeDNSServiceFetchDNSProcedure = "/proxymanager.dns.v1.BackofficeDNSService/FetchDNS"
	// BackofficeDNSServiceCreateDNSProcedure is the fully-qualified name of the BackofficeDNSService's
	// CreateDNS RPC.
	BackofficeDNSServiceCreateDNSProcedure = "/proxymanager.dns.v1.BackofficeDNSService/CreateDNS"
	// BackofficeDNSServiceUpdateDNSProcedure is the fully-qualified name of the BackofficeDNSService's
	// UpdateDNS RPC.
	BackofficeDNSServiceUpdateDNSProcedure = "/proxymanager.dns.v1.BackofficeDNSService/UpdateDNS"
)

// BackofficeDNSServiceClient is a client for the proxymanager.dns.v1.BackofficeDNSService service.
type BackofficeDNSServiceClient interface {
	FetchDNS(context.Context, *connect.Request[v1.BackofficeDNSServiceFetchDNSRequest]) (*connect.Response[v1.BackofficeDNSServiceFetchDNSResponse], error)
	CreateDNS(context.Context, *connect.Request[v1.BackofficeDNSServiceCreateDNSRequest]) (*connect.Response[v1.BackofficeDNSServiceCreateDNSResponse], error)
	UpdateDNS(context.Context, *connect.Request[v1.BackofficeDNSServiceUpdateDNSRequest]) (*connect.Response[v1.BackofficeDNSServiceUpdateDNSResponse], error)
}

// NewBackofficeDNSServiceClient constructs a client for the
// proxymanager.dns.v1.BackofficeDNSService service. By default, it uses the Connect protocol with
// the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed requests. To use
// the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackofficeDNSServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackofficeDNSServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backofficeDNSServiceMethods := v1.File_proxymanager_dns_v1_backoffice_proto.Services().ByName("BackofficeDNSService").Methods()
	return &backofficeDNSServiceClient{
		fetchDNS: connect.NewClient[v1.BackofficeDNSServiceFetchDNSRequest, v1.BackofficeDNSServiceFetchDNSResponse](
			httpClient,
			baseURL+BackofficeDNSServiceFetchDNSProcedure,
			connect.WithSchema(backofficeDNSServiceMethods.ByName("FetchDNS")),
			connect.WithClientOptions(opts...),
		),
		createDNS: connect.NewClient[v1.BackofficeDNSServiceCreateDNSRequest, v1.BackofficeDNSServiceCreateDNSResponse](
			httpClient,
			baseURL+BackofficeDNSServiceCreateDNSProcedure,
			connect.WithSchema(backofficeDNSServiceMethods.ByName("CreateDNS")),
			connect.WithClientOptions(opts...),
		),
		updateDNS: connect.NewClient[v1.BackofficeDNSServiceUpdateDNSRequest, v1.BackofficeDNSServiceUpdateDNSResponse](
			httpClient,
			baseURL+BackofficeDNSServiceUpdateDNSProcedure,
			connect.WithSchema(backofficeDNSServiceMethods.ByName("UpdateDNS")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backofficeDNSServiceClient implements BackofficeDNSServiceClient.
type backofficeDNSServiceClient struct {
	fetchDNS  *connect.Client[v1.BackofficeDNSServiceFetchDNSRequest, v1.BackofficeDNSServiceFetchDNSResponse]
	createDNS *connect.Client[v1.BackofficeDNSServiceCreateDNSRequest, v1.BackofficeDNSServiceCreateDNSResponse]
	updateDNS *connect.Client[v1.BackofficeDNSServiceUpdateDNSRequest, v1.BackofficeDNSServiceUpdateDNSResponse]
}

// FetchDNS calls proxymanager.dns.v1.BackofficeDNSService.FetchDNS.
func (c *backofficeDNSServiceClient) FetchDNS(ctx context.Context, req *connect.Request[v1.BackofficeDNSServiceFetchDNSRequest]) (*connect.Response[v1.BackofficeDNSServiceFetchDNSResponse], error) {
	return c.fetchDNS.CallUnary(ctx, req)
}

// CreateDNS calls proxymanager.dns.v1.BackofficeDNSService.CreateDNS.
func (c *backofficeDNSServiceClient) CreateDNS(ctx context.Context, req *connect.Request[v1.BackofficeDNSServiceCreateDNSRequest]) (*connect.Response[v1.BackofficeDNSServiceCreateDNSResponse], error) {
	return c.createDNS.CallUnary(ctx, req)
}

// UpdateDNS calls proxymanager.dns.v1.BackofficeDNSService.UpdateDNS.
func (c *backofficeDNSServiceClient) UpdateDNS(ctx context.Context, req *connect.Request[v1.BackofficeDNSServiceUpdateDNSRequest]) (*connect.Response[v1.BackofficeDNSServiceUpdateDNSResponse], error) {
	return c.updateDNS.CallUnary(ctx, req)
}

// BackofficeDNSServiceHandler is an implementation of the proxymanager.dns.v1.BackofficeDNSService
// service.
type BackofficeDNSServiceHandler interface {
	FetchDNS(context.Context, *connect.Request[v1.BackofficeDNSServiceFetchDNSRequest]) (*connect.Response[v1.BackofficeDNSServiceFetchDNSResponse], error)
	CreateDNS(context.Context, *connect.Request[v1.BackofficeDNSServiceCreateDNSRequest]) (*connect.Response[v1.BackofficeDNSServiceCreateDNSResponse], error)
	UpdateDNS(context.Context, *connect.Request[v1.BackofficeDNSServiceUpdateDNSRequest]) (*connect.Response[v1.BackofficeDNSServiceUpdateDNSResponse], error)
}

// NewBackofficeDNSServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackofficeDNSServiceHandler(svc BackofficeDNSServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backofficeDNSServiceMethods := v1.File_proxymanager_dns_v1_backoffice_proto.Services().ByName("BackofficeDNSService").Methods()
	backofficeDNSServiceFetchDNSHandler := connect.NewUnaryHandler(
		BackofficeDNSServiceFetchDNSProcedure,
		svc.FetchDNS,
		connect.WithSchema(backofficeDNSServiceMethods.ByName("FetchDNS")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeDNSServiceCreateDNSHandler := connect.NewUnaryHandler(
		BackofficeDNSServiceCreateDNSProcedure,
		svc.CreateDNS,
		connect.WithSchema(backofficeDNSServiceMethods.ByName("CreateDNS")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeDNSServiceUpdateDNSHandler := connect.NewUnaryHandler(
		BackofficeDNSServiceUpdateDNSProcedure,
		svc.UpdateDNS,
		connect.WithSchema(backofficeDNSServiceMethods.ByName("UpdateDNS")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.dns.v1.BackofficeDNSService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackofficeDNSServiceFetchDNSProcedure:
			backofficeDNSServiceFetchDNSHandler.ServeHTTP(w, r)
		case BackofficeDNSServiceCreateDNSProcedure:
			backofficeDNSServiceCreateDNSHandler.ServeHTTP(w, r)
		case BackofficeDNSServiceUpdateDNSProcedure:
			backofficeDNSServiceUpdateDNSHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackofficeDNSServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackofficeDNSServiceHandler struct{}

func (UnimplementedBackofficeDNSServiceHandler) FetchDNS(context.Context, *connect.Request[v1.BackofficeDNSServiceFetchDNSRequest]) (*connect.Response[v1.BackofficeDNSServiceFetchDNSResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.dns.v1.BackofficeDNSService.FetchDNS is not implemented"))
}

func (UnimplementedBackofficeDNSServiceHandler) CreateDNS(context.Context, *connect.Request[v1.BackofficeDNSServiceCreateDNSRequest]) (*connect.Response[v1.BackofficeDNSServiceCreateDNSResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.dns.v1.BackofficeDNSService.CreateDNS is not implemented"))
}

func (UnimplementedBackofficeDNSServiceHandler) UpdateDNS(context.Context, *connect.Request[v1.BackofficeDNSServiceUpdateDNSRequest]) (*connect.Response[v1.BackofficeDNSServiceUpdateDNSResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.dns.v1.BackofficeDNSService.UpdateDNS is not implemented"))
}
