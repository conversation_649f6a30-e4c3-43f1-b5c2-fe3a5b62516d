// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/dns/v1/merchant.proto

package dnsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/dns/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// MerchantDNSServiceName is the fully-qualified name of the MerchantDNSService service.
	MerchantDNSServiceName = "proxymanager.dns.v1.MerchantDNSService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// MerchantDNSServiceFetchDNSProcedure is the fully-qualified name of the MerchantDNSService's
	// FetchDNS RPC.
	MerchantDNSServiceFetchDNSProcedure = "/proxymanager.dns.v1.MerchantDNSService/FetchDNS"
)

// MerchantDNSServiceClient is a client for the proxymanager.dns.v1.MerchantDNSService service.
type MerchantDNSServiceClient interface {
	FetchDNS(context.Context, *connect.Request[v1.MerchantDNSServiceFetchDNSRequest]) (*connect.Response[v1.MerchantDNSServiceFetchDNSResponse], error)
}

// NewMerchantDNSServiceClient constructs a client for the proxymanager.dns.v1.MerchantDNSService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewMerchantDNSServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) MerchantDNSServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	merchantDNSServiceMethods := v1.File_proxymanager_dns_v1_merchant_proto.Services().ByName("MerchantDNSService").Methods()
	return &merchantDNSServiceClient{
		fetchDNS: connect.NewClient[v1.MerchantDNSServiceFetchDNSRequest, v1.MerchantDNSServiceFetchDNSResponse](
			httpClient,
			baseURL+MerchantDNSServiceFetchDNSProcedure,
			connect.WithSchema(merchantDNSServiceMethods.ByName("FetchDNS")),
			connect.WithClientOptions(opts...),
		),
	}
}

// merchantDNSServiceClient implements MerchantDNSServiceClient.
type merchantDNSServiceClient struct {
	fetchDNS *connect.Client[v1.MerchantDNSServiceFetchDNSRequest, v1.MerchantDNSServiceFetchDNSResponse]
}

// FetchDNS calls proxymanager.dns.v1.MerchantDNSService.FetchDNS.
func (c *merchantDNSServiceClient) FetchDNS(ctx context.Context, req *connect.Request[v1.MerchantDNSServiceFetchDNSRequest]) (*connect.Response[v1.MerchantDNSServiceFetchDNSResponse], error) {
	return c.fetchDNS.CallUnary(ctx, req)
}

// MerchantDNSServiceHandler is an implementation of the proxymanager.dns.v1.MerchantDNSService
// service.
type MerchantDNSServiceHandler interface {
	FetchDNS(context.Context, *connect.Request[v1.MerchantDNSServiceFetchDNSRequest]) (*connect.Response[v1.MerchantDNSServiceFetchDNSResponse], error)
}

// NewMerchantDNSServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewMerchantDNSServiceHandler(svc MerchantDNSServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	merchantDNSServiceMethods := v1.File_proxymanager_dns_v1_merchant_proto.Services().ByName("MerchantDNSService").Methods()
	merchantDNSServiceFetchDNSHandler := connect.NewUnaryHandler(
		MerchantDNSServiceFetchDNSProcedure,
		svc.FetchDNS,
		connect.WithSchema(merchantDNSServiceMethods.ByName("FetchDNS")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.dns.v1.MerchantDNSService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case MerchantDNSServiceFetchDNSProcedure:
			merchantDNSServiceFetchDNSHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedMerchantDNSServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedMerchantDNSServiceHandler struct{}

func (UnimplementedMerchantDNSServiceHandler) FetchDNS(context.Context, *connect.Request[v1.MerchantDNSServiceFetchDNSRequest]) (*connect.Response[v1.MerchantDNSServiceFetchDNSResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.dns.v1.MerchantDNSService.FetchDNS is not implemented"))
}
