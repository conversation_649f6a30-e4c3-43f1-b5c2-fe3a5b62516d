// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/dns/v1/controlplane.proto

package dnsv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/dns/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// DNSManagerServiceName is the fully-qualified name of the DNSManagerService service.
	DNSManagerServiceName = "proxymanager.dns.v1.DNSManagerService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// DNSManagerServiceGetBlockedDomainProcedure is the fully-qualified name of the DNSManagerService's
	// GetBlockedDomain RPC.
	DNSManagerServiceGetBlockedDomainProcedure = "/proxymanager.dns.v1.DNSManagerService/GetBlockedDomain"
)

// DNSManagerServiceClient is a client for the proxymanager.dns.v1.DNSManagerService service.
type DNSManagerServiceClient interface {
	GetBlockedDomain(context.Context, *connect.Request[v1.GetBlockedDomainRequest]) (*connect.Response[v1.GetBlockedDomainResponse], error)
}

// NewDNSManagerServiceClient constructs a client for the proxymanager.dns.v1.DNSManagerService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewDNSManagerServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) DNSManagerServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	dNSManagerServiceMethods := v1.File_proxymanager_dns_v1_controlplane_proto.Services().ByName("DNSManagerService").Methods()
	return &dNSManagerServiceClient{
		getBlockedDomain: connect.NewClient[v1.GetBlockedDomainRequest, v1.GetBlockedDomainResponse](
			httpClient,
			baseURL+DNSManagerServiceGetBlockedDomainProcedure,
			connect.WithSchema(dNSManagerServiceMethods.ByName("GetBlockedDomain")),
			connect.WithClientOptions(opts...),
		),
	}
}

// dNSManagerServiceClient implements DNSManagerServiceClient.
type dNSManagerServiceClient struct {
	getBlockedDomain *connect.Client[v1.GetBlockedDomainRequest, v1.GetBlockedDomainResponse]
}

// GetBlockedDomain calls proxymanager.dns.v1.DNSManagerService.GetBlockedDomain.
func (c *dNSManagerServiceClient) GetBlockedDomain(ctx context.Context, req *connect.Request[v1.GetBlockedDomainRequest]) (*connect.Response[v1.GetBlockedDomainResponse], error) {
	return c.getBlockedDomain.CallUnary(ctx, req)
}

// DNSManagerServiceHandler is an implementation of the proxymanager.dns.v1.DNSManagerService
// service.
type DNSManagerServiceHandler interface {
	GetBlockedDomain(context.Context, *connect.Request[v1.GetBlockedDomainRequest]) (*connect.Response[v1.GetBlockedDomainResponse], error)
}

// NewDNSManagerServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewDNSManagerServiceHandler(svc DNSManagerServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	dNSManagerServiceMethods := v1.File_proxymanager_dns_v1_controlplane_proto.Services().ByName("DNSManagerService").Methods()
	dNSManagerServiceGetBlockedDomainHandler := connect.NewUnaryHandler(
		DNSManagerServiceGetBlockedDomainProcedure,
		svc.GetBlockedDomain,
		connect.WithSchema(dNSManagerServiceMethods.ByName("GetBlockedDomain")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.dns.v1.DNSManagerService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case DNSManagerServiceGetBlockedDomainProcedure:
			dNSManagerServiceGetBlockedDomainHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedDNSManagerServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedDNSManagerServiceHandler struct{}

func (UnimplementedDNSManagerServiceHandler) GetBlockedDomain(context.Context, *connect.Request[v1.GetBlockedDomainRequest]) (*connect.Response[v1.GetBlockedDomainResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.dns.v1.DNSManagerService.GetBlockedDomain is not implemented"))
}
