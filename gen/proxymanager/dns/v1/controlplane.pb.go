// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/dns/v1/controlplane.proto

package dnsv1

import (
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetBlockedDomainRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBlockedDomainRequest) Reset() {
	*x = GetBlockedDomainRequest{}
	mi := &file_proxymanager_dns_v1_controlplane_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBlockedDomainRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBlockedDomainRequest) ProtoMessage() {}

func (x *GetBlockedDomainRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_dns_v1_controlplane_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBlockedDomainRequest.ProtoReflect.Descriptor instead.
func (*GetBlockedDomainRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_dns_v1_controlplane_proto_rawDescGZIP(), []int{0}
}

func (x *GetBlockedDomainRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type GetBlockedDomainResponse struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Error             *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination        *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ListBlockedDomain []string               `protobuf:"bytes,3,rep,name=list_blocked_domain,json=listBlockedDomain,proto3" json:"list_blocked_domain,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *GetBlockedDomainResponse) Reset() {
	*x = GetBlockedDomainResponse{}
	mi := &file_proxymanager_dns_v1_controlplane_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBlockedDomainResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBlockedDomainResponse) ProtoMessage() {}

func (x *GetBlockedDomainResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_dns_v1_controlplane_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBlockedDomainResponse.ProtoReflect.Descriptor instead.
func (*GetBlockedDomainResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_dns_v1_controlplane_proto_rawDescGZIP(), []int{1}
}

func (x *GetBlockedDomainResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GetBlockedDomainResponse) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *GetBlockedDomainResponse) GetListBlockedDomain() []string {
	if x != nil {
		return x.ListBlockedDomain
	}
	return nil
}

var File_proxymanager_dns_v1_controlplane_proto protoreflect.FileDescriptor

const file_proxymanager_dns_v1_controlplane_proto_rawDesc = "" +
	"\n" +
	"&proxymanager/dns/v1/controlplane.proto\x12\x13proxymanager.dns.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\"V\n" +
	"\x17GetBlockedDomainRequest\x12;\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xb6\x01\n" +
	"\x18GetBlockedDomainResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\x12.\n" +
	"\x13list_blocked_domain\x18\x03 \x03(\tR\x11listBlockedDomain2\x84\x01\n" +
	"\x11DNSManagerService\x12o\n" +
	"\x10GetBlockedDomain\x12,.proxymanager.dns.v1.GetBlockedDomainRequest\x1a-.proxymanager.dns.v1.GetBlockedDomainResponseBJZHgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/dns/v1;dnsv1b\x06proto3"

var (
	file_proxymanager_dns_v1_controlplane_proto_rawDescOnce sync.Once
	file_proxymanager_dns_v1_controlplane_proto_rawDescData []byte
)

func file_proxymanager_dns_v1_controlplane_proto_rawDescGZIP() []byte {
	file_proxymanager_dns_v1_controlplane_proto_rawDescOnce.Do(func() {
		file_proxymanager_dns_v1_controlplane_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_dns_v1_controlplane_proto_rawDesc), len(file_proxymanager_dns_v1_controlplane_proto_rawDesc)))
	})
	return file_proxymanager_dns_v1_controlplane_proto_rawDescData
}

var file_proxymanager_dns_v1_controlplane_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_proxymanager_dns_v1_controlplane_proto_goTypes = []any{
	(*GetBlockedDomainRequest)(nil),  // 0: proxymanager.dns.v1.GetBlockedDomainRequest
	(*GetBlockedDomainResponse)(nil), // 1: proxymanager.dns.v1.GetBlockedDomainResponse
	(*v1.PaginationRequest)(nil),     // 2: utils.v1.PaginationRequest
	(*v11.ErrorMessage)(nil),         // 3: errmsg.v1.ErrorMessage
}
var file_proxymanager_dns_v1_controlplane_proto_depIdxs = []int32{
	2, // 0: proxymanager.dns.v1.GetBlockedDomainRequest.pagination:type_name -> utils.v1.PaginationRequest
	3, // 1: proxymanager.dns.v1.GetBlockedDomainResponse.error:type_name -> errmsg.v1.ErrorMessage
	2, // 2: proxymanager.dns.v1.GetBlockedDomainResponse.pagination:type_name -> utils.v1.PaginationRequest
	0, // 3: proxymanager.dns.v1.DNSManagerService.GetBlockedDomain:input_type -> proxymanager.dns.v1.GetBlockedDomainRequest
	1, // 4: proxymanager.dns.v1.DNSManagerService.GetBlockedDomain:output_type -> proxymanager.dns.v1.GetBlockedDomainResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_proxymanager_dns_v1_controlplane_proto_init() }
func file_proxymanager_dns_v1_controlplane_proto_init() {
	if File_proxymanager_dns_v1_controlplane_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_dns_v1_controlplane_proto_rawDesc), len(file_proxymanager_dns_v1_controlplane_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_dns_v1_controlplane_proto_goTypes,
		DependencyIndexes: file_proxymanager_dns_v1_controlplane_proto_depIdxs,
		MessageInfos:      file_proxymanager_dns_v1_controlplane_proto_msgTypes,
	}.Build()
	File_proxymanager_dns_v1_controlplane_proto = out.File
	file_proxymanager_dns_v1_controlplane_proto_goTypes = nil
	file_proxymanager_dns_v1_controlplane_proto_depIdxs = nil
}
