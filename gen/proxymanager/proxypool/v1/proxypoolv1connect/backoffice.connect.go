// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/proxypool/v1/backoffice.proto

package proxypoolv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/proxypool/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackofficeProxyPoolServiceName is the fully-qualified name of the BackofficeProxyPoolService
	// service.
	BackofficeProxyPoolServiceName = "proxymanager.proxypool.v1.BackofficeProxyPoolService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackofficeProxyPoolServiceFetchProxyPoolProcedure is the fully-qualified name of the
	// BackofficeProxyPoolService's FetchProxyPool RPC.
	BackofficeProxyPoolServiceFetchProxyPoolProcedure = "/proxymanager.proxypool.v1.BackofficeProxyPoolService/FetchProxyPool"
	// BackofficeProxyPoolServiceRemoveProxyPoolProcedure is the fully-qualified name of the
	// BackofficeProxyPoolService's RemoveProxyPool RPC.
	BackofficeProxyPoolServiceRemoveProxyPoolProcedure = "/proxymanager.proxypool.v1.BackofficeProxyPoolService/RemoveProxyPool"
	// BackofficeProxyPoolServiceHealthCheckProcedure is the fully-qualified name of the
	// BackofficeProxyPoolService's HealthCheck RPC.
	BackofficeProxyPoolServiceHealthCheckProcedure = "/proxymanager.proxypool.v1.BackofficeProxyPoolService/HealthCheck"
)

// BackofficeProxyPoolServiceClient is a client for the
// proxymanager.proxypool.v1.BackofficeProxyPoolService service.
type BackofficeProxyPoolServiceClient interface {
	FetchProxyPool(context.Context, *connect.Request[v1.BackofficeProxyPoolServiceFetchProxyPoolRequest]) (*connect.Response[v1.BackofficeProxyPoolServiceFetchProxyPoolResponse], error)
	RemoveProxyPool(context.Context, *connect.Request[v1.BackofficeProxyPoolServiceRemoveProxyPoolRequest]) (*connect.Response[v1.BackofficeProxyPoolServiceRemoveProxyPoolResponse], error)
	HealthCheck(context.Context, *connect.Request[v1.BackofficeProxyPoolServiceHealthCheckRequest]) (*connect.Response[v1.BackofficeProxyPoolServiceHealthCheckResponse], error)
}

// NewBackofficeProxyPoolServiceClient constructs a client for the
// proxymanager.proxypool.v1.BackofficeProxyPoolService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackofficeProxyPoolServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackofficeProxyPoolServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backofficeProxyPoolServiceMethods := v1.File_proxymanager_proxypool_v1_backoffice_proto.Services().ByName("BackofficeProxyPoolService").Methods()
	return &backofficeProxyPoolServiceClient{
		fetchProxyPool: connect.NewClient[v1.BackofficeProxyPoolServiceFetchProxyPoolRequest, v1.BackofficeProxyPoolServiceFetchProxyPoolResponse](
			httpClient,
			baseURL+BackofficeProxyPoolServiceFetchProxyPoolProcedure,
			connect.WithSchema(backofficeProxyPoolServiceMethods.ByName("FetchProxyPool")),
			connect.WithClientOptions(opts...),
		),
		removeProxyPool: connect.NewClient[v1.BackofficeProxyPoolServiceRemoveProxyPoolRequest, v1.BackofficeProxyPoolServiceRemoveProxyPoolResponse](
			httpClient,
			baseURL+BackofficeProxyPoolServiceRemoveProxyPoolProcedure,
			connect.WithSchema(backofficeProxyPoolServiceMethods.ByName("RemoveProxyPool")),
			connect.WithClientOptions(opts...),
		),
		healthCheck: connect.NewClient[v1.BackofficeProxyPoolServiceHealthCheckRequest, v1.BackofficeProxyPoolServiceHealthCheckResponse](
			httpClient,
			baseURL+BackofficeProxyPoolServiceHealthCheckProcedure,
			connect.WithSchema(backofficeProxyPoolServiceMethods.ByName("HealthCheck")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backofficeProxyPoolServiceClient implements BackofficeProxyPoolServiceClient.
type backofficeProxyPoolServiceClient struct {
	fetchProxyPool  *connect.Client[v1.BackofficeProxyPoolServiceFetchProxyPoolRequest, v1.BackofficeProxyPoolServiceFetchProxyPoolResponse]
	removeProxyPool *connect.Client[v1.BackofficeProxyPoolServiceRemoveProxyPoolRequest, v1.BackofficeProxyPoolServiceRemoveProxyPoolResponse]
	healthCheck     *connect.Client[v1.BackofficeProxyPoolServiceHealthCheckRequest, v1.BackofficeProxyPoolServiceHealthCheckResponse]
}

// FetchProxyPool calls proxymanager.proxypool.v1.BackofficeProxyPoolService.FetchProxyPool.
func (c *backofficeProxyPoolServiceClient) FetchProxyPool(ctx context.Context, req *connect.Request[v1.BackofficeProxyPoolServiceFetchProxyPoolRequest]) (*connect.Response[v1.BackofficeProxyPoolServiceFetchProxyPoolResponse], error) {
	return c.fetchProxyPool.CallUnary(ctx, req)
}

// RemoveProxyPool calls proxymanager.proxypool.v1.BackofficeProxyPoolService.RemoveProxyPool.
func (c *backofficeProxyPoolServiceClient) RemoveProxyPool(ctx context.Context, req *connect.Request[v1.BackofficeProxyPoolServiceRemoveProxyPoolRequest]) (*connect.Response[v1.BackofficeProxyPoolServiceRemoveProxyPoolResponse], error) {
	return c.removeProxyPool.CallUnary(ctx, req)
}

// HealthCheck calls proxymanager.proxypool.v1.BackofficeProxyPoolService.HealthCheck.
func (c *backofficeProxyPoolServiceClient) HealthCheck(ctx context.Context, req *connect.Request[v1.BackofficeProxyPoolServiceHealthCheckRequest]) (*connect.Response[v1.BackofficeProxyPoolServiceHealthCheckResponse], error) {
	return c.healthCheck.CallUnary(ctx, req)
}

// BackofficeProxyPoolServiceHandler is an implementation of the
// proxymanager.proxypool.v1.BackofficeProxyPoolService service.
type BackofficeProxyPoolServiceHandler interface {
	FetchProxyPool(context.Context, *connect.Request[v1.BackofficeProxyPoolServiceFetchProxyPoolRequest]) (*connect.Response[v1.BackofficeProxyPoolServiceFetchProxyPoolResponse], error)
	RemoveProxyPool(context.Context, *connect.Request[v1.BackofficeProxyPoolServiceRemoveProxyPoolRequest]) (*connect.Response[v1.BackofficeProxyPoolServiceRemoveProxyPoolResponse], error)
	HealthCheck(context.Context, *connect.Request[v1.BackofficeProxyPoolServiceHealthCheckRequest]) (*connect.Response[v1.BackofficeProxyPoolServiceHealthCheckResponse], error)
}

// NewBackofficeProxyPoolServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackofficeProxyPoolServiceHandler(svc BackofficeProxyPoolServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backofficeProxyPoolServiceMethods := v1.File_proxymanager_proxypool_v1_backoffice_proto.Services().ByName("BackofficeProxyPoolService").Methods()
	backofficeProxyPoolServiceFetchProxyPoolHandler := connect.NewUnaryHandler(
		BackofficeProxyPoolServiceFetchProxyPoolProcedure,
		svc.FetchProxyPool,
		connect.WithSchema(backofficeProxyPoolServiceMethods.ByName("FetchProxyPool")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeProxyPoolServiceRemoveProxyPoolHandler := connect.NewUnaryHandler(
		BackofficeProxyPoolServiceRemoveProxyPoolProcedure,
		svc.RemoveProxyPool,
		connect.WithSchema(backofficeProxyPoolServiceMethods.ByName("RemoveProxyPool")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeProxyPoolServiceHealthCheckHandler := connect.NewUnaryHandler(
		BackofficeProxyPoolServiceHealthCheckProcedure,
		svc.HealthCheck,
		connect.WithSchema(backofficeProxyPoolServiceMethods.ByName("HealthCheck")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.proxypool.v1.BackofficeProxyPoolService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackofficeProxyPoolServiceFetchProxyPoolProcedure:
			backofficeProxyPoolServiceFetchProxyPoolHandler.ServeHTTP(w, r)
		case BackofficeProxyPoolServiceRemoveProxyPoolProcedure:
			backofficeProxyPoolServiceRemoveProxyPoolHandler.ServeHTTP(w, r)
		case BackofficeProxyPoolServiceHealthCheckProcedure:
			backofficeProxyPoolServiceHealthCheckHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackofficeProxyPoolServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackofficeProxyPoolServiceHandler struct{}

func (UnimplementedBackofficeProxyPoolServiceHandler) FetchProxyPool(context.Context, *connect.Request[v1.BackofficeProxyPoolServiceFetchProxyPoolRequest]) (*connect.Response[v1.BackofficeProxyPoolServiceFetchProxyPoolResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxypool.v1.BackofficeProxyPoolService.FetchProxyPool is not implemented"))
}

func (UnimplementedBackofficeProxyPoolServiceHandler) RemoveProxyPool(context.Context, *connect.Request[v1.BackofficeProxyPoolServiceRemoveProxyPoolRequest]) (*connect.Response[v1.BackofficeProxyPoolServiceRemoveProxyPoolResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxypool.v1.BackofficeProxyPoolService.RemoveProxyPool is not implemented"))
}

func (UnimplementedBackofficeProxyPoolServiceHandler) HealthCheck(context.Context, *connect.Request[v1.BackofficeProxyPoolServiceHealthCheckRequest]) (*connect.Response[v1.BackofficeProxyPoolServiceHealthCheckResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxypool.v1.BackofficeProxyPoolService.HealthCheck is not implemented"))
}
