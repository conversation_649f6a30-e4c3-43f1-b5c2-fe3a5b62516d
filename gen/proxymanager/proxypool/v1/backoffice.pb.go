// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/proxypool/v1/backoffice.proto

package proxypoolv1

import (
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackofficeProxyPoolServiceHealthCheckRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdProxyPool   string                 `protobuf:"bytes,1,opt,name=id_proxy_pool,json=idProxyPool,proto3" json:"id_proxy_pool,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeProxyPoolServiceHealthCheckRequest) Reset() {
	*x = BackofficeProxyPoolServiceHealthCheckRequest{}
	mi := &file_proxymanager_proxypool_v1_backoffice_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyPoolServiceHealthCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyPoolServiceHealthCheckRequest) ProtoMessage() {}

func (x *BackofficeProxyPoolServiceHealthCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxypool_v1_backoffice_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyPoolServiceHealthCheckRequest.ProtoReflect.Descriptor instead.
func (*BackofficeProxyPoolServiceHealthCheckRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxypool_v1_backoffice_proto_rawDescGZIP(), []int{0}
}

func (x *BackofficeProxyPoolServiceHealthCheckRequest) GetIdProxyPool() string {
	if x != nil {
		return x.IdProxyPool
	}
	return ""
}

type BackofficeProxyPoolServiceHealthCheckResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeProxyPoolServiceHealthCheckResponse) Reset() {
	*x = BackofficeProxyPoolServiceHealthCheckResponse{}
	mi := &file_proxymanager_proxypool_v1_backoffice_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyPoolServiceHealthCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyPoolServiceHealthCheckResponse) ProtoMessage() {}

func (x *BackofficeProxyPoolServiceHealthCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxypool_v1_backoffice_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyPoolServiceHealthCheckResponse.ProtoReflect.Descriptor instead.
func (*BackofficeProxyPoolServiceHealthCheckResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxypool_v1_backoffice_proto_rawDescGZIP(), []int{1}
}

func (x *BackofficeProxyPoolServiceHealthCheckResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeProxyPoolServiceRemoveProxyPoolRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdProxyPool   string                 `protobuf:"bytes,1,opt,name=id_proxy_pool,json=idProxyPool,proto3" json:"id_proxy_pool,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeProxyPoolServiceRemoveProxyPoolRequest) Reset() {
	*x = BackofficeProxyPoolServiceRemoveProxyPoolRequest{}
	mi := &file_proxymanager_proxypool_v1_backoffice_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyPoolServiceRemoveProxyPoolRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyPoolServiceRemoveProxyPoolRequest) ProtoMessage() {}

func (x *BackofficeProxyPoolServiceRemoveProxyPoolRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxypool_v1_backoffice_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyPoolServiceRemoveProxyPoolRequest.ProtoReflect.Descriptor instead.
func (*BackofficeProxyPoolServiceRemoveProxyPoolRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxypool_v1_backoffice_proto_rawDescGZIP(), []int{2}
}

func (x *BackofficeProxyPoolServiceRemoveProxyPoolRequest) GetIdProxyPool() string {
	if x != nil {
		return x.IdProxyPool
	}
	return ""
}

type BackofficeProxyPoolServiceRemoveProxyPoolResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeProxyPoolServiceRemoveProxyPoolResponse) Reset() {
	*x = BackofficeProxyPoolServiceRemoveProxyPoolResponse{}
	mi := &file_proxymanager_proxypool_v1_backoffice_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyPoolServiceRemoveProxyPoolResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyPoolServiceRemoveProxyPoolResponse) ProtoMessage() {}

func (x *BackofficeProxyPoolServiceRemoveProxyPoolResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxypool_v1_backoffice_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyPoolServiceRemoveProxyPoolResponse.ProtoReflect.Descriptor instead.
func (*BackofficeProxyPoolServiceRemoveProxyPoolResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxypool_v1_backoffice_proto_rawDescGZIP(), []int{3}
}

func (x *BackofficeProxyPoolServiceRemoveProxyPoolResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeProxyPoolServiceFetchProxyPoolRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Service        v11.ServiceType        `protobuf:"varint,1,opt,name=service,proto3,enum=algoenum.v1.ServiceType" json:"service,omitempty"`
	IpProxySearch  string                 `protobuf:"bytes,2,opt,name=ip_proxy_search,json=ipProxySearch,proto3" json:"ip_proxy_search,omitempty"`
	UsernameSearch string                 `protobuf:"bytes,3,opt,name=username_search,json=usernameSearch,proto3" json:"username_search,omitempty"`
	IdLocation     string                 `protobuf:"bytes,4,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	Pagination     *v12.PaginationRequest `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BackofficeProxyPoolServiceFetchProxyPoolRequest) Reset() {
	*x = BackofficeProxyPoolServiceFetchProxyPoolRequest{}
	mi := &file_proxymanager_proxypool_v1_backoffice_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyPoolServiceFetchProxyPoolRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyPoolServiceFetchProxyPoolRequest) ProtoMessage() {}

func (x *BackofficeProxyPoolServiceFetchProxyPoolRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxypool_v1_backoffice_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyPoolServiceFetchProxyPoolRequest.ProtoReflect.Descriptor instead.
func (*BackofficeProxyPoolServiceFetchProxyPoolRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxypool_v1_backoffice_proto_rawDescGZIP(), []int{4}
}

func (x *BackofficeProxyPoolServiceFetchProxyPoolRequest) GetService() v11.ServiceType {
	if x != nil {
		return x.Service
	}
	return v11.ServiceType(0)
}

func (x *BackofficeProxyPoolServiceFetchProxyPoolRequest) GetIpProxySearch() string {
	if x != nil {
		return x.IpProxySearch
	}
	return ""
}

func (x *BackofficeProxyPoolServiceFetchProxyPoolRequest) GetUsernameSearch() string {
	if x != nil {
		return x.UsernameSearch
	}
	return ""
}

func (x *BackofficeProxyPoolServiceFetchProxyPoolRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *BackofficeProxyPoolServiceFetchProxyPoolRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeProxyPoolServiceFetchProxyPoolResponse struct {
	state         protoimpl.MessageState                       `protogen:"open.v1"`
	Error         *v1.ErrorMessage                             `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v12.PaginationResponse                      `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ProxyPools    []*BackofficeProxyPoolServiceProxyPoolEntity `protobuf:"bytes,3,rep,name=proxy_pools,json=proxyPools,proto3" json:"proxy_pools,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeProxyPoolServiceFetchProxyPoolResponse) Reset() {
	*x = BackofficeProxyPoolServiceFetchProxyPoolResponse{}
	mi := &file_proxymanager_proxypool_v1_backoffice_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyPoolServiceFetchProxyPoolResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyPoolServiceFetchProxyPoolResponse) ProtoMessage() {}

func (x *BackofficeProxyPoolServiceFetchProxyPoolResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxypool_v1_backoffice_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyPoolServiceFetchProxyPoolResponse.ProtoReflect.Descriptor instead.
func (*BackofficeProxyPoolServiceFetchProxyPoolResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxypool_v1_backoffice_proto_rawDescGZIP(), []int{5}
}

func (x *BackofficeProxyPoolServiceFetchProxyPoolResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeProxyPoolServiceFetchProxyPoolResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficeProxyPoolServiceFetchProxyPoolResponse) GetProxyPools() []*BackofficeProxyPoolServiceProxyPoolEntity {
	if x != nil {
		return x.ProxyPools
	}
	return nil
}

type BackofficeProxyPoolServiceProxyPoolEntity struct {
	state         protoimpl.MessageState                             `protogen:"open.v1"`
	IdProxyPool   string                                             `protobuf:"bytes,1,opt,name=id_proxy_pool,json=idProxyPool,proto3" json:"id_proxy_pool,omitempty"`
	Username      string                                             `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Service       v11.ServiceType                                    `protobuf:"varint,3,opt,name=service,proto3,enum=algoenum.v1.ServiceType" json:"service,omitempty"`
	Region        *BackofficeProxyPoolServiceProxyPoolEntityLocation `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	Country       *BackofficeProxyPoolServiceProxyPoolEntityLocation `protobuf:"bytes,5,opt,name=country,proto3" json:"country,omitempty"`
	State         *BackofficeProxyPoolServiceProxyPoolEntityLocation `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	PublicIp      string                                             `protobuf:"bytes,7,opt,name=public_ip,json=publicIp,proto3" json:"public_ip,omitempty"`
	CreatedAt     int64                                              `protobuf:"varint,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeProxyPoolServiceProxyPoolEntity) Reset() {
	*x = BackofficeProxyPoolServiceProxyPoolEntity{}
	mi := &file_proxymanager_proxypool_v1_backoffice_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyPoolServiceProxyPoolEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyPoolServiceProxyPoolEntity) ProtoMessage() {}

func (x *BackofficeProxyPoolServiceProxyPoolEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxypool_v1_backoffice_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyPoolServiceProxyPoolEntity.ProtoReflect.Descriptor instead.
func (*BackofficeProxyPoolServiceProxyPoolEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxypool_v1_backoffice_proto_rawDescGZIP(), []int{6}
}

func (x *BackofficeProxyPoolServiceProxyPoolEntity) GetIdProxyPool() string {
	if x != nil {
		return x.IdProxyPool
	}
	return ""
}

func (x *BackofficeProxyPoolServiceProxyPoolEntity) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *BackofficeProxyPoolServiceProxyPoolEntity) GetService() v11.ServiceType {
	if x != nil {
		return x.Service
	}
	return v11.ServiceType(0)
}

func (x *BackofficeProxyPoolServiceProxyPoolEntity) GetRegion() *BackofficeProxyPoolServiceProxyPoolEntityLocation {
	if x != nil {
		return x.Region
	}
	return nil
}

func (x *BackofficeProxyPoolServiceProxyPoolEntity) GetCountry() *BackofficeProxyPoolServiceProxyPoolEntityLocation {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *BackofficeProxyPoolServiceProxyPoolEntity) GetState() *BackofficeProxyPoolServiceProxyPoolEntityLocation {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeProxyPoolServiceProxyPoolEntity) GetPublicIp() string {
	if x != nil {
		return x.PublicIp
	}
	return ""
}

func (x *BackofficeProxyPoolServiceProxyPoolEntity) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

type BackofficeProxyPoolServiceProxyPoolEntityLocation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdLocation    string                 `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	LocationLevel v11.LocationLevel      `protobuf:"varint,3,opt,name=location_level,json=locationLevel,proto3,enum=algoenum.v1.LocationLevel" json:"location_level,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeProxyPoolServiceProxyPoolEntityLocation) Reset() {
	*x = BackofficeProxyPoolServiceProxyPoolEntityLocation{}
	mi := &file_proxymanager_proxypool_v1_backoffice_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyPoolServiceProxyPoolEntityLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyPoolServiceProxyPoolEntityLocation) ProtoMessage() {}

func (x *BackofficeProxyPoolServiceProxyPoolEntityLocation) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxypool_v1_backoffice_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyPoolServiceProxyPoolEntityLocation.ProtoReflect.Descriptor instead.
func (*BackofficeProxyPoolServiceProxyPoolEntityLocation) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxypool_v1_backoffice_proto_rawDescGZIP(), []int{7}
}

func (x *BackofficeProxyPoolServiceProxyPoolEntityLocation) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *BackofficeProxyPoolServiceProxyPoolEntityLocation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeProxyPoolServiceProxyPoolEntityLocation) GetLocationLevel() v11.LocationLevel {
	if x != nil {
		return x.LocationLevel
	}
	return v11.LocationLevel(0)
}

var File_proxymanager_proxypool_v1_backoffice_proto protoreflect.FileDescriptor

const file_proxymanager_proxypool_v1_backoffice_proto_rawDesc = "" +
	"\n" +
	"*proxymanager/proxypool/v1/backoffice.proto\x12\x19proxymanager.proxypool.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x19algoenum/v1/service.proto\x1a algoenum/v1/location_level.proto\x1a\x14utils/v1/utils.proto\"R\n" +
	",BackofficeProxyPoolServiceHealthCheckRequest\x12\"\n" +
	"\rid_proxy_pool\x18\x01 \x01(\tR\vidProxyPool\"^\n" +
	"-BackofficeProxyPoolServiceHealthCheckResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"V\n" +
	"0BackofficeProxyPoolServiceRemoveProxyPoolRequest\x12\"\n" +
	"\rid_proxy_pool\x18\x01 \x01(\tR\vidProxyPool\"b\n" +
	"1BackofficeProxyPoolServiceRemoveProxyPoolResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x94\x02\n" +
	"/BackofficeProxyPoolServiceFetchProxyPoolRequest\x122\n" +
	"\aservice\x18\x01 \x01(\x0e2\x18.algoenum.v1.ServiceTypeR\aservice\x12&\n" +
	"\x0fip_proxy_search\x18\x02 \x01(\tR\ripProxySearch\x12'\n" +
	"\x0fusername_search\x18\x03 \x01(\tR\x0eusernameSearch\x12\x1f\n" +
	"\vid_location\x18\x04 \x01(\tR\n" +
	"idLocation\x12;\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x86\x02\n" +
	"0BackofficeProxyPoolServiceFetchProxyPoolResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12e\n" +
	"\vproxy_pools\x18\x03 \x03(\v2D.proxymanager.proxypool.v1.BackofficeProxyPoolServiceProxyPoolEntityR\n" +
	"proxyPools\"\x8d\x04\n" +
	")BackofficeProxyPoolServiceProxyPoolEntity\x12\"\n" +
	"\rid_proxy_pool\x18\x01 \x01(\tR\vidProxyPool\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x122\n" +
	"\aservice\x18\x03 \x01(\x0e2\x18.algoenum.v1.ServiceTypeR\aservice\x12d\n" +
	"\x06region\x18\x04 \x01(\v2L.proxymanager.proxypool.v1.BackofficeProxyPoolServiceProxyPoolEntityLocationR\x06region\x12f\n" +
	"\acountry\x18\x05 \x01(\v2L.proxymanager.proxypool.v1.BackofficeProxyPoolServiceProxyPoolEntityLocationR\acountry\x12b\n" +
	"\x05state\x18\x06 \x01(\v2L.proxymanager.proxypool.v1.BackofficeProxyPoolServiceProxyPoolEntityLocationR\x05state\x12\x1b\n" +
	"\tpublic_ip\x18\a \x01(\tR\bpublicIp\x12\x1d\n" +
	"\n" +
	"created_at\x18\b \x01(\x03R\tcreatedAt\"\xab\x01\n" +
	"1BackofficeProxyPoolServiceProxyPoolEntityLocation\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12A\n" +
	"\x0elocation_level\x18\x03 \x01(\x0e2\x1a.algoenum.v1.LocationLevelR\rlocationLevel2\x9a\x04\n" +
	"\x1aBackofficeProxyPoolService\x12\xa9\x01\n" +
	"\x0eFetchProxyPool\x12J.proxymanager.proxypool.v1.BackofficeProxyPoolServiceFetchProxyPoolRequest\x1aK.proxymanager.proxypool.v1.BackofficeProxyPoolServiceFetchProxyPoolResponse\x12\xac\x01\n" +
	"\x0fRemoveProxyPool\x12K.proxymanager.proxypool.v1.BackofficeProxyPoolServiceRemoveProxyPoolRequest\x1aL.proxymanager.proxypool.v1.BackofficeProxyPoolServiceRemoveProxyPoolResponse\x12\xa0\x01\n" +
	"\vHealthCheck\x12G.proxymanager.proxypool.v1.BackofficeProxyPoolServiceHealthCheckRequest\x1aH.proxymanager.proxypool.v1.BackofficeProxyPoolServiceHealthCheckResponseBVZTgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/proxypool/v1;proxypoolv1b\x06proto3"

var (
	file_proxymanager_proxypool_v1_backoffice_proto_rawDescOnce sync.Once
	file_proxymanager_proxypool_v1_backoffice_proto_rawDescData []byte
)

func file_proxymanager_proxypool_v1_backoffice_proto_rawDescGZIP() []byte {
	file_proxymanager_proxypool_v1_backoffice_proto_rawDescOnce.Do(func() {
		file_proxymanager_proxypool_v1_backoffice_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_proxypool_v1_backoffice_proto_rawDesc), len(file_proxymanager_proxypool_v1_backoffice_proto_rawDesc)))
	})
	return file_proxymanager_proxypool_v1_backoffice_proto_rawDescData
}

var file_proxymanager_proxypool_v1_backoffice_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_proxymanager_proxypool_v1_backoffice_proto_goTypes = []any{
	(*BackofficeProxyPoolServiceHealthCheckRequest)(nil),      // 0: proxymanager.proxypool.v1.BackofficeProxyPoolServiceHealthCheckRequest
	(*BackofficeProxyPoolServiceHealthCheckResponse)(nil),     // 1: proxymanager.proxypool.v1.BackofficeProxyPoolServiceHealthCheckResponse
	(*BackofficeProxyPoolServiceRemoveProxyPoolRequest)(nil),  // 2: proxymanager.proxypool.v1.BackofficeProxyPoolServiceRemoveProxyPoolRequest
	(*BackofficeProxyPoolServiceRemoveProxyPoolResponse)(nil), // 3: proxymanager.proxypool.v1.BackofficeProxyPoolServiceRemoveProxyPoolResponse
	(*BackofficeProxyPoolServiceFetchProxyPoolRequest)(nil),   // 4: proxymanager.proxypool.v1.BackofficeProxyPoolServiceFetchProxyPoolRequest
	(*BackofficeProxyPoolServiceFetchProxyPoolResponse)(nil),  // 5: proxymanager.proxypool.v1.BackofficeProxyPoolServiceFetchProxyPoolResponse
	(*BackofficeProxyPoolServiceProxyPoolEntity)(nil),         // 6: proxymanager.proxypool.v1.BackofficeProxyPoolServiceProxyPoolEntity
	(*BackofficeProxyPoolServiceProxyPoolEntityLocation)(nil), // 7: proxymanager.proxypool.v1.BackofficeProxyPoolServiceProxyPoolEntityLocation
	(*v1.ErrorMessage)(nil),                                   // 8: errmsg.v1.ErrorMessage
	(v11.ServiceType)(0),                                      // 9: algoenum.v1.ServiceType
	(*v12.PaginationRequest)(nil),                             // 10: utils.v1.PaginationRequest
	(*v12.PaginationResponse)(nil),                            // 11: utils.v1.PaginationResponse
	(v11.LocationLevel)(0),                                    // 12: algoenum.v1.LocationLevel
}
var file_proxymanager_proxypool_v1_backoffice_proto_depIdxs = []int32{
	8,  // 0: proxymanager.proxypool.v1.BackofficeProxyPoolServiceHealthCheckResponse.error:type_name -> errmsg.v1.ErrorMessage
	8,  // 1: proxymanager.proxypool.v1.BackofficeProxyPoolServiceRemoveProxyPoolResponse.error:type_name -> errmsg.v1.ErrorMessage
	9,  // 2: proxymanager.proxypool.v1.BackofficeProxyPoolServiceFetchProxyPoolRequest.service:type_name -> algoenum.v1.ServiceType
	10, // 3: proxymanager.proxypool.v1.BackofficeProxyPoolServiceFetchProxyPoolRequest.pagination:type_name -> utils.v1.PaginationRequest
	8,  // 4: proxymanager.proxypool.v1.BackofficeProxyPoolServiceFetchProxyPoolResponse.error:type_name -> errmsg.v1.ErrorMessage
	11, // 5: proxymanager.proxypool.v1.BackofficeProxyPoolServiceFetchProxyPoolResponse.pagination:type_name -> utils.v1.PaginationResponse
	6,  // 6: proxymanager.proxypool.v1.BackofficeProxyPoolServiceFetchProxyPoolResponse.proxy_pools:type_name -> proxymanager.proxypool.v1.BackofficeProxyPoolServiceProxyPoolEntity
	9,  // 7: proxymanager.proxypool.v1.BackofficeProxyPoolServiceProxyPoolEntity.service:type_name -> algoenum.v1.ServiceType
	7,  // 8: proxymanager.proxypool.v1.BackofficeProxyPoolServiceProxyPoolEntity.region:type_name -> proxymanager.proxypool.v1.BackofficeProxyPoolServiceProxyPoolEntityLocation
	7,  // 9: proxymanager.proxypool.v1.BackofficeProxyPoolServiceProxyPoolEntity.country:type_name -> proxymanager.proxypool.v1.BackofficeProxyPoolServiceProxyPoolEntityLocation
	7,  // 10: proxymanager.proxypool.v1.BackofficeProxyPoolServiceProxyPoolEntity.state:type_name -> proxymanager.proxypool.v1.BackofficeProxyPoolServiceProxyPoolEntityLocation
	12, // 11: proxymanager.proxypool.v1.BackofficeProxyPoolServiceProxyPoolEntityLocation.location_level:type_name -> algoenum.v1.LocationLevel
	4,  // 12: proxymanager.proxypool.v1.BackofficeProxyPoolService.FetchProxyPool:input_type -> proxymanager.proxypool.v1.BackofficeProxyPoolServiceFetchProxyPoolRequest
	2,  // 13: proxymanager.proxypool.v1.BackofficeProxyPoolService.RemoveProxyPool:input_type -> proxymanager.proxypool.v1.BackofficeProxyPoolServiceRemoveProxyPoolRequest
	0,  // 14: proxymanager.proxypool.v1.BackofficeProxyPoolService.HealthCheck:input_type -> proxymanager.proxypool.v1.BackofficeProxyPoolServiceHealthCheckRequest
	5,  // 15: proxymanager.proxypool.v1.BackofficeProxyPoolService.FetchProxyPool:output_type -> proxymanager.proxypool.v1.BackofficeProxyPoolServiceFetchProxyPoolResponse
	3,  // 16: proxymanager.proxypool.v1.BackofficeProxyPoolService.RemoveProxyPool:output_type -> proxymanager.proxypool.v1.BackofficeProxyPoolServiceRemoveProxyPoolResponse
	1,  // 17: proxymanager.proxypool.v1.BackofficeProxyPoolService.HealthCheck:output_type -> proxymanager.proxypool.v1.BackofficeProxyPoolServiceHealthCheckResponse
	15, // [15:18] is the sub-list for method output_type
	12, // [12:15] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_proxymanager_proxypool_v1_backoffice_proto_init() }
func file_proxymanager_proxypool_v1_backoffice_proto_init() {
	if File_proxymanager_proxypool_v1_backoffice_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_proxypool_v1_backoffice_proto_rawDesc), len(file_proxymanager_proxypool_v1_backoffice_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_proxypool_v1_backoffice_proto_goTypes,
		DependencyIndexes: file_proxymanager_proxypool_v1_backoffice_proto_depIdxs,
		MessageInfos:      file_proxymanager_proxypool_v1_backoffice_proto_msgTypes,
	}.Build()
	File_proxymanager_proxypool_v1_backoffice_proto = out.File
	file_proxymanager_proxypool_v1_backoffice_proto_goTypes = nil
	file_proxymanager_proxypool_v1_backoffice_proto_depIdxs = nil
}
