// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/subscription/v1/merchant.proto

package subscriptionv1

import (
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MerchantSubscriptionServiceFetchProxyTokenRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant     string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	IdUser         string                 `protobuf:"bytes,2,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	IdPlan         string                 `protobuf:"bytes,3,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdSubscription string                 `protobuf:"bytes,4,opt,name=id_subscription,json=idSubscription,proto3" json:"id_subscription,omitempty"`
	Pagination     *v1.PaginationRequest  `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *MerchantSubscriptionServiceFetchProxyTokenRequest) Reset() {
	*x = MerchantSubscriptionServiceFetchProxyTokenRequest{}
	mi := &file_proxymanager_subscription_v1_merchant_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantSubscriptionServiceFetchProxyTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantSubscriptionServiceFetchProxyTokenRequest) ProtoMessage() {}

func (x *MerchantSubscriptionServiceFetchProxyTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_merchant_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantSubscriptionServiceFetchProxyTokenRequest.ProtoReflect.Descriptor instead.
func (*MerchantSubscriptionServiceFetchProxyTokenRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_merchant_proto_rawDescGZIP(), []int{0}
}

func (x *MerchantSubscriptionServiceFetchProxyTokenRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *MerchantSubscriptionServiceFetchProxyTokenRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *MerchantSubscriptionServiceFetchProxyTokenRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *MerchantSubscriptionServiceFetchProxyTokenRequest) GetIdSubscription() string {
	if x != nil {
		return x.IdSubscription
	}
	return ""
}

func (x *MerchantSubscriptionServiceFetchProxyTokenRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantSubscriptionServiceFetchProxyTokenResponse struct {
	state         protoimpl.MessageState                  `protogen:"open.v1"`
	ProxyTokens   []*MerchantSubscriptionProxyTokenEntity `protobuf:"bytes,1,rep,name=proxy_tokens,json=proxyTokens,proto3" json:"proxy_tokens,omitempty"`
	Error         *v11.ErrorMessage                       `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v1.PaginationResponse                  `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantSubscriptionServiceFetchProxyTokenResponse) Reset() {
	*x = MerchantSubscriptionServiceFetchProxyTokenResponse{}
	mi := &file_proxymanager_subscription_v1_merchant_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantSubscriptionServiceFetchProxyTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantSubscriptionServiceFetchProxyTokenResponse) ProtoMessage() {}

func (x *MerchantSubscriptionServiceFetchProxyTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_merchant_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantSubscriptionServiceFetchProxyTokenResponse.ProtoReflect.Descriptor instead.
func (*MerchantSubscriptionServiceFetchProxyTokenResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_merchant_proto_rawDescGZIP(), []int{1}
}

func (x *MerchantSubscriptionServiceFetchProxyTokenResponse) GetProxyTokens() []*MerchantSubscriptionProxyTokenEntity {
	if x != nil {
		return x.ProxyTokens
	}
	return nil
}

func (x *MerchantSubscriptionServiceFetchProxyTokenResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantSubscriptionServiceFetchProxyTokenResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantSubscriptionProxyTokenEntity struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdProxyToken   string                 `protobuf:"bytes,1,opt,name=id_proxy_token,json=idProxyToken,proto3" json:"id_proxy_token,omitempty"`
	IdProxyProfile string                 `protobuf:"bytes,2,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	ProxyToken     string                 `protobuf:"bytes,3,opt,name=proxy_token,json=proxyToken,proto3" json:"proxy_token,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *MerchantSubscriptionProxyTokenEntity) Reset() {
	*x = MerchantSubscriptionProxyTokenEntity{}
	mi := &file_proxymanager_subscription_v1_merchant_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantSubscriptionProxyTokenEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantSubscriptionProxyTokenEntity) ProtoMessage() {}

func (x *MerchantSubscriptionProxyTokenEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_merchant_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantSubscriptionProxyTokenEntity.ProtoReflect.Descriptor instead.
func (*MerchantSubscriptionProxyTokenEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_merchant_proto_rawDescGZIP(), []int{2}
}

func (x *MerchantSubscriptionProxyTokenEntity) GetIdProxyToken() string {
	if x != nil {
		return x.IdProxyToken
	}
	return ""
}

func (x *MerchantSubscriptionProxyTokenEntity) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

func (x *MerchantSubscriptionProxyTokenEntity) GetProxyToken() string {
	if x != nil {
		return x.ProxyToken
	}
	return ""
}

type MerchantSubscriptionServiceFetchSubscriptionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdUser        string                 `protobuf:"bytes,2,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	IdPlan        string                 `protobuf:"bytes,3,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	ProxyToken    string                 `protobuf:"bytes,4,opt,name=proxy_token,json=proxyToken,proto3" json:"proxy_token,omitempty"`
	IsExpired     bool                   `protobuf:"varint,5,opt,name=is_expired,json=isExpired,proto3" json:"is_expired,omitempty"`
	State         *v1.State              `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,7,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantSubscriptionServiceFetchSubscriptionRequest) Reset() {
	*x = MerchantSubscriptionServiceFetchSubscriptionRequest{}
	mi := &file_proxymanager_subscription_v1_merchant_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantSubscriptionServiceFetchSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantSubscriptionServiceFetchSubscriptionRequest) ProtoMessage() {}

func (x *MerchantSubscriptionServiceFetchSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_merchant_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantSubscriptionServiceFetchSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*MerchantSubscriptionServiceFetchSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_merchant_proto_rawDescGZIP(), []int{3}
}

func (x *MerchantSubscriptionServiceFetchSubscriptionRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *MerchantSubscriptionServiceFetchSubscriptionRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *MerchantSubscriptionServiceFetchSubscriptionRequest) GetProxyToken() string {
	if x != nil {
		return x.ProxyToken
	}
	return ""
}

func (x *MerchantSubscriptionServiceFetchSubscriptionRequest) GetIsExpired() bool {
	if x != nil {
		return x.IsExpired
	}
	return false
}

func (x *MerchantSubscriptionServiceFetchSubscriptionRequest) GetState() *v1.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *MerchantSubscriptionServiceFetchSubscriptionRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantSubscriptionServiceFetchSubscriptionResponse struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Subscriptions []*MerchantSubscriptionEntity `protobuf:"bytes,1,rep,name=subscriptions,proto3" json:"subscriptions,omitempty"`
	Error         *v11.ErrorMessage             `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v1.PaginationResponse        `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantSubscriptionServiceFetchSubscriptionResponse) Reset() {
	*x = MerchantSubscriptionServiceFetchSubscriptionResponse{}
	mi := &file_proxymanager_subscription_v1_merchant_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantSubscriptionServiceFetchSubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantSubscriptionServiceFetchSubscriptionResponse) ProtoMessage() {}

func (x *MerchantSubscriptionServiceFetchSubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_merchant_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantSubscriptionServiceFetchSubscriptionResponse.ProtoReflect.Descriptor instead.
func (*MerchantSubscriptionServiceFetchSubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_merchant_proto_rawDescGZIP(), []int{4}
}

func (x *MerchantSubscriptionServiceFetchSubscriptionResponse) GetSubscriptions() []*MerchantSubscriptionEntity {
	if x != nil {
		return x.Subscriptions
	}
	return nil
}

func (x *MerchantSubscriptionServiceFetchSubscriptionResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantSubscriptionServiceFetchSubscriptionResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantSubscriptionEntity struct {
	state                 protoimpl.MessageState          `protogen:"open.v1"`
	IdSubscription        string                          `protobuf:"bytes,1,opt,name=id_subscription,json=idSubscription,proto3" json:"id_subscription,omitempty"`
	DataTransferRemaining float64                         `protobuf:"fixed64,4,opt,name=data_transfer_remaining,json=dataTransferRemaining,proto3" json:"data_transfer_remaining,omitempty"`
	ExpiredAt             int64                           `protobuf:"varint,5,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	IsActive              bool                            `protobuf:"varint,6,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	Plan                  *MerchantSubscriptionPlanEntity `protobuf:"bytes,7,opt,name=plan,proto3" json:"plan,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *MerchantSubscriptionEntity) Reset() {
	*x = MerchantSubscriptionEntity{}
	mi := &file_proxymanager_subscription_v1_merchant_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantSubscriptionEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantSubscriptionEntity) ProtoMessage() {}

func (x *MerchantSubscriptionEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_merchant_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantSubscriptionEntity.ProtoReflect.Descriptor instead.
func (*MerchantSubscriptionEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_merchant_proto_rawDescGZIP(), []int{5}
}

func (x *MerchantSubscriptionEntity) GetIdSubscription() string {
	if x != nil {
		return x.IdSubscription
	}
	return ""
}

func (x *MerchantSubscriptionEntity) GetDataTransferRemaining() float64 {
	if x != nil {
		return x.DataTransferRemaining
	}
	return 0
}

func (x *MerchantSubscriptionEntity) GetExpiredAt() int64 {
	if x != nil {
		return x.ExpiredAt
	}
	return 0
}

func (x *MerchantSubscriptionEntity) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *MerchantSubscriptionEntity) GetPlan() *MerchantSubscriptionPlanEntity {
	if x != nil {
		return x.Plan
	}
	return nil
}

type MerchantSubscriptionPlanEntity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdPlan        string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IsActive      bool                   `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantSubscriptionPlanEntity) Reset() {
	*x = MerchantSubscriptionPlanEntity{}
	mi := &file_proxymanager_subscription_v1_merchant_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantSubscriptionPlanEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantSubscriptionPlanEntity) ProtoMessage() {}

func (x *MerchantSubscriptionPlanEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_merchant_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantSubscriptionPlanEntity.ProtoReflect.Descriptor instead.
func (*MerchantSubscriptionPlanEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_merchant_proto_rawDescGZIP(), []int{6}
}

func (x *MerchantSubscriptionPlanEntity) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *MerchantSubscriptionPlanEntity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MerchantSubscriptionPlanEntity) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

var File_proxymanager_subscription_v1_merchant_proto protoreflect.FileDescriptor

const file_proxymanager_subscription_v1_merchant_proto_rawDesc = "" +
	"\n" +
	"+proxymanager/subscription/v1/merchant.proto\x12\x1cproxymanager.subscription.v1\x1a\x14utils/v1/utils.proto\x1a\x18errmsg/v1/errormsg.proto\"\xec\x01\n" +
	"1MerchantSubscriptionServiceFetchProxyTokenRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12\x17\n" +
	"\aid_user\x18\x02 \x01(\tR\x06idUser\x12\x17\n" +
	"\aid_plan\x18\x03 \x01(\tR\x06idPlan\x12'\n" +
	"\x0fid_subscription\x18\x04 \x01(\tR\x0eidSubscription\x12;\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x88\x02\n" +
	"2MerchantSubscriptionServiceFetchProxyTokenResponse\x12e\n" +
	"\fproxy_tokens\x18\x01 \x03(\v2B.proxymanager.subscription.v1.MerchantSubscriptionProxyTokenEntityR\vproxyTokens\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\x97\x01\n" +
	"$MerchantSubscriptionProxyTokenEntity\x12$\n" +
	"\x0eid_proxy_token\x18\x01 \x01(\tR\fidProxyToken\x12(\n" +
	"\x10id_proxy_profile\x18\x02 \x01(\tR\x0eidProxyProfile\x12\x1f\n" +
	"\vproxy_token\x18\x03 \x01(\tR\n" +
	"proxyToken\"\x8b\x02\n" +
	"3MerchantSubscriptionServiceFetchSubscriptionRequest\x12\x17\n" +
	"\aid_user\x18\x02 \x01(\tR\x06idUser\x12\x17\n" +
	"\aid_plan\x18\x03 \x01(\tR\x06idPlan\x12\x1f\n" +
	"\vproxy_token\x18\x04 \x01(\tR\n" +
	"proxyToken\x12\x1d\n" +
	"\n" +
	"is_expired\x18\x05 \x01(\bR\tisExpired\x12%\n" +
	"\x05state\x18\x06 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\a \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x83\x02\n" +
	"4MerchantSubscriptionServiceFetchSubscriptionResponse\x12^\n" +
	"\rsubscriptions\x18\x01 \x03(\v28.proxymanager.subscription.v1.MerchantSubscriptionEntityR\rsubscriptions\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\x8b\x02\n" +
	"\x1aMerchantSubscriptionEntity\x12'\n" +
	"\x0fid_subscription\x18\x01 \x01(\tR\x0eidSubscription\x126\n" +
	"\x17data_transfer_remaining\x18\x04 \x01(\x01R\x15dataTransferRemaining\x12\x1d\n" +
	"\n" +
	"expired_at\x18\x05 \x01(\x03R\texpiredAt\x12\x1b\n" +
	"\tis_active\x18\x06 \x01(\bR\bisActive\x12P\n" +
	"\x04plan\x18\a \x01(\v2<.proxymanager.subscription.v1.MerchantSubscriptionPlanEntityR\x04plan\"j\n" +
	"\x1eMerchantSubscriptionPlanEntity\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1b\n" +
	"\tis_active\x18\x04 \x01(\bR\bisActive2\x91\x03\n" +
	"\x1bMerchantSubscriptionService\x12\xba\x01\n" +
	"\x11FetchSubscription\x12Q.proxymanager.subscription.v1.MerchantSubscriptionServiceFetchSubscriptionRequest\x1aR.proxymanager.subscription.v1.MerchantSubscriptionServiceFetchSubscriptionResponse\x12\xb4\x01\n" +
	"\x0fFetchProxyToken\x12O.proxymanager.subscription.v1.MerchantSubscriptionServiceFetchProxyTokenRequest\x1aP.proxymanager.subscription.v1.MerchantSubscriptionServiceFetchProxyTokenResponseB\\ZZgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/subscription/v1;subscriptionv1b\x06proto3"

var (
	file_proxymanager_subscription_v1_merchant_proto_rawDescOnce sync.Once
	file_proxymanager_subscription_v1_merchant_proto_rawDescData []byte
)

func file_proxymanager_subscription_v1_merchant_proto_rawDescGZIP() []byte {
	file_proxymanager_subscription_v1_merchant_proto_rawDescOnce.Do(func() {
		file_proxymanager_subscription_v1_merchant_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_subscription_v1_merchant_proto_rawDesc), len(file_proxymanager_subscription_v1_merchant_proto_rawDesc)))
	})
	return file_proxymanager_subscription_v1_merchant_proto_rawDescData
}

var file_proxymanager_subscription_v1_merchant_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_proxymanager_subscription_v1_merchant_proto_goTypes = []any{
	(*MerchantSubscriptionServiceFetchProxyTokenRequest)(nil),    // 0: proxymanager.subscription.v1.MerchantSubscriptionServiceFetchProxyTokenRequest
	(*MerchantSubscriptionServiceFetchProxyTokenResponse)(nil),   // 1: proxymanager.subscription.v1.MerchantSubscriptionServiceFetchProxyTokenResponse
	(*MerchantSubscriptionProxyTokenEntity)(nil),                 // 2: proxymanager.subscription.v1.MerchantSubscriptionProxyTokenEntity
	(*MerchantSubscriptionServiceFetchSubscriptionRequest)(nil),  // 3: proxymanager.subscription.v1.MerchantSubscriptionServiceFetchSubscriptionRequest
	(*MerchantSubscriptionServiceFetchSubscriptionResponse)(nil), // 4: proxymanager.subscription.v1.MerchantSubscriptionServiceFetchSubscriptionResponse
	(*MerchantSubscriptionEntity)(nil),                           // 5: proxymanager.subscription.v1.MerchantSubscriptionEntity
	(*MerchantSubscriptionPlanEntity)(nil),                       // 6: proxymanager.subscription.v1.MerchantSubscriptionPlanEntity
	(*v1.PaginationRequest)(nil),                                 // 7: utils.v1.PaginationRequest
	(*v11.ErrorMessage)(nil),                                     // 8: errmsg.v1.ErrorMessage
	(*v1.PaginationResponse)(nil),                                // 9: utils.v1.PaginationResponse
	(*v1.State)(nil),                                             // 10: utils.v1.State
}
var file_proxymanager_subscription_v1_merchant_proto_depIdxs = []int32{
	7,  // 0: proxymanager.subscription.v1.MerchantSubscriptionServiceFetchProxyTokenRequest.pagination:type_name -> utils.v1.PaginationRequest
	2,  // 1: proxymanager.subscription.v1.MerchantSubscriptionServiceFetchProxyTokenResponse.proxy_tokens:type_name -> proxymanager.subscription.v1.MerchantSubscriptionProxyTokenEntity
	8,  // 2: proxymanager.subscription.v1.MerchantSubscriptionServiceFetchProxyTokenResponse.error:type_name -> errmsg.v1.ErrorMessage
	9,  // 3: proxymanager.subscription.v1.MerchantSubscriptionServiceFetchProxyTokenResponse.pagination:type_name -> utils.v1.PaginationResponse
	10, // 4: proxymanager.subscription.v1.MerchantSubscriptionServiceFetchSubscriptionRequest.state:type_name -> utils.v1.State
	7,  // 5: proxymanager.subscription.v1.MerchantSubscriptionServiceFetchSubscriptionRequest.pagination:type_name -> utils.v1.PaginationRequest
	5,  // 6: proxymanager.subscription.v1.MerchantSubscriptionServiceFetchSubscriptionResponse.subscriptions:type_name -> proxymanager.subscription.v1.MerchantSubscriptionEntity
	8,  // 7: proxymanager.subscription.v1.MerchantSubscriptionServiceFetchSubscriptionResponse.error:type_name -> errmsg.v1.ErrorMessage
	9,  // 8: proxymanager.subscription.v1.MerchantSubscriptionServiceFetchSubscriptionResponse.pagination:type_name -> utils.v1.PaginationResponse
	6,  // 9: proxymanager.subscription.v1.MerchantSubscriptionEntity.plan:type_name -> proxymanager.subscription.v1.MerchantSubscriptionPlanEntity
	3,  // 10: proxymanager.subscription.v1.MerchantSubscriptionService.FetchSubscription:input_type -> proxymanager.subscription.v1.MerchantSubscriptionServiceFetchSubscriptionRequest
	0,  // 11: proxymanager.subscription.v1.MerchantSubscriptionService.FetchProxyToken:input_type -> proxymanager.subscription.v1.MerchantSubscriptionServiceFetchProxyTokenRequest
	4,  // 12: proxymanager.subscription.v1.MerchantSubscriptionService.FetchSubscription:output_type -> proxymanager.subscription.v1.MerchantSubscriptionServiceFetchSubscriptionResponse
	1,  // 13: proxymanager.subscription.v1.MerchantSubscriptionService.FetchProxyToken:output_type -> proxymanager.subscription.v1.MerchantSubscriptionServiceFetchProxyTokenResponse
	12, // [12:14] is the sub-list for method output_type
	10, // [10:12] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_proxymanager_subscription_v1_merchant_proto_init() }
func file_proxymanager_subscription_v1_merchant_proto_init() {
	if File_proxymanager_subscription_v1_merchant_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_subscription_v1_merchant_proto_rawDesc), len(file_proxymanager_subscription_v1_merchant_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_subscription_v1_merchant_proto_goTypes,
		DependencyIndexes: file_proxymanager_subscription_v1_merchant_proto_depIdxs,
		MessageInfos:      file_proxymanager_subscription_v1_merchant_proto_msgTypes,
	}.Build()
	File_proxymanager_subscription_v1_merchant_proto = out.File
	file_proxymanager_subscription_v1_merchant_proto_goTypes = nil
	file_proxymanager_subscription_v1_merchant_proto_depIdxs = nil
}
