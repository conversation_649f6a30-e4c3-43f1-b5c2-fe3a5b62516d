// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/subscription/v1/internal.proto

package subscriptionv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InternalSubscriptionServiceCreateSubscriptionRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	IdPlan              string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdSubscription      string                 `protobuf:"bytes,2,opt,name=id_subscription,json=idSubscription,proto3" json:"id_subscription,omitempty"`
	IdUser              string                 `protobuf:"bytes,3,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	Duration            int64                  `protobuf:"varint,4,opt,name=duration,proto3" json:"duration,omitempty"`
	DataTransferInGbyte float64                `protobuf:"fixed64,5,opt,name=data_transfer_in_gbyte,json=dataTransferInGbyte,proto3" json:"data_transfer_in_gbyte,omitempty"`
	UserLat             float64                `protobuf:"fixed64,6,opt,name=user_lat,json=userLat,proto3" json:"user_lat,omitempty"`
	UserLong            float64                `protobuf:"fixed64,7,opt,name=user_long,json=userLong,proto3" json:"user_long,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *InternalSubscriptionServiceCreateSubscriptionRequest) Reset() {
	*x = InternalSubscriptionServiceCreateSubscriptionRequest{}
	mi := &file_proxymanager_subscription_v1_internal_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalSubscriptionServiceCreateSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalSubscriptionServiceCreateSubscriptionRequest) ProtoMessage() {}

func (x *InternalSubscriptionServiceCreateSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_internal_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalSubscriptionServiceCreateSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*InternalSubscriptionServiceCreateSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_internal_proto_rawDescGZIP(), []int{0}
}

func (x *InternalSubscriptionServiceCreateSubscriptionRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *InternalSubscriptionServiceCreateSubscriptionRequest) GetIdSubscription() string {
	if x != nil {
		return x.IdSubscription
	}
	return ""
}

func (x *InternalSubscriptionServiceCreateSubscriptionRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *InternalSubscriptionServiceCreateSubscriptionRequest) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *InternalSubscriptionServiceCreateSubscriptionRequest) GetDataTransferInGbyte() float64 {
	if x != nil {
		return x.DataTransferInGbyte
	}
	return 0
}

func (x *InternalSubscriptionServiceCreateSubscriptionRequest) GetUserLat() float64 {
	if x != nil {
		return x.UserLat
	}
	return 0
}

func (x *InternalSubscriptionServiceCreateSubscriptionRequest) GetUserLong() float64 {
	if x != nil {
		return x.UserLong
	}
	return 0
}

type InternalSubscriptionServiceCreateSubscriptionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalSubscriptionServiceCreateSubscriptionResponse) Reset() {
	*x = InternalSubscriptionServiceCreateSubscriptionResponse{}
	mi := &file_proxymanager_subscription_v1_internal_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalSubscriptionServiceCreateSubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalSubscriptionServiceCreateSubscriptionResponse) ProtoMessage() {}

func (x *InternalSubscriptionServiceCreateSubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_internal_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalSubscriptionServiceCreateSubscriptionResponse.ProtoReflect.Descriptor instead.
func (*InternalSubscriptionServiceCreateSubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_internal_proto_rawDescGZIP(), []int{1}
}

func (x *InternalSubscriptionServiceCreateSubscriptionResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type InternalSubscriptionServiceExtendSubscriptionRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	IdSubscription      string                 `protobuf:"bytes,1,opt,name=id_subscription,json=idSubscription,proto3" json:"id_subscription,omitempty"`
	Duration            int64                  `protobuf:"varint,2,opt,name=duration,proto3" json:"duration,omitempty"`
	DataTransferInGbyte float64                `protobuf:"fixed64,3,opt,name=data_transfer_in_gbyte,json=dataTransferInGbyte,proto3" json:"data_transfer_in_gbyte,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *InternalSubscriptionServiceExtendSubscriptionRequest) Reset() {
	*x = InternalSubscriptionServiceExtendSubscriptionRequest{}
	mi := &file_proxymanager_subscription_v1_internal_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalSubscriptionServiceExtendSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalSubscriptionServiceExtendSubscriptionRequest) ProtoMessage() {}

func (x *InternalSubscriptionServiceExtendSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_internal_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalSubscriptionServiceExtendSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*InternalSubscriptionServiceExtendSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_internal_proto_rawDescGZIP(), []int{2}
}

func (x *InternalSubscriptionServiceExtendSubscriptionRequest) GetIdSubscription() string {
	if x != nil {
		return x.IdSubscription
	}
	return ""
}

func (x *InternalSubscriptionServiceExtendSubscriptionRequest) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *InternalSubscriptionServiceExtendSubscriptionRequest) GetDataTransferInGbyte() float64 {
	if x != nil {
		return x.DataTransferInGbyte
	}
	return 0
}

type InternalSubscriptionServiceExtendSubscriptionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalSubscriptionServiceExtendSubscriptionResponse) Reset() {
	*x = InternalSubscriptionServiceExtendSubscriptionResponse{}
	mi := &file_proxymanager_subscription_v1_internal_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalSubscriptionServiceExtendSubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalSubscriptionServiceExtendSubscriptionResponse) ProtoMessage() {}

func (x *InternalSubscriptionServiceExtendSubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_internal_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalSubscriptionServiceExtendSubscriptionResponse.ProtoReflect.Descriptor instead.
func (*InternalSubscriptionServiceExtendSubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_internal_proto_rawDescGZIP(), []int{3}
}

func (x *InternalSubscriptionServiceExtendSubscriptionResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type InternalSubscriptionServiceFetchSubscriptionRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	ListIdSubscription []string               `protobuf:"bytes,1,rep,name=list_id_subscription,json=listIdSubscription,proto3" json:"list_id_subscription,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *InternalSubscriptionServiceFetchSubscriptionRequest) Reset() {
	*x = InternalSubscriptionServiceFetchSubscriptionRequest{}
	mi := &file_proxymanager_subscription_v1_internal_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalSubscriptionServiceFetchSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalSubscriptionServiceFetchSubscriptionRequest) ProtoMessage() {}

func (x *InternalSubscriptionServiceFetchSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_internal_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalSubscriptionServiceFetchSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*InternalSubscriptionServiceFetchSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_internal_proto_rawDescGZIP(), []int{4}
}

func (x *InternalSubscriptionServiceFetchSubscriptionRequest) GetListIdSubscription() []string {
	if x != nil {
		return x.ListIdSubscription
	}
	return nil
}

type InternalSubscriptionServiceFetchSubscriptionResponse struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Subscriptions []*InternalSubscriptionModel `protobuf:"bytes,1,rep,name=subscriptions,proto3" json:"subscriptions,omitempty"`
	Error         *v1.ErrorMessage             `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalSubscriptionServiceFetchSubscriptionResponse) Reset() {
	*x = InternalSubscriptionServiceFetchSubscriptionResponse{}
	mi := &file_proxymanager_subscription_v1_internal_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalSubscriptionServiceFetchSubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalSubscriptionServiceFetchSubscriptionResponse) ProtoMessage() {}

func (x *InternalSubscriptionServiceFetchSubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_internal_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalSubscriptionServiceFetchSubscriptionResponse.ProtoReflect.Descriptor instead.
func (*InternalSubscriptionServiceFetchSubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_internal_proto_rawDescGZIP(), []int{5}
}

func (x *InternalSubscriptionServiceFetchSubscriptionResponse) GetSubscriptions() []*InternalSubscriptionModel {
	if x != nil {
		return x.Subscriptions
	}
	return nil
}

func (x *InternalSubscriptionServiceFetchSubscriptionResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type InternalSubscriptionModel struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdSubscription string                 `protobuf:"bytes,1,opt,name=id_subscription,json=idSubscription,proto3" json:"id_subscription,omitempty"`
	IdMerchant     string                 `protobuf:"bytes,2,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	IdUser         string                 `protobuf:"bytes,3,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	Name           string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *InternalSubscriptionModel) Reset() {
	*x = InternalSubscriptionModel{}
	mi := &file_proxymanager_subscription_v1_internal_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalSubscriptionModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalSubscriptionModel) ProtoMessage() {}

func (x *InternalSubscriptionModel) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_internal_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalSubscriptionModel.ProtoReflect.Descriptor instead.
func (*InternalSubscriptionModel) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_internal_proto_rawDescGZIP(), []int{6}
}

func (x *InternalSubscriptionModel) GetIdSubscription() string {
	if x != nil {
		return x.IdSubscription
	}
	return ""
}

func (x *InternalSubscriptionModel) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *InternalSubscriptionModel) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *InternalSubscriptionModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_proxymanager_subscription_v1_internal_proto protoreflect.FileDescriptor

const file_proxymanager_subscription_v1_internal_proto_rawDesc = "" +
	"\n" +
	"+proxymanager/subscription/v1/internal.proto\x12\x1cproxymanager.subscription.v1\x1a\x18errmsg/v1/errormsg.proto\"\x9a\x02\n" +
	"4InternalSubscriptionServiceCreateSubscriptionRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12'\n" +
	"\x0fid_subscription\x18\x02 \x01(\tR\x0eidSubscription\x12\x17\n" +
	"\aid_user\x18\x03 \x01(\tR\x06idUser\x12\x1a\n" +
	"\bduration\x18\x04 \x01(\x03R\bduration\x123\n" +
	"\x16data_transfer_in_gbyte\x18\x05 \x01(\x01R\x13dataTransferInGbyte\x12\x19\n" +
	"\buser_lat\x18\x06 \x01(\x01R\auserLat\x12\x1b\n" +
	"\tuser_long\x18\a \x01(\x01R\buserLong\"f\n" +
	"5InternalSubscriptionServiceCreateSubscriptionResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xb0\x01\n" +
	"4InternalSubscriptionServiceExtendSubscriptionRequest\x12'\n" +
	"\x0fid_subscription\x18\x01 \x01(\tR\x0eidSubscription\x12\x1a\n" +
	"\bduration\x18\x02 \x01(\x03R\bduration\x123\n" +
	"\x16data_transfer_in_gbyte\x18\x03 \x01(\x01R\x13dataTransferInGbyte\"f\n" +
	"5InternalSubscriptionServiceExtendSubscriptionResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"g\n" +
	"3InternalSubscriptionServiceFetchSubscriptionRequest\x120\n" +
	"\x14list_id_subscription\x18\x01 \x03(\tR\x12listIdSubscription\"\xc4\x01\n" +
	"4InternalSubscriptionServiceFetchSubscriptionResponse\x12]\n" +
	"\rsubscriptions\x18\x01 \x03(\v27.proxymanager.subscription.v1.InternalSubscriptionModelR\rsubscriptions\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x92\x01\n" +
	"\x19InternalSubscriptionModel\x12'\n" +
	"\x0fid_subscription\x18\x01 \x01(\tR\x0eidSubscription\x12\x1f\n" +
	"\vid_merchant\x18\x02 \x01(\tR\n" +
	"idMerchant\x12\x17\n" +
	"\aid_user\x18\x03 \x01(\tR\x06idUser\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name2\xda\x04\n" +
	"\x1bInternalSubscriptionService\x12\xba\x01\n" +
	"\x11FetchSubscription\x12Q.proxymanager.subscription.v1.InternalSubscriptionServiceFetchSubscriptionRequest\x1aR.proxymanager.subscription.v1.InternalSubscriptionServiceFetchSubscriptionResponse\x12\xbd\x01\n" +
	"\x12CreateSubscription\x12R.proxymanager.subscription.v1.InternalSubscriptionServiceCreateSubscriptionRequest\x1aS.proxymanager.subscription.v1.InternalSubscriptionServiceCreateSubscriptionResponse\x12\xbd\x01\n" +
	"\x12ExtendSubscription\x12R.proxymanager.subscription.v1.InternalSubscriptionServiceExtendSubscriptionRequest\x1aS.proxymanager.subscription.v1.InternalSubscriptionServiceExtendSubscriptionResponseB\\ZZgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/subscription/v1;subscriptionv1b\x06proto3"

var (
	file_proxymanager_subscription_v1_internal_proto_rawDescOnce sync.Once
	file_proxymanager_subscription_v1_internal_proto_rawDescData []byte
)

func file_proxymanager_subscription_v1_internal_proto_rawDescGZIP() []byte {
	file_proxymanager_subscription_v1_internal_proto_rawDescOnce.Do(func() {
		file_proxymanager_subscription_v1_internal_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_subscription_v1_internal_proto_rawDesc), len(file_proxymanager_subscription_v1_internal_proto_rawDesc)))
	})
	return file_proxymanager_subscription_v1_internal_proto_rawDescData
}

var file_proxymanager_subscription_v1_internal_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_proxymanager_subscription_v1_internal_proto_goTypes = []any{
	(*InternalSubscriptionServiceCreateSubscriptionRequest)(nil),  // 0: proxymanager.subscription.v1.InternalSubscriptionServiceCreateSubscriptionRequest
	(*InternalSubscriptionServiceCreateSubscriptionResponse)(nil), // 1: proxymanager.subscription.v1.InternalSubscriptionServiceCreateSubscriptionResponse
	(*InternalSubscriptionServiceExtendSubscriptionRequest)(nil),  // 2: proxymanager.subscription.v1.InternalSubscriptionServiceExtendSubscriptionRequest
	(*InternalSubscriptionServiceExtendSubscriptionResponse)(nil), // 3: proxymanager.subscription.v1.InternalSubscriptionServiceExtendSubscriptionResponse
	(*InternalSubscriptionServiceFetchSubscriptionRequest)(nil),   // 4: proxymanager.subscription.v1.InternalSubscriptionServiceFetchSubscriptionRequest
	(*InternalSubscriptionServiceFetchSubscriptionResponse)(nil),  // 5: proxymanager.subscription.v1.InternalSubscriptionServiceFetchSubscriptionResponse
	(*InternalSubscriptionModel)(nil),                             // 6: proxymanager.subscription.v1.InternalSubscriptionModel
	(*v1.ErrorMessage)(nil),                                       // 7: errmsg.v1.ErrorMessage
}
var file_proxymanager_subscription_v1_internal_proto_depIdxs = []int32{
	7, // 0: proxymanager.subscription.v1.InternalSubscriptionServiceCreateSubscriptionResponse.error:type_name -> errmsg.v1.ErrorMessage
	7, // 1: proxymanager.subscription.v1.InternalSubscriptionServiceExtendSubscriptionResponse.error:type_name -> errmsg.v1.ErrorMessage
	6, // 2: proxymanager.subscription.v1.InternalSubscriptionServiceFetchSubscriptionResponse.subscriptions:type_name -> proxymanager.subscription.v1.InternalSubscriptionModel
	7, // 3: proxymanager.subscription.v1.InternalSubscriptionServiceFetchSubscriptionResponse.error:type_name -> errmsg.v1.ErrorMessage
	4, // 4: proxymanager.subscription.v1.InternalSubscriptionService.FetchSubscription:input_type -> proxymanager.subscription.v1.InternalSubscriptionServiceFetchSubscriptionRequest
	0, // 5: proxymanager.subscription.v1.InternalSubscriptionService.CreateSubscription:input_type -> proxymanager.subscription.v1.InternalSubscriptionServiceCreateSubscriptionRequest
	2, // 6: proxymanager.subscription.v1.InternalSubscriptionService.ExtendSubscription:input_type -> proxymanager.subscription.v1.InternalSubscriptionServiceExtendSubscriptionRequest
	5, // 7: proxymanager.subscription.v1.InternalSubscriptionService.FetchSubscription:output_type -> proxymanager.subscription.v1.InternalSubscriptionServiceFetchSubscriptionResponse
	1, // 8: proxymanager.subscription.v1.InternalSubscriptionService.CreateSubscription:output_type -> proxymanager.subscription.v1.InternalSubscriptionServiceCreateSubscriptionResponse
	3, // 9: proxymanager.subscription.v1.InternalSubscriptionService.ExtendSubscription:output_type -> proxymanager.subscription.v1.InternalSubscriptionServiceExtendSubscriptionResponse
	7, // [7:10] is the sub-list for method output_type
	4, // [4:7] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_proxymanager_subscription_v1_internal_proto_init() }
func file_proxymanager_subscription_v1_internal_proto_init() {
	if File_proxymanager_subscription_v1_internal_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_subscription_v1_internal_proto_rawDesc), len(file_proxymanager_subscription_v1_internal_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_subscription_v1_internal_proto_goTypes,
		DependencyIndexes: file_proxymanager_subscription_v1_internal_proto_depIdxs,
		MessageInfos:      file_proxymanager_subscription_v1_internal_proto_msgTypes,
	}.Build()
	File_proxymanager_subscription_v1_internal_proto = out.File
	file_proxymanager_subscription_v1_internal_proto_goTypes = nil
	file_proxymanager_subscription_v1_internal_proto_depIdxs = nil
}
