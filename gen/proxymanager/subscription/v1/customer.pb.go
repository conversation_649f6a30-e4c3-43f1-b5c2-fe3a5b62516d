// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/subscription/v1/customer.proto

package subscriptionv1

import (
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CustomerSubscriptionServiceUpdateSubscriptionRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdSubscription string                 `protobuf:"bytes,1,opt,name=id_subscription,json=idSubscription,proto3" json:"id_subscription,omitempty"`
	Name           string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerSubscriptionServiceUpdateSubscriptionRequest) Reset() {
	*x = CustomerSubscriptionServiceUpdateSubscriptionRequest{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionServiceUpdateSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionServiceUpdateSubscriptionRequest) ProtoMessage() {}

func (x *CustomerSubscriptionServiceUpdateSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionServiceUpdateSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionServiceUpdateSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerSubscriptionServiceUpdateSubscriptionRequest) GetIdSubscription() string {
	if x != nil {
		return x.IdSubscription
	}
	return ""
}

func (x *CustomerSubscriptionServiceUpdateSubscriptionRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CustomerSubscriptionServiceUpdateSubscriptionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerSubscriptionServiceUpdateSubscriptionResponse) Reset() {
	*x = CustomerSubscriptionServiceUpdateSubscriptionResponse{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionServiceUpdateSubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionServiceUpdateSubscriptionResponse) ProtoMessage() {}

func (x *CustomerSubscriptionServiceUpdateSubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionServiceUpdateSubscriptionResponse.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionServiceUpdateSubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerSubscriptionServiceUpdateSubscriptionResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerSubscriptionServiceRevokeProxyTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdProxyToken  string                 `protobuf:"bytes,1,opt,name=id_proxy_token,json=idProxyToken,proto3" json:"id_proxy_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerSubscriptionServiceRevokeProxyTokenRequest) Reset() {
	*x = CustomerSubscriptionServiceRevokeProxyTokenRequest{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionServiceRevokeProxyTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionServiceRevokeProxyTokenRequest) ProtoMessage() {}

func (x *CustomerSubscriptionServiceRevokeProxyTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionServiceRevokeProxyTokenRequest.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionServiceRevokeProxyTokenRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{2}
}

func (x *CustomerSubscriptionServiceRevokeProxyTokenRequest) GetIdProxyToken() string {
	if x != nil {
		return x.IdProxyToken
	}
	return ""
}

type CustomerSubscriptionServiceRevokeProxyTokenResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerSubscriptionServiceRevokeProxyTokenResponse) Reset() {
	*x = CustomerSubscriptionServiceRevokeProxyTokenResponse{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionServiceRevokeProxyTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionServiceRevokeProxyTokenResponse) ProtoMessage() {}

func (x *CustomerSubscriptionServiceRevokeProxyTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionServiceRevokeProxyTokenResponse.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionServiceRevokeProxyTokenResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{3}
}

func (x *CustomerSubscriptionServiceRevokeProxyTokenResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	IdProxyToken            string                 `protobuf:"bytes,1,opt,name=id_proxy_token,json=idProxyToken,proto3" json:"id_proxy_token,omitempty"`
	IdLocationOfBackConnect string                 `protobuf:"bytes,2,opt,name=id_location_of_back_connect,json=idLocationOfBackConnect,proto3" json:"id_location_of_back_connect,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest) Reset() {
	*x = CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest) ProtoMessage() {}

func (x *CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{4}
}

func (x *CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest) GetIdProxyToken() string {
	if x != nil {
		return x.IdProxyToken
	}
	return ""
}

func (x *CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest) GetIdLocationOfBackConnect() string {
	if x != nil {
		return x.IdLocationOfBackConnect
	}
	return ""
}

type CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse) Reset() {
	*x = CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse) ProtoMessage() {}

func (x *CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{5}
}

func (x *CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerSubscriptionServiceFetchUserPlanRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerSubscriptionServiceFetchUserPlanRequest) Reset() {
	*x = CustomerSubscriptionServiceFetchUserPlanRequest{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionServiceFetchUserPlanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionServiceFetchUserPlanRequest) ProtoMessage() {}

func (x *CustomerSubscriptionServiceFetchUserPlanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionServiceFetchUserPlanRequest.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionServiceFetchUserPlanRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{6}
}

type CustomerSubscriptionServiceFetchUserPlanResponse struct {
	state         protoimpl.MessageState                          `protogen:"open.v1"`
	Error         *v1.ErrorMessage                                `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse                         `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Plans         []*CustomerSubscriptionServiceFetchUserPlanPlan `protobuf:"bytes,3,rep,name=plans,proto3" json:"plans,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerSubscriptionServiceFetchUserPlanResponse) Reset() {
	*x = CustomerSubscriptionServiceFetchUserPlanResponse{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionServiceFetchUserPlanResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionServiceFetchUserPlanResponse) ProtoMessage() {}

func (x *CustomerSubscriptionServiceFetchUserPlanResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionServiceFetchUserPlanResponse.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionServiceFetchUserPlanResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{7}
}

func (x *CustomerSubscriptionServiceFetchUserPlanResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerSubscriptionServiceFetchUserPlanResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *CustomerSubscriptionServiceFetchUserPlanResponse) GetPlans() []*CustomerSubscriptionServiceFetchUserPlanPlan {
	if x != nil {
		return x.Plans
	}
	return nil
}

type CustomerSubscriptionServiceFetchUserPlanPlan struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdPlan        string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerSubscriptionServiceFetchUserPlanPlan) Reset() {
	*x = CustomerSubscriptionServiceFetchUserPlanPlan{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionServiceFetchUserPlanPlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionServiceFetchUserPlanPlan) ProtoMessage() {}

func (x *CustomerSubscriptionServiceFetchUserPlanPlan) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionServiceFetchUserPlanPlan.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionServiceFetchUserPlanPlan) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{8}
}

func (x *CustomerSubscriptionServiceFetchUserPlanPlan) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *CustomerSubscriptionServiceFetchUserPlanPlan) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CustomerSubscriptionServiceDeleteSubscriptionRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdSubscription string                 `protobuf:"bytes,1,opt,name=id_subscription,json=idSubscription,proto3" json:"id_subscription,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerSubscriptionServiceDeleteSubscriptionRequest) Reset() {
	*x = CustomerSubscriptionServiceDeleteSubscriptionRequest{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionServiceDeleteSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionServiceDeleteSubscriptionRequest) ProtoMessage() {}

func (x *CustomerSubscriptionServiceDeleteSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionServiceDeleteSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionServiceDeleteSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{9}
}

func (x *CustomerSubscriptionServiceDeleteSubscriptionRequest) GetIdSubscription() string {
	if x != nil {
		return x.IdSubscription
	}
	return ""
}

type CustomerSubscriptionServiceDeleteSubscriptionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerSubscriptionServiceDeleteSubscriptionResponse) Reset() {
	*x = CustomerSubscriptionServiceDeleteSubscriptionResponse{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionServiceDeleteSubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionServiceDeleteSubscriptionResponse) ProtoMessage() {}

func (x *CustomerSubscriptionServiceDeleteSubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionServiceDeleteSubscriptionResponse.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionServiceDeleteSubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{10}
}

func (x *CustomerSubscriptionServiceDeleteSubscriptionResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerSubscriptionServiceFetchProxyTokenRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdSubscription string                 `protobuf:"bytes,4,opt,name=id_subscription,json=idSubscription,proto3" json:"id_subscription,omitempty"`
	Pagination     *v11.PaginationRequest `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerSubscriptionServiceFetchProxyTokenRequest) Reset() {
	*x = CustomerSubscriptionServiceFetchProxyTokenRequest{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionServiceFetchProxyTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionServiceFetchProxyTokenRequest) ProtoMessage() {}

func (x *CustomerSubscriptionServiceFetchProxyTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionServiceFetchProxyTokenRequest.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionServiceFetchProxyTokenRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{11}
}

func (x *CustomerSubscriptionServiceFetchProxyTokenRequest) GetIdSubscription() string {
	if x != nil {
		return x.IdSubscription
	}
	return ""
}

func (x *CustomerSubscriptionServiceFetchProxyTokenRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerSubscriptionServiceFetchProxyTokenResponse struct {
	state         protoimpl.MessageState                  `protogen:"open.v1"`
	ProxyTokens   []*CustomerSubscriptionProxyTokenEntity `protobuf:"bytes,1,rep,name=proxy_tokens,json=proxyTokens,proto3" json:"proxy_tokens,omitempty"`
	Error         *v1.ErrorMessage                        `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse                 `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerSubscriptionServiceFetchProxyTokenResponse) Reset() {
	*x = CustomerSubscriptionServiceFetchProxyTokenResponse{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionServiceFetchProxyTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionServiceFetchProxyTokenResponse) ProtoMessage() {}

func (x *CustomerSubscriptionServiceFetchProxyTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionServiceFetchProxyTokenResponse.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionServiceFetchProxyTokenResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{12}
}

func (x *CustomerSubscriptionServiceFetchProxyTokenResponse) GetProxyTokens() []*CustomerSubscriptionProxyTokenEntity {
	if x != nil {
		return x.ProxyTokens
	}
	return nil
}

func (x *CustomerSubscriptionServiceFetchProxyTokenResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerSubscriptionServiceFetchProxyTokenResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerSubscriptionProxyTokenEntity struct {
	state          protoimpl.MessageState                           `protogen:"open.v1"`
	IdProxyToken   string                                           `protobuf:"bytes,1,opt,name=id_proxy_token,json=idProxyToken,proto3" json:"id_proxy_token,omitempty"`
	IdProxyProfile string                                           `protobuf:"bytes,2,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	ProxyToken     string                                           `protobuf:"bytes,3,opt,name=proxy_token,json=proxyToken,proto3" json:"proxy_token,omitempty"`
	BackConnect    *CustomerSubscriptionProxyTokenEntityBackConnect `protobuf:"bytes,4,opt,name=back_connect,json=backConnect,proto3" json:"back_connect,omitempty"`
	Proxy          *CustomerSubscriptionProxyTokenEntityProxy       `protobuf:"bytes,5,opt,name=proxy,proto3" json:"proxy,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerSubscriptionProxyTokenEntity) Reset() {
	*x = CustomerSubscriptionProxyTokenEntity{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionProxyTokenEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionProxyTokenEntity) ProtoMessage() {}

func (x *CustomerSubscriptionProxyTokenEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionProxyTokenEntity.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionProxyTokenEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{13}
}

func (x *CustomerSubscriptionProxyTokenEntity) GetIdProxyToken() string {
	if x != nil {
		return x.IdProxyToken
	}
	return ""
}

func (x *CustomerSubscriptionProxyTokenEntity) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

func (x *CustomerSubscriptionProxyTokenEntity) GetProxyToken() string {
	if x != nil {
		return x.ProxyToken
	}
	return ""
}

func (x *CustomerSubscriptionProxyTokenEntity) GetBackConnect() *CustomerSubscriptionProxyTokenEntityBackConnect {
	if x != nil {
		return x.BackConnect
	}
	return nil
}

func (x *CustomerSubscriptionProxyTokenEntity) GetProxy() *CustomerSubscriptionProxyTokenEntityProxy {
	if x != nil {
		return x.Proxy
	}
	return nil
}

type CustomerSubscriptionProxyTokenEntityBackConnect struct {
	state                protoimpl.MessageState        `protogen:"open.v1"`
	IdBackConnectManager string                        `protobuf:"bytes,1,opt,name=id_back_connect_manager,json=idBackConnectManager,proto3" json:"id_back_connect_manager,omitempty"`
	Name                 string                        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Country              *CustomerSubscriptionLocation `protobuf:"bytes,3,opt,name=country,proto3" json:"country,omitempty"`
	IpProxy              string                        `protobuf:"bytes,4,opt,name=ip_proxy,json=ipProxy,proto3" json:"ip_proxy,omitempty"`
	PortProxy            int64                         `protobuf:"varint,5,opt,name=port_proxy,json=portProxy,proto3" json:"port_proxy,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *CustomerSubscriptionProxyTokenEntityBackConnect) Reset() {
	*x = CustomerSubscriptionProxyTokenEntityBackConnect{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionProxyTokenEntityBackConnect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionProxyTokenEntityBackConnect) ProtoMessage() {}

func (x *CustomerSubscriptionProxyTokenEntityBackConnect) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionProxyTokenEntityBackConnect.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionProxyTokenEntityBackConnect) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{14}
}

func (x *CustomerSubscriptionProxyTokenEntityBackConnect) GetIdBackConnectManager() string {
	if x != nil {
		return x.IdBackConnectManager
	}
	return ""
}

func (x *CustomerSubscriptionProxyTokenEntityBackConnect) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerSubscriptionProxyTokenEntityBackConnect) GetCountry() *CustomerSubscriptionLocation {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *CustomerSubscriptionProxyTokenEntityBackConnect) GetIpProxy() string {
	if x != nil {
		return x.IpProxy
	}
	return ""
}

func (x *CustomerSubscriptionProxyTokenEntityBackConnect) GetPortProxy() int64 {
	if x != nil {
		return x.PortProxy
	}
	return 0
}

type CustomerSubscriptionProxyTokenEntityProxy struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Country       *CustomerSubscriptionLocation `protobuf:"bytes,1,opt,name=country,proto3" json:"country,omitempty"`
	State         *CustomerSubscriptionLocation `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
	PublicIp      string                        `protobuf:"bytes,3,opt,name=public_ip,json=publicIp,proto3" json:"public_ip,omitempty"`
	IpAllows      []string                      `protobuf:"bytes,4,rep,name=ip_allows,json=ipAllows,proto3" json:"ip_allows,omitempty"`
	UsernameProxy string                        `protobuf:"bytes,5,opt,name=username_proxy,json=usernameProxy,proto3" json:"username_proxy,omitempty"`
	PasswordProxy string                        `protobuf:"bytes,6,opt,name=password_proxy,json=passwordProxy,proto3" json:"password_proxy,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerSubscriptionProxyTokenEntityProxy) Reset() {
	*x = CustomerSubscriptionProxyTokenEntityProxy{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionProxyTokenEntityProxy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionProxyTokenEntityProxy) ProtoMessage() {}

func (x *CustomerSubscriptionProxyTokenEntityProxy) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionProxyTokenEntityProxy.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionProxyTokenEntityProxy) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{15}
}

func (x *CustomerSubscriptionProxyTokenEntityProxy) GetCountry() *CustomerSubscriptionLocation {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *CustomerSubscriptionProxyTokenEntityProxy) GetState() *CustomerSubscriptionLocation {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *CustomerSubscriptionProxyTokenEntityProxy) GetPublicIp() string {
	if x != nil {
		return x.PublicIp
	}
	return ""
}

func (x *CustomerSubscriptionProxyTokenEntityProxy) GetIpAllows() []string {
	if x != nil {
		return x.IpAllows
	}
	return nil
}

func (x *CustomerSubscriptionProxyTokenEntityProxy) GetUsernameProxy() string {
	if x != nil {
		return x.UsernameProxy
	}
	return ""
}

func (x *CustomerSubscriptionProxyTokenEntityProxy) GetPasswordProxy() string {
	if x != nil {
		return x.PasswordProxy
	}
	return ""
}

type CustomerSubscriptionLocation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Level         v12.LocationLevel      `protobuf:"varint,2,opt,name=level,proto3,enum=algoenum.v1.LocationLevel" json:"level,omitempty"`
	Emoji         string                 `protobuf:"bytes,3,opt,name=emoji,proto3" json:"emoji,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerSubscriptionLocation) Reset() {
	*x = CustomerSubscriptionLocation{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionLocation) ProtoMessage() {}

func (x *CustomerSubscriptionLocation) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionLocation.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionLocation) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{16}
}

func (x *CustomerSubscriptionLocation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerSubscriptionLocation) GetLevel() v12.LocationLevel {
	if x != nil {
		return x.Level
	}
	return v12.LocationLevel(0)
}

func (x *CustomerSubscriptionLocation) GetEmoji() string {
	if x != nil {
		return x.Emoji
	}
	return ""
}

type CustomerSubscriptionServiceFetchSubscriptionRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdPlan         string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdSubscription string                 `protobuf:"bytes,2,opt,name=id_subscription,json=idSubscription,proto3" json:"id_subscription,omitempty"`
	IsExpired      bool                   `protobuf:"varint,3,opt,name=is_expired,json=isExpired,proto3" json:"is_expired,omitempty"`
	NameSearch     string                 `protobuf:"bytes,4,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	Pagination     *v11.PaginationRequest `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerSubscriptionServiceFetchSubscriptionRequest) Reset() {
	*x = CustomerSubscriptionServiceFetchSubscriptionRequest{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionServiceFetchSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionServiceFetchSubscriptionRequest) ProtoMessage() {}

func (x *CustomerSubscriptionServiceFetchSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionServiceFetchSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionServiceFetchSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{17}
}

func (x *CustomerSubscriptionServiceFetchSubscriptionRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *CustomerSubscriptionServiceFetchSubscriptionRequest) GetIdSubscription() string {
	if x != nil {
		return x.IdSubscription
	}
	return ""
}

func (x *CustomerSubscriptionServiceFetchSubscriptionRequest) GetIsExpired() bool {
	if x != nil {
		return x.IsExpired
	}
	return false
}

func (x *CustomerSubscriptionServiceFetchSubscriptionRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *CustomerSubscriptionServiceFetchSubscriptionRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerSubscriptionServiceFetchSubscriptionResponse struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Subscriptions []*CustomerSubscriptionEntity `protobuf:"bytes,1,rep,name=subscriptions,proto3" json:"subscriptions,omitempty"`
	Error         *v1.ErrorMessage              `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse       `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerSubscriptionServiceFetchSubscriptionResponse) Reset() {
	*x = CustomerSubscriptionServiceFetchSubscriptionResponse{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionServiceFetchSubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionServiceFetchSubscriptionResponse) ProtoMessage() {}

func (x *CustomerSubscriptionServiceFetchSubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionServiceFetchSubscriptionResponse.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionServiceFetchSubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{18}
}

func (x *CustomerSubscriptionServiceFetchSubscriptionResponse) GetSubscriptions() []*CustomerSubscriptionEntity {
	if x != nil {
		return x.Subscriptions
	}
	return nil
}

func (x *CustomerSubscriptionServiceFetchSubscriptionResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerSubscriptionServiceFetchSubscriptionResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerSubscriptionEntity struct {
	state                 protoimpl.MessageState          `protogen:"open.v1"`
	IdSubscription        string                          `protobuf:"bytes,1,opt,name=id_subscription,json=idSubscription,proto3" json:"id_subscription,omitempty"`
	Name                  string                          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	DataTransferRemaining float64                         `protobuf:"fixed64,3,opt,name=data_transfer_remaining,json=dataTransferRemaining,proto3" json:"data_transfer_remaining,omitempty"`
	ExpiredAt             int64                           `protobuf:"varint,4,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	Plan                  *CustomerSubscriptionPlanEntity `protobuf:"bytes,5,opt,name=plan,proto3" json:"plan,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *CustomerSubscriptionEntity) Reset() {
	*x = CustomerSubscriptionEntity{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionEntity) ProtoMessage() {}

func (x *CustomerSubscriptionEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionEntity.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{19}
}

func (x *CustomerSubscriptionEntity) GetIdSubscription() string {
	if x != nil {
		return x.IdSubscription
	}
	return ""
}

func (x *CustomerSubscriptionEntity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerSubscriptionEntity) GetDataTransferRemaining() float64 {
	if x != nil {
		return x.DataTransferRemaining
	}
	return 0
}

func (x *CustomerSubscriptionEntity) GetExpiredAt() int64 {
	if x != nil {
		return x.ExpiredAt
	}
	return 0
}

func (x *CustomerSubscriptionEntity) GetPlan() *CustomerSubscriptionPlanEntity {
	if x != nil {
		return x.Plan
	}
	return nil
}

type CustomerSubscriptionPlanEntity struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	IdPlan          string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	Name            string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	ConcurrentProxy int64                  `protobuf:"varint,3,opt,name=concurrent_proxy,json=concurrentProxy,proto3" json:"concurrent_proxy,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CustomerSubscriptionPlanEntity) Reset() {
	*x = CustomerSubscriptionPlanEntity{}
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerSubscriptionPlanEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerSubscriptionPlanEntity) ProtoMessage() {}

func (x *CustomerSubscriptionPlanEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_customer_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerSubscriptionPlanEntity.ProtoReflect.Descriptor instead.
func (*CustomerSubscriptionPlanEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_customer_proto_rawDescGZIP(), []int{20}
}

func (x *CustomerSubscriptionPlanEntity) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *CustomerSubscriptionPlanEntity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerSubscriptionPlanEntity) GetConcurrentProxy() int64 {
	if x != nil {
		return x.ConcurrentProxy
	}
	return 0
}

var File_proxymanager_subscription_v1_customer_proto protoreflect.FileDescriptor

const file_proxymanager_subscription_v1_customer_proto_rawDesc = "" +
	"\n" +
	"+proxymanager/subscription/v1/customer.proto\x12\x1cproxymanager.subscription.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\x1a algoenum/v1/location_level.proto\"s\n" +
	"4CustomerSubscriptionServiceUpdateSubscriptionRequest\x12'\n" +
	"\x0fid_subscription\x18\x01 \x01(\tR\x0eidSubscription\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"f\n" +
	"5CustomerSubscriptionServiceUpdateSubscriptionResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"Z\n" +
	"2CustomerSubscriptionServiceRevokeProxyTokenRequest\x12$\n" +
	"\x0eid_proxy_token\x18\x01 \x01(\tR\fidProxyToken\"d\n" +
	"3CustomerSubscriptionServiceRevokeProxyTokenResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xa3\x01\n" +
	"=CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest\x12$\n" +
	"\x0eid_proxy_token\x18\x01 \x01(\tR\fidProxyToken\x12<\n" +
	"\x1bid_location_of_back_connect\x18\x02 \x01(\tR\x17idLocationOfBackConnect\"o\n" +
	">CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"1\n" +
	"/CustomerSubscriptionServiceFetchUserPlanRequest\"\x81\x02\n" +
	"0CustomerSubscriptionServiceFetchUserPlanResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12`\n" +
	"\x05plans\x18\x03 \x03(\v2J.proxymanager.subscription.v1.CustomerSubscriptionServiceFetchUserPlanPlanR\x05plans\"[\n" +
	",CustomerSubscriptionServiceFetchUserPlanPlan\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"_\n" +
	"4CustomerSubscriptionServiceDeleteSubscriptionRequest\x12'\n" +
	"\x0fid_subscription\x18\x01 \x01(\tR\x0eidSubscription\"f\n" +
	"5CustomerSubscriptionServiceDeleteSubscriptionResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x99\x01\n" +
	"1CustomerSubscriptionServiceFetchProxyTokenRequest\x12'\n" +
	"\x0fid_subscription\x18\x04 \x01(\tR\x0eidSubscription\x12;\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x88\x02\n" +
	"2CustomerSubscriptionServiceFetchProxyTokenResponse\x12e\n" +
	"\fproxy_tokens\x18\x01 \x03(\v2B.proxymanager.subscription.v1.CustomerSubscriptionProxyTokenEntityR\vproxyTokens\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\xe8\x02\n" +
	"$CustomerSubscriptionProxyTokenEntity\x12$\n" +
	"\x0eid_proxy_token\x18\x01 \x01(\tR\fidProxyToken\x12(\n" +
	"\x10id_proxy_profile\x18\x02 \x01(\tR\x0eidProxyProfile\x12\x1f\n" +
	"\vproxy_token\x18\x03 \x01(\tR\n" +
	"proxyToken\x12p\n" +
	"\fback_connect\x18\x04 \x01(\v2M.proxymanager.subscription.v1.CustomerSubscriptionProxyTokenEntityBackConnectR\vbackConnect\x12]\n" +
	"\x05proxy\x18\x05 \x01(\v2G.proxymanager.subscription.v1.CustomerSubscriptionProxyTokenEntityProxyR\x05proxy\"\x8c\x02\n" +
	"/CustomerSubscriptionProxyTokenEntityBackConnect\x125\n" +
	"\x17id_back_connect_manager\x18\x01 \x01(\tR\x14idBackConnectManager\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12T\n" +
	"\acountry\x18\x03 \x01(\v2:.proxymanager.subscription.v1.CustomerSubscriptionLocationR\acountry\x12\x19\n" +
	"\bip_proxy\x18\x04 \x01(\tR\aipProxy\x12\x1d\n" +
	"\n" +
	"port_proxy\x18\x05 \x01(\x03R\tportProxy\"\xdb\x02\n" +
	")CustomerSubscriptionProxyTokenEntityProxy\x12T\n" +
	"\acountry\x18\x01 \x01(\v2:.proxymanager.subscription.v1.CustomerSubscriptionLocationR\acountry\x12P\n" +
	"\x05state\x18\x02 \x01(\v2:.proxymanager.subscription.v1.CustomerSubscriptionLocationR\x05state\x12\x1b\n" +
	"\tpublic_ip\x18\x03 \x01(\tR\bpublicIp\x12\x1b\n" +
	"\tip_allows\x18\x04 \x03(\tR\bipAllows\x12%\n" +
	"\x0eusername_proxy\x18\x05 \x01(\tR\rusernameProxy\x12%\n" +
	"\x0epassword_proxy\x18\x06 \x01(\tR\rpasswordProxy\"z\n" +
	"\x1cCustomerSubscriptionLocation\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x120\n" +
	"\x05level\x18\x02 \x01(\x0e2\x1a.algoenum.v1.LocationLevelR\x05level\x12\x14\n" +
	"\x05emoji\x18\x03 \x01(\tR\x05emoji\"\xf4\x01\n" +
	"3CustomerSubscriptionServiceFetchSubscriptionRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12'\n" +
	"\x0fid_subscription\x18\x02 \x01(\tR\x0eidSubscription\x12\x1d\n" +
	"\n" +
	"is_expired\x18\x03 \x01(\bR\tisExpired\x12\x1f\n" +
	"\vname_search\x18\x04 \x01(\tR\n" +
	"nameSearch\x12;\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x83\x02\n" +
	"4CustomerSubscriptionServiceFetchSubscriptionResponse\x12^\n" +
	"\rsubscriptions\x18\x01 \x03(\v28.proxymanager.subscription.v1.CustomerSubscriptionEntityR\rsubscriptions\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\x82\x02\n" +
	"\x1aCustomerSubscriptionEntity\x12'\n" +
	"\x0fid_subscription\x18\x01 \x01(\tR\x0eidSubscription\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x126\n" +
	"\x17data_transfer_remaining\x18\x03 \x01(\x01R\x15dataTransferRemaining\x12\x1d\n" +
	"\n" +
	"expired_at\x18\x04 \x01(\x03R\texpiredAt\x12P\n" +
	"\x04plan\x18\x05 \x01(\v2<.proxymanager.subscription.v1.CustomerSubscriptionPlanEntityR\x04plan\"x\n" +
	"\x1eCustomerSubscriptionPlanEntity\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12)\n" +
	"\x10concurrent_proxy\x18\x03 \x01(\x03R\x0fconcurrentProxy2\xd7\n" +
	"\n" +
	"\x1bCustomerSubscriptionService\x12\xae\x01\n" +
	"\rFetchUserPlan\x12M.proxymanager.subscription.v1.CustomerSubscriptionServiceFetchUserPlanRequest\x1aN.proxymanager.subscription.v1.CustomerSubscriptionServiceFetchUserPlanResponse\x12\xba\x01\n" +
	"\x11FetchSubscription\x12Q.proxymanager.subscription.v1.CustomerSubscriptionServiceFetchSubscriptionRequest\x1aR.proxymanager.subscription.v1.CustomerSubscriptionServiceFetchSubscriptionResponse\x12\xbd\x01\n" +
	"\x12UpdateSubscription\x12R.proxymanager.subscription.v1.CustomerSubscriptionServiceUpdateSubscriptionRequest\x1aS.proxymanager.subscription.v1.CustomerSubscriptionServiceUpdateSubscriptionResponse\x12\xb4\x01\n" +
	"\x0fFetchProxyToken\x12O.proxymanager.subscription.v1.CustomerSubscriptionServiceFetchProxyTokenRequest\x1aP.proxymanager.subscription.v1.CustomerSubscriptionServiceFetchProxyTokenResponse\x12\xd8\x01\n" +
	"\x1bUpdateBackConnectProxyToken\x12[.proxymanager.subscription.v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest\x1a\\.proxymanager.subscription.v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse\x12\xb7\x01\n" +
	"\x10RevokeProxyToken\x12P.proxymanager.subscription.v1.CustomerSubscriptionServiceRevokeProxyTokenRequest\x1aQ.proxymanager.subscription.v1.CustomerSubscriptionServiceRevokeProxyTokenResponse\x12\xbd\x01\n" +
	"\x12DeleteSubscription\x12R.proxymanager.subscription.v1.CustomerSubscriptionServiceDeleteSubscriptionRequest\x1aS.proxymanager.subscription.v1.CustomerSubscriptionServiceDeleteSubscriptionResponseB\\ZZgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/subscription/v1;subscriptionv1b\x06proto3"

var (
	file_proxymanager_subscription_v1_customer_proto_rawDescOnce sync.Once
	file_proxymanager_subscription_v1_customer_proto_rawDescData []byte
)

func file_proxymanager_subscription_v1_customer_proto_rawDescGZIP() []byte {
	file_proxymanager_subscription_v1_customer_proto_rawDescOnce.Do(func() {
		file_proxymanager_subscription_v1_customer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_subscription_v1_customer_proto_rawDesc), len(file_proxymanager_subscription_v1_customer_proto_rawDesc)))
	})
	return file_proxymanager_subscription_v1_customer_proto_rawDescData
}

var file_proxymanager_subscription_v1_customer_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_proxymanager_subscription_v1_customer_proto_goTypes = []any{
	(*CustomerSubscriptionServiceUpdateSubscriptionRequest)(nil),           // 0: proxymanager.subscription.v1.CustomerSubscriptionServiceUpdateSubscriptionRequest
	(*CustomerSubscriptionServiceUpdateSubscriptionResponse)(nil),          // 1: proxymanager.subscription.v1.CustomerSubscriptionServiceUpdateSubscriptionResponse
	(*CustomerSubscriptionServiceRevokeProxyTokenRequest)(nil),             // 2: proxymanager.subscription.v1.CustomerSubscriptionServiceRevokeProxyTokenRequest
	(*CustomerSubscriptionServiceRevokeProxyTokenResponse)(nil),            // 3: proxymanager.subscription.v1.CustomerSubscriptionServiceRevokeProxyTokenResponse
	(*CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest)(nil),  // 4: proxymanager.subscription.v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest
	(*CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse)(nil), // 5: proxymanager.subscription.v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse
	(*CustomerSubscriptionServiceFetchUserPlanRequest)(nil),                // 6: proxymanager.subscription.v1.CustomerSubscriptionServiceFetchUserPlanRequest
	(*CustomerSubscriptionServiceFetchUserPlanResponse)(nil),               // 7: proxymanager.subscription.v1.CustomerSubscriptionServiceFetchUserPlanResponse
	(*CustomerSubscriptionServiceFetchUserPlanPlan)(nil),                   // 8: proxymanager.subscription.v1.CustomerSubscriptionServiceFetchUserPlanPlan
	(*CustomerSubscriptionServiceDeleteSubscriptionRequest)(nil),           // 9: proxymanager.subscription.v1.CustomerSubscriptionServiceDeleteSubscriptionRequest
	(*CustomerSubscriptionServiceDeleteSubscriptionResponse)(nil),          // 10: proxymanager.subscription.v1.CustomerSubscriptionServiceDeleteSubscriptionResponse
	(*CustomerSubscriptionServiceFetchProxyTokenRequest)(nil),              // 11: proxymanager.subscription.v1.CustomerSubscriptionServiceFetchProxyTokenRequest
	(*CustomerSubscriptionServiceFetchProxyTokenResponse)(nil),             // 12: proxymanager.subscription.v1.CustomerSubscriptionServiceFetchProxyTokenResponse
	(*CustomerSubscriptionProxyTokenEntity)(nil),                           // 13: proxymanager.subscription.v1.CustomerSubscriptionProxyTokenEntity
	(*CustomerSubscriptionProxyTokenEntityBackConnect)(nil),                // 14: proxymanager.subscription.v1.CustomerSubscriptionProxyTokenEntityBackConnect
	(*CustomerSubscriptionProxyTokenEntityProxy)(nil),                      // 15: proxymanager.subscription.v1.CustomerSubscriptionProxyTokenEntityProxy
	(*CustomerSubscriptionLocation)(nil),                                   // 16: proxymanager.subscription.v1.CustomerSubscriptionLocation
	(*CustomerSubscriptionServiceFetchSubscriptionRequest)(nil),            // 17: proxymanager.subscription.v1.CustomerSubscriptionServiceFetchSubscriptionRequest
	(*CustomerSubscriptionServiceFetchSubscriptionResponse)(nil),           // 18: proxymanager.subscription.v1.CustomerSubscriptionServiceFetchSubscriptionResponse
	(*CustomerSubscriptionEntity)(nil),                                     // 19: proxymanager.subscription.v1.CustomerSubscriptionEntity
	(*CustomerSubscriptionPlanEntity)(nil),                                 // 20: proxymanager.subscription.v1.CustomerSubscriptionPlanEntity
	(*v1.ErrorMessage)(nil),                                                // 21: errmsg.v1.ErrorMessage
	(*v11.PaginationResponse)(nil),                                         // 22: utils.v1.PaginationResponse
	(*v11.PaginationRequest)(nil),                                          // 23: utils.v1.PaginationRequest
	(v12.LocationLevel)(0),                                                 // 24: algoenum.v1.LocationLevel
}
var file_proxymanager_subscription_v1_customer_proto_depIdxs = []int32{
	21, // 0: proxymanager.subscription.v1.CustomerSubscriptionServiceUpdateSubscriptionResponse.error:type_name -> errmsg.v1.ErrorMessage
	21, // 1: proxymanager.subscription.v1.CustomerSubscriptionServiceRevokeProxyTokenResponse.error:type_name -> errmsg.v1.ErrorMessage
	21, // 2: proxymanager.subscription.v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse.error:type_name -> errmsg.v1.ErrorMessage
	21, // 3: proxymanager.subscription.v1.CustomerSubscriptionServiceFetchUserPlanResponse.error:type_name -> errmsg.v1.ErrorMessage
	22, // 4: proxymanager.subscription.v1.CustomerSubscriptionServiceFetchUserPlanResponse.pagination:type_name -> utils.v1.PaginationResponse
	8,  // 5: proxymanager.subscription.v1.CustomerSubscriptionServiceFetchUserPlanResponse.plans:type_name -> proxymanager.subscription.v1.CustomerSubscriptionServiceFetchUserPlanPlan
	21, // 6: proxymanager.subscription.v1.CustomerSubscriptionServiceDeleteSubscriptionResponse.error:type_name -> errmsg.v1.ErrorMessage
	23, // 7: proxymanager.subscription.v1.CustomerSubscriptionServiceFetchProxyTokenRequest.pagination:type_name -> utils.v1.PaginationRequest
	13, // 8: proxymanager.subscription.v1.CustomerSubscriptionServiceFetchProxyTokenResponse.proxy_tokens:type_name -> proxymanager.subscription.v1.CustomerSubscriptionProxyTokenEntity
	21, // 9: proxymanager.subscription.v1.CustomerSubscriptionServiceFetchProxyTokenResponse.error:type_name -> errmsg.v1.ErrorMessage
	22, // 10: proxymanager.subscription.v1.CustomerSubscriptionServiceFetchProxyTokenResponse.pagination:type_name -> utils.v1.PaginationResponse
	14, // 11: proxymanager.subscription.v1.CustomerSubscriptionProxyTokenEntity.back_connect:type_name -> proxymanager.subscription.v1.CustomerSubscriptionProxyTokenEntityBackConnect
	15, // 12: proxymanager.subscription.v1.CustomerSubscriptionProxyTokenEntity.proxy:type_name -> proxymanager.subscription.v1.CustomerSubscriptionProxyTokenEntityProxy
	16, // 13: proxymanager.subscription.v1.CustomerSubscriptionProxyTokenEntityBackConnect.country:type_name -> proxymanager.subscription.v1.CustomerSubscriptionLocation
	16, // 14: proxymanager.subscription.v1.CustomerSubscriptionProxyTokenEntityProxy.country:type_name -> proxymanager.subscription.v1.CustomerSubscriptionLocation
	16, // 15: proxymanager.subscription.v1.CustomerSubscriptionProxyTokenEntityProxy.state:type_name -> proxymanager.subscription.v1.CustomerSubscriptionLocation
	24, // 16: proxymanager.subscription.v1.CustomerSubscriptionLocation.level:type_name -> algoenum.v1.LocationLevel
	23, // 17: proxymanager.subscription.v1.CustomerSubscriptionServiceFetchSubscriptionRequest.pagination:type_name -> utils.v1.PaginationRequest
	19, // 18: proxymanager.subscription.v1.CustomerSubscriptionServiceFetchSubscriptionResponse.subscriptions:type_name -> proxymanager.subscription.v1.CustomerSubscriptionEntity
	21, // 19: proxymanager.subscription.v1.CustomerSubscriptionServiceFetchSubscriptionResponse.error:type_name -> errmsg.v1.ErrorMessage
	22, // 20: proxymanager.subscription.v1.CustomerSubscriptionServiceFetchSubscriptionResponse.pagination:type_name -> utils.v1.PaginationResponse
	20, // 21: proxymanager.subscription.v1.CustomerSubscriptionEntity.plan:type_name -> proxymanager.subscription.v1.CustomerSubscriptionPlanEntity
	6,  // 22: proxymanager.subscription.v1.CustomerSubscriptionService.FetchUserPlan:input_type -> proxymanager.subscription.v1.CustomerSubscriptionServiceFetchUserPlanRequest
	17, // 23: proxymanager.subscription.v1.CustomerSubscriptionService.FetchSubscription:input_type -> proxymanager.subscription.v1.CustomerSubscriptionServiceFetchSubscriptionRequest
	0,  // 24: proxymanager.subscription.v1.CustomerSubscriptionService.UpdateSubscription:input_type -> proxymanager.subscription.v1.CustomerSubscriptionServiceUpdateSubscriptionRequest
	11, // 25: proxymanager.subscription.v1.CustomerSubscriptionService.FetchProxyToken:input_type -> proxymanager.subscription.v1.CustomerSubscriptionServiceFetchProxyTokenRequest
	4,  // 26: proxymanager.subscription.v1.CustomerSubscriptionService.UpdateBackConnectProxyToken:input_type -> proxymanager.subscription.v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest
	2,  // 27: proxymanager.subscription.v1.CustomerSubscriptionService.RevokeProxyToken:input_type -> proxymanager.subscription.v1.CustomerSubscriptionServiceRevokeProxyTokenRequest
	9,  // 28: proxymanager.subscription.v1.CustomerSubscriptionService.DeleteSubscription:input_type -> proxymanager.subscription.v1.CustomerSubscriptionServiceDeleteSubscriptionRequest
	7,  // 29: proxymanager.subscription.v1.CustomerSubscriptionService.FetchUserPlan:output_type -> proxymanager.subscription.v1.CustomerSubscriptionServiceFetchUserPlanResponse
	18, // 30: proxymanager.subscription.v1.CustomerSubscriptionService.FetchSubscription:output_type -> proxymanager.subscription.v1.CustomerSubscriptionServiceFetchSubscriptionResponse
	1,  // 31: proxymanager.subscription.v1.CustomerSubscriptionService.UpdateSubscription:output_type -> proxymanager.subscription.v1.CustomerSubscriptionServiceUpdateSubscriptionResponse
	12, // 32: proxymanager.subscription.v1.CustomerSubscriptionService.FetchProxyToken:output_type -> proxymanager.subscription.v1.CustomerSubscriptionServiceFetchProxyTokenResponse
	5,  // 33: proxymanager.subscription.v1.CustomerSubscriptionService.UpdateBackConnectProxyToken:output_type -> proxymanager.subscription.v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse
	3,  // 34: proxymanager.subscription.v1.CustomerSubscriptionService.RevokeProxyToken:output_type -> proxymanager.subscription.v1.CustomerSubscriptionServiceRevokeProxyTokenResponse
	10, // 35: proxymanager.subscription.v1.CustomerSubscriptionService.DeleteSubscription:output_type -> proxymanager.subscription.v1.CustomerSubscriptionServiceDeleteSubscriptionResponse
	29, // [29:36] is the sub-list for method output_type
	22, // [22:29] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_proxymanager_subscription_v1_customer_proto_init() }
func file_proxymanager_subscription_v1_customer_proto_init() {
	if File_proxymanager_subscription_v1_customer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_subscription_v1_customer_proto_rawDesc), len(file_proxymanager_subscription_v1_customer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_subscription_v1_customer_proto_goTypes,
		DependencyIndexes: file_proxymanager_subscription_v1_customer_proto_depIdxs,
		MessageInfos:      file_proxymanager_subscription_v1_customer_proto_msgTypes,
	}.Build()
	File_proxymanager_subscription_v1_customer_proto = out.File
	file_proxymanager_subscription_v1_customer_proto_goTypes = nil
	file_proxymanager_subscription_v1_customer_proto_depIdxs = nil
}
