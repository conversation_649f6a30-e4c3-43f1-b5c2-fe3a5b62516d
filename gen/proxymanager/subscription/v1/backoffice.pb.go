// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/subscription/v1/backoffice.proto

package subscriptionv1

import (
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackofficeSubscriptionServiceFetchProxyTokenRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant     string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	IdUser         string                 `protobuf:"bytes,2,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	IdPlan         string                 `protobuf:"bytes,3,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdSubscription string                 `protobuf:"bytes,4,opt,name=id_subscription,json=idSubscription,proto3" json:"id_subscription,omitempty"`
	Pagination     *v1.PaginationRequest  `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BackofficeSubscriptionServiceFetchProxyTokenRequest) Reset() {
	*x = BackofficeSubscriptionServiceFetchProxyTokenRequest{}
	mi := &file_proxymanager_subscription_v1_backoffice_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeSubscriptionServiceFetchProxyTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeSubscriptionServiceFetchProxyTokenRequest) ProtoMessage() {}

func (x *BackofficeSubscriptionServiceFetchProxyTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_backoffice_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeSubscriptionServiceFetchProxyTokenRequest.ProtoReflect.Descriptor instead.
func (*BackofficeSubscriptionServiceFetchProxyTokenRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_backoffice_proto_rawDescGZIP(), []int{0}
}

func (x *BackofficeSubscriptionServiceFetchProxyTokenRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficeSubscriptionServiceFetchProxyTokenRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *BackofficeSubscriptionServiceFetchProxyTokenRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *BackofficeSubscriptionServiceFetchProxyTokenRequest) GetIdSubscription() string {
	if x != nil {
		return x.IdSubscription
	}
	return ""
}

func (x *BackofficeSubscriptionServiceFetchProxyTokenRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeSubscriptionServiceFetchProxyTokenResponse struct {
	state         protoimpl.MessageState                    `protogen:"open.v1"`
	ProxyTokens   []*BackofficeSubscriptionProxyTokenEntity `protobuf:"bytes,1,rep,name=proxy_tokens,json=proxyTokens,proto3" json:"proxy_tokens,omitempty"`
	Error         *v11.ErrorMessage                         `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v1.PaginationResponse                    `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeSubscriptionServiceFetchProxyTokenResponse) Reset() {
	*x = BackofficeSubscriptionServiceFetchProxyTokenResponse{}
	mi := &file_proxymanager_subscription_v1_backoffice_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeSubscriptionServiceFetchProxyTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeSubscriptionServiceFetchProxyTokenResponse) ProtoMessage() {}

func (x *BackofficeSubscriptionServiceFetchProxyTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_backoffice_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeSubscriptionServiceFetchProxyTokenResponse.ProtoReflect.Descriptor instead.
func (*BackofficeSubscriptionServiceFetchProxyTokenResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_backoffice_proto_rawDescGZIP(), []int{1}
}

func (x *BackofficeSubscriptionServiceFetchProxyTokenResponse) GetProxyTokens() []*BackofficeSubscriptionProxyTokenEntity {
	if x != nil {
		return x.ProxyTokens
	}
	return nil
}

func (x *BackofficeSubscriptionServiceFetchProxyTokenResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeSubscriptionServiceFetchProxyTokenResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeSubscriptionProxyTokenEntity struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdProxyToken   string                 `protobuf:"bytes,1,opt,name=id_proxy_token,json=idProxyToken,proto3" json:"id_proxy_token,omitempty"`
	IdProxyProfile string                 `protobuf:"bytes,2,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	ProxyToken     string                 `protobuf:"bytes,3,opt,name=proxy_token,json=proxyToken,proto3" json:"proxy_token,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BackofficeSubscriptionProxyTokenEntity) Reset() {
	*x = BackofficeSubscriptionProxyTokenEntity{}
	mi := &file_proxymanager_subscription_v1_backoffice_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeSubscriptionProxyTokenEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeSubscriptionProxyTokenEntity) ProtoMessage() {}

func (x *BackofficeSubscriptionProxyTokenEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_backoffice_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeSubscriptionProxyTokenEntity.ProtoReflect.Descriptor instead.
func (*BackofficeSubscriptionProxyTokenEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_backoffice_proto_rawDescGZIP(), []int{2}
}

func (x *BackofficeSubscriptionProxyTokenEntity) GetIdProxyToken() string {
	if x != nil {
		return x.IdProxyToken
	}
	return ""
}

func (x *BackofficeSubscriptionProxyTokenEntity) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

func (x *BackofficeSubscriptionProxyTokenEntity) GetProxyToken() string {
	if x != nil {
		return x.ProxyToken
	}
	return ""
}

type BackofficeSubscriptionServiceFetchSubscriptionRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant     string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	IdUser         string                 `protobuf:"bytes,2,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	IdPlan         string                 `protobuf:"bytes,3,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdSubscription string                 `protobuf:"bytes,4,opt,name=id_subscription,json=idSubscription,proto3" json:"id_subscription,omitempty"`
	IsExpired      bool                   `protobuf:"varint,5,opt,name=is_expired,json=isExpired,proto3" json:"is_expired,omitempty"`
	State          *v1.State              `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	Pagination     *v1.PaginationRequest  `protobuf:"bytes,7,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BackofficeSubscriptionServiceFetchSubscriptionRequest) Reset() {
	*x = BackofficeSubscriptionServiceFetchSubscriptionRequest{}
	mi := &file_proxymanager_subscription_v1_backoffice_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeSubscriptionServiceFetchSubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeSubscriptionServiceFetchSubscriptionRequest) ProtoMessage() {}

func (x *BackofficeSubscriptionServiceFetchSubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_backoffice_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeSubscriptionServiceFetchSubscriptionRequest.ProtoReflect.Descriptor instead.
func (*BackofficeSubscriptionServiceFetchSubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_backoffice_proto_rawDescGZIP(), []int{3}
}

func (x *BackofficeSubscriptionServiceFetchSubscriptionRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficeSubscriptionServiceFetchSubscriptionRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *BackofficeSubscriptionServiceFetchSubscriptionRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *BackofficeSubscriptionServiceFetchSubscriptionRequest) GetIdSubscription() string {
	if x != nil {
		return x.IdSubscription
	}
	return ""
}

func (x *BackofficeSubscriptionServiceFetchSubscriptionRequest) GetIsExpired() bool {
	if x != nil {
		return x.IsExpired
	}
	return false
}

func (x *BackofficeSubscriptionServiceFetchSubscriptionRequest) GetState() *v1.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeSubscriptionServiceFetchSubscriptionRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeSubscriptionServiceFetchSubscriptionResponse struct {
	state         protoimpl.MessageState          `protogen:"open.v1"`
	Subscriptions []*BackofficeSubscriptionEntity `protobuf:"bytes,1,rep,name=subscriptions,proto3" json:"subscriptions,omitempty"`
	Error         *v11.ErrorMessage               `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v1.PaginationResponse          `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeSubscriptionServiceFetchSubscriptionResponse) Reset() {
	*x = BackofficeSubscriptionServiceFetchSubscriptionResponse{}
	mi := &file_proxymanager_subscription_v1_backoffice_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeSubscriptionServiceFetchSubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeSubscriptionServiceFetchSubscriptionResponse) ProtoMessage() {}

func (x *BackofficeSubscriptionServiceFetchSubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_backoffice_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeSubscriptionServiceFetchSubscriptionResponse.ProtoReflect.Descriptor instead.
func (*BackofficeSubscriptionServiceFetchSubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_backoffice_proto_rawDescGZIP(), []int{4}
}

func (x *BackofficeSubscriptionServiceFetchSubscriptionResponse) GetSubscriptions() []*BackofficeSubscriptionEntity {
	if x != nil {
		return x.Subscriptions
	}
	return nil
}

func (x *BackofficeSubscriptionServiceFetchSubscriptionResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeSubscriptionServiceFetchSubscriptionResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeSubscriptionEntity struct {
	state                 protoimpl.MessageState            `protogen:"open.v1"`
	IdSubscription        string                            `protobuf:"bytes,1,opt,name=id_subscription,json=idSubscription,proto3" json:"id_subscription,omitempty"`
	ExpiredAt             int64                             `protobuf:"varint,4,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	DataTransferRemaining float64                           `protobuf:"fixed64,5,opt,name=data_transfer_remaining,json=dataTransferRemaining,proto3" json:"data_transfer_remaining,omitempty"`
	IsActive              bool                              `protobuf:"varint,6,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	Plan                  *BackofficeSubscriptionPlanEntity `protobuf:"bytes,7,opt,name=plan,proto3" json:"plan,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *BackofficeSubscriptionEntity) Reset() {
	*x = BackofficeSubscriptionEntity{}
	mi := &file_proxymanager_subscription_v1_backoffice_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeSubscriptionEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeSubscriptionEntity) ProtoMessage() {}

func (x *BackofficeSubscriptionEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_backoffice_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeSubscriptionEntity.ProtoReflect.Descriptor instead.
func (*BackofficeSubscriptionEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_backoffice_proto_rawDescGZIP(), []int{5}
}

func (x *BackofficeSubscriptionEntity) GetIdSubscription() string {
	if x != nil {
		return x.IdSubscription
	}
	return ""
}

func (x *BackofficeSubscriptionEntity) GetExpiredAt() int64 {
	if x != nil {
		return x.ExpiredAt
	}
	return 0
}

func (x *BackofficeSubscriptionEntity) GetDataTransferRemaining() float64 {
	if x != nil {
		return x.DataTransferRemaining
	}
	return 0
}

func (x *BackofficeSubscriptionEntity) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *BackofficeSubscriptionEntity) GetPlan() *BackofficeSubscriptionPlanEntity {
	if x != nil {
		return x.Plan
	}
	return nil
}

type BackofficeSubscriptionPlanEntity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdPlan        string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IsActive      bool                   `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeSubscriptionPlanEntity) Reset() {
	*x = BackofficeSubscriptionPlanEntity{}
	mi := &file_proxymanager_subscription_v1_backoffice_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeSubscriptionPlanEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeSubscriptionPlanEntity) ProtoMessage() {}

func (x *BackofficeSubscriptionPlanEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_subscription_v1_backoffice_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeSubscriptionPlanEntity.ProtoReflect.Descriptor instead.
func (*BackofficeSubscriptionPlanEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_subscription_v1_backoffice_proto_rawDescGZIP(), []int{6}
}

func (x *BackofficeSubscriptionPlanEntity) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *BackofficeSubscriptionPlanEntity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeSubscriptionPlanEntity) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

var File_proxymanager_subscription_v1_backoffice_proto protoreflect.FileDescriptor

const file_proxymanager_subscription_v1_backoffice_proto_rawDesc = "" +
	"\n" +
	"-proxymanager/subscription/v1/backoffice.proto\x12\x1cproxymanager.subscription.v1\x1a\x14utils/v1/utils.proto\x1a\x18errmsg/v1/errormsg.proto\"\xee\x01\n" +
	"3BackofficeSubscriptionServiceFetchProxyTokenRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12\x17\n" +
	"\aid_user\x18\x02 \x01(\tR\x06idUser\x12\x17\n" +
	"\aid_plan\x18\x03 \x01(\tR\x06idPlan\x12'\n" +
	"\x0fid_subscription\x18\x04 \x01(\tR\x0eidSubscription\x12;\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x8c\x02\n" +
	"4BackofficeSubscriptionServiceFetchProxyTokenResponse\x12g\n" +
	"\fproxy_tokens\x18\x01 \x03(\v2D.proxymanager.subscription.v1.BackofficeSubscriptionProxyTokenEntityR\vproxyTokens\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\x99\x01\n" +
	"&BackofficeSubscriptionProxyTokenEntity\x12$\n" +
	"\x0eid_proxy_token\x18\x01 \x01(\tR\fidProxyToken\x12(\n" +
	"\x10id_proxy_profile\x18\x02 \x01(\tR\x0eidProxyProfile\x12\x1f\n" +
	"\vproxy_token\x18\x03 \x01(\tR\n" +
	"proxyToken\"\xb6\x02\n" +
	"5BackofficeSubscriptionServiceFetchSubscriptionRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12\x17\n" +
	"\aid_user\x18\x02 \x01(\tR\x06idUser\x12\x17\n" +
	"\aid_plan\x18\x03 \x01(\tR\x06idPlan\x12'\n" +
	"\x0fid_subscription\x18\x04 \x01(\tR\x0eidSubscription\x12\x1d\n" +
	"\n" +
	"is_expired\x18\x05 \x01(\bR\tisExpired\x12%\n" +
	"\x05state\x18\x06 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\a \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x87\x02\n" +
	"6BackofficeSubscriptionServiceFetchSubscriptionResponse\x12`\n" +
	"\rsubscriptions\x18\x01 \x03(\v2:.proxymanager.subscription.v1.BackofficeSubscriptionEntityR\rsubscriptions\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\x8f\x02\n" +
	"\x1cBackofficeSubscriptionEntity\x12'\n" +
	"\x0fid_subscription\x18\x01 \x01(\tR\x0eidSubscription\x12\x1d\n" +
	"\n" +
	"expired_at\x18\x04 \x01(\x03R\texpiredAt\x126\n" +
	"\x17data_transfer_remaining\x18\x05 \x01(\x01R\x15dataTransferRemaining\x12\x1b\n" +
	"\tis_active\x18\x06 \x01(\bR\bisActive\x12R\n" +
	"\x04plan\x18\a \x01(\v2>.proxymanager.subscription.v1.BackofficeSubscriptionPlanEntityR\x04plan\"l\n" +
	" BackofficeSubscriptionPlanEntity\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1b\n" +
	"\tis_active\x18\x04 \x01(\bR\bisActive2\x9b\x03\n" +
	"\x1dBackofficeSubscriptionService\x12\xbe\x01\n" +
	"\x11FetchSubscription\x12S.proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchSubscriptionRequest\x1aT.proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchSubscriptionResponse\x12\xb8\x01\n" +
	"\x0fFetchProxyToken\x12Q.proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchProxyTokenRequest\x1aR.proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchProxyTokenResponseB\\ZZgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/subscription/v1;subscriptionv1b\x06proto3"

var (
	file_proxymanager_subscription_v1_backoffice_proto_rawDescOnce sync.Once
	file_proxymanager_subscription_v1_backoffice_proto_rawDescData []byte
)

func file_proxymanager_subscription_v1_backoffice_proto_rawDescGZIP() []byte {
	file_proxymanager_subscription_v1_backoffice_proto_rawDescOnce.Do(func() {
		file_proxymanager_subscription_v1_backoffice_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_subscription_v1_backoffice_proto_rawDesc), len(file_proxymanager_subscription_v1_backoffice_proto_rawDesc)))
	})
	return file_proxymanager_subscription_v1_backoffice_proto_rawDescData
}

var file_proxymanager_subscription_v1_backoffice_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_proxymanager_subscription_v1_backoffice_proto_goTypes = []any{
	(*BackofficeSubscriptionServiceFetchProxyTokenRequest)(nil),    // 0: proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchProxyTokenRequest
	(*BackofficeSubscriptionServiceFetchProxyTokenResponse)(nil),   // 1: proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchProxyTokenResponse
	(*BackofficeSubscriptionProxyTokenEntity)(nil),                 // 2: proxymanager.subscription.v1.BackofficeSubscriptionProxyTokenEntity
	(*BackofficeSubscriptionServiceFetchSubscriptionRequest)(nil),  // 3: proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchSubscriptionRequest
	(*BackofficeSubscriptionServiceFetchSubscriptionResponse)(nil), // 4: proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchSubscriptionResponse
	(*BackofficeSubscriptionEntity)(nil),                           // 5: proxymanager.subscription.v1.BackofficeSubscriptionEntity
	(*BackofficeSubscriptionPlanEntity)(nil),                       // 6: proxymanager.subscription.v1.BackofficeSubscriptionPlanEntity
	(*v1.PaginationRequest)(nil),                                   // 7: utils.v1.PaginationRequest
	(*v11.ErrorMessage)(nil),                                       // 8: errmsg.v1.ErrorMessage
	(*v1.PaginationResponse)(nil),                                  // 9: utils.v1.PaginationResponse
	(*v1.State)(nil),                                               // 10: utils.v1.State
}
var file_proxymanager_subscription_v1_backoffice_proto_depIdxs = []int32{
	7,  // 0: proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchProxyTokenRequest.pagination:type_name -> utils.v1.PaginationRequest
	2,  // 1: proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchProxyTokenResponse.proxy_tokens:type_name -> proxymanager.subscription.v1.BackofficeSubscriptionProxyTokenEntity
	8,  // 2: proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchProxyTokenResponse.error:type_name -> errmsg.v1.ErrorMessage
	9,  // 3: proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchProxyTokenResponse.pagination:type_name -> utils.v1.PaginationResponse
	10, // 4: proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchSubscriptionRequest.state:type_name -> utils.v1.State
	7,  // 5: proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchSubscriptionRequest.pagination:type_name -> utils.v1.PaginationRequest
	5,  // 6: proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchSubscriptionResponse.subscriptions:type_name -> proxymanager.subscription.v1.BackofficeSubscriptionEntity
	8,  // 7: proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchSubscriptionResponse.error:type_name -> errmsg.v1.ErrorMessage
	9,  // 8: proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchSubscriptionResponse.pagination:type_name -> utils.v1.PaginationResponse
	6,  // 9: proxymanager.subscription.v1.BackofficeSubscriptionEntity.plan:type_name -> proxymanager.subscription.v1.BackofficeSubscriptionPlanEntity
	3,  // 10: proxymanager.subscription.v1.BackofficeSubscriptionService.FetchSubscription:input_type -> proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchSubscriptionRequest
	0,  // 11: proxymanager.subscription.v1.BackofficeSubscriptionService.FetchProxyToken:input_type -> proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchProxyTokenRequest
	4,  // 12: proxymanager.subscription.v1.BackofficeSubscriptionService.FetchSubscription:output_type -> proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchSubscriptionResponse
	1,  // 13: proxymanager.subscription.v1.BackofficeSubscriptionService.FetchProxyToken:output_type -> proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchProxyTokenResponse
	12, // [12:14] is the sub-list for method output_type
	10, // [10:12] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_proxymanager_subscription_v1_backoffice_proto_init() }
func file_proxymanager_subscription_v1_backoffice_proto_init() {
	if File_proxymanager_subscription_v1_backoffice_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_subscription_v1_backoffice_proto_rawDesc), len(file_proxymanager_subscription_v1_backoffice_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_subscription_v1_backoffice_proto_goTypes,
		DependencyIndexes: file_proxymanager_subscription_v1_backoffice_proto_depIdxs,
		MessageInfos:      file_proxymanager_subscription_v1_backoffice_proto_msgTypes,
	}.Build()
	File_proxymanager_subscription_v1_backoffice_proto = out.File
	file_proxymanager_subscription_v1_backoffice_proto_goTypes = nil
	file_proxymanager_subscription_v1_backoffice_proto_depIdxs = nil
}
