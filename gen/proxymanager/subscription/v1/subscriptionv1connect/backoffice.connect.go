// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/subscription/v1/backoffice.proto

package subscriptionv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/subscription/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackofficeSubscriptionServiceName is the fully-qualified name of the
	// BackofficeSubscriptionService service.
	BackofficeSubscriptionServiceName = "proxymanager.subscription.v1.BackofficeSubscriptionService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackofficeSubscriptionServiceFetchSubscriptionProcedure is the fully-qualified name of the
	// BackofficeSubscriptionService's FetchSubscription RPC.
	BackofficeSubscriptionServiceFetchSubscriptionProcedure = "/proxymanager.subscription.v1.BackofficeSubscriptionService/FetchSubscription"
	// BackofficeSubscriptionServiceFetchProxyTokenProcedure is the fully-qualified name of the
	// BackofficeSubscriptionService's FetchProxyToken RPC.
	BackofficeSubscriptionServiceFetchProxyTokenProcedure = "/proxymanager.subscription.v1.BackofficeSubscriptionService/FetchProxyToken"
)

// BackofficeSubscriptionServiceClient is a client for the
// proxymanager.subscription.v1.BackofficeSubscriptionService service.
type BackofficeSubscriptionServiceClient interface {
	FetchSubscription(context.Context, *connect.Request[v1.BackofficeSubscriptionServiceFetchSubscriptionRequest]) (*connect.Response[v1.BackofficeSubscriptionServiceFetchSubscriptionResponse], error)
	FetchProxyToken(context.Context, *connect.Request[v1.BackofficeSubscriptionServiceFetchProxyTokenRequest]) (*connect.Response[v1.BackofficeSubscriptionServiceFetchProxyTokenResponse], error)
}

// NewBackofficeSubscriptionServiceClient constructs a client for the
// proxymanager.subscription.v1.BackofficeSubscriptionService service. By default, it uses the
// Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and sends
// uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackofficeSubscriptionServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackofficeSubscriptionServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backofficeSubscriptionServiceMethods := v1.File_proxymanager_subscription_v1_backoffice_proto.Services().ByName("BackofficeSubscriptionService").Methods()
	return &backofficeSubscriptionServiceClient{
		fetchSubscription: connect.NewClient[v1.BackofficeSubscriptionServiceFetchSubscriptionRequest, v1.BackofficeSubscriptionServiceFetchSubscriptionResponse](
			httpClient,
			baseURL+BackofficeSubscriptionServiceFetchSubscriptionProcedure,
			connect.WithSchema(backofficeSubscriptionServiceMethods.ByName("FetchSubscription")),
			connect.WithClientOptions(opts...),
		),
		fetchProxyToken: connect.NewClient[v1.BackofficeSubscriptionServiceFetchProxyTokenRequest, v1.BackofficeSubscriptionServiceFetchProxyTokenResponse](
			httpClient,
			baseURL+BackofficeSubscriptionServiceFetchProxyTokenProcedure,
			connect.WithSchema(backofficeSubscriptionServiceMethods.ByName("FetchProxyToken")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backofficeSubscriptionServiceClient implements BackofficeSubscriptionServiceClient.
type backofficeSubscriptionServiceClient struct {
	fetchSubscription *connect.Client[v1.BackofficeSubscriptionServiceFetchSubscriptionRequest, v1.BackofficeSubscriptionServiceFetchSubscriptionResponse]
	fetchProxyToken   *connect.Client[v1.BackofficeSubscriptionServiceFetchProxyTokenRequest, v1.BackofficeSubscriptionServiceFetchProxyTokenResponse]
}

// FetchSubscription calls
// proxymanager.subscription.v1.BackofficeSubscriptionService.FetchSubscription.
func (c *backofficeSubscriptionServiceClient) FetchSubscription(ctx context.Context, req *connect.Request[v1.BackofficeSubscriptionServiceFetchSubscriptionRequest]) (*connect.Response[v1.BackofficeSubscriptionServiceFetchSubscriptionResponse], error) {
	return c.fetchSubscription.CallUnary(ctx, req)
}

// FetchProxyToken calls proxymanager.subscription.v1.BackofficeSubscriptionService.FetchProxyToken.
func (c *backofficeSubscriptionServiceClient) FetchProxyToken(ctx context.Context, req *connect.Request[v1.BackofficeSubscriptionServiceFetchProxyTokenRequest]) (*connect.Response[v1.BackofficeSubscriptionServiceFetchProxyTokenResponse], error) {
	return c.fetchProxyToken.CallUnary(ctx, req)
}

// BackofficeSubscriptionServiceHandler is an implementation of the
// proxymanager.subscription.v1.BackofficeSubscriptionService service.
type BackofficeSubscriptionServiceHandler interface {
	FetchSubscription(context.Context, *connect.Request[v1.BackofficeSubscriptionServiceFetchSubscriptionRequest]) (*connect.Response[v1.BackofficeSubscriptionServiceFetchSubscriptionResponse], error)
	FetchProxyToken(context.Context, *connect.Request[v1.BackofficeSubscriptionServiceFetchProxyTokenRequest]) (*connect.Response[v1.BackofficeSubscriptionServiceFetchProxyTokenResponse], error)
}

// NewBackofficeSubscriptionServiceHandler builds an HTTP handler from the service implementation.
// It returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackofficeSubscriptionServiceHandler(svc BackofficeSubscriptionServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backofficeSubscriptionServiceMethods := v1.File_proxymanager_subscription_v1_backoffice_proto.Services().ByName("BackofficeSubscriptionService").Methods()
	backofficeSubscriptionServiceFetchSubscriptionHandler := connect.NewUnaryHandler(
		BackofficeSubscriptionServiceFetchSubscriptionProcedure,
		svc.FetchSubscription,
		connect.WithSchema(backofficeSubscriptionServiceMethods.ByName("FetchSubscription")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeSubscriptionServiceFetchProxyTokenHandler := connect.NewUnaryHandler(
		BackofficeSubscriptionServiceFetchProxyTokenProcedure,
		svc.FetchProxyToken,
		connect.WithSchema(backofficeSubscriptionServiceMethods.ByName("FetchProxyToken")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.subscription.v1.BackofficeSubscriptionService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackofficeSubscriptionServiceFetchSubscriptionProcedure:
			backofficeSubscriptionServiceFetchSubscriptionHandler.ServeHTTP(w, r)
		case BackofficeSubscriptionServiceFetchProxyTokenProcedure:
			backofficeSubscriptionServiceFetchProxyTokenHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackofficeSubscriptionServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackofficeSubscriptionServiceHandler struct{}

func (UnimplementedBackofficeSubscriptionServiceHandler) FetchSubscription(context.Context, *connect.Request[v1.BackofficeSubscriptionServiceFetchSubscriptionRequest]) (*connect.Response[v1.BackofficeSubscriptionServiceFetchSubscriptionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.subscription.v1.BackofficeSubscriptionService.FetchSubscription is not implemented"))
}

func (UnimplementedBackofficeSubscriptionServiceHandler) FetchProxyToken(context.Context, *connect.Request[v1.BackofficeSubscriptionServiceFetchProxyTokenRequest]) (*connect.Response[v1.BackofficeSubscriptionServiceFetchProxyTokenResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.subscription.v1.BackofficeSubscriptionService.FetchProxyToken is not implemented"))
}
