// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/subscription/v1/merchant.proto

package subscriptionv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/subscription/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// MerchantSubscriptionServiceName is the fully-qualified name of the MerchantSubscriptionService
	// service.
	MerchantSubscriptionServiceName = "proxymanager.subscription.v1.MerchantSubscriptionService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// MerchantSubscriptionServiceFetchSubscriptionProcedure is the fully-qualified name of the
	// MerchantSubscriptionService's FetchSubscription RPC.
	MerchantSubscriptionServiceFetchSubscriptionProcedure = "/proxymanager.subscription.v1.MerchantSubscriptionService/FetchSubscription"
	// MerchantSubscriptionServiceFetchProxyTokenProcedure is the fully-qualified name of the
	// MerchantSubscriptionService's FetchProxyToken RPC.
	MerchantSubscriptionServiceFetchProxyTokenProcedure = "/proxymanager.subscription.v1.MerchantSubscriptionService/FetchProxyToken"
)

// MerchantSubscriptionServiceClient is a client for the
// proxymanager.subscription.v1.MerchantSubscriptionService service.
type MerchantSubscriptionServiceClient interface {
	FetchSubscription(context.Context, *connect.Request[v1.MerchantSubscriptionServiceFetchSubscriptionRequest]) (*connect.Response[v1.MerchantSubscriptionServiceFetchSubscriptionResponse], error)
	FetchProxyToken(context.Context, *connect.Request[v1.MerchantSubscriptionServiceFetchProxyTokenRequest]) (*connect.Response[v1.MerchantSubscriptionServiceFetchProxyTokenResponse], error)
}

// NewMerchantSubscriptionServiceClient constructs a client for the
// proxymanager.subscription.v1.MerchantSubscriptionService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewMerchantSubscriptionServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) MerchantSubscriptionServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	merchantSubscriptionServiceMethods := v1.File_proxymanager_subscription_v1_merchant_proto.Services().ByName("MerchantSubscriptionService").Methods()
	return &merchantSubscriptionServiceClient{
		fetchSubscription: connect.NewClient[v1.MerchantSubscriptionServiceFetchSubscriptionRequest, v1.MerchantSubscriptionServiceFetchSubscriptionResponse](
			httpClient,
			baseURL+MerchantSubscriptionServiceFetchSubscriptionProcedure,
			connect.WithSchema(merchantSubscriptionServiceMethods.ByName("FetchSubscription")),
			connect.WithClientOptions(opts...),
		),
		fetchProxyToken: connect.NewClient[v1.MerchantSubscriptionServiceFetchProxyTokenRequest, v1.MerchantSubscriptionServiceFetchProxyTokenResponse](
			httpClient,
			baseURL+MerchantSubscriptionServiceFetchProxyTokenProcedure,
			connect.WithSchema(merchantSubscriptionServiceMethods.ByName("FetchProxyToken")),
			connect.WithClientOptions(opts...),
		),
	}
}

// merchantSubscriptionServiceClient implements MerchantSubscriptionServiceClient.
type merchantSubscriptionServiceClient struct {
	fetchSubscription *connect.Client[v1.MerchantSubscriptionServiceFetchSubscriptionRequest, v1.MerchantSubscriptionServiceFetchSubscriptionResponse]
	fetchProxyToken   *connect.Client[v1.MerchantSubscriptionServiceFetchProxyTokenRequest, v1.MerchantSubscriptionServiceFetchProxyTokenResponse]
}

// FetchSubscription calls
// proxymanager.subscription.v1.MerchantSubscriptionService.FetchSubscription.
func (c *merchantSubscriptionServiceClient) FetchSubscription(ctx context.Context, req *connect.Request[v1.MerchantSubscriptionServiceFetchSubscriptionRequest]) (*connect.Response[v1.MerchantSubscriptionServiceFetchSubscriptionResponse], error) {
	return c.fetchSubscription.CallUnary(ctx, req)
}

// FetchProxyToken calls proxymanager.subscription.v1.MerchantSubscriptionService.FetchProxyToken.
func (c *merchantSubscriptionServiceClient) FetchProxyToken(ctx context.Context, req *connect.Request[v1.MerchantSubscriptionServiceFetchProxyTokenRequest]) (*connect.Response[v1.MerchantSubscriptionServiceFetchProxyTokenResponse], error) {
	return c.fetchProxyToken.CallUnary(ctx, req)
}

// MerchantSubscriptionServiceHandler is an implementation of the
// proxymanager.subscription.v1.MerchantSubscriptionService service.
type MerchantSubscriptionServiceHandler interface {
	FetchSubscription(context.Context, *connect.Request[v1.MerchantSubscriptionServiceFetchSubscriptionRequest]) (*connect.Response[v1.MerchantSubscriptionServiceFetchSubscriptionResponse], error)
	FetchProxyToken(context.Context, *connect.Request[v1.MerchantSubscriptionServiceFetchProxyTokenRequest]) (*connect.Response[v1.MerchantSubscriptionServiceFetchProxyTokenResponse], error)
}

// NewMerchantSubscriptionServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewMerchantSubscriptionServiceHandler(svc MerchantSubscriptionServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	merchantSubscriptionServiceMethods := v1.File_proxymanager_subscription_v1_merchant_proto.Services().ByName("MerchantSubscriptionService").Methods()
	merchantSubscriptionServiceFetchSubscriptionHandler := connect.NewUnaryHandler(
		MerchantSubscriptionServiceFetchSubscriptionProcedure,
		svc.FetchSubscription,
		connect.WithSchema(merchantSubscriptionServiceMethods.ByName("FetchSubscription")),
		connect.WithHandlerOptions(opts...),
	)
	merchantSubscriptionServiceFetchProxyTokenHandler := connect.NewUnaryHandler(
		MerchantSubscriptionServiceFetchProxyTokenProcedure,
		svc.FetchProxyToken,
		connect.WithSchema(merchantSubscriptionServiceMethods.ByName("FetchProxyToken")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.subscription.v1.MerchantSubscriptionService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case MerchantSubscriptionServiceFetchSubscriptionProcedure:
			merchantSubscriptionServiceFetchSubscriptionHandler.ServeHTTP(w, r)
		case MerchantSubscriptionServiceFetchProxyTokenProcedure:
			merchantSubscriptionServiceFetchProxyTokenHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedMerchantSubscriptionServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedMerchantSubscriptionServiceHandler struct{}

func (UnimplementedMerchantSubscriptionServiceHandler) FetchSubscription(context.Context, *connect.Request[v1.MerchantSubscriptionServiceFetchSubscriptionRequest]) (*connect.Response[v1.MerchantSubscriptionServiceFetchSubscriptionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.subscription.v1.MerchantSubscriptionService.FetchSubscription is not implemented"))
}

func (UnimplementedMerchantSubscriptionServiceHandler) FetchProxyToken(context.Context, *connect.Request[v1.MerchantSubscriptionServiceFetchProxyTokenRequest]) (*connect.Response[v1.MerchantSubscriptionServiceFetchProxyTokenResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.subscription.v1.MerchantSubscriptionService.FetchProxyToken is not implemented"))
}
