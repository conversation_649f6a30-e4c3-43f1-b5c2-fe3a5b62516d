// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/subscription/v1/internal.proto

package subscriptionv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/subscription/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// InternalSubscriptionServiceName is the fully-qualified name of the InternalSubscriptionService
	// service.
	InternalSubscriptionServiceName = "proxymanager.subscription.v1.InternalSubscriptionService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// InternalSubscriptionServiceFetchSubscriptionProcedure is the fully-qualified name of the
	// InternalSubscriptionService's FetchSubscription RPC.
	InternalSubscriptionServiceFetchSubscriptionProcedure = "/proxymanager.subscription.v1.InternalSubscriptionService/FetchSubscription"
	// InternalSubscriptionServiceCreateSubscriptionProcedure is the fully-qualified name of the
	// InternalSubscriptionService's CreateSubscription RPC.
	InternalSubscriptionServiceCreateSubscriptionProcedure = "/proxymanager.subscription.v1.InternalSubscriptionService/CreateSubscription"
	// InternalSubscriptionServiceExtendSubscriptionProcedure is the fully-qualified name of the
	// InternalSubscriptionService's ExtendSubscription RPC.
	InternalSubscriptionServiceExtendSubscriptionProcedure = "/proxymanager.subscription.v1.InternalSubscriptionService/ExtendSubscription"
)

// InternalSubscriptionServiceClient is a client for the
// proxymanager.subscription.v1.InternalSubscriptionService service.
type InternalSubscriptionServiceClient interface {
	FetchSubscription(context.Context, *connect.Request[v1.InternalSubscriptionServiceFetchSubscriptionRequest]) (*connect.Response[v1.InternalSubscriptionServiceFetchSubscriptionResponse], error)
	CreateSubscription(context.Context, *connect.Request[v1.InternalSubscriptionServiceCreateSubscriptionRequest]) (*connect.Response[v1.InternalSubscriptionServiceCreateSubscriptionResponse], error)
	ExtendSubscription(context.Context, *connect.Request[v1.InternalSubscriptionServiceExtendSubscriptionRequest]) (*connect.Response[v1.InternalSubscriptionServiceExtendSubscriptionResponse], error)
}

// NewInternalSubscriptionServiceClient constructs a client for the
// proxymanager.subscription.v1.InternalSubscriptionService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewInternalSubscriptionServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) InternalSubscriptionServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	internalSubscriptionServiceMethods := v1.File_proxymanager_subscription_v1_internal_proto.Services().ByName("InternalSubscriptionService").Methods()
	return &internalSubscriptionServiceClient{
		fetchSubscription: connect.NewClient[v1.InternalSubscriptionServiceFetchSubscriptionRequest, v1.InternalSubscriptionServiceFetchSubscriptionResponse](
			httpClient,
			baseURL+InternalSubscriptionServiceFetchSubscriptionProcedure,
			connect.WithSchema(internalSubscriptionServiceMethods.ByName("FetchSubscription")),
			connect.WithClientOptions(opts...),
		),
		createSubscription: connect.NewClient[v1.InternalSubscriptionServiceCreateSubscriptionRequest, v1.InternalSubscriptionServiceCreateSubscriptionResponse](
			httpClient,
			baseURL+InternalSubscriptionServiceCreateSubscriptionProcedure,
			connect.WithSchema(internalSubscriptionServiceMethods.ByName("CreateSubscription")),
			connect.WithClientOptions(opts...),
		),
		extendSubscription: connect.NewClient[v1.InternalSubscriptionServiceExtendSubscriptionRequest, v1.InternalSubscriptionServiceExtendSubscriptionResponse](
			httpClient,
			baseURL+InternalSubscriptionServiceExtendSubscriptionProcedure,
			connect.WithSchema(internalSubscriptionServiceMethods.ByName("ExtendSubscription")),
			connect.WithClientOptions(opts...),
		),
	}
}

// internalSubscriptionServiceClient implements InternalSubscriptionServiceClient.
type internalSubscriptionServiceClient struct {
	fetchSubscription  *connect.Client[v1.InternalSubscriptionServiceFetchSubscriptionRequest, v1.InternalSubscriptionServiceFetchSubscriptionResponse]
	createSubscription *connect.Client[v1.InternalSubscriptionServiceCreateSubscriptionRequest, v1.InternalSubscriptionServiceCreateSubscriptionResponse]
	extendSubscription *connect.Client[v1.InternalSubscriptionServiceExtendSubscriptionRequest, v1.InternalSubscriptionServiceExtendSubscriptionResponse]
}

// FetchSubscription calls
// proxymanager.subscription.v1.InternalSubscriptionService.FetchSubscription.
func (c *internalSubscriptionServiceClient) FetchSubscription(ctx context.Context, req *connect.Request[v1.InternalSubscriptionServiceFetchSubscriptionRequest]) (*connect.Response[v1.InternalSubscriptionServiceFetchSubscriptionResponse], error) {
	return c.fetchSubscription.CallUnary(ctx, req)
}

// CreateSubscription calls
// proxymanager.subscription.v1.InternalSubscriptionService.CreateSubscription.
func (c *internalSubscriptionServiceClient) CreateSubscription(ctx context.Context, req *connect.Request[v1.InternalSubscriptionServiceCreateSubscriptionRequest]) (*connect.Response[v1.InternalSubscriptionServiceCreateSubscriptionResponse], error) {
	return c.createSubscription.CallUnary(ctx, req)
}

// ExtendSubscription calls
// proxymanager.subscription.v1.InternalSubscriptionService.ExtendSubscription.
func (c *internalSubscriptionServiceClient) ExtendSubscription(ctx context.Context, req *connect.Request[v1.InternalSubscriptionServiceExtendSubscriptionRequest]) (*connect.Response[v1.InternalSubscriptionServiceExtendSubscriptionResponse], error) {
	return c.extendSubscription.CallUnary(ctx, req)
}

// InternalSubscriptionServiceHandler is an implementation of the
// proxymanager.subscription.v1.InternalSubscriptionService service.
type InternalSubscriptionServiceHandler interface {
	FetchSubscription(context.Context, *connect.Request[v1.InternalSubscriptionServiceFetchSubscriptionRequest]) (*connect.Response[v1.InternalSubscriptionServiceFetchSubscriptionResponse], error)
	CreateSubscription(context.Context, *connect.Request[v1.InternalSubscriptionServiceCreateSubscriptionRequest]) (*connect.Response[v1.InternalSubscriptionServiceCreateSubscriptionResponse], error)
	ExtendSubscription(context.Context, *connect.Request[v1.InternalSubscriptionServiceExtendSubscriptionRequest]) (*connect.Response[v1.InternalSubscriptionServiceExtendSubscriptionResponse], error)
}

// NewInternalSubscriptionServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewInternalSubscriptionServiceHandler(svc InternalSubscriptionServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	internalSubscriptionServiceMethods := v1.File_proxymanager_subscription_v1_internal_proto.Services().ByName("InternalSubscriptionService").Methods()
	internalSubscriptionServiceFetchSubscriptionHandler := connect.NewUnaryHandler(
		InternalSubscriptionServiceFetchSubscriptionProcedure,
		svc.FetchSubscription,
		connect.WithSchema(internalSubscriptionServiceMethods.ByName("FetchSubscription")),
		connect.WithHandlerOptions(opts...),
	)
	internalSubscriptionServiceCreateSubscriptionHandler := connect.NewUnaryHandler(
		InternalSubscriptionServiceCreateSubscriptionProcedure,
		svc.CreateSubscription,
		connect.WithSchema(internalSubscriptionServiceMethods.ByName("CreateSubscription")),
		connect.WithHandlerOptions(opts...),
	)
	internalSubscriptionServiceExtendSubscriptionHandler := connect.NewUnaryHandler(
		InternalSubscriptionServiceExtendSubscriptionProcedure,
		svc.ExtendSubscription,
		connect.WithSchema(internalSubscriptionServiceMethods.ByName("ExtendSubscription")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.subscription.v1.InternalSubscriptionService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case InternalSubscriptionServiceFetchSubscriptionProcedure:
			internalSubscriptionServiceFetchSubscriptionHandler.ServeHTTP(w, r)
		case InternalSubscriptionServiceCreateSubscriptionProcedure:
			internalSubscriptionServiceCreateSubscriptionHandler.ServeHTTP(w, r)
		case InternalSubscriptionServiceExtendSubscriptionProcedure:
			internalSubscriptionServiceExtendSubscriptionHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedInternalSubscriptionServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedInternalSubscriptionServiceHandler struct{}

func (UnimplementedInternalSubscriptionServiceHandler) FetchSubscription(context.Context, *connect.Request[v1.InternalSubscriptionServiceFetchSubscriptionRequest]) (*connect.Response[v1.InternalSubscriptionServiceFetchSubscriptionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.subscription.v1.InternalSubscriptionService.FetchSubscription is not implemented"))
}

func (UnimplementedInternalSubscriptionServiceHandler) CreateSubscription(context.Context, *connect.Request[v1.InternalSubscriptionServiceCreateSubscriptionRequest]) (*connect.Response[v1.InternalSubscriptionServiceCreateSubscriptionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.subscription.v1.InternalSubscriptionService.CreateSubscription is not implemented"))
}

func (UnimplementedInternalSubscriptionServiceHandler) ExtendSubscription(context.Context, *connect.Request[v1.InternalSubscriptionServiceExtendSubscriptionRequest]) (*connect.Response[v1.InternalSubscriptionServiceExtendSubscriptionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.subscription.v1.InternalSubscriptionService.ExtendSubscription is not implemented"))
}
