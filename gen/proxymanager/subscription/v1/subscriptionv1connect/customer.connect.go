// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/subscription/v1/customer.proto

package subscriptionv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/subscription/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CustomerSubscriptionServiceName is the fully-qualified name of the CustomerSubscriptionService
	// service.
	CustomerSubscriptionServiceName = "proxymanager.subscription.v1.CustomerSubscriptionService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CustomerSubscriptionServiceFetchUserPlanProcedure is the fully-qualified name of the
	// CustomerSubscriptionService's FetchUserPlan RPC.
	CustomerSubscriptionServiceFetchUserPlanProcedure = "/proxymanager.subscription.v1.CustomerSubscriptionService/FetchUserPlan"
	// CustomerSubscriptionServiceFetchSubscriptionProcedure is the fully-qualified name of the
	// CustomerSubscriptionService's FetchSubscription RPC.
	CustomerSubscriptionServiceFetchSubscriptionProcedure = "/proxymanager.subscription.v1.CustomerSubscriptionService/FetchSubscription"
	// CustomerSubscriptionServiceUpdateSubscriptionProcedure is the fully-qualified name of the
	// CustomerSubscriptionService's UpdateSubscription RPC.
	CustomerSubscriptionServiceUpdateSubscriptionProcedure = "/proxymanager.subscription.v1.CustomerSubscriptionService/UpdateSubscription"
	// CustomerSubscriptionServiceFetchProxyTokenProcedure is the fully-qualified name of the
	// CustomerSubscriptionService's FetchProxyToken RPC.
	CustomerSubscriptionServiceFetchProxyTokenProcedure = "/proxymanager.subscription.v1.CustomerSubscriptionService/FetchProxyToken"
	// CustomerSubscriptionServiceUpdateBackConnectProxyTokenProcedure is the fully-qualified name of
	// the CustomerSubscriptionService's UpdateBackConnectProxyToken RPC.
	CustomerSubscriptionServiceUpdateBackConnectProxyTokenProcedure = "/proxymanager.subscription.v1.CustomerSubscriptionService/UpdateBackConnectProxyToken"
	// CustomerSubscriptionServiceRevokeProxyTokenProcedure is the fully-qualified name of the
	// CustomerSubscriptionService's RevokeProxyToken RPC.
	CustomerSubscriptionServiceRevokeProxyTokenProcedure = "/proxymanager.subscription.v1.CustomerSubscriptionService/RevokeProxyToken"
	// CustomerSubscriptionServiceDeleteSubscriptionProcedure is the fully-qualified name of the
	// CustomerSubscriptionService's DeleteSubscription RPC.
	CustomerSubscriptionServiceDeleteSubscriptionProcedure = "/proxymanager.subscription.v1.CustomerSubscriptionService/DeleteSubscription"
)

// CustomerSubscriptionServiceClient is a client for the
// proxymanager.subscription.v1.CustomerSubscriptionService service.
type CustomerSubscriptionServiceClient interface {
	FetchUserPlan(context.Context, *connect.Request[v1.CustomerSubscriptionServiceFetchUserPlanRequest]) (*connect.Response[v1.CustomerSubscriptionServiceFetchUserPlanResponse], error)
	FetchSubscription(context.Context, *connect.Request[v1.CustomerSubscriptionServiceFetchSubscriptionRequest]) (*connect.Response[v1.CustomerSubscriptionServiceFetchSubscriptionResponse], error)
	UpdateSubscription(context.Context, *connect.Request[v1.CustomerSubscriptionServiceUpdateSubscriptionRequest]) (*connect.Response[v1.CustomerSubscriptionServiceUpdateSubscriptionResponse], error)
	FetchProxyToken(context.Context, *connect.Request[v1.CustomerSubscriptionServiceFetchProxyTokenRequest]) (*connect.Response[v1.CustomerSubscriptionServiceFetchProxyTokenResponse], error)
	UpdateBackConnectProxyToken(context.Context, *connect.Request[v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest]) (*connect.Response[v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse], error)
	RevokeProxyToken(context.Context, *connect.Request[v1.CustomerSubscriptionServiceRevokeProxyTokenRequest]) (*connect.Response[v1.CustomerSubscriptionServiceRevokeProxyTokenResponse], error)
	DeleteSubscription(context.Context, *connect.Request[v1.CustomerSubscriptionServiceDeleteSubscriptionRequest]) (*connect.Response[v1.CustomerSubscriptionServiceDeleteSubscriptionResponse], error)
}

// NewCustomerSubscriptionServiceClient constructs a client for the
// proxymanager.subscription.v1.CustomerSubscriptionService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCustomerSubscriptionServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CustomerSubscriptionServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	customerSubscriptionServiceMethods := v1.File_proxymanager_subscription_v1_customer_proto.Services().ByName("CustomerSubscriptionService").Methods()
	return &customerSubscriptionServiceClient{
		fetchUserPlan: connect.NewClient[v1.CustomerSubscriptionServiceFetchUserPlanRequest, v1.CustomerSubscriptionServiceFetchUserPlanResponse](
			httpClient,
			baseURL+CustomerSubscriptionServiceFetchUserPlanProcedure,
			connect.WithSchema(customerSubscriptionServiceMethods.ByName("FetchUserPlan")),
			connect.WithClientOptions(opts...),
		),
		fetchSubscription: connect.NewClient[v1.CustomerSubscriptionServiceFetchSubscriptionRequest, v1.CustomerSubscriptionServiceFetchSubscriptionResponse](
			httpClient,
			baseURL+CustomerSubscriptionServiceFetchSubscriptionProcedure,
			connect.WithSchema(customerSubscriptionServiceMethods.ByName("FetchSubscription")),
			connect.WithClientOptions(opts...),
		),
		updateSubscription: connect.NewClient[v1.CustomerSubscriptionServiceUpdateSubscriptionRequest, v1.CustomerSubscriptionServiceUpdateSubscriptionResponse](
			httpClient,
			baseURL+CustomerSubscriptionServiceUpdateSubscriptionProcedure,
			connect.WithSchema(customerSubscriptionServiceMethods.ByName("UpdateSubscription")),
			connect.WithClientOptions(opts...),
		),
		fetchProxyToken: connect.NewClient[v1.CustomerSubscriptionServiceFetchProxyTokenRequest, v1.CustomerSubscriptionServiceFetchProxyTokenResponse](
			httpClient,
			baseURL+CustomerSubscriptionServiceFetchProxyTokenProcedure,
			connect.WithSchema(customerSubscriptionServiceMethods.ByName("FetchProxyToken")),
			connect.WithClientOptions(opts...),
		),
		updateBackConnectProxyToken: connect.NewClient[v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest, v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse](
			httpClient,
			baseURL+CustomerSubscriptionServiceUpdateBackConnectProxyTokenProcedure,
			connect.WithSchema(customerSubscriptionServiceMethods.ByName("UpdateBackConnectProxyToken")),
			connect.WithClientOptions(opts...),
		),
		revokeProxyToken: connect.NewClient[v1.CustomerSubscriptionServiceRevokeProxyTokenRequest, v1.CustomerSubscriptionServiceRevokeProxyTokenResponse](
			httpClient,
			baseURL+CustomerSubscriptionServiceRevokeProxyTokenProcedure,
			connect.WithSchema(customerSubscriptionServiceMethods.ByName("RevokeProxyToken")),
			connect.WithClientOptions(opts...),
		),
		deleteSubscription: connect.NewClient[v1.CustomerSubscriptionServiceDeleteSubscriptionRequest, v1.CustomerSubscriptionServiceDeleteSubscriptionResponse](
			httpClient,
			baseURL+CustomerSubscriptionServiceDeleteSubscriptionProcedure,
			connect.WithSchema(customerSubscriptionServiceMethods.ByName("DeleteSubscription")),
			connect.WithClientOptions(opts...),
		),
	}
}

// customerSubscriptionServiceClient implements CustomerSubscriptionServiceClient.
type customerSubscriptionServiceClient struct {
	fetchUserPlan               *connect.Client[v1.CustomerSubscriptionServiceFetchUserPlanRequest, v1.CustomerSubscriptionServiceFetchUserPlanResponse]
	fetchSubscription           *connect.Client[v1.CustomerSubscriptionServiceFetchSubscriptionRequest, v1.CustomerSubscriptionServiceFetchSubscriptionResponse]
	updateSubscription          *connect.Client[v1.CustomerSubscriptionServiceUpdateSubscriptionRequest, v1.CustomerSubscriptionServiceUpdateSubscriptionResponse]
	fetchProxyToken             *connect.Client[v1.CustomerSubscriptionServiceFetchProxyTokenRequest, v1.CustomerSubscriptionServiceFetchProxyTokenResponse]
	updateBackConnectProxyToken *connect.Client[v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest, v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse]
	revokeProxyToken            *connect.Client[v1.CustomerSubscriptionServiceRevokeProxyTokenRequest, v1.CustomerSubscriptionServiceRevokeProxyTokenResponse]
	deleteSubscription          *connect.Client[v1.CustomerSubscriptionServiceDeleteSubscriptionRequest, v1.CustomerSubscriptionServiceDeleteSubscriptionResponse]
}

// FetchUserPlan calls proxymanager.subscription.v1.CustomerSubscriptionService.FetchUserPlan.
func (c *customerSubscriptionServiceClient) FetchUserPlan(ctx context.Context, req *connect.Request[v1.CustomerSubscriptionServiceFetchUserPlanRequest]) (*connect.Response[v1.CustomerSubscriptionServiceFetchUserPlanResponse], error) {
	return c.fetchUserPlan.CallUnary(ctx, req)
}

// FetchSubscription calls
// proxymanager.subscription.v1.CustomerSubscriptionService.FetchSubscription.
func (c *customerSubscriptionServiceClient) FetchSubscription(ctx context.Context, req *connect.Request[v1.CustomerSubscriptionServiceFetchSubscriptionRequest]) (*connect.Response[v1.CustomerSubscriptionServiceFetchSubscriptionResponse], error) {
	return c.fetchSubscription.CallUnary(ctx, req)
}

// UpdateSubscription calls
// proxymanager.subscription.v1.CustomerSubscriptionService.UpdateSubscription.
func (c *customerSubscriptionServiceClient) UpdateSubscription(ctx context.Context, req *connect.Request[v1.CustomerSubscriptionServiceUpdateSubscriptionRequest]) (*connect.Response[v1.CustomerSubscriptionServiceUpdateSubscriptionResponse], error) {
	return c.updateSubscription.CallUnary(ctx, req)
}

// FetchProxyToken calls proxymanager.subscription.v1.CustomerSubscriptionService.FetchProxyToken.
func (c *customerSubscriptionServiceClient) FetchProxyToken(ctx context.Context, req *connect.Request[v1.CustomerSubscriptionServiceFetchProxyTokenRequest]) (*connect.Response[v1.CustomerSubscriptionServiceFetchProxyTokenResponse], error) {
	return c.fetchProxyToken.CallUnary(ctx, req)
}

// UpdateBackConnectProxyToken calls
// proxymanager.subscription.v1.CustomerSubscriptionService.UpdateBackConnectProxyToken.
func (c *customerSubscriptionServiceClient) UpdateBackConnectProxyToken(ctx context.Context, req *connect.Request[v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest]) (*connect.Response[v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse], error) {
	return c.updateBackConnectProxyToken.CallUnary(ctx, req)
}

// RevokeProxyToken calls proxymanager.subscription.v1.CustomerSubscriptionService.RevokeProxyToken.
func (c *customerSubscriptionServiceClient) RevokeProxyToken(ctx context.Context, req *connect.Request[v1.CustomerSubscriptionServiceRevokeProxyTokenRequest]) (*connect.Response[v1.CustomerSubscriptionServiceRevokeProxyTokenResponse], error) {
	return c.revokeProxyToken.CallUnary(ctx, req)
}

// DeleteSubscription calls
// proxymanager.subscription.v1.CustomerSubscriptionService.DeleteSubscription.
func (c *customerSubscriptionServiceClient) DeleteSubscription(ctx context.Context, req *connect.Request[v1.CustomerSubscriptionServiceDeleteSubscriptionRequest]) (*connect.Response[v1.CustomerSubscriptionServiceDeleteSubscriptionResponse], error) {
	return c.deleteSubscription.CallUnary(ctx, req)
}

// CustomerSubscriptionServiceHandler is an implementation of the
// proxymanager.subscription.v1.CustomerSubscriptionService service.
type CustomerSubscriptionServiceHandler interface {
	FetchUserPlan(context.Context, *connect.Request[v1.CustomerSubscriptionServiceFetchUserPlanRequest]) (*connect.Response[v1.CustomerSubscriptionServiceFetchUserPlanResponse], error)
	FetchSubscription(context.Context, *connect.Request[v1.CustomerSubscriptionServiceFetchSubscriptionRequest]) (*connect.Response[v1.CustomerSubscriptionServiceFetchSubscriptionResponse], error)
	UpdateSubscription(context.Context, *connect.Request[v1.CustomerSubscriptionServiceUpdateSubscriptionRequest]) (*connect.Response[v1.CustomerSubscriptionServiceUpdateSubscriptionResponse], error)
	FetchProxyToken(context.Context, *connect.Request[v1.CustomerSubscriptionServiceFetchProxyTokenRequest]) (*connect.Response[v1.CustomerSubscriptionServiceFetchProxyTokenResponse], error)
	UpdateBackConnectProxyToken(context.Context, *connect.Request[v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest]) (*connect.Response[v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse], error)
	RevokeProxyToken(context.Context, *connect.Request[v1.CustomerSubscriptionServiceRevokeProxyTokenRequest]) (*connect.Response[v1.CustomerSubscriptionServiceRevokeProxyTokenResponse], error)
	DeleteSubscription(context.Context, *connect.Request[v1.CustomerSubscriptionServiceDeleteSubscriptionRequest]) (*connect.Response[v1.CustomerSubscriptionServiceDeleteSubscriptionResponse], error)
}

// NewCustomerSubscriptionServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCustomerSubscriptionServiceHandler(svc CustomerSubscriptionServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	customerSubscriptionServiceMethods := v1.File_proxymanager_subscription_v1_customer_proto.Services().ByName("CustomerSubscriptionService").Methods()
	customerSubscriptionServiceFetchUserPlanHandler := connect.NewUnaryHandler(
		CustomerSubscriptionServiceFetchUserPlanProcedure,
		svc.FetchUserPlan,
		connect.WithSchema(customerSubscriptionServiceMethods.ByName("FetchUserPlan")),
		connect.WithHandlerOptions(opts...),
	)
	customerSubscriptionServiceFetchSubscriptionHandler := connect.NewUnaryHandler(
		CustomerSubscriptionServiceFetchSubscriptionProcedure,
		svc.FetchSubscription,
		connect.WithSchema(customerSubscriptionServiceMethods.ByName("FetchSubscription")),
		connect.WithHandlerOptions(opts...),
	)
	customerSubscriptionServiceUpdateSubscriptionHandler := connect.NewUnaryHandler(
		CustomerSubscriptionServiceUpdateSubscriptionProcedure,
		svc.UpdateSubscription,
		connect.WithSchema(customerSubscriptionServiceMethods.ByName("UpdateSubscription")),
		connect.WithHandlerOptions(opts...),
	)
	customerSubscriptionServiceFetchProxyTokenHandler := connect.NewUnaryHandler(
		CustomerSubscriptionServiceFetchProxyTokenProcedure,
		svc.FetchProxyToken,
		connect.WithSchema(customerSubscriptionServiceMethods.ByName("FetchProxyToken")),
		connect.WithHandlerOptions(opts...),
	)
	customerSubscriptionServiceUpdateBackConnectProxyTokenHandler := connect.NewUnaryHandler(
		CustomerSubscriptionServiceUpdateBackConnectProxyTokenProcedure,
		svc.UpdateBackConnectProxyToken,
		connect.WithSchema(customerSubscriptionServiceMethods.ByName("UpdateBackConnectProxyToken")),
		connect.WithHandlerOptions(opts...),
	)
	customerSubscriptionServiceRevokeProxyTokenHandler := connect.NewUnaryHandler(
		CustomerSubscriptionServiceRevokeProxyTokenProcedure,
		svc.RevokeProxyToken,
		connect.WithSchema(customerSubscriptionServiceMethods.ByName("RevokeProxyToken")),
		connect.WithHandlerOptions(opts...),
	)
	customerSubscriptionServiceDeleteSubscriptionHandler := connect.NewUnaryHandler(
		CustomerSubscriptionServiceDeleteSubscriptionProcedure,
		svc.DeleteSubscription,
		connect.WithSchema(customerSubscriptionServiceMethods.ByName("DeleteSubscription")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.subscription.v1.CustomerSubscriptionService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CustomerSubscriptionServiceFetchUserPlanProcedure:
			customerSubscriptionServiceFetchUserPlanHandler.ServeHTTP(w, r)
		case CustomerSubscriptionServiceFetchSubscriptionProcedure:
			customerSubscriptionServiceFetchSubscriptionHandler.ServeHTTP(w, r)
		case CustomerSubscriptionServiceUpdateSubscriptionProcedure:
			customerSubscriptionServiceUpdateSubscriptionHandler.ServeHTTP(w, r)
		case CustomerSubscriptionServiceFetchProxyTokenProcedure:
			customerSubscriptionServiceFetchProxyTokenHandler.ServeHTTP(w, r)
		case CustomerSubscriptionServiceUpdateBackConnectProxyTokenProcedure:
			customerSubscriptionServiceUpdateBackConnectProxyTokenHandler.ServeHTTP(w, r)
		case CustomerSubscriptionServiceRevokeProxyTokenProcedure:
			customerSubscriptionServiceRevokeProxyTokenHandler.ServeHTTP(w, r)
		case CustomerSubscriptionServiceDeleteSubscriptionProcedure:
			customerSubscriptionServiceDeleteSubscriptionHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCustomerSubscriptionServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCustomerSubscriptionServiceHandler struct{}

func (UnimplementedCustomerSubscriptionServiceHandler) FetchUserPlan(context.Context, *connect.Request[v1.CustomerSubscriptionServiceFetchUserPlanRequest]) (*connect.Response[v1.CustomerSubscriptionServiceFetchUserPlanResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.subscription.v1.CustomerSubscriptionService.FetchUserPlan is not implemented"))
}

func (UnimplementedCustomerSubscriptionServiceHandler) FetchSubscription(context.Context, *connect.Request[v1.CustomerSubscriptionServiceFetchSubscriptionRequest]) (*connect.Response[v1.CustomerSubscriptionServiceFetchSubscriptionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.subscription.v1.CustomerSubscriptionService.FetchSubscription is not implemented"))
}

func (UnimplementedCustomerSubscriptionServiceHandler) UpdateSubscription(context.Context, *connect.Request[v1.CustomerSubscriptionServiceUpdateSubscriptionRequest]) (*connect.Response[v1.CustomerSubscriptionServiceUpdateSubscriptionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.subscription.v1.CustomerSubscriptionService.UpdateSubscription is not implemented"))
}

func (UnimplementedCustomerSubscriptionServiceHandler) FetchProxyToken(context.Context, *connect.Request[v1.CustomerSubscriptionServiceFetchProxyTokenRequest]) (*connect.Response[v1.CustomerSubscriptionServiceFetchProxyTokenResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.subscription.v1.CustomerSubscriptionService.FetchProxyToken is not implemented"))
}

func (UnimplementedCustomerSubscriptionServiceHandler) UpdateBackConnectProxyToken(context.Context, *connect.Request[v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest]) (*connect.Response[v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.subscription.v1.CustomerSubscriptionService.UpdateBackConnectProxyToken is not implemented"))
}

func (UnimplementedCustomerSubscriptionServiceHandler) RevokeProxyToken(context.Context, *connect.Request[v1.CustomerSubscriptionServiceRevokeProxyTokenRequest]) (*connect.Response[v1.CustomerSubscriptionServiceRevokeProxyTokenResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.subscription.v1.CustomerSubscriptionService.RevokeProxyToken is not implemented"))
}

func (UnimplementedCustomerSubscriptionServiceHandler) DeleteSubscription(context.Context, *connect.Request[v1.CustomerSubscriptionServiceDeleteSubscriptionRequest]) (*connect.Response[v1.CustomerSubscriptionServiceDeleteSubscriptionResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.subscription.v1.CustomerSubscriptionService.DeleteSubscription is not implemented"))
}
