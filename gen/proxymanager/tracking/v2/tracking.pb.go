// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/tracking/v2/tracking.proto

package trackingv2

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrackingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ListTracking  []*TrackingSegment     `protobuf:"bytes,1,rep,name=list_tracking,json=listTracking,proto3" json:"list_tracking,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrackingRequest) Reset() {
	*x = TrackingRequest{}
	mi := &file_proxymanager_tracking_v2_tracking_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackingRequest) ProtoMessage() {}

func (x *TrackingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_tracking_v2_tracking_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackingRequest.ProtoReflect.Descriptor instead.
func (*TrackingRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_tracking_v2_tracking_proto_rawDescGZIP(), []int{0}
}

func (x *TrackingRequest) GetListTracking() []*TrackingSegment {
	if x != nil {
		return x.ListTracking
	}
	return nil
}

type TrackingSegment struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdSession     string                 `protobuf:"bytes,1,opt,name=id_session,json=idSession,proto3" json:"id_session,omitempty"`
	IpUser        string                 `protobuf:"bytes,2,opt,name=ip_user,json=ipUser,proto3" json:"ip_user,omitempty"`
	Domain        string                 `protobuf:"bytes,3,opt,name=domain,proto3" json:"domain,omitempty"`
	IpOfDomain    string                 `protobuf:"bytes,4,opt,name=ip_of_domain,json=ipOfDomain,proto3" json:"ip_of_domain,omitempty"`
	BytesUpload   int64                  `protobuf:"varint,5,opt,name=bytes_upload,json=bytesUpload,proto3" json:"bytes_upload,omitempty"`
	BytesDownload int64                  `protobuf:"varint,6,opt,name=bytes_download,json=bytesDownload,proto3" json:"bytes_download,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrackingSegment) Reset() {
	*x = TrackingSegment{}
	mi := &file_proxymanager_tracking_v2_tracking_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackingSegment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackingSegment) ProtoMessage() {}

func (x *TrackingSegment) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_tracking_v2_tracking_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackingSegment.ProtoReflect.Descriptor instead.
func (*TrackingSegment) Descriptor() ([]byte, []int) {
	return file_proxymanager_tracking_v2_tracking_proto_rawDescGZIP(), []int{1}
}

func (x *TrackingSegment) GetIdSession() string {
	if x != nil {
		return x.IdSession
	}
	return ""
}

func (x *TrackingSegment) GetIpUser() string {
	if x != nil {
		return x.IpUser
	}
	return ""
}

func (x *TrackingSegment) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *TrackingSegment) GetIpOfDomain() string {
	if x != nil {
		return x.IpOfDomain
	}
	return ""
}

func (x *TrackingSegment) GetBytesUpload() int64 {
	if x != nil {
		return x.BytesUpload
	}
	return 0
}

func (x *TrackingSegment) GetBytesDownload() int64 {
	if x != nil {
		return x.BytesDownload
	}
	return 0
}

type TrackingResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrackingResponse) Reset() {
	*x = TrackingResponse{}
	mi := &file_proxymanager_tracking_v2_tracking_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackingResponse) ProtoMessage() {}

func (x *TrackingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_tracking_v2_tracking_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackingResponse.ProtoReflect.Descriptor instead.
func (*TrackingResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_tracking_v2_tracking_proto_rawDescGZIP(), []int{2}
}

func (x *TrackingResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type CheckDomainBlockRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IpUser        string                 `protobuf:"bytes,1,opt,name=ip_user,json=ipUser,proto3" json:"ip_user,omitempty"`
	Domain        string                 `protobuf:"bytes,2,opt,name=domain,proto3" json:"domain,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckDomainBlockRequest) Reset() {
	*x = CheckDomainBlockRequest{}
	mi := &file_proxymanager_tracking_v2_tracking_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckDomainBlockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDomainBlockRequest) ProtoMessage() {}

func (x *CheckDomainBlockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_tracking_v2_tracking_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDomainBlockRequest.ProtoReflect.Descriptor instead.
func (*CheckDomainBlockRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_tracking_v2_tracking_proto_rawDescGZIP(), []int{3}
}

func (x *CheckDomainBlockRequest) GetIpUser() string {
	if x != nil {
		return x.IpUser
	}
	return ""
}

func (x *CheckDomainBlockRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

type CheckDomainBlockResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsAllow       bool                   `protobuf:"varint,1,opt,name=is_allow,json=isAllow,proto3" json:"is_allow,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckDomainBlockResponse) Reset() {
	*x = CheckDomainBlockResponse{}
	mi := &file_proxymanager_tracking_v2_tracking_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckDomainBlockResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckDomainBlockResponse) ProtoMessage() {}

func (x *CheckDomainBlockResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_tracking_v2_tracking_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckDomainBlockResponse.ProtoReflect.Descriptor instead.
func (*CheckDomainBlockResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_tracking_v2_tracking_proto_rawDescGZIP(), []int{4}
}

func (x *CheckDomainBlockResponse) GetIsAllow() bool {
	if x != nil {
		return x.IsAllow
	}
	return false
}

var File_proxymanager_tracking_v2_tracking_proto protoreflect.FileDescriptor

const file_proxymanager_tracking_v2_tracking_proto_rawDesc = "" +
	"\n" +
	"'proxymanager/tracking/v2/tracking.proto\x12\x18proxymanager.tracking.v2\"a\n" +
	"\x0fTrackingRequest\x12N\n" +
	"\rlist_tracking\x18\x01 \x03(\v2).proxymanager.tracking.v2.TrackingSegmentR\flistTracking\"\xcd\x01\n" +
	"\x0fTrackingSegment\x12\x1d\n" +
	"\n" +
	"id_session\x18\x01 \x01(\tR\tidSession\x12\x17\n" +
	"\aip_user\x18\x02 \x01(\tR\x06ipUser\x12\x16\n" +
	"\x06domain\x18\x03 \x01(\tR\x06domain\x12 \n" +
	"\fip_of_domain\x18\x04 \x01(\tR\n" +
	"ipOfDomain\x12!\n" +
	"\fbytes_upload\x18\x05 \x01(\x03R\vbytesUpload\x12%\n" +
	"\x0ebytes_download\x18\x06 \x01(\x03R\rbytesDownload\",\n" +
	"\x10TrackingResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"J\n" +
	"\x17CheckDomainBlockRequest\x12\x17\n" +
	"\aip_user\x18\x01 \x01(\tR\x06ipUser\x12\x16\n" +
	"\x06domain\x18\x02 \x01(\tR\x06domain\"5\n" +
	"\x18CheckDomainBlockResponse\x12\x19\n" +
	"\bis_allow\x18\x01 \x01(\bR\aisAllow2\xef\x01\n" +
	"\x0fTrackingService\x12a\n" +
	"\bTracking\x12).proxymanager.tracking.v2.TrackingRequest\x1a*.proxymanager.tracking.v2.TrackingResponse\x12y\n" +
	"\x10CheckDomainBlock\x121.proxymanager.tracking.v2.CheckDomainBlockRequest\x1a2.proxymanager.tracking.v2.CheckDomainBlockResponseBTZRgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/tracking/v2;trackingv2b\x06proto3"

var (
	file_proxymanager_tracking_v2_tracking_proto_rawDescOnce sync.Once
	file_proxymanager_tracking_v2_tracking_proto_rawDescData []byte
)

func file_proxymanager_tracking_v2_tracking_proto_rawDescGZIP() []byte {
	file_proxymanager_tracking_v2_tracking_proto_rawDescOnce.Do(func() {
		file_proxymanager_tracking_v2_tracking_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_tracking_v2_tracking_proto_rawDesc), len(file_proxymanager_tracking_v2_tracking_proto_rawDesc)))
	})
	return file_proxymanager_tracking_v2_tracking_proto_rawDescData
}

var file_proxymanager_tracking_v2_tracking_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_proxymanager_tracking_v2_tracking_proto_goTypes = []any{
	(*TrackingRequest)(nil),          // 0: proxymanager.tracking.v2.TrackingRequest
	(*TrackingSegment)(nil),          // 1: proxymanager.tracking.v2.TrackingSegment
	(*TrackingResponse)(nil),         // 2: proxymanager.tracking.v2.TrackingResponse
	(*CheckDomainBlockRequest)(nil),  // 3: proxymanager.tracking.v2.CheckDomainBlockRequest
	(*CheckDomainBlockResponse)(nil), // 4: proxymanager.tracking.v2.CheckDomainBlockResponse
}
var file_proxymanager_tracking_v2_tracking_proto_depIdxs = []int32{
	1, // 0: proxymanager.tracking.v2.TrackingRequest.list_tracking:type_name -> proxymanager.tracking.v2.TrackingSegment
	0, // 1: proxymanager.tracking.v2.TrackingService.Tracking:input_type -> proxymanager.tracking.v2.TrackingRequest
	3, // 2: proxymanager.tracking.v2.TrackingService.CheckDomainBlock:input_type -> proxymanager.tracking.v2.CheckDomainBlockRequest
	2, // 3: proxymanager.tracking.v2.TrackingService.Tracking:output_type -> proxymanager.tracking.v2.TrackingResponse
	4, // 4: proxymanager.tracking.v2.TrackingService.CheckDomainBlock:output_type -> proxymanager.tracking.v2.CheckDomainBlockResponse
	3, // [3:5] is the sub-list for method output_type
	1, // [1:3] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_proxymanager_tracking_v2_tracking_proto_init() }
func file_proxymanager_tracking_v2_tracking_proto_init() {
	if File_proxymanager_tracking_v2_tracking_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_tracking_v2_tracking_proto_rawDesc), len(file_proxymanager_tracking_v2_tracking_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_tracking_v2_tracking_proto_goTypes,
		DependencyIndexes: file_proxymanager_tracking_v2_tracking_proto_depIdxs,
		MessageInfos:      file_proxymanager_tracking_v2_tracking_proto_msgTypes,
	}.Build()
	File_proxymanager_tracking_v2_tracking_proto = out.File
	file_proxymanager_tracking_v2_tracking_proto_goTypes = nil
	file_proxymanager_tracking_v2_tracking_proto_depIdxs = nil
}
