// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/tracking/v1/tracking.proto

package trackingv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/tracking/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// TrackingServiceName is the fully-qualified name of the TrackingService service.
	TrackingServiceName = "proxymanager.tracking.v1.TrackingService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// TrackingServiceTrackingProcedure is the fully-qualified name of the TrackingService's Tracking
	// RPC.
	TrackingServiceTrackingProcedure = "/proxymanager.tracking.v1.TrackingService/Tracking"
)

// TrackingServiceClient is a client for the proxymanager.tracking.v1.TrackingService service.
type TrackingServiceClient interface {
	Tracking(context.Context, *connect.Request[v1.TrackingRequest]) (*connect.Response[v1.TrackingResponse], error)
}

// NewTrackingServiceClient constructs a client for the proxymanager.tracking.v1.TrackingService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewTrackingServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) TrackingServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	trackingServiceMethods := v1.File_proxymanager_tracking_v1_tracking_proto.Services().ByName("TrackingService").Methods()
	return &trackingServiceClient{
		tracking: connect.NewClient[v1.TrackingRequest, v1.TrackingResponse](
			httpClient,
			baseURL+TrackingServiceTrackingProcedure,
			connect.WithSchema(trackingServiceMethods.ByName("Tracking")),
			connect.WithClientOptions(opts...),
		),
	}
}

// trackingServiceClient implements TrackingServiceClient.
type trackingServiceClient struct {
	tracking *connect.Client[v1.TrackingRequest, v1.TrackingResponse]
}

// Tracking calls proxymanager.tracking.v1.TrackingService.Tracking.
func (c *trackingServiceClient) Tracking(ctx context.Context, req *connect.Request[v1.TrackingRequest]) (*connect.Response[v1.TrackingResponse], error) {
	return c.tracking.CallUnary(ctx, req)
}

// TrackingServiceHandler is an implementation of the proxymanager.tracking.v1.TrackingService
// service.
type TrackingServiceHandler interface {
	Tracking(context.Context, *connect.Request[v1.TrackingRequest]) (*connect.Response[v1.TrackingResponse], error)
}

// NewTrackingServiceHandler builds an HTTP handler from the service implementation. It returns the
// path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewTrackingServiceHandler(svc TrackingServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	trackingServiceMethods := v1.File_proxymanager_tracking_v1_tracking_proto.Services().ByName("TrackingService").Methods()
	trackingServiceTrackingHandler := connect.NewUnaryHandler(
		TrackingServiceTrackingProcedure,
		svc.Tracking,
		connect.WithSchema(trackingServiceMethods.ByName("Tracking")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.tracking.v1.TrackingService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case TrackingServiceTrackingProcedure:
			trackingServiceTrackingHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedTrackingServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedTrackingServiceHandler struct{}

func (UnimplementedTrackingServiceHandler) Tracking(context.Context, *connect.Request[v1.TrackingRequest]) (*connect.Response[v1.TrackingResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.tracking.v1.TrackingService.Tracking is not implemented"))
}
