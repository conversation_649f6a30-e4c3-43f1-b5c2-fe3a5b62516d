// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/tracking/v1/tracking.proto

package trackingv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TrackingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdSession     string                 `protobuf:"bytes,1,opt,name=id_session,json=idSession,proto3" json:"id_session,omitempty"`
	IpUser        string                 `protobuf:"bytes,2,opt,name=ip_user,json=ipUser,proto3" json:"ip_user,omitempty"`
	Domain        string                 `protobuf:"bytes,3,opt,name=domain,proto3" json:"domain,omitempty"`
	IpOfDomain    string                 `protobuf:"bytes,4,opt,name=ip_of_domain,json=ipOfDomain,proto3" json:"ip_of_domain,omitempty"`
	BytesUpload   int64                  `protobuf:"varint,5,opt,name=bytes_upload,json=bytesUpload,proto3" json:"bytes_upload,omitempty"`
	BytesDownload int64                  `protobuf:"varint,6,opt,name=bytes_download,json=bytesDownload,proto3" json:"bytes_download,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrackingRequest) Reset() {
	*x = TrackingRequest{}
	mi := &file_proxymanager_tracking_v1_tracking_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackingRequest) ProtoMessage() {}

func (x *TrackingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_tracking_v1_tracking_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackingRequest.ProtoReflect.Descriptor instead.
func (*TrackingRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_tracking_v1_tracking_proto_rawDescGZIP(), []int{0}
}

func (x *TrackingRequest) GetIdSession() string {
	if x != nil {
		return x.IdSession
	}
	return ""
}

func (x *TrackingRequest) GetIpUser() string {
	if x != nil {
		return x.IpUser
	}
	return ""
}

func (x *TrackingRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *TrackingRequest) GetIpOfDomain() string {
	if x != nil {
		return x.IpOfDomain
	}
	return ""
}

func (x *TrackingRequest) GetBytesUpload() int64 {
	if x != nil {
		return x.BytesUpload
	}
	return 0
}

func (x *TrackingRequest) GetBytesDownload() int64 {
	if x != nil {
		return x.BytesDownload
	}
	return 0
}

type TrackingResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TrackingResponse) Reset() {
	*x = TrackingResponse{}
	mi := &file_proxymanager_tracking_v1_tracking_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TrackingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TrackingResponse) ProtoMessage() {}

func (x *TrackingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_tracking_v1_tracking_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TrackingResponse.ProtoReflect.Descriptor instead.
func (*TrackingResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_tracking_v1_tracking_proto_rawDescGZIP(), []int{1}
}

func (x *TrackingResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

var File_proxymanager_tracking_v1_tracking_proto protoreflect.FileDescriptor

const file_proxymanager_tracking_v1_tracking_proto_rawDesc = "" +
	"\n" +
	"'proxymanager/tracking/v1/tracking.proto\x12\x18proxymanager.tracking.v1\"\xcd\x01\n" +
	"\x0fTrackingRequest\x12\x1d\n" +
	"\n" +
	"id_session\x18\x01 \x01(\tR\tidSession\x12\x17\n" +
	"\aip_user\x18\x02 \x01(\tR\x06ipUser\x12\x16\n" +
	"\x06domain\x18\x03 \x01(\tR\x06domain\x12 \n" +
	"\fip_of_domain\x18\x04 \x01(\tR\n" +
	"ipOfDomain\x12!\n" +
	"\fbytes_upload\x18\x05 \x01(\x03R\vbytesUpload\x12%\n" +
	"\x0ebytes_download\x18\x06 \x01(\x03R\rbytesDownload\",\n" +
	"\x10TrackingResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess2t\n" +
	"\x0fTrackingService\x12a\n" +
	"\bTracking\x12).proxymanager.tracking.v1.TrackingRequest\x1a*.proxymanager.tracking.v1.TrackingResponseBTZRgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/tracking/v1;trackingv1b\x06proto3"

var (
	file_proxymanager_tracking_v1_tracking_proto_rawDescOnce sync.Once
	file_proxymanager_tracking_v1_tracking_proto_rawDescData []byte
)

func file_proxymanager_tracking_v1_tracking_proto_rawDescGZIP() []byte {
	file_proxymanager_tracking_v1_tracking_proto_rawDescOnce.Do(func() {
		file_proxymanager_tracking_v1_tracking_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_tracking_v1_tracking_proto_rawDesc), len(file_proxymanager_tracking_v1_tracking_proto_rawDesc)))
	})
	return file_proxymanager_tracking_v1_tracking_proto_rawDescData
}

var file_proxymanager_tracking_v1_tracking_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_proxymanager_tracking_v1_tracking_proto_goTypes = []any{
	(*TrackingRequest)(nil),  // 0: proxymanager.tracking.v1.TrackingRequest
	(*TrackingResponse)(nil), // 1: proxymanager.tracking.v1.TrackingResponse
}
var file_proxymanager_tracking_v1_tracking_proto_depIdxs = []int32{
	0, // 0: proxymanager.tracking.v1.TrackingService.Tracking:input_type -> proxymanager.tracking.v1.TrackingRequest
	1, // 1: proxymanager.tracking.v1.TrackingService.Tracking:output_type -> proxymanager.tracking.v1.TrackingResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proxymanager_tracking_v1_tracking_proto_init() }
func file_proxymanager_tracking_v1_tracking_proto_init() {
	if File_proxymanager_tracking_v1_tracking_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_tracking_v1_tracking_proto_rawDesc), len(file_proxymanager_tracking_v1_tracking_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_tracking_v1_tracking_proto_goTypes,
		DependencyIndexes: file_proxymanager_tracking_v1_tracking_proto_depIdxs,
		MessageInfos:      file_proxymanager_tracking_v1_tracking_proto_msgTypes,
	}.Build()
	File_proxymanager_tracking_v1_tracking_proto = out.File
	file_proxymanager_tracking_v1_tracking_proto_goTypes = nil
	file_proxymanager_tracking_v1_tracking_proto_depIdxs = nil
}
