// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/plan/v1/internal.proto

package planv1

import (
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InternalPlanServiceFetchPlanRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant    string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	IdPlan        []string               `protobuf:"bytes,2,rep,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalPlanServiceFetchPlanRequest) Reset() {
	*x = InternalPlanServiceFetchPlanRequest{}
	mi := &file_proxymanager_plan_v1_internal_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalPlanServiceFetchPlanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalPlanServiceFetchPlanRequest) ProtoMessage() {}

func (x *InternalPlanServiceFetchPlanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_internal_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalPlanServiceFetchPlanRequest.ProtoReflect.Descriptor instead.
func (*InternalPlanServiceFetchPlanRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_internal_proto_rawDescGZIP(), []int{0}
}

func (x *InternalPlanServiceFetchPlanRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *InternalPlanServiceFetchPlanRequest) GetIdPlan() []string {
	if x != nil {
		return x.IdPlan
	}
	return nil
}

type InternalPlanServiceFetchPlanResponse struct {
	state         protoimpl.MessageState           `protogen:"open.v1"`
	Plan          []*InternalPlanServicePlanEntity `protobuf:"bytes,1,rep,name=plan,proto3" json:"plan,omitempty"`
	Error         *v1.ErrorMessage                 `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InternalPlanServiceFetchPlanResponse) Reset() {
	*x = InternalPlanServiceFetchPlanResponse{}
	mi := &file_proxymanager_plan_v1_internal_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalPlanServiceFetchPlanResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalPlanServiceFetchPlanResponse) ProtoMessage() {}

func (x *InternalPlanServiceFetchPlanResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_internal_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalPlanServiceFetchPlanResponse.ProtoReflect.Descriptor instead.
func (*InternalPlanServiceFetchPlanResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_internal_proto_rawDescGZIP(), []int{1}
}

func (x *InternalPlanServiceFetchPlanResponse) GetPlan() []*InternalPlanServicePlanEntity {
	if x != nil {
		return x.Plan
	}
	return nil
}

func (x *InternalPlanServiceFetchPlanResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type InternalPlanServicePlanEntity struct {
	state                     protoimpl.MessageState `protogen:"open.v1"`
	IdPlan                    string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdMerchant                string                 `protobuf:"bytes,2,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	Name                      string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	DataTransferType          v11.DataTransferType   `protobuf:"varint,4,opt,name=data_transfer_type,json=dataTransferType,proto3,enum=algoenum.v1.DataTransferType" json:"data_transfer_type,omitempty"`
	TimeToLiveProxyInSec      int64                  `protobuf:"varint,5,opt,name=time_to_live_proxy_in_sec,json=timeToLiveProxyInSec,proto3" json:"time_to_live_proxy_in_sec,omitempty"`
	TimeToChangePerProxyInSec int64                  `protobuf:"varint,6,opt,name=time_to_change_per_proxy_in_sec,json=timeToChangePerProxyInSec,proto3" json:"time_to_change_per_proxy_in_sec,omitempty"`
	ConcurrentProxy           int64                  `protobuf:"varint,7,opt,name=concurrent_proxy,json=concurrentProxy,proto3" json:"concurrent_proxy,omitempty"`
	IsActive                  bool                   `protobuf:"varint,8,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *InternalPlanServicePlanEntity) Reset() {
	*x = InternalPlanServicePlanEntity{}
	mi := &file_proxymanager_plan_v1_internal_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalPlanServicePlanEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalPlanServicePlanEntity) ProtoMessage() {}

func (x *InternalPlanServicePlanEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_internal_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalPlanServicePlanEntity.ProtoReflect.Descriptor instead.
func (*InternalPlanServicePlanEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_internal_proto_rawDescGZIP(), []int{2}
}

func (x *InternalPlanServicePlanEntity) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *InternalPlanServicePlanEntity) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *InternalPlanServicePlanEntity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *InternalPlanServicePlanEntity) GetDataTransferType() v11.DataTransferType {
	if x != nil {
		return x.DataTransferType
	}
	return v11.DataTransferType(0)
}

func (x *InternalPlanServicePlanEntity) GetTimeToLiveProxyInSec() int64 {
	if x != nil {
		return x.TimeToLiveProxyInSec
	}
	return 0
}

func (x *InternalPlanServicePlanEntity) GetTimeToChangePerProxyInSec() int64 {
	if x != nil {
		return x.TimeToChangePerProxyInSec
	}
	return 0
}

func (x *InternalPlanServicePlanEntity) GetConcurrentProxy() int64 {
	if x != nil {
		return x.ConcurrentProxy
	}
	return 0
}

func (x *InternalPlanServicePlanEntity) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

var File_proxymanager_plan_v1_internal_proto protoreflect.FileDescriptor

const file_proxymanager_plan_v1_internal_proto_rawDesc = "" +
	"\n" +
	"#proxymanager/plan/v1/internal.proto\x12\x14proxymanager.plan.v1\x1a\x18errmsg/v1/errormsg.proto\x1a$algoenum/v1/data_transfer_type.proto\"_\n" +
	"#InternalPlanServiceFetchPlanRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12\x17\n" +
	"\aid_plan\x18\x02 \x03(\tR\x06idPlan\"\x9e\x01\n" +
	"$InternalPlanServiceFetchPlanResponse\x12G\n" +
	"\x04plan\x18\x01 \x03(\v23.proxymanager.plan.v1.InternalPlanServicePlanEntityR\x04plan\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xff\x02\n" +
	"\x1dInternalPlanServicePlanEntity\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x1f\n" +
	"\vid_merchant\x18\x02 \x01(\tR\n" +
	"idMerchant\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12K\n" +
	"\x12data_transfer_type\x18\x04 \x01(\x0e2\x1d.algoenum.v1.DataTransferTypeR\x10dataTransferType\x127\n" +
	"\x19time_to_live_proxy_in_sec\x18\x05 \x01(\x03R\x14timeToLiveProxyInSec\x12B\n" +
	"\x1ftime_to_change_per_proxy_in_sec\x18\x06 \x01(\x03R\x19timeToChangePerProxyInSec\x12)\n" +
	"\x10concurrent_proxy\x18\a \x01(\x03R\x0fconcurrentProxy\x12\x1b\n" +
	"\tis_active\x18\b \x01(\bR\bisActive2\x9a\x01\n" +
	"\x13InternalPlanService\x12\x82\x01\n" +
	"\tFetchPlan\x129.proxymanager.plan.v1.InternalPlanServiceFetchPlanRequest\x1a:.proxymanager.plan.v1.InternalPlanServiceFetchPlanResponseBLZJgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/plan/v1;planv1b\x06proto3"

var (
	file_proxymanager_plan_v1_internal_proto_rawDescOnce sync.Once
	file_proxymanager_plan_v1_internal_proto_rawDescData []byte
)

func file_proxymanager_plan_v1_internal_proto_rawDescGZIP() []byte {
	file_proxymanager_plan_v1_internal_proto_rawDescOnce.Do(func() {
		file_proxymanager_plan_v1_internal_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_plan_v1_internal_proto_rawDesc), len(file_proxymanager_plan_v1_internal_proto_rawDesc)))
	})
	return file_proxymanager_plan_v1_internal_proto_rawDescData
}

var file_proxymanager_plan_v1_internal_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_proxymanager_plan_v1_internal_proto_goTypes = []any{
	(*InternalPlanServiceFetchPlanRequest)(nil),  // 0: proxymanager.plan.v1.InternalPlanServiceFetchPlanRequest
	(*InternalPlanServiceFetchPlanResponse)(nil), // 1: proxymanager.plan.v1.InternalPlanServiceFetchPlanResponse
	(*InternalPlanServicePlanEntity)(nil),        // 2: proxymanager.plan.v1.InternalPlanServicePlanEntity
	(*v1.ErrorMessage)(nil),                      // 3: errmsg.v1.ErrorMessage
	(v11.DataTransferType)(0),                    // 4: algoenum.v1.DataTransferType
}
var file_proxymanager_plan_v1_internal_proto_depIdxs = []int32{
	2, // 0: proxymanager.plan.v1.InternalPlanServiceFetchPlanResponse.plan:type_name -> proxymanager.plan.v1.InternalPlanServicePlanEntity
	3, // 1: proxymanager.plan.v1.InternalPlanServiceFetchPlanResponse.error:type_name -> errmsg.v1.ErrorMessage
	4, // 2: proxymanager.plan.v1.InternalPlanServicePlanEntity.data_transfer_type:type_name -> algoenum.v1.DataTransferType
	0, // 3: proxymanager.plan.v1.InternalPlanService.FetchPlan:input_type -> proxymanager.plan.v1.InternalPlanServiceFetchPlanRequest
	1, // 4: proxymanager.plan.v1.InternalPlanService.FetchPlan:output_type -> proxymanager.plan.v1.InternalPlanServiceFetchPlanResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_proxymanager_plan_v1_internal_proto_init() }
func file_proxymanager_plan_v1_internal_proto_init() {
	if File_proxymanager_plan_v1_internal_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_plan_v1_internal_proto_rawDesc), len(file_proxymanager_plan_v1_internal_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_plan_v1_internal_proto_goTypes,
		DependencyIndexes: file_proxymanager_plan_v1_internal_proto_depIdxs,
		MessageInfos:      file_proxymanager_plan_v1_internal_proto_msgTypes,
	}.Build()
	File_proxymanager_plan_v1_internal_proto = out.File
	file_proxymanager_plan_v1_internal_proto_goTypes = nil
	file_proxymanager_plan_v1_internal_proto_depIdxs = nil
}
