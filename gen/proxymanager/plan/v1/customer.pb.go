// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/plan/v1/customer.proto

package planv1

import (
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CustomerPlanServiceFetchAvailableLocationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,8,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPlanServiceFetchAvailableLocationRequest) Reset() {
	*x = CustomerPlanServiceFetchAvailableLocationRequest{}
	mi := &file_proxymanager_plan_v1_customer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPlanServiceFetchAvailableLocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPlanServiceFetchAvailableLocationRequest) ProtoMessage() {}

func (x *CustomerPlanServiceFetchAvailableLocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_customer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPlanServiceFetchAvailableLocationRequest.ProtoReflect.Descriptor instead.
func (*CustomerPlanServiceFetchAvailableLocationRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_customer_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerPlanServiceFetchAvailableLocationRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerPlanServiceFetchAvailableLocationResponse struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	Locations     []*CustomerPlanServicePlanLocation `protobuf:"bytes,1,rep,name=locations,proto3" json:"locations,omitempty"`
	Error         *v11.ErrorMessage                  `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v1.PaginationResponse             `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPlanServiceFetchAvailableLocationResponse) Reset() {
	*x = CustomerPlanServiceFetchAvailableLocationResponse{}
	mi := &file_proxymanager_plan_v1_customer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPlanServiceFetchAvailableLocationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPlanServiceFetchAvailableLocationResponse) ProtoMessage() {}

func (x *CustomerPlanServiceFetchAvailableLocationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_customer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPlanServiceFetchAvailableLocationResponse.ProtoReflect.Descriptor instead.
func (*CustomerPlanServiceFetchAvailableLocationResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_customer_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerPlanServiceFetchAvailableLocationResponse) GetLocations() []*CustomerPlanServicePlanLocation {
	if x != nil {
		return x.Locations
	}
	return nil
}

func (x *CustomerPlanServiceFetchAvailableLocationResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerPlanServiceFetchAvailableLocationResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerPlanServiceFetchPlanRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	IdPlan           string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdLocation       string                 `protobuf:"bytes,2,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	NameSearch       string                 `protobuf:"bytes,3,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	IpType           v12.IPType             `protobuf:"varint,4,opt,name=ip_type,json=ipType,proto3,enum=algoenum.v1.IPType" json:"ip_type,omitempty"`
	ProxyType        v12.ProxyType          `protobuf:"varint,5,opt,name=proxy_type,json=proxyType,proto3,enum=algoenum.v1.ProxyType" json:"proxy_type,omitempty"`
	ChangeType       v12.ChangeType         `protobuf:"varint,6,opt,name=change_type,json=changeType,proto3,enum=algoenum.v1.ChangeType" json:"change_type,omitempty"`
	DataTransferType v12.DataTransferType   `protobuf:"varint,7,opt,name=data_transfer_type,json=dataTransferType,proto3,enum=algoenum.v1.DataTransferType" json:"data_transfer_type,omitempty"`
	Pagination       *v1.PaginationRequest  `protobuf:"bytes,8,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CustomerPlanServiceFetchPlanRequest) Reset() {
	*x = CustomerPlanServiceFetchPlanRequest{}
	mi := &file_proxymanager_plan_v1_customer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPlanServiceFetchPlanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPlanServiceFetchPlanRequest) ProtoMessage() {}

func (x *CustomerPlanServiceFetchPlanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_customer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPlanServiceFetchPlanRequest.ProtoReflect.Descriptor instead.
func (*CustomerPlanServiceFetchPlanRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_customer_proto_rawDescGZIP(), []int{2}
}

func (x *CustomerPlanServiceFetchPlanRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *CustomerPlanServiceFetchPlanRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *CustomerPlanServiceFetchPlanRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *CustomerPlanServiceFetchPlanRequest) GetIpType() v12.IPType {
	if x != nil {
		return x.IpType
	}
	return v12.IPType(0)
}

func (x *CustomerPlanServiceFetchPlanRequest) GetProxyType() v12.ProxyType {
	if x != nil {
		return x.ProxyType
	}
	return v12.ProxyType(0)
}

func (x *CustomerPlanServiceFetchPlanRequest) GetChangeType() v12.ChangeType {
	if x != nil {
		return x.ChangeType
	}
	return v12.ChangeType(0)
}

func (x *CustomerPlanServiceFetchPlanRequest) GetDataTransferType() v12.DataTransferType {
	if x != nil {
		return x.DataTransferType
	}
	return v12.DataTransferType(0)
}

func (x *CustomerPlanServiceFetchPlanRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerPlanServiceFetchPlanResponse struct {
	state         protoimpl.MessageState           `protogen:"open.v1"`
	Plans         []*CustomerPlanServicePlanEntity `protobuf:"bytes,1,rep,name=plans,proto3" json:"plans,omitempty"`
	Error         *v11.ErrorMessage                `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v1.PaginationResponse           `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPlanServiceFetchPlanResponse) Reset() {
	*x = CustomerPlanServiceFetchPlanResponse{}
	mi := &file_proxymanager_plan_v1_customer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPlanServiceFetchPlanResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPlanServiceFetchPlanResponse) ProtoMessage() {}

func (x *CustomerPlanServiceFetchPlanResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_customer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPlanServiceFetchPlanResponse.ProtoReflect.Descriptor instead.
func (*CustomerPlanServiceFetchPlanResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_customer_proto_rawDescGZIP(), []int{3}
}

func (x *CustomerPlanServiceFetchPlanResponse) GetPlans() []*CustomerPlanServicePlanEntity {
	if x != nil {
		return x.Plans
	}
	return nil
}

func (x *CustomerPlanServiceFetchPlanResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerPlanServiceFetchPlanResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerPlanServicePlanEntity struct {
	state                     protoimpl.MessageState             `protogen:"open.v1"`
	IdPlan                    string                             `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	Name                      string                             `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Locations                 []*CustomerPlanServicePlanLocation `protobuf:"bytes,4,rep,name=locations,proto3" json:"locations,omitempty"`
	IpType                    v12.IPType                         `protobuf:"varint,5,opt,name=ip_type,json=ipType,proto3,enum=algoenum.v1.IPType" json:"ip_type,omitempty"`
	ProxyType                 v12.ProxyType                      `protobuf:"varint,6,opt,name=proxy_type,json=proxyType,proto3,enum=algoenum.v1.ProxyType" json:"proxy_type,omitempty"`
	ChangeType                v12.ChangeType                     `protobuf:"varint,7,opt,name=change_type,json=changeType,proto3,enum=algoenum.v1.ChangeType" json:"change_type,omitempty"`
	DataTransferType          v12.DataTransferType               `protobuf:"varint,8,opt,name=data_transfer_type,json=dataTransferType,proto3,enum=algoenum.v1.DataTransferType" json:"data_transfer_type,omitempty"`
	DataTransferInGbyte       int64                              `protobuf:"varint,9,opt,name=data_transfer_in_gbyte,json=dataTransferInGbyte,proto3" json:"data_transfer_in_gbyte,omitempty"`
	BandwidthPerProxyInMbit   int64                              `protobuf:"varint,10,opt,name=bandwidth_per_proxy_in_mbit,json=bandwidthPerProxyInMbit,proto3" json:"bandwidth_per_proxy_in_mbit,omitempty"`
	TimeToLivePerProxyInSec   int64                              `protobuf:"varint,11,opt,name=time_to_live_per_proxy_in_sec,json=timeToLivePerProxyInSec,proto3" json:"time_to_live_per_proxy_in_sec,omitempty"`
	TimeToChangePerProxyInSec int64                              `protobuf:"varint,12,opt,name=time_to_change_per_proxy_in_sec,json=timeToChangePerProxyInSec,proto3" json:"time_to_change_per_proxy_in_sec,omitempty"`
	ConcurrentProxy           int64                              `protobuf:"varint,13,opt,name=concurrent_proxy,json=concurrentProxy,proto3" json:"concurrent_proxy,omitempty"`
	Description               string                             `protobuf:"bytes,14,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *CustomerPlanServicePlanEntity) Reset() {
	*x = CustomerPlanServicePlanEntity{}
	mi := &file_proxymanager_plan_v1_customer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPlanServicePlanEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPlanServicePlanEntity) ProtoMessage() {}

func (x *CustomerPlanServicePlanEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_customer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPlanServicePlanEntity.ProtoReflect.Descriptor instead.
func (*CustomerPlanServicePlanEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_customer_proto_rawDescGZIP(), []int{4}
}

func (x *CustomerPlanServicePlanEntity) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *CustomerPlanServicePlanEntity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerPlanServicePlanEntity) GetLocations() []*CustomerPlanServicePlanLocation {
	if x != nil {
		return x.Locations
	}
	return nil
}

func (x *CustomerPlanServicePlanEntity) GetIpType() v12.IPType {
	if x != nil {
		return x.IpType
	}
	return v12.IPType(0)
}

func (x *CustomerPlanServicePlanEntity) GetProxyType() v12.ProxyType {
	if x != nil {
		return x.ProxyType
	}
	return v12.ProxyType(0)
}

func (x *CustomerPlanServicePlanEntity) GetChangeType() v12.ChangeType {
	if x != nil {
		return x.ChangeType
	}
	return v12.ChangeType(0)
}

func (x *CustomerPlanServicePlanEntity) GetDataTransferType() v12.DataTransferType {
	if x != nil {
		return x.DataTransferType
	}
	return v12.DataTransferType(0)
}

func (x *CustomerPlanServicePlanEntity) GetDataTransferInGbyte() int64 {
	if x != nil {
		return x.DataTransferInGbyte
	}
	return 0
}

func (x *CustomerPlanServicePlanEntity) GetBandwidthPerProxyInMbit() int64 {
	if x != nil {
		return x.BandwidthPerProxyInMbit
	}
	return 0
}

func (x *CustomerPlanServicePlanEntity) GetTimeToLivePerProxyInSec() int64 {
	if x != nil {
		return x.TimeToLivePerProxyInSec
	}
	return 0
}

func (x *CustomerPlanServicePlanEntity) GetTimeToChangePerProxyInSec() int64 {
	if x != nil {
		return x.TimeToChangePerProxyInSec
	}
	return 0
}

func (x *CustomerPlanServicePlanEntity) GetConcurrentProxy() int64 {
	if x != nil {
		return x.ConcurrentProxy
	}
	return 0
}

func (x *CustomerPlanServicePlanEntity) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type CustomerPlanServicePlanLocation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdLocation    string                 `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	LocationLevel v12.LocationLevel      `protobuf:"varint,3,opt,name=location_level,json=locationLevel,proto3,enum=algoenum.v1.LocationLevel" json:"location_level,omitempty"`
	Emoji         string                 `protobuf:"bytes,4,opt,name=emoji,proto3" json:"emoji,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPlanServicePlanLocation) Reset() {
	*x = CustomerPlanServicePlanLocation{}
	mi := &file_proxymanager_plan_v1_customer_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPlanServicePlanLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPlanServicePlanLocation) ProtoMessage() {}

func (x *CustomerPlanServicePlanLocation) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_customer_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPlanServicePlanLocation.ProtoReflect.Descriptor instead.
func (*CustomerPlanServicePlanLocation) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_customer_proto_rawDescGZIP(), []int{5}
}

func (x *CustomerPlanServicePlanLocation) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *CustomerPlanServicePlanLocation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerPlanServicePlanLocation) GetLocationLevel() v12.LocationLevel {
	if x != nil {
		return x.LocationLevel
	}
	return v12.LocationLevel(0)
}

func (x *CustomerPlanServicePlanLocation) GetEmoji() string {
	if x != nil {
		return x.Emoji
	}
	return ""
}

var File_proxymanager_plan_v1_customer_proto protoreflect.FileDescriptor

const file_proxymanager_plan_v1_customer_proto_rawDesc = "" +
	"\n" +
	"#proxymanager/plan/v1/customer.proto\x12\x14proxymanager.plan.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\x1a\x19algoenum/v1/ip_type.proto\x1a\x1calgoenum/v1/proxy_type.proto\x1a\x1dalgoenum/v1/change_type.proto\x1a$algoenum/v1/data_transfer_type.proto\x1a algoenum/v1/location_level.proto\"o\n" +
	"0CustomerPlanServiceFetchAvailableLocationRequest\x12;\n" +
	"\n" +
	"pagination\x18\b \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xf5\x01\n" +
	"1CustomerPlanServiceFetchAvailableLocationResponse\x12S\n" +
	"\tlocations\x18\x01 \x03(\v25.proxymanager.plan.v1.CustomerPlanServicePlanLocationR\tlocations\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\xa9\x03\n" +
	"#CustomerPlanServiceFetchPlanRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x1f\n" +
	"\vid_location\x18\x02 \x01(\tR\n" +
	"idLocation\x12\x1f\n" +
	"\vname_search\x18\x03 \x01(\tR\n" +
	"nameSearch\x12,\n" +
	"\aip_type\x18\x04 \x01(\x0e2\x13.algoenum.v1.IPTypeR\x06ipType\x125\n" +
	"\n" +
	"proxy_type\x18\x05 \x01(\x0e2\x16.algoenum.v1.ProxyTypeR\tproxyType\x128\n" +
	"\vchange_type\x18\x06 \x01(\x0e2\x17.algoenum.v1.ChangeTypeR\n" +
	"changeType\x12K\n" +
	"\x12data_transfer_type\x18\a \x01(\x0e2\x1d.algoenum.v1.DataTransferTypeR\x10dataTransferType\x12;\n" +
	"\n" +
	"pagination\x18\b \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xde\x01\n" +
	"$CustomerPlanServiceFetchPlanResponse\x12I\n" +
	"\x05plans\x18\x01 \x03(\v23.proxymanager.plan.v1.CustomerPlanServicePlanEntityR\x05plans\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\xd1\x05\n" +
	"\x1dCustomerPlanServicePlanEntity\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12S\n" +
	"\tlocations\x18\x04 \x03(\v25.proxymanager.plan.v1.CustomerPlanServicePlanLocationR\tlocations\x12,\n" +
	"\aip_type\x18\x05 \x01(\x0e2\x13.algoenum.v1.IPTypeR\x06ipType\x125\n" +
	"\n" +
	"proxy_type\x18\x06 \x01(\x0e2\x16.algoenum.v1.ProxyTypeR\tproxyType\x128\n" +
	"\vchange_type\x18\a \x01(\x0e2\x17.algoenum.v1.ChangeTypeR\n" +
	"changeType\x12K\n" +
	"\x12data_transfer_type\x18\b \x01(\x0e2\x1d.algoenum.v1.DataTransferTypeR\x10dataTransferType\x123\n" +
	"\x16data_transfer_in_gbyte\x18\t \x01(\x03R\x13dataTransferInGbyte\x12<\n" +
	"\x1bbandwidth_per_proxy_in_mbit\x18\n" +
	" \x01(\x03R\x17bandwidthPerProxyInMbit\x12>\n" +
	"\x1dtime_to_live_per_proxy_in_sec\x18\v \x01(\x03R\x17timeToLivePerProxyInSec\x12B\n" +
	"\x1ftime_to_change_per_proxy_in_sec\x18\f \x01(\x03R\x19timeToChangePerProxyInSec\x12)\n" +
	"\x10concurrent_proxy\x18\r \x01(\x03R\x0fconcurrentProxy\x12 \n" +
	"\vdescription\x18\x0e \x01(\tR\vdescription\"\xaf\x01\n" +
	"\x1fCustomerPlanServicePlanLocation\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12A\n" +
	"\x0elocation_level\x18\x03 \x01(\x0e2\x1a.algoenum.v1.LocationLevelR\rlocationLevel\x12\x14\n" +
	"\x05emoji\x18\x04 \x01(\tR\x05emoji2\xc6\x02\n" +
	"\x13CustomerPlanService\x12\xa9\x01\n" +
	"\x16FetchAvailableLocation\x12F.proxymanager.plan.v1.CustomerPlanServiceFetchAvailableLocationRequest\x1aG.proxymanager.plan.v1.CustomerPlanServiceFetchAvailableLocationResponse\x12\x82\x01\n" +
	"\tFetchPlan\x129.proxymanager.plan.v1.CustomerPlanServiceFetchPlanRequest\x1a:.proxymanager.plan.v1.CustomerPlanServiceFetchPlanResponseBLZJgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/plan/v1;planv1b\x06proto3"

var (
	file_proxymanager_plan_v1_customer_proto_rawDescOnce sync.Once
	file_proxymanager_plan_v1_customer_proto_rawDescData []byte
)

func file_proxymanager_plan_v1_customer_proto_rawDescGZIP() []byte {
	file_proxymanager_plan_v1_customer_proto_rawDescOnce.Do(func() {
		file_proxymanager_plan_v1_customer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_plan_v1_customer_proto_rawDesc), len(file_proxymanager_plan_v1_customer_proto_rawDesc)))
	})
	return file_proxymanager_plan_v1_customer_proto_rawDescData
}

var file_proxymanager_plan_v1_customer_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_proxymanager_plan_v1_customer_proto_goTypes = []any{
	(*CustomerPlanServiceFetchAvailableLocationRequest)(nil),  // 0: proxymanager.plan.v1.CustomerPlanServiceFetchAvailableLocationRequest
	(*CustomerPlanServiceFetchAvailableLocationResponse)(nil), // 1: proxymanager.plan.v1.CustomerPlanServiceFetchAvailableLocationResponse
	(*CustomerPlanServiceFetchPlanRequest)(nil),               // 2: proxymanager.plan.v1.CustomerPlanServiceFetchPlanRequest
	(*CustomerPlanServiceFetchPlanResponse)(nil),              // 3: proxymanager.plan.v1.CustomerPlanServiceFetchPlanResponse
	(*CustomerPlanServicePlanEntity)(nil),                     // 4: proxymanager.plan.v1.CustomerPlanServicePlanEntity
	(*CustomerPlanServicePlanLocation)(nil),                   // 5: proxymanager.plan.v1.CustomerPlanServicePlanLocation
	(*v1.PaginationRequest)(nil),                              // 6: utils.v1.PaginationRequest
	(*v11.ErrorMessage)(nil),                                  // 7: errmsg.v1.ErrorMessage
	(*v1.PaginationResponse)(nil),                             // 8: utils.v1.PaginationResponse
	(v12.IPType)(0),                                           // 9: algoenum.v1.IPType
	(v12.ProxyType)(0),                                        // 10: algoenum.v1.ProxyType
	(v12.ChangeType)(0),                                       // 11: algoenum.v1.ChangeType
	(v12.DataTransferType)(0),                                 // 12: algoenum.v1.DataTransferType
	(v12.LocationLevel)(0),                                    // 13: algoenum.v1.LocationLevel
}
var file_proxymanager_plan_v1_customer_proto_depIdxs = []int32{
	6,  // 0: proxymanager.plan.v1.CustomerPlanServiceFetchAvailableLocationRequest.pagination:type_name -> utils.v1.PaginationRequest
	5,  // 1: proxymanager.plan.v1.CustomerPlanServiceFetchAvailableLocationResponse.locations:type_name -> proxymanager.plan.v1.CustomerPlanServicePlanLocation
	7,  // 2: proxymanager.plan.v1.CustomerPlanServiceFetchAvailableLocationResponse.error:type_name -> errmsg.v1.ErrorMessage
	8,  // 3: proxymanager.plan.v1.CustomerPlanServiceFetchAvailableLocationResponse.pagination:type_name -> utils.v1.PaginationResponse
	9,  // 4: proxymanager.plan.v1.CustomerPlanServiceFetchPlanRequest.ip_type:type_name -> algoenum.v1.IPType
	10, // 5: proxymanager.plan.v1.CustomerPlanServiceFetchPlanRequest.proxy_type:type_name -> algoenum.v1.ProxyType
	11, // 6: proxymanager.plan.v1.CustomerPlanServiceFetchPlanRequest.change_type:type_name -> algoenum.v1.ChangeType
	12, // 7: proxymanager.plan.v1.CustomerPlanServiceFetchPlanRequest.data_transfer_type:type_name -> algoenum.v1.DataTransferType
	6,  // 8: proxymanager.plan.v1.CustomerPlanServiceFetchPlanRequest.pagination:type_name -> utils.v1.PaginationRequest
	4,  // 9: proxymanager.plan.v1.CustomerPlanServiceFetchPlanResponse.plans:type_name -> proxymanager.plan.v1.CustomerPlanServicePlanEntity
	7,  // 10: proxymanager.plan.v1.CustomerPlanServiceFetchPlanResponse.error:type_name -> errmsg.v1.ErrorMessage
	8,  // 11: proxymanager.plan.v1.CustomerPlanServiceFetchPlanResponse.pagination:type_name -> utils.v1.PaginationResponse
	5,  // 12: proxymanager.plan.v1.CustomerPlanServicePlanEntity.locations:type_name -> proxymanager.plan.v1.CustomerPlanServicePlanLocation
	9,  // 13: proxymanager.plan.v1.CustomerPlanServicePlanEntity.ip_type:type_name -> algoenum.v1.IPType
	10, // 14: proxymanager.plan.v1.CustomerPlanServicePlanEntity.proxy_type:type_name -> algoenum.v1.ProxyType
	11, // 15: proxymanager.plan.v1.CustomerPlanServicePlanEntity.change_type:type_name -> algoenum.v1.ChangeType
	12, // 16: proxymanager.plan.v1.CustomerPlanServicePlanEntity.data_transfer_type:type_name -> algoenum.v1.DataTransferType
	13, // 17: proxymanager.plan.v1.CustomerPlanServicePlanLocation.location_level:type_name -> algoenum.v1.LocationLevel
	0,  // 18: proxymanager.plan.v1.CustomerPlanService.FetchAvailableLocation:input_type -> proxymanager.plan.v1.CustomerPlanServiceFetchAvailableLocationRequest
	2,  // 19: proxymanager.plan.v1.CustomerPlanService.FetchPlan:input_type -> proxymanager.plan.v1.CustomerPlanServiceFetchPlanRequest
	1,  // 20: proxymanager.plan.v1.CustomerPlanService.FetchAvailableLocation:output_type -> proxymanager.plan.v1.CustomerPlanServiceFetchAvailableLocationResponse
	3,  // 21: proxymanager.plan.v1.CustomerPlanService.FetchPlan:output_type -> proxymanager.plan.v1.CustomerPlanServiceFetchPlanResponse
	20, // [20:22] is the sub-list for method output_type
	18, // [18:20] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_proxymanager_plan_v1_customer_proto_init() }
func file_proxymanager_plan_v1_customer_proto_init() {
	if File_proxymanager_plan_v1_customer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_plan_v1_customer_proto_rawDesc), len(file_proxymanager_plan_v1_customer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_plan_v1_customer_proto_goTypes,
		DependencyIndexes: file_proxymanager_plan_v1_customer_proto_depIdxs,
		MessageInfos:      file_proxymanager_plan_v1_customer_proto_msgTypes,
	}.Build()
	File_proxymanager_plan_v1_customer_proto = out.File
	file_proxymanager_plan_v1_customer_proto_goTypes = nil
	file_proxymanager_plan_v1_customer_proto_depIdxs = nil
}
