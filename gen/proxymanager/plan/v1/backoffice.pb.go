// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/plan/v1/backoffice.proto

package planv1

import (
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackofficePlanServiceConfigPlanBackConnectRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	IdPlan               string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdBackConnectManager string                 `protobuf:"bytes,2,opt,name=id_back_connect_manager,json=idBackConnectManager,proto3" json:"id_back_connect_manager,omitempty"`
	IsEnable             bool                   `protobuf:"varint,3,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *BackofficePlanServiceConfigPlanBackConnectRequest) Reset() {
	*x = BackofficePlanServiceConfigPlanBackConnectRequest{}
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanServiceConfigPlanBackConnectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanServiceConfigPlanBackConnectRequest) ProtoMessage() {}

func (x *BackofficePlanServiceConfigPlanBackConnectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanServiceConfigPlanBackConnectRequest.ProtoReflect.Descriptor instead.
func (*BackofficePlanServiceConfigPlanBackConnectRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_backoffice_proto_rawDescGZIP(), []int{0}
}

func (x *BackofficePlanServiceConfigPlanBackConnectRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *BackofficePlanServiceConfigPlanBackConnectRequest) GetIdBackConnectManager() string {
	if x != nil {
		return x.IdBackConnectManager
	}
	return ""
}

func (x *BackofficePlanServiceConfigPlanBackConnectRequest) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

type BackofficePlanServiceConfigPlanBackConnectResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePlanServiceConfigPlanBackConnectResponse) Reset() {
	*x = BackofficePlanServiceConfigPlanBackConnectResponse{}
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanServiceConfigPlanBackConnectResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanServiceConfigPlanBackConnectResponse) ProtoMessage() {}

func (x *BackofficePlanServiceConfigPlanBackConnectResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanServiceConfigPlanBackConnectResponse.ProtoReflect.Descriptor instead.
func (*BackofficePlanServiceConfigPlanBackConnectResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_backoffice_proto_rawDescGZIP(), []int{1}
}

func (x *BackofficePlanServiceConfigPlanBackConnectResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficePlanServiceConfigPlanLocationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdPlan        string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdLocation    string                 `protobuf:"bytes,2,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	IsEnable      bool                   `protobuf:"varint,3,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePlanServiceConfigPlanLocationRequest) Reset() {
	*x = BackofficePlanServiceConfigPlanLocationRequest{}
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanServiceConfigPlanLocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanServiceConfigPlanLocationRequest) ProtoMessage() {}

func (x *BackofficePlanServiceConfigPlanLocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanServiceConfigPlanLocationRequest.ProtoReflect.Descriptor instead.
func (*BackofficePlanServiceConfigPlanLocationRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_backoffice_proto_rawDescGZIP(), []int{2}
}

func (x *BackofficePlanServiceConfigPlanLocationRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *BackofficePlanServiceConfigPlanLocationRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *BackofficePlanServiceConfigPlanLocationRequest) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

type BackofficePlanServiceConfigPlanLocationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePlanServiceConfigPlanLocationResponse) Reset() {
	*x = BackofficePlanServiceConfigPlanLocationResponse{}
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanServiceConfigPlanLocationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanServiceConfigPlanLocationResponse) ProtoMessage() {}

func (x *BackofficePlanServiceConfigPlanLocationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanServiceConfigPlanLocationResponse.ProtoReflect.Descriptor instead.
func (*BackofficePlanServiceConfigPlanLocationResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_backoffice_proto_rawDescGZIP(), []int{3}
}

func (x *BackofficePlanServiceConfigPlanLocationResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficePlanServiceCreatePlanRequest struct {
	state                     protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant                string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	Name                      string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IpType                    v11.IPType             `protobuf:"varint,3,opt,name=ip_type,json=ipType,proto3,enum=algoenum.v1.IPType" json:"ip_type,omitempty"`
	ProxyType                 v11.ProxyType          `protobuf:"varint,4,opt,name=proxy_type,json=proxyType,proto3,enum=algoenum.v1.ProxyType" json:"proxy_type,omitempty"`
	ChangeType                v11.ChangeType         `protobuf:"varint,5,opt,name=change_type,json=changeType,proto3,enum=algoenum.v1.ChangeType" json:"change_type,omitempty"`
	DataTransferType          v11.DataTransferType   `protobuf:"varint,6,opt,name=data_transfer_type,json=dataTransferType,proto3,enum=algoenum.v1.DataTransferType" json:"data_transfer_type,omitempty"`
	BandwidthPerProxyInMbit   int64                  `protobuf:"varint,7,opt,name=bandwidth_per_proxy_in_mbit,json=bandwidthPerProxyInMbit,proto3" json:"bandwidth_per_proxy_in_mbit,omitempty"`
	TimeToLivePerProxyInSec   int64                  `protobuf:"varint,8,opt,name=time_to_live_per_proxy_in_sec,json=timeToLivePerProxyInSec,proto3" json:"time_to_live_per_proxy_in_sec,omitempty"`
	TimeToChangePerProxyInSec int64                  `protobuf:"varint,9,opt,name=time_to_change_per_proxy_in_sec,json=timeToChangePerProxyInSec,proto3" json:"time_to_change_per_proxy_in_sec,omitempty"`
	ConcurrentProxy           int64                  `protobuf:"varint,10,opt,name=concurrent_proxy,json=concurrentProxy,proto3" json:"concurrent_proxy,omitempty"`
	Description               string                 `protobuf:"bytes,11,opt,name=description,proto3" json:"description,omitempty"`
	Index                     int64                  `protobuf:"varint,12,opt,name=index,proto3" json:"index,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *BackofficePlanServiceCreatePlanRequest) Reset() {
	*x = BackofficePlanServiceCreatePlanRequest{}
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanServiceCreatePlanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanServiceCreatePlanRequest) ProtoMessage() {}

func (x *BackofficePlanServiceCreatePlanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanServiceCreatePlanRequest.ProtoReflect.Descriptor instead.
func (*BackofficePlanServiceCreatePlanRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_backoffice_proto_rawDescGZIP(), []int{4}
}

func (x *BackofficePlanServiceCreatePlanRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficePlanServiceCreatePlanRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficePlanServiceCreatePlanRequest) GetIpType() v11.IPType {
	if x != nil {
		return x.IpType
	}
	return v11.IPType(0)
}

func (x *BackofficePlanServiceCreatePlanRequest) GetProxyType() v11.ProxyType {
	if x != nil {
		return x.ProxyType
	}
	return v11.ProxyType(0)
}

func (x *BackofficePlanServiceCreatePlanRequest) GetChangeType() v11.ChangeType {
	if x != nil {
		return x.ChangeType
	}
	return v11.ChangeType(0)
}

func (x *BackofficePlanServiceCreatePlanRequest) GetDataTransferType() v11.DataTransferType {
	if x != nil {
		return x.DataTransferType
	}
	return v11.DataTransferType(0)
}

func (x *BackofficePlanServiceCreatePlanRequest) GetBandwidthPerProxyInMbit() int64 {
	if x != nil {
		return x.BandwidthPerProxyInMbit
	}
	return 0
}

func (x *BackofficePlanServiceCreatePlanRequest) GetTimeToLivePerProxyInSec() int64 {
	if x != nil {
		return x.TimeToLivePerProxyInSec
	}
	return 0
}

func (x *BackofficePlanServiceCreatePlanRequest) GetTimeToChangePerProxyInSec() int64 {
	if x != nil {
		return x.TimeToChangePerProxyInSec
	}
	return 0
}

func (x *BackofficePlanServiceCreatePlanRequest) GetConcurrentProxy() int64 {
	if x != nil {
		return x.ConcurrentProxy
	}
	return 0
}

func (x *BackofficePlanServiceCreatePlanRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BackofficePlanServiceCreatePlanRequest) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

type BackofficePlanServiceCreatePlanResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePlanServiceCreatePlanResponse) Reset() {
	*x = BackofficePlanServiceCreatePlanResponse{}
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanServiceCreatePlanResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanServiceCreatePlanResponse) ProtoMessage() {}

func (x *BackofficePlanServiceCreatePlanResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanServiceCreatePlanResponse.ProtoReflect.Descriptor instead.
func (*BackofficePlanServiceCreatePlanResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_backoffice_proto_rawDescGZIP(), []int{5}
}

func (x *BackofficePlanServiceCreatePlanResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficePlanServiceFetchPlanRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant       string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	IdPlan           string                 `protobuf:"bytes,2,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdLocation       string                 `protobuf:"bytes,3,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	NameSearch       string                 `protobuf:"bytes,4,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	IpType           v11.IPType             `protobuf:"varint,5,opt,name=ip_type,json=ipType,proto3,enum=algoenum.v1.IPType" json:"ip_type,omitempty"`
	ProxyType        v11.ProxyType          `protobuf:"varint,6,opt,name=proxy_type,json=proxyType,proto3,enum=algoenum.v1.ProxyType" json:"proxy_type,omitempty"`
	ChangeType       v11.ChangeType         `protobuf:"varint,7,opt,name=change_type,json=changeType,proto3,enum=algoenum.v1.ChangeType" json:"change_type,omitempty"`
	DataTransferType v11.DataTransferType   `protobuf:"varint,8,opt,name=data_transfer_type,json=dataTransferType,proto3,enum=algoenum.v1.DataTransferType" json:"data_transfer_type,omitempty"`
	State            *v12.State             `protobuf:"bytes,9,opt,name=state,proto3" json:"state,omitempty"`
	Pagination       *v12.PaginationRequest `protobuf:"bytes,10,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BackofficePlanServiceFetchPlanRequest) Reset() {
	*x = BackofficePlanServiceFetchPlanRequest{}
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanServiceFetchPlanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanServiceFetchPlanRequest) ProtoMessage() {}

func (x *BackofficePlanServiceFetchPlanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanServiceFetchPlanRequest.ProtoReflect.Descriptor instead.
func (*BackofficePlanServiceFetchPlanRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_backoffice_proto_rawDescGZIP(), []int{6}
}

func (x *BackofficePlanServiceFetchPlanRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficePlanServiceFetchPlanRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *BackofficePlanServiceFetchPlanRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *BackofficePlanServiceFetchPlanRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *BackofficePlanServiceFetchPlanRequest) GetIpType() v11.IPType {
	if x != nil {
		return x.IpType
	}
	return v11.IPType(0)
}

func (x *BackofficePlanServiceFetchPlanRequest) GetProxyType() v11.ProxyType {
	if x != nil {
		return x.ProxyType
	}
	return v11.ProxyType(0)
}

func (x *BackofficePlanServiceFetchPlanRequest) GetChangeType() v11.ChangeType {
	if x != nil {
		return x.ChangeType
	}
	return v11.ChangeType(0)
}

func (x *BackofficePlanServiceFetchPlanRequest) GetDataTransferType() v11.DataTransferType {
	if x != nil {
		return x.DataTransferType
	}
	return v11.DataTransferType(0)
}

func (x *BackofficePlanServiceFetchPlanRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficePlanServiceFetchPlanRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficePlanServiceFetchPlanResponse struct {
	state         protoimpl.MessageState             `protogen:"open.v1"`
	Error         *v1.ErrorMessage                   `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v12.PaginationResponse            `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Plans         []*BackofficePlanServicePlanEntity `protobuf:"bytes,3,rep,name=plans,proto3" json:"plans,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePlanServiceFetchPlanResponse) Reset() {
	*x = BackofficePlanServiceFetchPlanResponse{}
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanServiceFetchPlanResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanServiceFetchPlanResponse) ProtoMessage() {}

func (x *BackofficePlanServiceFetchPlanResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanServiceFetchPlanResponse.ProtoReflect.Descriptor instead.
func (*BackofficePlanServiceFetchPlanResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_backoffice_proto_rawDescGZIP(), []int{7}
}

func (x *BackofficePlanServiceFetchPlanResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficePlanServiceFetchPlanResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficePlanServiceFetchPlanResponse) GetPlans() []*BackofficePlanServicePlanEntity {
	if x != nil {
		return x.Plans
	}
	return nil
}

type BackofficePlanServiceUpdatePlanRequest struct {
	state                     protoimpl.MessageState `protogen:"open.v1"`
	IdPlan                    string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	Name                      string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IpType                    v11.IPType             `protobuf:"varint,3,opt,name=ip_type,json=ipType,proto3,enum=algoenum.v1.IPType" json:"ip_type,omitempty"`
	ProxyType                 v11.ProxyType          `protobuf:"varint,4,opt,name=proxy_type,json=proxyType,proto3,enum=algoenum.v1.ProxyType" json:"proxy_type,omitempty"`
	ChangeType                v11.ChangeType         `protobuf:"varint,5,opt,name=change_type,json=changeType,proto3,enum=algoenum.v1.ChangeType" json:"change_type,omitempty"`
	DataTransferType          v11.DataTransferType   `protobuf:"varint,6,opt,name=data_transfer_type,json=dataTransferType,proto3,enum=algoenum.v1.DataTransferType" json:"data_transfer_type,omitempty"`
	BandwidthPerProxyInMbit   int64                  `protobuf:"varint,7,opt,name=bandwidth_per_proxy_in_mbit,json=bandwidthPerProxyInMbit,proto3" json:"bandwidth_per_proxy_in_mbit,omitempty"`
	TimeToLivePerProxyInSec   int64                  `protobuf:"varint,8,opt,name=time_to_live_per_proxy_in_sec,json=timeToLivePerProxyInSec,proto3" json:"time_to_live_per_proxy_in_sec,omitempty"`
	TimeToChangePerProxyInSec int64                  `protobuf:"varint,9,opt,name=time_to_change_per_proxy_in_sec,json=timeToChangePerProxyInSec,proto3" json:"time_to_change_per_proxy_in_sec,omitempty"`
	ConcurrentProxy           int64                  `protobuf:"varint,10,opt,name=concurrent_proxy,json=concurrentProxy,proto3" json:"concurrent_proxy,omitempty"`
	Description               string                 `protobuf:"bytes,11,opt,name=description,proto3" json:"description,omitempty"`
	Index                     int64                  `protobuf:"varint,12,opt,name=index,proto3" json:"index,omitempty"`
	State                     *v12.State             `protobuf:"bytes,13,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *BackofficePlanServiceUpdatePlanRequest) Reset() {
	*x = BackofficePlanServiceUpdatePlanRequest{}
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanServiceUpdatePlanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanServiceUpdatePlanRequest) ProtoMessage() {}

func (x *BackofficePlanServiceUpdatePlanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanServiceUpdatePlanRequest.ProtoReflect.Descriptor instead.
func (*BackofficePlanServiceUpdatePlanRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_backoffice_proto_rawDescGZIP(), []int{8}
}

func (x *BackofficePlanServiceUpdatePlanRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *BackofficePlanServiceUpdatePlanRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficePlanServiceUpdatePlanRequest) GetIpType() v11.IPType {
	if x != nil {
		return x.IpType
	}
	return v11.IPType(0)
}

func (x *BackofficePlanServiceUpdatePlanRequest) GetProxyType() v11.ProxyType {
	if x != nil {
		return x.ProxyType
	}
	return v11.ProxyType(0)
}

func (x *BackofficePlanServiceUpdatePlanRequest) GetChangeType() v11.ChangeType {
	if x != nil {
		return x.ChangeType
	}
	return v11.ChangeType(0)
}

func (x *BackofficePlanServiceUpdatePlanRequest) GetDataTransferType() v11.DataTransferType {
	if x != nil {
		return x.DataTransferType
	}
	return v11.DataTransferType(0)
}

func (x *BackofficePlanServiceUpdatePlanRequest) GetBandwidthPerProxyInMbit() int64 {
	if x != nil {
		return x.BandwidthPerProxyInMbit
	}
	return 0
}

func (x *BackofficePlanServiceUpdatePlanRequest) GetTimeToLivePerProxyInSec() int64 {
	if x != nil {
		return x.TimeToLivePerProxyInSec
	}
	return 0
}

func (x *BackofficePlanServiceUpdatePlanRequest) GetTimeToChangePerProxyInSec() int64 {
	if x != nil {
		return x.TimeToChangePerProxyInSec
	}
	return 0
}

func (x *BackofficePlanServiceUpdatePlanRequest) GetConcurrentProxy() int64 {
	if x != nil {
		return x.ConcurrentProxy
	}
	return 0
}

func (x *BackofficePlanServiceUpdatePlanRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BackofficePlanServiceUpdatePlanRequest) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *BackofficePlanServiceUpdatePlanRequest) GetState() *v12.State {
	if x != nil {
		return x.State
	}
	return nil
}

type BackofficePlanServiceUpdatePlanResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePlanServiceUpdatePlanResponse) Reset() {
	*x = BackofficePlanServiceUpdatePlanResponse{}
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanServiceUpdatePlanResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanServiceUpdatePlanResponse) ProtoMessage() {}

func (x *BackofficePlanServiceUpdatePlanResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanServiceUpdatePlanResponse.ProtoReflect.Descriptor instead.
func (*BackofficePlanServiceUpdatePlanResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_backoffice_proto_rawDescGZIP(), []int{9}
}

func (x *BackofficePlanServiceUpdatePlanResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficePlanServicePlanEntity struct {
	state                     protoimpl.MessageState                   `protogen:"open.v1"`
	IdPlan                    string                                   `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	Name                      string                                   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Merchant                  *BackofficePlanServicePlanMerchantEntity `protobuf:"bytes,3,opt,name=merchant,proto3" json:"merchant,omitempty"`
	Locations                 []*BackofficePlanServicePlanLocation     `protobuf:"bytes,4,rep,name=locations,proto3" json:"locations,omitempty"`
	IpType                    v11.IPType                               `protobuf:"varint,5,opt,name=ip_type,json=ipType,proto3,enum=algoenum.v1.IPType" json:"ip_type,omitempty"`
	ProxyType                 v11.ProxyType                            `protobuf:"varint,6,opt,name=proxy_type,json=proxyType,proto3,enum=algoenum.v1.ProxyType" json:"proxy_type,omitempty"`
	ChangeType                v11.ChangeType                           `protobuf:"varint,7,opt,name=change_type,json=changeType,proto3,enum=algoenum.v1.ChangeType" json:"change_type,omitempty"`
	DataTransferType          v11.DataTransferType                     `protobuf:"varint,8,opt,name=data_transfer_type,json=dataTransferType,proto3,enum=algoenum.v1.DataTransferType" json:"data_transfer_type,omitempty"`
	DataTransferInGbyte       int64                                    `protobuf:"varint,9,opt,name=data_transfer_in_gbyte,json=dataTransferInGbyte,proto3" json:"data_transfer_in_gbyte,omitempty"`
	BandwidthPerProxyInMbit   int64                                    `protobuf:"varint,10,opt,name=bandwidth_per_proxy_in_mbit,json=bandwidthPerProxyInMbit,proto3" json:"bandwidth_per_proxy_in_mbit,omitempty"`
	TimeToLivePerProxyInSec   int64                                    `protobuf:"varint,11,opt,name=time_to_live_per_proxy_in_sec,json=timeToLivePerProxyInSec,proto3" json:"time_to_live_per_proxy_in_sec,omitempty"`
	TimeToChangePerProxyInSec int64                                    `protobuf:"varint,12,opt,name=time_to_change_per_proxy_in_sec,json=timeToChangePerProxyInSec,proto3" json:"time_to_change_per_proxy_in_sec,omitempty"`
	ConcurrentProxy           int64                                    `protobuf:"varint,13,opt,name=concurrent_proxy,json=concurrentProxy,proto3" json:"concurrent_proxy,omitempty"`
	Description               string                                   `protobuf:"bytes,14,opt,name=description,proto3" json:"description,omitempty"`
	Index                     int64                                    `protobuf:"varint,15,opt,name=index,proto3" json:"index,omitempty"`
	IsActive                  bool                                     `protobuf:"varint,16,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	BackConnects              []*BackofficePlanServicePlanBackConnect  `protobuf:"bytes,17,rep,name=back_connects,json=backConnects,proto3" json:"back_connects,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *BackofficePlanServicePlanEntity) Reset() {
	*x = BackofficePlanServicePlanEntity{}
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanServicePlanEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanServicePlanEntity) ProtoMessage() {}

func (x *BackofficePlanServicePlanEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanServicePlanEntity.ProtoReflect.Descriptor instead.
func (*BackofficePlanServicePlanEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_backoffice_proto_rawDescGZIP(), []int{10}
}

func (x *BackofficePlanServicePlanEntity) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *BackofficePlanServicePlanEntity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficePlanServicePlanEntity) GetMerchant() *BackofficePlanServicePlanMerchantEntity {
	if x != nil {
		return x.Merchant
	}
	return nil
}

func (x *BackofficePlanServicePlanEntity) GetLocations() []*BackofficePlanServicePlanLocation {
	if x != nil {
		return x.Locations
	}
	return nil
}

func (x *BackofficePlanServicePlanEntity) GetIpType() v11.IPType {
	if x != nil {
		return x.IpType
	}
	return v11.IPType(0)
}

func (x *BackofficePlanServicePlanEntity) GetProxyType() v11.ProxyType {
	if x != nil {
		return x.ProxyType
	}
	return v11.ProxyType(0)
}

func (x *BackofficePlanServicePlanEntity) GetChangeType() v11.ChangeType {
	if x != nil {
		return x.ChangeType
	}
	return v11.ChangeType(0)
}

func (x *BackofficePlanServicePlanEntity) GetDataTransferType() v11.DataTransferType {
	if x != nil {
		return x.DataTransferType
	}
	return v11.DataTransferType(0)
}

func (x *BackofficePlanServicePlanEntity) GetDataTransferInGbyte() int64 {
	if x != nil {
		return x.DataTransferInGbyte
	}
	return 0
}

func (x *BackofficePlanServicePlanEntity) GetBandwidthPerProxyInMbit() int64 {
	if x != nil {
		return x.BandwidthPerProxyInMbit
	}
	return 0
}

func (x *BackofficePlanServicePlanEntity) GetTimeToLivePerProxyInSec() int64 {
	if x != nil {
		return x.TimeToLivePerProxyInSec
	}
	return 0
}

func (x *BackofficePlanServicePlanEntity) GetTimeToChangePerProxyInSec() int64 {
	if x != nil {
		return x.TimeToChangePerProxyInSec
	}
	return 0
}

func (x *BackofficePlanServicePlanEntity) GetConcurrentProxy() int64 {
	if x != nil {
		return x.ConcurrentProxy
	}
	return 0
}

func (x *BackofficePlanServicePlanEntity) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BackofficePlanServicePlanEntity) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *BackofficePlanServicePlanEntity) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *BackofficePlanServicePlanEntity) GetBackConnects() []*BackofficePlanServicePlanBackConnect {
	if x != nil {
		return x.BackConnects
	}
	return nil
}

type BackofficePlanServicePlanBackConnect struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	IdBackConnectManager string                 `protobuf:"bytes,1,opt,name=id_back_connect_manager,json=idBackConnectManager,proto3" json:"id_back_connect_manager,omitempty"`
	Name                 string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *BackofficePlanServicePlanBackConnect) Reset() {
	*x = BackofficePlanServicePlanBackConnect{}
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanServicePlanBackConnect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanServicePlanBackConnect) ProtoMessage() {}

func (x *BackofficePlanServicePlanBackConnect) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanServicePlanBackConnect.ProtoReflect.Descriptor instead.
func (*BackofficePlanServicePlanBackConnect) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_backoffice_proto_rawDescGZIP(), []int{11}
}

func (x *BackofficePlanServicePlanBackConnect) GetIdBackConnectManager() string {
	if x != nil {
		return x.IdBackConnectManager
	}
	return ""
}

func (x *BackofficePlanServicePlanBackConnect) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type BackofficePlanServicePlanMerchantEntity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant    string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IsActive      bool                   `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePlanServicePlanMerchantEntity) Reset() {
	*x = BackofficePlanServicePlanMerchantEntity{}
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanServicePlanMerchantEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanServicePlanMerchantEntity) ProtoMessage() {}

func (x *BackofficePlanServicePlanMerchantEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanServicePlanMerchantEntity.ProtoReflect.Descriptor instead.
func (*BackofficePlanServicePlanMerchantEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_backoffice_proto_rawDescGZIP(), []int{12}
}

func (x *BackofficePlanServicePlanMerchantEntity) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficePlanServicePlanMerchantEntity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficePlanServicePlanMerchantEntity) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficePlanServicePlanLocation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdLocation    string                 `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	LocationLevel v11.LocationLevel      `protobuf:"varint,3,opt,name=location_level,json=locationLevel,proto3,enum=algoenum.v1.LocationLevel" json:"location_level,omitempty"`
	IsActive      bool                   `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePlanServicePlanLocation) Reset() {
	*x = BackofficePlanServicePlanLocation{}
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePlanServicePlanLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePlanServicePlanLocation) ProtoMessage() {}

func (x *BackofficePlanServicePlanLocation) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_backoffice_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePlanServicePlanLocation.ProtoReflect.Descriptor instead.
func (*BackofficePlanServicePlanLocation) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_backoffice_proto_rawDescGZIP(), []int{13}
}

func (x *BackofficePlanServicePlanLocation) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *BackofficePlanServicePlanLocation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficePlanServicePlanLocation) GetLocationLevel() v11.LocationLevel {
	if x != nil {
		return x.LocationLevel
	}
	return v11.LocationLevel(0)
}

func (x *BackofficePlanServicePlanLocation) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

var File_proxymanager_plan_v1_backoffice_proto protoreflect.FileDescriptor

const file_proxymanager_plan_v1_backoffice_proto_rawDesc = "" +
	"\n" +
	"%proxymanager/plan/v1/backoffice.proto\x12\x14proxymanager.plan.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\x1a\x19algoenum/v1/ip_type.proto\x1a\x1calgoenum/v1/proxy_type.proto\x1a\x1dalgoenum/v1/change_type.proto\x1a$algoenum/v1/data_transfer_type.proto\x1a algoenum/v1/location_level.proto\"\xa0\x01\n" +
	"1BackofficePlanServiceConfigPlanBackConnectRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x125\n" +
	"\x17id_back_connect_manager\x18\x02 \x01(\tR\x14idBackConnectManager\x12\x1b\n" +
	"\tis_enable\x18\x03 \x01(\bR\bisEnable\"c\n" +
	"2BackofficePlanServiceConfigPlanBackConnectResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x87\x01\n" +
	".BackofficePlanServiceConfigPlanLocationRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x1f\n" +
	"\vid_location\x18\x02 \x01(\tR\n" +
	"idLocation\x12\x1b\n" +
	"\tis_enable\x18\x03 \x01(\bR\bisEnable\"`\n" +
	"/BackofficePlanServiceConfigPlanLocationResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xee\x04\n" +
	"&BackofficePlanServiceCreatePlanRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12,\n" +
	"\aip_type\x18\x03 \x01(\x0e2\x13.algoenum.v1.IPTypeR\x06ipType\x125\n" +
	"\n" +
	"proxy_type\x18\x04 \x01(\x0e2\x16.algoenum.v1.ProxyTypeR\tproxyType\x128\n" +
	"\vchange_type\x18\x05 \x01(\x0e2\x17.algoenum.v1.ChangeTypeR\n" +
	"changeType\x12K\n" +
	"\x12data_transfer_type\x18\x06 \x01(\x0e2\x1d.algoenum.v1.DataTransferTypeR\x10dataTransferType\x12<\n" +
	"\x1bbandwidth_per_proxy_in_mbit\x18\a \x01(\x03R\x17bandwidthPerProxyInMbit\x12>\n" +
	"\x1dtime_to_live_per_proxy_in_sec\x18\b \x01(\x03R\x17timeToLivePerProxyInSec\x12B\n" +
	"\x1ftime_to_change_per_proxy_in_sec\x18\t \x01(\x03R\x19timeToChangePerProxyInSec\x12)\n" +
	"\x10concurrent_proxy\x18\n" +
	" \x01(\x03R\x0fconcurrentProxy\x12 \n" +
	"\vdescription\x18\v \x01(\tR\vdescription\x12\x14\n" +
	"\x05index\x18\f \x01(\x03R\x05index\"X\n" +
	"'BackofficePlanServiceCreatePlanResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xf3\x03\n" +
	"%BackofficePlanServiceFetchPlanRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12\x17\n" +
	"\aid_plan\x18\x02 \x01(\tR\x06idPlan\x12\x1f\n" +
	"\vid_location\x18\x03 \x01(\tR\n" +
	"idLocation\x12\x1f\n" +
	"\vname_search\x18\x04 \x01(\tR\n" +
	"nameSearch\x12,\n" +
	"\aip_type\x18\x05 \x01(\x0e2\x13.algoenum.v1.IPTypeR\x06ipType\x125\n" +
	"\n" +
	"proxy_type\x18\x06 \x01(\x0e2\x16.algoenum.v1.ProxyTypeR\tproxyType\x128\n" +
	"\vchange_type\x18\a \x01(\x0e2\x17.algoenum.v1.ChangeTypeR\n" +
	"changeType\x12K\n" +
	"\x12data_transfer_type\x18\b \x01(\x0e2\x1d.algoenum.v1.DataTransferTypeR\x10dataTransferType\x12%\n" +
	"\x05state\x18\t \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\n" +
	" \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xe2\x01\n" +
	"&BackofficePlanServiceFetchPlanResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12K\n" +
	"\x05plans\x18\x03 \x03(\v25.proxymanager.plan.v1.BackofficePlanServicePlanEntityR\x05plans\"\x8d\x05\n" +
	"&BackofficePlanServiceUpdatePlanRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12,\n" +
	"\aip_type\x18\x03 \x01(\x0e2\x13.algoenum.v1.IPTypeR\x06ipType\x125\n" +
	"\n" +
	"proxy_type\x18\x04 \x01(\x0e2\x16.algoenum.v1.ProxyTypeR\tproxyType\x128\n" +
	"\vchange_type\x18\x05 \x01(\x0e2\x17.algoenum.v1.ChangeTypeR\n" +
	"changeType\x12K\n" +
	"\x12data_transfer_type\x18\x06 \x01(\x0e2\x1d.algoenum.v1.DataTransferTypeR\x10dataTransferType\x12<\n" +
	"\x1bbandwidth_per_proxy_in_mbit\x18\a \x01(\x03R\x17bandwidthPerProxyInMbit\x12>\n" +
	"\x1dtime_to_live_per_proxy_in_sec\x18\b \x01(\x03R\x17timeToLivePerProxyInSec\x12B\n" +
	"\x1ftime_to_change_per_proxy_in_sec\x18\t \x01(\x03R\x19timeToChangePerProxyInSec\x12)\n" +
	"\x10concurrent_proxy\x18\n" +
	" \x01(\x03R\x0fconcurrentProxy\x12 \n" +
	"\vdescription\x18\v \x01(\tR\vdescription\x12\x14\n" +
	"\x05index\x18\f \x01(\x03R\x05index\x12%\n" +
	"\x05state\x18\r \x01(\v2\x0f.utils.v1.StateR\x05state\"X\n" +
	"'BackofficePlanServiceUpdatePlanResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xc4\a\n" +
	"\x1fBackofficePlanServicePlanEntity\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12Y\n" +
	"\bmerchant\x18\x03 \x01(\v2=.proxymanager.plan.v1.BackofficePlanServicePlanMerchantEntityR\bmerchant\x12U\n" +
	"\tlocations\x18\x04 \x03(\v27.proxymanager.plan.v1.BackofficePlanServicePlanLocationR\tlocations\x12,\n" +
	"\aip_type\x18\x05 \x01(\x0e2\x13.algoenum.v1.IPTypeR\x06ipType\x125\n" +
	"\n" +
	"proxy_type\x18\x06 \x01(\x0e2\x16.algoenum.v1.ProxyTypeR\tproxyType\x128\n" +
	"\vchange_type\x18\a \x01(\x0e2\x17.algoenum.v1.ChangeTypeR\n" +
	"changeType\x12K\n" +
	"\x12data_transfer_type\x18\b \x01(\x0e2\x1d.algoenum.v1.DataTransferTypeR\x10dataTransferType\x123\n" +
	"\x16data_transfer_in_gbyte\x18\t \x01(\x03R\x13dataTransferInGbyte\x12<\n" +
	"\x1bbandwidth_per_proxy_in_mbit\x18\n" +
	" \x01(\x03R\x17bandwidthPerProxyInMbit\x12>\n" +
	"\x1dtime_to_live_per_proxy_in_sec\x18\v \x01(\x03R\x17timeToLivePerProxyInSec\x12B\n" +
	"\x1ftime_to_change_per_proxy_in_sec\x18\f \x01(\x03R\x19timeToChangePerProxyInSec\x12)\n" +
	"\x10concurrent_proxy\x18\r \x01(\x03R\x0fconcurrentProxy\x12 \n" +
	"\vdescription\x18\x0e \x01(\tR\vdescription\x12\x14\n" +
	"\x05index\x18\x0f \x01(\x03R\x05index\x12\x1b\n" +
	"\tis_active\x18\x10 \x01(\bR\bisActive\x12_\n" +
	"\rback_connects\x18\x11 \x03(\v2:.proxymanager.plan.v1.BackofficePlanServicePlanBackConnectR\fbackConnects\"q\n" +
	"$BackofficePlanServicePlanBackConnect\x125\n" +
	"\x17id_back_connect_manager\x18\x01 \x01(\tR\x14idBackConnectManager\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"{\n" +
	"'BackofficePlanServicePlanMerchantEntity\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1b\n" +
	"\tis_active\x18\x03 \x01(\bR\bisActive\"\xb8\x01\n" +
	"!BackofficePlanServicePlanLocation\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12A\n" +
	"\x0elocation_level\x18\x03 \x01(\x0e2\x1a.algoenum.v1.LocationLevelR\rlocationLevel\x12\x1b\n" +
	"\tis_active\x18\x04 \x01(\bR\bisActive2\x89\x06\n" +
	"\x15BackofficePlanService\x12\x86\x01\n" +
	"\tFetchPlan\x12;.proxymanager.plan.v1.BackofficePlanServiceFetchPlanRequest\x1a<.proxymanager.plan.v1.BackofficePlanServiceFetchPlanResponse\x12\x89\x01\n" +
	"\n" +
	"CreatePlan\x12<.proxymanager.plan.v1.BackofficePlanServiceCreatePlanRequest\x1a=.proxymanager.plan.v1.BackofficePlanServiceCreatePlanResponse\x12\x89\x01\n" +
	"\n" +
	"UpdatePlan\x12<.proxymanager.plan.v1.BackofficePlanServiceUpdatePlanRequest\x1a=.proxymanager.plan.v1.BackofficePlanServiceUpdatePlanResponse\x12\xa1\x01\n" +
	"\x12ConfigPlanLocation\x12D.proxymanager.plan.v1.BackofficePlanServiceConfigPlanLocationRequest\x1aE.proxymanager.plan.v1.BackofficePlanServiceConfigPlanLocationResponse\x12\xaa\x01\n" +
	"\x15ConfigPlanBackConnect\x12G.proxymanager.plan.v1.BackofficePlanServiceConfigPlanBackConnectRequest\x1aH.proxymanager.plan.v1.BackofficePlanServiceConfigPlanBackConnectResponseBLZJgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/plan/v1;planv1b\x06proto3"

var (
	file_proxymanager_plan_v1_backoffice_proto_rawDescOnce sync.Once
	file_proxymanager_plan_v1_backoffice_proto_rawDescData []byte
)

func file_proxymanager_plan_v1_backoffice_proto_rawDescGZIP() []byte {
	file_proxymanager_plan_v1_backoffice_proto_rawDescOnce.Do(func() {
		file_proxymanager_plan_v1_backoffice_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_plan_v1_backoffice_proto_rawDesc), len(file_proxymanager_plan_v1_backoffice_proto_rawDesc)))
	})
	return file_proxymanager_plan_v1_backoffice_proto_rawDescData
}

var file_proxymanager_plan_v1_backoffice_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_proxymanager_plan_v1_backoffice_proto_goTypes = []any{
	(*BackofficePlanServiceConfigPlanBackConnectRequest)(nil),  // 0: proxymanager.plan.v1.BackofficePlanServiceConfigPlanBackConnectRequest
	(*BackofficePlanServiceConfigPlanBackConnectResponse)(nil), // 1: proxymanager.plan.v1.BackofficePlanServiceConfigPlanBackConnectResponse
	(*BackofficePlanServiceConfigPlanLocationRequest)(nil),     // 2: proxymanager.plan.v1.BackofficePlanServiceConfigPlanLocationRequest
	(*BackofficePlanServiceConfigPlanLocationResponse)(nil),    // 3: proxymanager.plan.v1.BackofficePlanServiceConfigPlanLocationResponse
	(*BackofficePlanServiceCreatePlanRequest)(nil),             // 4: proxymanager.plan.v1.BackofficePlanServiceCreatePlanRequest
	(*BackofficePlanServiceCreatePlanResponse)(nil),            // 5: proxymanager.plan.v1.BackofficePlanServiceCreatePlanResponse
	(*BackofficePlanServiceFetchPlanRequest)(nil),              // 6: proxymanager.plan.v1.BackofficePlanServiceFetchPlanRequest
	(*BackofficePlanServiceFetchPlanResponse)(nil),             // 7: proxymanager.plan.v1.BackofficePlanServiceFetchPlanResponse
	(*BackofficePlanServiceUpdatePlanRequest)(nil),             // 8: proxymanager.plan.v1.BackofficePlanServiceUpdatePlanRequest
	(*BackofficePlanServiceUpdatePlanResponse)(nil),            // 9: proxymanager.plan.v1.BackofficePlanServiceUpdatePlanResponse
	(*BackofficePlanServicePlanEntity)(nil),                    // 10: proxymanager.plan.v1.BackofficePlanServicePlanEntity
	(*BackofficePlanServicePlanBackConnect)(nil),               // 11: proxymanager.plan.v1.BackofficePlanServicePlanBackConnect
	(*BackofficePlanServicePlanMerchantEntity)(nil),            // 12: proxymanager.plan.v1.BackofficePlanServicePlanMerchantEntity
	(*BackofficePlanServicePlanLocation)(nil),                  // 13: proxymanager.plan.v1.BackofficePlanServicePlanLocation
	(*v1.ErrorMessage)(nil),                                    // 14: errmsg.v1.ErrorMessage
	(v11.IPType)(0),                                            // 15: algoenum.v1.IPType
	(v11.ProxyType)(0),                                         // 16: algoenum.v1.ProxyType
	(v11.ChangeType)(0),                                        // 17: algoenum.v1.ChangeType
	(v11.DataTransferType)(0),                                  // 18: algoenum.v1.DataTransferType
	(*v12.State)(nil),                                          // 19: utils.v1.State
	(*v12.PaginationRequest)(nil),                              // 20: utils.v1.PaginationRequest
	(*v12.PaginationResponse)(nil),                             // 21: utils.v1.PaginationResponse
	(v11.LocationLevel)(0),                                     // 22: algoenum.v1.LocationLevel
}
var file_proxymanager_plan_v1_backoffice_proto_depIdxs = []int32{
	14, // 0: proxymanager.plan.v1.BackofficePlanServiceConfigPlanBackConnectResponse.error:type_name -> errmsg.v1.ErrorMessage
	14, // 1: proxymanager.plan.v1.BackofficePlanServiceConfigPlanLocationResponse.error:type_name -> errmsg.v1.ErrorMessage
	15, // 2: proxymanager.plan.v1.BackofficePlanServiceCreatePlanRequest.ip_type:type_name -> algoenum.v1.IPType
	16, // 3: proxymanager.plan.v1.BackofficePlanServiceCreatePlanRequest.proxy_type:type_name -> algoenum.v1.ProxyType
	17, // 4: proxymanager.plan.v1.BackofficePlanServiceCreatePlanRequest.change_type:type_name -> algoenum.v1.ChangeType
	18, // 5: proxymanager.plan.v1.BackofficePlanServiceCreatePlanRequest.data_transfer_type:type_name -> algoenum.v1.DataTransferType
	14, // 6: proxymanager.plan.v1.BackofficePlanServiceCreatePlanResponse.error:type_name -> errmsg.v1.ErrorMessage
	15, // 7: proxymanager.plan.v1.BackofficePlanServiceFetchPlanRequest.ip_type:type_name -> algoenum.v1.IPType
	16, // 8: proxymanager.plan.v1.BackofficePlanServiceFetchPlanRequest.proxy_type:type_name -> algoenum.v1.ProxyType
	17, // 9: proxymanager.plan.v1.BackofficePlanServiceFetchPlanRequest.change_type:type_name -> algoenum.v1.ChangeType
	18, // 10: proxymanager.plan.v1.BackofficePlanServiceFetchPlanRequest.data_transfer_type:type_name -> algoenum.v1.DataTransferType
	19, // 11: proxymanager.plan.v1.BackofficePlanServiceFetchPlanRequest.state:type_name -> utils.v1.State
	20, // 12: proxymanager.plan.v1.BackofficePlanServiceFetchPlanRequest.pagination:type_name -> utils.v1.PaginationRequest
	14, // 13: proxymanager.plan.v1.BackofficePlanServiceFetchPlanResponse.error:type_name -> errmsg.v1.ErrorMessage
	21, // 14: proxymanager.plan.v1.BackofficePlanServiceFetchPlanResponse.pagination:type_name -> utils.v1.PaginationResponse
	10, // 15: proxymanager.plan.v1.BackofficePlanServiceFetchPlanResponse.plans:type_name -> proxymanager.plan.v1.BackofficePlanServicePlanEntity
	15, // 16: proxymanager.plan.v1.BackofficePlanServiceUpdatePlanRequest.ip_type:type_name -> algoenum.v1.IPType
	16, // 17: proxymanager.plan.v1.BackofficePlanServiceUpdatePlanRequest.proxy_type:type_name -> algoenum.v1.ProxyType
	17, // 18: proxymanager.plan.v1.BackofficePlanServiceUpdatePlanRequest.change_type:type_name -> algoenum.v1.ChangeType
	18, // 19: proxymanager.plan.v1.BackofficePlanServiceUpdatePlanRequest.data_transfer_type:type_name -> algoenum.v1.DataTransferType
	19, // 20: proxymanager.plan.v1.BackofficePlanServiceUpdatePlanRequest.state:type_name -> utils.v1.State
	14, // 21: proxymanager.plan.v1.BackofficePlanServiceUpdatePlanResponse.error:type_name -> errmsg.v1.ErrorMessage
	12, // 22: proxymanager.plan.v1.BackofficePlanServicePlanEntity.merchant:type_name -> proxymanager.plan.v1.BackofficePlanServicePlanMerchantEntity
	13, // 23: proxymanager.plan.v1.BackofficePlanServicePlanEntity.locations:type_name -> proxymanager.plan.v1.BackofficePlanServicePlanLocation
	15, // 24: proxymanager.plan.v1.BackofficePlanServicePlanEntity.ip_type:type_name -> algoenum.v1.IPType
	16, // 25: proxymanager.plan.v1.BackofficePlanServicePlanEntity.proxy_type:type_name -> algoenum.v1.ProxyType
	17, // 26: proxymanager.plan.v1.BackofficePlanServicePlanEntity.change_type:type_name -> algoenum.v1.ChangeType
	18, // 27: proxymanager.plan.v1.BackofficePlanServicePlanEntity.data_transfer_type:type_name -> algoenum.v1.DataTransferType
	11, // 28: proxymanager.plan.v1.BackofficePlanServicePlanEntity.back_connects:type_name -> proxymanager.plan.v1.BackofficePlanServicePlanBackConnect
	22, // 29: proxymanager.plan.v1.BackofficePlanServicePlanLocation.location_level:type_name -> algoenum.v1.LocationLevel
	6,  // 30: proxymanager.plan.v1.BackofficePlanService.FetchPlan:input_type -> proxymanager.plan.v1.BackofficePlanServiceFetchPlanRequest
	4,  // 31: proxymanager.plan.v1.BackofficePlanService.CreatePlan:input_type -> proxymanager.plan.v1.BackofficePlanServiceCreatePlanRequest
	8,  // 32: proxymanager.plan.v1.BackofficePlanService.UpdatePlan:input_type -> proxymanager.plan.v1.BackofficePlanServiceUpdatePlanRequest
	2,  // 33: proxymanager.plan.v1.BackofficePlanService.ConfigPlanLocation:input_type -> proxymanager.plan.v1.BackofficePlanServiceConfigPlanLocationRequest
	0,  // 34: proxymanager.plan.v1.BackofficePlanService.ConfigPlanBackConnect:input_type -> proxymanager.plan.v1.BackofficePlanServiceConfigPlanBackConnectRequest
	7,  // 35: proxymanager.plan.v1.BackofficePlanService.FetchPlan:output_type -> proxymanager.plan.v1.BackofficePlanServiceFetchPlanResponse
	5,  // 36: proxymanager.plan.v1.BackofficePlanService.CreatePlan:output_type -> proxymanager.plan.v1.BackofficePlanServiceCreatePlanResponse
	9,  // 37: proxymanager.plan.v1.BackofficePlanService.UpdatePlan:output_type -> proxymanager.plan.v1.BackofficePlanServiceUpdatePlanResponse
	3,  // 38: proxymanager.plan.v1.BackofficePlanService.ConfigPlanLocation:output_type -> proxymanager.plan.v1.BackofficePlanServiceConfigPlanLocationResponse
	1,  // 39: proxymanager.plan.v1.BackofficePlanService.ConfigPlanBackConnect:output_type -> proxymanager.plan.v1.BackofficePlanServiceConfigPlanBackConnectResponse
	35, // [35:40] is the sub-list for method output_type
	30, // [30:35] is the sub-list for method input_type
	30, // [30:30] is the sub-list for extension type_name
	30, // [30:30] is the sub-list for extension extendee
	0,  // [0:30] is the sub-list for field type_name
}

func init() { file_proxymanager_plan_v1_backoffice_proto_init() }
func file_proxymanager_plan_v1_backoffice_proto_init() {
	if File_proxymanager_plan_v1_backoffice_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_plan_v1_backoffice_proto_rawDesc), len(file_proxymanager_plan_v1_backoffice_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_plan_v1_backoffice_proto_goTypes,
		DependencyIndexes: file_proxymanager_plan_v1_backoffice_proto_depIdxs,
		MessageInfos:      file_proxymanager_plan_v1_backoffice_proto_msgTypes,
	}.Build()
	File_proxymanager_plan_v1_backoffice_proto = out.File
	file_proxymanager_plan_v1_backoffice_proto_goTypes = nil
	file_proxymanager_plan_v1_backoffice_proto_depIdxs = nil
}
