// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/plan/v1/merchant.proto

package planv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MerchantPlanServiceFetchPlanRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	IdPlan           string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdLocation       string                 `protobuf:"bytes,2,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	NameSearch       string                 `protobuf:"bytes,3,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	IpType           v1.IPType              `protobuf:"varint,4,opt,name=ip_type,json=ipType,proto3,enum=algoenum.v1.IPType" json:"ip_type,omitempty"`
	ProxyType        v1.ProxyType           `protobuf:"varint,5,opt,name=proxy_type,json=proxyType,proto3,enum=algoenum.v1.ProxyType" json:"proxy_type,omitempty"`
	ChangeType       v1.ChangeType          `protobuf:"varint,6,opt,name=change_type,json=changeType,proto3,enum=algoenum.v1.ChangeType" json:"change_type,omitempty"`
	DataTransferType v1.DataTransferType    `protobuf:"varint,7,opt,name=data_transfer_type,json=dataTransferType,proto3,enum=algoenum.v1.DataTransferType" json:"data_transfer_type,omitempty"`
	Pagination       *v11.PaginationRequest `protobuf:"bytes,8,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *MerchantPlanServiceFetchPlanRequest) Reset() {
	*x = MerchantPlanServiceFetchPlanRequest{}
	mi := &file_proxymanager_plan_v1_merchant_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPlanServiceFetchPlanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPlanServiceFetchPlanRequest) ProtoMessage() {}

func (x *MerchantPlanServiceFetchPlanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_merchant_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPlanServiceFetchPlanRequest.ProtoReflect.Descriptor instead.
func (*MerchantPlanServiceFetchPlanRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_merchant_proto_rawDescGZIP(), []int{0}
}

func (x *MerchantPlanServiceFetchPlanRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *MerchantPlanServiceFetchPlanRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *MerchantPlanServiceFetchPlanRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *MerchantPlanServiceFetchPlanRequest) GetIpType() v1.IPType {
	if x != nil {
		return x.IpType
	}
	return v1.IPType(0)
}

func (x *MerchantPlanServiceFetchPlanRequest) GetProxyType() v1.ProxyType {
	if x != nil {
		return x.ProxyType
	}
	return v1.ProxyType(0)
}

func (x *MerchantPlanServiceFetchPlanRequest) GetChangeType() v1.ChangeType {
	if x != nil {
		return x.ChangeType
	}
	return v1.ChangeType(0)
}

func (x *MerchantPlanServiceFetchPlanRequest) GetDataTransferType() v1.DataTransferType {
	if x != nil {
		return x.DataTransferType
	}
	return v1.DataTransferType(0)
}

func (x *MerchantPlanServiceFetchPlanRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantPlanServiceFetchPlanResponse struct {
	state         protoimpl.MessageState           `protogen:"open.v1"`
	Error         *v12.ErrorMessage                `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse          `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Plans         []*MerchantPlanServicePlanEntity `protobuf:"bytes,3,rep,name=plans,proto3" json:"plans,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantPlanServiceFetchPlanResponse) Reset() {
	*x = MerchantPlanServiceFetchPlanResponse{}
	mi := &file_proxymanager_plan_v1_merchant_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPlanServiceFetchPlanResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPlanServiceFetchPlanResponse) ProtoMessage() {}

func (x *MerchantPlanServiceFetchPlanResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_merchant_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPlanServiceFetchPlanResponse.ProtoReflect.Descriptor instead.
func (*MerchantPlanServiceFetchPlanResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_merchant_proto_rawDescGZIP(), []int{1}
}

func (x *MerchantPlanServiceFetchPlanResponse) GetError() *v12.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantPlanServiceFetchPlanResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *MerchantPlanServiceFetchPlanResponse) GetPlans() []*MerchantPlanServicePlanEntity {
	if x != nil {
		return x.Plans
	}
	return nil
}

type MerchantPlanServicePlanEntity struct {
	state                     protoimpl.MessageState             `protogen:"open.v1"`
	IdPlan                    string                             `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	Name                      string                             `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Locations                 []*MerchantPlanServicePlanLocation `protobuf:"bytes,3,rep,name=locations,proto3" json:"locations,omitempty"`
	IpType                    v1.IPType                          `protobuf:"varint,4,opt,name=ip_type,json=ipType,proto3,enum=algoenum.v1.IPType" json:"ip_type,omitempty"`
	ProxyType                 v1.ProxyType                       `protobuf:"varint,5,opt,name=proxy_type,json=proxyType,proto3,enum=algoenum.v1.ProxyType" json:"proxy_type,omitempty"`
	ChangeType                v1.ChangeType                      `protobuf:"varint,6,opt,name=change_type,json=changeType,proto3,enum=algoenum.v1.ChangeType" json:"change_type,omitempty"`
	DataTransferType          v1.DataTransferType                `protobuf:"varint,7,opt,name=data_transfer_type,json=dataTransferType,proto3,enum=algoenum.v1.DataTransferType" json:"data_transfer_type,omitempty"`
	DataTransferInGbyte       int64                              `protobuf:"varint,8,opt,name=data_transfer_in_gbyte,json=dataTransferInGbyte,proto3" json:"data_transfer_in_gbyte,omitempty"`
	BandwidthPerProxyInMbit   int64                              `protobuf:"varint,9,opt,name=bandwidth_per_proxy_in_mbit,json=bandwidthPerProxyInMbit,proto3" json:"bandwidth_per_proxy_in_mbit,omitempty"`
	TimeToLivePerProxyInSec   int64                              `protobuf:"varint,10,opt,name=time_to_live_per_proxy_in_sec,json=timeToLivePerProxyInSec,proto3" json:"time_to_live_per_proxy_in_sec,omitempty"`
	TimeToChangePerProxyInSec int64                              `protobuf:"varint,11,opt,name=time_to_change_per_proxy_in_sec,json=timeToChangePerProxyInSec,proto3" json:"time_to_change_per_proxy_in_sec,omitempty"`
	ConcurrentProxy           int64                              `protobuf:"varint,12,opt,name=concurrent_proxy,json=concurrentProxy,proto3" json:"concurrent_proxy,omitempty"`
	Description               string                             `protobuf:"bytes,13,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *MerchantPlanServicePlanEntity) Reset() {
	*x = MerchantPlanServicePlanEntity{}
	mi := &file_proxymanager_plan_v1_merchant_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPlanServicePlanEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPlanServicePlanEntity) ProtoMessage() {}

func (x *MerchantPlanServicePlanEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_merchant_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPlanServicePlanEntity.ProtoReflect.Descriptor instead.
func (*MerchantPlanServicePlanEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_merchant_proto_rawDescGZIP(), []int{2}
}

func (x *MerchantPlanServicePlanEntity) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *MerchantPlanServicePlanEntity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MerchantPlanServicePlanEntity) GetLocations() []*MerchantPlanServicePlanLocation {
	if x != nil {
		return x.Locations
	}
	return nil
}

func (x *MerchantPlanServicePlanEntity) GetIpType() v1.IPType {
	if x != nil {
		return x.IpType
	}
	return v1.IPType(0)
}

func (x *MerchantPlanServicePlanEntity) GetProxyType() v1.ProxyType {
	if x != nil {
		return x.ProxyType
	}
	return v1.ProxyType(0)
}

func (x *MerchantPlanServicePlanEntity) GetChangeType() v1.ChangeType {
	if x != nil {
		return x.ChangeType
	}
	return v1.ChangeType(0)
}

func (x *MerchantPlanServicePlanEntity) GetDataTransferType() v1.DataTransferType {
	if x != nil {
		return x.DataTransferType
	}
	return v1.DataTransferType(0)
}

func (x *MerchantPlanServicePlanEntity) GetDataTransferInGbyte() int64 {
	if x != nil {
		return x.DataTransferInGbyte
	}
	return 0
}

func (x *MerchantPlanServicePlanEntity) GetBandwidthPerProxyInMbit() int64 {
	if x != nil {
		return x.BandwidthPerProxyInMbit
	}
	return 0
}

func (x *MerchantPlanServicePlanEntity) GetTimeToLivePerProxyInSec() int64 {
	if x != nil {
		return x.TimeToLivePerProxyInSec
	}
	return 0
}

func (x *MerchantPlanServicePlanEntity) GetTimeToChangePerProxyInSec() int64 {
	if x != nil {
		return x.TimeToChangePerProxyInSec
	}
	return 0
}

func (x *MerchantPlanServicePlanEntity) GetConcurrentProxy() int64 {
	if x != nil {
		return x.ConcurrentProxy
	}
	return 0
}

func (x *MerchantPlanServicePlanEntity) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type MerchantPlanServicePlanLocation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdLocation    string                 `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	LocationLevel v1.LocationLevel       `protobuf:"varint,3,opt,name=location_level,json=locationLevel,proto3,enum=algoenum.v1.LocationLevel" json:"location_level,omitempty"`
	IsActive      bool                   `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantPlanServicePlanLocation) Reset() {
	*x = MerchantPlanServicePlanLocation{}
	mi := &file_proxymanager_plan_v1_merchant_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantPlanServicePlanLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantPlanServicePlanLocation) ProtoMessage() {}

func (x *MerchantPlanServicePlanLocation) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_plan_v1_merchant_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantPlanServicePlanLocation.ProtoReflect.Descriptor instead.
func (*MerchantPlanServicePlanLocation) Descriptor() ([]byte, []int) {
	return file_proxymanager_plan_v1_merchant_proto_rawDescGZIP(), []int{3}
}

func (x *MerchantPlanServicePlanLocation) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *MerchantPlanServicePlanLocation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MerchantPlanServicePlanLocation) GetLocationLevel() v1.LocationLevel {
	if x != nil {
		return x.LocationLevel
	}
	return v1.LocationLevel(0)
}

func (x *MerchantPlanServicePlanLocation) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

var File_proxymanager_plan_v1_merchant_proto protoreflect.FileDescriptor

const file_proxymanager_plan_v1_merchant_proto_rawDesc = "" +
	"\n" +
	"#proxymanager/plan/v1/merchant.proto\x12\x14proxymanager.plan.v1\x1a\x19algoenum/v1/ip_type.proto\x1a\x1calgoenum/v1/proxy_type.proto\x1a\x1dalgoenum/v1/change_type.proto\x1a$algoenum/v1/data_transfer_type.proto\x1a algoenum/v1/location_level.proto\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\"\xa9\x03\n" +
	"#MerchantPlanServiceFetchPlanRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x1f\n" +
	"\vid_location\x18\x02 \x01(\tR\n" +
	"idLocation\x12\x1f\n" +
	"\vname_search\x18\x03 \x01(\tR\n" +
	"nameSearch\x12,\n" +
	"\aip_type\x18\x04 \x01(\x0e2\x13.algoenum.v1.IPTypeR\x06ipType\x125\n" +
	"\n" +
	"proxy_type\x18\x05 \x01(\x0e2\x16.algoenum.v1.ProxyTypeR\tproxyType\x128\n" +
	"\vchange_type\x18\x06 \x01(\x0e2\x17.algoenum.v1.ChangeTypeR\n" +
	"changeType\x12K\n" +
	"\x12data_transfer_type\x18\a \x01(\x0e2\x1d.algoenum.v1.DataTransferTypeR\x10dataTransferType\x12;\n" +
	"\n" +
	"pagination\x18\b \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xde\x01\n" +
	"$MerchantPlanServiceFetchPlanResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12I\n" +
	"\x05plans\x18\x03 \x03(\v23.proxymanager.plan.v1.MerchantPlanServicePlanEntityR\x05plans\"\xd1\x05\n" +
	"\x1dMerchantPlanServicePlanEntity\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12S\n" +
	"\tlocations\x18\x03 \x03(\v25.proxymanager.plan.v1.MerchantPlanServicePlanLocationR\tlocations\x12,\n" +
	"\aip_type\x18\x04 \x01(\x0e2\x13.algoenum.v1.IPTypeR\x06ipType\x125\n" +
	"\n" +
	"proxy_type\x18\x05 \x01(\x0e2\x16.algoenum.v1.ProxyTypeR\tproxyType\x128\n" +
	"\vchange_type\x18\x06 \x01(\x0e2\x17.algoenum.v1.ChangeTypeR\n" +
	"changeType\x12K\n" +
	"\x12data_transfer_type\x18\a \x01(\x0e2\x1d.algoenum.v1.DataTransferTypeR\x10dataTransferType\x123\n" +
	"\x16data_transfer_in_gbyte\x18\b \x01(\x03R\x13dataTransferInGbyte\x12<\n" +
	"\x1bbandwidth_per_proxy_in_mbit\x18\t \x01(\x03R\x17bandwidthPerProxyInMbit\x12>\n" +
	"\x1dtime_to_live_per_proxy_in_sec\x18\n" +
	" \x01(\x03R\x17timeToLivePerProxyInSec\x12B\n" +
	"\x1ftime_to_change_per_proxy_in_sec\x18\v \x01(\x03R\x19timeToChangePerProxyInSec\x12)\n" +
	"\x10concurrent_proxy\x18\f \x01(\x03R\x0fconcurrentProxy\x12 \n" +
	"\vdescription\x18\r \x01(\tR\vdescription\"\xb6\x01\n" +
	"\x1fMerchantPlanServicePlanLocation\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12A\n" +
	"\x0elocation_level\x18\x03 \x01(\x0e2\x1a.algoenum.v1.LocationLevelR\rlocationLevel\x12\x1b\n" +
	"\tis_active\x18\x04 \x01(\bR\bisActive2\x9a\x01\n" +
	"\x13MerchantPlanService\x12\x82\x01\n" +
	"\tFetchPlan\x129.proxymanager.plan.v1.MerchantPlanServiceFetchPlanRequest\x1a:.proxymanager.plan.v1.MerchantPlanServiceFetchPlanResponseBLZJgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/plan/v1;planv1b\x06proto3"

var (
	file_proxymanager_plan_v1_merchant_proto_rawDescOnce sync.Once
	file_proxymanager_plan_v1_merchant_proto_rawDescData []byte
)

func file_proxymanager_plan_v1_merchant_proto_rawDescGZIP() []byte {
	file_proxymanager_plan_v1_merchant_proto_rawDescOnce.Do(func() {
		file_proxymanager_plan_v1_merchant_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_plan_v1_merchant_proto_rawDesc), len(file_proxymanager_plan_v1_merchant_proto_rawDesc)))
	})
	return file_proxymanager_plan_v1_merchant_proto_rawDescData
}

var file_proxymanager_plan_v1_merchant_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_proxymanager_plan_v1_merchant_proto_goTypes = []any{
	(*MerchantPlanServiceFetchPlanRequest)(nil),  // 0: proxymanager.plan.v1.MerchantPlanServiceFetchPlanRequest
	(*MerchantPlanServiceFetchPlanResponse)(nil), // 1: proxymanager.plan.v1.MerchantPlanServiceFetchPlanResponse
	(*MerchantPlanServicePlanEntity)(nil),        // 2: proxymanager.plan.v1.MerchantPlanServicePlanEntity
	(*MerchantPlanServicePlanLocation)(nil),      // 3: proxymanager.plan.v1.MerchantPlanServicePlanLocation
	(v1.IPType)(0),                               // 4: algoenum.v1.IPType
	(v1.ProxyType)(0),                            // 5: algoenum.v1.ProxyType
	(v1.ChangeType)(0),                           // 6: algoenum.v1.ChangeType
	(v1.DataTransferType)(0),                     // 7: algoenum.v1.DataTransferType
	(*v11.PaginationRequest)(nil),                // 8: utils.v1.PaginationRequest
	(*v12.ErrorMessage)(nil),                     // 9: errmsg.v1.ErrorMessage
	(*v11.PaginationResponse)(nil),               // 10: utils.v1.PaginationResponse
	(v1.LocationLevel)(0),                        // 11: algoenum.v1.LocationLevel
}
var file_proxymanager_plan_v1_merchant_proto_depIdxs = []int32{
	4,  // 0: proxymanager.plan.v1.MerchantPlanServiceFetchPlanRequest.ip_type:type_name -> algoenum.v1.IPType
	5,  // 1: proxymanager.plan.v1.MerchantPlanServiceFetchPlanRequest.proxy_type:type_name -> algoenum.v1.ProxyType
	6,  // 2: proxymanager.plan.v1.MerchantPlanServiceFetchPlanRequest.change_type:type_name -> algoenum.v1.ChangeType
	7,  // 3: proxymanager.plan.v1.MerchantPlanServiceFetchPlanRequest.data_transfer_type:type_name -> algoenum.v1.DataTransferType
	8,  // 4: proxymanager.plan.v1.MerchantPlanServiceFetchPlanRequest.pagination:type_name -> utils.v1.PaginationRequest
	9,  // 5: proxymanager.plan.v1.MerchantPlanServiceFetchPlanResponse.error:type_name -> errmsg.v1.ErrorMessage
	10, // 6: proxymanager.plan.v1.MerchantPlanServiceFetchPlanResponse.pagination:type_name -> utils.v1.PaginationResponse
	2,  // 7: proxymanager.plan.v1.MerchantPlanServiceFetchPlanResponse.plans:type_name -> proxymanager.plan.v1.MerchantPlanServicePlanEntity
	3,  // 8: proxymanager.plan.v1.MerchantPlanServicePlanEntity.locations:type_name -> proxymanager.plan.v1.MerchantPlanServicePlanLocation
	4,  // 9: proxymanager.plan.v1.MerchantPlanServicePlanEntity.ip_type:type_name -> algoenum.v1.IPType
	5,  // 10: proxymanager.plan.v1.MerchantPlanServicePlanEntity.proxy_type:type_name -> algoenum.v1.ProxyType
	6,  // 11: proxymanager.plan.v1.MerchantPlanServicePlanEntity.change_type:type_name -> algoenum.v1.ChangeType
	7,  // 12: proxymanager.plan.v1.MerchantPlanServicePlanEntity.data_transfer_type:type_name -> algoenum.v1.DataTransferType
	11, // 13: proxymanager.plan.v1.MerchantPlanServicePlanLocation.location_level:type_name -> algoenum.v1.LocationLevel
	0,  // 14: proxymanager.plan.v1.MerchantPlanService.FetchPlan:input_type -> proxymanager.plan.v1.MerchantPlanServiceFetchPlanRequest
	1,  // 15: proxymanager.plan.v1.MerchantPlanService.FetchPlan:output_type -> proxymanager.plan.v1.MerchantPlanServiceFetchPlanResponse
	15, // [15:16] is the sub-list for method output_type
	14, // [14:15] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_proxymanager_plan_v1_merchant_proto_init() }
func file_proxymanager_plan_v1_merchant_proto_init() {
	if File_proxymanager_plan_v1_merchant_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_plan_v1_merchant_proto_rawDesc), len(file_proxymanager_plan_v1_merchant_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_plan_v1_merchant_proto_goTypes,
		DependencyIndexes: file_proxymanager_plan_v1_merchant_proto_depIdxs,
		MessageInfos:      file_proxymanager_plan_v1_merchant_proto_msgTypes,
	}.Build()
	File_proxymanager_plan_v1_merchant_proto = out.File
	file_proxymanager_plan_v1_merchant_proto_goTypes = nil
	file_proxymanager_plan_v1_merchant_proto_depIdxs = nil
}
