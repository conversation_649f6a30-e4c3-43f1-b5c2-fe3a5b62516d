// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/plan/v1/merchant.proto

package planv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/plan/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// MerchantPlanServiceName is the fully-qualified name of the MerchantPlanService service.
	MerchantPlanServiceName = "proxymanager.plan.v1.MerchantPlanService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// MerchantPlanServiceFetchPlanProcedure is the fully-qualified name of the MerchantPlanService's
	// FetchPlan RPC.
	MerchantPlanServiceFetchPlanProcedure = "/proxymanager.plan.v1.MerchantPlanService/FetchPlan"
)

// MerchantPlanServiceClient is a client for the proxymanager.plan.v1.MerchantPlanService service.
type MerchantPlanServiceClient interface {
	FetchPlan(context.Context, *connect.Request[v1.MerchantPlanServiceFetchPlanRequest]) (*connect.Response[v1.MerchantPlanServiceFetchPlanResponse], error)
}

// NewMerchantPlanServiceClient constructs a client for the proxymanager.plan.v1.MerchantPlanService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewMerchantPlanServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) MerchantPlanServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	merchantPlanServiceMethods := v1.File_proxymanager_plan_v1_merchant_proto.Services().ByName("MerchantPlanService").Methods()
	return &merchantPlanServiceClient{
		fetchPlan: connect.NewClient[v1.MerchantPlanServiceFetchPlanRequest, v1.MerchantPlanServiceFetchPlanResponse](
			httpClient,
			baseURL+MerchantPlanServiceFetchPlanProcedure,
			connect.WithSchema(merchantPlanServiceMethods.ByName("FetchPlan")),
			connect.WithClientOptions(opts...),
		),
	}
}

// merchantPlanServiceClient implements MerchantPlanServiceClient.
type merchantPlanServiceClient struct {
	fetchPlan *connect.Client[v1.MerchantPlanServiceFetchPlanRequest, v1.MerchantPlanServiceFetchPlanResponse]
}

// FetchPlan calls proxymanager.plan.v1.MerchantPlanService.FetchPlan.
func (c *merchantPlanServiceClient) FetchPlan(ctx context.Context, req *connect.Request[v1.MerchantPlanServiceFetchPlanRequest]) (*connect.Response[v1.MerchantPlanServiceFetchPlanResponse], error) {
	return c.fetchPlan.CallUnary(ctx, req)
}

// MerchantPlanServiceHandler is an implementation of the proxymanager.plan.v1.MerchantPlanService
// service.
type MerchantPlanServiceHandler interface {
	FetchPlan(context.Context, *connect.Request[v1.MerchantPlanServiceFetchPlanRequest]) (*connect.Response[v1.MerchantPlanServiceFetchPlanResponse], error)
}

// NewMerchantPlanServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewMerchantPlanServiceHandler(svc MerchantPlanServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	merchantPlanServiceMethods := v1.File_proxymanager_plan_v1_merchant_proto.Services().ByName("MerchantPlanService").Methods()
	merchantPlanServiceFetchPlanHandler := connect.NewUnaryHandler(
		MerchantPlanServiceFetchPlanProcedure,
		svc.FetchPlan,
		connect.WithSchema(merchantPlanServiceMethods.ByName("FetchPlan")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.plan.v1.MerchantPlanService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case MerchantPlanServiceFetchPlanProcedure:
			merchantPlanServiceFetchPlanHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedMerchantPlanServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedMerchantPlanServiceHandler struct{}

func (UnimplementedMerchantPlanServiceHandler) FetchPlan(context.Context, *connect.Request[v1.MerchantPlanServiceFetchPlanRequest]) (*connect.Response[v1.MerchantPlanServiceFetchPlanResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.plan.v1.MerchantPlanService.FetchPlan is not implemented"))
}
