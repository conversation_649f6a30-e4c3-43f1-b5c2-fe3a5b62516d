// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/plan/v1/internal.proto

package planv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/plan/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// InternalPlanServiceName is the fully-qualified name of the InternalPlanService service.
	InternalPlanServiceName = "proxymanager.plan.v1.InternalPlanService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// InternalPlanServiceFetchPlanProcedure is the fully-qualified name of the InternalPlanService's
	// FetchPlan RPC.
	InternalPlanServiceFetchPlanProcedure = "/proxymanager.plan.v1.InternalPlanService/FetchPlan"
)

// InternalPlanServiceClient is a client for the proxymanager.plan.v1.InternalPlanService service.
type InternalPlanServiceClient interface {
	FetchPlan(context.Context, *connect.Request[v1.InternalPlanServiceFetchPlanRequest]) (*connect.Response[v1.InternalPlanServiceFetchPlanResponse], error)
}

// NewInternalPlanServiceClient constructs a client for the proxymanager.plan.v1.InternalPlanService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewInternalPlanServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) InternalPlanServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	internalPlanServiceMethods := v1.File_proxymanager_plan_v1_internal_proto.Services().ByName("InternalPlanService").Methods()
	return &internalPlanServiceClient{
		fetchPlan: connect.NewClient[v1.InternalPlanServiceFetchPlanRequest, v1.InternalPlanServiceFetchPlanResponse](
			httpClient,
			baseURL+InternalPlanServiceFetchPlanProcedure,
			connect.WithSchema(internalPlanServiceMethods.ByName("FetchPlan")),
			connect.WithClientOptions(opts...),
		),
	}
}

// internalPlanServiceClient implements InternalPlanServiceClient.
type internalPlanServiceClient struct {
	fetchPlan *connect.Client[v1.InternalPlanServiceFetchPlanRequest, v1.InternalPlanServiceFetchPlanResponse]
}

// FetchPlan calls proxymanager.plan.v1.InternalPlanService.FetchPlan.
func (c *internalPlanServiceClient) FetchPlan(ctx context.Context, req *connect.Request[v1.InternalPlanServiceFetchPlanRequest]) (*connect.Response[v1.InternalPlanServiceFetchPlanResponse], error) {
	return c.fetchPlan.CallUnary(ctx, req)
}

// InternalPlanServiceHandler is an implementation of the proxymanager.plan.v1.InternalPlanService
// service.
type InternalPlanServiceHandler interface {
	FetchPlan(context.Context, *connect.Request[v1.InternalPlanServiceFetchPlanRequest]) (*connect.Response[v1.InternalPlanServiceFetchPlanResponse], error)
}

// NewInternalPlanServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewInternalPlanServiceHandler(svc InternalPlanServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	internalPlanServiceMethods := v1.File_proxymanager_plan_v1_internal_proto.Services().ByName("InternalPlanService").Methods()
	internalPlanServiceFetchPlanHandler := connect.NewUnaryHandler(
		InternalPlanServiceFetchPlanProcedure,
		svc.FetchPlan,
		connect.WithSchema(internalPlanServiceMethods.ByName("FetchPlan")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.plan.v1.InternalPlanService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case InternalPlanServiceFetchPlanProcedure:
			internalPlanServiceFetchPlanHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedInternalPlanServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedInternalPlanServiceHandler struct{}

func (UnimplementedInternalPlanServiceHandler) FetchPlan(context.Context, *connect.Request[v1.InternalPlanServiceFetchPlanRequest]) (*connect.Response[v1.InternalPlanServiceFetchPlanResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.plan.v1.InternalPlanService.FetchPlan is not implemented"))
}
