// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/plan/v1/customer.proto

package planv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/plan/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CustomerPlanServiceName is the fully-qualified name of the CustomerPlanService service.
	CustomerPlanServiceName = "proxymanager.plan.v1.CustomerPlanService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CustomerPlanServiceFetchAvailableLocationProcedure is the fully-qualified name of the
	// CustomerPlanService's FetchAvailableLocation RPC.
	CustomerPlanServiceFetchAvailableLocationProcedure = "/proxymanager.plan.v1.CustomerPlanService/FetchAvailableLocation"
	// CustomerPlanServiceFetchPlanProcedure is the fully-qualified name of the CustomerPlanService's
	// FetchPlan RPC.
	CustomerPlanServiceFetchPlanProcedure = "/proxymanager.plan.v1.CustomerPlanService/FetchPlan"
)

// CustomerPlanServiceClient is a client for the proxymanager.plan.v1.CustomerPlanService service.
type CustomerPlanServiceClient interface {
	FetchAvailableLocation(context.Context, *connect.Request[v1.CustomerPlanServiceFetchAvailableLocationRequest]) (*connect.Response[v1.CustomerPlanServiceFetchAvailableLocationResponse], error)
	FetchPlan(context.Context, *connect.Request[v1.CustomerPlanServiceFetchPlanRequest]) (*connect.Response[v1.CustomerPlanServiceFetchPlanResponse], error)
}

// NewCustomerPlanServiceClient constructs a client for the proxymanager.plan.v1.CustomerPlanService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCustomerPlanServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CustomerPlanServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	customerPlanServiceMethods := v1.File_proxymanager_plan_v1_customer_proto.Services().ByName("CustomerPlanService").Methods()
	return &customerPlanServiceClient{
		fetchAvailableLocation: connect.NewClient[v1.CustomerPlanServiceFetchAvailableLocationRequest, v1.CustomerPlanServiceFetchAvailableLocationResponse](
			httpClient,
			baseURL+CustomerPlanServiceFetchAvailableLocationProcedure,
			connect.WithSchema(customerPlanServiceMethods.ByName("FetchAvailableLocation")),
			connect.WithClientOptions(opts...),
		),
		fetchPlan: connect.NewClient[v1.CustomerPlanServiceFetchPlanRequest, v1.CustomerPlanServiceFetchPlanResponse](
			httpClient,
			baseURL+CustomerPlanServiceFetchPlanProcedure,
			connect.WithSchema(customerPlanServiceMethods.ByName("FetchPlan")),
			connect.WithClientOptions(opts...),
		),
	}
}

// customerPlanServiceClient implements CustomerPlanServiceClient.
type customerPlanServiceClient struct {
	fetchAvailableLocation *connect.Client[v1.CustomerPlanServiceFetchAvailableLocationRequest, v1.CustomerPlanServiceFetchAvailableLocationResponse]
	fetchPlan              *connect.Client[v1.CustomerPlanServiceFetchPlanRequest, v1.CustomerPlanServiceFetchPlanResponse]
}

// FetchAvailableLocation calls proxymanager.plan.v1.CustomerPlanService.FetchAvailableLocation.
func (c *customerPlanServiceClient) FetchAvailableLocation(ctx context.Context, req *connect.Request[v1.CustomerPlanServiceFetchAvailableLocationRequest]) (*connect.Response[v1.CustomerPlanServiceFetchAvailableLocationResponse], error) {
	return c.fetchAvailableLocation.CallUnary(ctx, req)
}

// FetchPlan calls proxymanager.plan.v1.CustomerPlanService.FetchPlan.
func (c *customerPlanServiceClient) FetchPlan(ctx context.Context, req *connect.Request[v1.CustomerPlanServiceFetchPlanRequest]) (*connect.Response[v1.CustomerPlanServiceFetchPlanResponse], error) {
	return c.fetchPlan.CallUnary(ctx, req)
}

// CustomerPlanServiceHandler is an implementation of the proxymanager.plan.v1.CustomerPlanService
// service.
type CustomerPlanServiceHandler interface {
	FetchAvailableLocation(context.Context, *connect.Request[v1.CustomerPlanServiceFetchAvailableLocationRequest]) (*connect.Response[v1.CustomerPlanServiceFetchAvailableLocationResponse], error)
	FetchPlan(context.Context, *connect.Request[v1.CustomerPlanServiceFetchPlanRequest]) (*connect.Response[v1.CustomerPlanServiceFetchPlanResponse], error)
}

// NewCustomerPlanServiceHandler builds an HTTP handler from the service implementation. It returns
// the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCustomerPlanServiceHandler(svc CustomerPlanServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	customerPlanServiceMethods := v1.File_proxymanager_plan_v1_customer_proto.Services().ByName("CustomerPlanService").Methods()
	customerPlanServiceFetchAvailableLocationHandler := connect.NewUnaryHandler(
		CustomerPlanServiceFetchAvailableLocationProcedure,
		svc.FetchAvailableLocation,
		connect.WithSchema(customerPlanServiceMethods.ByName("FetchAvailableLocation")),
		connect.WithHandlerOptions(opts...),
	)
	customerPlanServiceFetchPlanHandler := connect.NewUnaryHandler(
		CustomerPlanServiceFetchPlanProcedure,
		svc.FetchPlan,
		connect.WithSchema(customerPlanServiceMethods.ByName("FetchPlan")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.plan.v1.CustomerPlanService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CustomerPlanServiceFetchAvailableLocationProcedure:
			customerPlanServiceFetchAvailableLocationHandler.ServeHTTP(w, r)
		case CustomerPlanServiceFetchPlanProcedure:
			customerPlanServiceFetchPlanHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCustomerPlanServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCustomerPlanServiceHandler struct{}

func (UnimplementedCustomerPlanServiceHandler) FetchAvailableLocation(context.Context, *connect.Request[v1.CustomerPlanServiceFetchAvailableLocationRequest]) (*connect.Response[v1.CustomerPlanServiceFetchAvailableLocationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.plan.v1.CustomerPlanService.FetchAvailableLocation is not implemented"))
}

func (UnimplementedCustomerPlanServiceHandler) FetchPlan(context.Context, *connect.Request[v1.CustomerPlanServiceFetchPlanRequest]) (*connect.Response[v1.CustomerPlanServiceFetchPlanResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.plan.v1.CustomerPlanService.FetchPlan is not implemented"))
}
