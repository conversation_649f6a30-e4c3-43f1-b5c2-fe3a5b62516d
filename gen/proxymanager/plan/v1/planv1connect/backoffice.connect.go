// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/plan/v1/backoffice.proto

package planv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/plan/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackofficePlanServiceName is the fully-qualified name of the BackofficePlanService service.
	BackofficePlanServiceName = "proxymanager.plan.v1.BackofficePlanService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackofficePlanServiceFetchPlanProcedure is the fully-qualified name of the
	// BackofficePlanService's FetchPlan RPC.
	BackofficePlanServiceFetchPlanProcedure = "/proxymanager.plan.v1.BackofficePlanService/FetchPlan"
	// BackofficePlanServiceCreatePlanProcedure is the fully-qualified name of the
	// BackofficePlanService's CreatePlan RPC.
	BackofficePlanServiceCreatePlanProcedure = "/proxymanager.plan.v1.BackofficePlanService/CreatePlan"
	// BackofficePlanServiceUpdatePlanProcedure is the fully-qualified name of the
	// BackofficePlanService's UpdatePlan RPC.
	BackofficePlanServiceUpdatePlanProcedure = "/proxymanager.plan.v1.BackofficePlanService/UpdatePlan"
	// BackofficePlanServiceConfigPlanLocationProcedure is the fully-qualified name of the
	// BackofficePlanService's ConfigPlanLocation RPC.
	BackofficePlanServiceConfigPlanLocationProcedure = "/proxymanager.plan.v1.BackofficePlanService/ConfigPlanLocation"
	// BackofficePlanServiceConfigPlanBackConnectProcedure is the fully-qualified name of the
	// BackofficePlanService's ConfigPlanBackConnect RPC.
	BackofficePlanServiceConfigPlanBackConnectProcedure = "/proxymanager.plan.v1.BackofficePlanService/ConfigPlanBackConnect"
)

// BackofficePlanServiceClient is a client for the proxymanager.plan.v1.BackofficePlanService
// service.
type BackofficePlanServiceClient interface {
	FetchPlan(context.Context, *connect.Request[v1.BackofficePlanServiceFetchPlanRequest]) (*connect.Response[v1.BackofficePlanServiceFetchPlanResponse], error)
	CreatePlan(context.Context, *connect.Request[v1.BackofficePlanServiceCreatePlanRequest]) (*connect.Response[v1.BackofficePlanServiceCreatePlanResponse], error)
	UpdatePlan(context.Context, *connect.Request[v1.BackofficePlanServiceUpdatePlanRequest]) (*connect.Response[v1.BackofficePlanServiceUpdatePlanResponse], error)
	ConfigPlanLocation(context.Context, *connect.Request[v1.BackofficePlanServiceConfigPlanLocationRequest]) (*connect.Response[v1.BackofficePlanServiceConfigPlanLocationResponse], error)
	ConfigPlanBackConnect(context.Context, *connect.Request[v1.BackofficePlanServiceConfigPlanBackConnectRequest]) (*connect.Response[v1.BackofficePlanServiceConfigPlanBackConnectResponse], error)
}

// NewBackofficePlanServiceClient constructs a client for the
// proxymanager.plan.v1.BackofficePlanService service. By default, it uses the Connect protocol with
// the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed requests. To use
// the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackofficePlanServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackofficePlanServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backofficePlanServiceMethods := v1.File_proxymanager_plan_v1_backoffice_proto.Services().ByName("BackofficePlanService").Methods()
	return &backofficePlanServiceClient{
		fetchPlan: connect.NewClient[v1.BackofficePlanServiceFetchPlanRequest, v1.BackofficePlanServiceFetchPlanResponse](
			httpClient,
			baseURL+BackofficePlanServiceFetchPlanProcedure,
			connect.WithSchema(backofficePlanServiceMethods.ByName("FetchPlan")),
			connect.WithClientOptions(opts...),
		),
		createPlan: connect.NewClient[v1.BackofficePlanServiceCreatePlanRequest, v1.BackofficePlanServiceCreatePlanResponse](
			httpClient,
			baseURL+BackofficePlanServiceCreatePlanProcedure,
			connect.WithSchema(backofficePlanServiceMethods.ByName("CreatePlan")),
			connect.WithClientOptions(opts...),
		),
		updatePlan: connect.NewClient[v1.BackofficePlanServiceUpdatePlanRequest, v1.BackofficePlanServiceUpdatePlanResponse](
			httpClient,
			baseURL+BackofficePlanServiceUpdatePlanProcedure,
			connect.WithSchema(backofficePlanServiceMethods.ByName("UpdatePlan")),
			connect.WithClientOptions(opts...),
		),
		configPlanLocation: connect.NewClient[v1.BackofficePlanServiceConfigPlanLocationRequest, v1.BackofficePlanServiceConfigPlanLocationResponse](
			httpClient,
			baseURL+BackofficePlanServiceConfigPlanLocationProcedure,
			connect.WithSchema(backofficePlanServiceMethods.ByName("ConfigPlanLocation")),
			connect.WithClientOptions(opts...),
		),
		configPlanBackConnect: connect.NewClient[v1.BackofficePlanServiceConfigPlanBackConnectRequest, v1.BackofficePlanServiceConfigPlanBackConnectResponse](
			httpClient,
			baseURL+BackofficePlanServiceConfigPlanBackConnectProcedure,
			connect.WithSchema(backofficePlanServiceMethods.ByName("ConfigPlanBackConnect")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backofficePlanServiceClient implements BackofficePlanServiceClient.
type backofficePlanServiceClient struct {
	fetchPlan             *connect.Client[v1.BackofficePlanServiceFetchPlanRequest, v1.BackofficePlanServiceFetchPlanResponse]
	createPlan            *connect.Client[v1.BackofficePlanServiceCreatePlanRequest, v1.BackofficePlanServiceCreatePlanResponse]
	updatePlan            *connect.Client[v1.BackofficePlanServiceUpdatePlanRequest, v1.BackofficePlanServiceUpdatePlanResponse]
	configPlanLocation    *connect.Client[v1.BackofficePlanServiceConfigPlanLocationRequest, v1.BackofficePlanServiceConfigPlanLocationResponse]
	configPlanBackConnect *connect.Client[v1.BackofficePlanServiceConfigPlanBackConnectRequest, v1.BackofficePlanServiceConfigPlanBackConnectResponse]
}

// FetchPlan calls proxymanager.plan.v1.BackofficePlanService.FetchPlan.
func (c *backofficePlanServiceClient) FetchPlan(ctx context.Context, req *connect.Request[v1.BackofficePlanServiceFetchPlanRequest]) (*connect.Response[v1.BackofficePlanServiceFetchPlanResponse], error) {
	return c.fetchPlan.CallUnary(ctx, req)
}

// CreatePlan calls proxymanager.plan.v1.BackofficePlanService.CreatePlan.
func (c *backofficePlanServiceClient) CreatePlan(ctx context.Context, req *connect.Request[v1.BackofficePlanServiceCreatePlanRequest]) (*connect.Response[v1.BackofficePlanServiceCreatePlanResponse], error) {
	return c.createPlan.CallUnary(ctx, req)
}

// UpdatePlan calls proxymanager.plan.v1.BackofficePlanService.UpdatePlan.
func (c *backofficePlanServiceClient) UpdatePlan(ctx context.Context, req *connect.Request[v1.BackofficePlanServiceUpdatePlanRequest]) (*connect.Response[v1.BackofficePlanServiceUpdatePlanResponse], error) {
	return c.updatePlan.CallUnary(ctx, req)
}

// ConfigPlanLocation calls proxymanager.plan.v1.BackofficePlanService.ConfigPlanLocation.
func (c *backofficePlanServiceClient) ConfigPlanLocation(ctx context.Context, req *connect.Request[v1.BackofficePlanServiceConfigPlanLocationRequest]) (*connect.Response[v1.BackofficePlanServiceConfigPlanLocationResponse], error) {
	return c.configPlanLocation.CallUnary(ctx, req)
}

// ConfigPlanBackConnect calls proxymanager.plan.v1.BackofficePlanService.ConfigPlanBackConnect.
func (c *backofficePlanServiceClient) ConfigPlanBackConnect(ctx context.Context, req *connect.Request[v1.BackofficePlanServiceConfigPlanBackConnectRequest]) (*connect.Response[v1.BackofficePlanServiceConfigPlanBackConnectResponse], error) {
	return c.configPlanBackConnect.CallUnary(ctx, req)
}

// BackofficePlanServiceHandler is an implementation of the
// proxymanager.plan.v1.BackofficePlanService service.
type BackofficePlanServiceHandler interface {
	FetchPlan(context.Context, *connect.Request[v1.BackofficePlanServiceFetchPlanRequest]) (*connect.Response[v1.BackofficePlanServiceFetchPlanResponse], error)
	CreatePlan(context.Context, *connect.Request[v1.BackofficePlanServiceCreatePlanRequest]) (*connect.Response[v1.BackofficePlanServiceCreatePlanResponse], error)
	UpdatePlan(context.Context, *connect.Request[v1.BackofficePlanServiceUpdatePlanRequest]) (*connect.Response[v1.BackofficePlanServiceUpdatePlanResponse], error)
	ConfigPlanLocation(context.Context, *connect.Request[v1.BackofficePlanServiceConfigPlanLocationRequest]) (*connect.Response[v1.BackofficePlanServiceConfigPlanLocationResponse], error)
	ConfigPlanBackConnect(context.Context, *connect.Request[v1.BackofficePlanServiceConfigPlanBackConnectRequest]) (*connect.Response[v1.BackofficePlanServiceConfigPlanBackConnectResponse], error)
}

// NewBackofficePlanServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackofficePlanServiceHandler(svc BackofficePlanServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backofficePlanServiceMethods := v1.File_proxymanager_plan_v1_backoffice_proto.Services().ByName("BackofficePlanService").Methods()
	backofficePlanServiceFetchPlanHandler := connect.NewUnaryHandler(
		BackofficePlanServiceFetchPlanProcedure,
		svc.FetchPlan,
		connect.WithSchema(backofficePlanServiceMethods.ByName("FetchPlan")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePlanServiceCreatePlanHandler := connect.NewUnaryHandler(
		BackofficePlanServiceCreatePlanProcedure,
		svc.CreatePlan,
		connect.WithSchema(backofficePlanServiceMethods.ByName("CreatePlan")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePlanServiceUpdatePlanHandler := connect.NewUnaryHandler(
		BackofficePlanServiceUpdatePlanProcedure,
		svc.UpdatePlan,
		connect.WithSchema(backofficePlanServiceMethods.ByName("UpdatePlan")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePlanServiceConfigPlanLocationHandler := connect.NewUnaryHandler(
		BackofficePlanServiceConfigPlanLocationProcedure,
		svc.ConfigPlanLocation,
		connect.WithSchema(backofficePlanServiceMethods.ByName("ConfigPlanLocation")),
		connect.WithHandlerOptions(opts...),
	)
	backofficePlanServiceConfigPlanBackConnectHandler := connect.NewUnaryHandler(
		BackofficePlanServiceConfigPlanBackConnectProcedure,
		svc.ConfigPlanBackConnect,
		connect.WithSchema(backofficePlanServiceMethods.ByName("ConfigPlanBackConnect")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.plan.v1.BackofficePlanService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackofficePlanServiceFetchPlanProcedure:
			backofficePlanServiceFetchPlanHandler.ServeHTTP(w, r)
		case BackofficePlanServiceCreatePlanProcedure:
			backofficePlanServiceCreatePlanHandler.ServeHTTP(w, r)
		case BackofficePlanServiceUpdatePlanProcedure:
			backofficePlanServiceUpdatePlanHandler.ServeHTTP(w, r)
		case BackofficePlanServiceConfigPlanLocationProcedure:
			backofficePlanServiceConfigPlanLocationHandler.ServeHTTP(w, r)
		case BackofficePlanServiceConfigPlanBackConnectProcedure:
			backofficePlanServiceConfigPlanBackConnectHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackofficePlanServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackofficePlanServiceHandler struct{}

func (UnimplementedBackofficePlanServiceHandler) FetchPlan(context.Context, *connect.Request[v1.BackofficePlanServiceFetchPlanRequest]) (*connect.Response[v1.BackofficePlanServiceFetchPlanResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.plan.v1.BackofficePlanService.FetchPlan is not implemented"))
}

func (UnimplementedBackofficePlanServiceHandler) CreatePlan(context.Context, *connect.Request[v1.BackofficePlanServiceCreatePlanRequest]) (*connect.Response[v1.BackofficePlanServiceCreatePlanResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.plan.v1.BackofficePlanService.CreatePlan is not implemented"))
}

func (UnimplementedBackofficePlanServiceHandler) UpdatePlan(context.Context, *connect.Request[v1.BackofficePlanServiceUpdatePlanRequest]) (*connect.Response[v1.BackofficePlanServiceUpdatePlanResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.plan.v1.BackofficePlanService.UpdatePlan is not implemented"))
}

func (UnimplementedBackofficePlanServiceHandler) ConfigPlanLocation(context.Context, *connect.Request[v1.BackofficePlanServiceConfigPlanLocationRequest]) (*connect.Response[v1.BackofficePlanServiceConfigPlanLocationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.plan.v1.BackofficePlanService.ConfigPlanLocation is not implemented"))
}

func (UnimplementedBackofficePlanServiceHandler) ConfigPlanBackConnect(context.Context, *connect.Request[v1.BackofficePlanServiceConfigPlanBackConnectRequest]) (*connect.Response[v1.BackofficePlanServiceConfigPlanBackConnectResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.plan.v1.BackofficePlanService.ConfigPlanBackConnect is not implemented"))
}
