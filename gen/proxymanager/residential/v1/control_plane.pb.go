// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/residential/v1/control_plane.proto

package residentialv1

import (
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ControlPlaneResidentialServiceFetchPortConfigRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdResNode     string                 `protobuf:"bytes,1,opt,name=id_res_node,json=idResNode,proto3" json:"id_res_node,omitempty"`
	NetworkPort   int64                  `protobuf:"varint,2,opt,name=network_port,json=networkPort,proto3" json:"network_port,omitempty"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	State         *v1.State              `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ControlPlaneResidentialServiceFetchPortConfigRequest) Reset() {
	*x = ControlPlaneResidentialServiceFetchPortConfigRequest{}
	mi := &file_proxymanager_residential_v1_control_plane_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ControlPlaneResidentialServiceFetchPortConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlPlaneResidentialServiceFetchPortConfigRequest) ProtoMessage() {}

func (x *ControlPlaneResidentialServiceFetchPortConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_control_plane_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlPlaneResidentialServiceFetchPortConfigRequest.ProtoReflect.Descriptor instead.
func (*ControlPlaneResidentialServiceFetchPortConfigRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_control_plane_proto_rawDescGZIP(), []int{0}
}

func (x *ControlPlaneResidentialServiceFetchPortConfigRequest) GetIdResNode() string {
	if x != nil {
		return x.IdResNode
	}
	return ""
}

func (x *ControlPlaneResidentialServiceFetchPortConfigRequest) GetNetworkPort() int64 {
	if x != nil {
		return x.NetworkPort
	}
	return 0
}

func (x *ControlPlaneResidentialServiceFetchPortConfigRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ControlPlaneResidentialServiceFetchPortConfigRequest) GetState() *v1.State {
	if x != nil {
		return x.State
	}
	return nil
}

type ControlPlaneResidentialServiceFetchPortConfigResponse struct {
	state         protoimpl.MessageState                             `protogen:"open.v1"`
	Configs       []*ControlPlaneResidentialServiceManagerPortConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	Error         *v11.ErrorMessage                                  `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v1.PaginationResponse                             `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ControlPlaneResidentialServiceFetchPortConfigResponse) Reset() {
	*x = ControlPlaneResidentialServiceFetchPortConfigResponse{}
	mi := &file_proxymanager_residential_v1_control_plane_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ControlPlaneResidentialServiceFetchPortConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlPlaneResidentialServiceFetchPortConfigResponse) ProtoMessage() {}

func (x *ControlPlaneResidentialServiceFetchPortConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_control_plane_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlPlaneResidentialServiceFetchPortConfigResponse.ProtoReflect.Descriptor instead.
func (*ControlPlaneResidentialServiceFetchPortConfigResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_control_plane_proto_rawDescGZIP(), []int{1}
}

func (x *ControlPlaneResidentialServiceFetchPortConfigResponse) GetConfigs() []*ControlPlaneResidentialServiceManagerPortConfig {
	if x != nil {
		return x.Configs
	}
	return nil
}

func (x *ControlPlaneResidentialServiceFetchPortConfigResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *ControlPlaneResidentialServiceFetchPortConfigResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type ControlPlaneResidentialServiceManagerPortConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdResPort     string                 `protobuf:"bytes,1,opt,name=id_res_port,json=idResPort,proto3" json:"id_res_port,omitempty"`
	NetworkPort   uint64                 `protobuf:"varint,2,opt,name=network_port,json=networkPort,proto3" json:"network_port,omitempty"`
	UserPppoe     string                 `protobuf:"bytes,3,opt,name=user_pppoe,json=userPppoe,proto3" json:"user_pppoe,omitempty"`
	PassPppoe     string                 `protobuf:"bytes,4,opt,name=pass_pppoe,json=passPppoe,proto3" json:"pass_pppoe,omitempty"`
	ListDevice    []*ManagerDevice       `protobuf:"bytes,5,rep,name=list_device,json=listDevice,proto3" json:"list_device,omitempty"`
	IsActive      bool                   `protobuf:"varint,6,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ControlPlaneResidentialServiceManagerPortConfig) Reset() {
	*x = ControlPlaneResidentialServiceManagerPortConfig{}
	mi := &file_proxymanager_residential_v1_control_plane_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ControlPlaneResidentialServiceManagerPortConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlPlaneResidentialServiceManagerPortConfig) ProtoMessage() {}

func (x *ControlPlaneResidentialServiceManagerPortConfig) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_control_plane_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlPlaneResidentialServiceManagerPortConfig.ProtoReflect.Descriptor instead.
func (*ControlPlaneResidentialServiceManagerPortConfig) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_control_plane_proto_rawDescGZIP(), []int{2}
}

func (x *ControlPlaneResidentialServiceManagerPortConfig) GetIdResPort() string {
	if x != nil {
		return x.IdResPort
	}
	return ""
}

func (x *ControlPlaneResidentialServiceManagerPortConfig) GetNetworkPort() uint64 {
	if x != nil {
		return x.NetworkPort
	}
	return 0
}

func (x *ControlPlaneResidentialServiceManagerPortConfig) GetUserPppoe() string {
	if x != nil {
		return x.UserPppoe
	}
	return ""
}

func (x *ControlPlaneResidentialServiceManagerPortConfig) GetPassPppoe() string {
	if x != nil {
		return x.PassPppoe
	}
	return ""
}

func (x *ControlPlaneResidentialServiceManagerPortConfig) GetListDevice() []*ManagerDevice {
	if x != nil {
		return x.ListDevice
	}
	return nil
}

func (x *ControlPlaneResidentialServiceManagerPortConfig) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type ManagerDevice struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdResDevice   string                 `protobuf:"bytes,1,opt,name=id_res_device,json=idResDevice,proto3" json:"id_res_device,omitempty"`
	DeviceName    string                 `protobuf:"bytes,2,opt,name=device_name,json=deviceName,proto3" json:"device_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ManagerDevice) Reset() {
	*x = ManagerDevice{}
	mi := &file_proxymanager_residential_v1_control_plane_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ManagerDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManagerDevice) ProtoMessage() {}

func (x *ManagerDevice) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_control_plane_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManagerDevice.ProtoReflect.Descriptor instead.
func (*ManagerDevice) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_control_plane_proto_rawDescGZIP(), []int{3}
}

func (x *ManagerDevice) GetIdResDevice() string {
	if x != nil {
		return x.IdResDevice
	}
	return ""
}

func (x *ManagerDevice) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

type ControlPlaneResidentialServiceUpdateNodeStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdResNode     string                 `protobuf:"bytes,1,opt,name=id_res_node,json=idResNode,proto3" json:"id_res_node,omitempty"`
	Ipv4          string                 `protobuf:"bytes,2,opt,name=ipv4,proto3" json:"ipv4,omitempty"`
	Ipv6          string                 `protobuf:"bytes,3,opt,name=ipv6,proto3" json:"ipv6,omitempty"`
	IpControl     string                 `protobuf:"bytes,4,opt,name=ip_control,json=ipControl,proto3" json:"ip_control,omitempty"`
	PortControl   uint64                 `protobuf:"varint,5,opt,name=port_control,json=portControl,proto3" json:"port_control,omitempty"`
	SpeedInGb     int64                  `protobuf:"varint,6,opt,name=speed_in_gb,json=speedInGb,proto3" json:"speed_in_gb,omitempty"`
	Status        v12.ResNodeStatus      `protobuf:"varint,7,opt,name=status,proto3,enum=algoenum.v1.ResNodeStatus" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ControlPlaneResidentialServiceUpdateNodeStatusRequest) Reset() {
	*x = ControlPlaneResidentialServiceUpdateNodeStatusRequest{}
	mi := &file_proxymanager_residential_v1_control_plane_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ControlPlaneResidentialServiceUpdateNodeStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlPlaneResidentialServiceUpdateNodeStatusRequest) ProtoMessage() {}

func (x *ControlPlaneResidentialServiceUpdateNodeStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_control_plane_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlPlaneResidentialServiceUpdateNodeStatusRequest.ProtoReflect.Descriptor instead.
func (*ControlPlaneResidentialServiceUpdateNodeStatusRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_control_plane_proto_rawDescGZIP(), []int{4}
}

func (x *ControlPlaneResidentialServiceUpdateNodeStatusRequest) GetIdResNode() string {
	if x != nil {
		return x.IdResNode
	}
	return ""
}

func (x *ControlPlaneResidentialServiceUpdateNodeStatusRequest) GetIpv4() string {
	if x != nil {
		return x.Ipv4
	}
	return ""
}

func (x *ControlPlaneResidentialServiceUpdateNodeStatusRequest) GetIpv6() string {
	if x != nil {
		return x.Ipv6
	}
	return ""
}

func (x *ControlPlaneResidentialServiceUpdateNodeStatusRequest) GetIpControl() string {
	if x != nil {
		return x.IpControl
	}
	return ""
}

func (x *ControlPlaneResidentialServiceUpdateNodeStatusRequest) GetPortControl() uint64 {
	if x != nil {
		return x.PortControl
	}
	return 0
}

func (x *ControlPlaneResidentialServiceUpdateNodeStatusRequest) GetSpeedInGb() int64 {
	if x != nil {
		return x.SpeedInGb
	}
	return 0
}

func (x *ControlPlaneResidentialServiceUpdateNodeStatusRequest) GetStatus() v12.ResNodeStatus {
	if x != nil {
		return x.Status
	}
	return v12.ResNodeStatus(0)
}

type ControlPlaneResidentialServiceUpdateNodeStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ControlPlaneResidentialServiceUpdateNodeStatusResponse) Reset() {
	*x = ControlPlaneResidentialServiceUpdateNodeStatusResponse{}
	mi := &file_proxymanager_residential_v1_control_plane_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ControlPlaneResidentialServiceUpdateNodeStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlPlaneResidentialServiceUpdateNodeStatusResponse) ProtoMessage() {}

func (x *ControlPlaneResidentialServiceUpdateNodeStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_control_plane_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlPlaneResidentialServiceUpdateNodeStatusResponse.ProtoReflect.Descriptor instead.
func (*ControlPlaneResidentialServiceUpdateNodeStatusResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_control_plane_proto_rawDescGZIP(), []int{5}
}

func (x *ControlPlaneResidentialServiceUpdateNodeStatusResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type ControlPlaneResidentialServiceUpdateDeviceStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdResDevice   string                 `protobuf:"bytes,1,opt,name=id_res_device,json=idResDevice,proto3" json:"id_res_device,omitempty"`
	Ipv4Prefix    string                 `protobuf:"bytes,2,opt,name=ipv4_prefix,json=ipv4Prefix,proto3" json:"ipv4_prefix,omitempty"`
	Ipv6Prefix    string                 `protobuf:"bytes,3,opt,name=ipv6_prefix,json=ipv6Prefix,proto3" json:"ipv6_prefix,omitempty"`
	IpProxy       string                 `protobuf:"bytes,4,opt,name=ip_proxy,json=ipProxy,proto3" json:"ip_proxy,omitempty"`
	S5Port        int64                  `protobuf:"varint,5,opt,name=s5_port,json=s5Port,proto3" json:"s5_port,omitempty"`
	IpControl     string                 `protobuf:"bytes,6,opt,name=ip_control,json=ipControl,proto3" json:"ip_control,omitempty"`
	PortControl   int64                  `protobuf:"varint,7,opt,name=port_control,json=portControl,proto3" json:"port_control,omitempty"`
	DeviceStatus  v12.ResDeviceStatus    `protobuf:"varint,8,opt,name=device_status,json=deviceStatus,proto3,enum=algoenum.v1.ResDeviceStatus" json:"device_status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ControlPlaneResidentialServiceUpdateDeviceStatusRequest) Reset() {
	*x = ControlPlaneResidentialServiceUpdateDeviceStatusRequest{}
	mi := &file_proxymanager_residential_v1_control_plane_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ControlPlaneResidentialServiceUpdateDeviceStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlPlaneResidentialServiceUpdateDeviceStatusRequest) ProtoMessage() {}

func (x *ControlPlaneResidentialServiceUpdateDeviceStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_control_plane_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlPlaneResidentialServiceUpdateDeviceStatusRequest.ProtoReflect.Descriptor instead.
func (*ControlPlaneResidentialServiceUpdateDeviceStatusRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_control_plane_proto_rawDescGZIP(), []int{6}
}

func (x *ControlPlaneResidentialServiceUpdateDeviceStatusRequest) GetIdResDevice() string {
	if x != nil {
		return x.IdResDevice
	}
	return ""
}

func (x *ControlPlaneResidentialServiceUpdateDeviceStatusRequest) GetIpv4Prefix() string {
	if x != nil {
		return x.Ipv4Prefix
	}
	return ""
}

func (x *ControlPlaneResidentialServiceUpdateDeviceStatusRequest) GetIpv6Prefix() string {
	if x != nil {
		return x.Ipv6Prefix
	}
	return ""
}

func (x *ControlPlaneResidentialServiceUpdateDeviceStatusRequest) GetIpProxy() string {
	if x != nil {
		return x.IpProxy
	}
	return ""
}

func (x *ControlPlaneResidentialServiceUpdateDeviceStatusRequest) GetS5Port() int64 {
	if x != nil {
		return x.S5Port
	}
	return 0
}

func (x *ControlPlaneResidentialServiceUpdateDeviceStatusRequest) GetIpControl() string {
	if x != nil {
		return x.IpControl
	}
	return ""
}

func (x *ControlPlaneResidentialServiceUpdateDeviceStatusRequest) GetPortControl() int64 {
	if x != nil {
		return x.PortControl
	}
	return 0
}

func (x *ControlPlaneResidentialServiceUpdateDeviceStatusRequest) GetDeviceStatus() v12.ResDeviceStatus {
	if x != nil {
		return x.DeviceStatus
	}
	return v12.ResDeviceStatus(0)
}

type ControlPlaneResidentialServiceUpdateDeviceStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ControlPlaneResidentialServiceUpdateDeviceStatusResponse) Reset() {
	*x = ControlPlaneResidentialServiceUpdateDeviceStatusResponse{}
	mi := &file_proxymanager_residential_v1_control_plane_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ControlPlaneResidentialServiceUpdateDeviceStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlPlaneResidentialServiceUpdateDeviceStatusResponse) ProtoMessage() {}

func (x *ControlPlaneResidentialServiceUpdateDeviceStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_control_plane_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlPlaneResidentialServiceUpdateDeviceStatusResponse.ProtoReflect.Descriptor instead.
func (*ControlPlaneResidentialServiceUpdateDeviceStatusResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_control_plane_proto_rawDescGZIP(), []int{7}
}

func (x *ControlPlaneResidentialServiceUpdateDeviceStatusResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_proxymanager_residential_v1_control_plane_proto protoreflect.FileDescriptor

const file_proxymanager_residential_v1_control_plane_proto_rawDesc = "" +
	"\n" +
	"/proxymanager/residential/v1/control_plane.proto\x12\x1bproxymanager.residential.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\x1a\x1aalgoenum/v1/res_node.proto\"\xdd\x01\n" +
	"4ControlPlaneResidentialServiceFetchPortConfigRequest\x12\x1e\n" +
	"\vid_res_node\x18\x01 \x01(\tR\tidResNode\x12!\n" +
	"\fnetwork_port\x18\x02 \x01(\x03R\vnetworkPort\x12;\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\x12%\n" +
	"\x05state\x18\x04 \x01(\v2\x0f.utils.v1.StateR\x05state\"\x8c\x02\n" +
	"5ControlPlaneResidentialServiceFetchPortConfigResponse\x12f\n" +
	"\aconfigs\x18\x01 \x03(\v2L.proxymanager.residential.v1.ControlPlaneResidentialServiceManagerPortConfigR\aconfigs\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\"\x9c\x02\n" +
	"/ControlPlaneResidentialServiceManagerPortConfig\x12\x1e\n" +
	"\vid_res_port\x18\x01 \x01(\tR\tidResPort\x12!\n" +
	"\fnetwork_port\x18\x02 \x01(\x04R\vnetworkPort\x12\x1d\n" +
	"\n" +
	"user_pppoe\x18\x03 \x01(\tR\tuserPppoe\x12\x1d\n" +
	"\n" +
	"pass_pppoe\x18\x04 \x01(\tR\tpassPppoe\x12K\n" +
	"\vlist_device\x18\x05 \x03(\v2*.proxymanager.residential.v1.ManagerDeviceR\n" +
	"listDevice\x12\x1b\n" +
	"\tis_active\x18\x06 \x01(\bR\bisActive\"T\n" +
	"\rManagerDevice\x12\"\n" +
	"\rid_res_device\x18\x01 \x01(\tR\vidResDevice\x12\x1f\n" +
	"\vdevice_name\x18\x02 \x01(\tR\n" +
	"deviceName\"\x95\x02\n" +
	"5ControlPlaneResidentialServiceUpdateNodeStatusRequest\x12\x1e\n" +
	"\vid_res_node\x18\x01 \x01(\tR\tidResNode\x12\x12\n" +
	"\x04ipv4\x18\x02 \x01(\tR\x04ipv4\x12\x12\n" +
	"\x04ipv6\x18\x03 \x01(\tR\x04ipv6\x12\x1d\n" +
	"\n" +
	"ip_control\x18\x04 \x01(\tR\tipControl\x12!\n" +
	"\fport_control\x18\x05 \x01(\x04R\vportControl\x12\x1e\n" +
	"\vspeed_in_gb\x18\x06 \x01(\x03R\tspeedInGb\x122\n" +
	"\x06status\x18\a \x01(\x0e2\x1a.algoenum.v1.ResNodeStatusR\x06status\"g\n" +
	"6ControlPlaneResidentialServiceUpdateNodeStatusResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xd8\x02\n" +
	"7ControlPlaneResidentialServiceUpdateDeviceStatusRequest\x12\"\n" +
	"\rid_res_device\x18\x01 \x01(\tR\vidResDevice\x12\x1f\n" +
	"\vipv4_prefix\x18\x02 \x01(\tR\n" +
	"ipv4Prefix\x12\x1f\n" +
	"\vipv6_prefix\x18\x03 \x01(\tR\n" +
	"ipv6Prefix\x12\x19\n" +
	"\bip_proxy\x18\x04 \x01(\tR\aipProxy\x12\x17\n" +
	"\as5_port\x18\x05 \x01(\x03R\x06s5Port\x12\x1d\n" +
	"\n" +
	"ip_control\x18\x06 \x01(\tR\tipControl\x12!\n" +
	"\fport_control\x18\a \x01(\x03R\vportControl\x12A\n" +
	"\rdevice_status\x18\b \x01(\x0e2\x1c.algoenum.v1.ResDeviceStatusR\fdeviceStatus\"i\n" +
	"8ControlPlaneResidentialServiceUpdateDeviceStatusResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error2\xdd\x04\n" +
	"\x1eControlPlaneResidentialService\x12\xb8\x01\n" +
	"\x0fFetchPortConfig\x12Q.proxymanager.residential.v1.ControlPlaneResidentialServiceFetchPortConfigRequest\x1aR.proxymanager.residential.v1.ControlPlaneResidentialServiceFetchPortConfigResponse\x12\xbb\x01\n" +
	"\x10UpdateNodeStatus\x12R.proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateNodeStatusRequest\x1aS.proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateNodeStatusResponse\x12\xc1\x01\n" +
	"\x12UpdateDeviceStatus\x12T.proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateDeviceStatusRequest\x1aU.proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateDeviceStatusResponseBZZXgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/residential/v1;residentialv1b\x06proto3"

var (
	file_proxymanager_residential_v1_control_plane_proto_rawDescOnce sync.Once
	file_proxymanager_residential_v1_control_plane_proto_rawDescData []byte
)

func file_proxymanager_residential_v1_control_plane_proto_rawDescGZIP() []byte {
	file_proxymanager_residential_v1_control_plane_proto_rawDescOnce.Do(func() {
		file_proxymanager_residential_v1_control_plane_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_residential_v1_control_plane_proto_rawDesc), len(file_proxymanager_residential_v1_control_plane_proto_rawDesc)))
	})
	return file_proxymanager_residential_v1_control_plane_proto_rawDescData
}

var file_proxymanager_residential_v1_control_plane_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_proxymanager_residential_v1_control_plane_proto_goTypes = []any{
	(*ControlPlaneResidentialServiceFetchPortConfigRequest)(nil),  // 0: proxymanager.residential.v1.ControlPlaneResidentialServiceFetchPortConfigRequest
	(*ControlPlaneResidentialServiceFetchPortConfigResponse)(nil), // 1: proxymanager.residential.v1.ControlPlaneResidentialServiceFetchPortConfigResponse
	(*ControlPlaneResidentialServiceManagerPortConfig)(nil),       // 2: proxymanager.residential.v1.ControlPlaneResidentialServiceManagerPortConfig
	(*ManagerDevice)(nil), // 3: proxymanager.residential.v1.ManagerDevice
	(*ControlPlaneResidentialServiceUpdateNodeStatusRequest)(nil),    // 4: proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateNodeStatusRequest
	(*ControlPlaneResidentialServiceUpdateNodeStatusResponse)(nil),   // 5: proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateNodeStatusResponse
	(*ControlPlaneResidentialServiceUpdateDeviceStatusRequest)(nil),  // 6: proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateDeviceStatusRequest
	(*ControlPlaneResidentialServiceUpdateDeviceStatusResponse)(nil), // 7: proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateDeviceStatusResponse
	(*v1.PaginationRequest)(nil),                                     // 8: utils.v1.PaginationRequest
	(*v1.State)(nil),                                                 // 9: utils.v1.State
	(*v11.ErrorMessage)(nil),                                         // 10: errmsg.v1.ErrorMessage
	(*v1.PaginationResponse)(nil),                                    // 11: utils.v1.PaginationResponse
	(v12.ResNodeStatus)(0),                                           // 12: algoenum.v1.ResNodeStatus
	(v12.ResDeviceStatus)(0),                                         // 13: algoenum.v1.ResDeviceStatus
}
var file_proxymanager_residential_v1_control_plane_proto_depIdxs = []int32{
	8,  // 0: proxymanager.residential.v1.ControlPlaneResidentialServiceFetchPortConfigRequest.pagination:type_name -> utils.v1.PaginationRequest
	9,  // 1: proxymanager.residential.v1.ControlPlaneResidentialServiceFetchPortConfigRequest.state:type_name -> utils.v1.State
	2,  // 2: proxymanager.residential.v1.ControlPlaneResidentialServiceFetchPortConfigResponse.configs:type_name -> proxymanager.residential.v1.ControlPlaneResidentialServiceManagerPortConfig
	10, // 3: proxymanager.residential.v1.ControlPlaneResidentialServiceFetchPortConfigResponse.error:type_name -> errmsg.v1.ErrorMessage
	11, // 4: proxymanager.residential.v1.ControlPlaneResidentialServiceFetchPortConfigResponse.pagination:type_name -> utils.v1.PaginationResponse
	3,  // 5: proxymanager.residential.v1.ControlPlaneResidentialServiceManagerPortConfig.list_device:type_name -> proxymanager.residential.v1.ManagerDevice
	12, // 6: proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateNodeStatusRequest.status:type_name -> algoenum.v1.ResNodeStatus
	10, // 7: proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateNodeStatusResponse.error:type_name -> errmsg.v1.ErrorMessage
	13, // 8: proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateDeviceStatusRequest.device_status:type_name -> algoenum.v1.ResDeviceStatus
	10, // 9: proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateDeviceStatusResponse.error:type_name -> errmsg.v1.ErrorMessage
	0,  // 10: proxymanager.residential.v1.ControlPlaneResidentialService.FetchPortConfig:input_type -> proxymanager.residential.v1.ControlPlaneResidentialServiceFetchPortConfigRequest
	4,  // 11: proxymanager.residential.v1.ControlPlaneResidentialService.UpdateNodeStatus:input_type -> proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateNodeStatusRequest
	6,  // 12: proxymanager.residential.v1.ControlPlaneResidentialService.UpdateDeviceStatus:input_type -> proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateDeviceStatusRequest
	1,  // 13: proxymanager.residential.v1.ControlPlaneResidentialService.FetchPortConfig:output_type -> proxymanager.residential.v1.ControlPlaneResidentialServiceFetchPortConfigResponse
	5,  // 14: proxymanager.residential.v1.ControlPlaneResidentialService.UpdateNodeStatus:output_type -> proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateNodeStatusResponse
	7,  // 15: proxymanager.residential.v1.ControlPlaneResidentialService.UpdateDeviceStatus:output_type -> proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateDeviceStatusResponse
	13, // [13:16] is the sub-list for method output_type
	10, // [10:13] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_proxymanager_residential_v1_control_plane_proto_init() }
func file_proxymanager_residential_v1_control_plane_proto_init() {
	if File_proxymanager_residential_v1_control_plane_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_residential_v1_control_plane_proto_rawDesc), len(file_proxymanager_residential_v1_control_plane_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_residential_v1_control_plane_proto_goTypes,
		DependencyIndexes: file_proxymanager_residential_v1_control_plane_proto_depIdxs,
		MessageInfos:      file_proxymanager_residential_v1_control_plane_proto_msgTypes,
	}.Build()
	File_proxymanager_residential_v1_control_plane_proto = out.File
	file_proxymanager_residential_v1_control_plane_proto_goTypes = nil
	file_proxymanager_residential_v1_control_plane_proto_depIdxs = nil
}
