// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/residential/v1/backoffice.proto

package residentialv1

import (
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v13 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/telco/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackofficeResidentialServiceRestartPortRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdResPort     string                 `protobuf:"bytes,1,opt,name=id_res_port,json=idResPort,proto3" json:"id_res_port,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeResidentialServiceRestartPortRequest) Reset() {
	*x = BackofficeResidentialServiceRestartPortRequest{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeResidentialServiceRestartPortRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeResidentialServiceRestartPortRequest) ProtoMessage() {}

func (x *BackofficeResidentialServiceRestartPortRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeResidentialServiceRestartPortRequest.ProtoReflect.Descriptor instead.
func (*BackofficeResidentialServiceRestartPortRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{0}
}

func (x *BackofficeResidentialServiceRestartPortRequest) GetIdResPort() string {
	if x != nil {
		return x.IdResPort
	}
	return ""
}

type BackofficeResidentialServiceRestartPortResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeResidentialServiceRestartPortResponse) Reset() {
	*x = BackofficeResidentialServiceRestartPortResponse{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeResidentialServiceRestartPortResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeResidentialServiceRestartPortResponse) ProtoMessage() {}

func (x *BackofficeResidentialServiceRestartPortResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeResidentialServiceRestartPortResponse.ProtoReflect.Descriptor instead.
func (*BackofficeResidentialServiceRestartPortResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{1}
}

func (x *BackofficeResidentialServiceRestartPortResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CreateResAccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdTelco       string                 `protobuf:"bytes,1,opt,name=id_telco,json=idTelco,proto3" json:"id_telco,omitempty"`
	IdLocation    string                 `protobuf:"bytes,2,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	Username      string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateResAccountRequest) Reset() {
	*x = CreateResAccountRequest{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateResAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateResAccountRequest) ProtoMessage() {}

func (x *CreateResAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateResAccountRequest.ProtoReflect.Descriptor instead.
func (*CreateResAccountRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{2}
}

func (x *CreateResAccountRequest) GetIdTelco() string {
	if x != nil {
		return x.IdTelco
	}
	return ""
}

func (x *CreateResAccountRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *CreateResAccountRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *CreateResAccountRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type CreateResAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateResAccountResponse) Reset() {
	*x = CreateResAccountResponse{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateResAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateResAccountResponse) ProtoMessage() {}

func (x *CreateResAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateResAccountResponse.ProtoReflect.Descriptor instead.
func (*CreateResAccountResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{3}
}

func (x *CreateResAccountResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type FetchResAccountRequest struct {
	state          protoimpl.MessageState  `protogen:"open.v1"`
	IdResAccount   string                  `protobuf:"bytes,1,opt,name=id_res_account,json=idResAccount,proto3" json:"id_res_account,omitempty"`
	IdLocation     string                  `protobuf:"bytes,2,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	UsernameSearch string                  `protobuf:"bytes,3,opt,name=username_search,json=usernameSearch,proto3" json:"username_search,omitempty"`
	Binding        *FetchResAccountBinding `protobuf:"bytes,4,opt,name=binding,proto3" json:"binding,omitempty"`
	State          *v11.State              `protobuf:"bytes,5,opt,name=state,proto3" json:"state,omitempty"`
	Pagination     *v11.PaginationRequest  `protobuf:"bytes,6,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *FetchResAccountRequest) Reset() {
	*x = FetchResAccountRequest{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchResAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchResAccountRequest) ProtoMessage() {}

func (x *FetchResAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchResAccountRequest.ProtoReflect.Descriptor instead.
func (*FetchResAccountRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{4}
}

func (x *FetchResAccountRequest) GetIdResAccount() string {
	if x != nil {
		return x.IdResAccount
	}
	return ""
}

func (x *FetchResAccountRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *FetchResAccountRequest) GetUsernameSearch() string {
	if x != nil {
		return x.UsernameSearch
	}
	return ""
}

func (x *FetchResAccountRequest) GetBinding() *FetchResAccountBinding {
	if x != nil {
		return x.Binding
	}
	return nil
}

func (x *FetchResAccountRequest) GetState() *v11.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *FetchResAccountRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type FetchResAccountBinding struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Enable        bool                   `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	HasBinding    bool                   `protobuf:"varint,2,opt,name=has_binding,json=hasBinding,proto3" json:"has_binding,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FetchResAccountBinding) Reset() {
	*x = FetchResAccountBinding{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchResAccountBinding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchResAccountBinding) ProtoMessage() {}

func (x *FetchResAccountBinding) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchResAccountBinding.ProtoReflect.Descriptor instead.
func (*FetchResAccountBinding) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{5}
}

func (x *FetchResAccountBinding) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *FetchResAccountBinding) GetHasBinding() bool {
	if x != nil {
		return x.HasBinding
	}
	return false
}

type FetchResAccountResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Items         []*Account              `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FetchResAccountResponse) Reset() {
	*x = FetchResAccountResponse{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchResAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchResAccountResponse) ProtoMessage() {}

func (x *FetchResAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchResAccountResponse.ProtoReflect.Descriptor instead.
func (*FetchResAccountResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{6}
}

func (x *FetchResAccountResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *FetchResAccountResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *FetchResAccountResponse) GetItems() []*Account {
	if x != nil {
		return x.Items
	}
	return nil
}

type UpdateResAccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdResAccount  string                 `protobuf:"bytes,1,opt,name=id_res_account,json=idResAccount,proto3" json:"id_res_account,omitempty"`
	IdTelco       string                 `protobuf:"bytes,2,opt,name=id_telco,json=idTelco,proto3" json:"id_telco,omitempty"`
	IdLocation    string                 `protobuf:"bytes,3,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	Username      string                 `protobuf:"bytes,4,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,5,opt,name=password,proto3" json:"password,omitempty"`
	State         *v11.State             `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateResAccountRequest) Reset() {
	*x = UpdateResAccountRequest{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateResAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateResAccountRequest) ProtoMessage() {}

func (x *UpdateResAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateResAccountRequest.ProtoReflect.Descriptor instead.
func (*UpdateResAccountRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateResAccountRequest) GetIdResAccount() string {
	if x != nil {
		return x.IdResAccount
	}
	return ""
}

func (x *UpdateResAccountRequest) GetIdTelco() string {
	if x != nil {
		return x.IdTelco
	}
	return ""
}

func (x *UpdateResAccountRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *UpdateResAccountRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UpdateResAccountRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *UpdateResAccountRequest) GetState() *v11.State {
	if x != nil {
		return x.State
	}
	return nil
}

type UpdateResAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateResAccountResponse) Reset() {
	*x = UpdateResAccountResponse{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateResAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateResAccountResponse) ProtoMessage() {}

func (x *UpdateResAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateResAccountResponse.ProtoReflect.Descriptor instead.
func (*UpdateResAccountResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateResAccountResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CreateResNodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdLocation    string                 `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	PowerDeviceId string                 `protobuf:"bytes,2,opt,name=power_device_id,json=powerDeviceId,proto3" json:"power_device_id,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateResNodeRequest) Reset() {
	*x = CreateResNodeRequest{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateResNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateResNodeRequest) ProtoMessage() {}

func (x *CreateResNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateResNodeRequest.ProtoReflect.Descriptor instead.
func (*CreateResNodeRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{9}
}

func (x *CreateResNodeRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *CreateResNodeRequest) GetPowerDeviceId() string {
	if x != nil {
		return x.PowerDeviceId
	}
	return ""
}

func (x *CreateResNodeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CreateResNodeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateResNodeResponse) Reset() {
	*x = CreateResNodeResponse{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateResNodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateResNodeResponse) ProtoMessage() {}

func (x *CreateResNodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateResNodeResponse.ProtoReflect.Descriptor instead.
func (*CreateResNodeResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{10}
}

func (x *CreateResNodeResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type UpdateResNodeRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	IdResNode         string                 `protobuf:"bytes,1,opt,name=id_res_node,json=idResNode,proto3" json:"id_res_node,omitempty"`
	IdLocation        string                 `protobuf:"bytes,2,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	Name              string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	PowerDeviceId     string                 `protobuf:"bytes,4,opt,name=power_device_id,json=powerDeviceId,proto3" json:"power_device_id,omitempty"`
	IsChangeSecretKey bool                   `protobuf:"varint,5,opt,name=is_change_secret_key,json=isChangeSecretKey,proto3" json:"is_change_secret_key,omitempty"`
	State             *v11.State             `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UpdateResNodeRequest) Reset() {
	*x = UpdateResNodeRequest{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateResNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateResNodeRequest) ProtoMessage() {}

func (x *UpdateResNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateResNodeRequest.ProtoReflect.Descriptor instead.
func (*UpdateResNodeRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateResNodeRequest) GetIdResNode() string {
	if x != nil {
		return x.IdResNode
	}
	return ""
}

func (x *UpdateResNodeRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *UpdateResNodeRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateResNodeRequest) GetPowerDeviceId() string {
	if x != nil {
		return x.PowerDeviceId
	}
	return ""
}

func (x *UpdateResNodeRequest) GetIsChangeSecretKey() bool {
	if x != nil {
		return x.IsChangeSecretKey
	}
	return false
}

func (x *UpdateResNodeRequest) GetState() *v11.State {
	if x != nil {
		return x.State
	}
	return nil
}

type UpdateResNodeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateResNodeResponse) Reset() {
	*x = UpdateResNodeResponse{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateResNodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateResNodeResponse) ProtoMessage() {}

func (x *UpdateResNodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateResNodeResponse.ProtoReflect.Descriptor instead.
func (*UpdateResNodeResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateResNodeResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type FetchResNodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdResNode     string                 `protobuf:"bytes,1,opt,name=id_res_node,json=idResNode,proto3" json:"id_res_node,omitempty"`
	PowerDeviceId string                 `protobuf:"bytes,2,opt,name=power_device_id,json=powerDeviceId,proto3" json:"power_device_id,omitempty"`
	NameSearch    string                 `protobuf:"bytes,3,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	NodeStatus    v12.ResNodeStatus      `protobuf:"varint,4,opt,name=node_status,json=nodeStatus,proto3,enum=algoenum.v1.ResNodeStatus" json:"node_status,omitempty"`
	PowerState    v12.ResNodePowerState  `protobuf:"varint,5,opt,name=power_state,json=powerState,proto3,enum=algoenum.v1.ResNodePowerState" json:"power_state,omitempty"`
	State         *v11.State             `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v11.PaginationRequest `protobuf:"bytes,7,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FetchResNodeRequest) Reset() {
	*x = FetchResNodeRequest{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchResNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchResNodeRequest) ProtoMessage() {}

func (x *FetchResNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchResNodeRequest.ProtoReflect.Descriptor instead.
func (*FetchResNodeRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{13}
}

func (x *FetchResNodeRequest) GetIdResNode() string {
	if x != nil {
		return x.IdResNode
	}
	return ""
}

func (x *FetchResNodeRequest) GetPowerDeviceId() string {
	if x != nil {
		return x.PowerDeviceId
	}
	return ""
}

func (x *FetchResNodeRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *FetchResNodeRequest) GetNodeStatus() v12.ResNodeStatus {
	if x != nil {
		return x.NodeStatus
	}
	return v12.ResNodeStatus(0)
}

func (x *FetchResNodeRequest) GetPowerState() v12.ResNodePowerState {
	if x != nil {
		return x.PowerState
	}
	return v12.ResNodePowerState(0)
}

func (x *FetchResNodeRequest) GetState() *v11.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *FetchResNodeRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type FetchResNodeResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Items         []*Node                 `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FetchResNodeResponse) Reset() {
	*x = FetchResNodeResponse{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchResNodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchResNodeResponse) ProtoMessage() {}

func (x *FetchResNodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchResNodeResponse.ProtoReflect.Descriptor instead.
func (*FetchResNodeResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{14}
}

func (x *FetchResNodeResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *FetchResNodeResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *FetchResNodeResponse) GetItems() []*Node {
	if x != nil {
		return x.Items
	}
	return nil
}

type FetchResPortRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdResNode     string                 `protobuf:"bytes,1,opt,name=id_res_node,json=idResNode,proto3" json:"id_res_node,omitempty"`
	IdResPort     string                 `protobuf:"bytes,2,opt,name=id_res_port,json=idResPort,proto3" json:"id_res_port,omitempty"`
	NetworkPort   uint64                 `protobuf:"varint,3,opt,name=network_port,json=networkPort,proto3" json:"network_port,omitempty"`
	State         *v11.State             `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v11.PaginationRequest `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FetchResPortRequest) Reset() {
	*x = FetchResPortRequest{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchResPortRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchResPortRequest) ProtoMessage() {}

func (x *FetchResPortRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchResPortRequest.ProtoReflect.Descriptor instead.
func (*FetchResPortRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{15}
}

func (x *FetchResPortRequest) GetIdResNode() string {
	if x != nil {
		return x.IdResNode
	}
	return ""
}

func (x *FetchResPortRequest) GetIdResPort() string {
	if x != nil {
		return x.IdResPort
	}
	return ""
}

func (x *FetchResPortRequest) GetNetworkPort() uint64 {
	if x != nil {
		return x.NetworkPort
	}
	return 0
}

func (x *FetchResPortRequest) GetState() *v11.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *FetchResPortRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type FetchResPortResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Items         []*BackofficePort       `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FetchResPortResponse) Reset() {
	*x = FetchResPortResponse{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchResPortResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchResPortResponse) ProtoMessage() {}

func (x *FetchResPortResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchResPortResponse.ProtoReflect.Descriptor instead.
func (*FetchResPortResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{16}
}

func (x *FetchResPortResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *FetchResPortResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *FetchResPortResponse) GetItems() []*BackofficePort {
	if x != nil {
		return x.Items
	}
	return nil
}

type UpdateResPortRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	IdResPort         string                 `protobuf:"bytes,1,opt,name=id_res_port,json=idResPort,proto3" json:"id_res_port,omitempty"`
	IdResAccount      string                 `protobuf:"bytes,2,opt,name=id_res_account,json=idResAccount,proto3" json:"id_res_account,omitempty"`
	TotalDeviceActive uint64                 `protobuf:"varint,3,opt,name=total_device_active,json=totalDeviceActive,proto3" json:"total_device_active,omitempty"`
	State             *v11.State             `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UpdateResPortRequest) Reset() {
	*x = UpdateResPortRequest{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateResPortRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateResPortRequest) ProtoMessage() {}

func (x *UpdateResPortRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateResPortRequest.ProtoReflect.Descriptor instead.
func (*UpdateResPortRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{17}
}

func (x *UpdateResPortRequest) GetIdResPort() string {
	if x != nil {
		return x.IdResPort
	}
	return ""
}

func (x *UpdateResPortRequest) GetIdResAccount() string {
	if x != nil {
		return x.IdResAccount
	}
	return ""
}

func (x *UpdateResPortRequest) GetTotalDeviceActive() uint64 {
	if x != nil {
		return x.TotalDeviceActive
	}
	return 0
}

func (x *UpdateResPortRequest) GetState() *v11.State {
	if x != nil {
		return x.State
	}
	return nil
}

type UpdateResPortResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateResPortResponse) Reset() {
	*x = UpdateResPortResponse{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateResPortResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateResPortResponse) ProtoMessage() {}

func (x *UpdateResPortResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateResPortResponse.ProtoReflect.Descriptor instead.
func (*UpdateResPortResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{18}
}

func (x *UpdateResPortResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type FetchResDeviceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdResPort     string                 `protobuf:"bytes,1,opt,name=id_res_port,json=idResPort,proto3" json:"id_res_port,omitempty"`
	NameSearch    string                 `protobuf:"bytes,2,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	IpSearch      string                 `protobuf:"bytes,3,opt,name=ip_search,json=ipSearch,proto3" json:"ip_search,omitempty"`
	State         *v11.State             `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	Pagination    *v11.PaginationRequest `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FetchResDeviceRequest) Reset() {
	*x = FetchResDeviceRequest{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchResDeviceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchResDeviceRequest) ProtoMessage() {}

func (x *FetchResDeviceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchResDeviceRequest.ProtoReflect.Descriptor instead.
func (*FetchResDeviceRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{19}
}

func (x *FetchResDeviceRequest) GetIdResPort() string {
	if x != nil {
		return x.IdResPort
	}
	return ""
}

func (x *FetchResDeviceRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *FetchResDeviceRequest) GetIpSearch() string {
	if x != nil {
		return x.IpSearch
	}
	return ""
}

func (x *FetchResDeviceRequest) GetState() *v11.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *FetchResDeviceRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type FetchResDeviceResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Error         *v1.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Items         []*BackofficeDevice     `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FetchResDeviceResponse) Reset() {
	*x = FetchResDeviceResponse{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchResDeviceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchResDeviceResponse) ProtoMessage() {}

func (x *FetchResDeviceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchResDeviceResponse.ProtoReflect.Descriptor instead.
func (*FetchResDeviceResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{20}
}

func (x *FetchResDeviceResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *FetchResDeviceResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *FetchResDeviceResponse) GetItems() []*BackofficeDevice {
	if x != nil {
		return x.Items
	}
	return nil
}

type Account struct {
	state        protoimpl.MessageState    `protogen:"open.v1"`
	IdResAccount string                    `protobuf:"bytes,1,opt,name=id_res_account,json=idResAccount,proto3" json:"id_res_account,omitempty"`
	Telco        *v13.BackofficeTelcoModel `protobuf:"bytes,2,opt,name=telco,proto3" json:"telco,omitempty"`
	// controlplane.location.v1.BackofficeLocationModel location = 3;
	Node          *AccountNode `protobuf:"bytes,4,opt,name=node,proto3" json:"node,omitempty"`
	Port          *AccountPort `protobuf:"bytes,5,opt,name=port,proto3" json:"port,omitempty"`
	Username      string       `protobuf:"bytes,6,opt,name=username,proto3" json:"username,omitempty"`
	Password      string       `protobuf:"bytes,7,opt,name=password,proto3" json:"password,omitempty"`
	IsActive      bool         `protobuf:"varint,8,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	State         bool         `protobuf:"varint,9,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Account) Reset() {
	*x = Account{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Account) ProtoMessage() {}

func (x *Account) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Account.ProtoReflect.Descriptor instead.
func (*Account) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{21}
}

func (x *Account) GetIdResAccount() string {
	if x != nil {
		return x.IdResAccount
	}
	return ""
}

func (x *Account) GetTelco() *v13.BackofficeTelcoModel {
	if x != nil {
		return x.Telco
	}
	return nil
}

func (x *Account) GetNode() *AccountNode {
	if x != nil {
		return x.Node
	}
	return nil
}

func (x *Account) GetPort() *AccountPort {
	if x != nil {
		return x.Port
	}
	return nil
}

func (x *Account) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *Account) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Account) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *Account) GetState() bool {
	if x != nil {
		return x.State
	}
	return false
}

type AccountNode struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdResNode     string                 `protobuf:"bytes,1,opt,name=id_res_node,json=idResNode,proto3" json:"id_res_node,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IsActive      bool                   `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountNode) Reset() {
	*x = AccountNode{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountNode) ProtoMessage() {}

func (x *AccountNode) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountNode.ProtoReflect.Descriptor instead.
func (*AccountNode) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{22}
}

func (x *AccountNode) GetIdResNode() string {
	if x != nil {
		return x.IdResNode
	}
	return ""
}

func (x *AccountNode) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AccountNode) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type AccountPort struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdResPort     string                 `protobuf:"bytes,1,opt,name=id_res_port,json=idResPort,proto3" json:"id_res_port,omitempty"`
	NetworkPort   uint64                 `protobuf:"varint,2,opt,name=network_port,json=networkPort,proto3" json:"network_port,omitempty"`
	IsActive      bool                   `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountPort) Reset() {
	*x = AccountPort{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountPort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountPort) ProtoMessage() {}

func (x *AccountPort) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountPort.ProtoReflect.Descriptor instead.
func (*AccountPort) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{23}
}

func (x *AccountPort) GetIdResPort() string {
	if x != nil {
		return x.IdResPort
	}
	return ""
}

func (x *AccountPort) GetNetworkPort() uint64 {
	if x != nil {
		return x.NetworkPort
	}
	return 0
}

func (x *AccountPort) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type Node struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	IdResNode string                 `protobuf:"bytes,1,opt,name=id_res_node,json=idResNode,proto3" json:"id_res_node,omitempty"`
	// controlplane.location.v1.BackofficeLocationModel location = 2;
	AesSecretB64  string                `protobuf:"bytes,3,opt,name=aes_secret_b64,json=aesSecretB64,proto3" json:"aes_secret_b64,omitempty"`
	IvSecretB64   string                `protobuf:"bytes,4,opt,name=iv_secret_b64,json=ivSecretB64,proto3" json:"iv_secret_b64,omitempty"`
	IdPowerDevice string                `protobuf:"bytes,5,opt,name=id_power_device,json=idPowerDevice,proto3" json:"id_power_device,omitempty"`
	Name          string                `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	Ipv4          string                `protobuf:"bytes,7,opt,name=ipv4,proto3" json:"ipv4,omitempty"`
	Ipv6          string                `protobuf:"bytes,8,opt,name=ipv6,proto3" json:"ipv6,omitempty"`
	ControlPort   int64                 `protobuf:"varint,9,opt,name=control_port,json=controlPort,proto3" json:"control_port,omitempty"`
	Status        v12.ResNodeStatus     `protobuf:"varint,10,opt,name=status,proto3,enum=algoenum.v1.ResNodeStatus" json:"status,omitempty"`
	PowerState    v12.ResNodePowerState `protobuf:"varint,11,opt,name=power_state,json=powerState,proto3,enum=algoenum.v1.ResNodePowerState" json:"power_state,omitempty"`
	SpeedInGb     uint64                `protobuf:"varint,12,opt,name=speed_in_gb,json=speedInGb,proto3" json:"speed_in_gb,omitempty"`
	IsActive      bool                  `protobuf:"varint,13,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	State         bool                  `protobuf:"varint,14,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Node) Reset() {
	*x = Node{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Node) ProtoMessage() {}

func (x *Node) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Node.ProtoReflect.Descriptor instead.
func (*Node) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{24}
}

func (x *Node) GetIdResNode() string {
	if x != nil {
		return x.IdResNode
	}
	return ""
}

func (x *Node) GetAesSecretB64() string {
	if x != nil {
		return x.AesSecretB64
	}
	return ""
}

func (x *Node) GetIvSecretB64() string {
	if x != nil {
		return x.IvSecretB64
	}
	return ""
}

func (x *Node) GetIdPowerDevice() string {
	if x != nil {
		return x.IdPowerDevice
	}
	return ""
}

func (x *Node) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Node) GetIpv4() string {
	if x != nil {
		return x.Ipv4
	}
	return ""
}

func (x *Node) GetIpv6() string {
	if x != nil {
		return x.Ipv6
	}
	return ""
}

func (x *Node) GetControlPort() int64 {
	if x != nil {
		return x.ControlPort
	}
	return 0
}

func (x *Node) GetStatus() v12.ResNodeStatus {
	if x != nil {
		return x.Status
	}
	return v12.ResNodeStatus(0)
}

func (x *Node) GetPowerState() v12.ResNodePowerState {
	if x != nil {
		return x.PowerState
	}
	return v12.ResNodePowerState(0)
}

func (x *Node) GetSpeedInGb() uint64 {
	if x != nil {
		return x.SpeedInGb
	}
	return 0
}

func (x *Node) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *Node) GetState() bool {
	if x != nil {
		return x.State
	}
	return false
}

type BackofficePort struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	IdResPort         string                 `protobuf:"bytes,1,opt,name=id_res_port,json=idResPort,proto3" json:"id_res_port,omitempty"`
	NetworkPort       uint64                 `protobuf:"varint,2,opt,name=network_port,json=networkPort,proto3" json:"network_port,omitempty"`
	TotalDeviceActive uint64                 `protobuf:"varint,3,opt,name=total_device_active,json=totalDeviceActive,proto3" json:"total_device_active,omitempty"`
	Account           *BackofficePortAccount `protobuf:"bytes,4,opt,name=account,proto3" json:"account,omitempty"`
	IsActive          bool                   `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	State             bool                   `protobuf:"varint,6,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BackofficePort) Reset() {
	*x = BackofficePort{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePort) ProtoMessage() {}

func (x *BackofficePort) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePort.ProtoReflect.Descriptor instead.
func (*BackofficePort) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{25}
}

func (x *BackofficePort) GetIdResPort() string {
	if x != nil {
		return x.IdResPort
	}
	return ""
}

func (x *BackofficePort) GetNetworkPort() uint64 {
	if x != nil {
		return x.NetworkPort
	}
	return 0
}

func (x *BackofficePort) GetTotalDeviceActive() uint64 {
	if x != nil {
		return x.TotalDeviceActive
	}
	return 0
}

func (x *BackofficePort) GetAccount() *BackofficePortAccount {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *BackofficePort) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *BackofficePort) GetState() bool {
	if x != nil {
		return x.State
	}
	return false
}

type BackofficePortAccount struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	IdResAccount  string                      `protobuf:"bytes,1,opt,name=id_res_account,json=idResAccount,proto3" json:"id_res_account,omitempty"`
	Telco         *BackofficePortAccountTelco `protobuf:"bytes,2,opt,name=telco,proto3" json:"telco,omitempty"`
	Username      string                      `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	IsActive      bool                        `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePortAccount) Reset() {
	*x = BackofficePortAccount{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePortAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePortAccount) ProtoMessage() {}

func (x *BackofficePortAccount) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePortAccount.ProtoReflect.Descriptor instead.
func (*BackofficePortAccount) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{26}
}

func (x *BackofficePortAccount) GetIdResAccount() string {
	if x != nil {
		return x.IdResAccount
	}
	return ""
}

func (x *BackofficePortAccount) GetTelco() *BackofficePortAccountTelco {
	if x != nil {
		return x.Telco
	}
	return nil
}

func (x *BackofficePortAccount) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *BackofficePortAccount) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficePortAccountTelco struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdTelco       string                 `protobuf:"bytes,1,opt,name=id_telco,json=idTelco,proto3" json:"id_telco,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IsActive      bool                   `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficePortAccountTelco) Reset() {
	*x = BackofficePortAccountTelco{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficePortAccountTelco) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficePortAccountTelco) ProtoMessage() {}

func (x *BackofficePortAccountTelco) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficePortAccountTelco.ProtoReflect.Descriptor instead.
func (*BackofficePortAccountTelco) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{27}
}

func (x *BackofficePortAccountTelco) GetIdTelco() string {
	if x != nil {
		return x.IdTelco
	}
	return ""
}

func (x *BackofficePortAccountTelco) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficePortAccountTelco) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficeDevice struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdResDevice   string                 `protobuf:"bytes,1,opt,name=id_res_device,json=idResDevice,proto3" json:"id_res_device,omitempty"`
	Node          *BackofficeDeviceNode  `protobuf:"bytes,2,opt,name=node,proto3" json:"node,omitempty"`
	Port          *BackofficeDevicePort  `protobuf:"bytes,3,opt,name=port,proto3" json:"port,omitempty"`
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	IpProxy       string                 `protobuf:"bytes,5,opt,name=ip_proxy,json=ipProxy,proto3" json:"ip_proxy,omitempty"`
	PortProxy     uint64                 `protobuf:"varint,6,opt,name=port_proxy,json=portProxy,proto3" json:"port_proxy,omitempty"`
	DeviceStatus  v12.ResDeviceStatus    `protobuf:"varint,7,opt,name=device_status,json=deviceStatus,proto3,enum=algoenum.v1.ResDeviceStatus" json:"device_status,omitempty"`
	Proxy         *BackofficeDeviceProxy `protobuf:"bytes,8,opt,name=proxy,proto3" json:"proxy,omitempty"`
	IsActive      bool                   `protobuf:"varint,9,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	State         bool                   `protobuf:"varint,10,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeDevice) Reset() {
	*x = BackofficeDevice{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeDevice) ProtoMessage() {}

func (x *BackofficeDevice) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeDevice.ProtoReflect.Descriptor instead.
func (*BackofficeDevice) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{28}
}

func (x *BackofficeDevice) GetIdResDevice() string {
	if x != nil {
		return x.IdResDevice
	}
	return ""
}

func (x *BackofficeDevice) GetNode() *BackofficeDeviceNode {
	if x != nil {
		return x.Node
	}
	return nil
}

func (x *BackofficeDevice) GetPort() *BackofficeDevicePort {
	if x != nil {
		return x.Port
	}
	return nil
}

func (x *BackofficeDevice) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeDevice) GetIpProxy() string {
	if x != nil {
		return x.IpProxy
	}
	return ""
}

func (x *BackofficeDevice) GetPortProxy() uint64 {
	if x != nil {
		return x.PortProxy
	}
	return 0
}

func (x *BackofficeDevice) GetDeviceStatus() v12.ResDeviceStatus {
	if x != nil {
		return x.DeviceStatus
	}
	return v12.ResDeviceStatus(0)
}

func (x *BackofficeDevice) GetProxy() *BackofficeDeviceProxy {
	if x != nil {
		return x.Proxy
	}
	return nil
}

func (x *BackofficeDevice) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *BackofficeDevice) GetState() bool {
	if x != nil {
		return x.State
	}
	return false
}

type BackofficeDeviceNode struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	IdResNode     string                        `protobuf:"bytes,1,opt,name=id_res_node,json=idResNode,proto3" json:"id_res_node,omitempty"`
	Name          string                        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Location      *BackofficeDeviceNodeLocation `protobuf:"bytes,3,opt,name=location,proto3" json:"location,omitempty"`
	IsActive      bool                          `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeDeviceNode) Reset() {
	*x = BackofficeDeviceNode{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeDeviceNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeDeviceNode) ProtoMessage() {}

func (x *BackofficeDeviceNode) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeDeviceNode.ProtoReflect.Descriptor instead.
func (*BackofficeDeviceNode) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{29}
}

func (x *BackofficeDeviceNode) GetIdResNode() string {
	if x != nil {
		return x.IdResNode
	}
	return ""
}

func (x *BackofficeDeviceNode) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeDeviceNode) GetLocation() *BackofficeDeviceNodeLocation {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *BackofficeDeviceNode) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficeDeviceNodeLocation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdLocation    string                 `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IsActive      bool                   `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeDeviceNodeLocation) Reset() {
	*x = BackofficeDeviceNodeLocation{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeDeviceNodeLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeDeviceNodeLocation) ProtoMessage() {}

func (x *BackofficeDeviceNodeLocation) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeDeviceNodeLocation.ProtoReflect.Descriptor instead.
func (*BackofficeDeviceNodeLocation) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{30}
}

func (x *BackofficeDeviceNodeLocation) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *BackofficeDeviceNodeLocation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeDeviceNodeLocation) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficeDevicePort struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	IdResPort     string                       `protobuf:"bytes,1,opt,name=id_res_port,json=idResPort,proto3" json:"id_res_port,omitempty"`
	NetworkPort   uint64                       `protobuf:"varint,2,opt,name=network_port,json=networkPort,proto3" json:"network_port,omitempty"`
	Account       *BackofficeDevicePortAccount `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	IsActive      bool                         `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeDevicePort) Reset() {
	*x = BackofficeDevicePort{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeDevicePort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeDevicePort) ProtoMessage() {}

func (x *BackofficeDevicePort) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeDevicePort.ProtoReflect.Descriptor instead.
func (*BackofficeDevicePort) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{31}
}

func (x *BackofficeDevicePort) GetIdResPort() string {
	if x != nil {
		return x.IdResPort
	}
	return ""
}

func (x *BackofficeDevicePort) GetNetworkPort() uint64 {
	if x != nil {
		return x.NetworkPort
	}
	return 0
}

func (x *BackofficeDevicePort) GetAccount() *BackofficeDevicePortAccount {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *BackofficeDevicePort) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficeDevicePortAccount struct {
	state         protoimpl.MessageState            `protogen:"open.v1"`
	IdResAccount  string                            `protobuf:"bytes,1,opt,name=id_res_account,json=idResAccount,proto3" json:"id_res_account,omitempty"`
	Telco         *BackofficeDevicePortAccountTelco `protobuf:"bytes,2,opt,name=telco,proto3" json:"telco,omitempty"`
	Username      string                            `protobuf:"bytes,4,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                            `protobuf:"bytes,5,opt,name=password,proto3" json:"password,omitempty"`
	IsActive      bool                              `protobuf:"varint,6,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeDevicePortAccount) Reset() {
	*x = BackofficeDevicePortAccount{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeDevicePortAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeDevicePortAccount) ProtoMessage() {}

func (x *BackofficeDevicePortAccount) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeDevicePortAccount.ProtoReflect.Descriptor instead.
func (*BackofficeDevicePortAccount) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{32}
}

func (x *BackofficeDevicePortAccount) GetIdResAccount() string {
	if x != nil {
		return x.IdResAccount
	}
	return ""
}

func (x *BackofficeDevicePortAccount) GetTelco() *BackofficeDevicePortAccountTelco {
	if x != nil {
		return x.Telco
	}
	return nil
}

func (x *BackofficeDevicePortAccount) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *BackofficeDevicePortAccount) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *BackofficeDevicePortAccount) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficeDevicePortAccountTelco struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdTelco       string                 `protobuf:"bytes,1,opt,name=id_telco,json=idTelco,proto3" json:"id_telco,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Asn           string                 `protobuf:"bytes,3,opt,name=asn,proto3" json:"asn,omitempty"`
	IsActive      bool                   `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeDevicePortAccountTelco) Reset() {
	*x = BackofficeDevicePortAccountTelco{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeDevicePortAccountTelco) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeDevicePortAccountTelco) ProtoMessage() {}

func (x *BackofficeDevicePortAccountTelco) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeDevicePortAccountTelco.ProtoReflect.Descriptor instead.
func (*BackofficeDevicePortAccountTelco) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{33}
}

func (x *BackofficeDevicePortAccountTelco) GetIdTelco() string {
	if x != nil {
		return x.IdTelco
	}
	return ""
}

func (x *BackofficeDevicePortAccountTelco) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeDevicePortAccountTelco) GetAsn() string {
	if x != nil {
		return x.Asn
	}
	return ""
}

func (x *BackofficeDevicePortAccountTelco) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type BackofficeDeviceProxy struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ipv4          string                 `protobuf:"bytes,1,opt,name=ipv4,proto3" json:"ipv4,omitempty"`
	Ipv6          string                 `protobuf:"bytes,2,opt,name=ipv6,proto3" json:"ipv6,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeDeviceProxy) Reset() {
	*x = BackofficeDeviceProxy{}
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeDeviceProxy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeDeviceProxy) ProtoMessage() {}

func (x *BackofficeDeviceProxy) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_backoffice_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeDeviceProxy.ProtoReflect.Descriptor instead.
func (*BackofficeDeviceProxy) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP(), []int{34}
}

func (x *BackofficeDeviceProxy) GetIpv4() string {
	if x != nil {
		return x.Ipv4
	}
	return ""
}

func (x *BackofficeDeviceProxy) GetIpv6() string {
	if x != nil {
		return x.Ipv6
	}
	return ""
}

var File_proxymanager_residential_v1_backoffice_proto protoreflect.FileDescriptor

const file_proxymanager_residential_v1_backoffice_proto_rawDesc = "" +
	"\n" +
	",proxymanager/residential/v1/backoffice.proto\x12\x1bproxymanager.residential.v1\x1a\x14utils/v1/utils.proto\x1a\x18errmsg/v1/errormsg.proto\x1a&proxymanager/telco/v1/backoffice.proto\x1a\x1aalgoenum/v1/res_node.proto\"P\n" +
	".BackofficeResidentialServiceRestartPortRequest\x12\x1e\n" +
	"\vid_res_port\x18\x01 \x01(\tR\tidResPort\"`\n" +
	"/BackofficeResidentialServiceRestartPortResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x8d\x01\n" +
	"\x17CreateResAccountRequest\x12\x19\n" +
	"\bid_telco\x18\x01 \x01(\tR\aidTelco\x12\x1f\n" +
	"\vid_location\x18\x02 \x01(\tR\n" +
	"idLocation\x12\x1a\n" +
	"\busername\x18\x03 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x04 \x01(\tR\bpassword\"I\n" +
	"\x18CreateResAccountResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xbb\x02\n" +
	"\x16FetchResAccountRequest\x12$\n" +
	"\x0eid_res_account\x18\x01 \x01(\tR\fidResAccount\x12\x1f\n" +
	"\vid_location\x18\x02 \x01(\tR\n" +
	"idLocation\x12'\n" +
	"\x0fusername_search\x18\x03 \x01(\tR\x0eusernameSearch\x12M\n" +
	"\abinding\x18\x04 \x01(\v23.proxymanager.residential.v1.FetchResAccountBindingR\abinding\x12%\n" +
	"\x05state\x18\x05 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x06 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"Q\n" +
	"\x16FetchResAccountBinding\x12\x16\n" +
	"\x06enable\x18\x01 \x01(\bR\x06enable\x12\x1f\n" +
	"\vhas_binding\x18\x02 \x01(\bR\n" +
	"hasBinding\"\xc2\x01\n" +
	"\x17FetchResAccountResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12:\n" +
	"\x05items\x18\x03 \x03(\v2$.proxymanager.residential.v1.AccountR\x05items\"\xda\x01\n" +
	"\x17UpdateResAccountRequest\x12$\n" +
	"\x0eid_res_account\x18\x01 \x01(\tR\fidResAccount\x12\x19\n" +
	"\bid_telco\x18\x02 \x01(\tR\aidTelco\x12\x1f\n" +
	"\vid_location\x18\x03 \x01(\tR\n" +
	"idLocation\x12\x1a\n" +
	"\busername\x18\x04 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x05 \x01(\tR\bpassword\x12%\n" +
	"\x05state\x18\x06 \x01(\v2\x0f.utils.v1.StateR\x05state\"I\n" +
	"\x18UpdateResAccountResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"s\n" +
	"\x14CreateResNodeRequest\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x12&\n" +
	"\x0fpower_device_id\x18\x02 \x01(\tR\rpowerDeviceId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\"F\n" +
	"\x15CreateResNodeResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xeb\x01\n" +
	"\x14UpdateResNodeRequest\x12\x1e\n" +
	"\vid_res_node\x18\x01 \x01(\tR\tidResNode\x12\x1f\n" +
	"\vid_location\x18\x02 \x01(\tR\n" +
	"idLocation\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12&\n" +
	"\x0fpower_device_id\x18\x04 \x01(\tR\rpowerDeviceId\x12/\n" +
	"\x14is_change_secret_key\x18\x05 \x01(\bR\x11isChangeSecretKey\x12%\n" +
	"\x05state\x18\x06 \x01(\v2\x0f.utils.v1.StateR\x05state\"F\n" +
	"\x15UpdateResNodeResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xe0\x02\n" +
	"\x13FetchResNodeRequest\x12\x1e\n" +
	"\vid_res_node\x18\x01 \x01(\tR\tidResNode\x12&\n" +
	"\x0fpower_device_id\x18\x02 \x01(\tR\rpowerDeviceId\x12\x1f\n" +
	"\vname_search\x18\x03 \x01(\tR\n" +
	"nameSearch\x12;\n" +
	"\vnode_status\x18\x04 \x01(\x0e2\x1a.algoenum.v1.ResNodeStatusR\n" +
	"nodeStatus\x12?\n" +
	"\vpower_state\x18\x05 \x01(\x0e2\x1e.algoenum.v1.ResNodePowerStateR\n" +
	"powerState\x12%\n" +
	"\x05state\x18\x06 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\a \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xbc\x01\n" +
	"\x14FetchResNodeResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x127\n" +
	"\x05items\x18\x03 \x03(\v2!.proxymanager.residential.v1.NodeR\x05items\"\xdc\x01\n" +
	"\x13FetchResPortRequest\x12\x1e\n" +
	"\vid_res_node\x18\x01 \x01(\tR\tidResNode\x12\x1e\n" +
	"\vid_res_port\x18\x02 \x01(\tR\tidResPort\x12!\n" +
	"\fnetwork_port\x18\x03 \x01(\x04R\vnetworkPort\x12%\n" +
	"\x05state\x18\x04 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xc6\x01\n" +
	"\x14FetchResPortResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12A\n" +
	"\x05items\x18\x03 \x03(\v2+.proxymanager.residential.v1.BackofficePortR\x05items\"\xb3\x01\n" +
	"\x14UpdateResPortRequest\x12\x1e\n" +
	"\vid_res_port\x18\x01 \x01(\tR\tidResPort\x12$\n" +
	"\x0eid_res_account\x18\x02 \x01(\tR\fidResAccount\x12.\n" +
	"\x13total_device_active\x18\x03 \x01(\x04R\x11totalDeviceActive\x12%\n" +
	"\x05state\x18\x06 \x01(\v2\x0f.utils.v1.StateR\x05state\"F\n" +
	"\x15UpdateResPortResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xd9\x01\n" +
	"\x15FetchResDeviceRequest\x12\x1e\n" +
	"\vid_res_port\x18\x01 \x01(\tR\tidResPort\x12\x1f\n" +
	"\vname_search\x18\x02 \x01(\tR\n" +
	"nameSearch\x12\x1b\n" +
	"\tip_search\x18\x03 \x01(\tR\bipSearch\x12%\n" +
	"\x05state\x18\x04 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xca\x01\n" +
	"\x16FetchResDeviceResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12C\n" +
	"\x05items\x18\x03 \x03(\v2-.proxymanager.residential.v1.BackofficeDeviceR\x05items\"\xd9\x02\n" +
	"\aAccount\x12$\n" +
	"\x0eid_res_account\x18\x01 \x01(\tR\fidResAccount\x12A\n" +
	"\x05telco\x18\x02 \x01(\v2+.proxymanager.telco.v1.BackofficeTelcoModelR\x05telco\x12<\n" +
	"\x04node\x18\x04 \x01(\v2(.proxymanager.residential.v1.AccountNodeR\x04node\x12<\n" +
	"\x04port\x18\x05 \x01(\v2(.proxymanager.residential.v1.AccountPortR\x04port\x12\x1a\n" +
	"\busername\x18\x06 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\a \x01(\tR\bpassword\x12\x1b\n" +
	"\tis_active\x18\b \x01(\bR\bisActive\x12\x14\n" +
	"\x05state\x18\t \x01(\bR\x05state\"^\n" +
	"\vAccountNode\x12\x1e\n" +
	"\vid_res_node\x18\x01 \x01(\tR\tidResNode\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1b\n" +
	"\tis_active\x18\x03 \x01(\bR\bisActive\"m\n" +
	"\vAccountPort\x12\x1e\n" +
	"\vid_res_port\x18\x01 \x01(\tR\tidResPort\x12!\n" +
	"\fnetwork_port\x18\x02 \x01(\x04R\vnetworkPort\x12\x1b\n" +
	"\tis_active\x18\x03 \x01(\bR\bisActive\"\xbf\x03\n" +
	"\x04Node\x12\x1e\n" +
	"\vid_res_node\x18\x01 \x01(\tR\tidResNode\x12$\n" +
	"\x0eaes_secret_b64\x18\x03 \x01(\tR\faesSecretB64\x12\"\n" +
	"\riv_secret_b64\x18\x04 \x01(\tR\vivSecretB64\x12&\n" +
	"\x0fid_power_device\x18\x05 \x01(\tR\ridPowerDevice\x12\x12\n" +
	"\x04name\x18\x06 \x01(\tR\x04name\x12\x12\n" +
	"\x04ipv4\x18\a \x01(\tR\x04ipv4\x12\x12\n" +
	"\x04ipv6\x18\b \x01(\tR\x04ipv6\x12!\n" +
	"\fcontrol_port\x18\t \x01(\x03R\vcontrolPort\x122\n" +
	"\x06status\x18\n" +
	" \x01(\x0e2\x1a.algoenum.v1.ResNodeStatusR\x06status\x12?\n" +
	"\vpower_state\x18\v \x01(\x0e2\x1e.algoenum.v1.ResNodePowerStateR\n" +
	"powerState\x12\x1e\n" +
	"\vspeed_in_gb\x18\f \x01(\x04R\tspeedInGb\x12\x1b\n" +
	"\tis_active\x18\r \x01(\bR\bisActive\x12\x14\n" +
	"\x05state\x18\x0e \x01(\bR\x05state\"\x84\x02\n" +
	"\x0eBackofficePort\x12\x1e\n" +
	"\vid_res_port\x18\x01 \x01(\tR\tidResPort\x12!\n" +
	"\fnetwork_port\x18\x02 \x01(\x04R\vnetworkPort\x12.\n" +
	"\x13total_device_active\x18\x03 \x01(\x04R\x11totalDeviceActive\x12L\n" +
	"\aaccount\x18\x04 \x01(\v22.proxymanager.residential.v1.BackofficePortAccountR\aaccount\x12\x1b\n" +
	"\tis_active\x18\x05 \x01(\bR\bisActive\x12\x14\n" +
	"\x05state\x18\x06 \x01(\bR\x05state\"\xc5\x01\n" +
	"\x15BackofficePortAccount\x12$\n" +
	"\x0eid_res_account\x18\x01 \x01(\tR\fidResAccount\x12M\n" +
	"\x05telco\x18\x02 \x01(\v27.proxymanager.residential.v1.BackofficePortAccountTelcoR\x05telco\x12\x1a\n" +
	"\busername\x18\x03 \x01(\tR\busername\x12\x1b\n" +
	"\tis_active\x18\x04 \x01(\bR\bisActive\"h\n" +
	"\x1aBackofficePortAccountTelco\x12\x19\n" +
	"\bid_telco\x18\x01 \x01(\tR\aidTelco\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1b\n" +
	"\tis_active\x18\x03 \x01(\bR\bisActive\"\xd2\x03\n" +
	"\x10BackofficeDevice\x12\"\n" +
	"\rid_res_device\x18\x01 \x01(\tR\vidResDevice\x12E\n" +
	"\x04node\x18\x02 \x01(\v21.proxymanager.residential.v1.BackofficeDeviceNodeR\x04node\x12E\n" +
	"\x04port\x18\x03 \x01(\v21.proxymanager.residential.v1.BackofficeDevicePortR\x04port\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x19\n" +
	"\bip_proxy\x18\x05 \x01(\tR\aipProxy\x12\x1d\n" +
	"\n" +
	"port_proxy\x18\x06 \x01(\x04R\tportProxy\x12A\n" +
	"\rdevice_status\x18\a \x01(\x0e2\x1c.algoenum.v1.ResDeviceStatusR\fdeviceStatus\x12H\n" +
	"\x05proxy\x18\b \x01(\v22.proxymanager.residential.v1.BackofficeDeviceProxyR\x05proxy\x12\x1b\n" +
	"\tis_active\x18\t \x01(\bR\bisActive\x12\x14\n" +
	"\x05state\x18\n" +
	" \x01(\bR\x05state\"\xbe\x01\n" +
	"\x14BackofficeDeviceNode\x12\x1e\n" +
	"\vid_res_node\x18\x01 \x01(\tR\tidResNode\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12U\n" +
	"\blocation\x18\x03 \x01(\v29.proxymanager.residential.v1.BackofficeDeviceNodeLocationR\blocation\x12\x1b\n" +
	"\tis_active\x18\x04 \x01(\bR\bisActive\"p\n" +
	"\x1cBackofficeDeviceNodeLocation\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1b\n" +
	"\tis_active\x18\x03 \x01(\bR\bisActive\"\xca\x01\n" +
	"\x14BackofficeDevicePort\x12\x1e\n" +
	"\vid_res_port\x18\x01 \x01(\tR\tidResPort\x12!\n" +
	"\fnetwork_port\x18\x02 \x01(\x04R\vnetworkPort\x12R\n" +
	"\aaccount\x18\x03 \x01(\v28.proxymanager.residential.v1.BackofficeDevicePortAccountR\aaccount\x12\x1b\n" +
	"\tis_active\x18\x04 \x01(\bR\bisActive\"\xed\x01\n" +
	"\x1bBackofficeDevicePortAccount\x12$\n" +
	"\x0eid_res_account\x18\x01 \x01(\tR\fidResAccount\x12S\n" +
	"\x05telco\x18\x02 \x01(\v2=.proxymanager.residential.v1.BackofficeDevicePortAccountTelcoR\x05telco\x12\x1a\n" +
	"\busername\x18\x04 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x05 \x01(\tR\bpassword\x12\x1b\n" +
	"\tis_active\x18\x06 \x01(\bR\bisActive\"\x80\x01\n" +
	" BackofficeDevicePortAccountTelco\x12\x19\n" +
	"\bid_telco\x18\x01 \x01(\tR\aidTelco\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x10\n" +
	"\x03asn\x18\x03 \x01(\tR\x03asn\x12\x1b\n" +
	"\tis_active\x18\x04 \x01(\bR\bisActive\"?\n" +
	"\x15BackofficeDeviceProxy\x12\x12\n" +
	"\x04ipv4\x18\x01 \x01(\tR\x04ipv4\x12\x12\n" +
	"\x04ipv6\x18\x02 \x01(\tR\x04ipv62\x96\n" +
	"\n" +
	"\x1cBackofficeResidentialService\x12|\n" +
	"\x0fFetchResAccount\x123.proxymanager.residential.v1.FetchResAccountRequest\x1a4.proxymanager.residential.v1.FetchResAccountResponse\x12\x7f\n" +
	"\x10CreateResAccount\x124.proxymanager.residential.v1.CreateResAccountRequest\x1a5.proxymanager.residential.v1.CreateResAccountResponse\x12\x7f\n" +
	"\x10UpdateResAccount\x124.proxymanager.residential.v1.UpdateResAccountRequest\x1a5.proxymanager.residential.v1.UpdateResAccountResponse\x12s\n" +
	"\fFetchResNode\x120.proxymanager.residential.v1.FetchResNodeRequest\x1a1.proxymanager.residential.v1.FetchResNodeResponse\x12v\n" +
	"\rCreateResNode\x121.proxymanager.residential.v1.CreateResNodeRequest\x1a2.proxymanager.residential.v1.CreateResNodeResponse\x12v\n" +
	"\rUpdateResNode\x121.proxymanager.residential.v1.UpdateResNodeRequest\x1a2.proxymanager.residential.v1.UpdateResNodeResponse\x12s\n" +
	"\fFetchResPort\x120.proxymanager.residential.v1.FetchResPortRequest\x1a1.proxymanager.residential.v1.FetchResPortResponse\x12v\n" +
	"\rUpdateResPort\x121.proxymanager.residential.v1.UpdateResPortRequest\x1a2.proxymanager.residential.v1.UpdateResPortResponse\x12y\n" +
	"\x0eFetchResDevice\x122.proxymanager.residential.v1.FetchResDeviceRequest\x1a3.proxymanager.residential.v1.FetchResDeviceResponse\x12\xa8\x01\n" +
	"\vRestartPort\x12K.proxymanager.residential.v1.BackofficeResidentialServiceRestartPortRequest\x1aL.proxymanager.residential.v1.BackofficeResidentialServiceRestartPortResponseBZZXgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/residential/v1;residentialv1b\x06proto3"

var (
	file_proxymanager_residential_v1_backoffice_proto_rawDescOnce sync.Once
	file_proxymanager_residential_v1_backoffice_proto_rawDescData []byte
)

func file_proxymanager_residential_v1_backoffice_proto_rawDescGZIP() []byte {
	file_proxymanager_residential_v1_backoffice_proto_rawDescOnce.Do(func() {
		file_proxymanager_residential_v1_backoffice_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_residential_v1_backoffice_proto_rawDesc), len(file_proxymanager_residential_v1_backoffice_proto_rawDesc)))
	})
	return file_proxymanager_residential_v1_backoffice_proto_rawDescData
}

var file_proxymanager_residential_v1_backoffice_proto_msgTypes = make([]protoimpl.MessageInfo, 35)
var file_proxymanager_residential_v1_backoffice_proto_goTypes = []any{
	(*BackofficeResidentialServiceRestartPortRequest)(nil),  // 0: proxymanager.residential.v1.BackofficeResidentialServiceRestartPortRequest
	(*BackofficeResidentialServiceRestartPortResponse)(nil), // 1: proxymanager.residential.v1.BackofficeResidentialServiceRestartPortResponse
	(*CreateResAccountRequest)(nil),                         // 2: proxymanager.residential.v1.CreateResAccountRequest
	(*CreateResAccountResponse)(nil),                        // 3: proxymanager.residential.v1.CreateResAccountResponse
	(*FetchResAccountRequest)(nil),                          // 4: proxymanager.residential.v1.FetchResAccountRequest
	(*FetchResAccountBinding)(nil),                          // 5: proxymanager.residential.v1.FetchResAccountBinding
	(*FetchResAccountResponse)(nil),                         // 6: proxymanager.residential.v1.FetchResAccountResponse
	(*UpdateResAccountRequest)(nil),                         // 7: proxymanager.residential.v1.UpdateResAccountRequest
	(*UpdateResAccountResponse)(nil),                        // 8: proxymanager.residential.v1.UpdateResAccountResponse
	(*CreateResNodeRequest)(nil),                            // 9: proxymanager.residential.v1.CreateResNodeRequest
	(*CreateResNodeResponse)(nil),                           // 10: proxymanager.residential.v1.CreateResNodeResponse
	(*UpdateResNodeRequest)(nil),                            // 11: proxymanager.residential.v1.UpdateResNodeRequest
	(*UpdateResNodeResponse)(nil),                           // 12: proxymanager.residential.v1.UpdateResNodeResponse
	(*FetchResNodeRequest)(nil),                             // 13: proxymanager.residential.v1.FetchResNodeRequest
	(*FetchResNodeResponse)(nil),                            // 14: proxymanager.residential.v1.FetchResNodeResponse
	(*FetchResPortRequest)(nil),                             // 15: proxymanager.residential.v1.FetchResPortRequest
	(*FetchResPortResponse)(nil),                            // 16: proxymanager.residential.v1.FetchResPortResponse
	(*UpdateResPortRequest)(nil),                            // 17: proxymanager.residential.v1.UpdateResPortRequest
	(*UpdateResPortResponse)(nil),                           // 18: proxymanager.residential.v1.UpdateResPortResponse
	(*FetchResDeviceRequest)(nil),                           // 19: proxymanager.residential.v1.FetchResDeviceRequest
	(*FetchResDeviceResponse)(nil),                          // 20: proxymanager.residential.v1.FetchResDeviceResponse
	(*Account)(nil),                                         // 21: proxymanager.residential.v1.Account
	(*AccountNode)(nil),                                     // 22: proxymanager.residential.v1.AccountNode
	(*AccountPort)(nil),                                     // 23: proxymanager.residential.v1.AccountPort
	(*Node)(nil),                                            // 24: proxymanager.residential.v1.Node
	(*BackofficePort)(nil),                                  // 25: proxymanager.residential.v1.BackofficePort
	(*BackofficePortAccount)(nil),                           // 26: proxymanager.residential.v1.BackofficePortAccount
	(*BackofficePortAccountTelco)(nil),                      // 27: proxymanager.residential.v1.BackofficePortAccountTelco
	(*BackofficeDevice)(nil),                                // 28: proxymanager.residential.v1.BackofficeDevice
	(*BackofficeDeviceNode)(nil),                            // 29: proxymanager.residential.v1.BackofficeDeviceNode
	(*BackofficeDeviceNodeLocation)(nil),                    // 30: proxymanager.residential.v1.BackofficeDeviceNodeLocation
	(*BackofficeDevicePort)(nil),                            // 31: proxymanager.residential.v1.BackofficeDevicePort
	(*BackofficeDevicePortAccount)(nil),                     // 32: proxymanager.residential.v1.BackofficeDevicePortAccount
	(*BackofficeDevicePortAccountTelco)(nil),                // 33: proxymanager.residential.v1.BackofficeDevicePortAccountTelco
	(*BackofficeDeviceProxy)(nil),                           // 34: proxymanager.residential.v1.BackofficeDeviceProxy
	(*v1.ErrorMessage)(nil),                                 // 35: errmsg.v1.ErrorMessage
	(*v11.State)(nil),                                       // 36: utils.v1.State
	(*v11.PaginationRequest)(nil),                           // 37: utils.v1.PaginationRequest
	(*v11.PaginationResponse)(nil),                          // 38: utils.v1.PaginationResponse
	(v12.ResNodeStatus)(0),                                  // 39: algoenum.v1.ResNodeStatus
	(v12.ResNodePowerState)(0),                              // 40: algoenum.v1.ResNodePowerState
	(*v13.BackofficeTelcoModel)(nil),                        // 41: proxymanager.telco.v1.BackofficeTelcoModel
	(v12.ResDeviceStatus)(0),                                // 42: algoenum.v1.ResDeviceStatus
}
var file_proxymanager_residential_v1_backoffice_proto_depIdxs = []int32{
	35, // 0: proxymanager.residential.v1.BackofficeResidentialServiceRestartPortResponse.error:type_name -> errmsg.v1.ErrorMessage
	35, // 1: proxymanager.residential.v1.CreateResAccountResponse.error:type_name -> errmsg.v1.ErrorMessage
	5,  // 2: proxymanager.residential.v1.FetchResAccountRequest.binding:type_name -> proxymanager.residential.v1.FetchResAccountBinding
	36, // 3: proxymanager.residential.v1.FetchResAccountRequest.state:type_name -> utils.v1.State
	37, // 4: proxymanager.residential.v1.FetchResAccountRequest.pagination:type_name -> utils.v1.PaginationRequest
	35, // 5: proxymanager.residential.v1.FetchResAccountResponse.error:type_name -> errmsg.v1.ErrorMessage
	38, // 6: proxymanager.residential.v1.FetchResAccountResponse.pagination:type_name -> utils.v1.PaginationResponse
	21, // 7: proxymanager.residential.v1.FetchResAccountResponse.items:type_name -> proxymanager.residential.v1.Account
	36, // 8: proxymanager.residential.v1.UpdateResAccountRequest.state:type_name -> utils.v1.State
	35, // 9: proxymanager.residential.v1.UpdateResAccountResponse.error:type_name -> errmsg.v1.ErrorMessage
	35, // 10: proxymanager.residential.v1.CreateResNodeResponse.error:type_name -> errmsg.v1.ErrorMessage
	36, // 11: proxymanager.residential.v1.UpdateResNodeRequest.state:type_name -> utils.v1.State
	35, // 12: proxymanager.residential.v1.UpdateResNodeResponse.error:type_name -> errmsg.v1.ErrorMessage
	39, // 13: proxymanager.residential.v1.FetchResNodeRequest.node_status:type_name -> algoenum.v1.ResNodeStatus
	40, // 14: proxymanager.residential.v1.FetchResNodeRequest.power_state:type_name -> algoenum.v1.ResNodePowerState
	36, // 15: proxymanager.residential.v1.FetchResNodeRequest.state:type_name -> utils.v1.State
	37, // 16: proxymanager.residential.v1.FetchResNodeRequest.pagination:type_name -> utils.v1.PaginationRequest
	35, // 17: proxymanager.residential.v1.FetchResNodeResponse.error:type_name -> errmsg.v1.ErrorMessage
	38, // 18: proxymanager.residential.v1.FetchResNodeResponse.pagination:type_name -> utils.v1.PaginationResponse
	24, // 19: proxymanager.residential.v1.FetchResNodeResponse.items:type_name -> proxymanager.residential.v1.Node
	36, // 20: proxymanager.residential.v1.FetchResPortRequest.state:type_name -> utils.v1.State
	37, // 21: proxymanager.residential.v1.FetchResPortRequest.pagination:type_name -> utils.v1.PaginationRequest
	35, // 22: proxymanager.residential.v1.FetchResPortResponse.error:type_name -> errmsg.v1.ErrorMessage
	38, // 23: proxymanager.residential.v1.FetchResPortResponse.pagination:type_name -> utils.v1.PaginationResponse
	25, // 24: proxymanager.residential.v1.FetchResPortResponse.items:type_name -> proxymanager.residential.v1.BackofficePort
	36, // 25: proxymanager.residential.v1.UpdateResPortRequest.state:type_name -> utils.v1.State
	35, // 26: proxymanager.residential.v1.UpdateResPortResponse.error:type_name -> errmsg.v1.ErrorMessage
	36, // 27: proxymanager.residential.v1.FetchResDeviceRequest.state:type_name -> utils.v1.State
	37, // 28: proxymanager.residential.v1.FetchResDeviceRequest.pagination:type_name -> utils.v1.PaginationRequest
	35, // 29: proxymanager.residential.v1.FetchResDeviceResponse.error:type_name -> errmsg.v1.ErrorMessage
	38, // 30: proxymanager.residential.v1.FetchResDeviceResponse.pagination:type_name -> utils.v1.PaginationResponse
	28, // 31: proxymanager.residential.v1.FetchResDeviceResponse.items:type_name -> proxymanager.residential.v1.BackofficeDevice
	41, // 32: proxymanager.residential.v1.Account.telco:type_name -> proxymanager.telco.v1.BackofficeTelcoModel
	22, // 33: proxymanager.residential.v1.Account.node:type_name -> proxymanager.residential.v1.AccountNode
	23, // 34: proxymanager.residential.v1.Account.port:type_name -> proxymanager.residential.v1.AccountPort
	39, // 35: proxymanager.residential.v1.Node.status:type_name -> algoenum.v1.ResNodeStatus
	40, // 36: proxymanager.residential.v1.Node.power_state:type_name -> algoenum.v1.ResNodePowerState
	26, // 37: proxymanager.residential.v1.BackofficePort.account:type_name -> proxymanager.residential.v1.BackofficePortAccount
	27, // 38: proxymanager.residential.v1.BackofficePortAccount.telco:type_name -> proxymanager.residential.v1.BackofficePortAccountTelco
	29, // 39: proxymanager.residential.v1.BackofficeDevice.node:type_name -> proxymanager.residential.v1.BackofficeDeviceNode
	31, // 40: proxymanager.residential.v1.BackofficeDevice.port:type_name -> proxymanager.residential.v1.BackofficeDevicePort
	42, // 41: proxymanager.residential.v1.BackofficeDevice.device_status:type_name -> algoenum.v1.ResDeviceStatus
	34, // 42: proxymanager.residential.v1.BackofficeDevice.proxy:type_name -> proxymanager.residential.v1.BackofficeDeviceProxy
	30, // 43: proxymanager.residential.v1.BackofficeDeviceNode.location:type_name -> proxymanager.residential.v1.BackofficeDeviceNodeLocation
	32, // 44: proxymanager.residential.v1.BackofficeDevicePort.account:type_name -> proxymanager.residential.v1.BackofficeDevicePortAccount
	33, // 45: proxymanager.residential.v1.BackofficeDevicePortAccount.telco:type_name -> proxymanager.residential.v1.BackofficeDevicePortAccountTelco
	4,  // 46: proxymanager.residential.v1.BackofficeResidentialService.FetchResAccount:input_type -> proxymanager.residential.v1.FetchResAccountRequest
	2,  // 47: proxymanager.residential.v1.BackofficeResidentialService.CreateResAccount:input_type -> proxymanager.residential.v1.CreateResAccountRequest
	7,  // 48: proxymanager.residential.v1.BackofficeResidentialService.UpdateResAccount:input_type -> proxymanager.residential.v1.UpdateResAccountRequest
	13, // 49: proxymanager.residential.v1.BackofficeResidentialService.FetchResNode:input_type -> proxymanager.residential.v1.FetchResNodeRequest
	9,  // 50: proxymanager.residential.v1.BackofficeResidentialService.CreateResNode:input_type -> proxymanager.residential.v1.CreateResNodeRequest
	11, // 51: proxymanager.residential.v1.BackofficeResidentialService.UpdateResNode:input_type -> proxymanager.residential.v1.UpdateResNodeRequest
	15, // 52: proxymanager.residential.v1.BackofficeResidentialService.FetchResPort:input_type -> proxymanager.residential.v1.FetchResPortRequest
	17, // 53: proxymanager.residential.v1.BackofficeResidentialService.UpdateResPort:input_type -> proxymanager.residential.v1.UpdateResPortRequest
	19, // 54: proxymanager.residential.v1.BackofficeResidentialService.FetchResDevice:input_type -> proxymanager.residential.v1.FetchResDeviceRequest
	0,  // 55: proxymanager.residential.v1.BackofficeResidentialService.RestartPort:input_type -> proxymanager.residential.v1.BackofficeResidentialServiceRestartPortRequest
	6,  // 56: proxymanager.residential.v1.BackofficeResidentialService.FetchResAccount:output_type -> proxymanager.residential.v1.FetchResAccountResponse
	3,  // 57: proxymanager.residential.v1.BackofficeResidentialService.CreateResAccount:output_type -> proxymanager.residential.v1.CreateResAccountResponse
	8,  // 58: proxymanager.residential.v1.BackofficeResidentialService.UpdateResAccount:output_type -> proxymanager.residential.v1.UpdateResAccountResponse
	14, // 59: proxymanager.residential.v1.BackofficeResidentialService.FetchResNode:output_type -> proxymanager.residential.v1.FetchResNodeResponse
	10, // 60: proxymanager.residential.v1.BackofficeResidentialService.CreateResNode:output_type -> proxymanager.residential.v1.CreateResNodeResponse
	12, // 61: proxymanager.residential.v1.BackofficeResidentialService.UpdateResNode:output_type -> proxymanager.residential.v1.UpdateResNodeResponse
	16, // 62: proxymanager.residential.v1.BackofficeResidentialService.FetchResPort:output_type -> proxymanager.residential.v1.FetchResPortResponse
	18, // 63: proxymanager.residential.v1.BackofficeResidentialService.UpdateResPort:output_type -> proxymanager.residential.v1.UpdateResPortResponse
	20, // 64: proxymanager.residential.v1.BackofficeResidentialService.FetchResDevice:output_type -> proxymanager.residential.v1.FetchResDeviceResponse
	1,  // 65: proxymanager.residential.v1.BackofficeResidentialService.RestartPort:output_type -> proxymanager.residential.v1.BackofficeResidentialServiceRestartPortResponse
	56, // [56:66] is the sub-list for method output_type
	46, // [46:56] is the sub-list for method input_type
	46, // [46:46] is the sub-list for extension type_name
	46, // [46:46] is the sub-list for extension extendee
	0,  // [0:46] is the sub-list for field type_name
}

func init() { file_proxymanager_residential_v1_backoffice_proto_init() }
func file_proxymanager_residential_v1_backoffice_proto_init() {
	if File_proxymanager_residential_v1_backoffice_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_residential_v1_backoffice_proto_rawDesc), len(file_proxymanager_residential_v1_backoffice_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   35,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_residential_v1_backoffice_proto_goTypes,
		DependencyIndexes: file_proxymanager_residential_v1_backoffice_proto_depIdxs,
		MessageInfos:      file_proxymanager_residential_v1_backoffice_proto_msgTypes,
	}.Build()
	File_proxymanager_residential_v1_backoffice_proto = out.File
	file_proxymanager_residential_v1_backoffice_proto_goTypes = nil
	file_proxymanager_residential_v1_backoffice_proto_depIdxs = nil
}
