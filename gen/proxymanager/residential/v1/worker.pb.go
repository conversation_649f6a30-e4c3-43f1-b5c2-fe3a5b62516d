// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/residential/v1/worker.proto

package residentialv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/hop/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WorkerResidentialServiceHealthCheckRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkerResidentialServiceHealthCheckRequest) Reset() {
	*x = WorkerResidentialServiceHealthCheckRequest{}
	mi := &file_proxymanager_residential_v1_worker_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkerResidentialServiceHealthCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerResidentialServiceHealthCheckRequest) ProtoMessage() {}

func (x *WorkerResidentialServiceHealthCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_worker_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerResidentialServiceHealthCheckRequest.ProtoReflect.Descriptor instead.
func (*WorkerResidentialServiceHealthCheckRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_worker_proto_rawDescGZIP(), []int{0}
}

type WorkerResidentialServiceHealthCheckResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkerResidentialServiceHealthCheckResponse) Reset() {
	*x = WorkerResidentialServiceHealthCheckResponse{}
	mi := &file_proxymanager_residential_v1_worker_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkerResidentialServiceHealthCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerResidentialServiceHealthCheckResponse) ProtoMessage() {}

func (x *WorkerResidentialServiceHealthCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_worker_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerResidentialServiceHealthCheckResponse.ProtoReflect.Descriptor instead.
func (*WorkerResidentialServiceHealthCheckResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_worker_proto_rawDescGZIP(), []int{1}
}

func (x *WorkerResidentialServiceHealthCheckResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type WorkerResidentialServiceConfigRouteRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdResDevice   string                 `protobuf:"bytes,1,opt,name=id_res_device,json=idResDevice,proto3" json:"id_res_device,omitempty"`
	DeviceName    string                 `protobuf:"bytes,2,opt,name=device_name,json=deviceName,proto3" json:"device_name,omitempty"`
	IsAdd         bool                   `protobuf:"varint,3,opt,name=is_add,json=isAdd,proto3" json:"is_add,omitempty"`
	Hop           *v11.Hop               `protobuf:"bytes,4,opt,name=hop,proto3" json:"hop,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkerResidentialServiceConfigRouteRequest) Reset() {
	*x = WorkerResidentialServiceConfigRouteRequest{}
	mi := &file_proxymanager_residential_v1_worker_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkerResidentialServiceConfigRouteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerResidentialServiceConfigRouteRequest) ProtoMessage() {}

func (x *WorkerResidentialServiceConfigRouteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_worker_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerResidentialServiceConfigRouteRequest.ProtoReflect.Descriptor instead.
func (*WorkerResidentialServiceConfigRouteRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_worker_proto_rawDescGZIP(), []int{2}
}

func (x *WorkerResidentialServiceConfigRouteRequest) GetIdResDevice() string {
	if x != nil {
		return x.IdResDevice
	}
	return ""
}

func (x *WorkerResidentialServiceConfigRouteRequest) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *WorkerResidentialServiceConfigRouteRequest) GetIsAdd() bool {
	if x != nil {
		return x.IsAdd
	}
	return false
}

func (x *WorkerResidentialServiceConfigRouteRequest) GetHop() *v11.Hop {
	if x != nil {
		return x.Hop
	}
	return nil
}

type WorkerResidentialServiceConfigRouteResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,2,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkerResidentialServiceConfigRouteResponse) Reset() {
	*x = WorkerResidentialServiceConfigRouteResponse{}
	mi := &file_proxymanager_residential_v1_worker_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkerResidentialServiceConfigRouteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerResidentialServiceConfigRouteResponse) ProtoMessage() {}

func (x *WorkerResidentialServiceConfigRouteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_worker_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerResidentialServiceConfigRouteResponse.ProtoReflect.Descriptor instead.
func (*WorkerResidentialServiceConfigRouteResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_worker_proto_rawDescGZIP(), []int{3}
}

func (x *WorkerResidentialServiceConfigRouteResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_proxymanager_residential_v1_worker_proto protoreflect.FileDescriptor

const file_proxymanager_residential_v1_worker_proto_rawDesc = "" +
	"\n" +
	"(proxymanager/residential/v1/worker.proto\x12\x1bproxymanager.residential.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x10hop/v1/hop.proto\",\n" +
	"*WorkerResidentialServiceHealthCheckRequest\"\\\n" +
	"+WorkerResidentialServiceHealthCheckResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xa7\x01\n" +
	"*WorkerResidentialServiceConfigRouteRequest\x12\"\n" +
	"\rid_res_device\x18\x01 \x01(\tR\vidResDevice\x12\x1f\n" +
	"\vdevice_name\x18\x02 \x01(\tR\n" +
	"deviceName\x12\x15\n" +
	"\x06is_add\x18\x03 \x01(\bR\x05isAdd\x12\x1d\n" +
	"\x03hop\x18\x04 \x01(\v2\v.hop.v1.HopR\x03hop\"\\\n" +
	"+WorkerResidentialServiceConfigRouteResponse\x12-\n" +
	"\x05error\x18\x02 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error2\xe0\x02\n" +
	"\x18WorkerResidentialService\x12\xa0\x01\n" +
	"\vHealthCheck\x12G.proxymanager.residential.v1.WorkerResidentialServiceHealthCheckRequest\x1aH.proxymanager.residential.v1.WorkerResidentialServiceHealthCheckResponse\x12\xa0\x01\n" +
	"\vConfigRoute\x12G.proxymanager.residential.v1.WorkerResidentialServiceConfigRouteRequest\x1aH.proxymanager.residential.v1.WorkerResidentialServiceConfigRouteResponseBZZXgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/residential/v1;residentialv1b\x06proto3"

var (
	file_proxymanager_residential_v1_worker_proto_rawDescOnce sync.Once
	file_proxymanager_residential_v1_worker_proto_rawDescData []byte
)

func file_proxymanager_residential_v1_worker_proto_rawDescGZIP() []byte {
	file_proxymanager_residential_v1_worker_proto_rawDescOnce.Do(func() {
		file_proxymanager_residential_v1_worker_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_residential_v1_worker_proto_rawDesc), len(file_proxymanager_residential_v1_worker_proto_rawDesc)))
	})
	return file_proxymanager_residential_v1_worker_proto_rawDescData
}

var file_proxymanager_residential_v1_worker_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_proxymanager_residential_v1_worker_proto_goTypes = []any{
	(*WorkerResidentialServiceHealthCheckRequest)(nil),  // 0: proxymanager.residential.v1.WorkerResidentialServiceHealthCheckRequest
	(*WorkerResidentialServiceHealthCheckResponse)(nil), // 1: proxymanager.residential.v1.WorkerResidentialServiceHealthCheckResponse
	(*WorkerResidentialServiceConfigRouteRequest)(nil),  // 2: proxymanager.residential.v1.WorkerResidentialServiceConfigRouteRequest
	(*WorkerResidentialServiceConfigRouteResponse)(nil), // 3: proxymanager.residential.v1.WorkerResidentialServiceConfigRouteResponse
	(*v1.ErrorMessage)(nil),                             // 4: errmsg.v1.ErrorMessage
	(*v11.Hop)(nil),                                     // 5: hop.v1.Hop
}
var file_proxymanager_residential_v1_worker_proto_depIdxs = []int32{
	4, // 0: proxymanager.residential.v1.WorkerResidentialServiceHealthCheckResponse.error:type_name -> errmsg.v1.ErrorMessage
	5, // 1: proxymanager.residential.v1.WorkerResidentialServiceConfigRouteRequest.hop:type_name -> hop.v1.Hop
	4, // 2: proxymanager.residential.v1.WorkerResidentialServiceConfigRouteResponse.error:type_name -> errmsg.v1.ErrorMessage
	0, // 3: proxymanager.residential.v1.WorkerResidentialService.HealthCheck:input_type -> proxymanager.residential.v1.WorkerResidentialServiceHealthCheckRequest
	2, // 4: proxymanager.residential.v1.WorkerResidentialService.ConfigRoute:input_type -> proxymanager.residential.v1.WorkerResidentialServiceConfigRouteRequest
	1, // 5: proxymanager.residential.v1.WorkerResidentialService.HealthCheck:output_type -> proxymanager.residential.v1.WorkerResidentialServiceHealthCheckResponse
	3, // 6: proxymanager.residential.v1.WorkerResidentialService.ConfigRoute:output_type -> proxymanager.residential.v1.WorkerResidentialServiceConfigRouteResponse
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_proxymanager_residential_v1_worker_proto_init() }
func file_proxymanager_residential_v1_worker_proto_init() {
	if File_proxymanager_residential_v1_worker_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_residential_v1_worker_proto_rawDesc), len(file_proxymanager_residential_v1_worker_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_residential_v1_worker_proto_goTypes,
		DependencyIndexes: file_proxymanager_residential_v1_worker_proto_depIdxs,
		MessageInfos:      file_proxymanager_residential_v1_worker_proto_msgTypes,
	}.Build()
	File_proxymanager_residential_v1_worker_proto = out.File
	file_proxymanager_residential_v1_worker_proto_goTypes = nil
	file_proxymanager_residential_v1_worker_proto_depIdxs = nil
}
