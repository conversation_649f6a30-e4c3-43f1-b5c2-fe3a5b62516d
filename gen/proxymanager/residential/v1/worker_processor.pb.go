// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/residential/v1/worker_processor.proto

package residentialv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WorkerProcessorResidentialServiceHealthCheckRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkerProcessorResidentialServiceHealthCheckRequest) Reset() {
	*x = WorkerProcessorResidentialServiceHealthCheckRequest{}
	mi := &file_proxymanager_residential_v1_worker_processor_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkerProcessorResidentialServiceHealthCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerProcessorResidentialServiceHealthCheckRequest) ProtoMessage() {}

func (x *WorkerProcessorResidentialServiceHealthCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_worker_processor_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerProcessorResidentialServiceHealthCheckRequest.ProtoReflect.Descriptor instead.
func (*WorkerProcessorResidentialServiceHealthCheckRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_worker_processor_proto_rawDescGZIP(), []int{0}
}

type WorkerProcessorResidentialServiceHealthCheckResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkerProcessorResidentialServiceHealthCheckResponse) Reset() {
	*x = WorkerProcessorResidentialServiceHealthCheckResponse{}
	mi := &file_proxymanager_residential_v1_worker_processor_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkerProcessorResidentialServiceHealthCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerProcessorResidentialServiceHealthCheckResponse) ProtoMessage() {}

func (x *WorkerProcessorResidentialServiceHealthCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_worker_processor_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerProcessorResidentialServiceHealthCheckResponse.ProtoReflect.Descriptor instead.
func (*WorkerProcessorResidentialServiceHealthCheckResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_worker_processor_proto_rawDescGZIP(), []int{1}
}

func (x *WorkerProcessorResidentialServiceHealthCheckResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type WorkerProcessorResidentialServiceRestartNodeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkerProcessorResidentialServiceRestartNodeRequest) Reset() {
	*x = WorkerProcessorResidentialServiceRestartNodeRequest{}
	mi := &file_proxymanager_residential_v1_worker_processor_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkerProcessorResidentialServiceRestartNodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerProcessorResidentialServiceRestartNodeRequest) ProtoMessage() {}

func (x *WorkerProcessorResidentialServiceRestartNodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_worker_processor_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerProcessorResidentialServiceRestartNodeRequest.ProtoReflect.Descriptor instead.
func (*WorkerProcessorResidentialServiceRestartNodeRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_worker_processor_proto_rawDescGZIP(), []int{2}
}

type WorkerProcessorResidentialServiceRestartNodeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkerProcessorResidentialServiceRestartNodeResponse) Reset() {
	*x = WorkerProcessorResidentialServiceRestartNodeResponse{}
	mi := &file_proxymanager_residential_v1_worker_processor_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkerProcessorResidentialServiceRestartNodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerProcessorResidentialServiceRestartNodeResponse) ProtoMessage() {}

func (x *WorkerProcessorResidentialServiceRestartNodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_worker_processor_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerProcessorResidentialServiceRestartNodeResponse.ProtoReflect.Descriptor instead.
func (*WorkerProcessorResidentialServiceRestartNodeResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_worker_processor_proto_rawDescGZIP(), []int{3}
}

func (x *WorkerProcessorResidentialServiceRestartNodeResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type WorkerProcessorResidentialServiceRestartPortRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdResPort     string                 `protobuf:"bytes,1,opt,name=id_res_port,json=idResPort,proto3" json:"id_res_port,omitempty"`
	NetworkPort   int64                  `protobuf:"varint,2,opt,name=network_port,json=networkPort,proto3" json:"network_port,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkerProcessorResidentialServiceRestartPortRequest) Reset() {
	*x = WorkerProcessorResidentialServiceRestartPortRequest{}
	mi := &file_proxymanager_residential_v1_worker_processor_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkerProcessorResidentialServiceRestartPortRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerProcessorResidentialServiceRestartPortRequest) ProtoMessage() {}

func (x *WorkerProcessorResidentialServiceRestartPortRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_worker_processor_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerProcessorResidentialServiceRestartPortRequest.ProtoReflect.Descriptor instead.
func (*WorkerProcessorResidentialServiceRestartPortRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_worker_processor_proto_rawDescGZIP(), []int{4}
}

func (x *WorkerProcessorResidentialServiceRestartPortRequest) GetIdResPort() string {
	if x != nil {
		return x.IdResPort
	}
	return ""
}

func (x *WorkerProcessorResidentialServiceRestartPortRequest) GetNetworkPort() int64 {
	if x != nil {
		return x.NetworkPort
	}
	return 0
}

type WorkerProcessorResidentialServiceRestartPortResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkerProcessorResidentialServiceRestartPortResponse) Reset() {
	*x = WorkerProcessorResidentialServiceRestartPortResponse{}
	mi := &file_proxymanager_residential_v1_worker_processor_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkerProcessorResidentialServiceRestartPortResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerProcessorResidentialServiceRestartPortResponse) ProtoMessage() {}

func (x *WorkerProcessorResidentialServiceRestartPortResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_worker_processor_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerProcessorResidentialServiceRestartPortResponse.ProtoReflect.Descriptor instead.
func (*WorkerProcessorResidentialServiceRestartPortResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_worker_processor_proto_rawDescGZIP(), []int{5}
}

func (x *WorkerProcessorResidentialServiceRestartPortResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type WorkerProcessorResidentialServiceRestartDeviceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdResDevice   string                 `protobuf:"bytes,1,opt,name=id_res_device,json=idResDevice,proto3" json:"id_res_device,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkerProcessorResidentialServiceRestartDeviceRequest) Reset() {
	*x = WorkerProcessorResidentialServiceRestartDeviceRequest{}
	mi := &file_proxymanager_residential_v1_worker_processor_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkerProcessorResidentialServiceRestartDeviceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerProcessorResidentialServiceRestartDeviceRequest) ProtoMessage() {}

func (x *WorkerProcessorResidentialServiceRestartDeviceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_worker_processor_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerProcessorResidentialServiceRestartDeviceRequest.ProtoReflect.Descriptor instead.
func (*WorkerProcessorResidentialServiceRestartDeviceRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_worker_processor_proto_rawDescGZIP(), []int{6}
}

func (x *WorkerProcessorResidentialServiceRestartDeviceRequest) GetIdResDevice() string {
	if x != nil {
		return x.IdResDevice
	}
	return ""
}

type WorkerProcessorResidentialServiceRestartDeviceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WorkerProcessorResidentialServiceRestartDeviceResponse) Reset() {
	*x = WorkerProcessorResidentialServiceRestartDeviceResponse{}
	mi := &file_proxymanager_residential_v1_worker_processor_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WorkerProcessorResidentialServiceRestartDeviceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkerProcessorResidentialServiceRestartDeviceResponse) ProtoMessage() {}

func (x *WorkerProcessorResidentialServiceRestartDeviceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_residential_v1_worker_processor_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkerProcessorResidentialServiceRestartDeviceResponse.ProtoReflect.Descriptor instead.
func (*WorkerProcessorResidentialServiceRestartDeviceResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_residential_v1_worker_processor_proto_rawDescGZIP(), []int{7}
}

func (x *WorkerProcessorResidentialServiceRestartDeviceResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_proxymanager_residential_v1_worker_processor_proto protoreflect.FileDescriptor

const file_proxymanager_residential_v1_worker_processor_proto_rawDesc = "" +
	"\n" +
	"2proxymanager/residential/v1/worker_processor.proto\x12\x1bproxymanager.residential.v1\x1a\x18errmsg/v1/errormsg.proto\"5\n" +
	"3WorkerProcessorResidentialServiceHealthCheckRequest\"e\n" +
	"4WorkerProcessorResidentialServiceHealthCheckResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"5\n" +
	"3WorkerProcessorResidentialServiceRestartNodeRequest\"e\n" +
	"4WorkerProcessorResidentialServiceRestartNodeResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"x\n" +
	"3WorkerProcessorResidentialServiceRestartPortRequest\x12\x1e\n" +
	"\vid_res_port\x18\x01 \x01(\tR\tidResPort\x12!\n" +
	"\fnetwork_port\x18\x02 \x01(\x03R\vnetworkPort\"e\n" +
	"4WorkerProcessorResidentialServiceRestartPortResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"[\n" +
	"5WorkerProcessorResidentialServiceRestartDeviceRequest\x12\"\n" +
	"\rid_res_device\x18\x01 \x01(\tR\vidResDevice\"g\n" +
	"6WorkerProcessorResidentialServiceRestartDeviceResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error2\xfd\x05\n" +
	"!WorkerProcessorResidentialService\x12\xb2\x01\n" +
	"\vHealthCheck\x12P.proxymanager.residential.v1.WorkerProcessorResidentialServiceHealthCheckRequest\x1aQ.proxymanager.residential.v1.WorkerProcessorResidentialServiceHealthCheckResponse\x12\xb2\x01\n" +
	"\vRestartNode\x12P.proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartNodeRequest\x1aQ.proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartNodeResponse\x12\xb2\x01\n" +
	"\vRestartPort\x12P.proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartPortRequest\x1aQ.proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartPortResponse\x12\xb8\x01\n" +
	"\rRestartDevice\x12R.proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartDeviceRequest\x1aS.proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartDeviceResponseBZZXgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/residential/v1;residentialv1b\x06proto3"

var (
	file_proxymanager_residential_v1_worker_processor_proto_rawDescOnce sync.Once
	file_proxymanager_residential_v1_worker_processor_proto_rawDescData []byte
)

func file_proxymanager_residential_v1_worker_processor_proto_rawDescGZIP() []byte {
	file_proxymanager_residential_v1_worker_processor_proto_rawDescOnce.Do(func() {
		file_proxymanager_residential_v1_worker_processor_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_residential_v1_worker_processor_proto_rawDesc), len(file_proxymanager_residential_v1_worker_processor_proto_rawDesc)))
	})
	return file_proxymanager_residential_v1_worker_processor_proto_rawDescData
}

var file_proxymanager_residential_v1_worker_processor_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_proxymanager_residential_v1_worker_processor_proto_goTypes = []any{
	(*WorkerProcessorResidentialServiceHealthCheckRequest)(nil),    // 0: proxymanager.residential.v1.WorkerProcessorResidentialServiceHealthCheckRequest
	(*WorkerProcessorResidentialServiceHealthCheckResponse)(nil),   // 1: proxymanager.residential.v1.WorkerProcessorResidentialServiceHealthCheckResponse
	(*WorkerProcessorResidentialServiceRestartNodeRequest)(nil),    // 2: proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartNodeRequest
	(*WorkerProcessorResidentialServiceRestartNodeResponse)(nil),   // 3: proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartNodeResponse
	(*WorkerProcessorResidentialServiceRestartPortRequest)(nil),    // 4: proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartPortRequest
	(*WorkerProcessorResidentialServiceRestartPortResponse)(nil),   // 5: proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartPortResponse
	(*WorkerProcessorResidentialServiceRestartDeviceRequest)(nil),  // 6: proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartDeviceRequest
	(*WorkerProcessorResidentialServiceRestartDeviceResponse)(nil), // 7: proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartDeviceResponse
	(*v1.ErrorMessage)(nil), // 8: errmsg.v1.ErrorMessage
}
var file_proxymanager_residential_v1_worker_processor_proto_depIdxs = []int32{
	8, // 0: proxymanager.residential.v1.WorkerProcessorResidentialServiceHealthCheckResponse.error:type_name -> errmsg.v1.ErrorMessage
	8, // 1: proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartNodeResponse.error:type_name -> errmsg.v1.ErrorMessage
	8, // 2: proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartPortResponse.error:type_name -> errmsg.v1.ErrorMessage
	8, // 3: proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartDeviceResponse.error:type_name -> errmsg.v1.ErrorMessage
	0, // 4: proxymanager.residential.v1.WorkerProcessorResidentialService.HealthCheck:input_type -> proxymanager.residential.v1.WorkerProcessorResidentialServiceHealthCheckRequest
	2, // 5: proxymanager.residential.v1.WorkerProcessorResidentialService.RestartNode:input_type -> proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartNodeRequest
	4, // 6: proxymanager.residential.v1.WorkerProcessorResidentialService.RestartPort:input_type -> proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartPortRequest
	6, // 7: proxymanager.residential.v1.WorkerProcessorResidentialService.RestartDevice:input_type -> proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartDeviceRequest
	1, // 8: proxymanager.residential.v1.WorkerProcessorResidentialService.HealthCheck:output_type -> proxymanager.residential.v1.WorkerProcessorResidentialServiceHealthCheckResponse
	3, // 9: proxymanager.residential.v1.WorkerProcessorResidentialService.RestartNode:output_type -> proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartNodeResponse
	5, // 10: proxymanager.residential.v1.WorkerProcessorResidentialService.RestartPort:output_type -> proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartPortResponse
	7, // 11: proxymanager.residential.v1.WorkerProcessorResidentialService.RestartDevice:output_type -> proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartDeviceResponse
	8, // [8:12] is the sub-list for method output_type
	4, // [4:8] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_proxymanager_residential_v1_worker_processor_proto_init() }
func file_proxymanager_residential_v1_worker_processor_proto_init() {
	if File_proxymanager_residential_v1_worker_processor_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_residential_v1_worker_processor_proto_rawDesc), len(file_proxymanager_residential_v1_worker_processor_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_residential_v1_worker_processor_proto_goTypes,
		DependencyIndexes: file_proxymanager_residential_v1_worker_processor_proto_depIdxs,
		MessageInfos:      file_proxymanager_residential_v1_worker_processor_proto_msgTypes,
	}.Build()
	File_proxymanager_residential_v1_worker_processor_proto = out.File
	file_proxymanager_residential_v1_worker_processor_proto_goTypes = nil
	file_proxymanager_residential_v1_worker_processor_proto_depIdxs = nil
}
