// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/residential/v1/backoffice.proto

package residentialv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/residential/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackofficeResidentialServiceName is the fully-qualified name of the BackofficeResidentialService
	// service.
	BackofficeResidentialServiceName = "proxymanager.residential.v1.BackofficeResidentialService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackofficeResidentialServiceFetchResAccountProcedure is the fully-qualified name of the
	// BackofficeResidentialService's FetchResAccount RPC.
	BackofficeResidentialServiceFetchResAccountProcedure = "/proxymanager.residential.v1.BackofficeResidentialService/FetchResAccount"
	// BackofficeResidentialServiceCreateResAccountProcedure is the fully-qualified name of the
	// BackofficeResidentialService's CreateResAccount RPC.
	BackofficeResidentialServiceCreateResAccountProcedure = "/proxymanager.residential.v1.BackofficeResidentialService/CreateResAccount"
	// BackofficeResidentialServiceUpdateResAccountProcedure is the fully-qualified name of the
	// BackofficeResidentialService's UpdateResAccount RPC.
	BackofficeResidentialServiceUpdateResAccountProcedure = "/proxymanager.residential.v1.BackofficeResidentialService/UpdateResAccount"
	// BackofficeResidentialServiceFetchResNodeProcedure is the fully-qualified name of the
	// BackofficeResidentialService's FetchResNode RPC.
	BackofficeResidentialServiceFetchResNodeProcedure = "/proxymanager.residential.v1.BackofficeResidentialService/FetchResNode"
	// BackofficeResidentialServiceCreateResNodeProcedure is the fully-qualified name of the
	// BackofficeResidentialService's CreateResNode RPC.
	BackofficeResidentialServiceCreateResNodeProcedure = "/proxymanager.residential.v1.BackofficeResidentialService/CreateResNode"
	// BackofficeResidentialServiceUpdateResNodeProcedure is the fully-qualified name of the
	// BackofficeResidentialService's UpdateResNode RPC.
	BackofficeResidentialServiceUpdateResNodeProcedure = "/proxymanager.residential.v1.BackofficeResidentialService/UpdateResNode"
	// BackofficeResidentialServiceFetchResPortProcedure is the fully-qualified name of the
	// BackofficeResidentialService's FetchResPort RPC.
	BackofficeResidentialServiceFetchResPortProcedure = "/proxymanager.residential.v1.BackofficeResidentialService/FetchResPort"
	// BackofficeResidentialServiceUpdateResPortProcedure is the fully-qualified name of the
	// BackofficeResidentialService's UpdateResPort RPC.
	BackofficeResidentialServiceUpdateResPortProcedure = "/proxymanager.residential.v1.BackofficeResidentialService/UpdateResPort"
	// BackofficeResidentialServiceFetchResDeviceProcedure is the fully-qualified name of the
	// BackofficeResidentialService's FetchResDevice RPC.
	BackofficeResidentialServiceFetchResDeviceProcedure = "/proxymanager.residential.v1.BackofficeResidentialService/FetchResDevice"
	// BackofficeResidentialServiceRestartPortProcedure is the fully-qualified name of the
	// BackofficeResidentialService's RestartPort RPC.
	BackofficeResidentialServiceRestartPortProcedure = "/proxymanager.residential.v1.BackofficeResidentialService/RestartPort"
)

// BackofficeResidentialServiceClient is a client for the
// proxymanager.residential.v1.BackofficeResidentialService service.
type BackofficeResidentialServiceClient interface {
	FetchResAccount(context.Context, *connect.Request[v1.FetchResAccountRequest]) (*connect.Response[v1.FetchResAccountResponse], error)
	CreateResAccount(context.Context, *connect.Request[v1.CreateResAccountRequest]) (*connect.Response[v1.CreateResAccountResponse], error)
	UpdateResAccount(context.Context, *connect.Request[v1.UpdateResAccountRequest]) (*connect.Response[v1.UpdateResAccountResponse], error)
	FetchResNode(context.Context, *connect.Request[v1.FetchResNodeRequest]) (*connect.Response[v1.FetchResNodeResponse], error)
	CreateResNode(context.Context, *connect.Request[v1.CreateResNodeRequest]) (*connect.Response[v1.CreateResNodeResponse], error)
	UpdateResNode(context.Context, *connect.Request[v1.UpdateResNodeRequest]) (*connect.Response[v1.UpdateResNodeResponse], error)
	FetchResPort(context.Context, *connect.Request[v1.FetchResPortRequest]) (*connect.Response[v1.FetchResPortResponse], error)
	UpdateResPort(context.Context, *connect.Request[v1.UpdateResPortRequest]) (*connect.Response[v1.UpdateResPortResponse], error)
	FetchResDevice(context.Context, *connect.Request[v1.FetchResDeviceRequest]) (*connect.Response[v1.FetchResDeviceResponse], error)
	RestartPort(context.Context, *connect.Request[v1.BackofficeResidentialServiceRestartPortRequest]) (*connect.Response[v1.BackofficeResidentialServiceRestartPortResponse], error)
}

// NewBackofficeResidentialServiceClient constructs a client for the
// proxymanager.residential.v1.BackofficeResidentialService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackofficeResidentialServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackofficeResidentialServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backofficeResidentialServiceMethods := v1.File_proxymanager_residential_v1_backoffice_proto.Services().ByName("BackofficeResidentialService").Methods()
	return &backofficeResidentialServiceClient{
		fetchResAccount: connect.NewClient[v1.FetchResAccountRequest, v1.FetchResAccountResponse](
			httpClient,
			baseURL+BackofficeResidentialServiceFetchResAccountProcedure,
			connect.WithSchema(backofficeResidentialServiceMethods.ByName("FetchResAccount")),
			connect.WithClientOptions(opts...),
		),
		createResAccount: connect.NewClient[v1.CreateResAccountRequest, v1.CreateResAccountResponse](
			httpClient,
			baseURL+BackofficeResidentialServiceCreateResAccountProcedure,
			connect.WithSchema(backofficeResidentialServiceMethods.ByName("CreateResAccount")),
			connect.WithClientOptions(opts...),
		),
		updateResAccount: connect.NewClient[v1.UpdateResAccountRequest, v1.UpdateResAccountResponse](
			httpClient,
			baseURL+BackofficeResidentialServiceUpdateResAccountProcedure,
			connect.WithSchema(backofficeResidentialServiceMethods.ByName("UpdateResAccount")),
			connect.WithClientOptions(opts...),
		),
		fetchResNode: connect.NewClient[v1.FetchResNodeRequest, v1.FetchResNodeResponse](
			httpClient,
			baseURL+BackofficeResidentialServiceFetchResNodeProcedure,
			connect.WithSchema(backofficeResidentialServiceMethods.ByName("FetchResNode")),
			connect.WithClientOptions(opts...),
		),
		createResNode: connect.NewClient[v1.CreateResNodeRequest, v1.CreateResNodeResponse](
			httpClient,
			baseURL+BackofficeResidentialServiceCreateResNodeProcedure,
			connect.WithSchema(backofficeResidentialServiceMethods.ByName("CreateResNode")),
			connect.WithClientOptions(opts...),
		),
		updateResNode: connect.NewClient[v1.UpdateResNodeRequest, v1.UpdateResNodeResponse](
			httpClient,
			baseURL+BackofficeResidentialServiceUpdateResNodeProcedure,
			connect.WithSchema(backofficeResidentialServiceMethods.ByName("UpdateResNode")),
			connect.WithClientOptions(opts...),
		),
		fetchResPort: connect.NewClient[v1.FetchResPortRequest, v1.FetchResPortResponse](
			httpClient,
			baseURL+BackofficeResidentialServiceFetchResPortProcedure,
			connect.WithSchema(backofficeResidentialServiceMethods.ByName("FetchResPort")),
			connect.WithClientOptions(opts...),
		),
		updateResPort: connect.NewClient[v1.UpdateResPortRequest, v1.UpdateResPortResponse](
			httpClient,
			baseURL+BackofficeResidentialServiceUpdateResPortProcedure,
			connect.WithSchema(backofficeResidentialServiceMethods.ByName("UpdateResPort")),
			connect.WithClientOptions(opts...),
		),
		fetchResDevice: connect.NewClient[v1.FetchResDeviceRequest, v1.FetchResDeviceResponse](
			httpClient,
			baseURL+BackofficeResidentialServiceFetchResDeviceProcedure,
			connect.WithSchema(backofficeResidentialServiceMethods.ByName("FetchResDevice")),
			connect.WithClientOptions(opts...),
		),
		restartPort: connect.NewClient[v1.BackofficeResidentialServiceRestartPortRequest, v1.BackofficeResidentialServiceRestartPortResponse](
			httpClient,
			baseURL+BackofficeResidentialServiceRestartPortProcedure,
			connect.WithSchema(backofficeResidentialServiceMethods.ByName("RestartPort")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backofficeResidentialServiceClient implements BackofficeResidentialServiceClient.
type backofficeResidentialServiceClient struct {
	fetchResAccount  *connect.Client[v1.FetchResAccountRequest, v1.FetchResAccountResponse]
	createResAccount *connect.Client[v1.CreateResAccountRequest, v1.CreateResAccountResponse]
	updateResAccount *connect.Client[v1.UpdateResAccountRequest, v1.UpdateResAccountResponse]
	fetchResNode     *connect.Client[v1.FetchResNodeRequest, v1.FetchResNodeResponse]
	createResNode    *connect.Client[v1.CreateResNodeRequest, v1.CreateResNodeResponse]
	updateResNode    *connect.Client[v1.UpdateResNodeRequest, v1.UpdateResNodeResponse]
	fetchResPort     *connect.Client[v1.FetchResPortRequest, v1.FetchResPortResponse]
	updateResPort    *connect.Client[v1.UpdateResPortRequest, v1.UpdateResPortResponse]
	fetchResDevice   *connect.Client[v1.FetchResDeviceRequest, v1.FetchResDeviceResponse]
	restartPort      *connect.Client[v1.BackofficeResidentialServiceRestartPortRequest, v1.BackofficeResidentialServiceRestartPortResponse]
}

// FetchResAccount calls proxymanager.residential.v1.BackofficeResidentialService.FetchResAccount.
func (c *backofficeResidentialServiceClient) FetchResAccount(ctx context.Context, req *connect.Request[v1.FetchResAccountRequest]) (*connect.Response[v1.FetchResAccountResponse], error) {
	return c.fetchResAccount.CallUnary(ctx, req)
}

// CreateResAccount calls proxymanager.residential.v1.BackofficeResidentialService.CreateResAccount.
func (c *backofficeResidentialServiceClient) CreateResAccount(ctx context.Context, req *connect.Request[v1.CreateResAccountRequest]) (*connect.Response[v1.CreateResAccountResponse], error) {
	return c.createResAccount.CallUnary(ctx, req)
}

// UpdateResAccount calls proxymanager.residential.v1.BackofficeResidentialService.UpdateResAccount.
func (c *backofficeResidentialServiceClient) UpdateResAccount(ctx context.Context, req *connect.Request[v1.UpdateResAccountRequest]) (*connect.Response[v1.UpdateResAccountResponse], error) {
	return c.updateResAccount.CallUnary(ctx, req)
}

// FetchResNode calls proxymanager.residential.v1.BackofficeResidentialService.FetchResNode.
func (c *backofficeResidentialServiceClient) FetchResNode(ctx context.Context, req *connect.Request[v1.FetchResNodeRequest]) (*connect.Response[v1.FetchResNodeResponse], error) {
	return c.fetchResNode.CallUnary(ctx, req)
}

// CreateResNode calls proxymanager.residential.v1.BackofficeResidentialService.CreateResNode.
func (c *backofficeResidentialServiceClient) CreateResNode(ctx context.Context, req *connect.Request[v1.CreateResNodeRequest]) (*connect.Response[v1.CreateResNodeResponse], error) {
	return c.createResNode.CallUnary(ctx, req)
}

// UpdateResNode calls proxymanager.residential.v1.BackofficeResidentialService.UpdateResNode.
func (c *backofficeResidentialServiceClient) UpdateResNode(ctx context.Context, req *connect.Request[v1.UpdateResNodeRequest]) (*connect.Response[v1.UpdateResNodeResponse], error) {
	return c.updateResNode.CallUnary(ctx, req)
}

// FetchResPort calls proxymanager.residential.v1.BackofficeResidentialService.FetchResPort.
func (c *backofficeResidentialServiceClient) FetchResPort(ctx context.Context, req *connect.Request[v1.FetchResPortRequest]) (*connect.Response[v1.FetchResPortResponse], error) {
	return c.fetchResPort.CallUnary(ctx, req)
}

// UpdateResPort calls proxymanager.residential.v1.BackofficeResidentialService.UpdateResPort.
func (c *backofficeResidentialServiceClient) UpdateResPort(ctx context.Context, req *connect.Request[v1.UpdateResPortRequest]) (*connect.Response[v1.UpdateResPortResponse], error) {
	return c.updateResPort.CallUnary(ctx, req)
}

// FetchResDevice calls proxymanager.residential.v1.BackofficeResidentialService.FetchResDevice.
func (c *backofficeResidentialServiceClient) FetchResDevice(ctx context.Context, req *connect.Request[v1.FetchResDeviceRequest]) (*connect.Response[v1.FetchResDeviceResponse], error) {
	return c.fetchResDevice.CallUnary(ctx, req)
}

// RestartPort calls proxymanager.residential.v1.BackofficeResidentialService.RestartPort.
func (c *backofficeResidentialServiceClient) RestartPort(ctx context.Context, req *connect.Request[v1.BackofficeResidentialServiceRestartPortRequest]) (*connect.Response[v1.BackofficeResidentialServiceRestartPortResponse], error) {
	return c.restartPort.CallUnary(ctx, req)
}

// BackofficeResidentialServiceHandler is an implementation of the
// proxymanager.residential.v1.BackofficeResidentialService service.
type BackofficeResidentialServiceHandler interface {
	FetchResAccount(context.Context, *connect.Request[v1.FetchResAccountRequest]) (*connect.Response[v1.FetchResAccountResponse], error)
	CreateResAccount(context.Context, *connect.Request[v1.CreateResAccountRequest]) (*connect.Response[v1.CreateResAccountResponse], error)
	UpdateResAccount(context.Context, *connect.Request[v1.UpdateResAccountRequest]) (*connect.Response[v1.UpdateResAccountResponse], error)
	FetchResNode(context.Context, *connect.Request[v1.FetchResNodeRequest]) (*connect.Response[v1.FetchResNodeResponse], error)
	CreateResNode(context.Context, *connect.Request[v1.CreateResNodeRequest]) (*connect.Response[v1.CreateResNodeResponse], error)
	UpdateResNode(context.Context, *connect.Request[v1.UpdateResNodeRequest]) (*connect.Response[v1.UpdateResNodeResponse], error)
	FetchResPort(context.Context, *connect.Request[v1.FetchResPortRequest]) (*connect.Response[v1.FetchResPortResponse], error)
	UpdateResPort(context.Context, *connect.Request[v1.UpdateResPortRequest]) (*connect.Response[v1.UpdateResPortResponse], error)
	FetchResDevice(context.Context, *connect.Request[v1.FetchResDeviceRequest]) (*connect.Response[v1.FetchResDeviceResponse], error)
	RestartPort(context.Context, *connect.Request[v1.BackofficeResidentialServiceRestartPortRequest]) (*connect.Response[v1.BackofficeResidentialServiceRestartPortResponse], error)
}

// NewBackofficeResidentialServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackofficeResidentialServiceHandler(svc BackofficeResidentialServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backofficeResidentialServiceMethods := v1.File_proxymanager_residential_v1_backoffice_proto.Services().ByName("BackofficeResidentialService").Methods()
	backofficeResidentialServiceFetchResAccountHandler := connect.NewUnaryHandler(
		BackofficeResidentialServiceFetchResAccountProcedure,
		svc.FetchResAccount,
		connect.WithSchema(backofficeResidentialServiceMethods.ByName("FetchResAccount")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeResidentialServiceCreateResAccountHandler := connect.NewUnaryHandler(
		BackofficeResidentialServiceCreateResAccountProcedure,
		svc.CreateResAccount,
		connect.WithSchema(backofficeResidentialServiceMethods.ByName("CreateResAccount")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeResidentialServiceUpdateResAccountHandler := connect.NewUnaryHandler(
		BackofficeResidentialServiceUpdateResAccountProcedure,
		svc.UpdateResAccount,
		connect.WithSchema(backofficeResidentialServiceMethods.ByName("UpdateResAccount")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeResidentialServiceFetchResNodeHandler := connect.NewUnaryHandler(
		BackofficeResidentialServiceFetchResNodeProcedure,
		svc.FetchResNode,
		connect.WithSchema(backofficeResidentialServiceMethods.ByName("FetchResNode")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeResidentialServiceCreateResNodeHandler := connect.NewUnaryHandler(
		BackofficeResidentialServiceCreateResNodeProcedure,
		svc.CreateResNode,
		connect.WithSchema(backofficeResidentialServiceMethods.ByName("CreateResNode")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeResidentialServiceUpdateResNodeHandler := connect.NewUnaryHandler(
		BackofficeResidentialServiceUpdateResNodeProcedure,
		svc.UpdateResNode,
		connect.WithSchema(backofficeResidentialServiceMethods.ByName("UpdateResNode")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeResidentialServiceFetchResPortHandler := connect.NewUnaryHandler(
		BackofficeResidentialServiceFetchResPortProcedure,
		svc.FetchResPort,
		connect.WithSchema(backofficeResidentialServiceMethods.ByName("FetchResPort")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeResidentialServiceUpdateResPortHandler := connect.NewUnaryHandler(
		BackofficeResidentialServiceUpdateResPortProcedure,
		svc.UpdateResPort,
		connect.WithSchema(backofficeResidentialServiceMethods.ByName("UpdateResPort")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeResidentialServiceFetchResDeviceHandler := connect.NewUnaryHandler(
		BackofficeResidentialServiceFetchResDeviceProcedure,
		svc.FetchResDevice,
		connect.WithSchema(backofficeResidentialServiceMethods.ByName("FetchResDevice")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeResidentialServiceRestartPortHandler := connect.NewUnaryHandler(
		BackofficeResidentialServiceRestartPortProcedure,
		svc.RestartPort,
		connect.WithSchema(backofficeResidentialServiceMethods.ByName("RestartPort")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.residential.v1.BackofficeResidentialService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackofficeResidentialServiceFetchResAccountProcedure:
			backofficeResidentialServiceFetchResAccountHandler.ServeHTTP(w, r)
		case BackofficeResidentialServiceCreateResAccountProcedure:
			backofficeResidentialServiceCreateResAccountHandler.ServeHTTP(w, r)
		case BackofficeResidentialServiceUpdateResAccountProcedure:
			backofficeResidentialServiceUpdateResAccountHandler.ServeHTTP(w, r)
		case BackofficeResidentialServiceFetchResNodeProcedure:
			backofficeResidentialServiceFetchResNodeHandler.ServeHTTP(w, r)
		case BackofficeResidentialServiceCreateResNodeProcedure:
			backofficeResidentialServiceCreateResNodeHandler.ServeHTTP(w, r)
		case BackofficeResidentialServiceUpdateResNodeProcedure:
			backofficeResidentialServiceUpdateResNodeHandler.ServeHTTP(w, r)
		case BackofficeResidentialServiceFetchResPortProcedure:
			backofficeResidentialServiceFetchResPortHandler.ServeHTTP(w, r)
		case BackofficeResidentialServiceUpdateResPortProcedure:
			backofficeResidentialServiceUpdateResPortHandler.ServeHTTP(w, r)
		case BackofficeResidentialServiceFetchResDeviceProcedure:
			backofficeResidentialServiceFetchResDeviceHandler.ServeHTTP(w, r)
		case BackofficeResidentialServiceRestartPortProcedure:
			backofficeResidentialServiceRestartPortHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackofficeResidentialServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackofficeResidentialServiceHandler struct{}

func (UnimplementedBackofficeResidentialServiceHandler) FetchResAccount(context.Context, *connect.Request[v1.FetchResAccountRequest]) (*connect.Response[v1.FetchResAccountResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.BackofficeResidentialService.FetchResAccount is not implemented"))
}

func (UnimplementedBackofficeResidentialServiceHandler) CreateResAccount(context.Context, *connect.Request[v1.CreateResAccountRequest]) (*connect.Response[v1.CreateResAccountResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.BackofficeResidentialService.CreateResAccount is not implemented"))
}

func (UnimplementedBackofficeResidentialServiceHandler) UpdateResAccount(context.Context, *connect.Request[v1.UpdateResAccountRequest]) (*connect.Response[v1.UpdateResAccountResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.BackofficeResidentialService.UpdateResAccount is not implemented"))
}

func (UnimplementedBackofficeResidentialServiceHandler) FetchResNode(context.Context, *connect.Request[v1.FetchResNodeRequest]) (*connect.Response[v1.FetchResNodeResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.BackofficeResidentialService.FetchResNode is not implemented"))
}

func (UnimplementedBackofficeResidentialServiceHandler) CreateResNode(context.Context, *connect.Request[v1.CreateResNodeRequest]) (*connect.Response[v1.CreateResNodeResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.BackofficeResidentialService.CreateResNode is not implemented"))
}

func (UnimplementedBackofficeResidentialServiceHandler) UpdateResNode(context.Context, *connect.Request[v1.UpdateResNodeRequest]) (*connect.Response[v1.UpdateResNodeResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.BackofficeResidentialService.UpdateResNode is not implemented"))
}

func (UnimplementedBackofficeResidentialServiceHandler) FetchResPort(context.Context, *connect.Request[v1.FetchResPortRequest]) (*connect.Response[v1.FetchResPortResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.BackofficeResidentialService.FetchResPort is not implemented"))
}

func (UnimplementedBackofficeResidentialServiceHandler) UpdateResPort(context.Context, *connect.Request[v1.UpdateResPortRequest]) (*connect.Response[v1.UpdateResPortResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.BackofficeResidentialService.UpdateResPort is not implemented"))
}

func (UnimplementedBackofficeResidentialServiceHandler) FetchResDevice(context.Context, *connect.Request[v1.FetchResDeviceRequest]) (*connect.Response[v1.FetchResDeviceResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.BackofficeResidentialService.FetchResDevice is not implemented"))
}

func (UnimplementedBackofficeResidentialServiceHandler) RestartPort(context.Context, *connect.Request[v1.BackofficeResidentialServiceRestartPortRequest]) (*connect.Response[v1.BackofficeResidentialServiceRestartPortResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.BackofficeResidentialService.RestartPort is not implemented"))
}
