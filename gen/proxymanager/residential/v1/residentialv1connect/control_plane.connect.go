// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/residential/v1/control_plane.proto

package residentialv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/residential/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// ControlPlaneResidentialServiceName is the fully-qualified name of the
	// ControlPlaneResidentialService service.
	ControlPlaneResidentialServiceName = "proxymanager.residential.v1.ControlPlaneResidentialService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// ControlPlaneResidentialServiceFetchPortConfigProcedure is the fully-qualified name of the
	// ControlPlaneResidentialService's FetchPortConfig RPC.
	ControlPlaneResidentialServiceFetchPortConfigProcedure = "/proxymanager.residential.v1.ControlPlaneResidentialService/FetchPortConfig"
	// ControlPlaneResidentialServiceUpdateNodeStatusProcedure is the fully-qualified name of the
	// ControlPlaneResidentialService's UpdateNodeStatus RPC.
	ControlPlaneResidentialServiceUpdateNodeStatusProcedure = "/proxymanager.residential.v1.ControlPlaneResidentialService/UpdateNodeStatus"
	// ControlPlaneResidentialServiceUpdateDeviceStatusProcedure is the fully-qualified name of the
	// ControlPlaneResidentialService's UpdateDeviceStatus RPC.
	ControlPlaneResidentialServiceUpdateDeviceStatusProcedure = "/proxymanager.residential.v1.ControlPlaneResidentialService/UpdateDeviceStatus"
)

// ControlPlaneResidentialServiceClient is a client for the
// proxymanager.residential.v1.ControlPlaneResidentialService service.
type ControlPlaneResidentialServiceClient interface {
	FetchPortConfig(context.Context, *connect.Request[v1.ControlPlaneResidentialServiceFetchPortConfigRequest]) (*connect.Response[v1.ControlPlaneResidentialServiceFetchPortConfigResponse], error)
	UpdateNodeStatus(context.Context, *connect.Request[v1.ControlPlaneResidentialServiceUpdateNodeStatusRequest]) (*connect.Response[v1.ControlPlaneResidentialServiceUpdateNodeStatusResponse], error)
	UpdateDeviceStatus(context.Context, *connect.Request[v1.ControlPlaneResidentialServiceUpdateDeviceStatusRequest]) (*connect.Response[v1.ControlPlaneResidentialServiceUpdateDeviceStatusResponse], error)
}

// NewControlPlaneResidentialServiceClient constructs a client for the
// proxymanager.residential.v1.ControlPlaneResidentialService service. By default, it uses the
// Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and sends
// uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewControlPlaneResidentialServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) ControlPlaneResidentialServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	controlPlaneResidentialServiceMethods := v1.File_proxymanager_residential_v1_control_plane_proto.Services().ByName("ControlPlaneResidentialService").Methods()
	return &controlPlaneResidentialServiceClient{
		fetchPortConfig: connect.NewClient[v1.ControlPlaneResidentialServiceFetchPortConfigRequest, v1.ControlPlaneResidentialServiceFetchPortConfigResponse](
			httpClient,
			baseURL+ControlPlaneResidentialServiceFetchPortConfigProcedure,
			connect.WithSchema(controlPlaneResidentialServiceMethods.ByName("FetchPortConfig")),
			connect.WithClientOptions(opts...),
		),
		updateNodeStatus: connect.NewClient[v1.ControlPlaneResidentialServiceUpdateNodeStatusRequest, v1.ControlPlaneResidentialServiceUpdateNodeStatusResponse](
			httpClient,
			baseURL+ControlPlaneResidentialServiceUpdateNodeStatusProcedure,
			connect.WithSchema(controlPlaneResidentialServiceMethods.ByName("UpdateNodeStatus")),
			connect.WithClientOptions(opts...),
		),
		updateDeviceStatus: connect.NewClient[v1.ControlPlaneResidentialServiceUpdateDeviceStatusRequest, v1.ControlPlaneResidentialServiceUpdateDeviceStatusResponse](
			httpClient,
			baseURL+ControlPlaneResidentialServiceUpdateDeviceStatusProcedure,
			connect.WithSchema(controlPlaneResidentialServiceMethods.ByName("UpdateDeviceStatus")),
			connect.WithClientOptions(opts...),
		),
	}
}

// controlPlaneResidentialServiceClient implements ControlPlaneResidentialServiceClient.
type controlPlaneResidentialServiceClient struct {
	fetchPortConfig    *connect.Client[v1.ControlPlaneResidentialServiceFetchPortConfigRequest, v1.ControlPlaneResidentialServiceFetchPortConfigResponse]
	updateNodeStatus   *connect.Client[v1.ControlPlaneResidentialServiceUpdateNodeStatusRequest, v1.ControlPlaneResidentialServiceUpdateNodeStatusResponse]
	updateDeviceStatus *connect.Client[v1.ControlPlaneResidentialServiceUpdateDeviceStatusRequest, v1.ControlPlaneResidentialServiceUpdateDeviceStatusResponse]
}

// FetchPortConfig calls proxymanager.residential.v1.ControlPlaneResidentialService.FetchPortConfig.
func (c *controlPlaneResidentialServiceClient) FetchPortConfig(ctx context.Context, req *connect.Request[v1.ControlPlaneResidentialServiceFetchPortConfigRequest]) (*connect.Response[v1.ControlPlaneResidentialServiceFetchPortConfigResponse], error) {
	return c.fetchPortConfig.CallUnary(ctx, req)
}

// UpdateNodeStatus calls
// proxymanager.residential.v1.ControlPlaneResidentialService.UpdateNodeStatus.
func (c *controlPlaneResidentialServiceClient) UpdateNodeStatus(ctx context.Context, req *connect.Request[v1.ControlPlaneResidentialServiceUpdateNodeStatusRequest]) (*connect.Response[v1.ControlPlaneResidentialServiceUpdateNodeStatusResponse], error) {
	return c.updateNodeStatus.CallUnary(ctx, req)
}

// UpdateDeviceStatus calls
// proxymanager.residential.v1.ControlPlaneResidentialService.UpdateDeviceStatus.
func (c *controlPlaneResidentialServiceClient) UpdateDeviceStatus(ctx context.Context, req *connect.Request[v1.ControlPlaneResidentialServiceUpdateDeviceStatusRequest]) (*connect.Response[v1.ControlPlaneResidentialServiceUpdateDeviceStatusResponse], error) {
	return c.updateDeviceStatus.CallUnary(ctx, req)
}

// ControlPlaneResidentialServiceHandler is an implementation of the
// proxymanager.residential.v1.ControlPlaneResidentialService service.
type ControlPlaneResidentialServiceHandler interface {
	FetchPortConfig(context.Context, *connect.Request[v1.ControlPlaneResidentialServiceFetchPortConfigRequest]) (*connect.Response[v1.ControlPlaneResidentialServiceFetchPortConfigResponse], error)
	UpdateNodeStatus(context.Context, *connect.Request[v1.ControlPlaneResidentialServiceUpdateNodeStatusRequest]) (*connect.Response[v1.ControlPlaneResidentialServiceUpdateNodeStatusResponse], error)
	UpdateDeviceStatus(context.Context, *connect.Request[v1.ControlPlaneResidentialServiceUpdateDeviceStatusRequest]) (*connect.Response[v1.ControlPlaneResidentialServiceUpdateDeviceStatusResponse], error)
}

// NewControlPlaneResidentialServiceHandler builds an HTTP handler from the service implementation.
// It returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewControlPlaneResidentialServiceHandler(svc ControlPlaneResidentialServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	controlPlaneResidentialServiceMethods := v1.File_proxymanager_residential_v1_control_plane_proto.Services().ByName("ControlPlaneResidentialService").Methods()
	controlPlaneResidentialServiceFetchPortConfigHandler := connect.NewUnaryHandler(
		ControlPlaneResidentialServiceFetchPortConfigProcedure,
		svc.FetchPortConfig,
		connect.WithSchema(controlPlaneResidentialServiceMethods.ByName("FetchPortConfig")),
		connect.WithHandlerOptions(opts...),
	)
	controlPlaneResidentialServiceUpdateNodeStatusHandler := connect.NewUnaryHandler(
		ControlPlaneResidentialServiceUpdateNodeStatusProcedure,
		svc.UpdateNodeStatus,
		connect.WithSchema(controlPlaneResidentialServiceMethods.ByName("UpdateNodeStatus")),
		connect.WithHandlerOptions(opts...),
	)
	controlPlaneResidentialServiceUpdateDeviceStatusHandler := connect.NewUnaryHandler(
		ControlPlaneResidentialServiceUpdateDeviceStatusProcedure,
		svc.UpdateDeviceStatus,
		connect.WithSchema(controlPlaneResidentialServiceMethods.ByName("UpdateDeviceStatus")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.residential.v1.ControlPlaneResidentialService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case ControlPlaneResidentialServiceFetchPortConfigProcedure:
			controlPlaneResidentialServiceFetchPortConfigHandler.ServeHTTP(w, r)
		case ControlPlaneResidentialServiceUpdateNodeStatusProcedure:
			controlPlaneResidentialServiceUpdateNodeStatusHandler.ServeHTTP(w, r)
		case ControlPlaneResidentialServiceUpdateDeviceStatusProcedure:
			controlPlaneResidentialServiceUpdateDeviceStatusHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedControlPlaneResidentialServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedControlPlaneResidentialServiceHandler struct{}

func (UnimplementedControlPlaneResidentialServiceHandler) FetchPortConfig(context.Context, *connect.Request[v1.ControlPlaneResidentialServiceFetchPortConfigRequest]) (*connect.Response[v1.ControlPlaneResidentialServiceFetchPortConfigResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.ControlPlaneResidentialService.FetchPortConfig is not implemented"))
}

func (UnimplementedControlPlaneResidentialServiceHandler) UpdateNodeStatus(context.Context, *connect.Request[v1.ControlPlaneResidentialServiceUpdateNodeStatusRequest]) (*connect.Response[v1.ControlPlaneResidentialServiceUpdateNodeStatusResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.ControlPlaneResidentialService.UpdateNodeStatus is not implemented"))
}

func (UnimplementedControlPlaneResidentialServiceHandler) UpdateDeviceStatus(context.Context, *connect.Request[v1.ControlPlaneResidentialServiceUpdateDeviceStatusRequest]) (*connect.Response[v1.ControlPlaneResidentialServiceUpdateDeviceStatusResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.ControlPlaneResidentialService.UpdateDeviceStatus is not implemented"))
}
