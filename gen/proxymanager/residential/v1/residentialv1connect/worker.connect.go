// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/residential/v1/worker.proto

package residentialv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/residential/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// WorkerResidentialServiceName is the fully-qualified name of the WorkerResidentialService service.
	WorkerResidentialServiceName = "proxymanager.residential.v1.WorkerResidentialService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// WorkerResidentialServiceHealthCheckProcedure is the fully-qualified name of the
	// WorkerResidentialService's HealthCheck RPC.
	WorkerResidentialServiceHealthCheckProcedure = "/proxymanager.residential.v1.WorkerResidentialService/HealthCheck"
	// WorkerResidentialServiceConfigRouteProcedure is the fully-qualified name of the
	// WorkerResidentialService's ConfigRoute RPC.
	WorkerResidentialServiceConfigRouteProcedure = "/proxymanager.residential.v1.WorkerResidentialService/ConfigRoute"
)

// WorkerResidentialServiceClient is a client for the
// proxymanager.residential.v1.WorkerResidentialService service.
type WorkerResidentialServiceClient interface {
	HealthCheck(context.Context, *connect.Request[v1.WorkerResidentialServiceHealthCheckRequest]) (*connect.Response[v1.WorkerResidentialServiceHealthCheckResponse], error)
	ConfigRoute(context.Context, *connect.Request[v1.WorkerResidentialServiceConfigRouteRequest]) (*connect.Response[v1.WorkerResidentialServiceConfigRouteResponse], error)
}

// NewWorkerResidentialServiceClient constructs a client for the
// proxymanager.residential.v1.WorkerResidentialService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewWorkerResidentialServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) WorkerResidentialServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	workerResidentialServiceMethods := v1.File_proxymanager_residential_v1_worker_proto.Services().ByName("WorkerResidentialService").Methods()
	return &workerResidentialServiceClient{
		healthCheck: connect.NewClient[v1.WorkerResidentialServiceHealthCheckRequest, v1.WorkerResidentialServiceHealthCheckResponse](
			httpClient,
			baseURL+WorkerResidentialServiceHealthCheckProcedure,
			connect.WithSchema(workerResidentialServiceMethods.ByName("HealthCheck")),
			connect.WithClientOptions(opts...),
		),
		configRoute: connect.NewClient[v1.WorkerResidentialServiceConfigRouteRequest, v1.WorkerResidentialServiceConfigRouteResponse](
			httpClient,
			baseURL+WorkerResidentialServiceConfigRouteProcedure,
			connect.WithSchema(workerResidentialServiceMethods.ByName("ConfigRoute")),
			connect.WithClientOptions(opts...),
		),
	}
}

// workerResidentialServiceClient implements WorkerResidentialServiceClient.
type workerResidentialServiceClient struct {
	healthCheck *connect.Client[v1.WorkerResidentialServiceHealthCheckRequest, v1.WorkerResidentialServiceHealthCheckResponse]
	configRoute *connect.Client[v1.WorkerResidentialServiceConfigRouteRequest, v1.WorkerResidentialServiceConfigRouteResponse]
}

// HealthCheck calls proxymanager.residential.v1.WorkerResidentialService.HealthCheck.
func (c *workerResidentialServiceClient) HealthCheck(ctx context.Context, req *connect.Request[v1.WorkerResidentialServiceHealthCheckRequest]) (*connect.Response[v1.WorkerResidentialServiceHealthCheckResponse], error) {
	return c.healthCheck.CallUnary(ctx, req)
}

// ConfigRoute calls proxymanager.residential.v1.WorkerResidentialService.ConfigRoute.
func (c *workerResidentialServiceClient) ConfigRoute(ctx context.Context, req *connect.Request[v1.WorkerResidentialServiceConfigRouteRequest]) (*connect.Response[v1.WorkerResidentialServiceConfigRouteResponse], error) {
	return c.configRoute.CallUnary(ctx, req)
}

// WorkerResidentialServiceHandler is an implementation of the
// proxymanager.residential.v1.WorkerResidentialService service.
type WorkerResidentialServiceHandler interface {
	HealthCheck(context.Context, *connect.Request[v1.WorkerResidentialServiceHealthCheckRequest]) (*connect.Response[v1.WorkerResidentialServiceHealthCheckResponse], error)
	ConfigRoute(context.Context, *connect.Request[v1.WorkerResidentialServiceConfigRouteRequest]) (*connect.Response[v1.WorkerResidentialServiceConfigRouteResponse], error)
}

// NewWorkerResidentialServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewWorkerResidentialServiceHandler(svc WorkerResidentialServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	workerResidentialServiceMethods := v1.File_proxymanager_residential_v1_worker_proto.Services().ByName("WorkerResidentialService").Methods()
	workerResidentialServiceHealthCheckHandler := connect.NewUnaryHandler(
		WorkerResidentialServiceHealthCheckProcedure,
		svc.HealthCheck,
		connect.WithSchema(workerResidentialServiceMethods.ByName("HealthCheck")),
		connect.WithHandlerOptions(opts...),
	)
	workerResidentialServiceConfigRouteHandler := connect.NewUnaryHandler(
		WorkerResidentialServiceConfigRouteProcedure,
		svc.ConfigRoute,
		connect.WithSchema(workerResidentialServiceMethods.ByName("ConfigRoute")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.residential.v1.WorkerResidentialService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case WorkerResidentialServiceHealthCheckProcedure:
			workerResidentialServiceHealthCheckHandler.ServeHTTP(w, r)
		case WorkerResidentialServiceConfigRouteProcedure:
			workerResidentialServiceConfigRouteHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedWorkerResidentialServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedWorkerResidentialServiceHandler struct{}

func (UnimplementedWorkerResidentialServiceHandler) HealthCheck(context.Context, *connect.Request[v1.WorkerResidentialServiceHealthCheckRequest]) (*connect.Response[v1.WorkerResidentialServiceHealthCheckResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.WorkerResidentialService.HealthCheck is not implemented"))
}

func (UnimplementedWorkerResidentialServiceHandler) ConfigRoute(context.Context, *connect.Request[v1.WorkerResidentialServiceConfigRouteRequest]) (*connect.Response[v1.WorkerResidentialServiceConfigRouteResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.WorkerResidentialService.ConfigRoute is not implemented"))
}
