// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/residential/v1/worker_processor.proto

package residentialv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/residential/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// WorkerProcessorResidentialServiceName is the fully-qualified name of the
	// WorkerProcessorResidentialService service.
	WorkerProcessorResidentialServiceName = "proxymanager.residential.v1.WorkerProcessorResidentialService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// WorkerProcessorResidentialServiceHealthCheckProcedure is the fully-qualified name of the
	// WorkerProcessorResidentialService's HealthCheck RPC.
	WorkerProcessorResidentialServiceHealthCheckProcedure = "/proxymanager.residential.v1.WorkerProcessorResidentialService/HealthCheck"
	// WorkerProcessorResidentialServiceRestartNodeProcedure is the fully-qualified name of the
	// WorkerProcessorResidentialService's RestartNode RPC.
	WorkerProcessorResidentialServiceRestartNodeProcedure = "/proxymanager.residential.v1.WorkerProcessorResidentialService/RestartNode"
	// WorkerProcessorResidentialServiceRestartPortProcedure is the fully-qualified name of the
	// WorkerProcessorResidentialService's RestartPort RPC.
	WorkerProcessorResidentialServiceRestartPortProcedure = "/proxymanager.residential.v1.WorkerProcessorResidentialService/RestartPort"
	// WorkerProcessorResidentialServiceRestartDeviceProcedure is the fully-qualified name of the
	// WorkerProcessorResidentialService's RestartDevice RPC.
	WorkerProcessorResidentialServiceRestartDeviceProcedure = "/proxymanager.residential.v1.WorkerProcessorResidentialService/RestartDevice"
)

// WorkerProcessorResidentialServiceClient is a client for the
// proxymanager.residential.v1.WorkerProcessorResidentialService service.
type WorkerProcessorResidentialServiceClient interface {
	HealthCheck(context.Context, *connect.Request[v1.WorkerProcessorResidentialServiceHealthCheckRequest]) (*connect.Response[v1.WorkerProcessorResidentialServiceHealthCheckResponse], error)
	RestartNode(context.Context, *connect.Request[v1.WorkerProcessorResidentialServiceRestartNodeRequest]) (*connect.Response[v1.WorkerProcessorResidentialServiceRestartNodeResponse], error)
	RestartPort(context.Context, *connect.Request[v1.WorkerProcessorResidentialServiceRestartPortRequest]) (*connect.Response[v1.WorkerProcessorResidentialServiceRestartPortResponse], error)
	RestartDevice(context.Context, *connect.Request[v1.WorkerProcessorResidentialServiceRestartDeviceRequest]) (*connect.Response[v1.WorkerProcessorResidentialServiceRestartDeviceResponse], error)
}

// NewWorkerProcessorResidentialServiceClient constructs a client for the
// proxymanager.residential.v1.WorkerProcessorResidentialService service. By default, it uses the
// Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and sends
// uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewWorkerProcessorResidentialServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) WorkerProcessorResidentialServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	workerProcessorResidentialServiceMethods := v1.File_proxymanager_residential_v1_worker_processor_proto.Services().ByName("WorkerProcessorResidentialService").Methods()
	return &workerProcessorResidentialServiceClient{
		healthCheck: connect.NewClient[v1.WorkerProcessorResidentialServiceHealthCheckRequest, v1.WorkerProcessorResidentialServiceHealthCheckResponse](
			httpClient,
			baseURL+WorkerProcessorResidentialServiceHealthCheckProcedure,
			connect.WithSchema(workerProcessorResidentialServiceMethods.ByName("HealthCheck")),
			connect.WithClientOptions(opts...),
		),
		restartNode: connect.NewClient[v1.WorkerProcessorResidentialServiceRestartNodeRequest, v1.WorkerProcessorResidentialServiceRestartNodeResponse](
			httpClient,
			baseURL+WorkerProcessorResidentialServiceRestartNodeProcedure,
			connect.WithSchema(workerProcessorResidentialServiceMethods.ByName("RestartNode")),
			connect.WithClientOptions(opts...),
		),
		restartPort: connect.NewClient[v1.WorkerProcessorResidentialServiceRestartPortRequest, v1.WorkerProcessorResidentialServiceRestartPortResponse](
			httpClient,
			baseURL+WorkerProcessorResidentialServiceRestartPortProcedure,
			connect.WithSchema(workerProcessorResidentialServiceMethods.ByName("RestartPort")),
			connect.WithClientOptions(opts...),
		),
		restartDevice: connect.NewClient[v1.WorkerProcessorResidentialServiceRestartDeviceRequest, v1.WorkerProcessorResidentialServiceRestartDeviceResponse](
			httpClient,
			baseURL+WorkerProcessorResidentialServiceRestartDeviceProcedure,
			connect.WithSchema(workerProcessorResidentialServiceMethods.ByName("RestartDevice")),
			connect.WithClientOptions(opts...),
		),
	}
}

// workerProcessorResidentialServiceClient implements WorkerProcessorResidentialServiceClient.
type workerProcessorResidentialServiceClient struct {
	healthCheck   *connect.Client[v1.WorkerProcessorResidentialServiceHealthCheckRequest, v1.WorkerProcessorResidentialServiceHealthCheckResponse]
	restartNode   *connect.Client[v1.WorkerProcessorResidentialServiceRestartNodeRequest, v1.WorkerProcessorResidentialServiceRestartNodeResponse]
	restartPort   *connect.Client[v1.WorkerProcessorResidentialServiceRestartPortRequest, v1.WorkerProcessorResidentialServiceRestartPortResponse]
	restartDevice *connect.Client[v1.WorkerProcessorResidentialServiceRestartDeviceRequest, v1.WorkerProcessorResidentialServiceRestartDeviceResponse]
}

// HealthCheck calls proxymanager.residential.v1.WorkerProcessorResidentialService.HealthCheck.
func (c *workerProcessorResidentialServiceClient) HealthCheck(ctx context.Context, req *connect.Request[v1.WorkerProcessorResidentialServiceHealthCheckRequest]) (*connect.Response[v1.WorkerProcessorResidentialServiceHealthCheckResponse], error) {
	return c.healthCheck.CallUnary(ctx, req)
}

// RestartNode calls proxymanager.residential.v1.WorkerProcessorResidentialService.RestartNode.
func (c *workerProcessorResidentialServiceClient) RestartNode(ctx context.Context, req *connect.Request[v1.WorkerProcessorResidentialServiceRestartNodeRequest]) (*connect.Response[v1.WorkerProcessorResidentialServiceRestartNodeResponse], error) {
	return c.restartNode.CallUnary(ctx, req)
}

// RestartPort calls proxymanager.residential.v1.WorkerProcessorResidentialService.RestartPort.
func (c *workerProcessorResidentialServiceClient) RestartPort(ctx context.Context, req *connect.Request[v1.WorkerProcessorResidentialServiceRestartPortRequest]) (*connect.Response[v1.WorkerProcessorResidentialServiceRestartPortResponse], error) {
	return c.restartPort.CallUnary(ctx, req)
}

// RestartDevice calls proxymanager.residential.v1.WorkerProcessorResidentialService.RestartDevice.
func (c *workerProcessorResidentialServiceClient) RestartDevice(ctx context.Context, req *connect.Request[v1.WorkerProcessorResidentialServiceRestartDeviceRequest]) (*connect.Response[v1.WorkerProcessorResidentialServiceRestartDeviceResponse], error) {
	return c.restartDevice.CallUnary(ctx, req)
}

// WorkerProcessorResidentialServiceHandler is an implementation of the
// proxymanager.residential.v1.WorkerProcessorResidentialService service.
type WorkerProcessorResidentialServiceHandler interface {
	HealthCheck(context.Context, *connect.Request[v1.WorkerProcessorResidentialServiceHealthCheckRequest]) (*connect.Response[v1.WorkerProcessorResidentialServiceHealthCheckResponse], error)
	RestartNode(context.Context, *connect.Request[v1.WorkerProcessorResidentialServiceRestartNodeRequest]) (*connect.Response[v1.WorkerProcessorResidentialServiceRestartNodeResponse], error)
	RestartPort(context.Context, *connect.Request[v1.WorkerProcessorResidentialServiceRestartPortRequest]) (*connect.Response[v1.WorkerProcessorResidentialServiceRestartPortResponse], error)
	RestartDevice(context.Context, *connect.Request[v1.WorkerProcessorResidentialServiceRestartDeviceRequest]) (*connect.Response[v1.WorkerProcessorResidentialServiceRestartDeviceResponse], error)
}

// NewWorkerProcessorResidentialServiceHandler builds an HTTP handler from the service
// implementation. It returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewWorkerProcessorResidentialServiceHandler(svc WorkerProcessorResidentialServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	workerProcessorResidentialServiceMethods := v1.File_proxymanager_residential_v1_worker_processor_proto.Services().ByName("WorkerProcessorResidentialService").Methods()
	workerProcessorResidentialServiceHealthCheckHandler := connect.NewUnaryHandler(
		WorkerProcessorResidentialServiceHealthCheckProcedure,
		svc.HealthCheck,
		connect.WithSchema(workerProcessorResidentialServiceMethods.ByName("HealthCheck")),
		connect.WithHandlerOptions(opts...),
	)
	workerProcessorResidentialServiceRestartNodeHandler := connect.NewUnaryHandler(
		WorkerProcessorResidentialServiceRestartNodeProcedure,
		svc.RestartNode,
		connect.WithSchema(workerProcessorResidentialServiceMethods.ByName("RestartNode")),
		connect.WithHandlerOptions(opts...),
	)
	workerProcessorResidentialServiceRestartPortHandler := connect.NewUnaryHandler(
		WorkerProcessorResidentialServiceRestartPortProcedure,
		svc.RestartPort,
		connect.WithSchema(workerProcessorResidentialServiceMethods.ByName("RestartPort")),
		connect.WithHandlerOptions(opts...),
	)
	workerProcessorResidentialServiceRestartDeviceHandler := connect.NewUnaryHandler(
		WorkerProcessorResidentialServiceRestartDeviceProcedure,
		svc.RestartDevice,
		connect.WithSchema(workerProcessorResidentialServiceMethods.ByName("RestartDevice")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.residential.v1.WorkerProcessorResidentialService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case WorkerProcessorResidentialServiceHealthCheckProcedure:
			workerProcessorResidentialServiceHealthCheckHandler.ServeHTTP(w, r)
		case WorkerProcessorResidentialServiceRestartNodeProcedure:
			workerProcessorResidentialServiceRestartNodeHandler.ServeHTTP(w, r)
		case WorkerProcessorResidentialServiceRestartPortProcedure:
			workerProcessorResidentialServiceRestartPortHandler.ServeHTTP(w, r)
		case WorkerProcessorResidentialServiceRestartDeviceProcedure:
			workerProcessorResidentialServiceRestartDeviceHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedWorkerProcessorResidentialServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedWorkerProcessorResidentialServiceHandler struct{}

func (UnimplementedWorkerProcessorResidentialServiceHandler) HealthCheck(context.Context, *connect.Request[v1.WorkerProcessorResidentialServiceHealthCheckRequest]) (*connect.Response[v1.WorkerProcessorResidentialServiceHealthCheckResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.WorkerProcessorResidentialService.HealthCheck is not implemented"))
}

func (UnimplementedWorkerProcessorResidentialServiceHandler) RestartNode(context.Context, *connect.Request[v1.WorkerProcessorResidentialServiceRestartNodeRequest]) (*connect.Response[v1.WorkerProcessorResidentialServiceRestartNodeResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.WorkerProcessorResidentialService.RestartNode is not implemented"))
}

func (UnimplementedWorkerProcessorResidentialServiceHandler) RestartPort(context.Context, *connect.Request[v1.WorkerProcessorResidentialServiceRestartPortRequest]) (*connect.Response[v1.WorkerProcessorResidentialServiceRestartPortResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.WorkerProcessorResidentialService.RestartPort is not implemented"))
}

func (UnimplementedWorkerProcessorResidentialServiceHandler) RestartDevice(context.Context, *connect.Request[v1.WorkerProcessorResidentialServiceRestartDeviceRequest]) (*connect.Response[v1.WorkerProcessorResidentialServiceRestartDeviceResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.residential.v1.WorkerProcessorResidentialService.RestartDevice is not implemented"))
}
