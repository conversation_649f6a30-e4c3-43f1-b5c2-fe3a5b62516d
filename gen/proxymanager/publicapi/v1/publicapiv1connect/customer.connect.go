// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/publicapi/v1/customer.proto

package publicapiv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/publicapi/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CustomerPublicAPIServiceName is the fully-qualified name of the CustomerPublicAPIService service.
	CustomerPublicAPIServiceName = "proxymanager.publicapi.v1.CustomerPublicAPIService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CustomerPublicAPIServiceChangeProxyProcedure is the fully-qualified name of the
	// CustomerPublicAPIService's ChangeProxy RPC.
	CustomerPublicAPIServiceChangeProxyProcedure = "/proxymanager.publicapi.v1.CustomerPublicAPIService/ChangeProxy"
)

// CustomerPublicAPIServiceClient is a client for the
// proxymanager.publicapi.v1.CustomerPublicAPIService service.
type CustomerPublicAPIServiceClient interface {
	ChangeProxy(context.Context, *connect.Request[v1.CustomerPublicAPIServiceChangeProxyRequest]) (*connect.Response[v1.CustomerPublicAPIServiceChangeProxyResponse], error)
}

// NewCustomerPublicAPIServiceClient constructs a client for the
// proxymanager.publicapi.v1.CustomerPublicAPIService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCustomerPublicAPIServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CustomerPublicAPIServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	customerPublicAPIServiceMethods := v1.File_proxymanager_publicapi_v1_customer_proto.Services().ByName("CustomerPublicAPIService").Methods()
	return &customerPublicAPIServiceClient{
		changeProxy: connect.NewClient[v1.CustomerPublicAPIServiceChangeProxyRequest, v1.CustomerPublicAPIServiceChangeProxyResponse](
			httpClient,
			baseURL+CustomerPublicAPIServiceChangeProxyProcedure,
			connect.WithSchema(customerPublicAPIServiceMethods.ByName("ChangeProxy")),
			connect.WithClientOptions(opts...),
		),
	}
}

// customerPublicAPIServiceClient implements CustomerPublicAPIServiceClient.
type customerPublicAPIServiceClient struct {
	changeProxy *connect.Client[v1.CustomerPublicAPIServiceChangeProxyRequest, v1.CustomerPublicAPIServiceChangeProxyResponse]
}

// ChangeProxy calls proxymanager.publicapi.v1.CustomerPublicAPIService.ChangeProxy.
func (c *customerPublicAPIServiceClient) ChangeProxy(ctx context.Context, req *connect.Request[v1.CustomerPublicAPIServiceChangeProxyRequest]) (*connect.Response[v1.CustomerPublicAPIServiceChangeProxyResponse], error) {
	return c.changeProxy.CallUnary(ctx, req)
}

// CustomerPublicAPIServiceHandler is an implementation of the
// proxymanager.publicapi.v1.CustomerPublicAPIService service.
type CustomerPublicAPIServiceHandler interface {
	ChangeProxy(context.Context, *connect.Request[v1.CustomerPublicAPIServiceChangeProxyRequest]) (*connect.Response[v1.CustomerPublicAPIServiceChangeProxyResponse], error)
}

// NewCustomerPublicAPIServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCustomerPublicAPIServiceHandler(svc CustomerPublicAPIServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	customerPublicAPIServiceMethods := v1.File_proxymanager_publicapi_v1_customer_proto.Services().ByName("CustomerPublicAPIService").Methods()
	customerPublicAPIServiceChangeProxyHandler := connect.NewUnaryHandler(
		CustomerPublicAPIServiceChangeProxyProcedure,
		svc.ChangeProxy,
		connect.WithSchema(customerPublicAPIServiceMethods.ByName("ChangeProxy")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.publicapi.v1.CustomerPublicAPIService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CustomerPublicAPIServiceChangeProxyProcedure:
			customerPublicAPIServiceChangeProxyHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCustomerPublicAPIServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCustomerPublicAPIServiceHandler struct{}

func (UnimplementedCustomerPublicAPIServiceHandler) ChangeProxy(context.Context, *connect.Request[v1.CustomerPublicAPIServiceChangeProxyRequest]) (*connect.Response[v1.CustomerPublicAPIServiceChangeProxyResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.publicapi.v1.CustomerPublicAPIService.ChangeProxy is not implemented"))
}
