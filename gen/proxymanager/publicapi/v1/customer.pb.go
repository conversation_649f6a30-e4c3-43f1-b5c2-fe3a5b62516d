// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/publicapi/v1/customer.proto

package publicapiv1

import (
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CustomerPublicAPIServiceChangeProxyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProxyToken    string                 `protobuf:"bytes,1,opt,name=proxy_token,json=proxyToken,proto3" json:"proxy_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPublicAPIServiceChangeProxyRequest) Reset() {
	*x = CustomerPublicAPIServiceChangeProxyRequest{}
	mi := &file_proxymanager_publicapi_v1_customer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPublicAPIServiceChangeProxyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPublicAPIServiceChangeProxyRequest) ProtoMessage() {}

func (x *CustomerPublicAPIServiceChangeProxyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_publicapi_v1_customer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPublicAPIServiceChangeProxyRequest.ProtoReflect.Descriptor instead.
func (*CustomerPublicAPIServiceChangeProxyRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_publicapi_v1_customer_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerPublicAPIServiceChangeProxyRequest) GetProxyToken() string {
	if x != nil {
		return x.ProxyToken
	}
	return ""
}

type CustomerPublicAPIServiceChangeProxyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Proxy         *ProxyEntity           `protobuf:"bytes,2,opt,name=proxy,proto3" json:"proxy,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPublicAPIServiceChangeProxyResponse) Reset() {
	*x = CustomerPublicAPIServiceChangeProxyResponse{}
	mi := &file_proxymanager_publicapi_v1_customer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPublicAPIServiceChangeProxyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPublicAPIServiceChangeProxyResponse) ProtoMessage() {}

func (x *CustomerPublicAPIServiceChangeProxyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_publicapi_v1_customer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPublicAPIServiceChangeProxyResponse.ProtoReflect.Descriptor instead.
func (*CustomerPublicAPIServiceChangeProxyResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_publicapi_v1_customer_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerPublicAPIServiceChangeProxyResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerPublicAPIServiceChangeProxyResponse) GetProxy() *ProxyEntity {
	if x != nil {
		return x.Proxy
	}
	return nil
}

type ProxyEntity struct {
	state         protoimpl.MessageState                `protogen:"open.v1"`
	Proxy         string                                `protobuf:"bytes,1,opt,name=proxy,proto3" json:"proxy,omitempty"`
	IpProxy       string                                `protobuf:"bytes,2,opt,name=ip_proxy,json=ipProxy,proto3" json:"ip_proxy,omitempty"`
	PortProxy     int64                                 `protobuf:"varint,3,opt,name=port_proxy,json=portProxy,proto3" json:"port_proxy,omitempty"`
	IpAllows      []string                              `protobuf:"bytes,4,rep,name=ip_allows,json=ipAllows,proto3" json:"ip_allows,omitempty"`
	UsernameProxy string                                `protobuf:"bytes,5,opt,name=username_proxy,json=usernameProxy,proto3" json:"username_proxy,omitempty"`
	PasswordProxy string                                `protobuf:"bytes,6,opt,name=password_proxy,json=passwordProxy,proto3" json:"password_proxy,omitempty"`
	IpPublic      string                                `protobuf:"bytes,7,opt,name=ip_public,json=ipPublic,proto3" json:"ip_public,omitempty"`
	Country       *CustomerPublicAPIProxyEntityLocation `protobuf:"bytes,9,opt,name=country,proto3" json:"country,omitempty"`
	State         *CustomerPublicAPIProxyEntityLocation `protobuf:"bytes,10,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProxyEntity) Reset() {
	*x = ProxyEntity{}
	mi := &file_proxymanager_publicapi_v1_customer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProxyEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProxyEntity) ProtoMessage() {}

func (x *ProxyEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_publicapi_v1_customer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProxyEntity.ProtoReflect.Descriptor instead.
func (*ProxyEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_publicapi_v1_customer_proto_rawDescGZIP(), []int{2}
}

func (x *ProxyEntity) GetProxy() string {
	if x != nil {
		return x.Proxy
	}
	return ""
}

func (x *ProxyEntity) GetIpProxy() string {
	if x != nil {
		return x.IpProxy
	}
	return ""
}

func (x *ProxyEntity) GetPortProxy() int64 {
	if x != nil {
		return x.PortProxy
	}
	return 0
}

func (x *ProxyEntity) GetIpAllows() []string {
	if x != nil {
		return x.IpAllows
	}
	return nil
}

func (x *ProxyEntity) GetUsernameProxy() string {
	if x != nil {
		return x.UsernameProxy
	}
	return ""
}

func (x *ProxyEntity) GetPasswordProxy() string {
	if x != nil {
		return x.PasswordProxy
	}
	return ""
}

func (x *ProxyEntity) GetIpPublic() string {
	if x != nil {
		return x.IpPublic
	}
	return ""
}

func (x *ProxyEntity) GetCountry() *CustomerPublicAPIProxyEntityLocation {
	if x != nil {
		return x.Country
	}
	return nil
}

func (x *ProxyEntity) GetState() *CustomerPublicAPIProxyEntityLocation {
	if x != nil {
		return x.State
	}
	return nil
}

type CustomerPublicAPIProxyEntityLocation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Level         v11.LocationLevel      `protobuf:"varint,2,opt,name=level,proto3,enum=algoenum.v1.LocationLevel" json:"level,omitempty"`
	Emoji         string                 `protobuf:"bytes,3,opt,name=emoji,proto3" json:"emoji,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerPublicAPIProxyEntityLocation) Reset() {
	*x = CustomerPublicAPIProxyEntityLocation{}
	mi := &file_proxymanager_publicapi_v1_customer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerPublicAPIProxyEntityLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerPublicAPIProxyEntityLocation) ProtoMessage() {}

func (x *CustomerPublicAPIProxyEntityLocation) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_publicapi_v1_customer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerPublicAPIProxyEntityLocation.ProtoReflect.Descriptor instead.
func (*CustomerPublicAPIProxyEntityLocation) Descriptor() ([]byte, []int) {
	return file_proxymanager_publicapi_v1_customer_proto_rawDescGZIP(), []int{3}
}

func (x *CustomerPublicAPIProxyEntityLocation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerPublicAPIProxyEntityLocation) GetLevel() v11.LocationLevel {
	if x != nil {
		return x.Level
	}
	return v11.LocationLevel(0)
}

func (x *CustomerPublicAPIProxyEntityLocation) GetEmoji() string {
	if x != nil {
		return x.Emoji
	}
	return ""
}

var File_proxymanager_publicapi_v1_customer_proto protoreflect.FileDescriptor

const file_proxymanager_publicapi_v1_customer_proto_rawDesc = "" +
	"\n" +
	"(proxymanager/publicapi/v1/customer.proto\x12\x19proxymanager.publicapi.v1\x1a\x18errmsg/v1/errormsg.proto\x1a algoenum/v1/location_level.proto\"M\n" +
	"*CustomerPublicAPIServiceChangeProxyRequest\x12\x1f\n" +
	"\vproxy_token\x18\x01 \x01(\tR\n" +
	"proxyToken\"\x9a\x01\n" +
	"+CustomerPublicAPIServiceChangeProxyResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\x05proxy\x18\x02 \x01(\v2&.proxymanager.publicapi.v1.ProxyEntityR\x05proxy\"\x97\x03\n" +
	"\vProxyEntity\x12\x14\n" +
	"\x05proxy\x18\x01 \x01(\tR\x05proxy\x12\x19\n" +
	"\bip_proxy\x18\x02 \x01(\tR\aipProxy\x12\x1d\n" +
	"\n" +
	"port_proxy\x18\x03 \x01(\x03R\tportProxy\x12\x1b\n" +
	"\tip_allows\x18\x04 \x03(\tR\bipAllows\x12%\n" +
	"\x0eusername_proxy\x18\x05 \x01(\tR\rusernameProxy\x12%\n" +
	"\x0epassword_proxy\x18\x06 \x01(\tR\rpasswordProxy\x12\x1b\n" +
	"\tip_public\x18\a \x01(\tR\bipPublic\x12Y\n" +
	"\acountry\x18\t \x01(\v2?.proxymanager.publicapi.v1.CustomerPublicAPIProxyEntityLocationR\acountry\x12U\n" +
	"\x05state\x18\n" +
	" \x01(\v2?.proxymanager.publicapi.v1.CustomerPublicAPIProxyEntityLocationR\x05state\"\x82\x01\n" +
	"$CustomerPublicAPIProxyEntityLocation\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x120\n" +
	"\x05level\x18\x02 \x01(\x0e2\x1a.algoenum.v1.LocationLevelR\x05level\x12\x14\n" +
	"\x05emoji\x18\x03 \x01(\tR\x05emoji2\xb9\x01\n" +
	"\x18CustomerPublicAPIService\x12\x9c\x01\n" +
	"\vChangeProxy\x12E.proxymanager.publicapi.v1.CustomerPublicAPIServiceChangeProxyRequest\x1aF.proxymanager.publicapi.v1.CustomerPublicAPIServiceChangeProxyResponseBVZTgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/publicapi/v1;publicapiv1b\x06proto3"

var (
	file_proxymanager_publicapi_v1_customer_proto_rawDescOnce sync.Once
	file_proxymanager_publicapi_v1_customer_proto_rawDescData []byte
)

func file_proxymanager_publicapi_v1_customer_proto_rawDescGZIP() []byte {
	file_proxymanager_publicapi_v1_customer_proto_rawDescOnce.Do(func() {
		file_proxymanager_publicapi_v1_customer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_publicapi_v1_customer_proto_rawDesc), len(file_proxymanager_publicapi_v1_customer_proto_rawDesc)))
	})
	return file_proxymanager_publicapi_v1_customer_proto_rawDescData
}

var file_proxymanager_publicapi_v1_customer_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_proxymanager_publicapi_v1_customer_proto_goTypes = []any{
	(*CustomerPublicAPIServiceChangeProxyRequest)(nil),  // 0: proxymanager.publicapi.v1.CustomerPublicAPIServiceChangeProxyRequest
	(*CustomerPublicAPIServiceChangeProxyResponse)(nil), // 1: proxymanager.publicapi.v1.CustomerPublicAPIServiceChangeProxyResponse
	(*ProxyEntity)(nil),                          // 2: proxymanager.publicapi.v1.ProxyEntity
	(*CustomerPublicAPIProxyEntityLocation)(nil), // 3: proxymanager.publicapi.v1.CustomerPublicAPIProxyEntityLocation
	(*v1.ErrorMessage)(nil),                      // 4: errmsg.v1.ErrorMessage
	(v11.LocationLevel)(0),                       // 5: algoenum.v1.LocationLevel
}
var file_proxymanager_publicapi_v1_customer_proto_depIdxs = []int32{
	4, // 0: proxymanager.publicapi.v1.CustomerPublicAPIServiceChangeProxyResponse.error:type_name -> errmsg.v1.ErrorMessage
	2, // 1: proxymanager.publicapi.v1.CustomerPublicAPIServiceChangeProxyResponse.proxy:type_name -> proxymanager.publicapi.v1.ProxyEntity
	3, // 2: proxymanager.publicapi.v1.ProxyEntity.country:type_name -> proxymanager.publicapi.v1.CustomerPublicAPIProxyEntityLocation
	3, // 3: proxymanager.publicapi.v1.ProxyEntity.state:type_name -> proxymanager.publicapi.v1.CustomerPublicAPIProxyEntityLocation
	5, // 4: proxymanager.publicapi.v1.CustomerPublicAPIProxyEntityLocation.level:type_name -> algoenum.v1.LocationLevel
	0, // 5: proxymanager.publicapi.v1.CustomerPublicAPIService.ChangeProxy:input_type -> proxymanager.publicapi.v1.CustomerPublicAPIServiceChangeProxyRequest
	1, // 6: proxymanager.publicapi.v1.CustomerPublicAPIService.ChangeProxy:output_type -> proxymanager.publicapi.v1.CustomerPublicAPIServiceChangeProxyResponse
	6, // [6:7] is the sub-list for method output_type
	5, // [5:6] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_proxymanager_publicapi_v1_customer_proto_init() }
func file_proxymanager_publicapi_v1_customer_proto_init() {
	if File_proxymanager_publicapi_v1_customer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_publicapi_v1_customer_proto_rawDesc), len(file_proxymanager_publicapi_v1_customer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_publicapi_v1_customer_proto_goTypes,
		DependencyIndexes: file_proxymanager_publicapi_v1_customer_proto_depIdxs,
		MessageInfos:      file_proxymanager_publicapi_v1_customer_proto_msgTypes,
	}.Build()
	File_proxymanager_publicapi_v1_customer_proto = out.File
	file_proxymanager_publicapi_v1_customer_proto_goTypes = nil
	file_proxymanager_publicapi_v1_customer_proto_depIdxs = nil
}
