// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/proxyprofile/v1/customer.proto

package proxyprofilev1

import (
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CustomerProxyProfileServiceResetProxyProfileRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdProxyProfile string                 `protobuf:"bytes,1,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceResetProxyProfileRequest) Reset() {
	*x = CustomerProxyProfileServiceResetProxyProfileRequest{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceResetProxyProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceResetProxyProfileRequest) ProtoMessage() {}

func (x *CustomerProxyProfileServiceResetProxyProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceResetProxyProfileRequest.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceResetProxyProfileRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerProxyProfileServiceResetProxyProfileRequest) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

type CustomerProxyProfileServiceResetProxyProfileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceResetProxyProfileResponse) Reset() {
	*x = CustomerProxyProfileServiceResetProxyProfileResponse{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceResetProxyProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceResetProxyProfileResponse) ProtoMessage() {}

func (x *CustomerProxyProfileServiceResetProxyProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceResetProxyProfileResponse.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceResetProxyProfileResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerProxyProfileServiceResetProxyProfileResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerProxyProfileServiceApplyProxyProfileRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdProxyProfile string                 `protobuf:"bytes,1,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	IdProxyToken   []string               `protobuf:"bytes,2,rep,name=id_proxy_token,json=idProxyToken,proto3" json:"id_proxy_token,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceApplyProxyProfileRequest) Reset() {
	*x = CustomerProxyProfileServiceApplyProxyProfileRequest{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceApplyProxyProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceApplyProxyProfileRequest) ProtoMessage() {}

func (x *CustomerProxyProfileServiceApplyProxyProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceApplyProxyProfileRequest.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceApplyProxyProfileRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{2}
}

func (x *CustomerProxyProfileServiceApplyProxyProfileRequest) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

func (x *CustomerProxyProfileServiceApplyProxyProfileRequest) GetIdProxyToken() []string {
	if x != nil {
		return x.IdProxyToken
	}
	return nil
}

type CustomerProxyProfileServiceApplyProxyProfileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceApplyProxyProfileResponse) Reset() {
	*x = CustomerProxyProfileServiceApplyProxyProfileResponse{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceApplyProxyProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceApplyProxyProfileResponse) ProtoMessage() {}

func (x *CustomerProxyProfileServiceApplyProxyProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceApplyProxyProfileResponse.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceApplyProxyProfileResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{3}
}

func (x *CustomerProxyProfileServiceApplyProxyProfileResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerProxyProfileServiceConfigProxyProfileTelcoRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdProxyProfile string                 `protobuf:"bytes,1,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	IdTelco        string                 `protobuf:"bytes,2,opt,name=id_telco,json=idTelco,proto3" json:"id_telco,omitempty"`
	IsEnable       bool                   `protobuf:"varint,3,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceConfigProxyProfileTelcoRequest) Reset() {
	*x = CustomerProxyProfileServiceConfigProxyProfileTelcoRequest{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceConfigProxyProfileTelcoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceConfigProxyProfileTelcoRequest) ProtoMessage() {}

func (x *CustomerProxyProfileServiceConfigProxyProfileTelcoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceConfigProxyProfileTelcoRequest.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceConfigProxyProfileTelcoRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{4}
}

func (x *CustomerProxyProfileServiceConfigProxyProfileTelcoRequest) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

func (x *CustomerProxyProfileServiceConfigProxyProfileTelcoRequest) GetIdTelco() string {
	if x != nil {
		return x.IdTelco
	}
	return ""
}

func (x *CustomerProxyProfileServiceConfigProxyProfileTelcoRequest) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

type CustomerProxyProfileServiceConfigProxyProfileTelcoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceConfigProxyProfileTelcoResponse) Reset() {
	*x = CustomerProxyProfileServiceConfigProxyProfileTelcoResponse{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceConfigProxyProfileTelcoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceConfigProxyProfileTelcoResponse) ProtoMessage() {}

func (x *CustomerProxyProfileServiceConfigProxyProfileTelcoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceConfigProxyProfileTelcoResponse.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceConfigProxyProfileTelcoResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{5}
}

func (x *CustomerProxyProfileServiceConfigProxyProfileTelcoResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerProxyProfileServiceConfigProxyProfileLocationRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdProxyProfile string                 `protobuf:"bytes,1,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	IdLocation     string                 `protobuf:"bytes,2,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	IsEnable       bool                   `protobuf:"varint,3,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceConfigProxyProfileLocationRequest) Reset() {
	*x = CustomerProxyProfileServiceConfigProxyProfileLocationRequest{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceConfigProxyProfileLocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceConfigProxyProfileLocationRequest) ProtoMessage() {}

func (x *CustomerProxyProfileServiceConfigProxyProfileLocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceConfigProxyProfileLocationRequest.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceConfigProxyProfileLocationRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{6}
}

func (x *CustomerProxyProfileServiceConfigProxyProfileLocationRequest) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

func (x *CustomerProxyProfileServiceConfigProxyProfileLocationRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *CustomerProxyProfileServiceConfigProxyProfileLocationRequest) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

type CustomerProxyProfileServiceConfigProxyProfileLocationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceConfigProxyProfileLocationResponse) Reset() {
	*x = CustomerProxyProfileServiceConfigProxyProfileLocationResponse{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceConfigProxyProfileLocationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceConfigProxyProfileLocationResponse) ProtoMessage() {}

func (x *CustomerProxyProfileServiceConfigProxyProfileLocationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceConfigProxyProfileLocationResponse.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceConfigProxyProfileLocationResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{7}
}

func (x *CustomerProxyProfileServiceConfigProxyProfileLocationResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdProxyProfile string                 `protobuf:"bytes,1,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	IpAllow        string                 `protobuf:"bytes,2,opt,name=ip_allow,json=ipAllow,proto3" json:"ip_allow,omitempty"`
	IsEnable       bool                   `protobuf:"varint,3,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest) Reset() {
	*x = CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest) ProtoMessage() {}

func (x *CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{8}
}

func (x *CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

func (x *CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest) GetIpAllow() string {
	if x != nil {
		return x.IpAllow
	}
	return ""
}

func (x *CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

type CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse) Reset() {
	*x = CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse) ProtoMessage() {}

func (x *CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{9}
}

func (x *CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerProxyProfileServiceFetchProxyProfileRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdPlan         string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdProxyProfile string                 `protobuf:"bytes,2,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	Pagination     *v11.PaginationRequest `protobuf:"bytes,3,opt,name=pagination,proto3" json:"pagination,omitempty"`
	IsPublic       bool                   `protobuf:"varint,4,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceFetchProxyProfileRequest) Reset() {
	*x = CustomerProxyProfileServiceFetchProxyProfileRequest{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceFetchProxyProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceFetchProxyProfileRequest) ProtoMessage() {}

func (x *CustomerProxyProfileServiceFetchProxyProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceFetchProxyProfileRequest.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceFetchProxyProfileRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{10}
}

func (x *CustomerProxyProfileServiceFetchProxyProfileRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *CustomerProxyProfileServiceFetchProxyProfileRequest) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

func (x *CustomerProxyProfileServiceFetchProxyProfileRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *CustomerProxyProfileServiceFetchProxyProfileRequest) GetIsPublic() bool {
	if x != nil {
		return x.IsPublic
	}
	return false
}

type CustomerProxyProfileServiceFetchProxyProfileResponse struct {
	state         protoimpl.MessageState                           `protogen:"open.v1"`
	Error         *v1.ErrorMessage                                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse                          `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ProxyProfiles []*CustomerProxyProfileServiceProxyProfileEntity `protobuf:"bytes,3,rep,name=proxy_profiles,json=proxyProfiles,proto3" json:"proxy_profiles,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceFetchProxyProfileResponse) Reset() {
	*x = CustomerProxyProfileServiceFetchProxyProfileResponse{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceFetchProxyProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceFetchProxyProfileResponse) ProtoMessage() {}

func (x *CustomerProxyProfileServiceFetchProxyProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceFetchProxyProfileResponse.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceFetchProxyProfileResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{11}
}

func (x *CustomerProxyProfileServiceFetchProxyProfileResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerProxyProfileServiceFetchProxyProfileResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *CustomerProxyProfileServiceFetchProxyProfileResponse) GetProxyProfiles() []*CustomerProxyProfileServiceProxyProfileEntity {
	if x != nil {
		return x.ProxyProfiles
	}
	return nil
}

type CustomerProxyProfileServiceProxyProfileEntity struct {
	state                      protoimpl.MessageState                                   `protogen:"open.v1"`
	IdProxyProfile             string                                                   `protobuf:"bytes,1,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	Name                       string                                                   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Dns                        *CustomerProxyProfileServiceProxyProfileEntityDNS        `protobuf:"bytes,3,opt,name=dns,proto3" json:"dns,omitempty"`
	AutoSwitchIpAfterInSec     int64                                                    `protobuf:"varint,4,opt,name=auto_switch_ip_after_in_sec,json=autoSwitchIpAfterInSec,proto3" json:"auto_switch_ip_after_in_sec,omitempty"`
	AutoSwitchIpWhenDisconnect bool                                                     `protobuf:"varint,5,opt,name=auto_switch_ip_when_disconnect,json=autoSwitchIpWhenDisconnect,proto3" json:"auto_switch_ip_when_disconnect,omitempty"`
	IsStaticUsernamePassword   bool                                                     `protobuf:"varint,6,opt,name=is_static_username_password,json=isStaticUsernamePassword,proto3" json:"is_static_username_password,omitempty"`
	IpAllows                   []string                                                 `protobuf:"bytes,7,rep,name=ip_allows,json=ipAllows,proto3" json:"ip_allows,omitempty"`
	Telcos                     []*CustomerProxyProfileServiceProxyProfileEntityTelco    `protobuf:"bytes,8,rep,name=telcos,proto3" json:"telcos,omitempty"`
	Locations                  []*CustomerProxyProfileServiceProxyProfileEntityLocation `protobuf:"bytes,9,rep,name=locations,proto3" json:"locations,omitempty"`
	BandwidthPerProxyMbit      int64                                                    `protobuf:"varint,10,opt,name=bandwidth_per_proxy_mbit,json=bandwidthPerProxyMbit,proto3" json:"bandwidth_per_proxy_mbit,omitempty"`
	UsernameProxy              string                                                   `protobuf:"bytes,13,opt,name=username_proxy,json=usernameProxy,proto3" json:"username_proxy,omitempty"`
	PasswordProxy              string                                                   `protobuf:"bytes,14,opt,name=password_proxy,json=passwordProxy,proto3" json:"password_proxy,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceProxyProfileEntity) Reset() {
	*x = CustomerProxyProfileServiceProxyProfileEntity{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceProxyProfileEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceProxyProfileEntity) ProtoMessage() {}

func (x *CustomerProxyProfileServiceProxyProfileEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceProxyProfileEntity.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceProxyProfileEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{12}
}

func (x *CustomerProxyProfileServiceProxyProfileEntity) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

func (x *CustomerProxyProfileServiceProxyProfileEntity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerProxyProfileServiceProxyProfileEntity) GetDns() *CustomerProxyProfileServiceProxyProfileEntityDNS {
	if x != nil {
		return x.Dns
	}
	return nil
}

func (x *CustomerProxyProfileServiceProxyProfileEntity) GetAutoSwitchIpAfterInSec() int64 {
	if x != nil {
		return x.AutoSwitchIpAfterInSec
	}
	return 0
}

func (x *CustomerProxyProfileServiceProxyProfileEntity) GetAutoSwitchIpWhenDisconnect() bool {
	if x != nil {
		return x.AutoSwitchIpWhenDisconnect
	}
	return false
}

func (x *CustomerProxyProfileServiceProxyProfileEntity) GetIsStaticUsernamePassword() bool {
	if x != nil {
		return x.IsStaticUsernamePassword
	}
	return false
}

func (x *CustomerProxyProfileServiceProxyProfileEntity) GetIpAllows() []string {
	if x != nil {
		return x.IpAllows
	}
	return nil
}

func (x *CustomerProxyProfileServiceProxyProfileEntity) GetTelcos() []*CustomerProxyProfileServiceProxyProfileEntityTelco {
	if x != nil {
		return x.Telcos
	}
	return nil
}

func (x *CustomerProxyProfileServiceProxyProfileEntity) GetLocations() []*CustomerProxyProfileServiceProxyProfileEntityLocation {
	if x != nil {
		return x.Locations
	}
	return nil
}

func (x *CustomerProxyProfileServiceProxyProfileEntity) GetBandwidthPerProxyMbit() int64 {
	if x != nil {
		return x.BandwidthPerProxyMbit
	}
	return 0
}

func (x *CustomerProxyProfileServiceProxyProfileEntity) GetUsernameProxy() string {
	if x != nil {
		return x.UsernameProxy
	}
	return ""
}

func (x *CustomerProxyProfileServiceProxyProfileEntity) GetPasswordProxy() string {
	if x != nil {
		return x.PasswordProxy
	}
	return ""
}

type CustomerProxyProfileServiceProxyProfileEntityDNS struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdDns         string                 `protobuf:"bytes,1,opt,name=id_dns,json=idDns,proto3" json:"id_dns,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Dns_1         string                 `protobuf:"bytes,3,opt,name=dns_1,json=dns1,proto3" json:"dns_1,omitempty"`
	Dns_2         string                 `protobuf:"bytes,4,opt,name=dns_2,json=dns2,proto3" json:"dns_2,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceProxyProfileEntityDNS) Reset() {
	*x = CustomerProxyProfileServiceProxyProfileEntityDNS{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceProxyProfileEntityDNS) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceProxyProfileEntityDNS) ProtoMessage() {}

func (x *CustomerProxyProfileServiceProxyProfileEntityDNS) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceProxyProfileEntityDNS.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceProxyProfileEntityDNS) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{13}
}

func (x *CustomerProxyProfileServiceProxyProfileEntityDNS) GetIdDns() string {
	if x != nil {
		return x.IdDns
	}
	return ""
}

func (x *CustomerProxyProfileServiceProxyProfileEntityDNS) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerProxyProfileServiceProxyProfileEntityDNS) GetDns_1() string {
	if x != nil {
		return x.Dns_1
	}
	return ""
}

func (x *CustomerProxyProfileServiceProxyProfileEntityDNS) GetDns_2() string {
	if x != nil {
		return x.Dns_2
	}
	return ""
}

type CustomerProxyProfileServiceProxyProfileEntityTelco struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdTelco       string                 `protobuf:"bytes,1,opt,name=id_telco,json=idTelco,proto3" json:"id_telco,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceProxyProfileEntityTelco) Reset() {
	*x = CustomerProxyProfileServiceProxyProfileEntityTelco{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceProxyProfileEntityTelco) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceProxyProfileEntityTelco) ProtoMessage() {}

func (x *CustomerProxyProfileServiceProxyProfileEntityTelco) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceProxyProfileEntityTelco.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceProxyProfileEntityTelco) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{14}
}

func (x *CustomerProxyProfileServiceProxyProfileEntityTelco) GetIdTelco() string {
	if x != nil {
		return x.IdTelco
	}
	return ""
}

func (x *CustomerProxyProfileServiceProxyProfileEntityTelco) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CustomerProxyProfileServiceProxyProfileEntityLocation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdLocation    string                 `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	Level         v12.LocationLevel      `protobuf:"varint,2,opt,name=level,proto3,enum=algoenum.v1.LocationLevel" json:"level,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceProxyProfileEntityLocation) Reset() {
	*x = CustomerProxyProfileServiceProxyProfileEntityLocation{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceProxyProfileEntityLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceProxyProfileEntityLocation) ProtoMessage() {}

func (x *CustomerProxyProfileServiceProxyProfileEntityLocation) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceProxyProfileEntityLocation.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceProxyProfileEntityLocation) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{15}
}

func (x *CustomerProxyProfileServiceProxyProfileEntityLocation) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *CustomerProxyProfileServiceProxyProfileEntityLocation) GetLevel() v12.LocationLevel {
	if x != nil {
		return x.Level
	}
	return v12.LocationLevel(0)
}

func (x *CustomerProxyProfileServiceProxyProfileEntityLocation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CustomerProxyProfileServiceCreateProxyProfileRequest struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	IdPlan                     string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdDns                      string                 `protobuf:"bytes,2,opt,name=id_dns,json=idDns,proto3" json:"id_dns,omitempty"`
	Name                       string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	AutoSwitchIpAfterInSec     int64                  `protobuf:"varint,4,opt,name=auto_switch_ip_after_in_sec,json=autoSwitchIpAfterInSec,proto3" json:"auto_switch_ip_after_in_sec,omitempty"`
	AutoSwitchIpWhenDisconnect bool                   `protobuf:"varint,5,opt,name=auto_switch_ip_when_disconnect,json=autoSwitchIpWhenDisconnect,proto3" json:"auto_switch_ip_when_disconnect,omitempty"`
	IsStaticUsernamePassword   bool                   `protobuf:"varint,6,opt,name=is_static_username_password,json=isStaticUsernamePassword,proto3" json:"is_static_username_password,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceCreateProxyProfileRequest) Reset() {
	*x = CustomerProxyProfileServiceCreateProxyProfileRequest{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceCreateProxyProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceCreateProxyProfileRequest) ProtoMessage() {}

func (x *CustomerProxyProfileServiceCreateProxyProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceCreateProxyProfileRequest.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceCreateProxyProfileRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{16}
}

func (x *CustomerProxyProfileServiceCreateProxyProfileRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *CustomerProxyProfileServiceCreateProxyProfileRequest) GetIdDns() string {
	if x != nil {
		return x.IdDns
	}
	return ""
}

func (x *CustomerProxyProfileServiceCreateProxyProfileRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerProxyProfileServiceCreateProxyProfileRequest) GetAutoSwitchIpAfterInSec() int64 {
	if x != nil {
		return x.AutoSwitchIpAfterInSec
	}
	return 0
}

func (x *CustomerProxyProfileServiceCreateProxyProfileRequest) GetAutoSwitchIpWhenDisconnect() bool {
	if x != nil {
		return x.AutoSwitchIpWhenDisconnect
	}
	return false
}

func (x *CustomerProxyProfileServiceCreateProxyProfileRequest) GetIsStaticUsernamePassword() bool {
	if x != nil {
		return x.IsStaticUsernamePassword
	}
	return false
}

type CustomerProxyProfileServiceCreateProxyProfileResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Error          *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	IdProxyProfile string                 `protobuf:"bytes,2,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceCreateProxyProfileResponse) Reset() {
	*x = CustomerProxyProfileServiceCreateProxyProfileResponse{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceCreateProxyProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceCreateProxyProfileResponse) ProtoMessage() {}

func (x *CustomerProxyProfileServiceCreateProxyProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceCreateProxyProfileResponse.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceCreateProxyProfileResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{17}
}

func (x *CustomerProxyProfileServiceCreateProxyProfileResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerProxyProfileServiceCreateProxyProfileResponse) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

type CustomerProxyProfileServiceUpdateProxyProfileRequest struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	IdProxyProfile             string                 `protobuf:"bytes,1,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	IdDns                      string                 `protobuf:"bytes,2,opt,name=id_dns,json=idDns,proto3" json:"id_dns,omitempty"`
	Name                       string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	AutoSwitchIpAfterInSec     int64                  `protobuf:"varint,4,opt,name=auto_switch_ip_after_in_sec,json=autoSwitchIpAfterInSec,proto3" json:"auto_switch_ip_after_in_sec,omitempty"`
	AutoSwitchIpWhenDisconnect bool                   `protobuf:"varint,5,opt,name=auto_switch_ip_when_disconnect,json=autoSwitchIpWhenDisconnect,proto3" json:"auto_switch_ip_when_disconnect,omitempty"`
	IsStaticUsernamePassword   bool                   `protobuf:"varint,6,opt,name=is_static_username_password,json=isStaticUsernamePassword,proto3" json:"is_static_username_password,omitempty"`
	RevokeUsernamePassword     bool                   `protobuf:"varint,7,opt,name=revoke_username_password,json=revokeUsernamePassword,proto3" json:"revoke_username_password,omitempty"`
	BandwidthPerProxyMbit      int64                  `protobuf:"varint,8,opt,name=bandwidth_per_proxy_mbit,json=bandwidthPerProxyMbit,proto3" json:"bandwidth_per_proxy_mbit,omitempty"`
	State                      *v11.State             `protobuf:"bytes,9,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceUpdateProxyProfileRequest) Reset() {
	*x = CustomerProxyProfileServiceUpdateProxyProfileRequest{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceUpdateProxyProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceUpdateProxyProfileRequest) ProtoMessage() {}

func (x *CustomerProxyProfileServiceUpdateProxyProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceUpdateProxyProfileRequest.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceUpdateProxyProfileRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{18}
}

func (x *CustomerProxyProfileServiceUpdateProxyProfileRequest) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

func (x *CustomerProxyProfileServiceUpdateProxyProfileRequest) GetIdDns() string {
	if x != nil {
		return x.IdDns
	}
	return ""
}

func (x *CustomerProxyProfileServiceUpdateProxyProfileRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerProxyProfileServiceUpdateProxyProfileRequest) GetAutoSwitchIpAfterInSec() int64 {
	if x != nil {
		return x.AutoSwitchIpAfterInSec
	}
	return 0
}

func (x *CustomerProxyProfileServiceUpdateProxyProfileRequest) GetAutoSwitchIpWhenDisconnect() bool {
	if x != nil {
		return x.AutoSwitchIpWhenDisconnect
	}
	return false
}

func (x *CustomerProxyProfileServiceUpdateProxyProfileRequest) GetIsStaticUsernamePassword() bool {
	if x != nil {
		return x.IsStaticUsernamePassword
	}
	return false
}

func (x *CustomerProxyProfileServiceUpdateProxyProfileRequest) GetRevokeUsernamePassword() bool {
	if x != nil {
		return x.RevokeUsernamePassword
	}
	return false
}

func (x *CustomerProxyProfileServiceUpdateProxyProfileRequest) GetBandwidthPerProxyMbit() int64 {
	if x != nil {
		return x.BandwidthPerProxyMbit
	}
	return 0
}

func (x *CustomerProxyProfileServiceUpdateProxyProfileRequest) GetState() *v11.State {
	if x != nil {
		return x.State
	}
	return nil
}

type CustomerProxyProfileServiceUpdateProxyProfileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerProxyProfileServiceUpdateProxyProfileResponse) Reset() {
	*x = CustomerProxyProfileServiceUpdateProxyProfileResponse{}
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerProxyProfileServiceUpdateProxyProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerProxyProfileServiceUpdateProxyProfileResponse) ProtoMessage() {}

func (x *CustomerProxyProfileServiceUpdateProxyProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_customer_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerProxyProfileServiceUpdateProxyProfileResponse.ProtoReflect.Descriptor instead.
func (*CustomerProxyProfileServiceUpdateProxyProfileResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP(), []int{19}
}

func (x *CustomerProxyProfileServiceUpdateProxyProfileResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_proxymanager_proxyprofile_v1_customer_proto protoreflect.FileDescriptor

const file_proxymanager_proxyprofile_v1_customer_proto_rawDesc = "" +
	"\n" +
	"+proxymanager/proxyprofile/v1/customer.proto\x12\x1cproxymanager.proxyprofile.v1\x1a\x14utils/v1/utils.proto\x1a\x18errmsg/v1/errormsg.proto\x1a algoenum/v1/location_level.proto\"_\n" +
	"3CustomerProxyProfileServiceResetProxyProfileRequest\x12(\n" +
	"\x10id_proxy_profile\x18\x01 \x01(\tR\x0eidProxyProfile\"e\n" +
	"4CustomerProxyProfileServiceResetProxyProfileResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x85\x01\n" +
	"3CustomerProxyProfileServiceApplyProxyProfileRequest\x12(\n" +
	"\x10id_proxy_profile\x18\x01 \x01(\tR\x0eidProxyProfile\x12$\n" +
	"\x0eid_proxy_token\x18\x02 \x03(\tR\fidProxyToken\"e\n" +
	"4CustomerProxyProfileServiceApplyProxyProfileResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x9d\x01\n" +
	"9CustomerProxyProfileServiceConfigProxyProfileTelcoRequest\x12(\n" +
	"\x10id_proxy_profile\x18\x01 \x01(\tR\x0eidProxyProfile\x12\x19\n" +
	"\bid_telco\x18\x02 \x01(\tR\aidTelco\x12\x1b\n" +
	"\tis_enable\x18\x03 \x01(\bR\bisEnable\"k\n" +
	":CustomerProxyProfileServiceConfigProxyProfileTelcoResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xa6\x01\n" +
	"<CustomerProxyProfileServiceConfigProxyProfileLocationRequest\x12(\n" +
	"\x10id_proxy_profile\x18\x01 \x01(\tR\x0eidProxyProfile\x12\x1f\n" +
	"\vid_location\x18\x02 \x01(\tR\n" +
	"idLocation\x12\x1b\n" +
	"\tis_enable\x18\x03 \x01(\bR\bisEnable\"n\n" +
	"=CustomerProxyProfileServiceConfigProxyProfileLocationResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x9f\x01\n" +
	";CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest\x12(\n" +
	"\x10id_proxy_profile\x18\x01 \x01(\tR\x0eidProxyProfile\x12\x19\n" +
	"\bip_allow\x18\x02 \x01(\tR\aipAllow\x12\x1b\n" +
	"\tis_enable\x18\x03 \x01(\bR\bisEnable\"m\n" +
	"<CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xd2\x01\n" +
	"3CustomerProxyProfileServiceFetchProxyProfileRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12(\n" +
	"\x10id_proxy_profile\x18\x02 \x01(\tR\x0eidProxyProfile\x12;\n" +
	"\n" +
	"pagination\x18\x03 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\x12\x1b\n" +
	"\tis_public\x18\x04 \x01(\bR\bisPublic\"\x97\x02\n" +
	"4CustomerProxyProfileServiceFetchProxyProfileResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12r\n" +
	"\x0eproxy_profiles\x18\x03 \x03(\v2K.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntityR\rproxyProfiles\"\x90\x06\n" +
	"-CustomerProxyProfileServiceProxyProfileEntity\x12(\n" +
	"\x10id_proxy_profile\x18\x01 \x01(\tR\x0eidProxyProfile\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12`\n" +
	"\x03dns\x18\x03 \x01(\v2N.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntityDNSR\x03dns\x12;\n" +
	"\x1bauto_switch_ip_after_in_sec\x18\x04 \x01(\x03R\x16autoSwitchIpAfterInSec\x12B\n" +
	"\x1eauto_switch_ip_when_disconnect\x18\x05 \x01(\bR\x1aautoSwitchIpWhenDisconnect\x12=\n" +
	"\x1bis_static_username_password\x18\x06 \x01(\bR\x18isStaticUsernamePassword\x12\x1b\n" +
	"\tip_allows\x18\a \x03(\tR\bipAllows\x12h\n" +
	"\x06telcos\x18\b \x03(\v2P.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntityTelcoR\x06telcos\x12q\n" +
	"\tlocations\x18\t \x03(\v2S.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntityLocationR\tlocations\x127\n" +
	"\x18bandwidth_per_proxy_mbit\x18\n" +
	" \x01(\x03R\x15bandwidthPerProxyMbit\x12%\n" +
	"\x0eusername_proxy\x18\r \x01(\tR\rusernameProxy\x12%\n" +
	"\x0epassword_proxy\x18\x0e \x01(\tR\rpasswordProxy\"\x87\x01\n" +
	"0CustomerProxyProfileServiceProxyProfileEntityDNS\x12\x15\n" +
	"\x06id_dns\x18\x01 \x01(\tR\x05idDns\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x13\n" +
	"\x05dns_1\x18\x03 \x01(\tR\x04dns1\x12\x13\n" +
	"\x05dns_2\x18\x04 \x01(\tR\x04dns2\"c\n" +
	"2CustomerProxyProfileServiceProxyProfileEntityTelco\x12\x19\n" +
	"\bid_telco\x18\x01 \x01(\tR\aidTelco\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"\x9e\x01\n" +
	"5CustomerProxyProfileServiceProxyProfileEntityLocation\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x120\n" +
	"\x05level\x18\x02 \x01(\x0e2\x1a.algoenum.v1.LocationLevelR\x05level\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\"\xba\x02\n" +
	"4CustomerProxyProfileServiceCreateProxyProfileRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x15\n" +
	"\x06id_dns\x18\x02 \x01(\tR\x05idDns\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12;\n" +
	"\x1bauto_switch_ip_after_in_sec\x18\x04 \x01(\x03R\x16autoSwitchIpAfterInSec\x12B\n" +
	"\x1eauto_switch_ip_when_disconnect\x18\x05 \x01(\bR\x1aautoSwitchIpWhenDisconnect\x12=\n" +
	"\x1bis_static_username_password\x18\x06 \x01(\bR\x18isStaticUsernamePassword\"\x90\x01\n" +
	"5CustomerProxyProfileServiceCreateProxyProfileResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12(\n" +
	"\x10id_proxy_profile\x18\x02 \x01(\tR\x0eidProxyProfile\"\xe5\x03\n" +
	"4CustomerProxyProfileServiceUpdateProxyProfileRequest\x12(\n" +
	"\x10id_proxy_profile\x18\x01 \x01(\tR\x0eidProxyProfile\x12\x15\n" +
	"\x06id_dns\x18\x02 \x01(\tR\x05idDns\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12;\n" +
	"\x1bauto_switch_ip_after_in_sec\x18\x04 \x01(\x03R\x16autoSwitchIpAfterInSec\x12B\n" +
	"\x1eauto_switch_ip_when_disconnect\x18\x05 \x01(\bR\x1aautoSwitchIpWhenDisconnect\x12=\n" +
	"\x1bis_static_username_password\x18\x06 \x01(\bR\x18isStaticUsernamePassword\x128\n" +
	"\x18revoke_username_password\x18\a \x01(\bR\x16revokeUsernamePassword\x127\n" +
	"\x18bandwidth_per_proxy_mbit\x18\b \x01(\x03R\x15bandwidthPerProxyMbit\x12%\n" +
	"\x05state\x18\t \x01(\v2\x0f.utils.v1.StateR\x05state\"f\n" +
	"5CustomerProxyProfileServiceUpdateProxyProfileResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error2\xd0\f\n" +
	"\x1bCustomerProxyProfileService\x12\xba\x01\n" +
	"\x11ApplyProxyProfile\x12Q.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceApplyProxyProfileRequest\x1aR.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceApplyProxyProfileResponse\x12\xba\x01\n" +
	"\x11ResetProxyProfile\x12Q.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceResetProxyProfileRequest\x1aR.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceResetProxyProfileResponse\x12\xba\x01\n" +
	"\x11FetchProxyProfile\x12Q.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceFetchProxyProfileRequest\x1aR.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceFetchProxyProfileResponse\x12\xbd\x01\n" +
	"\x12CreateProxyProfile\x12R.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceCreateProxyProfileRequest\x1aS.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceCreateProxyProfileResponse\x12\xbd\x01\n" +
	"\x12UpdateProxyProfile\x12R.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceUpdateProxyProfileRequest\x1aS.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceUpdateProxyProfileResponse\x12\xd5\x01\n" +
	"\x1aConfigProxyProfileLocation\x12Z.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileLocationRequest\x1a[.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileLocationResponse\x12\xd2\x01\n" +
	"\x19ConfigProxyProfileIPAllow\x12Y.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest\x1aZ.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse\x12\xcc\x01\n" +
	"\x17ConfigProxyProfileTelco\x12W.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileTelcoRequest\x1aX.proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileTelcoResponseB\\ZZgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/proxyprofile/v1;proxyprofilev1b\x06proto3"

var (
	file_proxymanager_proxyprofile_v1_customer_proto_rawDescOnce sync.Once
	file_proxymanager_proxyprofile_v1_customer_proto_rawDescData []byte
)

func file_proxymanager_proxyprofile_v1_customer_proto_rawDescGZIP() []byte {
	file_proxymanager_proxyprofile_v1_customer_proto_rawDescOnce.Do(func() {
		file_proxymanager_proxyprofile_v1_customer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_proxyprofile_v1_customer_proto_rawDesc), len(file_proxymanager_proxyprofile_v1_customer_proto_rawDesc)))
	})
	return file_proxymanager_proxyprofile_v1_customer_proto_rawDescData
}

var file_proxymanager_proxyprofile_v1_customer_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_proxymanager_proxyprofile_v1_customer_proto_goTypes = []any{
	(*CustomerProxyProfileServiceResetProxyProfileRequest)(nil),           // 0: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceResetProxyProfileRequest
	(*CustomerProxyProfileServiceResetProxyProfileResponse)(nil),          // 1: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceResetProxyProfileResponse
	(*CustomerProxyProfileServiceApplyProxyProfileRequest)(nil),           // 2: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceApplyProxyProfileRequest
	(*CustomerProxyProfileServiceApplyProxyProfileResponse)(nil),          // 3: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceApplyProxyProfileResponse
	(*CustomerProxyProfileServiceConfigProxyProfileTelcoRequest)(nil),     // 4: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileTelcoRequest
	(*CustomerProxyProfileServiceConfigProxyProfileTelcoResponse)(nil),    // 5: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileTelcoResponse
	(*CustomerProxyProfileServiceConfigProxyProfileLocationRequest)(nil),  // 6: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileLocationRequest
	(*CustomerProxyProfileServiceConfigProxyProfileLocationResponse)(nil), // 7: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileLocationResponse
	(*CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest)(nil),   // 8: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest
	(*CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse)(nil),  // 9: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse
	(*CustomerProxyProfileServiceFetchProxyProfileRequest)(nil),           // 10: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceFetchProxyProfileRequest
	(*CustomerProxyProfileServiceFetchProxyProfileResponse)(nil),          // 11: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceFetchProxyProfileResponse
	(*CustomerProxyProfileServiceProxyProfileEntity)(nil),                 // 12: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntity
	(*CustomerProxyProfileServiceProxyProfileEntityDNS)(nil),              // 13: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntityDNS
	(*CustomerProxyProfileServiceProxyProfileEntityTelco)(nil),            // 14: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntityTelco
	(*CustomerProxyProfileServiceProxyProfileEntityLocation)(nil),         // 15: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntityLocation
	(*CustomerProxyProfileServiceCreateProxyProfileRequest)(nil),          // 16: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceCreateProxyProfileRequest
	(*CustomerProxyProfileServiceCreateProxyProfileResponse)(nil),         // 17: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceCreateProxyProfileResponse
	(*CustomerProxyProfileServiceUpdateProxyProfileRequest)(nil),          // 18: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceUpdateProxyProfileRequest
	(*CustomerProxyProfileServiceUpdateProxyProfileResponse)(nil),         // 19: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceUpdateProxyProfileResponse
	(*v1.ErrorMessage)(nil),        // 20: errmsg.v1.ErrorMessage
	(*v11.PaginationRequest)(nil),  // 21: utils.v1.PaginationRequest
	(*v11.PaginationResponse)(nil), // 22: utils.v1.PaginationResponse
	(v12.LocationLevel)(0),         // 23: algoenum.v1.LocationLevel
	(*v11.State)(nil),              // 24: utils.v1.State
}
var file_proxymanager_proxyprofile_v1_customer_proto_depIdxs = []int32{
	20, // 0: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceResetProxyProfileResponse.error:type_name -> errmsg.v1.ErrorMessage
	20, // 1: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceApplyProxyProfileResponse.error:type_name -> errmsg.v1.ErrorMessage
	20, // 2: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileTelcoResponse.error:type_name -> errmsg.v1.ErrorMessage
	20, // 3: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileLocationResponse.error:type_name -> errmsg.v1.ErrorMessage
	20, // 4: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse.error:type_name -> errmsg.v1.ErrorMessage
	21, // 5: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceFetchProxyProfileRequest.pagination:type_name -> utils.v1.PaginationRequest
	20, // 6: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceFetchProxyProfileResponse.error:type_name -> errmsg.v1.ErrorMessage
	22, // 7: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceFetchProxyProfileResponse.pagination:type_name -> utils.v1.PaginationResponse
	12, // 8: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceFetchProxyProfileResponse.proxy_profiles:type_name -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntity
	13, // 9: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntity.dns:type_name -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntityDNS
	14, // 10: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntity.telcos:type_name -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntityTelco
	15, // 11: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntity.locations:type_name -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntityLocation
	23, // 12: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntityLocation.level:type_name -> algoenum.v1.LocationLevel
	20, // 13: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceCreateProxyProfileResponse.error:type_name -> errmsg.v1.ErrorMessage
	24, // 14: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceUpdateProxyProfileRequest.state:type_name -> utils.v1.State
	20, // 15: proxymanager.proxyprofile.v1.CustomerProxyProfileServiceUpdateProxyProfileResponse.error:type_name -> errmsg.v1.ErrorMessage
	2,  // 16: proxymanager.proxyprofile.v1.CustomerProxyProfileService.ApplyProxyProfile:input_type -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceApplyProxyProfileRequest
	0,  // 17: proxymanager.proxyprofile.v1.CustomerProxyProfileService.ResetProxyProfile:input_type -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceResetProxyProfileRequest
	10, // 18: proxymanager.proxyprofile.v1.CustomerProxyProfileService.FetchProxyProfile:input_type -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceFetchProxyProfileRequest
	16, // 19: proxymanager.proxyprofile.v1.CustomerProxyProfileService.CreateProxyProfile:input_type -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceCreateProxyProfileRequest
	18, // 20: proxymanager.proxyprofile.v1.CustomerProxyProfileService.UpdateProxyProfile:input_type -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceUpdateProxyProfileRequest
	6,  // 21: proxymanager.proxyprofile.v1.CustomerProxyProfileService.ConfigProxyProfileLocation:input_type -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileLocationRequest
	8,  // 22: proxymanager.proxyprofile.v1.CustomerProxyProfileService.ConfigProxyProfileIPAllow:input_type -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest
	4,  // 23: proxymanager.proxyprofile.v1.CustomerProxyProfileService.ConfigProxyProfileTelco:input_type -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileTelcoRequest
	3,  // 24: proxymanager.proxyprofile.v1.CustomerProxyProfileService.ApplyProxyProfile:output_type -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceApplyProxyProfileResponse
	1,  // 25: proxymanager.proxyprofile.v1.CustomerProxyProfileService.ResetProxyProfile:output_type -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceResetProxyProfileResponse
	11, // 26: proxymanager.proxyprofile.v1.CustomerProxyProfileService.FetchProxyProfile:output_type -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceFetchProxyProfileResponse
	17, // 27: proxymanager.proxyprofile.v1.CustomerProxyProfileService.CreateProxyProfile:output_type -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceCreateProxyProfileResponse
	19, // 28: proxymanager.proxyprofile.v1.CustomerProxyProfileService.UpdateProxyProfile:output_type -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceUpdateProxyProfileResponse
	7,  // 29: proxymanager.proxyprofile.v1.CustomerProxyProfileService.ConfigProxyProfileLocation:output_type -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileLocationResponse
	9,  // 30: proxymanager.proxyprofile.v1.CustomerProxyProfileService.ConfigProxyProfileIPAllow:output_type -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse
	5,  // 31: proxymanager.proxyprofile.v1.CustomerProxyProfileService.ConfigProxyProfileTelco:output_type -> proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileTelcoResponse
	24, // [24:32] is the sub-list for method output_type
	16, // [16:24] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_proxymanager_proxyprofile_v1_customer_proto_init() }
func file_proxymanager_proxyprofile_v1_customer_proto_init() {
	if File_proxymanager_proxyprofile_v1_customer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_proxyprofile_v1_customer_proto_rawDesc), len(file_proxymanager_proxyprofile_v1_customer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_proxyprofile_v1_customer_proto_goTypes,
		DependencyIndexes: file_proxymanager_proxyprofile_v1_customer_proto_depIdxs,
		MessageInfos:      file_proxymanager_proxyprofile_v1_customer_proto_msgTypes,
	}.Build()
	File_proxymanager_proxyprofile_v1_customer_proto = out.File
	file_proxymanager_proxyprofile_v1_customer_proto_goTypes = nil
	file_proxymanager_proxyprofile_v1_customer_proto_depIdxs = nil
}
