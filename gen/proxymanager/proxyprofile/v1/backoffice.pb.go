// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/proxyprofile/v1/backoffice.proto

package proxyprofilev1

import (
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackofficeProxyProfileServiceResetProxyProfileRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdProxyProfile string                 `protobuf:"bytes,1,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceResetProxyProfileRequest) Reset() {
	*x = BackofficeProxyProfileServiceResetProxyProfileRequest{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceResetProxyProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceResetProxyProfileRequest) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceResetProxyProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceResetProxyProfileRequest.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceResetProxyProfileRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{0}
}

func (x *BackofficeProxyProfileServiceResetProxyProfileRequest) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

type BackofficeProxyProfileServiceResetProxyProfileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceResetProxyProfileResponse) Reset() {
	*x = BackofficeProxyProfileServiceResetProxyProfileResponse{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceResetProxyProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceResetProxyProfileResponse) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceResetProxyProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceResetProxyProfileResponse.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceResetProxyProfileResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{1}
}

func (x *BackofficeProxyProfileServiceResetProxyProfileResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeProxyProfileServiceFetchProxyProfileRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant     string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	IdUser         string                 `protobuf:"bytes,2,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	IdPlan         string                 `protobuf:"bytes,3,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdProxyProfile string                 `protobuf:"bytes,4,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	State          *v11.State             `protobuf:"bytes,5,opt,name=state,proto3" json:"state,omitempty"`
	Pagination     *v11.PaginationRequest `protobuf:"bytes,6,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceFetchProxyProfileRequest) Reset() {
	*x = BackofficeProxyProfileServiceFetchProxyProfileRequest{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceFetchProxyProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceFetchProxyProfileRequest) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceFetchProxyProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceFetchProxyProfileRequest.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceFetchProxyProfileRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{2}
}

func (x *BackofficeProxyProfileServiceFetchProxyProfileRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficeProxyProfileServiceFetchProxyProfileRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *BackofficeProxyProfileServiceFetchProxyProfileRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *BackofficeProxyProfileServiceFetchProxyProfileRequest) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

func (x *BackofficeProxyProfileServiceFetchProxyProfileRequest) GetState() *v11.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeProxyProfileServiceFetchProxyProfileRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeProxyProfileServiceFetchProxyProfileResponse struct {
	state         protoimpl.MessageState                             `protogen:"open.v1"`
	Error         *v1.ErrorMessage                                   `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse                            `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ProxyProfiles []*BackofficeProxyProfileServiceProxyProfileEntity `protobuf:"bytes,3,rep,name=proxy_profiles,json=proxyProfiles,proto3" json:"proxy_profiles,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceFetchProxyProfileResponse) Reset() {
	*x = BackofficeProxyProfileServiceFetchProxyProfileResponse{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceFetchProxyProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceFetchProxyProfileResponse) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceFetchProxyProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceFetchProxyProfileResponse.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceFetchProxyProfileResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{3}
}

func (x *BackofficeProxyProfileServiceFetchProxyProfileResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeProxyProfileServiceFetchProxyProfileResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficeProxyProfileServiceFetchProxyProfileResponse) GetProxyProfiles() []*BackofficeProxyProfileServiceProxyProfileEntity {
	if x != nil {
		return x.ProxyProfiles
	}
	return nil
}

type BackofficeProxyProfileServiceProxyProfileEntity struct {
	state                      protoimpl.MessageState                                     `protogen:"open.v1"`
	IdProxyProfile             string                                                     `protobuf:"bytes,1,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	Name                       string                                                     `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Dns                        *BackofficeProxyProfileServiceProxyProfileEntityDNS        `protobuf:"bytes,3,opt,name=dns,proto3" json:"dns,omitempty"`
	AutoSwitchIpAfterInSec     int64                                                      `protobuf:"varint,4,opt,name=auto_switch_ip_after_in_sec,json=autoSwitchIpAfterInSec,proto3" json:"auto_switch_ip_after_in_sec,omitempty"`
	AutoSwitchIpWhenDisconnect bool                                                       `protobuf:"varint,5,opt,name=auto_switch_ip_when_disconnect,json=autoSwitchIpWhenDisconnect,proto3" json:"auto_switch_ip_when_disconnect,omitempty"`
	IsStaticUsernamePassword   bool                                                       `protobuf:"varint,6,opt,name=is_static_username_password,json=isStaticUsernamePassword,proto3" json:"is_static_username_password,omitempty"`
	UsernameProxy              string                                                     `protobuf:"bytes,7,opt,name=username_proxy,json=usernameProxy,proto3" json:"username_proxy,omitempty"`
	PasswordProxy              string                                                     `protobuf:"bytes,8,opt,name=password_proxy,json=passwordProxy,proto3" json:"password_proxy,omitempty"`
	IpAllows                   []string                                                   `protobuf:"bytes,9,rep,name=ip_allows,json=ipAllows,proto3" json:"ip_allows,omitempty"`
	Telcos                     []*BackofficeProxyProfileServiceProxyProfileEntityTelco    `protobuf:"bytes,10,rep,name=telcos,proto3" json:"telcos,omitempty"`
	Locations                  []*BackofficeProxyProfileServiceProxyProfileEntityLocation `protobuf:"bytes,11,rep,name=locations,proto3" json:"locations,omitempty"`
	IsActive                   bool                                                       `protobuf:"varint,12,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	IsDefaultProfile           bool                                                       `protobuf:"varint,13,opt,name=is_default_profile,json=isDefaultProfile,proto3" json:"is_default_profile,omitempty"`
	CanUpdate                  bool                                                       `protobuf:"varint,14,opt,name=can_update,json=canUpdate,proto3" json:"can_update,omitempty"`
	BandwidthPerProxyMbit      int64                                                      `protobuf:"varint,15,opt,name=bandwidth_per_proxy_mbit,json=bandwidthPerProxyMbit,proto3" json:"bandwidth_per_proxy_mbit,omitempty"`
	IsPublic                   bool                                                       `protobuf:"varint,16,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) Reset() {
	*x = BackofficeProxyProfileServiceProxyProfileEntity{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceProxyProfileEntity) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceProxyProfileEntity.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceProxyProfileEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{4}
}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) GetDns() *BackofficeProxyProfileServiceProxyProfileEntityDNS {
	if x != nil {
		return x.Dns
	}
	return nil
}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) GetAutoSwitchIpAfterInSec() int64 {
	if x != nil {
		return x.AutoSwitchIpAfterInSec
	}
	return 0
}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) GetAutoSwitchIpWhenDisconnect() bool {
	if x != nil {
		return x.AutoSwitchIpWhenDisconnect
	}
	return false
}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) GetIsStaticUsernamePassword() bool {
	if x != nil {
		return x.IsStaticUsernamePassword
	}
	return false
}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) GetUsernameProxy() string {
	if x != nil {
		return x.UsernameProxy
	}
	return ""
}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) GetPasswordProxy() string {
	if x != nil {
		return x.PasswordProxy
	}
	return ""
}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) GetIpAllows() []string {
	if x != nil {
		return x.IpAllows
	}
	return nil
}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) GetTelcos() []*BackofficeProxyProfileServiceProxyProfileEntityTelco {
	if x != nil {
		return x.Telcos
	}
	return nil
}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) GetLocations() []*BackofficeProxyProfileServiceProxyProfileEntityLocation {
	if x != nil {
		return x.Locations
	}
	return nil
}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) GetIsDefaultProfile() bool {
	if x != nil {
		return x.IsDefaultProfile
	}
	return false
}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) GetCanUpdate() bool {
	if x != nil {
		return x.CanUpdate
	}
	return false
}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) GetBandwidthPerProxyMbit() int64 {
	if x != nil {
		return x.BandwidthPerProxyMbit
	}
	return 0
}

func (x *BackofficeProxyProfileServiceProxyProfileEntity) GetIsPublic() bool {
	if x != nil {
		return x.IsPublic
	}
	return false
}

type BackofficeProxyProfileServiceProxyProfileEntityDNS struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdDns         string                 `protobuf:"bytes,1,opt,name=id_dns,json=idDns,proto3" json:"id_dns,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Dns_1         string                 `protobuf:"bytes,3,opt,name=dns_1,json=dns1,proto3" json:"dns_1,omitempty"`
	Dns_2         string                 `protobuf:"bytes,4,opt,name=dns_2,json=dns2,proto3" json:"dns_2,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceProxyProfileEntityDNS) Reset() {
	*x = BackofficeProxyProfileServiceProxyProfileEntityDNS{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceProxyProfileEntityDNS) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceProxyProfileEntityDNS) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceProxyProfileEntityDNS) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceProxyProfileEntityDNS.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceProxyProfileEntityDNS) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{5}
}

func (x *BackofficeProxyProfileServiceProxyProfileEntityDNS) GetIdDns() string {
	if x != nil {
		return x.IdDns
	}
	return ""
}

func (x *BackofficeProxyProfileServiceProxyProfileEntityDNS) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeProxyProfileServiceProxyProfileEntityDNS) GetDns_1() string {
	if x != nil {
		return x.Dns_1
	}
	return ""
}

func (x *BackofficeProxyProfileServiceProxyProfileEntityDNS) GetDns_2() string {
	if x != nil {
		return x.Dns_2
	}
	return ""
}

type BackofficeProxyProfileServiceProxyProfileEntityTelco struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdTelco       string                 `protobuf:"bytes,1,opt,name=id_telco,json=idTelco,proto3" json:"id_telco,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceProxyProfileEntityTelco) Reset() {
	*x = BackofficeProxyProfileServiceProxyProfileEntityTelco{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceProxyProfileEntityTelco) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceProxyProfileEntityTelco) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceProxyProfileEntityTelco) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceProxyProfileEntityTelco.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceProxyProfileEntityTelco) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{6}
}

func (x *BackofficeProxyProfileServiceProxyProfileEntityTelco) GetIdTelco() string {
	if x != nil {
		return x.IdTelco
	}
	return ""
}

func (x *BackofficeProxyProfileServiceProxyProfileEntityTelco) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type BackofficeProxyProfileServiceProxyProfileEntityLocation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdLocation    string                 `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	Level         v12.LocationLevel      `protobuf:"varint,2,opt,name=level,proto3,enum=algoenum.v1.LocationLevel" json:"level,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceProxyProfileEntityLocation) Reset() {
	*x = BackofficeProxyProfileServiceProxyProfileEntityLocation{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceProxyProfileEntityLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceProxyProfileEntityLocation) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceProxyProfileEntityLocation) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceProxyProfileEntityLocation.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceProxyProfileEntityLocation) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{7}
}

func (x *BackofficeProxyProfileServiceProxyProfileEntityLocation) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *BackofficeProxyProfileServiceProxyProfileEntityLocation) GetLevel() v12.LocationLevel {
	if x != nil {
		return x.Level
	}
	return v12.LocationLevel(0)
}

func (x *BackofficeProxyProfileServiceProxyProfileEntityLocation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type BackofficeProxyProfileServiceConfigProxyProfileLocationRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdProxyProfile string                 `protobuf:"bytes,1,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	IdLocation     string                 `protobuf:"bytes,2,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	IsEnable       bool                   `protobuf:"varint,3,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileLocationRequest) Reset() {
	*x = BackofficeProxyProfileServiceConfigProxyProfileLocationRequest{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileLocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceConfigProxyProfileLocationRequest) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceConfigProxyProfileLocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceConfigProxyProfileLocationRequest.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceConfigProxyProfileLocationRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{8}
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileLocationRequest) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileLocationRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileLocationRequest) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

type BackofficeProxyProfileServiceConfigProxyProfileLocationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileLocationResponse) Reset() {
	*x = BackofficeProxyProfileServiceConfigProxyProfileLocationResponse{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileLocationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceConfigProxyProfileLocationResponse) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceConfigProxyProfileLocationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceConfigProxyProfileLocationResponse.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceConfigProxyProfileLocationResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{9}
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileLocationResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdProxyProfile string                 `protobuf:"bytes,1,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	IpAllow        string                 `protobuf:"bytes,2,opt,name=ip_allow,json=ipAllow,proto3" json:"ip_allow,omitempty"`
	IsEnable       bool                   `protobuf:"varint,3,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest) Reset() {
	*x = BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{10}
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest) GetIpAllow() string {
	if x != nil {
		return x.IpAllow
	}
	return ""
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

type BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse) Reset() {
	*x = BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{11}
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdProxyProfile string                 `protobuf:"bytes,1,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	IdTelco        string                 `protobuf:"bytes,2,opt,name=id_telco,json=idTelco,proto3" json:"id_telco,omitempty"`
	IsEnable       bool                   `protobuf:"varint,3,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest) Reset() {
	*x = BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{12}
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest) GetIdTelco() string {
	if x != nil {
		return x.IdTelco
	}
	return ""
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

type BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse) Reset() {
	*x = BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{13}
}

func (x *BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeProxyProfileServiceSetDefaultProxyProfileRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdPlan         string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdProxyProfile string                 `protobuf:"bytes,2,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceSetDefaultProxyProfileRequest) Reset() {
	*x = BackofficeProxyProfileServiceSetDefaultProxyProfileRequest{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceSetDefaultProxyProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceSetDefaultProxyProfileRequest) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceSetDefaultProxyProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceSetDefaultProxyProfileRequest.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceSetDefaultProxyProfileRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{14}
}

func (x *BackofficeProxyProfileServiceSetDefaultProxyProfileRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *BackofficeProxyProfileServiceSetDefaultProxyProfileRequest) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

type BackofficeProxyProfileServiceSetDefaultProxyProfileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceSetDefaultProxyProfileResponse) Reset() {
	*x = BackofficeProxyProfileServiceSetDefaultProxyProfileResponse{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceSetDefaultProxyProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceSetDefaultProxyProfileResponse) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceSetDefaultProxyProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceSetDefaultProxyProfileResponse.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceSetDefaultProxyProfileResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{15}
}

func (x *BackofficeProxyProfileServiceSetDefaultProxyProfileResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeProxyProfileServiceCreateProxyProfileRequest struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	IdPlan                     string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdDns                      string                 `protobuf:"bytes,2,opt,name=id_dns,json=idDns,proto3" json:"id_dns,omitempty"`
	Name                       string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	AutoSwitchIpAfterInSec     int64                  `protobuf:"varint,4,opt,name=auto_switch_ip_after_in_sec,json=autoSwitchIpAfterInSec,proto3" json:"auto_switch_ip_after_in_sec,omitempty"`
	AutoSwitchIpWhenDisconnect bool                   `protobuf:"varint,5,opt,name=auto_switch_ip_when_disconnect,json=autoSwitchIpWhenDisconnect,proto3" json:"auto_switch_ip_when_disconnect,omitempty"`
	IsStaticUsernamePassword   bool                   `protobuf:"varint,6,opt,name=is_static_username_password,json=isStaticUsernamePassword,proto3" json:"is_static_username_password,omitempty"`
	BandwidthPerProxyMbit      int64                  `protobuf:"varint,7,opt,name=bandwidth_per_proxy_mbit,json=bandwidthPerProxyMbit,proto3" json:"bandwidth_per_proxy_mbit,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceCreateProxyProfileRequest) Reset() {
	*x = BackofficeProxyProfileServiceCreateProxyProfileRequest{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceCreateProxyProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceCreateProxyProfileRequest) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceCreateProxyProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceCreateProxyProfileRequest.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceCreateProxyProfileRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{16}
}

func (x *BackofficeProxyProfileServiceCreateProxyProfileRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *BackofficeProxyProfileServiceCreateProxyProfileRequest) GetIdDns() string {
	if x != nil {
		return x.IdDns
	}
	return ""
}

func (x *BackofficeProxyProfileServiceCreateProxyProfileRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeProxyProfileServiceCreateProxyProfileRequest) GetAutoSwitchIpAfterInSec() int64 {
	if x != nil {
		return x.AutoSwitchIpAfterInSec
	}
	return 0
}

func (x *BackofficeProxyProfileServiceCreateProxyProfileRequest) GetAutoSwitchIpWhenDisconnect() bool {
	if x != nil {
		return x.AutoSwitchIpWhenDisconnect
	}
	return false
}

func (x *BackofficeProxyProfileServiceCreateProxyProfileRequest) GetIsStaticUsernamePassword() bool {
	if x != nil {
		return x.IsStaticUsernamePassword
	}
	return false
}

func (x *BackofficeProxyProfileServiceCreateProxyProfileRequest) GetBandwidthPerProxyMbit() int64 {
	if x != nil {
		return x.BandwidthPerProxyMbit
	}
	return 0
}

type BackofficeProxyProfileServiceCreateProxyProfileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceCreateProxyProfileResponse) Reset() {
	*x = BackofficeProxyProfileServiceCreateProxyProfileResponse{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceCreateProxyProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceCreateProxyProfileResponse) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceCreateProxyProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceCreateProxyProfileResponse.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceCreateProxyProfileResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{17}
}

func (x *BackofficeProxyProfileServiceCreateProxyProfileResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeProxyProfileServiceUpdateProxyProfileRequest struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	IdProxyProfile             string                 `protobuf:"bytes,1,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	IdDns                      string                 `protobuf:"bytes,2,opt,name=id_dns,json=idDns,proto3" json:"id_dns,omitempty"`
	Name                       string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	AutoSwitchIpAfterInSec     int64                  `protobuf:"varint,4,opt,name=auto_switch_ip_after_in_sec,json=autoSwitchIpAfterInSec,proto3" json:"auto_switch_ip_after_in_sec,omitempty"`
	AutoSwitchIpWhenDisconnect bool                   `protobuf:"varint,5,opt,name=auto_switch_ip_when_disconnect,json=autoSwitchIpWhenDisconnect,proto3" json:"auto_switch_ip_when_disconnect,omitempty"`
	IsStaticUsernamePassword   bool                   `protobuf:"varint,6,opt,name=is_static_username_password,json=isStaticUsernamePassword,proto3" json:"is_static_username_password,omitempty"`
	RevokeUsernamePassword     bool                   `protobuf:"varint,7,opt,name=revoke_username_password,json=revokeUsernamePassword,proto3" json:"revoke_username_password,omitempty"`
	BandwidthPerProxyMbit      int64                  `protobuf:"varint,8,opt,name=bandwidth_per_proxy_mbit,json=bandwidthPerProxyMbit,proto3" json:"bandwidth_per_proxy_mbit,omitempty"`
	State                      *v11.State             `protobuf:"bytes,9,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceUpdateProxyProfileRequest) Reset() {
	*x = BackofficeProxyProfileServiceUpdateProxyProfileRequest{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceUpdateProxyProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceUpdateProxyProfileRequest) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceUpdateProxyProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceUpdateProxyProfileRequest.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceUpdateProxyProfileRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{18}
}

func (x *BackofficeProxyProfileServiceUpdateProxyProfileRequest) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

func (x *BackofficeProxyProfileServiceUpdateProxyProfileRequest) GetIdDns() string {
	if x != nil {
		return x.IdDns
	}
	return ""
}

func (x *BackofficeProxyProfileServiceUpdateProxyProfileRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeProxyProfileServiceUpdateProxyProfileRequest) GetAutoSwitchIpAfterInSec() int64 {
	if x != nil {
		return x.AutoSwitchIpAfterInSec
	}
	return 0
}

func (x *BackofficeProxyProfileServiceUpdateProxyProfileRequest) GetAutoSwitchIpWhenDisconnect() bool {
	if x != nil {
		return x.AutoSwitchIpWhenDisconnect
	}
	return false
}

func (x *BackofficeProxyProfileServiceUpdateProxyProfileRequest) GetIsStaticUsernamePassword() bool {
	if x != nil {
		return x.IsStaticUsernamePassword
	}
	return false
}

func (x *BackofficeProxyProfileServiceUpdateProxyProfileRequest) GetRevokeUsernamePassword() bool {
	if x != nil {
		return x.RevokeUsernamePassword
	}
	return false
}

func (x *BackofficeProxyProfileServiceUpdateProxyProfileRequest) GetBandwidthPerProxyMbit() int64 {
	if x != nil {
		return x.BandwidthPerProxyMbit
	}
	return 0
}

func (x *BackofficeProxyProfileServiceUpdateProxyProfileRequest) GetState() *v11.State {
	if x != nil {
		return x.State
	}
	return nil
}

type BackofficeProxyProfileServiceUpdateProxyProfileResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeProxyProfileServiceUpdateProxyProfileResponse) Reset() {
	*x = BackofficeProxyProfileServiceUpdateProxyProfileResponse{}
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeProxyProfileServiceUpdateProxyProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeProxyProfileServiceUpdateProxyProfileResponse) ProtoMessage() {}

func (x *BackofficeProxyProfileServiceUpdateProxyProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeProxyProfileServiceUpdateProxyProfileResponse.ProtoReflect.Descriptor instead.
func (*BackofficeProxyProfileServiceUpdateProxyProfileResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP(), []int{19}
}

func (x *BackofficeProxyProfileServiceUpdateProxyProfileResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_proxymanager_proxyprofile_v1_backoffice_proto protoreflect.FileDescriptor

const file_proxymanager_proxyprofile_v1_backoffice_proto_rawDesc = "" +
	"\n" +
	"-proxymanager/proxyprofile/v1/backoffice.proto\x12\x1cproxymanager.proxyprofile.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\x1a algoenum/v1/location_level.proto\"a\n" +
	"5BackofficeProxyProfileServiceResetProxyProfileRequest\x12(\n" +
	"\x10id_proxy_profile\x18\x01 \x01(\tR\x0eidProxyProfile\"g\n" +
	"6BackofficeProxyProfileServiceResetProxyProfileResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x98\x02\n" +
	"5BackofficeProxyProfileServiceFetchProxyProfileRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12\x17\n" +
	"\aid_user\x18\x02 \x01(\tR\x06idUser\x12\x17\n" +
	"\aid_plan\x18\x03 \x01(\tR\x06idPlan\x12(\n" +
	"\x10id_proxy_profile\x18\x04 \x01(\tR\x0eidProxyProfile\x12%\n" +
	"\x05state\x18\x05 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x06 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x9b\x02\n" +
	"6BackofficeProxyProfileServiceFetchProxyProfileResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12t\n" +
	"\x0eproxy_profiles\x18\x03 \x03(\v2M.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntityR\rproxyProfiles\"\x9f\a\n" +
	"/BackofficeProxyProfileServiceProxyProfileEntity\x12(\n" +
	"\x10id_proxy_profile\x18\x01 \x01(\tR\x0eidProxyProfile\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12b\n" +
	"\x03dns\x18\x03 \x01(\v2P.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntityDNSR\x03dns\x12;\n" +
	"\x1bauto_switch_ip_after_in_sec\x18\x04 \x01(\x03R\x16autoSwitchIpAfterInSec\x12B\n" +
	"\x1eauto_switch_ip_when_disconnect\x18\x05 \x01(\bR\x1aautoSwitchIpWhenDisconnect\x12=\n" +
	"\x1bis_static_username_password\x18\x06 \x01(\bR\x18isStaticUsernamePassword\x12%\n" +
	"\x0eusername_proxy\x18\a \x01(\tR\rusernameProxy\x12%\n" +
	"\x0epassword_proxy\x18\b \x01(\tR\rpasswordProxy\x12\x1b\n" +
	"\tip_allows\x18\t \x03(\tR\bipAllows\x12j\n" +
	"\x06telcos\x18\n" +
	" \x03(\v2R.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntityTelcoR\x06telcos\x12s\n" +
	"\tlocations\x18\v \x03(\v2U.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntityLocationR\tlocations\x12\x1b\n" +
	"\tis_active\x18\f \x01(\bR\bisActive\x12,\n" +
	"\x12is_default_profile\x18\r \x01(\bR\x10isDefaultProfile\x12\x1d\n" +
	"\n" +
	"can_update\x18\x0e \x01(\bR\tcanUpdate\x127\n" +
	"\x18bandwidth_per_proxy_mbit\x18\x0f \x01(\x03R\x15bandwidthPerProxyMbit\x12\x1b\n" +
	"\tis_public\x18\x10 \x01(\bR\bisPublic\"\x89\x01\n" +
	"2BackofficeProxyProfileServiceProxyProfileEntityDNS\x12\x15\n" +
	"\x06id_dns\x18\x01 \x01(\tR\x05idDns\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x13\n" +
	"\x05dns_1\x18\x03 \x01(\tR\x04dns1\x12\x13\n" +
	"\x05dns_2\x18\x04 \x01(\tR\x04dns2\"e\n" +
	"4BackofficeProxyProfileServiceProxyProfileEntityTelco\x12\x19\n" +
	"\bid_telco\x18\x01 \x01(\tR\aidTelco\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"\xa0\x01\n" +
	"7BackofficeProxyProfileServiceProxyProfileEntityLocation\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x120\n" +
	"\x05level\x18\x02 \x01(\x0e2\x1a.algoenum.v1.LocationLevelR\x05level\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\"\xa8\x01\n" +
	">BackofficeProxyProfileServiceConfigProxyProfileLocationRequest\x12(\n" +
	"\x10id_proxy_profile\x18\x01 \x01(\tR\x0eidProxyProfile\x12\x1f\n" +
	"\vid_location\x18\x02 \x01(\tR\n" +
	"idLocation\x12\x1b\n" +
	"\tis_enable\x18\x03 \x01(\bR\bisEnable\"p\n" +
	"?BackofficeProxyProfileServiceConfigProxyProfileLocationResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xa1\x01\n" +
	"=BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest\x12(\n" +
	"\x10id_proxy_profile\x18\x01 \x01(\tR\x0eidProxyProfile\x12\x19\n" +
	"\bip_allow\x18\x02 \x01(\tR\aipAllow\x12\x1b\n" +
	"\tis_enable\x18\x03 \x01(\bR\bisEnable\"o\n" +
	">BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x9f\x01\n" +
	";BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest\x12(\n" +
	"\x10id_proxy_profile\x18\x01 \x01(\tR\x0eidProxyProfile\x12\x19\n" +
	"\bid_telco\x18\x02 \x01(\tR\aidTelco\x12\x1b\n" +
	"\tis_enable\x18\x03 \x01(\bR\bisEnable\"m\n" +
	"<BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x7f\n" +
	":BackofficeProxyProfileServiceSetDefaultProxyProfileRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12(\n" +
	"\x10id_proxy_profile\x18\x02 \x01(\tR\x0eidProxyProfile\"l\n" +
	";BackofficeProxyProfileServiceSetDefaultProxyProfileResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xf5\x02\n" +
	"6BackofficeProxyProfileServiceCreateProxyProfileRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x15\n" +
	"\x06id_dns\x18\x02 \x01(\tR\x05idDns\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12;\n" +
	"\x1bauto_switch_ip_after_in_sec\x18\x04 \x01(\x03R\x16autoSwitchIpAfterInSec\x12B\n" +
	"\x1eauto_switch_ip_when_disconnect\x18\x05 \x01(\bR\x1aautoSwitchIpWhenDisconnect\x12=\n" +
	"\x1bis_static_username_password\x18\x06 \x01(\bR\x18isStaticUsernamePassword\x127\n" +
	"\x18bandwidth_per_proxy_mbit\x18\a \x01(\x03R\x15bandwidthPerProxyMbit\"h\n" +
	"7BackofficeProxyProfileServiceCreateProxyProfileResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xe7\x03\n" +
	"6BackofficeProxyProfileServiceUpdateProxyProfileRequest\x12(\n" +
	"\x10id_proxy_profile\x18\x01 \x01(\tR\x0eidProxyProfile\x12\x15\n" +
	"\x06id_dns\x18\x02 \x01(\tR\x05idDns\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12;\n" +
	"\x1bauto_switch_ip_after_in_sec\x18\x04 \x01(\x03R\x16autoSwitchIpAfterInSec\x12B\n" +
	"\x1eauto_switch_ip_when_disconnect\x18\x05 \x01(\bR\x1aautoSwitchIpWhenDisconnect\x12=\n" +
	"\x1bis_static_username_password\x18\x06 \x01(\bR\x18isStaticUsernamePassword\x128\n" +
	"\x18revoke_username_password\x18\a \x01(\bR\x16revokeUsernamePassword\x127\n" +
	"\x18bandwidth_per_proxy_mbit\x18\b \x01(\x03R\x15bandwidthPerProxyMbit\x12%\n" +
	"\x05state\x18\t \x01(\v2\x0f.utils.v1.StateR\x05state\"h\n" +
	"7BackofficeProxyProfileServiceUpdateProxyProfileResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error2\x81\r\n" +
	"\x1dBackofficeProxyProfileService\x12\xbe\x01\n" +
	"\x11FetchProxyProfile\x12S.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceFetchProxyProfileRequest\x1aT.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceFetchProxyProfileResponse\x12\xbe\x01\n" +
	"\x11ResetProxyProfile\x12S.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceResetProxyProfileRequest\x1aT.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceResetProxyProfileResponse\x12\xcd\x01\n" +
	"\x16SetDefaultProxyProfile\x12X.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceSetDefaultProxyProfileRequest\x1aY.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceSetDefaultProxyProfileResponse\x12\xc1\x01\n" +
	"\x12CreateProxyProfile\x12T.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceCreateProxyProfileRequest\x1aU.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceCreateProxyProfileResponse\x12\xc1\x01\n" +
	"\x12UpdateProxyProfile\x12T.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceUpdateProxyProfileRequest\x1aU.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceUpdateProxyProfileResponse\x12\xd9\x01\n" +
	"\x1aConfigProxyProfileLocation\x12\\.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileLocationRequest\x1a].proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileLocationResponse\x12\xd6\x01\n" +
	"\x19ConfigProxyProfileIPAllow\x12[.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest\x1a\\.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse\x12\xd0\x01\n" +
	"\x17ConfigProxyProfileTelco\x12Y.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest\x1aZ.proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoResponseB\\ZZgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/proxyprofile/v1;proxyprofilev1b\x06proto3"

var (
	file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescOnce sync.Once
	file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescData []byte
)

func file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescGZIP() []byte {
	file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescOnce.Do(func() {
		file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_proxyprofile_v1_backoffice_proto_rawDesc), len(file_proxymanager_proxyprofile_v1_backoffice_proto_rawDesc)))
	})
	return file_proxymanager_proxyprofile_v1_backoffice_proto_rawDescData
}

var file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_proxymanager_proxyprofile_v1_backoffice_proto_goTypes = []any{
	(*BackofficeProxyProfileServiceResetProxyProfileRequest)(nil),           // 0: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceResetProxyProfileRequest
	(*BackofficeProxyProfileServiceResetProxyProfileResponse)(nil),          // 1: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceResetProxyProfileResponse
	(*BackofficeProxyProfileServiceFetchProxyProfileRequest)(nil),           // 2: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceFetchProxyProfileRequest
	(*BackofficeProxyProfileServiceFetchProxyProfileResponse)(nil),          // 3: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceFetchProxyProfileResponse
	(*BackofficeProxyProfileServiceProxyProfileEntity)(nil),                 // 4: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntity
	(*BackofficeProxyProfileServiceProxyProfileEntityDNS)(nil),              // 5: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntityDNS
	(*BackofficeProxyProfileServiceProxyProfileEntityTelco)(nil),            // 6: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntityTelco
	(*BackofficeProxyProfileServiceProxyProfileEntityLocation)(nil),         // 7: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntityLocation
	(*BackofficeProxyProfileServiceConfigProxyProfileLocationRequest)(nil),  // 8: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileLocationRequest
	(*BackofficeProxyProfileServiceConfigProxyProfileLocationResponse)(nil), // 9: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileLocationResponse
	(*BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest)(nil),   // 10: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest
	(*BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse)(nil),  // 11: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse
	(*BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest)(nil),     // 12: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest
	(*BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse)(nil),    // 13: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse
	(*BackofficeProxyProfileServiceSetDefaultProxyProfileRequest)(nil),      // 14: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceSetDefaultProxyProfileRequest
	(*BackofficeProxyProfileServiceSetDefaultProxyProfileResponse)(nil),     // 15: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceSetDefaultProxyProfileResponse
	(*BackofficeProxyProfileServiceCreateProxyProfileRequest)(nil),          // 16: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceCreateProxyProfileRequest
	(*BackofficeProxyProfileServiceCreateProxyProfileResponse)(nil),         // 17: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceCreateProxyProfileResponse
	(*BackofficeProxyProfileServiceUpdateProxyProfileRequest)(nil),          // 18: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceUpdateProxyProfileRequest
	(*BackofficeProxyProfileServiceUpdateProxyProfileResponse)(nil),         // 19: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceUpdateProxyProfileResponse
	(*v1.ErrorMessage)(nil),        // 20: errmsg.v1.ErrorMessage
	(*v11.State)(nil),              // 21: utils.v1.State
	(*v11.PaginationRequest)(nil),  // 22: utils.v1.PaginationRequest
	(*v11.PaginationResponse)(nil), // 23: utils.v1.PaginationResponse
	(v12.LocationLevel)(0),         // 24: algoenum.v1.LocationLevel
}
var file_proxymanager_proxyprofile_v1_backoffice_proto_depIdxs = []int32{
	20, // 0: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceResetProxyProfileResponse.error:type_name -> errmsg.v1.ErrorMessage
	21, // 1: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceFetchProxyProfileRequest.state:type_name -> utils.v1.State
	22, // 2: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceFetchProxyProfileRequest.pagination:type_name -> utils.v1.PaginationRequest
	20, // 3: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceFetchProxyProfileResponse.error:type_name -> errmsg.v1.ErrorMessage
	23, // 4: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceFetchProxyProfileResponse.pagination:type_name -> utils.v1.PaginationResponse
	4,  // 5: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceFetchProxyProfileResponse.proxy_profiles:type_name -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntity
	5,  // 6: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntity.dns:type_name -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntityDNS
	6,  // 7: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntity.telcos:type_name -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntityTelco
	7,  // 8: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntity.locations:type_name -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntityLocation
	24, // 9: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntityLocation.level:type_name -> algoenum.v1.LocationLevel
	20, // 10: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileLocationResponse.error:type_name -> errmsg.v1.ErrorMessage
	20, // 11: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse.error:type_name -> errmsg.v1.ErrorMessage
	20, // 12: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse.error:type_name -> errmsg.v1.ErrorMessage
	20, // 13: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceSetDefaultProxyProfileResponse.error:type_name -> errmsg.v1.ErrorMessage
	20, // 14: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceCreateProxyProfileResponse.error:type_name -> errmsg.v1.ErrorMessage
	21, // 15: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceUpdateProxyProfileRequest.state:type_name -> utils.v1.State
	20, // 16: proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceUpdateProxyProfileResponse.error:type_name -> errmsg.v1.ErrorMessage
	2,  // 17: proxymanager.proxyprofile.v1.BackofficeProxyProfileService.FetchProxyProfile:input_type -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceFetchProxyProfileRequest
	0,  // 18: proxymanager.proxyprofile.v1.BackofficeProxyProfileService.ResetProxyProfile:input_type -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceResetProxyProfileRequest
	14, // 19: proxymanager.proxyprofile.v1.BackofficeProxyProfileService.SetDefaultProxyProfile:input_type -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceSetDefaultProxyProfileRequest
	16, // 20: proxymanager.proxyprofile.v1.BackofficeProxyProfileService.CreateProxyProfile:input_type -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceCreateProxyProfileRequest
	18, // 21: proxymanager.proxyprofile.v1.BackofficeProxyProfileService.UpdateProxyProfile:input_type -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceUpdateProxyProfileRequest
	8,  // 22: proxymanager.proxyprofile.v1.BackofficeProxyProfileService.ConfigProxyProfileLocation:input_type -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileLocationRequest
	10, // 23: proxymanager.proxyprofile.v1.BackofficeProxyProfileService.ConfigProxyProfileIPAllow:input_type -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest
	12, // 24: proxymanager.proxyprofile.v1.BackofficeProxyProfileService.ConfigProxyProfileTelco:input_type -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest
	3,  // 25: proxymanager.proxyprofile.v1.BackofficeProxyProfileService.FetchProxyProfile:output_type -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceFetchProxyProfileResponse
	1,  // 26: proxymanager.proxyprofile.v1.BackofficeProxyProfileService.ResetProxyProfile:output_type -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceResetProxyProfileResponse
	15, // 27: proxymanager.proxyprofile.v1.BackofficeProxyProfileService.SetDefaultProxyProfile:output_type -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceSetDefaultProxyProfileResponse
	17, // 28: proxymanager.proxyprofile.v1.BackofficeProxyProfileService.CreateProxyProfile:output_type -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceCreateProxyProfileResponse
	19, // 29: proxymanager.proxyprofile.v1.BackofficeProxyProfileService.UpdateProxyProfile:output_type -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceUpdateProxyProfileResponse
	9,  // 30: proxymanager.proxyprofile.v1.BackofficeProxyProfileService.ConfigProxyProfileLocation:output_type -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileLocationResponse
	11, // 31: proxymanager.proxyprofile.v1.BackofficeProxyProfileService.ConfigProxyProfileIPAllow:output_type -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse
	13, // 32: proxymanager.proxyprofile.v1.BackofficeProxyProfileService.ConfigProxyProfileTelco:output_type -> proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse
	25, // [25:33] is the sub-list for method output_type
	17, // [17:25] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_proxymanager_proxyprofile_v1_backoffice_proto_init() }
func file_proxymanager_proxyprofile_v1_backoffice_proto_init() {
	if File_proxymanager_proxyprofile_v1_backoffice_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_proxyprofile_v1_backoffice_proto_rawDesc), len(file_proxymanager_proxyprofile_v1_backoffice_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_proxyprofile_v1_backoffice_proto_goTypes,
		DependencyIndexes: file_proxymanager_proxyprofile_v1_backoffice_proto_depIdxs,
		MessageInfos:      file_proxymanager_proxyprofile_v1_backoffice_proto_msgTypes,
	}.Build()
	File_proxymanager_proxyprofile_v1_backoffice_proto = out.File
	file_proxymanager_proxyprofile_v1_backoffice_proto_goTypes = nil
	file_proxymanager_proxyprofile_v1_backoffice_proto_depIdxs = nil
}
