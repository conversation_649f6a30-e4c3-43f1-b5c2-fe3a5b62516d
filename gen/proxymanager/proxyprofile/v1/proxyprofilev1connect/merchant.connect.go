// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/proxyprofile/v1/merchant.proto

package proxyprofilev1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/proxyprofile/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// MerchantProxyProfileServiceName is the fully-qualified name of the MerchantProxyProfileService
	// service.
	MerchantProxyProfileServiceName = "proxymanager.proxyprofile.v1.MerchantProxyProfileService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// MerchantProxyProfileServiceFetchProxyProfileProcedure is the fully-qualified name of the
	// MerchantProxyProfileService's FetchProxyProfile RPC.
	MerchantProxyProfileServiceFetchProxyProfileProcedure = "/proxymanager.proxyprofile.v1.MerchantProxyProfileService/FetchProxyProfile"
)

// MerchantProxyProfileServiceClient is a client for the
// proxymanager.proxyprofile.v1.MerchantProxyProfileService service.
type MerchantProxyProfileServiceClient interface {
	FetchProxyProfile(context.Context, *connect.Request[v1.MerchantProxyProfileServiceFetchProxyProfileRequest]) (*connect.Response[v1.MerchantProxyProfileServiceFetchProxyProfileResponse], error)
}

// NewMerchantProxyProfileServiceClient constructs a client for the
// proxymanager.proxyprofile.v1.MerchantProxyProfileService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewMerchantProxyProfileServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) MerchantProxyProfileServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	merchantProxyProfileServiceMethods := v1.File_proxymanager_proxyprofile_v1_merchant_proto.Services().ByName("MerchantProxyProfileService").Methods()
	return &merchantProxyProfileServiceClient{
		fetchProxyProfile: connect.NewClient[v1.MerchantProxyProfileServiceFetchProxyProfileRequest, v1.MerchantProxyProfileServiceFetchProxyProfileResponse](
			httpClient,
			baseURL+MerchantProxyProfileServiceFetchProxyProfileProcedure,
			connect.WithSchema(merchantProxyProfileServiceMethods.ByName("FetchProxyProfile")),
			connect.WithClientOptions(opts...),
		),
	}
}

// merchantProxyProfileServiceClient implements MerchantProxyProfileServiceClient.
type merchantProxyProfileServiceClient struct {
	fetchProxyProfile *connect.Client[v1.MerchantProxyProfileServiceFetchProxyProfileRequest, v1.MerchantProxyProfileServiceFetchProxyProfileResponse]
}

// FetchProxyProfile calls
// proxymanager.proxyprofile.v1.MerchantProxyProfileService.FetchProxyProfile.
func (c *merchantProxyProfileServiceClient) FetchProxyProfile(ctx context.Context, req *connect.Request[v1.MerchantProxyProfileServiceFetchProxyProfileRequest]) (*connect.Response[v1.MerchantProxyProfileServiceFetchProxyProfileResponse], error) {
	return c.fetchProxyProfile.CallUnary(ctx, req)
}

// MerchantProxyProfileServiceHandler is an implementation of the
// proxymanager.proxyprofile.v1.MerchantProxyProfileService service.
type MerchantProxyProfileServiceHandler interface {
	FetchProxyProfile(context.Context, *connect.Request[v1.MerchantProxyProfileServiceFetchProxyProfileRequest]) (*connect.Response[v1.MerchantProxyProfileServiceFetchProxyProfileResponse], error)
}

// NewMerchantProxyProfileServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewMerchantProxyProfileServiceHandler(svc MerchantProxyProfileServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	merchantProxyProfileServiceMethods := v1.File_proxymanager_proxyprofile_v1_merchant_proto.Services().ByName("MerchantProxyProfileService").Methods()
	merchantProxyProfileServiceFetchProxyProfileHandler := connect.NewUnaryHandler(
		MerchantProxyProfileServiceFetchProxyProfileProcedure,
		svc.FetchProxyProfile,
		connect.WithSchema(merchantProxyProfileServiceMethods.ByName("FetchProxyProfile")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.proxyprofile.v1.MerchantProxyProfileService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case MerchantProxyProfileServiceFetchProxyProfileProcedure:
			merchantProxyProfileServiceFetchProxyProfileHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedMerchantProxyProfileServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedMerchantProxyProfileServiceHandler struct{}

func (UnimplementedMerchantProxyProfileServiceHandler) FetchProxyProfile(context.Context, *connect.Request[v1.MerchantProxyProfileServiceFetchProxyProfileRequest]) (*connect.Response[v1.MerchantProxyProfileServiceFetchProxyProfileResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxyprofile.v1.MerchantProxyProfileService.FetchProxyProfile is not implemented"))
}
