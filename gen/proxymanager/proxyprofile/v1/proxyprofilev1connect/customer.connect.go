// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/proxyprofile/v1/customer.proto

package proxyprofilev1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/proxyprofile/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CustomerProxyProfileServiceName is the fully-qualified name of the CustomerProxyProfileService
	// service.
	CustomerProxyProfileServiceName = "proxymanager.proxyprofile.v1.CustomerProxyProfileService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CustomerProxyProfileServiceApplyProxyProfileProcedure is the fully-qualified name of the
	// CustomerProxyProfileService's ApplyProxyProfile RPC.
	CustomerProxyProfileServiceApplyProxyProfileProcedure = "/proxymanager.proxyprofile.v1.CustomerProxyProfileService/ApplyProxyProfile"
	// CustomerProxyProfileServiceResetProxyProfileProcedure is the fully-qualified name of the
	// CustomerProxyProfileService's ResetProxyProfile RPC.
	CustomerProxyProfileServiceResetProxyProfileProcedure = "/proxymanager.proxyprofile.v1.CustomerProxyProfileService/ResetProxyProfile"
	// CustomerProxyProfileServiceFetchProxyProfileProcedure is the fully-qualified name of the
	// CustomerProxyProfileService's FetchProxyProfile RPC.
	CustomerProxyProfileServiceFetchProxyProfileProcedure = "/proxymanager.proxyprofile.v1.CustomerProxyProfileService/FetchProxyProfile"
	// CustomerProxyProfileServiceCreateProxyProfileProcedure is the fully-qualified name of the
	// CustomerProxyProfileService's CreateProxyProfile RPC.
	CustomerProxyProfileServiceCreateProxyProfileProcedure = "/proxymanager.proxyprofile.v1.CustomerProxyProfileService/CreateProxyProfile"
	// CustomerProxyProfileServiceUpdateProxyProfileProcedure is the fully-qualified name of the
	// CustomerProxyProfileService's UpdateProxyProfile RPC.
	CustomerProxyProfileServiceUpdateProxyProfileProcedure = "/proxymanager.proxyprofile.v1.CustomerProxyProfileService/UpdateProxyProfile"
	// CustomerProxyProfileServiceConfigProxyProfileLocationProcedure is the fully-qualified name of the
	// CustomerProxyProfileService's ConfigProxyProfileLocation RPC.
	CustomerProxyProfileServiceConfigProxyProfileLocationProcedure = "/proxymanager.proxyprofile.v1.CustomerProxyProfileService/ConfigProxyProfileLocation"
	// CustomerProxyProfileServiceConfigProxyProfileIPAllowProcedure is the fully-qualified name of the
	// CustomerProxyProfileService's ConfigProxyProfileIPAllow RPC.
	CustomerProxyProfileServiceConfigProxyProfileIPAllowProcedure = "/proxymanager.proxyprofile.v1.CustomerProxyProfileService/ConfigProxyProfileIPAllow"
	// CustomerProxyProfileServiceConfigProxyProfileTelcoProcedure is the fully-qualified name of the
	// CustomerProxyProfileService's ConfigProxyProfileTelco RPC.
	CustomerProxyProfileServiceConfigProxyProfileTelcoProcedure = "/proxymanager.proxyprofile.v1.CustomerProxyProfileService/ConfigProxyProfileTelco"
)

// CustomerProxyProfileServiceClient is a client for the
// proxymanager.proxyprofile.v1.CustomerProxyProfileService service.
type CustomerProxyProfileServiceClient interface {
	ApplyProxyProfile(context.Context, *connect.Request[v1.CustomerProxyProfileServiceApplyProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceApplyProxyProfileResponse], error)
	ResetProxyProfile(context.Context, *connect.Request[v1.CustomerProxyProfileServiceResetProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceResetProxyProfileResponse], error)
	FetchProxyProfile(context.Context, *connect.Request[v1.CustomerProxyProfileServiceFetchProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceFetchProxyProfileResponse], error)
	CreateProxyProfile(context.Context, *connect.Request[v1.CustomerProxyProfileServiceCreateProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceCreateProxyProfileResponse], error)
	UpdateProxyProfile(context.Context, *connect.Request[v1.CustomerProxyProfileServiceUpdateProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceUpdateProxyProfileResponse], error)
	ConfigProxyProfileLocation(context.Context, *connect.Request[v1.CustomerProxyProfileServiceConfigProxyProfileLocationRequest]) (*connect.Response[v1.CustomerProxyProfileServiceConfigProxyProfileLocationResponse], error)
	ConfigProxyProfileIPAllow(context.Context, *connect.Request[v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest]) (*connect.Response[v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse], error)
	ConfigProxyProfileTelco(context.Context, *connect.Request[v1.CustomerProxyProfileServiceConfigProxyProfileTelcoRequest]) (*connect.Response[v1.CustomerProxyProfileServiceConfigProxyProfileTelcoResponse], error)
}

// NewCustomerProxyProfileServiceClient constructs a client for the
// proxymanager.proxyprofile.v1.CustomerProxyProfileService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCustomerProxyProfileServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CustomerProxyProfileServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	customerProxyProfileServiceMethods := v1.File_proxymanager_proxyprofile_v1_customer_proto.Services().ByName("CustomerProxyProfileService").Methods()
	return &customerProxyProfileServiceClient{
		applyProxyProfile: connect.NewClient[v1.CustomerProxyProfileServiceApplyProxyProfileRequest, v1.CustomerProxyProfileServiceApplyProxyProfileResponse](
			httpClient,
			baseURL+CustomerProxyProfileServiceApplyProxyProfileProcedure,
			connect.WithSchema(customerProxyProfileServiceMethods.ByName("ApplyProxyProfile")),
			connect.WithClientOptions(opts...),
		),
		resetProxyProfile: connect.NewClient[v1.CustomerProxyProfileServiceResetProxyProfileRequest, v1.CustomerProxyProfileServiceResetProxyProfileResponse](
			httpClient,
			baseURL+CustomerProxyProfileServiceResetProxyProfileProcedure,
			connect.WithSchema(customerProxyProfileServiceMethods.ByName("ResetProxyProfile")),
			connect.WithClientOptions(opts...),
		),
		fetchProxyProfile: connect.NewClient[v1.CustomerProxyProfileServiceFetchProxyProfileRequest, v1.CustomerProxyProfileServiceFetchProxyProfileResponse](
			httpClient,
			baseURL+CustomerProxyProfileServiceFetchProxyProfileProcedure,
			connect.WithSchema(customerProxyProfileServiceMethods.ByName("FetchProxyProfile")),
			connect.WithClientOptions(opts...),
		),
		createProxyProfile: connect.NewClient[v1.CustomerProxyProfileServiceCreateProxyProfileRequest, v1.CustomerProxyProfileServiceCreateProxyProfileResponse](
			httpClient,
			baseURL+CustomerProxyProfileServiceCreateProxyProfileProcedure,
			connect.WithSchema(customerProxyProfileServiceMethods.ByName("CreateProxyProfile")),
			connect.WithClientOptions(opts...),
		),
		updateProxyProfile: connect.NewClient[v1.CustomerProxyProfileServiceUpdateProxyProfileRequest, v1.CustomerProxyProfileServiceUpdateProxyProfileResponse](
			httpClient,
			baseURL+CustomerProxyProfileServiceUpdateProxyProfileProcedure,
			connect.WithSchema(customerProxyProfileServiceMethods.ByName("UpdateProxyProfile")),
			connect.WithClientOptions(opts...),
		),
		configProxyProfileLocation: connect.NewClient[v1.CustomerProxyProfileServiceConfigProxyProfileLocationRequest, v1.CustomerProxyProfileServiceConfigProxyProfileLocationResponse](
			httpClient,
			baseURL+CustomerProxyProfileServiceConfigProxyProfileLocationProcedure,
			connect.WithSchema(customerProxyProfileServiceMethods.ByName("ConfigProxyProfileLocation")),
			connect.WithClientOptions(opts...),
		),
		configProxyProfileIPAllow: connect.NewClient[v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest, v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse](
			httpClient,
			baseURL+CustomerProxyProfileServiceConfigProxyProfileIPAllowProcedure,
			connect.WithSchema(customerProxyProfileServiceMethods.ByName("ConfigProxyProfileIPAllow")),
			connect.WithClientOptions(opts...),
		),
		configProxyProfileTelco: connect.NewClient[v1.CustomerProxyProfileServiceConfigProxyProfileTelcoRequest, v1.CustomerProxyProfileServiceConfigProxyProfileTelcoResponse](
			httpClient,
			baseURL+CustomerProxyProfileServiceConfigProxyProfileTelcoProcedure,
			connect.WithSchema(customerProxyProfileServiceMethods.ByName("ConfigProxyProfileTelco")),
			connect.WithClientOptions(opts...),
		),
	}
}

// customerProxyProfileServiceClient implements CustomerProxyProfileServiceClient.
type customerProxyProfileServiceClient struct {
	applyProxyProfile          *connect.Client[v1.CustomerProxyProfileServiceApplyProxyProfileRequest, v1.CustomerProxyProfileServiceApplyProxyProfileResponse]
	resetProxyProfile          *connect.Client[v1.CustomerProxyProfileServiceResetProxyProfileRequest, v1.CustomerProxyProfileServiceResetProxyProfileResponse]
	fetchProxyProfile          *connect.Client[v1.CustomerProxyProfileServiceFetchProxyProfileRequest, v1.CustomerProxyProfileServiceFetchProxyProfileResponse]
	createProxyProfile         *connect.Client[v1.CustomerProxyProfileServiceCreateProxyProfileRequest, v1.CustomerProxyProfileServiceCreateProxyProfileResponse]
	updateProxyProfile         *connect.Client[v1.CustomerProxyProfileServiceUpdateProxyProfileRequest, v1.CustomerProxyProfileServiceUpdateProxyProfileResponse]
	configProxyProfileLocation *connect.Client[v1.CustomerProxyProfileServiceConfigProxyProfileLocationRequest, v1.CustomerProxyProfileServiceConfigProxyProfileLocationResponse]
	configProxyProfileIPAllow  *connect.Client[v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest, v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse]
	configProxyProfileTelco    *connect.Client[v1.CustomerProxyProfileServiceConfigProxyProfileTelcoRequest, v1.CustomerProxyProfileServiceConfigProxyProfileTelcoResponse]
}

// ApplyProxyProfile calls
// proxymanager.proxyprofile.v1.CustomerProxyProfileService.ApplyProxyProfile.
func (c *customerProxyProfileServiceClient) ApplyProxyProfile(ctx context.Context, req *connect.Request[v1.CustomerProxyProfileServiceApplyProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceApplyProxyProfileResponse], error) {
	return c.applyProxyProfile.CallUnary(ctx, req)
}

// ResetProxyProfile calls
// proxymanager.proxyprofile.v1.CustomerProxyProfileService.ResetProxyProfile.
func (c *customerProxyProfileServiceClient) ResetProxyProfile(ctx context.Context, req *connect.Request[v1.CustomerProxyProfileServiceResetProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceResetProxyProfileResponse], error) {
	return c.resetProxyProfile.CallUnary(ctx, req)
}

// FetchProxyProfile calls
// proxymanager.proxyprofile.v1.CustomerProxyProfileService.FetchProxyProfile.
func (c *customerProxyProfileServiceClient) FetchProxyProfile(ctx context.Context, req *connect.Request[v1.CustomerProxyProfileServiceFetchProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceFetchProxyProfileResponse], error) {
	return c.fetchProxyProfile.CallUnary(ctx, req)
}

// CreateProxyProfile calls
// proxymanager.proxyprofile.v1.CustomerProxyProfileService.CreateProxyProfile.
func (c *customerProxyProfileServiceClient) CreateProxyProfile(ctx context.Context, req *connect.Request[v1.CustomerProxyProfileServiceCreateProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceCreateProxyProfileResponse], error) {
	return c.createProxyProfile.CallUnary(ctx, req)
}

// UpdateProxyProfile calls
// proxymanager.proxyprofile.v1.CustomerProxyProfileService.UpdateProxyProfile.
func (c *customerProxyProfileServiceClient) UpdateProxyProfile(ctx context.Context, req *connect.Request[v1.CustomerProxyProfileServiceUpdateProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceUpdateProxyProfileResponse], error) {
	return c.updateProxyProfile.CallUnary(ctx, req)
}

// ConfigProxyProfileLocation calls
// proxymanager.proxyprofile.v1.CustomerProxyProfileService.ConfigProxyProfileLocation.
func (c *customerProxyProfileServiceClient) ConfigProxyProfileLocation(ctx context.Context, req *connect.Request[v1.CustomerProxyProfileServiceConfigProxyProfileLocationRequest]) (*connect.Response[v1.CustomerProxyProfileServiceConfigProxyProfileLocationResponse], error) {
	return c.configProxyProfileLocation.CallUnary(ctx, req)
}

// ConfigProxyProfileIPAllow calls
// proxymanager.proxyprofile.v1.CustomerProxyProfileService.ConfigProxyProfileIPAllow.
func (c *customerProxyProfileServiceClient) ConfigProxyProfileIPAllow(ctx context.Context, req *connect.Request[v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest]) (*connect.Response[v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse], error) {
	return c.configProxyProfileIPAllow.CallUnary(ctx, req)
}

// ConfigProxyProfileTelco calls
// proxymanager.proxyprofile.v1.CustomerProxyProfileService.ConfigProxyProfileTelco.
func (c *customerProxyProfileServiceClient) ConfigProxyProfileTelco(ctx context.Context, req *connect.Request[v1.CustomerProxyProfileServiceConfigProxyProfileTelcoRequest]) (*connect.Response[v1.CustomerProxyProfileServiceConfigProxyProfileTelcoResponse], error) {
	return c.configProxyProfileTelco.CallUnary(ctx, req)
}

// CustomerProxyProfileServiceHandler is an implementation of the
// proxymanager.proxyprofile.v1.CustomerProxyProfileService service.
type CustomerProxyProfileServiceHandler interface {
	ApplyProxyProfile(context.Context, *connect.Request[v1.CustomerProxyProfileServiceApplyProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceApplyProxyProfileResponse], error)
	ResetProxyProfile(context.Context, *connect.Request[v1.CustomerProxyProfileServiceResetProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceResetProxyProfileResponse], error)
	FetchProxyProfile(context.Context, *connect.Request[v1.CustomerProxyProfileServiceFetchProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceFetchProxyProfileResponse], error)
	CreateProxyProfile(context.Context, *connect.Request[v1.CustomerProxyProfileServiceCreateProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceCreateProxyProfileResponse], error)
	UpdateProxyProfile(context.Context, *connect.Request[v1.CustomerProxyProfileServiceUpdateProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceUpdateProxyProfileResponse], error)
	ConfigProxyProfileLocation(context.Context, *connect.Request[v1.CustomerProxyProfileServiceConfigProxyProfileLocationRequest]) (*connect.Response[v1.CustomerProxyProfileServiceConfigProxyProfileLocationResponse], error)
	ConfigProxyProfileIPAllow(context.Context, *connect.Request[v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest]) (*connect.Response[v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse], error)
	ConfigProxyProfileTelco(context.Context, *connect.Request[v1.CustomerProxyProfileServiceConfigProxyProfileTelcoRequest]) (*connect.Response[v1.CustomerProxyProfileServiceConfigProxyProfileTelcoResponse], error)
}

// NewCustomerProxyProfileServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCustomerProxyProfileServiceHandler(svc CustomerProxyProfileServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	customerProxyProfileServiceMethods := v1.File_proxymanager_proxyprofile_v1_customer_proto.Services().ByName("CustomerProxyProfileService").Methods()
	customerProxyProfileServiceApplyProxyProfileHandler := connect.NewUnaryHandler(
		CustomerProxyProfileServiceApplyProxyProfileProcedure,
		svc.ApplyProxyProfile,
		connect.WithSchema(customerProxyProfileServiceMethods.ByName("ApplyProxyProfile")),
		connect.WithHandlerOptions(opts...),
	)
	customerProxyProfileServiceResetProxyProfileHandler := connect.NewUnaryHandler(
		CustomerProxyProfileServiceResetProxyProfileProcedure,
		svc.ResetProxyProfile,
		connect.WithSchema(customerProxyProfileServiceMethods.ByName("ResetProxyProfile")),
		connect.WithHandlerOptions(opts...),
	)
	customerProxyProfileServiceFetchProxyProfileHandler := connect.NewUnaryHandler(
		CustomerProxyProfileServiceFetchProxyProfileProcedure,
		svc.FetchProxyProfile,
		connect.WithSchema(customerProxyProfileServiceMethods.ByName("FetchProxyProfile")),
		connect.WithHandlerOptions(opts...),
	)
	customerProxyProfileServiceCreateProxyProfileHandler := connect.NewUnaryHandler(
		CustomerProxyProfileServiceCreateProxyProfileProcedure,
		svc.CreateProxyProfile,
		connect.WithSchema(customerProxyProfileServiceMethods.ByName("CreateProxyProfile")),
		connect.WithHandlerOptions(opts...),
	)
	customerProxyProfileServiceUpdateProxyProfileHandler := connect.NewUnaryHandler(
		CustomerProxyProfileServiceUpdateProxyProfileProcedure,
		svc.UpdateProxyProfile,
		connect.WithSchema(customerProxyProfileServiceMethods.ByName("UpdateProxyProfile")),
		connect.WithHandlerOptions(opts...),
	)
	customerProxyProfileServiceConfigProxyProfileLocationHandler := connect.NewUnaryHandler(
		CustomerProxyProfileServiceConfigProxyProfileLocationProcedure,
		svc.ConfigProxyProfileLocation,
		connect.WithSchema(customerProxyProfileServiceMethods.ByName("ConfigProxyProfileLocation")),
		connect.WithHandlerOptions(opts...),
	)
	customerProxyProfileServiceConfigProxyProfileIPAllowHandler := connect.NewUnaryHandler(
		CustomerProxyProfileServiceConfigProxyProfileIPAllowProcedure,
		svc.ConfigProxyProfileIPAllow,
		connect.WithSchema(customerProxyProfileServiceMethods.ByName("ConfigProxyProfileIPAllow")),
		connect.WithHandlerOptions(opts...),
	)
	customerProxyProfileServiceConfigProxyProfileTelcoHandler := connect.NewUnaryHandler(
		CustomerProxyProfileServiceConfigProxyProfileTelcoProcedure,
		svc.ConfigProxyProfileTelco,
		connect.WithSchema(customerProxyProfileServiceMethods.ByName("ConfigProxyProfileTelco")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.proxyprofile.v1.CustomerProxyProfileService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CustomerProxyProfileServiceApplyProxyProfileProcedure:
			customerProxyProfileServiceApplyProxyProfileHandler.ServeHTTP(w, r)
		case CustomerProxyProfileServiceResetProxyProfileProcedure:
			customerProxyProfileServiceResetProxyProfileHandler.ServeHTTP(w, r)
		case CustomerProxyProfileServiceFetchProxyProfileProcedure:
			customerProxyProfileServiceFetchProxyProfileHandler.ServeHTTP(w, r)
		case CustomerProxyProfileServiceCreateProxyProfileProcedure:
			customerProxyProfileServiceCreateProxyProfileHandler.ServeHTTP(w, r)
		case CustomerProxyProfileServiceUpdateProxyProfileProcedure:
			customerProxyProfileServiceUpdateProxyProfileHandler.ServeHTTP(w, r)
		case CustomerProxyProfileServiceConfigProxyProfileLocationProcedure:
			customerProxyProfileServiceConfigProxyProfileLocationHandler.ServeHTTP(w, r)
		case CustomerProxyProfileServiceConfigProxyProfileIPAllowProcedure:
			customerProxyProfileServiceConfigProxyProfileIPAllowHandler.ServeHTTP(w, r)
		case CustomerProxyProfileServiceConfigProxyProfileTelcoProcedure:
			customerProxyProfileServiceConfigProxyProfileTelcoHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCustomerProxyProfileServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCustomerProxyProfileServiceHandler struct{}

func (UnimplementedCustomerProxyProfileServiceHandler) ApplyProxyProfile(context.Context, *connect.Request[v1.CustomerProxyProfileServiceApplyProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceApplyProxyProfileResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxyprofile.v1.CustomerProxyProfileService.ApplyProxyProfile is not implemented"))
}

func (UnimplementedCustomerProxyProfileServiceHandler) ResetProxyProfile(context.Context, *connect.Request[v1.CustomerProxyProfileServiceResetProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceResetProxyProfileResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxyprofile.v1.CustomerProxyProfileService.ResetProxyProfile is not implemented"))
}

func (UnimplementedCustomerProxyProfileServiceHandler) FetchProxyProfile(context.Context, *connect.Request[v1.CustomerProxyProfileServiceFetchProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceFetchProxyProfileResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxyprofile.v1.CustomerProxyProfileService.FetchProxyProfile is not implemented"))
}

func (UnimplementedCustomerProxyProfileServiceHandler) CreateProxyProfile(context.Context, *connect.Request[v1.CustomerProxyProfileServiceCreateProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceCreateProxyProfileResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxyprofile.v1.CustomerProxyProfileService.CreateProxyProfile is not implemented"))
}

func (UnimplementedCustomerProxyProfileServiceHandler) UpdateProxyProfile(context.Context, *connect.Request[v1.CustomerProxyProfileServiceUpdateProxyProfileRequest]) (*connect.Response[v1.CustomerProxyProfileServiceUpdateProxyProfileResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxyprofile.v1.CustomerProxyProfileService.UpdateProxyProfile is not implemented"))
}

func (UnimplementedCustomerProxyProfileServiceHandler) ConfigProxyProfileLocation(context.Context, *connect.Request[v1.CustomerProxyProfileServiceConfigProxyProfileLocationRequest]) (*connect.Response[v1.CustomerProxyProfileServiceConfigProxyProfileLocationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxyprofile.v1.CustomerProxyProfileService.ConfigProxyProfileLocation is not implemented"))
}

func (UnimplementedCustomerProxyProfileServiceHandler) ConfigProxyProfileIPAllow(context.Context, *connect.Request[v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest]) (*connect.Response[v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxyprofile.v1.CustomerProxyProfileService.ConfigProxyProfileIPAllow is not implemented"))
}

func (UnimplementedCustomerProxyProfileServiceHandler) ConfigProxyProfileTelco(context.Context, *connect.Request[v1.CustomerProxyProfileServiceConfigProxyProfileTelcoRequest]) (*connect.Response[v1.CustomerProxyProfileServiceConfigProxyProfileTelcoResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxyprofile.v1.CustomerProxyProfileService.ConfigProxyProfileTelco is not implemented"))
}
