// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/proxyprofile/v1/backoffice.proto

package proxyprofilev1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/proxyprofile/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackofficeProxyProfileServiceName is the fully-qualified name of the
	// BackofficeProxyProfileService service.
	BackofficeProxyProfileServiceName = "proxymanager.proxyprofile.v1.BackofficeProxyProfileService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackofficeProxyProfileServiceFetchProxyProfileProcedure is the fully-qualified name of the
	// BackofficeProxyProfileService's FetchProxyProfile RPC.
	BackofficeProxyProfileServiceFetchProxyProfileProcedure = "/proxymanager.proxyprofile.v1.BackofficeProxyProfileService/FetchProxyProfile"
	// BackofficeProxyProfileServiceResetProxyProfileProcedure is the fully-qualified name of the
	// BackofficeProxyProfileService's ResetProxyProfile RPC.
	BackofficeProxyProfileServiceResetProxyProfileProcedure = "/proxymanager.proxyprofile.v1.BackofficeProxyProfileService/ResetProxyProfile"
	// BackofficeProxyProfileServiceSetDefaultProxyProfileProcedure is the fully-qualified name of the
	// BackofficeProxyProfileService's SetDefaultProxyProfile RPC.
	BackofficeProxyProfileServiceSetDefaultProxyProfileProcedure = "/proxymanager.proxyprofile.v1.BackofficeProxyProfileService/SetDefaultProxyProfile"
	// BackofficeProxyProfileServiceCreateProxyProfileProcedure is the fully-qualified name of the
	// BackofficeProxyProfileService's CreateProxyProfile RPC.
	BackofficeProxyProfileServiceCreateProxyProfileProcedure = "/proxymanager.proxyprofile.v1.BackofficeProxyProfileService/CreateProxyProfile"
	// BackofficeProxyProfileServiceUpdateProxyProfileProcedure is the fully-qualified name of the
	// BackofficeProxyProfileService's UpdateProxyProfile RPC.
	BackofficeProxyProfileServiceUpdateProxyProfileProcedure = "/proxymanager.proxyprofile.v1.BackofficeProxyProfileService/UpdateProxyProfile"
	// BackofficeProxyProfileServiceConfigProxyProfileLocationProcedure is the fully-qualified name of
	// the BackofficeProxyProfileService's ConfigProxyProfileLocation RPC.
	BackofficeProxyProfileServiceConfigProxyProfileLocationProcedure = "/proxymanager.proxyprofile.v1.BackofficeProxyProfileService/ConfigProxyProfileLocation"
	// BackofficeProxyProfileServiceConfigProxyProfileIPAllowProcedure is the fully-qualified name of
	// the BackofficeProxyProfileService's ConfigProxyProfileIPAllow RPC.
	BackofficeProxyProfileServiceConfigProxyProfileIPAllowProcedure = "/proxymanager.proxyprofile.v1.BackofficeProxyProfileService/ConfigProxyProfileIPAllow"
	// BackofficeProxyProfileServiceConfigProxyProfileTelcoProcedure is the fully-qualified name of the
	// BackofficeProxyProfileService's ConfigProxyProfileTelco RPC.
	BackofficeProxyProfileServiceConfigProxyProfileTelcoProcedure = "/proxymanager.proxyprofile.v1.BackofficeProxyProfileService/ConfigProxyProfileTelco"
)

// BackofficeProxyProfileServiceClient is a client for the
// proxymanager.proxyprofile.v1.BackofficeProxyProfileService service.
type BackofficeProxyProfileServiceClient interface {
	FetchProxyProfile(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceFetchProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceFetchProxyProfileResponse], error)
	ResetProxyProfile(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceResetProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceResetProxyProfileResponse], error)
	SetDefaultProxyProfile(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceSetDefaultProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceSetDefaultProxyProfileResponse], error)
	CreateProxyProfile(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceCreateProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceCreateProxyProfileResponse], error)
	UpdateProxyProfile(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceUpdateProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceUpdateProxyProfileResponse], error)
	ConfigProxyProfileLocation(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceConfigProxyProfileLocationRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceConfigProxyProfileLocationResponse], error)
	ConfigProxyProfileIPAllow(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse], error)
	ConfigProxyProfileTelco(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse], error)
}

// NewBackofficeProxyProfileServiceClient constructs a client for the
// proxymanager.proxyprofile.v1.BackofficeProxyProfileService service. By default, it uses the
// Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and sends
// uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackofficeProxyProfileServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackofficeProxyProfileServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backofficeProxyProfileServiceMethods := v1.File_proxymanager_proxyprofile_v1_backoffice_proto.Services().ByName("BackofficeProxyProfileService").Methods()
	return &backofficeProxyProfileServiceClient{
		fetchProxyProfile: connect.NewClient[v1.BackofficeProxyProfileServiceFetchProxyProfileRequest, v1.BackofficeProxyProfileServiceFetchProxyProfileResponse](
			httpClient,
			baseURL+BackofficeProxyProfileServiceFetchProxyProfileProcedure,
			connect.WithSchema(backofficeProxyProfileServiceMethods.ByName("FetchProxyProfile")),
			connect.WithClientOptions(opts...),
		),
		resetProxyProfile: connect.NewClient[v1.BackofficeProxyProfileServiceResetProxyProfileRequest, v1.BackofficeProxyProfileServiceResetProxyProfileResponse](
			httpClient,
			baseURL+BackofficeProxyProfileServiceResetProxyProfileProcedure,
			connect.WithSchema(backofficeProxyProfileServiceMethods.ByName("ResetProxyProfile")),
			connect.WithClientOptions(opts...),
		),
		setDefaultProxyProfile: connect.NewClient[v1.BackofficeProxyProfileServiceSetDefaultProxyProfileRequest, v1.BackofficeProxyProfileServiceSetDefaultProxyProfileResponse](
			httpClient,
			baseURL+BackofficeProxyProfileServiceSetDefaultProxyProfileProcedure,
			connect.WithSchema(backofficeProxyProfileServiceMethods.ByName("SetDefaultProxyProfile")),
			connect.WithClientOptions(opts...),
		),
		createProxyProfile: connect.NewClient[v1.BackofficeProxyProfileServiceCreateProxyProfileRequest, v1.BackofficeProxyProfileServiceCreateProxyProfileResponse](
			httpClient,
			baseURL+BackofficeProxyProfileServiceCreateProxyProfileProcedure,
			connect.WithSchema(backofficeProxyProfileServiceMethods.ByName("CreateProxyProfile")),
			connect.WithClientOptions(opts...),
		),
		updateProxyProfile: connect.NewClient[v1.BackofficeProxyProfileServiceUpdateProxyProfileRequest, v1.BackofficeProxyProfileServiceUpdateProxyProfileResponse](
			httpClient,
			baseURL+BackofficeProxyProfileServiceUpdateProxyProfileProcedure,
			connect.WithSchema(backofficeProxyProfileServiceMethods.ByName("UpdateProxyProfile")),
			connect.WithClientOptions(opts...),
		),
		configProxyProfileLocation: connect.NewClient[v1.BackofficeProxyProfileServiceConfigProxyProfileLocationRequest, v1.BackofficeProxyProfileServiceConfigProxyProfileLocationResponse](
			httpClient,
			baseURL+BackofficeProxyProfileServiceConfigProxyProfileLocationProcedure,
			connect.WithSchema(backofficeProxyProfileServiceMethods.ByName("ConfigProxyProfileLocation")),
			connect.WithClientOptions(opts...),
		),
		configProxyProfileIPAllow: connect.NewClient[v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest, v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse](
			httpClient,
			baseURL+BackofficeProxyProfileServiceConfigProxyProfileIPAllowProcedure,
			connect.WithSchema(backofficeProxyProfileServiceMethods.ByName("ConfigProxyProfileIPAllow")),
			connect.WithClientOptions(opts...),
		),
		configProxyProfileTelco: connect.NewClient[v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest, v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse](
			httpClient,
			baseURL+BackofficeProxyProfileServiceConfigProxyProfileTelcoProcedure,
			connect.WithSchema(backofficeProxyProfileServiceMethods.ByName("ConfigProxyProfileTelco")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backofficeProxyProfileServiceClient implements BackofficeProxyProfileServiceClient.
type backofficeProxyProfileServiceClient struct {
	fetchProxyProfile          *connect.Client[v1.BackofficeProxyProfileServiceFetchProxyProfileRequest, v1.BackofficeProxyProfileServiceFetchProxyProfileResponse]
	resetProxyProfile          *connect.Client[v1.BackofficeProxyProfileServiceResetProxyProfileRequest, v1.BackofficeProxyProfileServiceResetProxyProfileResponse]
	setDefaultProxyProfile     *connect.Client[v1.BackofficeProxyProfileServiceSetDefaultProxyProfileRequest, v1.BackofficeProxyProfileServiceSetDefaultProxyProfileResponse]
	createProxyProfile         *connect.Client[v1.BackofficeProxyProfileServiceCreateProxyProfileRequest, v1.BackofficeProxyProfileServiceCreateProxyProfileResponse]
	updateProxyProfile         *connect.Client[v1.BackofficeProxyProfileServiceUpdateProxyProfileRequest, v1.BackofficeProxyProfileServiceUpdateProxyProfileResponse]
	configProxyProfileLocation *connect.Client[v1.BackofficeProxyProfileServiceConfigProxyProfileLocationRequest, v1.BackofficeProxyProfileServiceConfigProxyProfileLocationResponse]
	configProxyProfileIPAllow  *connect.Client[v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest, v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse]
	configProxyProfileTelco    *connect.Client[v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest, v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse]
}

// FetchProxyProfile calls
// proxymanager.proxyprofile.v1.BackofficeProxyProfileService.FetchProxyProfile.
func (c *backofficeProxyProfileServiceClient) FetchProxyProfile(ctx context.Context, req *connect.Request[v1.BackofficeProxyProfileServiceFetchProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceFetchProxyProfileResponse], error) {
	return c.fetchProxyProfile.CallUnary(ctx, req)
}

// ResetProxyProfile calls
// proxymanager.proxyprofile.v1.BackofficeProxyProfileService.ResetProxyProfile.
func (c *backofficeProxyProfileServiceClient) ResetProxyProfile(ctx context.Context, req *connect.Request[v1.BackofficeProxyProfileServiceResetProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceResetProxyProfileResponse], error) {
	return c.resetProxyProfile.CallUnary(ctx, req)
}

// SetDefaultProxyProfile calls
// proxymanager.proxyprofile.v1.BackofficeProxyProfileService.SetDefaultProxyProfile.
func (c *backofficeProxyProfileServiceClient) SetDefaultProxyProfile(ctx context.Context, req *connect.Request[v1.BackofficeProxyProfileServiceSetDefaultProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceSetDefaultProxyProfileResponse], error) {
	return c.setDefaultProxyProfile.CallUnary(ctx, req)
}

// CreateProxyProfile calls
// proxymanager.proxyprofile.v1.BackofficeProxyProfileService.CreateProxyProfile.
func (c *backofficeProxyProfileServiceClient) CreateProxyProfile(ctx context.Context, req *connect.Request[v1.BackofficeProxyProfileServiceCreateProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceCreateProxyProfileResponse], error) {
	return c.createProxyProfile.CallUnary(ctx, req)
}

// UpdateProxyProfile calls
// proxymanager.proxyprofile.v1.BackofficeProxyProfileService.UpdateProxyProfile.
func (c *backofficeProxyProfileServiceClient) UpdateProxyProfile(ctx context.Context, req *connect.Request[v1.BackofficeProxyProfileServiceUpdateProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceUpdateProxyProfileResponse], error) {
	return c.updateProxyProfile.CallUnary(ctx, req)
}

// ConfigProxyProfileLocation calls
// proxymanager.proxyprofile.v1.BackofficeProxyProfileService.ConfigProxyProfileLocation.
func (c *backofficeProxyProfileServiceClient) ConfigProxyProfileLocation(ctx context.Context, req *connect.Request[v1.BackofficeProxyProfileServiceConfigProxyProfileLocationRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceConfigProxyProfileLocationResponse], error) {
	return c.configProxyProfileLocation.CallUnary(ctx, req)
}

// ConfigProxyProfileIPAllow calls
// proxymanager.proxyprofile.v1.BackofficeProxyProfileService.ConfigProxyProfileIPAllow.
func (c *backofficeProxyProfileServiceClient) ConfigProxyProfileIPAllow(ctx context.Context, req *connect.Request[v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse], error) {
	return c.configProxyProfileIPAllow.CallUnary(ctx, req)
}

// ConfigProxyProfileTelco calls
// proxymanager.proxyprofile.v1.BackofficeProxyProfileService.ConfigProxyProfileTelco.
func (c *backofficeProxyProfileServiceClient) ConfigProxyProfileTelco(ctx context.Context, req *connect.Request[v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse], error) {
	return c.configProxyProfileTelco.CallUnary(ctx, req)
}

// BackofficeProxyProfileServiceHandler is an implementation of the
// proxymanager.proxyprofile.v1.BackofficeProxyProfileService service.
type BackofficeProxyProfileServiceHandler interface {
	FetchProxyProfile(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceFetchProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceFetchProxyProfileResponse], error)
	ResetProxyProfile(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceResetProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceResetProxyProfileResponse], error)
	SetDefaultProxyProfile(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceSetDefaultProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceSetDefaultProxyProfileResponse], error)
	CreateProxyProfile(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceCreateProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceCreateProxyProfileResponse], error)
	UpdateProxyProfile(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceUpdateProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceUpdateProxyProfileResponse], error)
	ConfigProxyProfileLocation(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceConfigProxyProfileLocationRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceConfigProxyProfileLocationResponse], error)
	ConfigProxyProfileIPAllow(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse], error)
	ConfigProxyProfileTelco(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse], error)
}

// NewBackofficeProxyProfileServiceHandler builds an HTTP handler from the service implementation.
// It returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackofficeProxyProfileServiceHandler(svc BackofficeProxyProfileServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backofficeProxyProfileServiceMethods := v1.File_proxymanager_proxyprofile_v1_backoffice_proto.Services().ByName("BackofficeProxyProfileService").Methods()
	backofficeProxyProfileServiceFetchProxyProfileHandler := connect.NewUnaryHandler(
		BackofficeProxyProfileServiceFetchProxyProfileProcedure,
		svc.FetchProxyProfile,
		connect.WithSchema(backofficeProxyProfileServiceMethods.ByName("FetchProxyProfile")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeProxyProfileServiceResetProxyProfileHandler := connect.NewUnaryHandler(
		BackofficeProxyProfileServiceResetProxyProfileProcedure,
		svc.ResetProxyProfile,
		connect.WithSchema(backofficeProxyProfileServiceMethods.ByName("ResetProxyProfile")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeProxyProfileServiceSetDefaultProxyProfileHandler := connect.NewUnaryHandler(
		BackofficeProxyProfileServiceSetDefaultProxyProfileProcedure,
		svc.SetDefaultProxyProfile,
		connect.WithSchema(backofficeProxyProfileServiceMethods.ByName("SetDefaultProxyProfile")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeProxyProfileServiceCreateProxyProfileHandler := connect.NewUnaryHandler(
		BackofficeProxyProfileServiceCreateProxyProfileProcedure,
		svc.CreateProxyProfile,
		connect.WithSchema(backofficeProxyProfileServiceMethods.ByName("CreateProxyProfile")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeProxyProfileServiceUpdateProxyProfileHandler := connect.NewUnaryHandler(
		BackofficeProxyProfileServiceUpdateProxyProfileProcedure,
		svc.UpdateProxyProfile,
		connect.WithSchema(backofficeProxyProfileServiceMethods.ByName("UpdateProxyProfile")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeProxyProfileServiceConfigProxyProfileLocationHandler := connect.NewUnaryHandler(
		BackofficeProxyProfileServiceConfigProxyProfileLocationProcedure,
		svc.ConfigProxyProfileLocation,
		connect.WithSchema(backofficeProxyProfileServiceMethods.ByName("ConfigProxyProfileLocation")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeProxyProfileServiceConfigProxyProfileIPAllowHandler := connect.NewUnaryHandler(
		BackofficeProxyProfileServiceConfigProxyProfileIPAllowProcedure,
		svc.ConfigProxyProfileIPAllow,
		connect.WithSchema(backofficeProxyProfileServiceMethods.ByName("ConfigProxyProfileIPAllow")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeProxyProfileServiceConfigProxyProfileTelcoHandler := connect.NewUnaryHandler(
		BackofficeProxyProfileServiceConfigProxyProfileTelcoProcedure,
		svc.ConfigProxyProfileTelco,
		connect.WithSchema(backofficeProxyProfileServiceMethods.ByName("ConfigProxyProfileTelco")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.proxyprofile.v1.BackofficeProxyProfileService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackofficeProxyProfileServiceFetchProxyProfileProcedure:
			backofficeProxyProfileServiceFetchProxyProfileHandler.ServeHTTP(w, r)
		case BackofficeProxyProfileServiceResetProxyProfileProcedure:
			backofficeProxyProfileServiceResetProxyProfileHandler.ServeHTTP(w, r)
		case BackofficeProxyProfileServiceSetDefaultProxyProfileProcedure:
			backofficeProxyProfileServiceSetDefaultProxyProfileHandler.ServeHTTP(w, r)
		case BackofficeProxyProfileServiceCreateProxyProfileProcedure:
			backofficeProxyProfileServiceCreateProxyProfileHandler.ServeHTTP(w, r)
		case BackofficeProxyProfileServiceUpdateProxyProfileProcedure:
			backofficeProxyProfileServiceUpdateProxyProfileHandler.ServeHTTP(w, r)
		case BackofficeProxyProfileServiceConfigProxyProfileLocationProcedure:
			backofficeProxyProfileServiceConfigProxyProfileLocationHandler.ServeHTTP(w, r)
		case BackofficeProxyProfileServiceConfigProxyProfileIPAllowProcedure:
			backofficeProxyProfileServiceConfigProxyProfileIPAllowHandler.ServeHTTP(w, r)
		case BackofficeProxyProfileServiceConfigProxyProfileTelcoProcedure:
			backofficeProxyProfileServiceConfigProxyProfileTelcoHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackofficeProxyProfileServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackofficeProxyProfileServiceHandler struct{}

func (UnimplementedBackofficeProxyProfileServiceHandler) FetchProxyProfile(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceFetchProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceFetchProxyProfileResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxyprofile.v1.BackofficeProxyProfileService.FetchProxyProfile is not implemented"))
}

func (UnimplementedBackofficeProxyProfileServiceHandler) ResetProxyProfile(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceResetProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceResetProxyProfileResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxyprofile.v1.BackofficeProxyProfileService.ResetProxyProfile is not implemented"))
}

func (UnimplementedBackofficeProxyProfileServiceHandler) SetDefaultProxyProfile(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceSetDefaultProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceSetDefaultProxyProfileResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxyprofile.v1.BackofficeProxyProfileService.SetDefaultProxyProfile is not implemented"))
}

func (UnimplementedBackofficeProxyProfileServiceHandler) CreateProxyProfile(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceCreateProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceCreateProxyProfileResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxyprofile.v1.BackofficeProxyProfileService.CreateProxyProfile is not implemented"))
}

func (UnimplementedBackofficeProxyProfileServiceHandler) UpdateProxyProfile(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceUpdateProxyProfileRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceUpdateProxyProfileResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxyprofile.v1.BackofficeProxyProfileService.UpdateProxyProfile is not implemented"))
}

func (UnimplementedBackofficeProxyProfileServiceHandler) ConfigProxyProfileLocation(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceConfigProxyProfileLocationRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceConfigProxyProfileLocationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxyprofile.v1.BackofficeProxyProfileService.ConfigProxyProfileLocation is not implemented"))
}

func (UnimplementedBackofficeProxyProfileServiceHandler) ConfigProxyProfileIPAllow(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxyprofile.v1.BackofficeProxyProfileService.ConfigProxyProfileIPAllow is not implemented"))
}

func (UnimplementedBackofficeProxyProfileServiceHandler) ConfigProxyProfileTelco(context.Context, *connect.Request[v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest]) (*connect.Response[v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.proxyprofile.v1.BackofficeProxyProfileService.ConfigProxyProfileTelco is not implemented"))
}
