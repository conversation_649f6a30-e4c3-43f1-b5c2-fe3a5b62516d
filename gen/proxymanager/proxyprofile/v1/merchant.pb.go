// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/proxyprofile/v1/merchant.proto

package proxyprofilev1

import (
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MerchantProxyProfileServiceFetchProxyProfileRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdUser         string                 `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	IdPlan         string                 `protobuf:"bytes,2,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdProxyProfile string                 `protobuf:"bytes,3,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	State          *v1.State              `protobuf:"bytes,4,opt,name=state,proto3" json:"state,omitempty"`
	Pagination     *v1.PaginationRequest  `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *MerchantProxyProfileServiceFetchProxyProfileRequest) Reset() {
	*x = MerchantProxyProfileServiceFetchProxyProfileRequest{}
	mi := &file_proxymanager_proxyprofile_v1_merchant_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantProxyProfileServiceFetchProxyProfileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantProxyProfileServiceFetchProxyProfileRequest) ProtoMessage() {}

func (x *MerchantProxyProfileServiceFetchProxyProfileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_merchant_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantProxyProfileServiceFetchProxyProfileRequest.ProtoReflect.Descriptor instead.
func (*MerchantProxyProfileServiceFetchProxyProfileRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_merchant_proto_rawDescGZIP(), []int{0}
}

func (x *MerchantProxyProfileServiceFetchProxyProfileRequest) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *MerchantProxyProfileServiceFetchProxyProfileRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *MerchantProxyProfileServiceFetchProxyProfileRequest) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

func (x *MerchantProxyProfileServiceFetchProxyProfileRequest) GetState() *v1.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *MerchantProxyProfileServiceFetchProxyProfileRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type MerchantProxyProfileServiceFetchProxyProfileResponse struct {
	state         protoimpl.MessageState                           `protogen:"open.v1"`
	Error         *v11.ErrorMessage                                `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v1.PaginationResponse                           `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	ProxyProfiles []*MerchantProxyProfileServiceProxyProfileEntity `protobuf:"bytes,3,rep,name=proxy_profiles,json=proxyProfiles,proto3" json:"proxy_profiles,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantProxyProfileServiceFetchProxyProfileResponse) Reset() {
	*x = MerchantProxyProfileServiceFetchProxyProfileResponse{}
	mi := &file_proxymanager_proxyprofile_v1_merchant_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantProxyProfileServiceFetchProxyProfileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantProxyProfileServiceFetchProxyProfileResponse) ProtoMessage() {}

func (x *MerchantProxyProfileServiceFetchProxyProfileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_merchant_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantProxyProfileServiceFetchProxyProfileResponse.ProtoReflect.Descriptor instead.
func (*MerchantProxyProfileServiceFetchProxyProfileResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_merchant_proto_rawDescGZIP(), []int{1}
}

func (x *MerchantProxyProfileServiceFetchProxyProfileResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *MerchantProxyProfileServiceFetchProxyProfileResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *MerchantProxyProfileServiceFetchProxyProfileResponse) GetProxyProfiles() []*MerchantProxyProfileServiceProxyProfileEntity {
	if x != nil {
		return x.ProxyProfiles
	}
	return nil
}

type MerchantProxyProfileServiceProxyProfileEntity struct {
	state                      protoimpl.MessageState                                   `protogen:"open.v1"`
	IdProxyProfile             string                                                   `protobuf:"bytes,1,opt,name=id_proxy_profile,json=idProxyProfile,proto3" json:"id_proxy_profile,omitempty"`
	Name                       string                                                   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Dns                        *MerchantProxyProfileServiceProxyProfileEntityDNS        `protobuf:"bytes,3,opt,name=dns,proto3" json:"dns,omitempty"`
	AutoSwitchIpAfterInSec     int64                                                    `protobuf:"varint,4,opt,name=auto_switch_ip_after_in_sec,json=autoSwitchIpAfterInSec,proto3" json:"auto_switch_ip_after_in_sec,omitempty"`
	AutoSwitchIpWhenDisconnect bool                                                     `protobuf:"varint,5,opt,name=auto_switch_ip_when_disconnect,json=autoSwitchIpWhenDisconnect,proto3" json:"auto_switch_ip_when_disconnect,omitempty"`
	IsStaticUsernamePassword   bool                                                     `protobuf:"varint,6,opt,name=is_static_username_password,json=isStaticUsernamePassword,proto3" json:"is_static_username_password,omitempty"`
	IpAllows                   []string                                                 `protobuf:"bytes,7,rep,name=ip_allows,json=ipAllows,proto3" json:"ip_allows,omitempty"`
	Telcos                     []*MerchantProxyProfileServiceProxyProfileEntityTelco    `protobuf:"bytes,8,rep,name=telcos,proto3" json:"telcos,omitempty"`
	Locations                  []*MerchantProxyProfileServiceProxyProfileEntityLocation `protobuf:"bytes,9,rep,name=locations,proto3" json:"locations,omitempty"`
	IsActive                   bool                                                     `protobuf:"varint,10,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	IsDefaultProfile           bool                                                     `protobuf:"varint,11,opt,name=is_default_profile,json=isDefaultProfile,proto3" json:"is_default_profile,omitempty"`
	BandwidthPerProxyMbit      int64                                                    `protobuf:"varint,12,opt,name=bandwidth_per_proxy_mbit,json=bandwidthPerProxyMbit,proto3" json:"bandwidth_per_proxy_mbit,omitempty"`
	UsernameProxy              string                                                   `protobuf:"bytes,13,opt,name=username_proxy,json=usernameProxy,proto3" json:"username_proxy,omitempty"`
	PasswordProxy              string                                                   `protobuf:"bytes,14,opt,name=password_proxy,json=passwordProxy,proto3" json:"password_proxy,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *MerchantProxyProfileServiceProxyProfileEntity) Reset() {
	*x = MerchantProxyProfileServiceProxyProfileEntity{}
	mi := &file_proxymanager_proxyprofile_v1_merchant_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantProxyProfileServiceProxyProfileEntity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantProxyProfileServiceProxyProfileEntity) ProtoMessage() {}

func (x *MerchantProxyProfileServiceProxyProfileEntity) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_merchant_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantProxyProfileServiceProxyProfileEntity.ProtoReflect.Descriptor instead.
func (*MerchantProxyProfileServiceProxyProfileEntity) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_merchant_proto_rawDescGZIP(), []int{2}
}

func (x *MerchantProxyProfileServiceProxyProfileEntity) GetIdProxyProfile() string {
	if x != nil {
		return x.IdProxyProfile
	}
	return ""
}

func (x *MerchantProxyProfileServiceProxyProfileEntity) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MerchantProxyProfileServiceProxyProfileEntity) GetDns() *MerchantProxyProfileServiceProxyProfileEntityDNS {
	if x != nil {
		return x.Dns
	}
	return nil
}

func (x *MerchantProxyProfileServiceProxyProfileEntity) GetAutoSwitchIpAfterInSec() int64 {
	if x != nil {
		return x.AutoSwitchIpAfterInSec
	}
	return 0
}

func (x *MerchantProxyProfileServiceProxyProfileEntity) GetAutoSwitchIpWhenDisconnect() bool {
	if x != nil {
		return x.AutoSwitchIpWhenDisconnect
	}
	return false
}

func (x *MerchantProxyProfileServiceProxyProfileEntity) GetIsStaticUsernamePassword() bool {
	if x != nil {
		return x.IsStaticUsernamePassword
	}
	return false
}

func (x *MerchantProxyProfileServiceProxyProfileEntity) GetIpAllows() []string {
	if x != nil {
		return x.IpAllows
	}
	return nil
}

func (x *MerchantProxyProfileServiceProxyProfileEntity) GetTelcos() []*MerchantProxyProfileServiceProxyProfileEntityTelco {
	if x != nil {
		return x.Telcos
	}
	return nil
}

func (x *MerchantProxyProfileServiceProxyProfileEntity) GetLocations() []*MerchantProxyProfileServiceProxyProfileEntityLocation {
	if x != nil {
		return x.Locations
	}
	return nil
}

func (x *MerchantProxyProfileServiceProxyProfileEntity) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *MerchantProxyProfileServiceProxyProfileEntity) GetIsDefaultProfile() bool {
	if x != nil {
		return x.IsDefaultProfile
	}
	return false
}

func (x *MerchantProxyProfileServiceProxyProfileEntity) GetBandwidthPerProxyMbit() int64 {
	if x != nil {
		return x.BandwidthPerProxyMbit
	}
	return 0
}

func (x *MerchantProxyProfileServiceProxyProfileEntity) GetUsernameProxy() string {
	if x != nil {
		return x.UsernameProxy
	}
	return ""
}

func (x *MerchantProxyProfileServiceProxyProfileEntity) GetPasswordProxy() string {
	if x != nil {
		return x.PasswordProxy
	}
	return ""
}

type MerchantProxyProfileUser struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdUser        string                 `protobuf:"bytes,1,opt,name=id_user,json=idUser,proto3" json:"id_user,omitempty"`
	Email         string                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantProxyProfileUser) Reset() {
	*x = MerchantProxyProfileUser{}
	mi := &file_proxymanager_proxyprofile_v1_merchant_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantProxyProfileUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantProxyProfileUser) ProtoMessage() {}

func (x *MerchantProxyProfileUser) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_merchant_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantProxyProfileUser.ProtoReflect.Descriptor instead.
func (*MerchantProxyProfileUser) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_merchant_proto_rawDescGZIP(), []int{3}
}

func (x *MerchantProxyProfileUser) GetIdUser() string {
	if x != nil {
		return x.IdUser
	}
	return ""
}

func (x *MerchantProxyProfileUser) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type MerchantProxyProfilePlan struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	IdPlan           string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	Name             string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IpType           v12.IPType             `protobuf:"varint,4,opt,name=ip_type,json=ipType,proto3,enum=algoenum.v1.IPType" json:"ip_type,omitempty"`
	ProxyType        v12.ProxyType          `protobuf:"varint,5,opt,name=proxy_type,json=proxyType,proto3,enum=algoenum.v1.ProxyType" json:"proxy_type,omitempty"`
	ChangeType       v12.ChangeType         `protobuf:"varint,6,opt,name=change_type,json=changeType,proto3,enum=algoenum.v1.ChangeType" json:"change_type,omitempty"`
	DataTransferType v12.DataTransferType   `protobuf:"varint,7,opt,name=data_transfer_type,json=dataTransferType,proto3,enum=algoenum.v1.DataTransferType" json:"data_transfer_type,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *MerchantProxyProfilePlan) Reset() {
	*x = MerchantProxyProfilePlan{}
	mi := &file_proxymanager_proxyprofile_v1_merchant_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantProxyProfilePlan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantProxyProfilePlan) ProtoMessage() {}

func (x *MerchantProxyProfilePlan) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_merchant_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantProxyProfilePlan.ProtoReflect.Descriptor instead.
func (*MerchantProxyProfilePlan) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_merchant_proto_rawDescGZIP(), []int{4}
}

func (x *MerchantProxyProfilePlan) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *MerchantProxyProfilePlan) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MerchantProxyProfilePlan) GetIpType() v12.IPType {
	if x != nil {
		return x.IpType
	}
	return v12.IPType(0)
}

func (x *MerchantProxyProfilePlan) GetProxyType() v12.ProxyType {
	if x != nil {
		return x.ProxyType
	}
	return v12.ProxyType(0)
}

func (x *MerchantProxyProfilePlan) GetChangeType() v12.ChangeType {
	if x != nil {
		return x.ChangeType
	}
	return v12.ChangeType(0)
}

func (x *MerchantProxyProfilePlan) GetDataTransferType() v12.DataTransferType {
	if x != nil {
		return x.DataTransferType
	}
	return v12.DataTransferType(0)
}

type MerchantProxyProfileServiceProxyProfileEntityDNS struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdDns         string                 `protobuf:"bytes,1,opt,name=id_dns,json=idDns,proto3" json:"id_dns,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Dns_1         string                 `protobuf:"bytes,3,opt,name=dns_1,json=dns1,proto3" json:"dns_1,omitempty"`
	Dns_2         string                 `protobuf:"bytes,4,opt,name=dns_2,json=dns2,proto3" json:"dns_2,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantProxyProfileServiceProxyProfileEntityDNS) Reset() {
	*x = MerchantProxyProfileServiceProxyProfileEntityDNS{}
	mi := &file_proxymanager_proxyprofile_v1_merchant_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantProxyProfileServiceProxyProfileEntityDNS) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantProxyProfileServiceProxyProfileEntityDNS) ProtoMessage() {}

func (x *MerchantProxyProfileServiceProxyProfileEntityDNS) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_merchant_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantProxyProfileServiceProxyProfileEntityDNS.ProtoReflect.Descriptor instead.
func (*MerchantProxyProfileServiceProxyProfileEntityDNS) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_merchant_proto_rawDescGZIP(), []int{5}
}

func (x *MerchantProxyProfileServiceProxyProfileEntityDNS) GetIdDns() string {
	if x != nil {
		return x.IdDns
	}
	return ""
}

func (x *MerchantProxyProfileServiceProxyProfileEntityDNS) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MerchantProxyProfileServiceProxyProfileEntityDNS) GetDns_1() string {
	if x != nil {
		return x.Dns_1
	}
	return ""
}

func (x *MerchantProxyProfileServiceProxyProfileEntityDNS) GetDns_2() string {
	if x != nil {
		return x.Dns_2
	}
	return ""
}

type MerchantProxyProfileServiceProxyProfileEntityTelco struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdTelco       string                 `protobuf:"bytes,1,opt,name=id_telco,json=idTelco,proto3" json:"id_telco,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantProxyProfileServiceProxyProfileEntityTelco) Reset() {
	*x = MerchantProxyProfileServiceProxyProfileEntityTelco{}
	mi := &file_proxymanager_proxyprofile_v1_merchant_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantProxyProfileServiceProxyProfileEntityTelco) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantProxyProfileServiceProxyProfileEntityTelco) ProtoMessage() {}

func (x *MerchantProxyProfileServiceProxyProfileEntityTelco) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_merchant_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantProxyProfileServiceProxyProfileEntityTelco.ProtoReflect.Descriptor instead.
func (*MerchantProxyProfileServiceProxyProfileEntityTelco) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_merchant_proto_rawDescGZIP(), []int{6}
}

func (x *MerchantProxyProfileServiceProxyProfileEntityTelco) GetIdTelco() string {
	if x != nil {
		return x.IdTelco
	}
	return ""
}

func (x *MerchantProxyProfileServiceProxyProfileEntityTelco) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type MerchantProxyProfileServiceProxyProfileEntityLocation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdLocation    string                 `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	Level         v12.LocationLevel      `protobuf:"varint,2,opt,name=level,proto3,enum=algoenum.v1.LocationLevel" json:"level,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MerchantProxyProfileServiceProxyProfileEntityLocation) Reset() {
	*x = MerchantProxyProfileServiceProxyProfileEntityLocation{}
	mi := &file_proxymanager_proxyprofile_v1_merchant_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MerchantProxyProfileServiceProxyProfileEntityLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantProxyProfileServiceProxyProfileEntityLocation) ProtoMessage() {}

func (x *MerchantProxyProfileServiceProxyProfileEntityLocation) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_proxyprofile_v1_merchant_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantProxyProfileServiceProxyProfileEntityLocation.ProtoReflect.Descriptor instead.
func (*MerchantProxyProfileServiceProxyProfileEntityLocation) Descriptor() ([]byte, []int) {
	return file_proxymanager_proxyprofile_v1_merchant_proto_rawDescGZIP(), []int{7}
}

func (x *MerchantProxyProfileServiceProxyProfileEntityLocation) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *MerchantProxyProfileServiceProxyProfileEntityLocation) GetLevel() v12.LocationLevel {
	if x != nil {
		return x.Level
	}
	return v12.LocationLevel(0)
}

func (x *MerchantProxyProfileServiceProxyProfileEntityLocation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_proxymanager_proxyprofile_v1_merchant_proto protoreflect.FileDescriptor

const file_proxymanager_proxyprofile_v1_merchant_proto_rawDesc = "" +
	"\n" +
	"+proxymanager/proxyprofile/v1/merchant.proto\x12\x1cproxymanager.proxyprofile.v1\x1a\x19algoenum/v1/ip_type.proto\x1a\x1calgoenum/v1/proxy_type.proto\x1a\x1dalgoenum/v1/change_type.proto\x1a$algoenum/v1/data_transfer_type.proto\x1a algoenum/v1/location_level.proto\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\"\xf5\x01\n" +
	"3MerchantProxyProfileServiceFetchProxyProfileRequest\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x12\x17\n" +
	"\aid_plan\x18\x02 \x01(\tR\x06idPlan\x12(\n" +
	"\x10id_proxy_profile\x18\x03 \x01(\tR\x0eidProxyProfile\x12%\n" +
	"\x05state\x18\x04 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x97\x02\n" +
	"4MerchantProxyProfileServiceFetchProxyProfileResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12r\n" +
	"\x0eproxy_profiles\x18\x03 \x03(\v2K.proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntityR\rproxyProfiles\"\xdb\x06\n" +
	"-MerchantProxyProfileServiceProxyProfileEntity\x12(\n" +
	"\x10id_proxy_profile\x18\x01 \x01(\tR\x0eidProxyProfile\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12`\n" +
	"\x03dns\x18\x03 \x01(\v2N.proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntityDNSR\x03dns\x12;\n" +
	"\x1bauto_switch_ip_after_in_sec\x18\x04 \x01(\x03R\x16autoSwitchIpAfterInSec\x12B\n" +
	"\x1eauto_switch_ip_when_disconnect\x18\x05 \x01(\bR\x1aautoSwitchIpWhenDisconnect\x12=\n" +
	"\x1bis_static_username_password\x18\x06 \x01(\bR\x18isStaticUsernamePassword\x12\x1b\n" +
	"\tip_allows\x18\a \x03(\tR\bipAllows\x12h\n" +
	"\x06telcos\x18\b \x03(\v2P.proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntityTelcoR\x06telcos\x12q\n" +
	"\tlocations\x18\t \x03(\v2S.proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntityLocationR\tlocations\x12\x1b\n" +
	"\tis_active\x18\n" +
	" \x01(\bR\bisActive\x12,\n" +
	"\x12is_default_profile\x18\v \x01(\bR\x10isDefaultProfile\x127\n" +
	"\x18bandwidth_per_proxy_mbit\x18\f \x01(\x03R\x15bandwidthPerProxyMbit\x12%\n" +
	"\x0eusername_proxy\x18\r \x01(\tR\rusernameProxy\x12%\n" +
	"\x0epassword_proxy\x18\x0e \x01(\tR\rpasswordProxy\"I\n" +
	"\x18MerchantProxyProfileUser\x12\x17\n" +
	"\aid_user\x18\x01 \x01(\tR\x06idUser\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\"\xb3\x02\n" +
	"\x18MerchantProxyProfilePlan\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12,\n" +
	"\aip_type\x18\x04 \x01(\x0e2\x13.algoenum.v1.IPTypeR\x06ipType\x125\n" +
	"\n" +
	"proxy_type\x18\x05 \x01(\x0e2\x16.algoenum.v1.ProxyTypeR\tproxyType\x128\n" +
	"\vchange_type\x18\x06 \x01(\x0e2\x17.algoenum.v1.ChangeTypeR\n" +
	"changeType\x12K\n" +
	"\x12data_transfer_type\x18\a \x01(\x0e2\x1d.algoenum.v1.DataTransferTypeR\x10dataTransferType\"\x87\x01\n" +
	"0MerchantProxyProfileServiceProxyProfileEntityDNS\x12\x15\n" +
	"\x06id_dns\x18\x01 \x01(\tR\x05idDns\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x13\n" +
	"\x05dns_1\x18\x03 \x01(\tR\x04dns1\x12\x13\n" +
	"\x05dns_2\x18\x04 \x01(\tR\x04dns2\"c\n" +
	"2MerchantProxyProfileServiceProxyProfileEntityTelco\x12\x19\n" +
	"\bid_telco\x18\x01 \x01(\tR\aidTelco\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"\x9e\x01\n" +
	"5MerchantProxyProfileServiceProxyProfileEntityLocation\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x120\n" +
	"\x05level\x18\x02 \x01(\x0e2\x1a.algoenum.v1.LocationLevelR\x05level\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name2\xda\x01\n" +
	"\x1bMerchantProxyProfileService\x12\xba\x01\n" +
	"\x11FetchProxyProfile\x12Q.proxymanager.proxyprofile.v1.MerchantProxyProfileServiceFetchProxyProfileRequest\x1aR.proxymanager.proxyprofile.v1.MerchantProxyProfileServiceFetchProxyProfileResponseB\\ZZgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/proxyprofile/v1;proxyprofilev1b\x06proto3"

var (
	file_proxymanager_proxyprofile_v1_merchant_proto_rawDescOnce sync.Once
	file_proxymanager_proxyprofile_v1_merchant_proto_rawDescData []byte
)

func file_proxymanager_proxyprofile_v1_merchant_proto_rawDescGZIP() []byte {
	file_proxymanager_proxyprofile_v1_merchant_proto_rawDescOnce.Do(func() {
		file_proxymanager_proxyprofile_v1_merchant_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_proxyprofile_v1_merchant_proto_rawDesc), len(file_proxymanager_proxyprofile_v1_merchant_proto_rawDesc)))
	})
	return file_proxymanager_proxyprofile_v1_merchant_proto_rawDescData
}

var file_proxymanager_proxyprofile_v1_merchant_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_proxymanager_proxyprofile_v1_merchant_proto_goTypes = []any{
	(*MerchantProxyProfileServiceFetchProxyProfileRequest)(nil),   // 0: proxymanager.proxyprofile.v1.MerchantProxyProfileServiceFetchProxyProfileRequest
	(*MerchantProxyProfileServiceFetchProxyProfileResponse)(nil),  // 1: proxymanager.proxyprofile.v1.MerchantProxyProfileServiceFetchProxyProfileResponse
	(*MerchantProxyProfileServiceProxyProfileEntity)(nil),         // 2: proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntity
	(*MerchantProxyProfileUser)(nil),                              // 3: proxymanager.proxyprofile.v1.MerchantProxyProfileUser
	(*MerchantProxyProfilePlan)(nil),                              // 4: proxymanager.proxyprofile.v1.MerchantProxyProfilePlan
	(*MerchantProxyProfileServiceProxyProfileEntityDNS)(nil),      // 5: proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntityDNS
	(*MerchantProxyProfileServiceProxyProfileEntityTelco)(nil),    // 6: proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntityTelco
	(*MerchantProxyProfileServiceProxyProfileEntityLocation)(nil), // 7: proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntityLocation
	(*v1.State)(nil),              // 8: utils.v1.State
	(*v1.PaginationRequest)(nil),  // 9: utils.v1.PaginationRequest
	(*v11.ErrorMessage)(nil),      // 10: errmsg.v1.ErrorMessage
	(*v1.PaginationResponse)(nil), // 11: utils.v1.PaginationResponse
	(v12.IPType)(0),               // 12: algoenum.v1.IPType
	(v12.ProxyType)(0),            // 13: algoenum.v1.ProxyType
	(v12.ChangeType)(0),           // 14: algoenum.v1.ChangeType
	(v12.DataTransferType)(0),     // 15: algoenum.v1.DataTransferType
	(v12.LocationLevel)(0),        // 16: algoenum.v1.LocationLevel
}
var file_proxymanager_proxyprofile_v1_merchant_proto_depIdxs = []int32{
	8,  // 0: proxymanager.proxyprofile.v1.MerchantProxyProfileServiceFetchProxyProfileRequest.state:type_name -> utils.v1.State
	9,  // 1: proxymanager.proxyprofile.v1.MerchantProxyProfileServiceFetchProxyProfileRequest.pagination:type_name -> utils.v1.PaginationRequest
	10, // 2: proxymanager.proxyprofile.v1.MerchantProxyProfileServiceFetchProxyProfileResponse.error:type_name -> errmsg.v1.ErrorMessage
	11, // 3: proxymanager.proxyprofile.v1.MerchantProxyProfileServiceFetchProxyProfileResponse.pagination:type_name -> utils.v1.PaginationResponse
	2,  // 4: proxymanager.proxyprofile.v1.MerchantProxyProfileServiceFetchProxyProfileResponse.proxy_profiles:type_name -> proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntity
	5,  // 5: proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntity.dns:type_name -> proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntityDNS
	6,  // 6: proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntity.telcos:type_name -> proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntityTelco
	7,  // 7: proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntity.locations:type_name -> proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntityLocation
	12, // 8: proxymanager.proxyprofile.v1.MerchantProxyProfilePlan.ip_type:type_name -> algoenum.v1.IPType
	13, // 9: proxymanager.proxyprofile.v1.MerchantProxyProfilePlan.proxy_type:type_name -> algoenum.v1.ProxyType
	14, // 10: proxymanager.proxyprofile.v1.MerchantProxyProfilePlan.change_type:type_name -> algoenum.v1.ChangeType
	15, // 11: proxymanager.proxyprofile.v1.MerchantProxyProfilePlan.data_transfer_type:type_name -> algoenum.v1.DataTransferType
	16, // 12: proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntityLocation.level:type_name -> algoenum.v1.LocationLevel
	0,  // 13: proxymanager.proxyprofile.v1.MerchantProxyProfileService.FetchProxyProfile:input_type -> proxymanager.proxyprofile.v1.MerchantProxyProfileServiceFetchProxyProfileRequest
	1,  // 14: proxymanager.proxyprofile.v1.MerchantProxyProfileService.FetchProxyProfile:output_type -> proxymanager.proxyprofile.v1.MerchantProxyProfileServiceFetchProxyProfileResponse
	14, // [14:15] is the sub-list for method output_type
	13, // [13:14] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_proxymanager_proxyprofile_v1_merchant_proto_init() }
func file_proxymanager_proxyprofile_v1_merchant_proto_init() {
	if File_proxymanager_proxyprofile_v1_merchant_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_proxyprofile_v1_merchant_proto_rawDesc), len(file_proxymanager_proxyprofile_v1_merchant_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_proxyprofile_v1_merchant_proto_goTypes,
		DependencyIndexes: file_proxymanager_proxyprofile_v1_merchant_proto_depIdxs,
		MessageInfos:      file_proxymanager_proxyprofile_v1_merchant_proto_msgTypes,
	}.Build()
	File_proxymanager_proxyprofile_v1_merchant_proto = out.File
	file_proxymanager_proxyprofile_v1_merchant_proto_goTypes = nil
	file_proxymanager_proxyprofile_v1_merchant_proto_depIdxs = nil
}
