// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/backconnect/v1/worker.proto

package backconnectv1

import (
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/hop/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackConnectWorkerServiceConfigHopRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Hop           *v1.Hop                `protobuf:"bytes,1,opt,name=hop,proto3" json:"hop,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectWorkerServiceConfigHopRequest) Reset() {
	*x = BackConnectWorkerServiceConfigHopRequest{}
	mi := &file_proxymanager_backconnect_v1_worker_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectWorkerServiceConfigHopRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectWorkerServiceConfigHopRequest) ProtoMessage() {}

func (x *BackConnectWorkerServiceConfigHopRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_worker_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectWorkerServiceConfigHopRequest.ProtoReflect.Descriptor instead.
func (*BackConnectWorkerServiceConfigHopRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_worker_proto_rawDescGZIP(), []int{0}
}

func (x *BackConnectWorkerServiceConfigHopRequest) GetHop() *v1.Hop {
	if x != nil {
		return x.Hop
	}
	return nil
}

type BackConnectWorkerServiceConfigHopResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectWorkerServiceConfigHopResponse) Reset() {
	*x = BackConnectWorkerServiceConfigHopResponse{}
	mi := &file_proxymanager_backconnect_v1_worker_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectWorkerServiceConfigHopResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectWorkerServiceConfigHopResponse) ProtoMessage() {}

func (x *BackConnectWorkerServiceConfigHopResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_worker_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectWorkerServiceConfigHopResponse.ProtoReflect.Descriptor instead.
func (*BackConnectWorkerServiceConfigHopResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_worker_proto_rawDescGZIP(), []int{1}
}

func (x *BackConnectWorkerServiceConfigHopResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackConnectWorkerServiceHealthCheckRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectWorkerServiceHealthCheckRequest) Reset() {
	*x = BackConnectWorkerServiceHealthCheckRequest{}
	mi := &file_proxymanager_backconnect_v1_worker_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectWorkerServiceHealthCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectWorkerServiceHealthCheckRequest) ProtoMessage() {}

func (x *BackConnectWorkerServiceHealthCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_worker_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectWorkerServiceHealthCheckRequest.ProtoReflect.Descriptor instead.
func (*BackConnectWorkerServiceHealthCheckRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_worker_proto_rawDescGZIP(), []int{2}
}

type BackConnectWorkerServiceHealthCheckResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectWorkerServiceHealthCheckResponse) Reset() {
	*x = BackConnectWorkerServiceHealthCheckResponse{}
	mi := &file_proxymanager_backconnect_v1_worker_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectWorkerServiceHealthCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectWorkerServiceHealthCheckResponse) ProtoMessage() {}

func (x *BackConnectWorkerServiceHealthCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_worker_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectWorkerServiceHealthCheckResponse.ProtoReflect.Descriptor instead.
func (*BackConnectWorkerServiceHealthCheckResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_worker_proto_rawDescGZIP(), []int{3}
}

func (x *BackConnectWorkerServiceHealthCheckResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_proxymanager_backconnect_v1_worker_proto protoreflect.FileDescriptor

const file_proxymanager_backconnect_v1_worker_proto_rawDesc = "" +
	"\n" +
	"(proxymanager/backconnect/v1/worker.proto\x12\x1bproxymanager.backconnect.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x10hop/v1/hop.proto\"I\n" +
	"(BackConnectWorkerServiceConfigHopRequest\x12\x1d\n" +
	"\x03hop\x18\x01 \x01(\v2\v.hop.v1.HopR\x03hop\"Z\n" +
	")BackConnectWorkerServiceConfigHopResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\",\n" +
	"*BackConnectWorkerServiceHealthCheckRequest\"\\\n" +
	"+BackConnectWorkerServiceHealthCheckResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error2\xda\x02\n" +
	"\x18BackConnectWorkerService\x12\xa0\x01\n" +
	"\vHealthCheck\x12G.proxymanager.backconnect.v1.BackConnectWorkerServiceHealthCheckRequest\x1aH.proxymanager.backconnect.v1.BackConnectWorkerServiceHealthCheckResponse\x12\x9a\x01\n" +
	"\tConfigHop\x12E.proxymanager.backconnect.v1.BackConnectWorkerServiceConfigHopRequest\x1aF.proxymanager.backconnect.v1.BackConnectWorkerServiceConfigHopResponseBZZXgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/backconnect/v1;backconnectv1b\x06proto3"

var (
	file_proxymanager_backconnect_v1_worker_proto_rawDescOnce sync.Once
	file_proxymanager_backconnect_v1_worker_proto_rawDescData []byte
)

func file_proxymanager_backconnect_v1_worker_proto_rawDescGZIP() []byte {
	file_proxymanager_backconnect_v1_worker_proto_rawDescOnce.Do(func() {
		file_proxymanager_backconnect_v1_worker_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_backconnect_v1_worker_proto_rawDesc), len(file_proxymanager_backconnect_v1_worker_proto_rawDesc)))
	})
	return file_proxymanager_backconnect_v1_worker_proto_rawDescData
}

var file_proxymanager_backconnect_v1_worker_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_proxymanager_backconnect_v1_worker_proto_goTypes = []any{
	(*BackConnectWorkerServiceConfigHopRequest)(nil),    // 0: proxymanager.backconnect.v1.BackConnectWorkerServiceConfigHopRequest
	(*BackConnectWorkerServiceConfigHopResponse)(nil),   // 1: proxymanager.backconnect.v1.BackConnectWorkerServiceConfigHopResponse
	(*BackConnectWorkerServiceHealthCheckRequest)(nil),  // 2: proxymanager.backconnect.v1.BackConnectWorkerServiceHealthCheckRequest
	(*BackConnectWorkerServiceHealthCheckResponse)(nil), // 3: proxymanager.backconnect.v1.BackConnectWorkerServiceHealthCheckResponse
	(*v1.Hop)(nil),           // 4: hop.v1.Hop
	(*v11.ErrorMessage)(nil), // 5: errmsg.v1.ErrorMessage
}
var file_proxymanager_backconnect_v1_worker_proto_depIdxs = []int32{
	4, // 0: proxymanager.backconnect.v1.BackConnectWorkerServiceConfigHopRequest.hop:type_name -> hop.v1.Hop
	5, // 1: proxymanager.backconnect.v1.BackConnectWorkerServiceConfigHopResponse.error:type_name -> errmsg.v1.ErrorMessage
	5, // 2: proxymanager.backconnect.v1.BackConnectWorkerServiceHealthCheckResponse.error:type_name -> errmsg.v1.ErrorMessage
	2, // 3: proxymanager.backconnect.v1.BackConnectWorkerService.HealthCheck:input_type -> proxymanager.backconnect.v1.BackConnectWorkerServiceHealthCheckRequest
	0, // 4: proxymanager.backconnect.v1.BackConnectWorkerService.ConfigHop:input_type -> proxymanager.backconnect.v1.BackConnectWorkerServiceConfigHopRequest
	3, // 5: proxymanager.backconnect.v1.BackConnectWorkerService.HealthCheck:output_type -> proxymanager.backconnect.v1.BackConnectWorkerServiceHealthCheckResponse
	1, // 6: proxymanager.backconnect.v1.BackConnectWorkerService.ConfigHop:output_type -> proxymanager.backconnect.v1.BackConnectWorkerServiceConfigHopResponse
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_proxymanager_backconnect_v1_worker_proto_init() }
func file_proxymanager_backconnect_v1_worker_proto_init() {
	if File_proxymanager_backconnect_v1_worker_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_backconnect_v1_worker_proto_rawDesc), len(file_proxymanager_backconnect_v1_worker_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_backconnect_v1_worker_proto_goTypes,
		DependencyIndexes: file_proxymanager_backconnect_v1_worker_proto_depIdxs,
		MessageInfos:      file_proxymanager_backconnect_v1_worker_proto_msgTypes,
	}.Build()
	File_proxymanager_backconnect_v1_worker_proto = out.File
	file_proxymanager_backconnect_v1_worker_proto_goTypes = nil
	file_proxymanager_backconnect_v1_worker_proto_depIdxs = nil
}
