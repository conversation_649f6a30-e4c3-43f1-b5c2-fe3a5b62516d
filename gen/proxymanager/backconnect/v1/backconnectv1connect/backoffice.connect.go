// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/backconnect/v1/backoffice.proto

package backconnectv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/backconnect/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackofficeBackConnectServiceName is the fully-qualified name of the BackofficeBackConnectService
	// service.
	BackofficeBackConnectServiceName = "proxymanager.backconnect.v1.BackofficeBackConnectService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackofficeBackConnectServiceFetchBackConnectManagerProcedure is the fully-qualified name of the
	// BackofficeBackConnectService's FetchBackConnectManager RPC.
	BackofficeBackConnectServiceFetchBackConnectManagerProcedure = "/proxymanager.backconnect.v1.BackofficeBackConnectService/FetchBackConnectManager"
	// BackofficeBackConnectServiceCreateBackConnectManagerProcedure is the fully-qualified name of the
	// BackofficeBackConnectService's CreateBackConnectManager RPC.
	BackofficeBackConnectServiceCreateBackConnectManagerProcedure = "/proxymanager.backconnect.v1.BackofficeBackConnectService/CreateBackConnectManager"
	// BackofficeBackConnectServiceUpdateBackConnectManagerProcedure is the fully-qualified name of the
	// BackofficeBackConnectService's UpdateBackConnectManager RPC.
	BackofficeBackConnectServiceUpdateBackConnectManagerProcedure = "/proxymanager.backconnect.v1.BackofficeBackConnectService/UpdateBackConnectManager"
	// BackofficeBackConnectServiceFetchBackConnectPortProcedure is the fully-qualified name of the
	// BackofficeBackConnectService's FetchBackConnectPort RPC.
	BackofficeBackConnectServiceFetchBackConnectPortProcedure = "/proxymanager.backconnect.v1.BackofficeBackConnectService/FetchBackConnectPort"
)

// BackofficeBackConnectServiceClient is a client for the
// proxymanager.backconnect.v1.BackofficeBackConnectService service.
type BackofficeBackConnectServiceClient interface {
	FetchBackConnectManager(context.Context, *connect.Request[v1.BackofficeBackConnectServiceFetchBackConnectManagerRequest]) (*connect.Response[v1.BackofficeBackConnectServiceFetchBackConnectManagerResponse], error)
	CreateBackConnectManager(context.Context, *connect.Request[v1.BackofficeBackConnectServiceCreateBackConnectManagerRequest]) (*connect.Response[v1.BackofficeBackConnectServiceCreateBackConnectManagerResponse], error)
	UpdateBackConnectManager(context.Context, *connect.Request[v1.BackofficeBackConnectServiceUpdateBackConnectManagerRequest]) (*connect.Response[v1.BackofficeBackConnectServiceUpdateBackConnectManagerResponse], error)
	FetchBackConnectPort(context.Context, *connect.Request[v1.BackofficeBackConnectServiceFetchBackConnectPortRequest]) (*connect.Response[v1.BackofficeBackConnectServiceFetchBackConnectPortResponse], error)
}

// NewBackofficeBackConnectServiceClient constructs a client for the
// proxymanager.backconnect.v1.BackofficeBackConnectService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackofficeBackConnectServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackofficeBackConnectServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backofficeBackConnectServiceMethods := v1.File_proxymanager_backconnect_v1_backoffice_proto.Services().ByName("BackofficeBackConnectService").Methods()
	return &backofficeBackConnectServiceClient{
		fetchBackConnectManager: connect.NewClient[v1.BackofficeBackConnectServiceFetchBackConnectManagerRequest, v1.BackofficeBackConnectServiceFetchBackConnectManagerResponse](
			httpClient,
			baseURL+BackofficeBackConnectServiceFetchBackConnectManagerProcedure,
			connect.WithSchema(backofficeBackConnectServiceMethods.ByName("FetchBackConnectManager")),
			connect.WithClientOptions(opts...),
		),
		createBackConnectManager: connect.NewClient[v1.BackofficeBackConnectServiceCreateBackConnectManagerRequest, v1.BackofficeBackConnectServiceCreateBackConnectManagerResponse](
			httpClient,
			baseURL+BackofficeBackConnectServiceCreateBackConnectManagerProcedure,
			connect.WithSchema(backofficeBackConnectServiceMethods.ByName("CreateBackConnectManager")),
			connect.WithClientOptions(opts...),
		),
		updateBackConnectManager: connect.NewClient[v1.BackofficeBackConnectServiceUpdateBackConnectManagerRequest, v1.BackofficeBackConnectServiceUpdateBackConnectManagerResponse](
			httpClient,
			baseURL+BackofficeBackConnectServiceUpdateBackConnectManagerProcedure,
			connect.WithSchema(backofficeBackConnectServiceMethods.ByName("UpdateBackConnectManager")),
			connect.WithClientOptions(opts...),
		),
		fetchBackConnectPort: connect.NewClient[v1.BackofficeBackConnectServiceFetchBackConnectPortRequest, v1.BackofficeBackConnectServiceFetchBackConnectPortResponse](
			httpClient,
			baseURL+BackofficeBackConnectServiceFetchBackConnectPortProcedure,
			connect.WithSchema(backofficeBackConnectServiceMethods.ByName("FetchBackConnectPort")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backofficeBackConnectServiceClient implements BackofficeBackConnectServiceClient.
type backofficeBackConnectServiceClient struct {
	fetchBackConnectManager  *connect.Client[v1.BackofficeBackConnectServiceFetchBackConnectManagerRequest, v1.BackofficeBackConnectServiceFetchBackConnectManagerResponse]
	createBackConnectManager *connect.Client[v1.BackofficeBackConnectServiceCreateBackConnectManagerRequest, v1.BackofficeBackConnectServiceCreateBackConnectManagerResponse]
	updateBackConnectManager *connect.Client[v1.BackofficeBackConnectServiceUpdateBackConnectManagerRequest, v1.BackofficeBackConnectServiceUpdateBackConnectManagerResponse]
	fetchBackConnectPort     *connect.Client[v1.BackofficeBackConnectServiceFetchBackConnectPortRequest, v1.BackofficeBackConnectServiceFetchBackConnectPortResponse]
}

// FetchBackConnectManager calls
// proxymanager.backconnect.v1.BackofficeBackConnectService.FetchBackConnectManager.
func (c *backofficeBackConnectServiceClient) FetchBackConnectManager(ctx context.Context, req *connect.Request[v1.BackofficeBackConnectServiceFetchBackConnectManagerRequest]) (*connect.Response[v1.BackofficeBackConnectServiceFetchBackConnectManagerResponse], error) {
	return c.fetchBackConnectManager.CallUnary(ctx, req)
}

// CreateBackConnectManager calls
// proxymanager.backconnect.v1.BackofficeBackConnectService.CreateBackConnectManager.
func (c *backofficeBackConnectServiceClient) CreateBackConnectManager(ctx context.Context, req *connect.Request[v1.BackofficeBackConnectServiceCreateBackConnectManagerRequest]) (*connect.Response[v1.BackofficeBackConnectServiceCreateBackConnectManagerResponse], error) {
	return c.createBackConnectManager.CallUnary(ctx, req)
}

// UpdateBackConnectManager calls
// proxymanager.backconnect.v1.BackofficeBackConnectService.UpdateBackConnectManager.
func (c *backofficeBackConnectServiceClient) UpdateBackConnectManager(ctx context.Context, req *connect.Request[v1.BackofficeBackConnectServiceUpdateBackConnectManagerRequest]) (*connect.Response[v1.BackofficeBackConnectServiceUpdateBackConnectManagerResponse], error) {
	return c.updateBackConnectManager.CallUnary(ctx, req)
}

// FetchBackConnectPort calls
// proxymanager.backconnect.v1.BackofficeBackConnectService.FetchBackConnectPort.
func (c *backofficeBackConnectServiceClient) FetchBackConnectPort(ctx context.Context, req *connect.Request[v1.BackofficeBackConnectServiceFetchBackConnectPortRequest]) (*connect.Response[v1.BackofficeBackConnectServiceFetchBackConnectPortResponse], error) {
	return c.fetchBackConnectPort.CallUnary(ctx, req)
}

// BackofficeBackConnectServiceHandler is an implementation of the
// proxymanager.backconnect.v1.BackofficeBackConnectService service.
type BackofficeBackConnectServiceHandler interface {
	FetchBackConnectManager(context.Context, *connect.Request[v1.BackofficeBackConnectServiceFetchBackConnectManagerRequest]) (*connect.Response[v1.BackofficeBackConnectServiceFetchBackConnectManagerResponse], error)
	CreateBackConnectManager(context.Context, *connect.Request[v1.BackofficeBackConnectServiceCreateBackConnectManagerRequest]) (*connect.Response[v1.BackofficeBackConnectServiceCreateBackConnectManagerResponse], error)
	UpdateBackConnectManager(context.Context, *connect.Request[v1.BackofficeBackConnectServiceUpdateBackConnectManagerRequest]) (*connect.Response[v1.BackofficeBackConnectServiceUpdateBackConnectManagerResponse], error)
	FetchBackConnectPort(context.Context, *connect.Request[v1.BackofficeBackConnectServiceFetchBackConnectPortRequest]) (*connect.Response[v1.BackofficeBackConnectServiceFetchBackConnectPortResponse], error)
}

// NewBackofficeBackConnectServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackofficeBackConnectServiceHandler(svc BackofficeBackConnectServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backofficeBackConnectServiceMethods := v1.File_proxymanager_backconnect_v1_backoffice_proto.Services().ByName("BackofficeBackConnectService").Methods()
	backofficeBackConnectServiceFetchBackConnectManagerHandler := connect.NewUnaryHandler(
		BackofficeBackConnectServiceFetchBackConnectManagerProcedure,
		svc.FetchBackConnectManager,
		connect.WithSchema(backofficeBackConnectServiceMethods.ByName("FetchBackConnectManager")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeBackConnectServiceCreateBackConnectManagerHandler := connect.NewUnaryHandler(
		BackofficeBackConnectServiceCreateBackConnectManagerProcedure,
		svc.CreateBackConnectManager,
		connect.WithSchema(backofficeBackConnectServiceMethods.ByName("CreateBackConnectManager")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeBackConnectServiceUpdateBackConnectManagerHandler := connect.NewUnaryHandler(
		BackofficeBackConnectServiceUpdateBackConnectManagerProcedure,
		svc.UpdateBackConnectManager,
		connect.WithSchema(backofficeBackConnectServiceMethods.ByName("UpdateBackConnectManager")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeBackConnectServiceFetchBackConnectPortHandler := connect.NewUnaryHandler(
		BackofficeBackConnectServiceFetchBackConnectPortProcedure,
		svc.FetchBackConnectPort,
		connect.WithSchema(backofficeBackConnectServiceMethods.ByName("FetchBackConnectPort")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.backconnect.v1.BackofficeBackConnectService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackofficeBackConnectServiceFetchBackConnectManagerProcedure:
			backofficeBackConnectServiceFetchBackConnectManagerHandler.ServeHTTP(w, r)
		case BackofficeBackConnectServiceCreateBackConnectManagerProcedure:
			backofficeBackConnectServiceCreateBackConnectManagerHandler.ServeHTTP(w, r)
		case BackofficeBackConnectServiceUpdateBackConnectManagerProcedure:
			backofficeBackConnectServiceUpdateBackConnectManagerHandler.ServeHTTP(w, r)
		case BackofficeBackConnectServiceFetchBackConnectPortProcedure:
			backofficeBackConnectServiceFetchBackConnectPortHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackofficeBackConnectServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackofficeBackConnectServiceHandler struct{}

func (UnimplementedBackofficeBackConnectServiceHandler) FetchBackConnectManager(context.Context, *connect.Request[v1.BackofficeBackConnectServiceFetchBackConnectManagerRequest]) (*connect.Response[v1.BackofficeBackConnectServiceFetchBackConnectManagerResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.backconnect.v1.BackofficeBackConnectService.FetchBackConnectManager is not implemented"))
}

func (UnimplementedBackofficeBackConnectServiceHandler) CreateBackConnectManager(context.Context, *connect.Request[v1.BackofficeBackConnectServiceCreateBackConnectManagerRequest]) (*connect.Response[v1.BackofficeBackConnectServiceCreateBackConnectManagerResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.backconnect.v1.BackofficeBackConnectService.CreateBackConnectManager is not implemented"))
}

func (UnimplementedBackofficeBackConnectServiceHandler) UpdateBackConnectManager(context.Context, *connect.Request[v1.BackofficeBackConnectServiceUpdateBackConnectManagerRequest]) (*connect.Response[v1.BackofficeBackConnectServiceUpdateBackConnectManagerResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.backconnect.v1.BackofficeBackConnectService.UpdateBackConnectManager is not implemented"))
}

func (UnimplementedBackofficeBackConnectServiceHandler) FetchBackConnectPort(context.Context, *connect.Request[v1.BackofficeBackConnectServiceFetchBackConnectPortRequest]) (*connect.Response[v1.BackofficeBackConnectServiceFetchBackConnectPortResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.backconnect.v1.BackofficeBackConnectService.FetchBackConnectPort is not implemented"))
}
