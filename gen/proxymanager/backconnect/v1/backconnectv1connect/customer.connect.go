// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/backconnect/v1/customer.proto

package backconnectv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/backconnect/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CustomerBackConnectServiceName is the fully-qualified name of the CustomerBackConnectService
	// service.
	CustomerBackConnectServiceName = "proxymanager.backconnect.v1.CustomerBackConnectService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CustomerBackConnectServiceFetchAvailableLocationOfBackConnectProcedure is the fully-qualified
	// name of the CustomerBackConnectService's FetchAvailableLocationOfBackConnect RPC.
	CustomerBackConnectServiceFetchAvailableLocationOfBackConnectProcedure = "/proxymanager.backconnect.v1.CustomerBackConnectService/FetchAvailableLocationOfBackConnect"
)

// CustomerBackConnectServiceClient is a client for the
// proxymanager.backconnect.v1.CustomerBackConnectService service.
type CustomerBackConnectServiceClient interface {
	FetchAvailableLocationOfBackConnect(context.Context, *connect.Request[v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest]) (*connect.Response[v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse], error)
}

// NewCustomerBackConnectServiceClient constructs a client for the
// proxymanager.backconnect.v1.CustomerBackConnectService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCustomerBackConnectServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CustomerBackConnectServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	customerBackConnectServiceMethods := v1.File_proxymanager_backconnect_v1_customer_proto.Services().ByName("CustomerBackConnectService").Methods()
	return &customerBackConnectServiceClient{
		fetchAvailableLocationOfBackConnect: connect.NewClient[v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest, v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse](
			httpClient,
			baseURL+CustomerBackConnectServiceFetchAvailableLocationOfBackConnectProcedure,
			connect.WithSchema(customerBackConnectServiceMethods.ByName("FetchAvailableLocationOfBackConnect")),
			connect.WithClientOptions(opts...),
		),
	}
}

// customerBackConnectServiceClient implements CustomerBackConnectServiceClient.
type customerBackConnectServiceClient struct {
	fetchAvailableLocationOfBackConnect *connect.Client[v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest, v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse]
}

// FetchAvailableLocationOfBackConnect calls
// proxymanager.backconnect.v1.CustomerBackConnectService.FetchAvailableLocationOfBackConnect.
func (c *customerBackConnectServiceClient) FetchAvailableLocationOfBackConnect(ctx context.Context, req *connect.Request[v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest]) (*connect.Response[v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse], error) {
	return c.fetchAvailableLocationOfBackConnect.CallUnary(ctx, req)
}

// CustomerBackConnectServiceHandler is an implementation of the
// proxymanager.backconnect.v1.CustomerBackConnectService service.
type CustomerBackConnectServiceHandler interface {
	FetchAvailableLocationOfBackConnect(context.Context, *connect.Request[v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest]) (*connect.Response[v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse], error)
}

// NewCustomerBackConnectServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCustomerBackConnectServiceHandler(svc CustomerBackConnectServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	customerBackConnectServiceMethods := v1.File_proxymanager_backconnect_v1_customer_proto.Services().ByName("CustomerBackConnectService").Methods()
	customerBackConnectServiceFetchAvailableLocationOfBackConnectHandler := connect.NewUnaryHandler(
		CustomerBackConnectServiceFetchAvailableLocationOfBackConnectProcedure,
		svc.FetchAvailableLocationOfBackConnect,
		connect.WithSchema(customerBackConnectServiceMethods.ByName("FetchAvailableLocationOfBackConnect")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.backconnect.v1.CustomerBackConnectService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CustomerBackConnectServiceFetchAvailableLocationOfBackConnectProcedure:
			customerBackConnectServiceFetchAvailableLocationOfBackConnectHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCustomerBackConnectServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCustomerBackConnectServiceHandler struct{}

func (UnimplementedCustomerBackConnectServiceHandler) FetchAvailableLocationOfBackConnect(context.Context, *connect.Request[v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest]) (*connect.Response[v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.backconnect.v1.CustomerBackConnectService.FetchAvailableLocationOfBackConnect is not implemented"))
}
