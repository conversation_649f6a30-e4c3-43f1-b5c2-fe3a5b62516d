// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/backconnect/v1/controlplane.proto

package backconnectv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/backconnect/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackConnectControlPlaneServiceName is the fully-qualified name of the
	// BackConnectControlPlaneService service.
	BackConnectControlPlaneServiceName = "proxymanager.backconnect.v1.BackConnectControlPlaneService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackConnectControlPlaneServiceRegisterManagerProcedure is the fully-qualified name of the
	// BackConnectControlPlaneService's RegisterManager RPC.
	BackConnectControlPlaneServiceRegisterManagerProcedure = "/proxymanager.backconnect.v1.BackConnectControlPlaneService/RegisterManager"
	// BackConnectControlPlaneServiceFetchPortProcedure is the fully-qualified name of the
	// BackConnectControlPlaneService's FetchPort RPC.
	BackConnectControlPlaneServiceFetchPortProcedure = "/proxymanager.backconnect.v1.BackConnectControlPlaneService/FetchPort"
	// BackConnectControlPlaneServiceTrackingProcedure is the fully-qualified name of the
	// BackConnectControlPlaneService's Tracking RPC.
	BackConnectControlPlaneServiceTrackingProcedure = "/proxymanager.backconnect.v1.BackConnectControlPlaneService/Tracking"
)

// BackConnectControlPlaneServiceClient is a client for the
// proxymanager.backconnect.v1.BackConnectControlPlaneService service.
type BackConnectControlPlaneServiceClient interface {
	RegisterManager(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceRegisterManagerRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceRegisterManagerResponse], error)
	FetchPort(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceFetchPortRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceFetchPortResponse], error)
	Tracking(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceTrackingRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceTrackingResponse], error)
}

// NewBackConnectControlPlaneServiceClient constructs a client for the
// proxymanager.backconnect.v1.BackConnectControlPlaneService service. By default, it uses the
// Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and sends
// uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackConnectControlPlaneServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackConnectControlPlaneServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backConnectControlPlaneServiceMethods := v1.File_proxymanager_backconnect_v1_controlplane_proto.Services().ByName("BackConnectControlPlaneService").Methods()
	return &backConnectControlPlaneServiceClient{
		registerManager: connect.NewClient[v1.BackConnectControlPlaneServiceRegisterManagerRequest, v1.BackConnectControlPlaneServiceRegisterManagerResponse](
			httpClient,
			baseURL+BackConnectControlPlaneServiceRegisterManagerProcedure,
			connect.WithSchema(backConnectControlPlaneServiceMethods.ByName("RegisterManager")),
			connect.WithClientOptions(opts...),
		),
		fetchPort: connect.NewClient[v1.BackConnectControlPlaneServiceFetchPortRequest, v1.BackConnectControlPlaneServiceFetchPortResponse](
			httpClient,
			baseURL+BackConnectControlPlaneServiceFetchPortProcedure,
			connect.WithSchema(backConnectControlPlaneServiceMethods.ByName("FetchPort")),
			connect.WithClientOptions(opts...),
		),
		tracking: connect.NewClient[v1.BackConnectControlPlaneServiceTrackingRequest, v1.BackConnectControlPlaneServiceTrackingResponse](
			httpClient,
			baseURL+BackConnectControlPlaneServiceTrackingProcedure,
			connect.WithSchema(backConnectControlPlaneServiceMethods.ByName("Tracking")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backConnectControlPlaneServiceClient implements BackConnectControlPlaneServiceClient.
type backConnectControlPlaneServiceClient struct {
	registerManager *connect.Client[v1.BackConnectControlPlaneServiceRegisterManagerRequest, v1.BackConnectControlPlaneServiceRegisterManagerResponse]
	fetchPort       *connect.Client[v1.BackConnectControlPlaneServiceFetchPortRequest, v1.BackConnectControlPlaneServiceFetchPortResponse]
	tracking        *connect.Client[v1.BackConnectControlPlaneServiceTrackingRequest, v1.BackConnectControlPlaneServiceTrackingResponse]
}

// RegisterManager calls proxymanager.backconnect.v1.BackConnectControlPlaneService.RegisterManager.
func (c *backConnectControlPlaneServiceClient) RegisterManager(ctx context.Context, req *connect.Request[v1.BackConnectControlPlaneServiceRegisterManagerRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceRegisterManagerResponse], error) {
	return c.registerManager.CallUnary(ctx, req)
}

// FetchPort calls proxymanager.backconnect.v1.BackConnectControlPlaneService.FetchPort.
func (c *backConnectControlPlaneServiceClient) FetchPort(ctx context.Context, req *connect.Request[v1.BackConnectControlPlaneServiceFetchPortRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceFetchPortResponse], error) {
	return c.fetchPort.CallUnary(ctx, req)
}

// Tracking calls proxymanager.backconnect.v1.BackConnectControlPlaneService.Tracking.
func (c *backConnectControlPlaneServiceClient) Tracking(ctx context.Context, req *connect.Request[v1.BackConnectControlPlaneServiceTrackingRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceTrackingResponse], error) {
	return c.tracking.CallUnary(ctx, req)
}

// BackConnectControlPlaneServiceHandler is an implementation of the
// proxymanager.backconnect.v1.BackConnectControlPlaneService service.
type BackConnectControlPlaneServiceHandler interface {
	RegisterManager(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceRegisterManagerRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceRegisterManagerResponse], error)
	FetchPort(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceFetchPortRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceFetchPortResponse], error)
	Tracking(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceTrackingRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceTrackingResponse], error)
}

// NewBackConnectControlPlaneServiceHandler builds an HTTP handler from the service implementation.
// It returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackConnectControlPlaneServiceHandler(svc BackConnectControlPlaneServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backConnectControlPlaneServiceMethods := v1.File_proxymanager_backconnect_v1_controlplane_proto.Services().ByName("BackConnectControlPlaneService").Methods()
	backConnectControlPlaneServiceRegisterManagerHandler := connect.NewUnaryHandler(
		BackConnectControlPlaneServiceRegisterManagerProcedure,
		svc.RegisterManager,
		connect.WithSchema(backConnectControlPlaneServiceMethods.ByName("RegisterManager")),
		connect.WithHandlerOptions(opts...),
	)
	backConnectControlPlaneServiceFetchPortHandler := connect.NewUnaryHandler(
		BackConnectControlPlaneServiceFetchPortProcedure,
		svc.FetchPort,
		connect.WithSchema(backConnectControlPlaneServiceMethods.ByName("FetchPort")),
		connect.WithHandlerOptions(opts...),
	)
	backConnectControlPlaneServiceTrackingHandler := connect.NewUnaryHandler(
		BackConnectControlPlaneServiceTrackingProcedure,
		svc.Tracking,
		connect.WithSchema(backConnectControlPlaneServiceMethods.ByName("Tracking")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.backconnect.v1.BackConnectControlPlaneService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackConnectControlPlaneServiceRegisterManagerProcedure:
			backConnectControlPlaneServiceRegisterManagerHandler.ServeHTTP(w, r)
		case BackConnectControlPlaneServiceFetchPortProcedure:
			backConnectControlPlaneServiceFetchPortHandler.ServeHTTP(w, r)
		case BackConnectControlPlaneServiceTrackingProcedure:
			backConnectControlPlaneServiceTrackingHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackConnectControlPlaneServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackConnectControlPlaneServiceHandler struct{}

func (UnimplementedBackConnectControlPlaneServiceHandler) RegisterManager(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceRegisterManagerRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceRegisterManagerResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.backconnect.v1.BackConnectControlPlaneService.RegisterManager is not implemented"))
}

func (UnimplementedBackConnectControlPlaneServiceHandler) FetchPort(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceFetchPortRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceFetchPortResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.backconnect.v1.BackConnectControlPlaneService.FetchPort is not implemented"))
}

func (UnimplementedBackConnectControlPlaneServiceHandler) Tracking(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceTrackingRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceTrackingResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.backconnect.v1.BackConnectControlPlaneService.Tracking is not implemented"))
}
