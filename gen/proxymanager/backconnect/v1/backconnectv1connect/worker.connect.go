// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/backconnect/v1/worker.proto

package backconnectv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/backconnect/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackConnectWorkerServiceName is the fully-qualified name of the BackConnectWorkerService service.
	BackConnectWorkerServiceName = "proxymanager.backconnect.v1.BackConnectWorkerService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackConnectWorkerServiceHealthCheckProcedure is the fully-qualified name of the
	// BackConnectWorkerService's HealthCheck RPC.
	BackConnectWorkerServiceHealthCheckProcedure = "/proxymanager.backconnect.v1.BackConnectWorkerService/HealthCheck"
	// BackConnectWorkerServiceConfigHopProcedure is the fully-qualified name of the
	// BackConnectWorkerService's ConfigHop RPC.
	BackConnectWorkerServiceConfigHopProcedure = "/proxymanager.backconnect.v1.BackConnectWorkerService/ConfigHop"
)

// BackConnectWorkerServiceClient is a client for the
// proxymanager.backconnect.v1.BackConnectWorkerService service.
type BackConnectWorkerServiceClient interface {
	HealthCheck(context.Context, *connect.Request[v1.BackConnectWorkerServiceHealthCheckRequest]) (*connect.Response[v1.BackConnectWorkerServiceHealthCheckResponse], error)
	ConfigHop(context.Context, *connect.Request[v1.BackConnectWorkerServiceConfigHopRequest]) (*connect.Response[v1.BackConnectWorkerServiceConfigHopResponse], error)
}

// NewBackConnectWorkerServiceClient constructs a client for the
// proxymanager.backconnect.v1.BackConnectWorkerService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackConnectWorkerServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackConnectWorkerServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backConnectWorkerServiceMethods := v1.File_proxymanager_backconnect_v1_worker_proto.Services().ByName("BackConnectWorkerService").Methods()
	return &backConnectWorkerServiceClient{
		healthCheck: connect.NewClient[v1.BackConnectWorkerServiceHealthCheckRequest, v1.BackConnectWorkerServiceHealthCheckResponse](
			httpClient,
			baseURL+BackConnectWorkerServiceHealthCheckProcedure,
			connect.WithSchema(backConnectWorkerServiceMethods.ByName("HealthCheck")),
			connect.WithClientOptions(opts...),
		),
		configHop: connect.NewClient[v1.BackConnectWorkerServiceConfigHopRequest, v1.BackConnectWorkerServiceConfigHopResponse](
			httpClient,
			baseURL+BackConnectWorkerServiceConfigHopProcedure,
			connect.WithSchema(backConnectWorkerServiceMethods.ByName("ConfigHop")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backConnectWorkerServiceClient implements BackConnectWorkerServiceClient.
type backConnectWorkerServiceClient struct {
	healthCheck *connect.Client[v1.BackConnectWorkerServiceHealthCheckRequest, v1.BackConnectWorkerServiceHealthCheckResponse]
	configHop   *connect.Client[v1.BackConnectWorkerServiceConfigHopRequest, v1.BackConnectWorkerServiceConfigHopResponse]
}

// HealthCheck calls proxymanager.backconnect.v1.BackConnectWorkerService.HealthCheck.
func (c *backConnectWorkerServiceClient) HealthCheck(ctx context.Context, req *connect.Request[v1.BackConnectWorkerServiceHealthCheckRequest]) (*connect.Response[v1.BackConnectWorkerServiceHealthCheckResponse], error) {
	return c.healthCheck.CallUnary(ctx, req)
}

// ConfigHop calls proxymanager.backconnect.v1.BackConnectWorkerService.ConfigHop.
func (c *backConnectWorkerServiceClient) ConfigHop(ctx context.Context, req *connect.Request[v1.BackConnectWorkerServiceConfigHopRequest]) (*connect.Response[v1.BackConnectWorkerServiceConfigHopResponse], error) {
	return c.configHop.CallUnary(ctx, req)
}

// BackConnectWorkerServiceHandler is an implementation of the
// proxymanager.backconnect.v1.BackConnectWorkerService service.
type BackConnectWorkerServiceHandler interface {
	HealthCheck(context.Context, *connect.Request[v1.BackConnectWorkerServiceHealthCheckRequest]) (*connect.Response[v1.BackConnectWorkerServiceHealthCheckResponse], error)
	ConfigHop(context.Context, *connect.Request[v1.BackConnectWorkerServiceConfigHopRequest]) (*connect.Response[v1.BackConnectWorkerServiceConfigHopResponse], error)
}

// NewBackConnectWorkerServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackConnectWorkerServiceHandler(svc BackConnectWorkerServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backConnectWorkerServiceMethods := v1.File_proxymanager_backconnect_v1_worker_proto.Services().ByName("BackConnectWorkerService").Methods()
	backConnectWorkerServiceHealthCheckHandler := connect.NewUnaryHandler(
		BackConnectWorkerServiceHealthCheckProcedure,
		svc.HealthCheck,
		connect.WithSchema(backConnectWorkerServiceMethods.ByName("HealthCheck")),
		connect.WithHandlerOptions(opts...),
	)
	backConnectWorkerServiceConfigHopHandler := connect.NewUnaryHandler(
		BackConnectWorkerServiceConfigHopProcedure,
		svc.ConfigHop,
		connect.WithSchema(backConnectWorkerServiceMethods.ByName("ConfigHop")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.backconnect.v1.BackConnectWorkerService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackConnectWorkerServiceHealthCheckProcedure:
			backConnectWorkerServiceHealthCheckHandler.ServeHTTP(w, r)
		case BackConnectWorkerServiceConfigHopProcedure:
			backConnectWorkerServiceConfigHopHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackConnectWorkerServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackConnectWorkerServiceHandler struct{}

func (UnimplementedBackConnectWorkerServiceHandler) HealthCheck(context.Context, *connect.Request[v1.BackConnectWorkerServiceHealthCheckRequest]) (*connect.Response[v1.BackConnectWorkerServiceHealthCheckResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.backconnect.v1.BackConnectWorkerService.HealthCheck is not implemented"))
}

func (UnimplementedBackConnectWorkerServiceHandler) ConfigHop(context.Context, *connect.Request[v1.BackConnectWorkerServiceConfigHopRequest]) (*connect.Response[v1.BackConnectWorkerServiceConfigHopResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.backconnect.v1.BackConnectWorkerService.ConfigHop is not implemented"))
}
