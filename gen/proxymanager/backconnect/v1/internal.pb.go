// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/backconnect/v1/internal.proto

package backconnectv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_proxymanager_backconnect_v1_internal_proto protoreflect.FileDescriptor

const file_proxymanager_backconnect_v1_internal_proto_rawDesc = "" +
	"\n" +
	"*proxymanager/backconnect/v1/internal.proto\x12\x1bproxymanager.backconnect.v1BZZXgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/backconnect/v1;backconnectv1b\x06proto3"

var file_proxymanager_backconnect_v1_internal_proto_goTypes = []any{}
var file_proxymanager_backconnect_v1_internal_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proxymanager_backconnect_v1_internal_proto_init() }
func file_proxymanager_backconnect_v1_internal_proto_init() {
	if File_proxymanager_backconnect_v1_internal_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_backconnect_v1_internal_proto_rawDesc), len(file_proxymanager_backconnect_v1_internal_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proxymanager_backconnect_v1_internal_proto_goTypes,
		DependencyIndexes: file_proxymanager_backconnect_v1_internal_proto_depIdxs,
	}.Build()
	File_proxymanager_backconnect_v1_internal_proto = out.File
	file_proxymanager_backconnect_v1_internal_proto_goTypes = nil
	file_proxymanager_backconnect_v1_internal_proto_depIdxs = nil
}
