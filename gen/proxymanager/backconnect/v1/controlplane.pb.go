// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/backconnect/v1/controlplane.proto

package backconnectv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackConnectControlPlaneServiceTrackingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdSession     string                 `protobuf:"bytes,1,opt,name=id_session,json=idSession,proto3" json:"id_session,omitempty"`
	PacketType    v1.PacketType          `protobuf:"varint,2,opt,name=packet_type,json=packetType,proto3,enum=algoenum.v1.PacketType" json:"packet_type,omitempty"`
	IpUser        string                 `protobuf:"bytes,3,opt,name=ip_user,json=ipUser,proto3" json:"ip_user,omitempty"`
	Domain        string                 `protobuf:"bytes,4,opt,name=domain,proto3" json:"domain,omitempty"`
	IpOfDomain    string                 `protobuf:"bytes,5,opt,name=ip_of_domain,json=ipOfDomain,proto3" json:"ip_of_domain,omitempty"`
	BytesUpload   int64                  `protobuf:"varint,6,opt,name=bytes_upload,json=bytesUpload,proto3" json:"bytes_upload,omitempty"`
	BytesDownload int64                  `protobuf:"varint,7,opt,name=bytes_download,json=bytesDownload,proto3" json:"bytes_download,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectControlPlaneServiceTrackingRequest) Reset() {
	*x = BackConnectControlPlaneServiceTrackingRequest{}
	mi := &file_proxymanager_backconnect_v1_controlplane_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectControlPlaneServiceTrackingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectControlPlaneServiceTrackingRequest) ProtoMessage() {}

func (x *BackConnectControlPlaneServiceTrackingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_controlplane_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectControlPlaneServiceTrackingRequest.ProtoReflect.Descriptor instead.
func (*BackConnectControlPlaneServiceTrackingRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_controlplane_proto_rawDescGZIP(), []int{0}
}

func (x *BackConnectControlPlaneServiceTrackingRequest) GetIdSession() string {
	if x != nil {
		return x.IdSession
	}
	return ""
}

func (x *BackConnectControlPlaneServiceTrackingRequest) GetPacketType() v1.PacketType {
	if x != nil {
		return x.PacketType
	}
	return v1.PacketType(0)
}

func (x *BackConnectControlPlaneServiceTrackingRequest) GetIpUser() string {
	if x != nil {
		return x.IpUser
	}
	return ""
}

func (x *BackConnectControlPlaneServiceTrackingRequest) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *BackConnectControlPlaneServiceTrackingRequest) GetIpOfDomain() string {
	if x != nil {
		return x.IpOfDomain
	}
	return ""
}

func (x *BackConnectControlPlaneServiceTrackingRequest) GetBytesUpload() int64 {
	if x != nil {
		return x.BytesUpload
	}
	return 0
}

func (x *BackConnectControlPlaneServiceTrackingRequest) GetBytesDownload() int64 {
	if x != nil {
		return x.BytesDownload
	}
	return 0
}

func (x *BackConnectControlPlaneServiceTrackingRequest) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

type BackConnectControlPlaneServiceTrackingResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectControlPlaneServiceTrackingResponse) Reset() {
	*x = BackConnectControlPlaneServiceTrackingResponse{}
	mi := &file_proxymanager_backconnect_v1_controlplane_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectControlPlaneServiceTrackingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectControlPlaneServiceTrackingResponse) ProtoMessage() {}

func (x *BackConnectControlPlaneServiceTrackingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_controlplane_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectControlPlaneServiceTrackingResponse.ProtoReflect.Descriptor instead.
func (*BackConnectControlPlaneServiceTrackingResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_controlplane_proto_rawDescGZIP(), []int{1}
}

func (x *BackConnectControlPlaneServiceTrackingResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type BackConnectControlPlaneServiceRegisterManagerRequest struct {
	state                protoimpl.MessageState      `protogen:"open.v1"`
	IdBackconnectManager string                      `protobuf:"bytes,1,opt,name=id_backconnect_manager,json=idBackconnectManager,proto3" json:"id_backconnect_manager,omitempty"`
	PublicIp             string                      `protobuf:"bytes,2,opt,name=public_ip,json=publicIp,proto3" json:"public_ip,omitempty"`
	ControlIp            string                      `protobuf:"bytes,3,opt,name=control_ip,json=controlIp,proto3" json:"control_ip,omitempty"`
	ControlPort          int64                       `protobuf:"varint,4,opt,name=control_port,json=controlPort,proto3" json:"control_port,omitempty"`
	Status               v1.BackConnectManagerStatus `protobuf:"varint,5,opt,name=status,proto3,enum=algoenum.v1.BackConnectManagerStatus" json:"status,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *BackConnectControlPlaneServiceRegisterManagerRequest) Reset() {
	*x = BackConnectControlPlaneServiceRegisterManagerRequest{}
	mi := &file_proxymanager_backconnect_v1_controlplane_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectControlPlaneServiceRegisterManagerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectControlPlaneServiceRegisterManagerRequest) ProtoMessage() {}

func (x *BackConnectControlPlaneServiceRegisterManagerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_controlplane_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectControlPlaneServiceRegisterManagerRequest.ProtoReflect.Descriptor instead.
func (*BackConnectControlPlaneServiceRegisterManagerRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_controlplane_proto_rawDescGZIP(), []int{2}
}

func (x *BackConnectControlPlaneServiceRegisterManagerRequest) GetIdBackconnectManager() string {
	if x != nil {
		return x.IdBackconnectManager
	}
	return ""
}

func (x *BackConnectControlPlaneServiceRegisterManagerRequest) GetPublicIp() string {
	if x != nil {
		return x.PublicIp
	}
	return ""
}

func (x *BackConnectControlPlaneServiceRegisterManagerRequest) GetControlIp() string {
	if x != nil {
		return x.ControlIp
	}
	return ""
}

func (x *BackConnectControlPlaneServiceRegisterManagerRequest) GetControlPort() int64 {
	if x != nil {
		return x.ControlPort
	}
	return 0
}

func (x *BackConnectControlPlaneServiceRegisterManagerRequest) GetStatus() v1.BackConnectManagerStatus {
	if x != nil {
		return x.Status
	}
	return v1.BackConnectManagerStatus(0)
}

type BackConnectControlPlaneServiceRegisterManagerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectControlPlaneServiceRegisterManagerResponse) Reset() {
	*x = BackConnectControlPlaneServiceRegisterManagerResponse{}
	mi := &file_proxymanager_backconnect_v1_controlplane_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectControlPlaneServiceRegisterManagerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectControlPlaneServiceRegisterManagerResponse) ProtoMessage() {}

func (x *BackConnectControlPlaneServiceRegisterManagerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_controlplane_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectControlPlaneServiceRegisterManagerResponse.ProtoReflect.Descriptor instead.
func (*BackConnectControlPlaneServiceRegisterManagerResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_controlplane_proto_rawDescGZIP(), []int{3}
}

func (x *BackConnectControlPlaneServiceRegisterManagerResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackConnectControlPlaneServiceFetchPortRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	IdBackConnectManager string                 `protobuf:"bytes,1,opt,name=id_back_connect_manager,json=idBackConnectManager,proto3" json:"id_back_connect_manager,omitempty"`
	Pagination           *v12.PaginationRequest `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *BackConnectControlPlaneServiceFetchPortRequest) Reset() {
	*x = BackConnectControlPlaneServiceFetchPortRequest{}
	mi := &file_proxymanager_backconnect_v1_controlplane_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectControlPlaneServiceFetchPortRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectControlPlaneServiceFetchPortRequest) ProtoMessage() {}

func (x *BackConnectControlPlaneServiceFetchPortRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_controlplane_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectControlPlaneServiceFetchPortRequest.ProtoReflect.Descriptor instead.
func (*BackConnectControlPlaneServiceFetchPortRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_controlplane_proto_rawDescGZIP(), []int{4}
}

func (x *BackConnectControlPlaneServiceFetchPortRequest) GetIdBackConnectManager() string {
	if x != nil {
		return x.IdBackConnectManager
	}
	return ""
}

func (x *BackConnectControlPlaneServiceFetchPortRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackConnectControlPlaneServiceFetchPortResponse struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	Ports         []*ControlPlaneBackConnectPort `protobuf:"bytes,1,rep,name=ports,proto3" json:"ports,omitempty"`
	Pagination    *v12.PaginationResponse        `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Error         *v11.ErrorMessage              `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectControlPlaneServiceFetchPortResponse) Reset() {
	*x = BackConnectControlPlaneServiceFetchPortResponse{}
	mi := &file_proxymanager_backconnect_v1_controlplane_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectControlPlaneServiceFetchPortResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectControlPlaneServiceFetchPortResponse) ProtoMessage() {}

func (x *BackConnectControlPlaneServiceFetchPortResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_controlplane_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectControlPlaneServiceFetchPortResponse.ProtoReflect.Descriptor instead.
func (*BackConnectControlPlaneServiceFetchPortResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_controlplane_proto_rawDescGZIP(), []int{5}
}

func (x *BackConnectControlPlaneServiceFetchPortResponse) GetPorts() []*ControlPlaneBackConnectPort {
	if x != nil {
		return x.Ports
	}
	return nil
}

func (x *BackConnectControlPlaneServiceFetchPortResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackConnectControlPlaneServiceFetchPortResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type ControlPlaneBackConnectPort struct {
	state             protoimpl.MessageState   `protogen:"open.v1"`
	IdBackConnectPort string                   `protobuf:"bytes,1,opt,name=id_back_connect_port,json=idBackConnectPort,proto3" json:"id_back_connect_port,omitempty"`
	Port              int64                    `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	ProxyProtocol     v1.ProxyProtocol         `protobuf:"varint,3,opt,name=proxy_protocol,json=proxyProtocol,proto3,enum=algoenum.v1.ProxyProtocol" json:"proxy_protocol,omitempty"`
	Status            v1.BackConnectPortStatus `protobuf:"varint,4,opt,name=status,proto3,enum=algoenum.v1.BackConnectPortStatus" json:"status,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ControlPlaneBackConnectPort) Reset() {
	*x = ControlPlaneBackConnectPort{}
	mi := &file_proxymanager_backconnect_v1_controlplane_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ControlPlaneBackConnectPort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ControlPlaneBackConnectPort) ProtoMessage() {}

func (x *ControlPlaneBackConnectPort) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_controlplane_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ControlPlaneBackConnectPort.ProtoReflect.Descriptor instead.
func (*ControlPlaneBackConnectPort) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_controlplane_proto_rawDescGZIP(), []int{6}
}

func (x *ControlPlaneBackConnectPort) GetIdBackConnectPort() string {
	if x != nil {
		return x.IdBackConnectPort
	}
	return ""
}

func (x *ControlPlaneBackConnectPort) GetPort() int64 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *ControlPlaneBackConnectPort) GetProxyProtocol() v1.ProxyProtocol {
	if x != nil {
		return x.ProxyProtocol
	}
	return v1.ProxyProtocol(0)
}

func (x *ControlPlaneBackConnectPort) GetStatus() v1.BackConnectPortStatus {
	if x != nil {
		return x.Status
	}
	return v1.BackConnectPortStatus(0)
}

var File_proxymanager_backconnect_v1_controlplane_proto protoreflect.FileDescriptor

const file_proxymanager_backconnect_v1_controlplane_proto_rawDesc = "" +
	"\n" +
	".proxymanager/backconnect/v1/controlplane.proto\x12\x1bproxymanager.backconnect.v1\x1a\x18errmsg/v1/errormsg.proto\x1a algoenum/v1/proxy_protocol.proto\x1a*algoenum/v1/back_connect_port_status.proto\x1a-algoenum/v1/back_connect_manager_status.proto\x1a\x1dalgoenum/v1/packet_type.proto\x1a\x14utils/v1/utils.proto\"\xc4\x02\n" +
	"-BackConnectControlPlaneServiceTrackingRequest\x12\x1d\n" +
	"\n" +
	"id_session\x18\x01 \x01(\tR\tidSession\x128\n" +
	"\vpacket_type\x18\x02 \x01(\x0e2\x17.algoenum.v1.PacketTypeR\n" +
	"packetType\x12\x17\n" +
	"\aip_user\x18\x03 \x01(\tR\x06ipUser\x12\x16\n" +
	"\x06domain\x18\x04 \x01(\tR\x06domain\x12 \n" +
	"\fip_of_domain\x18\x05 \x01(\tR\n" +
	"ipOfDomain\x12!\n" +
	"\fbytes_upload\x18\x06 \x01(\x03R\vbytesUpload\x12%\n" +
	"\x0ebytes_download\x18\a \x01(\x03R\rbytesDownload\x12\x1d\n" +
	"\n" +
	"created_at\x18\b \x01(\x03R\tcreatedAt\"J\n" +
	".BackConnectControlPlaneServiceTrackingResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"\x8a\x02\n" +
	"4BackConnectControlPlaneServiceRegisterManagerRequest\x124\n" +
	"\x16id_backconnect_manager\x18\x01 \x01(\tR\x14idBackconnectManager\x12\x1b\n" +
	"\tpublic_ip\x18\x02 \x01(\tR\bpublicIp\x12\x1d\n" +
	"\n" +
	"control_ip\x18\x03 \x01(\tR\tcontrolIp\x12!\n" +
	"\fcontrol_port\x18\x04 \x01(\x03R\vcontrolPort\x12=\n" +
	"\x06status\x18\x05 \x01(\x0e2%.algoenum.v1.BackConnectManagerStatusR\x06status\"f\n" +
	"5BackConnectControlPlaneServiceRegisterManagerResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xa4\x01\n" +
	".BackConnectControlPlaneServiceFetchPortRequest\x125\n" +
	"\x17id_back_connect_manager\x18\x01 \x01(\tR\x14idBackConnectManager\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xee\x01\n" +
	"/BackConnectControlPlaneServiceFetchPortResponse\x12N\n" +
	"\x05ports\x18\x01 \x03(\v28.proxymanager.backconnect.v1.ControlPlaneBackConnectPortR\x05ports\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xe1\x01\n" +
	"\x1bControlPlaneBackConnectPort\x12/\n" +
	"\x14id_back_connect_port\x18\x01 \x01(\tR\x11idBackConnectPort\x12\x12\n" +
	"\x04port\x18\x02 \x01(\x03R\x04port\x12A\n" +
	"\x0eproxy_protocol\x18\x03 \x01(\x0e2\x1a.algoenum.v1.ProxyProtocolR\rproxyProtocol\x12:\n" +
	"\x06status\x18\x04 \x01(\x0e2\".algoenum.v1.BackConnectPortStatusR\x06status2\xaa\x04\n" +
	"\x1eBackConnectControlPlaneService\x12\xb8\x01\n" +
	"\x0fRegisterManager\x12Q.proxymanager.backconnect.v1.BackConnectControlPlaneServiceRegisterManagerRequest\x1aR.proxymanager.backconnect.v1.BackConnectControlPlaneServiceRegisterManagerResponse\x12\xa6\x01\n" +
	"\tFetchPort\x12K.proxymanager.backconnect.v1.BackConnectControlPlaneServiceFetchPortRequest\x1aL.proxymanager.backconnect.v1.BackConnectControlPlaneServiceFetchPortResponse\x12\xa3\x01\n" +
	"\bTracking\x12J.proxymanager.backconnect.v1.BackConnectControlPlaneServiceTrackingRequest\x1aK.proxymanager.backconnect.v1.BackConnectControlPlaneServiceTrackingResponseBZZXgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/backconnect/v1;backconnectv1b\x06proto3"

var (
	file_proxymanager_backconnect_v1_controlplane_proto_rawDescOnce sync.Once
	file_proxymanager_backconnect_v1_controlplane_proto_rawDescData []byte
)

func file_proxymanager_backconnect_v1_controlplane_proto_rawDescGZIP() []byte {
	file_proxymanager_backconnect_v1_controlplane_proto_rawDescOnce.Do(func() {
		file_proxymanager_backconnect_v1_controlplane_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_backconnect_v1_controlplane_proto_rawDesc), len(file_proxymanager_backconnect_v1_controlplane_proto_rawDesc)))
	})
	return file_proxymanager_backconnect_v1_controlplane_proto_rawDescData
}

var file_proxymanager_backconnect_v1_controlplane_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_proxymanager_backconnect_v1_controlplane_proto_goTypes = []any{
	(*BackConnectControlPlaneServiceTrackingRequest)(nil),         // 0: proxymanager.backconnect.v1.BackConnectControlPlaneServiceTrackingRequest
	(*BackConnectControlPlaneServiceTrackingResponse)(nil),        // 1: proxymanager.backconnect.v1.BackConnectControlPlaneServiceTrackingResponse
	(*BackConnectControlPlaneServiceRegisterManagerRequest)(nil),  // 2: proxymanager.backconnect.v1.BackConnectControlPlaneServiceRegisterManagerRequest
	(*BackConnectControlPlaneServiceRegisterManagerResponse)(nil), // 3: proxymanager.backconnect.v1.BackConnectControlPlaneServiceRegisterManagerResponse
	(*BackConnectControlPlaneServiceFetchPortRequest)(nil),        // 4: proxymanager.backconnect.v1.BackConnectControlPlaneServiceFetchPortRequest
	(*BackConnectControlPlaneServiceFetchPortResponse)(nil),       // 5: proxymanager.backconnect.v1.BackConnectControlPlaneServiceFetchPortResponse
	(*ControlPlaneBackConnectPort)(nil),                           // 6: proxymanager.backconnect.v1.ControlPlaneBackConnectPort
	(v1.PacketType)(0),                                            // 7: algoenum.v1.PacketType
	(v1.BackConnectManagerStatus)(0),                              // 8: algoenum.v1.BackConnectManagerStatus
	(*v11.ErrorMessage)(nil),                                      // 9: errmsg.v1.ErrorMessage
	(*v12.PaginationRequest)(nil),                                 // 10: utils.v1.PaginationRequest
	(*v12.PaginationResponse)(nil),                                // 11: utils.v1.PaginationResponse
	(v1.ProxyProtocol)(0),                                         // 12: algoenum.v1.ProxyProtocol
	(v1.BackConnectPortStatus)(0),                                 // 13: algoenum.v1.BackConnectPortStatus
}
var file_proxymanager_backconnect_v1_controlplane_proto_depIdxs = []int32{
	7,  // 0: proxymanager.backconnect.v1.BackConnectControlPlaneServiceTrackingRequest.packet_type:type_name -> algoenum.v1.PacketType
	8,  // 1: proxymanager.backconnect.v1.BackConnectControlPlaneServiceRegisterManagerRequest.status:type_name -> algoenum.v1.BackConnectManagerStatus
	9,  // 2: proxymanager.backconnect.v1.BackConnectControlPlaneServiceRegisterManagerResponse.error:type_name -> errmsg.v1.ErrorMessage
	10, // 3: proxymanager.backconnect.v1.BackConnectControlPlaneServiceFetchPortRequest.pagination:type_name -> utils.v1.PaginationRequest
	6,  // 4: proxymanager.backconnect.v1.BackConnectControlPlaneServiceFetchPortResponse.ports:type_name -> proxymanager.backconnect.v1.ControlPlaneBackConnectPort
	11, // 5: proxymanager.backconnect.v1.BackConnectControlPlaneServiceFetchPortResponse.pagination:type_name -> utils.v1.PaginationResponse
	9,  // 6: proxymanager.backconnect.v1.BackConnectControlPlaneServiceFetchPortResponse.error:type_name -> errmsg.v1.ErrorMessage
	12, // 7: proxymanager.backconnect.v1.ControlPlaneBackConnectPort.proxy_protocol:type_name -> algoenum.v1.ProxyProtocol
	13, // 8: proxymanager.backconnect.v1.ControlPlaneBackConnectPort.status:type_name -> algoenum.v1.BackConnectPortStatus
	2,  // 9: proxymanager.backconnect.v1.BackConnectControlPlaneService.RegisterManager:input_type -> proxymanager.backconnect.v1.BackConnectControlPlaneServiceRegisterManagerRequest
	4,  // 10: proxymanager.backconnect.v1.BackConnectControlPlaneService.FetchPort:input_type -> proxymanager.backconnect.v1.BackConnectControlPlaneServiceFetchPortRequest
	0,  // 11: proxymanager.backconnect.v1.BackConnectControlPlaneService.Tracking:input_type -> proxymanager.backconnect.v1.BackConnectControlPlaneServiceTrackingRequest
	3,  // 12: proxymanager.backconnect.v1.BackConnectControlPlaneService.RegisterManager:output_type -> proxymanager.backconnect.v1.BackConnectControlPlaneServiceRegisterManagerResponse
	5,  // 13: proxymanager.backconnect.v1.BackConnectControlPlaneService.FetchPort:output_type -> proxymanager.backconnect.v1.BackConnectControlPlaneServiceFetchPortResponse
	1,  // 14: proxymanager.backconnect.v1.BackConnectControlPlaneService.Tracking:output_type -> proxymanager.backconnect.v1.BackConnectControlPlaneServiceTrackingResponse
	12, // [12:15] is the sub-list for method output_type
	9,  // [9:12] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_proxymanager_backconnect_v1_controlplane_proto_init() }
func file_proxymanager_backconnect_v1_controlplane_proto_init() {
	if File_proxymanager_backconnect_v1_controlplane_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_backconnect_v1_controlplane_proto_rawDesc), len(file_proxymanager_backconnect_v1_controlplane_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_backconnect_v1_controlplane_proto_goTypes,
		DependencyIndexes: file_proxymanager_backconnect_v1_controlplane_proto_depIdxs,
		MessageInfos:      file_proxymanager_backconnect_v1_controlplane_proto_msgTypes,
	}.Build()
	File_proxymanager_backconnect_v1_controlplane_proto = out.File
	file_proxymanager_backconnect_v1_controlplane_proto_goTypes = nil
	file_proxymanager_backconnect_v1_controlplane_proto_depIdxs = nil
}
