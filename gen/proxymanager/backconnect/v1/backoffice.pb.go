// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/backconnect/v1/backoffice.proto

package backconnectv1

import (
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackofficeBackConnectServiceCreateBackConnectManagerRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant    string                 `protobuf:"bytes,1,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	StartPort     int64                  `protobuf:"varint,3,opt,name=start_port,json=startPort,proto3" json:"start_port,omitempty"`
	EndPort       int64                  `protobuf:"varint,4,opt,name=end_port,json=endPort,proto3" json:"end_port,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeBackConnectServiceCreateBackConnectManagerRequest) Reset() {
	*x = BackofficeBackConnectServiceCreateBackConnectManagerRequest{}
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeBackConnectServiceCreateBackConnectManagerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeBackConnectServiceCreateBackConnectManagerRequest) ProtoMessage() {}

func (x *BackofficeBackConnectServiceCreateBackConnectManagerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeBackConnectServiceCreateBackConnectManagerRequest.ProtoReflect.Descriptor instead.
func (*BackofficeBackConnectServiceCreateBackConnectManagerRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{0}
}

func (x *BackofficeBackConnectServiceCreateBackConnectManagerRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficeBackConnectServiceCreateBackConnectManagerRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeBackConnectServiceCreateBackConnectManagerRequest) GetStartPort() int64 {
	if x != nil {
		return x.StartPort
	}
	return 0
}

func (x *BackofficeBackConnectServiceCreateBackConnectManagerRequest) GetEndPort() int64 {
	if x != nil {
		return x.EndPort
	}
	return 0
}

type BackofficeBackConnectServiceCreateBackConnectManagerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeBackConnectServiceCreateBackConnectManagerResponse) Reset() {
	*x = BackofficeBackConnectServiceCreateBackConnectManagerResponse{}
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeBackConnectServiceCreateBackConnectManagerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeBackConnectServiceCreateBackConnectManagerResponse) ProtoMessage() {}

func (x *BackofficeBackConnectServiceCreateBackConnectManagerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeBackConnectServiceCreateBackConnectManagerResponse.ProtoReflect.Descriptor instead.
func (*BackofficeBackConnectServiceCreateBackConnectManagerResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{1}
}

func (x *BackofficeBackConnectServiceCreateBackConnectManagerResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeBackConnectServiceFetchBackConnectManagerRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdMerchant     string                 `protobuf:"bytes,3,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	IdLocation     string                 `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	IdTelco        string                 `protobuf:"bytes,2,opt,name=id_telco,json=idTelco,proto3" json:"id_telco,omitempty"`
	PublicIpSearch string                 `protobuf:"bytes,4,opt,name=public_ip_search,json=publicIpSearch,proto3" json:"public_ip_search,omitempty"`
	State          *v11.State             `protobuf:"bytes,5,opt,name=state,proto3" json:"state,omitempty"`
	Pagination     *v11.PaginationRequest `protobuf:"bytes,6,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BackofficeBackConnectServiceFetchBackConnectManagerRequest) Reset() {
	*x = BackofficeBackConnectServiceFetchBackConnectManagerRequest{}
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeBackConnectServiceFetchBackConnectManagerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeBackConnectServiceFetchBackConnectManagerRequest) ProtoMessage() {}

func (x *BackofficeBackConnectServiceFetchBackConnectManagerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeBackConnectServiceFetchBackConnectManagerRequest.ProtoReflect.Descriptor instead.
func (*BackofficeBackConnectServiceFetchBackConnectManagerRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{2}
}

func (x *BackofficeBackConnectServiceFetchBackConnectManagerRequest) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficeBackConnectServiceFetchBackConnectManagerRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *BackofficeBackConnectServiceFetchBackConnectManagerRequest) GetIdTelco() string {
	if x != nil {
		return x.IdTelco
	}
	return ""
}

func (x *BackofficeBackConnectServiceFetchBackConnectManagerRequest) GetPublicIpSearch() string {
	if x != nil {
		return x.PublicIpSearch
	}
	return ""
}

func (x *BackofficeBackConnectServiceFetchBackConnectManagerRequest) GetState() *v11.State {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *BackofficeBackConnectServiceFetchBackConnectManagerRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeBackConnectServiceFetchBackConnectManagerResponse struct {
	state               protoimpl.MessageState                            `protogen:"open.v1"`
	BackConnectManagers []*BackofficeBackConnectServiceBackConnectManager `protobuf:"bytes,1,rep,name=back_connect_managers,json=backConnectManagers,proto3" json:"back_connect_managers,omitempty"`
	Pagination          *v11.PaginationResponse                           `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Error               *v1.ErrorMessage                                  `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *BackofficeBackConnectServiceFetchBackConnectManagerResponse) Reset() {
	*x = BackofficeBackConnectServiceFetchBackConnectManagerResponse{}
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeBackConnectServiceFetchBackConnectManagerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeBackConnectServiceFetchBackConnectManagerResponse) ProtoMessage() {}

func (x *BackofficeBackConnectServiceFetchBackConnectManagerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeBackConnectServiceFetchBackConnectManagerResponse.ProtoReflect.Descriptor instead.
func (*BackofficeBackConnectServiceFetchBackConnectManagerResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{3}
}

func (x *BackofficeBackConnectServiceFetchBackConnectManagerResponse) GetBackConnectManagers() []*BackofficeBackConnectServiceBackConnectManager {
	if x != nil {
		return x.BackConnectManagers
	}
	return nil
}

func (x *BackofficeBackConnectServiceFetchBackConnectManagerResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficeBackConnectServiceFetchBackConnectManagerResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeBackConnectServiceBackConnectManager struct {
	state                protoimpl.MessageState                                  `protogen:"open.v1"`
	IdBackConnectManager string                                                  `protobuf:"bytes,1,opt,name=id_back_connect_manager,json=idBackConnectManager,proto3" json:"id_back_connect_manager,omitempty"`
	Name                 string                                                  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IdMerchant           string                                                  `protobuf:"bytes,3,opt,name=id_merchant,json=idMerchant,proto3" json:"id_merchant,omitempty"`
	Location             *BackofficeBackConnectServiceBackConnectManagerLocation `protobuf:"bytes,4,opt,name=location,proto3" json:"location,omitempty"`
	Telco                *BackofficeBackConnectServiceBackConnectManagerTelco    `protobuf:"bytes,5,opt,name=telco,proto3" json:"telco,omitempty"`
	ControlIp            string                                                  `protobuf:"bytes,6,opt,name=control_ip,json=controlIp,proto3" json:"control_ip,omitempty"`
	ControlPort          int64                                                   `protobuf:"varint,7,opt,name=control_port,json=controlPort,proto3" json:"control_port,omitempty"`
	PublicIp             string                                                  `protobuf:"bytes,8,opt,name=public_ip,json=publicIp,proto3" json:"public_ip,omitempty"`
	Domain               string                                                  `protobuf:"bytes,9,opt,name=domain,proto3" json:"domain,omitempty"`
	Status               v12.BackConnectManagerStatus                            `protobuf:"varint,10,opt,name=status,proto3,enum=algoenum.v1.BackConnectManagerStatus" json:"status,omitempty"`
	IsActive             bool                                                    `protobuf:"varint,11,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	StartPort            bool                                                    `protobuf:"varint,12,opt,name=start_port,json=startPort,proto3" json:"start_port,omitempty"`
	EndPort              bool                                                    `protobuf:"varint,13,opt,name=end_port,json=endPort,proto3" json:"end_port,omitempty"`
	State                bool                                                    `protobuf:"varint,14,opt,name=state,proto3" json:"state,omitempty"`
	AesKey               string                                                  `protobuf:"bytes,15,opt,name=aes_key,json=aesKey,proto3" json:"aes_key,omitempty"`
	IvRandom             string                                                  `protobuf:"bytes,16,opt,name=iv_random,json=ivRandom,proto3" json:"iv_random,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *BackofficeBackConnectServiceBackConnectManager) Reset() {
	*x = BackofficeBackConnectServiceBackConnectManager{}
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeBackConnectServiceBackConnectManager) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeBackConnectServiceBackConnectManager) ProtoMessage() {}

func (x *BackofficeBackConnectServiceBackConnectManager) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeBackConnectServiceBackConnectManager.ProtoReflect.Descriptor instead.
func (*BackofficeBackConnectServiceBackConnectManager) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{4}
}

func (x *BackofficeBackConnectServiceBackConnectManager) GetIdBackConnectManager() string {
	if x != nil {
		return x.IdBackConnectManager
	}
	return ""
}

func (x *BackofficeBackConnectServiceBackConnectManager) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeBackConnectServiceBackConnectManager) GetIdMerchant() string {
	if x != nil {
		return x.IdMerchant
	}
	return ""
}

func (x *BackofficeBackConnectServiceBackConnectManager) GetLocation() *BackofficeBackConnectServiceBackConnectManagerLocation {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *BackofficeBackConnectServiceBackConnectManager) GetTelco() *BackofficeBackConnectServiceBackConnectManagerTelco {
	if x != nil {
		return x.Telco
	}
	return nil
}

func (x *BackofficeBackConnectServiceBackConnectManager) GetControlIp() string {
	if x != nil {
		return x.ControlIp
	}
	return ""
}

func (x *BackofficeBackConnectServiceBackConnectManager) GetControlPort() int64 {
	if x != nil {
		return x.ControlPort
	}
	return 0
}

func (x *BackofficeBackConnectServiceBackConnectManager) GetPublicIp() string {
	if x != nil {
		return x.PublicIp
	}
	return ""
}

func (x *BackofficeBackConnectServiceBackConnectManager) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *BackofficeBackConnectServiceBackConnectManager) GetStatus() v12.BackConnectManagerStatus {
	if x != nil {
		return x.Status
	}
	return v12.BackConnectManagerStatus(0)
}

func (x *BackofficeBackConnectServiceBackConnectManager) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *BackofficeBackConnectServiceBackConnectManager) GetStartPort() bool {
	if x != nil {
		return x.StartPort
	}
	return false
}

func (x *BackofficeBackConnectServiceBackConnectManager) GetEndPort() bool {
	if x != nil {
		return x.EndPort
	}
	return false
}

func (x *BackofficeBackConnectServiceBackConnectManager) GetState() bool {
	if x != nil {
		return x.State
	}
	return false
}

func (x *BackofficeBackConnectServiceBackConnectManager) GetAesKey() string {
	if x != nil {
		return x.AesKey
	}
	return ""
}

func (x *BackofficeBackConnectServiceBackConnectManager) GetIvRandom() string {
	if x != nil {
		return x.IvRandom
	}
	return ""
}

type BackofficeBackConnectServiceBackConnectManagerLocation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdLocation    string                 `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	LocationLevel v12.LocationLevel      `protobuf:"varint,2,opt,name=location_level,json=locationLevel,proto3,enum=algoenum.v1.LocationLevel" json:"location_level,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Emoji         string                 `protobuf:"bytes,4,opt,name=emoji,proto3" json:"emoji,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeBackConnectServiceBackConnectManagerLocation) Reset() {
	*x = BackofficeBackConnectServiceBackConnectManagerLocation{}
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeBackConnectServiceBackConnectManagerLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeBackConnectServiceBackConnectManagerLocation) ProtoMessage() {}

func (x *BackofficeBackConnectServiceBackConnectManagerLocation) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeBackConnectServiceBackConnectManagerLocation.ProtoReflect.Descriptor instead.
func (*BackofficeBackConnectServiceBackConnectManagerLocation) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{5}
}

func (x *BackofficeBackConnectServiceBackConnectManagerLocation) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *BackofficeBackConnectServiceBackConnectManagerLocation) GetLocationLevel() v12.LocationLevel {
	if x != nil {
		return x.LocationLevel
	}
	return v12.LocationLevel(0)
}

func (x *BackofficeBackConnectServiceBackConnectManagerLocation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeBackConnectServiceBackConnectManagerLocation) GetEmoji() string {
	if x != nil {
		return x.Emoji
	}
	return ""
}

type BackofficeBackConnectServiceBackConnectManagerTelco struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdTelco       string                 `protobuf:"bytes,1,opt,name=id_telco,json=idTelco,proto3" json:"id_telco,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeBackConnectServiceBackConnectManagerTelco) Reset() {
	*x = BackofficeBackConnectServiceBackConnectManagerTelco{}
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeBackConnectServiceBackConnectManagerTelco) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeBackConnectServiceBackConnectManagerTelco) ProtoMessage() {}

func (x *BackofficeBackConnectServiceBackConnectManagerTelco) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeBackConnectServiceBackConnectManagerTelco.ProtoReflect.Descriptor instead.
func (*BackofficeBackConnectServiceBackConnectManagerTelco) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{6}
}

func (x *BackofficeBackConnectServiceBackConnectManagerTelco) GetIdTelco() string {
	if x != nil {
		return x.IdTelco
	}
	return ""
}

func (x *BackofficeBackConnectServiceBackConnectManagerTelco) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type BackofficeBackConnectServiceUpdateBackConnectManagerRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	IdBackConnectManager string                 `protobuf:"bytes,1,opt,name=id_back_connect_manager,json=idBackConnectManager,proto3" json:"id_back_connect_manager,omitempty"`
	IdLocation           string                 `protobuf:"bytes,2,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	IdTelco              string                 `protobuf:"bytes,3,opt,name=id_telco,json=idTelco,proto3" json:"id_telco,omitempty"`
	Name                 string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	IsChangeAesKey       bool                   `protobuf:"varint,5,opt,name=is_change_aes_key,json=isChangeAesKey,proto3" json:"is_change_aes_key,omitempty"`
	State                *v11.State             `protobuf:"bytes,6,opt,name=state,proto3" json:"state,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *BackofficeBackConnectServiceUpdateBackConnectManagerRequest) Reset() {
	*x = BackofficeBackConnectServiceUpdateBackConnectManagerRequest{}
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeBackConnectServiceUpdateBackConnectManagerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeBackConnectServiceUpdateBackConnectManagerRequest) ProtoMessage() {}

func (x *BackofficeBackConnectServiceUpdateBackConnectManagerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeBackConnectServiceUpdateBackConnectManagerRequest.ProtoReflect.Descriptor instead.
func (*BackofficeBackConnectServiceUpdateBackConnectManagerRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{7}
}

func (x *BackofficeBackConnectServiceUpdateBackConnectManagerRequest) GetIdBackConnectManager() string {
	if x != nil {
		return x.IdBackConnectManager
	}
	return ""
}

func (x *BackofficeBackConnectServiceUpdateBackConnectManagerRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *BackofficeBackConnectServiceUpdateBackConnectManagerRequest) GetIdTelco() string {
	if x != nil {
		return x.IdTelco
	}
	return ""
}

func (x *BackofficeBackConnectServiceUpdateBackConnectManagerRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeBackConnectServiceUpdateBackConnectManagerRequest) GetIsChangeAesKey() bool {
	if x != nil {
		return x.IsChangeAesKey
	}
	return false
}

func (x *BackofficeBackConnectServiceUpdateBackConnectManagerRequest) GetState() *v11.State {
	if x != nil {
		return x.State
	}
	return nil
}

type BackofficeBackConnectServiceUpdateBackConnectManagerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeBackConnectServiceUpdateBackConnectManagerResponse) Reset() {
	*x = BackofficeBackConnectServiceUpdateBackConnectManagerResponse{}
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeBackConnectServiceUpdateBackConnectManagerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeBackConnectServiceUpdateBackConnectManagerResponse) ProtoMessage() {}

func (x *BackofficeBackConnectServiceUpdateBackConnectManagerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeBackConnectServiceUpdateBackConnectManagerResponse.ProtoReflect.Descriptor instead.
func (*BackofficeBackConnectServiceUpdateBackConnectManagerResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{8}
}

func (x *BackofficeBackConnectServiceUpdateBackConnectManagerResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeBackConnectServiceFetchBackConnectPortRequest struct {
	state                protoimpl.MessageState    `protogen:"open.v1"`
	IdBackConnectManager string                    `protobuf:"bytes,1,opt,name=id_back_connect_manager,json=idBackConnectManager,proto3" json:"id_back_connect_manager,omitempty"`
	IdProxyToken         string                    `protobuf:"bytes,2,opt,name=id_proxy_token,json=idProxyToken,proto3" json:"id_proxy_token,omitempty"`
	Status               v12.BackConnectPortStatus `protobuf:"varint,3,opt,name=status,proto3,enum=algoenum.v1.BackConnectPortStatus" json:"status,omitempty"`
	Port                 int64                     `protobuf:"varint,4,opt,name=port,proto3" json:"port,omitempty"`
	Pagination           *v11.PaginationRequest    `protobuf:"bytes,5,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *BackofficeBackConnectServiceFetchBackConnectPortRequest) Reset() {
	*x = BackofficeBackConnectServiceFetchBackConnectPortRequest{}
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeBackConnectServiceFetchBackConnectPortRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeBackConnectServiceFetchBackConnectPortRequest) ProtoMessage() {}

func (x *BackofficeBackConnectServiceFetchBackConnectPortRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeBackConnectServiceFetchBackConnectPortRequest.ProtoReflect.Descriptor instead.
func (*BackofficeBackConnectServiceFetchBackConnectPortRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{9}
}

func (x *BackofficeBackConnectServiceFetchBackConnectPortRequest) GetIdBackConnectManager() string {
	if x != nil {
		return x.IdBackConnectManager
	}
	return ""
}

func (x *BackofficeBackConnectServiceFetchBackConnectPortRequest) GetIdProxyToken() string {
	if x != nil {
		return x.IdProxyToken
	}
	return ""
}

func (x *BackofficeBackConnectServiceFetchBackConnectPortRequest) GetStatus() v12.BackConnectPortStatus {
	if x != nil {
		return x.Status
	}
	return v12.BackConnectPortStatus(0)
}

func (x *BackofficeBackConnectServiceFetchBackConnectPortRequest) GetPort() int64 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *BackofficeBackConnectServiceFetchBackConnectPortRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeBackConnectServiceFetchBackConnectPortResponse struct {
	state            protoimpl.MessageState                         `protogen:"open.v1"`
	Error            *v1.ErrorMessage                               `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination       *v11.PaginationResponse                        `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	BackConnectPorts []*BackofficeBackConnectServiceBackConnectPort `protobuf:"bytes,3,rep,name=back_connect_ports,json=backConnectPorts,proto3" json:"back_connect_ports,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BackofficeBackConnectServiceFetchBackConnectPortResponse) Reset() {
	*x = BackofficeBackConnectServiceFetchBackConnectPortResponse{}
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeBackConnectServiceFetchBackConnectPortResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeBackConnectServiceFetchBackConnectPortResponse) ProtoMessage() {}

func (x *BackofficeBackConnectServiceFetchBackConnectPortResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeBackConnectServiceFetchBackConnectPortResponse.ProtoReflect.Descriptor instead.
func (*BackofficeBackConnectServiceFetchBackConnectPortResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{10}
}

func (x *BackofficeBackConnectServiceFetchBackConnectPortResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeBackConnectServiceFetchBackConnectPortResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficeBackConnectServiceFetchBackConnectPortResponse) GetBackConnectPorts() []*BackofficeBackConnectServiceBackConnectPort {
	if x != nil {
		return x.BackConnectPorts
	}
	return nil
}

type BackofficeBackConnectServiceBackConnectPort struct {
	state             protoimpl.MessageState    `protogen:"open.v1"`
	IdBackConnectPort string                    `protobuf:"bytes,1,opt,name=id_back_connect_port,json=idBackConnectPort,proto3" json:"id_back_connect_port,omitempty"`
	Port              int64                     `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	ProxyProtocol     v12.ProxyProtocol         `protobuf:"varint,3,opt,name=proxy_protocol,json=proxyProtocol,proto3,enum=algoenum.v1.ProxyProtocol" json:"proxy_protocol,omitempty"`
	Status            v12.BackConnectPortStatus `protobuf:"varint,4,opt,name=status,proto3,enum=algoenum.v1.BackConnectPortStatus" json:"status,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BackofficeBackConnectServiceBackConnectPort) Reset() {
	*x = BackofficeBackConnectServiceBackConnectPort{}
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeBackConnectServiceBackConnectPort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeBackConnectServiceBackConnectPort) ProtoMessage() {}

func (x *BackofficeBackConnectServiceBackConnectPort) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_backoffice_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeBackConnectServiceBackConnectPort.ProtoReflect.Descriptor instead.
func (*BackofficeBackConnectServiceBackConnectPort) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{11}
}

func (x *BackofficeBackConnectServiceBackConnectPort) GetIdBackConnectPort() string {
	if x != nil {
		return x.IdBackConnectPort
	}
	return ""
}

func (x *BackofficeBackConnectServiceBackConnectPort) GetPort() int64 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *BackofficeBackConnectServiceBackConnectPort) GetProxyProtocol() v12.ProxyProtocol {
	if x != nil {
		return x.ProxyProtocol
	}
	return v12.ProxyProtocol(0)
}

func (x *BackofficeBackConnectServiceBackConnectPort) GetStatus() v12.BackConnectPortStatus {
	if x != nil {
		return x.Status
	}
	return v12.BackConnectPortStatus(0)
}

var File_proxymanager_backconnect_v1_backoffice_proto protoreflect.FileDescriptor

const file_proxymanager_backconnect_v1_backoffice_proto_rawDesc = "" +
	"\n" +
	",proxymanager/backconnect/v1/backoffice.proto\x12\x1bproxymanager.backconnect.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\x1a-algoenum/v1/back_connect_manager_status.proto\x1a*algoenum/v1/back_connect_port_status.proto\x1a algoenum/v1/location_level.proto\x1a algoenum/v1/proxy_protocol.proto\"\xac\x01\n" +
	";BackofficeBackConnectServiceCreateBackConnectManagerRequest\x12\x1f\n" +
	"\vid_merchant\x18\x01 \x01(\tR\n" +
	"idMerchant\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"start_port\x18\x03 \x01(\x03R\tstartPort\x12\x19\n" +
	"\bend_port\x18\x04 \x01(\x03R\aendPort\"m\n" +
	"<BackofficeBackConnectServiceCreateBackConnectManagerResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xa7\x02\n" +
	":BackofficeBackConnectServiceFetchBackConnectManagerRequest\x12\x1f\n" +
	"\vid_merchant\x18\x03 \x01(\tR\n" +
	"idMerchant\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x12\x19\n" +
	"\bid_telco\x18\x02 \x01(\tR\aidTelco\x12(\n" +
	"\x10public_ip_search\x18\x04 \x01(\tR\x0epublicIpSearch\x12%\n" +
	"\x05state\x18\x05 \x01(\v2\x0f.utils.v1.StateR\x05state\x12;\n" +
	"\n" +
	"pagination\x18\x06 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xab\x02\n" +
	";BackofficeBackConnectServiceFetchBackConnectManagerResponse\x12\x7f\n" +
	"\x15back_connect_managers\x18\x01 \x03(\v2K.proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectManagerR\x13backConnectManagers\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xce\x05\n" +
	".BackofficeBackConnectServiceBackConnectManager\x125\n" +
	"\x17id_back_connect_manager\x18\x01 \x01(\tR\x14idBackConnectManager\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1f\n" +
	"\vid_merchant\x18\x03 \x01(\tR\n" +
	"idMerchant\x12o\n" +
	"\blocation\x18\x04 \x01(\v2S.proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectManagerLocationR\blocation\x12f\n" +
	"\x05telco\x18\x05 \x01(\v2P.proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectManagerTelcoR\x05telco\x12\x1d\n" +
	"\n" +
	"control_ip\x18\x06 \x01(\tR\tcontrolIp\x12!\n" +
	"\fcontrol_port\x18\a \x01(\x03R\vcontrolPort\x12\x1b\n" +
	"\tpublic_ip\x18\b \x01(\tR\bpublicIp\x12\x16\n" +
	"\x06domain\x18\t \x01(\tR\x06domain\x12=\n" +
	"\x06status\x18\n" +
	" \x01(\x0e2%.algoenum.v1.BackConnectManagerStatusR\x06status\x12\x1b\n" +
	"\tis_active\x18\v \x01(\bR\bisActive\x12\x1d\n" +
	"\n" +
	"start_port\x18\f \x01(\bR\tstartPort\x12\x19\n" +
	"\bend_port\x18\r \x01(\bR\aendPort\x12\x14\n" +
	"\x05state\x18\x0e \x01(\bR\x05state\x12\x17\n" +
	"\aaes_key\x18\x0f \x01(\tR\x06aesKey\x12\x1b\n" +
	"\tiv_random\x18\x10 \x01(\tR\bivRandom\"\xc6\x01\n" +
	"6BackofficeBackConnectServiceBackConnectManagerLocation\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x12A\n" +
	"\x0elocation_level\x18\x02 \x01(\x0e2\x1a.algoenum.v1.LocationLevelR\rlocationLevel\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x14\n" +
	"\x05emoji\x18\x04 \x01(\tR\x05emoji\"d\n" +
	"3BackofficeBackConnectServiceBackConnectManagerTelco\x12\x19\n" +
	"\bid_telco\x18\x01 \x01(\tR\aidTelco\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\"\x96\x02\n" +
	";BackofficeBackConnectServiceUpdateBackConnectManagerRequest\x125\n" +
	"\x17id_back_connect_manager\x18\x01 \x01(\tR\x14idBackConnectManager\x12\x1f\n" +
	"\vid_location\x18\x02 \x01(\tR\n" +
	"idLocation\x12\x19\n" +
	"\bid_telco\x18\x03 \x01(\tR\aidTelco\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12)\n" +
	"\x11is_change_aes_key\x18\x05 \x01(\bR\x0eisChangeAesKey\x12%\n" +
	"\x05state\x18\x06 \x01(\v2\x0f.utils.v1.StateR\x05state\"m\n" +
	"<BackofficeBackConnectServiceUpdateBackConnectManagerResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xa3\x02\n" +
	"7BackofficeBackConnectServiceFetchBackConnectPortRequest\x125\n" +
	"\x17id_back_connect_manager\x18\x01 \x01(\tR\x14idBackConnectManager\x12$\n" +
	"\x0eid_proxy_token\x18\x02 \x01(\tR\fidProxyToken\x12:\n" +
	"\x06status\x18\x03 \x01(\x0e2\".algoenum.v1.BackConnectPortStatusR\x06status\x12\x12\n" +
	"\x04port\x18\x04 \x01(\x03R\x04port\x12;\n" +
	"\n" +
	"pagination\x18\x05 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\x9f\x02\n" +
	"8BackofficeBackConnectServiceFetchBackConnectPortResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12v\n" +
	"\x12back_connect_ports\x18\x03 \x03(\v2H.proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectPortR\x10backConnectPorts\"\xf1\x01\n" +
	"+BackofficeBackConnectServiceBackConnectPort\x12/\n" +
	"\x14id_back_connect_port\x18\x01 \x01(\tR\x11idBackConnectPort\x12\x12\n" +
	"\x04port\x18\x02 \x01(\x03R\x04port\x12A\n" +
	"\x0eproxy_protocol\x18\x03 \x01(\x0e2\x1a.algoenum.v1.ProxyProtocolR\rproxyProtocol\x12:\n" +
	"\x06status\x18\x04 \x01(\x0e2\".algoenum.v1.BackConnectPortStatusR\x06status2\xd7\x06\n" +
	"\x1cBackofficeBackConnectService\x12\xcc\x01\n" +
	"\x17FetchBackConnectManager\x12W.proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectManagerRequest\x1aX.proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectManagerResponse\x12\xcf\x01\n" +
	"\x18CreateBackConnectManager\x12X.proxymanager.backconnect.v1.BackofficeBackConnectServiceCreateBackConnectManagerRequest\x1aY.proxymanager.backconnect.v1.BackofficeBackConnectServiceCreateBackConnectManagerResponse\x12\xcf\x01\n" +
	"\x18UpdateBackConnectManager\x12X.proxymanager.backconnect.v1.BackofficeBackConnectServiceUpdateBackConnectManagerRequest\x1aY.proxymanager.backconnect.v1.BackofficeBackConnectServiceUpdateBackConnectManagerResponse\x12\xc3\x01\n" +
	"\x14FetchBackConnectPort\x12T.proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectPortRequest\x1aU.proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectPortResponseBZZXgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/backconnect/v1;backconnectv1b\x06proto3"

var (
	file_proxymanager_backconnect_v1_backoffice_proto_rawDescOnce sync.Once
	file_proxymanager_backconnect_v1_backoffice_proto_rawDescData []byte
)

func file_proxymanager_backconnect_v1_backoffice_proto_rawDescGZIP() []byte {
	file_proxymanager_backconnect_v1_backoffice_proto_rawDescOnce.Do(func() {
		file_proxymanager_backconnect_v1_backoffice_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_backconnect_v1_backoffice_proto_rawDesc), len(file_proxymanager_backconnect_v1_backoffice_proto_rawDesc)))
	})
	return file_proxymanager_backconnect_v1_backoffice_proto_rawDescData
}

var file_proxymanager_backconnect_v1_backoffice_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_proxymanager_backconnect_v1_backoffice_proto_goTypes = []any{
	(*BackofficeBackConnectServiceCreateBackConnectManagerRequest)(nil),  // 0: proxymanager.backconnect.v1.BackofficeBackConnectServiceCreateBackConnectManagerRequest
	(*BackofficeBackConnectServiceCreateBackConnectManagerResponse)(nil), // 1: proxymanager.backconnect.v1.BackofficeBackConnectServiceCreateBackConnectManagerResponse
	(*BackofficeBackConnectServiceFetchBackConnectManagerRequest)(nil),   // 2: proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectManagerRequest
	(*BackofficeBackConnectServiceFetchBackConnectManagerResponse)(nil),  // 3: proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectManagerResponse
	(*BackofficeBackConnectServiceBackConnectManager)(nil),               // 4: proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectManager
	(*BackofficeBackConnectServiceBackConnectManagerLocation)(nil),       // 5: proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectManagerLocation
	(*BackofficeBackConnectServiceBackConnectManagerTelco)(nil),          // 6: proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectManagerTelco
	(*BackofficeBackConnectServiceUpdateBackConnectManagerRequest)(nil),  // 7: proxymanager.backconnect.v1.BackofficeBackConnectServiceUpdateBackConnectManagerRequest
	(*BackofficeBackConnectServiceUpdateBackConnectManagerResponse)(nil), // 8: proxymanager.backconnect.v1.BackofficeBackConnectServiceUpdateBackConnectManagerResponse
	(*BackofficeBackConnectServiceFetchBackConnectPortRequest)(nil),      // 9: proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectPortRequest
	(*BackofficeBackConnectServiceFetchBackConnectPortResponse)(nil),     // 10: proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectPortResponse
	(*BackofficeBackConnectServiceBackConnectPort)(nil),                  // 11: proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectPort
	(*v1.ErrorMessage)(nil),           // 12: errmsg.v1.ErrorMessage
	(*v11.State)(nil),                 // 13: utils.v1.State
	(*v11.PaginationRequest)(nil),     // 14: utils.v1.PaginationRequest
	(*v11.PaginationResponse)(nil),    // 15: utils.v1.PaginationResponse
	(v12.BackConnectManagerStatus)(0), // 16: algoenum.v1.BackConnectManagerStatus
	(v12.LocationLevel)(0),            // 17: algoenum.v1.LocationLevel
	(v12.BackConnectPortStatus)(0),    // 18: algoenum.v1.BackConnectPortStatus
	(v12.ProxyProtocol)(0),            // 19: algoenum.v1.ProxyProtocol
}
var file_proxymanager_backconnect_v1_backoffice_proto_depIdxs = []int32{
	12, // 0: proxymanager.backconnect.v1.BackofficeBackConnectServiceCreateBackConnectManagerResponse.error:type_name -> errmsg.v1.ErrorMessage
	13, // 1: proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectManagerRequest.state:type_name -> utils.v1.State
	14, // 2: proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectManagerRequest.pagination:type_name -> utils.v1.PaginationRequest
	4,  // 3: proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectManagerResponse.back_connect_managers:type_name -> proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectManager
	15, // 4: proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectManagerResponse.pagination:type_name -> utils.v1.PaginationResponse
	12, // 5: proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectManagerResponse.error:type_name -> errmsg.v1.ErrorMessage
	5,  // 6: proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectManager.location:type_name -> proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectManagerLocation
	6,  // 7: proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectManager.telco:type_name -> proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectManagerTelco
	16, // 8: proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectManager.status:type_name -> algoenum.v1.BackConnectManagerStatus
	17, // 9: proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectManagerLocation.location_level:type_name -> algoenum.v1.LocationLevel
	13, // 10: proxymanager.backconnect.v1.BackofficeBackConnectServiceUpdateBackConnectManagerRequest.state:type_name -> utils.v1.State
	12, // 11: proxymanager.backconnect.v1.BackofficeBackConnectServiceUpdateBackConnectManagerResponse.error:type_name -> errmsg.v1.ErrorMessage
	18, // 12: proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectPortRequest.status:type_name -> algoenum.v1.BackConnectPortStatus
	14, // 13: proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectPortRequest.pagination:type_name -> utils.v1.PaginationRequest
	12, // 14: proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectPortResponse.error:type_name -> errmsg.v1.ErrorMessage
	15, // 15: proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectPortResponse.pagination:type_name -> utils.v1.PaginationResponse
	11, // 16: proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectPortResponse.back_connect_ports:type_name -> proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectPort
	19, // 17: proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectPort.proxy_protocol:type_name -> algoenum.v1.ProxyProtocol
	18, // 18: proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectPort.status:type_name -> algoenum.v1.BackConnectPortStatus
	2,  // 19: proxymanager.backconnect.v1.BackofficeBackConnectService.FetchBackConnectManager:input_type -> proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectManagerRequest
	0,  // 20: proxymanager.backconnect.v1.BackofficeBackConnectService.CreateBackConnectManager:input_type -> proxymanager.backconnect.v1.BackofficeBackConnectServiceCreateBackConnectManagerRequest
	7,  // 21: proxymanager.backconnect.v1.BackofficeBackConnectService.UpdateBackConnectManager:input_type -> proxymanager.backconnect.v1.BackofficeBackConnectServiceUpdateBackConnectManagerRequest
	9,  // 22: proxymanager.backconnect.v1.BackofficeBackConnectService.FetchBackConnectPort:input_type -> proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectPortRequest
	3,  // 23: proxymanager.backconnect.v1.BackofficeBackConnectService.FetchBackConnectManager:output_type -> proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectManagerResponse
	1,  // 24: proxymanager.backconnect.v1.BackofficeBackConnectService.CreateBackConnectManager:output_type -> proxymanager.backconnect.v1.BackofficeBackConnectServiceCreateBackConnectManagerResponse
	8,  // 25: proxymanager.backconnect.v1.BackofficeBackConnectService.UpdateBackConnectManager:output_type -> proxymanager.backconnect.v1.BackofficeBackConnectServiceUpdateBackConnectManagerResponse
	10, // 26: proxymanager.backconnect.v1.BackofficeBackConnectService.FetchBackConnectPort:output_type -> proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectPortResponse
	23, // [23:27] is the sub-list for method output_type
	19, // [19:23] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_proxymanager_backconnect_v1_backoffice_proto_init() }
func file_proxymanager_backconnect_v1_backoffice_proto_init() {
	if File_proxymanager_backconnect_v1_backoffice_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_backconnect_v1_backoffice_proto_rawDesc), len(file_proxymanager_backconnect_v1_backoffice_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_backconnect_v1_backoffice_proto_goTypes,
		DependencyIndexes: file_proxymanager_backconnect_v1_backoffice_proto_depIdxs,
		MessageInfos:      file_proxymanager_backconnect_v1_backoffice_proto_msgTypes,
	}.Build()
	File_proxymanager_backconnect_v1_backoffice_proto = out.File
	file_proxymanager_backconnect_v1_backoffice_proto_goTypes = nil
	file_proxymanager_backconnect_v1_backoffice_proto_depIdxs = nil
}
