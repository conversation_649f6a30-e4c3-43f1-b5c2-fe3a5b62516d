// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/backconnect/v1/customer.proto

package backconnectv1

import (
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdPlan        string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest) Reset() {
	*x = CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest{}
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest) ProtoMessage() {}

func (x *CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest.ProtoReflect.Descriptor instead.
func (*CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_customer_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	Error         *v11.ErrorMessage              `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Locations     []*CustomerBackConnectLocation `protobuf:"bytes,2,rep,name=locations,proto3" json:"locations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse) Reset() {
	*x = CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse{}
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse) ProtoMessage() {}

func (x *CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse.ProtoReflect.Descriptor instead.
func (*CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_customer_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse) GetLocations() []*CustomerBackConnectLocation {
	if x != nil {
		return x.Locations
	}
	return nil
}

type CustomerBackConnectLocation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdLocation    string                 `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Level         v12.LocationLevel      `protobuf:"varint,3,opt,name=level,proto3,enum=algoenum.v1.LocationLevel" json:"level,omitempty"`
	Emoji         string                 `protobuf:"bytes,4,opt,name=emoji,proto3" json:"emoji,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerBackConnectLocation) Reset() {
	*x = CustomerBackConnectLocation{}
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerBackConnectLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerBackConnectLocation) ProtoMessage() {}

func (x *CustomerBackConnectLocation) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerBackConnectLocation.ProtoReflect.Descriptor instead.
func (*CustomerBackConnectLocation) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_customer_proto_rawDescGZIP(), []int{2}
}

func (x *CustomerBackConnectLocation) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *CustomerBackConnectLocation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerBackConnectLocation) GetLevel() v12.LocationLevel {
	if x != nil {
		return x.Level
	}
	return v12.LocationLevel(0)
}

func (x *CustomerBackConnectLocation) GetEmoji() string {
	if x != nil {
		return x.Emoji
	}
	return ""
}

type CustomerBackConnectServiceCreateBackConnectManagerRequest struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	IdLocation string                 `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	// string id_telco = 2;
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	StartPort     int64  `protobuf:"varint,4,opt,name=start_port,json=startPort,proto3" json:"start_port,omitempty"`
	EndPort       int64  `protobuf:"varint,5,opt,name=end_port,json=endPort,proto3" json:"end_port,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerBackConnectServiceCreateBackConnectManagerRequest) Reset() {
	*x = CustomerBackConnectServiceCreateBackConnectManagerRequest{}
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerBackConnectServiceCreateBackConnectManagerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerBackConnectServiceCreateBackConnectManagerRequest) ProtoMessage() {}

func (x *CustomerBackConnectServiceCreateBackConnectManagerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerBackConnectServiceCreateBackConnectManagerRequest.ProtoReflect.Descriptor instead.
func (*CustomerBackConnectServiceCreateBackConnectManagerRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_customer_proto_rawDescGZIP(), []int{3}
}

func (x *CustomerBackConnectServiceCreateBackConnectManagerRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *CustomerBackConnectServiceCreateBackConnectManagerRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerBackConnectServiceCreateBackConnectManagerRequest) GetStartPort() int64 {
	if x != nil {
		return x.StartPort
	}
	return 0
}

func (x *CustomerBackConnectServiceCreateBackConnectManagerRequest) GetEndPort() int64 {
	if x != nil {
		return x.EndPort
	}
	return 0
}

type CustomerBackConnectServiceCreateBackConnectManagerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerBackConnectServiceCreateBackConnectManagerResponse) Reset() {
	*x = CustomerBackConnectServiceCreateBackConnectManagerResponse{}
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerBackConnectServiceCreateBackConnectManagerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerBackConnectServiceCreateBackConnectManagerResponse) ProtoMessage() {}

func (x *CustomerBackConnectServiceCreateBackConnectManagerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerBackConnectServiceCreateBackConnectManagerResponse.ProtoReflect.Descriptor instead.
func (*CustomerBackConnectServiceCreateBackConnectManagerResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_customer_proto_rawDescGZIP(), []int{4}
}

func (x *CustomerBackConnectServiceCreateBackConnectManagerResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerBackConnectServiceFetchAvailableBackConnectRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	IdPlan         string                 `protobuf:"bytes,1,opt,name=id_plan,json=idPlan,proto3" json:"id_plan,omitempty"`
	IdLocation     string                 `protobuf:"bytes,2,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	PublicIpSearch string                 `protobuf:"bytes,3,opt,name=public_ip_search,json=publicIpSearch,proto3" json:"public_ip_search,omitempty"`
	Pagination     *v1.PaginationRequest  `protobuf:"bytes,4,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CustomerBackConnectServiceFetchAvailableBackConnectRequest) Reset() {
	*x = CustomerBackConnectServiceFetchAvailableBackConnectRequest{}
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerBackConnectServiceFetchAvailableBackConnectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerBackConnectServiceFetchAvailableBackConnectRequest) ProtoMessage() {}

func (x *CustomerBackConnectServiceFetchAvailableBackConnectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerBackConnectServiceFetchAvailableBackConnectRequest.ProtoReflect.Descriptor instead.
func (*CustomerBackConnectServiceFetchAvailableBackConnectRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_customer_proto_rawDescGZIP(), []int{5}
}

func (x *CustomerBackConnectServiceFetchAvailableBackConnectRequest) GetIdPlan() string {
	if x != nil {
		return x.IdPlan
	}
	return ""
}

func (x *CustomerBackConnectServiceFetchAvailableBackConnectRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *CustomerBackConnectServiceFetchAvailableBackConnectRequest) GetPublicIpSearch() string {
	if x != nil {
		return x.PublicIpSearch
	}
	return ""
}

func (x *CustomerBackConnectServiceFetchAvailableBackConnectRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerBackConnectServiceFetchAvailableBackConnectResponse struct {
	state               protoimpl.MessageState                          `protogen:"open.v1"`
	BackConnectManagers []*CustomerBackConnectServiceBackConnectManager `protobuf:"bytes,1,rep,name=back_connect_managers,json=backConnectManagers,proto3" json:"back_connect_managers,omitempty"`
	Pagination          *v1.PaginationResponse                          `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Error               *v11.ErrorMessage                               `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *CustomerBackConnectServiceFetchAvailableBackConnectResponse) Reset() {
	*x = CustomerBackConnectServiceFetchAvailableBackConnectResponse{}
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerBackConnectServiceFetchAvailableBackConnectResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerBackConnectServiceFetchAvailableBackConnectResponse) ProtoMessage() {}

func (x *CustomerBackConnectServiceFetchAvailableBackConnectResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerBackConnectServiceFetchAvailableBackConnectResponse.ProtoReflect.Descriptor instead.
func (*CustomerBackConnectServiceFetchAvailableBackConnectResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_customer_proto_rawDescGZIP(), []int{6}
}

func (x *CustomerBackConnectServiceFetchAvailableBackConnectResponse) GetBackConnectManagers() []*CustomerBackConnectServiceBackConnectManager {
	if x != nil {
		return x.BackConnectManagers
	}
	return nil
}

func (x *CustomerBackConnectServiceFetchAvailableBackConnectResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *CustomerBackConnectServiceFetchAvailableBackConnectResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type CustomerBackConnectServiceBackConnectManager struct {
	state                protoimpl.MessageState                                `protogen:"open.v1"`
	IdBackConnectManager string                                                `protobuf:"bytes,1,opt,name=id_back_connect_manager,json=idBackConnectManager,proto3" json:"id_back_connect_manager,omitempty"`
	Name                 string                                                `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Location             *CustomerBackConnectServiceBackConnectManagerLocation `protobuf:"bytes,3,opt,name=location,proto3" json:"location,omitempty"`
	Telco                *CustomerBackConnectServiceBackConnectManagerTelco    `protobuf:"bytes,4,opt,name=telco,proto3" json:"telco,omitempty"`
	PublicIp             string                                                `protobuf:"bytes,5,opt,name=public_ip,json=publicIp,proto3" json:"public_ip,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *CustomerBackConnectServiceBackConnectManager) Reset() {
	*x = CustomerBackConnectServiceBackConnectManager{}
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerBackConnectServiceBackConnectManager) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerBackConnectServiceBackConnectManager) ProtoMessage() {}

func (x *CustomerBackConnectServiceBackConnectManager) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerBackConnectServiceBackConnectManager.ProtoReflect.Descriptor instead.
func (*CustomerBackConnectServiceBackConnectManager) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_customer_proto_rawDescGZIP(), []int{7}
}

func (x *CustomerBackConnectServiceBackConnectManager) GetIdBackConnectManager() string {
	if x != nil {
		return x.IdBackConnectManager
	}
	return ""
}

func (x *CustomerBackConnectServiceBackConnectManager) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerBackConnectServiceBackConnectManager) GetLocation() *CustomerBackConnectServiceBackConnectManagerLocation {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *CustomerBackConnectServiceBackConnectManager) GetTelco() *CustomerBackConnectServiceBackConnectManagerTelco {
	if x != nil {
		return x.Telco
	}
	return nil
}

func (x *CustomerBackConnectServiceBackConnectManager) GetPublicIp() string {
	if x != nil {
		return x.PublicIp
	}
	return ""
}

type CustomerBackConnectServiceBackConnectManagerLocation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdLocation    string                 `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	LocationLevel v12.LocationLevel      `protobuf:"varint,2,opt,name=location_level,json=locationLevel,proto3,enum=algoenum.v1.LocationLevel" json:"location_level,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	IsActive      bool                   `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerBackConnectServiceBackConnectManagerLocation) Reset() {
	*x = CustomerBackConnectServiceBackConnectManagerLocation{}
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerBackConnectServiceBackConnectManagerLocation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerBackConnectServiceBackConnectManagerLocation) ProtoMessage() {}

func (x *CustomerBackConnectServiceBackConnectManagerLocation) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerBackConnectServiceBackConnectManagerLocation.ProtoReflect.Descriptor instead.
func (*CustomerBackConnectServiceBackConnectManagerLocation) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_customer_proto_rawDescGZIP(), []int{8}
}

func (x *CustomerBackConnectServiceBackConnectManagerLocation) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *CustomerBackConnectServiceBackConnectManagerLocation) GetLocationLevel() v12.LocationLevel {
	if x != nil {
		return x.LocationLevel
	}
	return v12.LocationLevel(0)
}

func (x *CustomerBackConnectServiceBackConnectManagerLocation) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerBackConnectServiceBackConnectManagerLocation) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

type CustomerBackConnectServiceBackConnectManagerTelco struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdTelco       string                 `protobuf:"bytes,1,opt,name=id_telco,json=idTelco,proto3" json:"id_telco,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerBackConnectServiceBackConnectManagerTelco) Reset() {
	*x = CustomerBackConnectServiceBackConnectManagerTelco{}
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerBackConnectServiceBackConnectManagerTelco) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerBackConnectServiceBackConnectManagerTelco) ProtoMessage() {}

func (x *CustomerBackConnectServiceBackConnectManagerTelco) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_backconnect_v1_customer_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerBackConnectServiceBackConnectManagerTelco.ProtoReflect.Descriptor instead.
func (*CustomerBackConnectServiceBackConnectManagerTelco) Descriptor() ([]byte, []int) {
	return file_proxymanager_backconnect_v1_customer_proto_rawDescGZIP(), []int{9}
}

func (x *CustomerBackConnectServiceBackConnectManagerTelco) GetIdTelco() string {
	if x != nil {
		return x.IdTelco
	}
	return ""
}

func (x *CustomerBackConnectServiceBackConnectManagerTelco) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_proxymanager_backconnect_v1_customer_proto protoreflect.FileDescriptor

const file_proxymanager_backconnect_v1_customer_proto_rawDesc = "" +
	"\n" +
	"*proxymanager/backconnect/v1/customer.proto\x12\x1bproxymanager.backconnect.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\x1a algoenum/v1/location_level.proto\"\x9c\x01\n" +
	"DCustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12;\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xce\x01\n" +
	"ECustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12V\n" +
	"\tlocations\x18\x02 \x03(\v28.proxymanager.backconnect.v1.CustomerBackConnectLocationR\tlocations\"\x9a\x01\n" +
	"\x1bCustomerBackConnectLocation\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x120\n" +
	"\x05level\x18\x03 \x01(\x0e2\x1a.algoenum.v1.LocationLevelR\x05level\x12\x14\n" +
	"\x05emoji\x18\x04 \x01(\tR\x05emoji\"\xaa\x01\n" +
	"9CustomerBackConnectServiceCreateBackConnectManagerRequest\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"start_port\x18\x04 \x01(\x03R\tstartPort\x12\x19\n" +
	"\bend_port\x18\x05 \x01(\x03R\aendPort\"k\n" +
	":CustomerBackConnectServiceCreateBackConnectManagerResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xdd\x01\n" +
	":CustomerBackConnectServiceFetchAvailableBackConnectRequest\x12\x17\n" +
	"\aid_plan\x18\x01 \x01(\tR\x06idPlan\x12\x1f\n" +
	"\vid_location\x18\x02 \x01(\tR\n" +
	"idLocation\x12(\n" +
	"\x10public_ip_search\x18\x03 \x01(\tR\x0epublicIpSearch\x12;\n" +
	"\n" +
	"pagination\x18\x04 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xa9\x02\n" +
	";CustomerBackConnectServiceFetchAvailableBackConnectResponse\x12}\n" +
	"\x15back_connect_managers\x18\x01 \x03(\v2I.proxymanager.backconnect.v1.CustomerBackConnectServiceBackConnectManagerR\x13backConnectManagers\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xeb\x02\n" +
	",CustomerBackConnectServiceBackConnectManager\x125\n" +
	"\x17id_back_connect_manager\x18\x01 \x01(\tR\x14idBackConnectManager\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12m\n" +
	"\blocation\x18\x03 \x01(\v2Q.proxymanager.backconnect.v1.CustomerBackConnectServiceBackConnectManagerLocationR\blocation\x12d\n" +
	"\x05telco\x18\x04 \x01(\v2N.proxymanager.backconnect.v1.CustomerBackConnectServiceBackConnectManagerTelcoR\x05telco\x12\x1b\n" +
	"\tpublic_ip\x18\x05 \x01(\tR\bpublicIp\"\xcb\x01\n" +
	"4CustomerBackConnectServiceBackConnectManagerLocation\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x12A\n" +
	"\x0elocation_level\x18\x02 \x01(\x0e2\x1a.algoenum.v1.LocationLevelR\rlocationLevel\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x1b\n" +
	"\tis_active\x18\x04 \x01(\bR\bisActive\"b\n" +
	"1CustomerBackConnectServiceBackConnectManagerTelco\x12\x19\n" +
	"\bid_telco\x18\x01 \x01(\tR\aidTelco\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name2\x8b\x02\n" +
	"\x1aCustomerBackConnectService\x12\xec\x01\n" +
	"#FetchAvailableLocationOfBackConnect\x12a.proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest\x1ab.proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponseBZZXgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/backconnect/v1;backconnectv1b\x06proto3"

var (
	file_proxymanager_backconnect_v1_customer_proto_rawDescOnce sync.Once
	file_proxymanager_backconnect_v1_customer_proto_rawDescData []byte
)

func file_proxymanager_backconnect_v1_customer_proto_rawDescGZIP() []byte {
	file_proxymanager_backconnect_v1_customer_proto_rawDescOnce.Do(func() {
		file_proxymanager_backconnect_v1_customer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_backconnect_v1_customer_proto_rawDesc), len(file_proxymanager_backconnect_v1_customer_proto_rawDesc)))
	})
	return file_proxymanager_backconnect_v1_customer_proto_rawDescData
}

var file_proxymanager_backconnect_v1_customer_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_proxymanager_backconnect_v1_customer_proto_goTypes = []any{
	(*CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest)(nil),  // 0: proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest
	(*CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse)(nil), // 1: proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse
	(*CustomerBackConnectLocation)(nil),                                           // 2: proxymanager.backconnect.v1.CustomerBackConnectLocation
	(*CustomerBackConnectServiceCreateBackConnectManagerRequest)(nil),             // 3: proxymanager.backconnect.v1.CustomerBackConnectServiceCreateBackConnectManagerRequest
	(*CustomerBackConnectServiceCreateBackConnectManagerResponse)(nil),            // 4: proxymanager.backconnect.v1.CustomerBackConnectServiceCreateBackConnectManagerResponse
	(*CustomerBackConnectServiceFetchAvailableBackConnectRequest)(nil),            // 5: proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableBackConnectRequest
	(*CustomerBackConnectServiceFetchAvailableBackConnectResponse)(nil),           // 6: proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableBackConnectResponse
	(*CustomerBackConnectServiceBackConnectManager)(nil),                          // 7: proxymanager.backconnect.v1.CustomerBackConnectServiceBackConnectManager
	(*CustomerBackConnectServiceBackConnectManagerLocation)(nil),                  // 8: proxymanager.backconnect.v1.CustomerBackConnectServiceBackConnectManagerLocation
	(*CustomerBackConnectServiceBackConnectManagerTelco)(nil),                     // 9: proxymanager.backconnect.v1.CustomerBackConnectServiceBackConnectManagerTelco
	(*v1.PaginationRequest)(nil),                                                  // 10: utils.v1.PaginationRequest
	(*v11.ErrorMessage)(nil),                                                      // 11: errmsg.v1.ErrorMessage
	(v12.LocationLevel)(0),                                                        // 12: algoenum.v1.LocationLevel
	(*v1.PaginationResponse)(nil),                                                 // 13: utils.v1.PaginationResponse
}
var file_proxymanager_backconnect_v1_customer_proto_depIdxs = []int32{
	10, // 0: proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest.pagination:type_name -> utils.v1.PaginationRequest
	11, // 1: proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse.error:type_name -> errmsg.v1.ErrorMessage
	2,  // 2: proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse.locations:type_name -> proxymanager.backconnect.v1.CustomerBackConnectLocation
	12, // 3: proxymanager.backconnect.v1.CustomerBackConnectLocation.level:type_name -> algoenum.v1.LocationLevel
	11, // 4: proxymanager.backconnect.v1.CustomerBackConnectServiceCreateBackConnectManagerResponse.error:type_name -> errmsg.v1.ErrorMessage
	10, // 5: proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableBackConnectRequest.pagination:type_name -> utils.v1.PaginationRequest
	7,  // 6: proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableBackConnectResponse.back_connect_managers:type_name -> proxymanager.backconnect.v1.CustomerBackConnectServiceBackConnectManager
	13, // 7: proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableBackConnectResponse.pagination:type_name -> utils.v1.PaginationResponse
	11, // 8: proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableBackConnectResponse.error:type_name -> errmsg.v1.ErrorMessage
	8,  // 9: proxymanager.backconnect.v1.CustomerBackConnectServiceBackConnectManager.location:type_name -> proxymanager.backconnect.v1.CustomerBackConnectServiceBackConnectManagerLocation
	9,  // 10: proxymanager.backconnect.v1.CustomerBackConnectServiceBackConnectManager.telco:type_name -> proxymanager.backconnect.v1.CustomerBackConnectServiceBackConnectManagerTelco
	12, // 11: proxymanager.backconnect.v1.CustomerBackConnectServiceBackConnectManagerLocation.location_level:type_name -> algoenum.v1.LocationLevel
	0,  // 12: proxymanager.backconnect.v1.CustomerBackConnectService.FetchAvailableLocationOfBackConnect:input_type -> proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest
	1,  // 13: proxymanager.backconnect.v1.CustomerBackConnectService.FetchAvailableLocationOfBackConnect:output_type -> proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse
	13, // [13:14] is the sub-list for method output_type
	12, // [12:13] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_proxymanager_backconnect_v1_customer_proto_init() }
func file_proxymanager_backconnect_v1_customer_proto_init() {
	if File_proxymanager_backconnect_v1_customer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_backconnect_v1_customer_proto_rawDesc), len(file_proxymanager_backconnect_v1_customer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_backconnect_v1_customer_proto_goTypes,
		DependencyIndexes: file_proxymanager_backconnect_v1_customer_proto_depIdxs,
		MessageInfos:      file_proxymanager_backconnect_v1_customer_proto_msgTypes,
	}.Build()
	File_proxymanager_backconnect_v1_customer_proto = out.File
	file_proxymanager_backconnect_v1_customer_proto_goTypes = nil
	file_proxymanager_backconnect_v1_customer_proto_depIdxs = nil
}
