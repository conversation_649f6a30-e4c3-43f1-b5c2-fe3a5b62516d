// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/location/v1/backoffice.proto

package locationv1

import (
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackofficeLocationServiceReloadCacheRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeLocationServiceReloadCacheRequest) Reset() {
	*x = BackofficeLocationServiceReloadCacheRequest{}
	mi := &file_proxymanager_location_v1_backoffice_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeLocationServiceReloadCacheRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeLocationServiceReloadCacheRequest) ProtoMessage() {}

func (x *BackofficeLocationServiceReloadCacheRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_location_v1_backoffice_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeLocationServiceReloadCacheRequest.ProtoReflect.Descriptor instead.
func (*BackofficeLocationServiceReloadCacheRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_location_v1_backoffice_proto_rawDescGZIP(), []int{0}
}

type BackofficeLocationServiceReloadCacheResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeLocationServiceReloadCacheResponse) Reset() {
	*x = BackofficeLocationServiceReloadCacheResponse{}
	mi := &file_proxymanager_location_v1_backoffice_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeLocationServiceReloadCacheResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeLocationServiceReloadCacheResponse) ProtoMessage() {}

func (x *BackofficeLocationServiceReloadCacheResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_location_v1_backoffice_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeLocationServiceReloadCacheResponse.ProtoReflect.Descriptor instead.
func (*BackofficeLocationServiceReloadCacheResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_location_v1_backoffice_proto_rawDescGZIP(), []int{1}
}

func (x *BackofficeLocationServiceReloadCacheResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeLocationServiceFetchLocationRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	IdLocation       string                 `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	IdLocations      []string               `protobuf:"bytes,2,rep,name=id_locations,json=idLocations,proto3" json:"id_locations,omitempty"`
	IdParentLocation string                 `protobuf:"bytes,3,opt,name=id_parent_location,json=idParentLocation,proto3" json:"id_parent_location,omitempty"`
	LocationLevel    v11.LocationLevel      `protobuf:"varint,4,opt,name=location_level,json=locationLevel,proto3,enum=algoenum.v1.LocationLevel" json:"location_level,omitempty"`
	NameSearch       string                 `protobuf:"bytes,5,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	IsoCodeSearch    string                 `protobuf:"bytes,6,opt,name=iso_code_search,json=isoCodeSearch,proto3" json:"iso_code_search,omitempty"`
	Pagination       *v12.PaginationRequest `protobuf:"bytes,7,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BackofficeLocationServiceFetchLocationRequest) Reset() {
	*x = BackofficeLocationServiceFetchLocationRequest{}
	mi := &file_proxymanager_location_v1_backoffice_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeLocationServiceFetchLocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeLocationServiceFetchLocationRequest) ProtoMessage() {}

func (x *BackofficeLocationServiceFetchLocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_location_v1_backoffice_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeLocationServiceFetchLocationRequest.ProtoReflect.Descriptor instead.
func (*BackofficeLocationServiceFetchLocationRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_location_v1_backoffice_proto_rawDescGZIP(), []int{2}
}

func (x *BackofficeLocationServiceFetchLocationRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *BackofficeLocationServiceFetchLocationRequest) GetIdLocations() []string {
	if x != nil {
		return x.IdLocations
	}
	return nil
}

func (x *BackofficeLocationServiceFetchLocationRequest) GetIdParentLocation() string {
	if x != nil {
		return x.IdParentLocation
	}
	return ""
}

func (x *BackofficeLocationServiceFetchLocationRequest) GetLocationLevel() v11.LocationLevel {
	if x != nil {
		return x.LocationLevel
	}
	return v11.LocationLevel(0)
}

func (x *BackofficeLocationServiceFetchLocationRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *BackofficeLocationServiceFetchLocationRequest) GetIsoCodeSearch() string {
	if x != nil {
		return x.IsoCodeSearch
	}
	return ""
}

func (x *BackofficeLocationServiceFetchLocationRequest) GetPagination() *v12.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackofficeLocationServiceFetchLocationResponse struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Error         *v1.ErrorMessage           `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v12.PaginationResponse    `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Locations     []*BackofficeLocationModel `protobuf:"bytes,3,rep,name=locations,proto3" json:"locations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeLocationServiceFetchLocationResponse) Reset() {
	*x = BackofficeLocationServiceFetchLocationResponse{}
	mi := &file_proxymanager_location_v1_backoffice_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeLocationServiceFetchLocationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeLocationServiceFetchLocationResponse) ProtoMessage() {}

func (x *BackofficeLocationServiceFetchLocationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_location_v1_backoffice_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeLocationServiceFetchLocationResponse.ProtoReflect.Descriptor instead.
func (*BackofficeLocationServiceFetchLocationResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_location_v1_backoffice_proto_rawDescGZIP(), []int{3}
}

func (x *BackofficeLocationServiceFetchLocationResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackofficeLocationServiceFetchLocationResponse) GetPagination() *v12.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackofficeLocationServiceFetchLocationResponse) GetLocations() []*BackofficeLocationModel {
	if x != nil {
		return x.Locations
	}
	return nil
}

type BackofficeLocationServiceCreateLocationRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	IdParentLocation string                 `protobuf:"bytes,1,opt,name=id_parent_location,json=idParentLocation,proto3" json:"id_parent_location,omitempty"`
	LocationLevel    v11.LocationLevel      `protobuf:"varint,2,opt,name=location_level,json=locationLevel,proto3,enum=algoenum.v1.LocationLevel" json:"location_level,omitempty"`
	Name             string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	IsoCode          string                 `protobuf:"bytes,4,opt,name=iso_code,json=isoCode,proto3" json:"iso_code,omitempty"`
	Emoji            string                 `protobuf:"bytes,5,opt,name=emoji,proto3" json:"emoji,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BackofficeLocationServiceCreateLocationRequest) Reset() {
	*x = BackofficeLocationServiceCreateLocationRequest{}
	mi := &file_proxymanager_location_v1_backoffice_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeLocationServiceCreateLocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeLocationServiceCreateLocationRequest) ProtoMessage() {}

func (x *BackofficeLocationServiceCreateLocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_location_v1_backoffice_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeLocationServiceCreateLocationRequest.ProtoReflect.Descriptor instead.
func (*BackofficeLocationServiceCreateLocationRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_location_v1_backoffice_proto_rawDescGZIP(), []int{4}
}

func (x *BackofficeLocationServiceCreateLocationRequest) GetIdParentLocation() string {
	if x != nil {
		return x.IdParentLocation
	}
	return ""
}

func (x *BackofficeLocationServiceCreateLocationRequest) GetLocationLevel() v11.LocationLevel {
	if x != nil {
		return x.LocationLevel
	}
	return v11.LocationLevel(0)
}

func (x *BackofficeLocationServiceCreateLocationRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeLocationServiceCreateLocationRequest) GetIsoCode() string {
	if x != nil {
		return x.IsoCode
	}
	return ""
}

func (x *BackofficeLocationServiceCreateLocationRequest) GetEmoji() string {
	if x != nil {
		return x.Emoji
	}
	return ""
}

type BackofficeLocationServiceCreateLocationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeLocationServiceCreateLocationResponse) Reset() {
	*x = BackofficeLocationServiceCreateLocationResponse{}
	mi := &file_proxymanager_location_v1_backoffice_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeLocationServiceCreateLocationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeLocationServiceCreateLocationResponse) ProtoMessage() {}

func (x *BackofficeLocationServiceCreateLocationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_location_v1_backoffice_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeLocationServiceCreateLocationResponse.ProtoReflect.Descriptor instead.
func (*BackofficeLocationServiceCreateLocationResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_location_v1_backoffice_proto_rawDescGZIP(), []int{5}
}

func (x *BackofficeLocationServiceCreateLocationResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeLocationServiceUpdateLocationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdLocation    string                 `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	LocationLevel v11.LocationLevel      `protobuf:"varint,2,opt,name=location_level,json=locationLevel,proto3,enum=algoenum.v1.LocationLevel" json:"location_level,omitempty"`
	Emoji         string                 `protobuf:"bytes,3,opt,name=emoji,proto3" json:"emoji,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeLocationServiceUpdateLocationRequest) Reset() {
	*x = BackofficeLocationServiceUpdateLocationRequest{}
	mi := &file_proxymanager_location_v1_backoffice_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeLocationServiceUpdateLocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeLocationServiceUpdateLocationRequest) ProtoMessage() {}

func (x *BackofficeLocationServiceUpdateLocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_location_v1_backoffice_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeLocationServiceUpdateLocationRequest.ProtoReflect.Descriptor instead.
func (*BackofficeLocationServiceUpdateLocationRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_location_v1_backoffice_proto_rawDescGZIP(), []int{6}
}

func (x *BackofficeLocationServiceUpdateLocationRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *BackofficeLocationServiceUpdateLocationRequest) GetLocationLevel() v11.LocationLevel {
	if x != nil {
		return x.LocationLevel
	}
	return v11.LocationLevel(0)
}

func (x *BackofficeLocationServiceUpdateLocationRequest) GetEmoji() string {
	if x != nil {
		return x.Emoji
	}
	return ""
}

type BackofficeLocationServiceUpdateLocationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeLocationServiceUpdateLocationResponse) Reset() {
	*x = BackofficeLocationServiceUpdateLocationResponse{}
	mi := &file_proxymanager_location_v1_backoffice_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeLocationServiceUpdateLocationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeLocationServiceUpdateLocationResponse) ProtoMessage() {}

func (x *BackofficeLocationServiceUpdateLocationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_location_v1_backoffice_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeLocationServiceUpdateLocationResponse.ProtoReflect.Descriptor instead.
func (*BackofficeLocationServiceUpdateLocationResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_location_v1_backoffice_proto_rawDescGZIP(), []int{7}
}

func (x *BackofficeLocationServiceUpdateLocationResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackofficeLocationModel struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	IdLocation    string                   `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	Parent        *BackofficeLocationModel `protobuf:"bytes,2,opt,name=parent,proto3" json:"parent,omitempty"`
	LocationLevel v11.LocationLevel        `protobuf:"varint,3,opt,name=location_level,json=locationLevel,proto3,enum=algoenum.v1.LocationLevel" json:"location_level,omitempty"`
	Name          string                   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	IsoCode       string                   `protobuf:"bytes,5,opt,name=iso_code,json=isoCode,proto3" json:"iso_code,omitempty"`
	Emoji         string                   `protobuf:"bytes,6,opt,name=emoji,proto3" json:"emoji,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackofficeLocationModel) Reset() {
	*x = BackofficeLocationModel{}
	mi := &file_proxymanager_location_v1_backoffice_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackofficeLocationModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackofficeLocationModel) ProtoMessage() {}

func (x *BackofficeLocationModel) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_location_v1_backoffice_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackofficeLocationModel.ProtoReflect.Descriptor instead.
func (*BackofficeLocationModel) Descriptor() ([]byte, []int) {
	return file_proxymanager_location_v1_backoffice_proto_rawDescGZIP(), []int{8}
}

func (x *BackofficeLocationModel) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *BackofficeLocationModel) GetParent() *BackofficeLocationModel {
	if x != nil {
		return x.Parent
	}
	return nil
}

func (x *BackofficeLocationModel) GetLocationLevel() v11.LocationLevel {
	if x != nil {
		return x.LocationLevel
	}
	return v11.LocationLevel(0)
}

func (x *BackofficeLocationModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BackofficeLocationModel) GetIsoCode() string {
	if x != nil {
		return x.IsoCode
	}
	return ""
}

func (x *BackofficeLocationModel) GetEmoji() string {
	if x != nil {
		return x.Emoji
	}
	return ""
}

var File_proxymanager_location_v1_backoffice_proto protoreflect.FileDescriptor

const file_proxymanager_location_v1_backoffice_proto_rawDesc = "" +
	"\n" +
	")proxymanager/location/v1/backoffice.proto\x12\x18proxymanager.location.v1\x1a\x14utils/v1/utils.proto\x1a\x18errmsg/v1/errormsg.proto\x1a algoenum/v1/location_level.proto\"-\n" +
	"+BackofficeLocationServiceReloadCacheRequest\"]\n" +
	",BackofficeLocationServiceReloadCacheResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xea\x02\n" +
	"-BackofficeLocationServiceFetchLocationRequest\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x12!\n" +
	"\fid_locations\x18\x02 \x03(\tR\vidLocations\x12,\n" +
	"\x12id_parent_location\x18\x03 \x01(\tR\x10idParentLocation\x12A\n" +
	"\x0elocation_level\x18\x04 \x01(\x0e2\x1a.algoenum.v1.LocationLevelR\rlocationLevel\x12\x1f\n" +
	"\vname_search\x18\x05 \x01(\tR\n" +
	"nameSearch\x12&\n" +
	"\x0fiso_code_search\x18\x06 \x01(\tR\risoCodeSearch\x12;\n" +
	"\n" +
	"pagination\x18\a \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xee\x01\n" +
	".BackofficeLocationServiceFetchLocationResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12O\n" +
	"\tlocations\x18\x03 \x03(\v21.proxymanager.location.v1.BackofficeLocationModelR\tlocations\"\xe6\x01\n" +
	".BackofficeLocationServiceCreateLocationRequest\x12,\n" +
	"\x12id_parent_location\x18\x01 \x01(\tR\x10idParentLocation\x12A\n" +
	"\x0elocation_level\x18\x02 \x01(\x0e2\x1a.algoenum.v1.LocationLevelR\rlocationLevel\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x19\n" +
	"\biso_code\x18\x04 \x01(\tR\aisoCode\x12\x14\n" +
	"\x05emoji\x18\x05 \x01(\tR\x05emoji\"`\n" +
	"/BackofficeLocationServiceCreateLocationResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xaa\x01\n" +
	".BackofficeLocationServiceUpdateLocationRequest\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x12A\n" +
	"\x0elocation_level\x18\x02 \x01(\x0e2\x1a.algoenum.v1.LocationLevelR\rlocationLevel\x12\x14\n" +
	"\x05emoji\x18\x03 \x01(\tR\x05emoji\"`\n" +
	"/BackofficeLocationServiceUpdateLocationResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\x8d\x02\n" +
	"\x17BackofficeLocationModel\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x12I\n" +
	"\x06parent\x18\x02 \x01(\v21.proxymanager.location.v1.BackofficeLocationModelR\x06parent\x12A\n" +
	"\x0elocation_level\x18\x03 \x01(\x0e2\x1a.algoenum.v1.LocationLevelR\rlocationLevel\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x19\n" +
	"\biso_code\x18\x05 \x01(\tR\aisoCode\x12\x14\n" +
	"\x05emoji\x18\x06 \x01(\tR\x05emoji2\xaf\x05\n" +
	"\x19BackofficeLocationService\x12\x9c\x01\n" +
	"\vReloadCache\x12E.proxymanager.location.v1.BackofficeLocationServiceReloadCacheRequest\x1aF.proxymanager.location.v1.BackofficeLocationServiceReloadCacheResponse\x12\xa2\x01\n" +
	"\rFetchLocation\x12G.proxymanager.location.v1.BackofficeLocationServiceFetchLocationRequest\x1aH.proxymanager.location.v1.BackofficeLocationServiceFetchLocationResponse\x12\xa5\x01\n" +
	"\x0eCreateLocation\x12H.proxymanager.location.v1.BackofficeLocationServiceCreateLocationRequest\x1aI.proxymanager.location.v1.BackofficeLocationServiceCreateLocationResponse\x12\xa5\x01\n" +
	"\x0eUpdateLocation\x12H.proxymanager.location.v1.BackofficeLocationServiceUpdateLocationRequest\x1aI.proxymanager.location.v1.BackofficeLocationServiceUpdateLocationResponseBTZRgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/location/v1;locationv1b\x06proto3"

var (
	file_proxymanager_location_v1_backoffice_proto_rawDescOnce sync.Once
	file_proxymanager_location_v1_backoffice_proto_rawDescData []byte
)

func file_proxymanager_location_v1_backoffice_proto_rawDescGZIP() []byte {
	file_proxymanager_location_v1_backoffice_proto_rawDescOnce.Do(func() {
		file_proxymanager_location_v1_backoffice_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_location_v1_backoffice_proto_rawDesc), len(file_proxymanager_location_v1_backoffice_proto_rawDesc)))
	})
	return file_proxymanager_location_v1_backoffice_proto_rawDescData
}

var file_proxymanager_location_v1_backoffice_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_proxymanager_location_v1_backoffice_proto_goTypes = []any{
	(*BackofficeLocationServiceReloadCacheRequest)(nil),     // 0: proxymanager.location.v1.BackofficeLocationServiceReloadCacheRequest
	(*BackofficeLocationServiceReloadCacheResponse)(nil),    // 1: proxymanager.location.v1.BackofficeLocationServiceReloadCacheResponse
	(*BackofficeLocationServiceFetchLocationRequest)(nil),   // 2: proxymanager.location.v1.BackofficeLocationServiceFetchLocationRequest
	(*BackofficeLocationServiceFetchLocationResponse)(nil),  // 3: proxymanager.location.v1.BackofficeLocationServiceFetchLocationResponse
	(*BackofficeLocationServiceCreateLocationRequest)(nil),  // 4: proxymanager.location.v1.BackofficeLocationServiceCreateLocationRequest
	(*BackofficeLocationServiceCreateLocationResponse)(nil), // 5: proxymanager.location.v1.BackofficeLocationServiceCreateLocationResponse
	(*BackofficeLocationServiceUpdateLocationRequest)(nil),  // 6: proxymanager.location.v1.BackofficeLocationServiceUpdateLocationRequest
	(*BackofficeLocationServiceUpdateLocationResponse)(nil), // 7: proxymanager.location.v1.BackofficeLocationServiceUpdateLocationResponse
	(*BackofficeLocationModel)(nil),                         // 8: proxymanager.location.v1.BackofficeLocationModel
	(*v1.ErrorMessage)(nil),                                 // 9: errmsg.v1.ErrorMessage
	(v11.LocationLevel)(0),                                  // 10: algoenum.v1.LocationLevel
	(*v12.PaginationRequest)(nil),                           // 11: utils.v1.PaginationRequest
	(*v12.PaginationResponse)(nil),                          // 12: utils.v1.PaginationResponse
}
var file_proxymanager_location_v1_backoffice_proto_depIdxs = []int32{
	9,  // 0: proxymanager.location.v1.BackofficeLocationServiceReloadCacheResponse.error:type_name -> errmsg.v1.ErrorMessage
	10, // 1: proxymanager.location.v1.BackofficeLocationServiceFetchLocationRequest.location_level:type_name -> algoenum.v1.LocationLevel
	11, // 2: proxymanager.location.v1.BackofficeLocationServiceFetchLocationRequest.pagination:type_name -> utils.v1.PaginationRequest
	9,  // 3: proxymanager.location.v1.BackofficeLocationServiceFetchLocationResponse.error:type_name -> errmsg.v1.ErrorMessage
	12, // 4: proxymanager.location.v1.BackofficeLocationServiceFetchLocationResponse.pagination:type_name -> utils.v1.PaginationResponse
	8,  // 5: proxymanager.location.v1.BackofficeLocationServiceFetchLocationResponse.locations:type_name -> proxymanager.location.v1.BackofficeLocationModel
	10, // 6: proxymanager.location.v1.BackofficeLocationServiceCreateLocationRequest.location_level:type_name -> algoenum.v1.LocationLevel
	9,  // 7: proxymanager.location.v1.BackofficeLocationServiceCreateLocationResponse.error:type_name -> errmsg.v1.ErrorMessage
	10, // 8: proxymanager.location.v1.BackofficeLocationServiceUpdateLocationRequest.location_level:type_name -> algoenum.v1.LocationLevel
	9,  // 9: proxymanager.location.v1.BackofficeLocationServiceUpdateLocationResponse.error:type_name -> errmsg.v1.ErrorMessage
	8,  // 10: proxymanager.location.v1.BackofficeLocationModel.parent:type_name -> proxymanager.location.v1.BackofficeLocationModel
	10, // 11: proxymanager.location.v1.BackofficeLocationModel.location_level:type_name -> algoenum.v1.LocationLevel
	0,  // 12: proxymanager.location.v1.BackofficeLocationService.ReloadCache:input_type -> proxymanager.location.v1.BackofficeLocationServiceReloadCacheRequest
	2,  // 13: proxymanager.location.v1.BackofficeLocationService.FetchLocation:input_type -> proxymanager.location.v1.BackofficeLocationServiceFetchLocationRequest
	4,  // 14: proxymanager.location.v1.BackofficeLocationService.CreateLocation:input_type -> proxymanager.location.v1.BackofficeLocationServiceCreateLocationRequest
	6,  // 15: proxymanager.location.v1.BackofficeLocationService.UpdateLocation:input_type -> proxymanager.location.v1.BackofficeLocationServiceUpdateLocationRequest
	1,  // 16: proxymanager.location.v1.BackofficeLocationService.ReloadCache:output_type -> proxymanager.location.v1.BackofficeLocationServiceReloadCacheResponse
	3,  // 17: proxymanager.location.v1.BackofficeLocationService.FetchLocation:output_type -> proxymanager.location.v1.BackofficeLocationServiceFetchLocationResponse
	5,  // 18: proxymanager.location.v1.BackofficeLocationService.CreateLocation:output_type -> proxymanager.location.v1.BackofficeLocationServiceCreateLocationResponse
	7,  // 19: proxymanager.location.v1.BackofficeLocationService.UpdateLocation:output_type -> proxymanager.location.v1.BackofficeLocationServiceUpdateLocationResponse
	16, // [16:20] is the sub-list for method output_type
	12, // [12:16] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_proxymanager_location_v1_backoffice_proto_init() }
func file_proxymanager_location_v1_backoffice_proto_init() {
	if File_proxymanager_location_v1_backoffice_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_location_v1_backoffice_proto_rawDesc), len(file_proxymanager_location_v1_backoffice_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_location_v1_backoffice_proto_goTypes,
		DependencyIndexes: file_proxymanager_location_v1_backoffice_proto_depIdxs,
		MessageInfos:      file_proxymanager_location_v1_backoffice_proto_msgTypes,
	}.Build()
	File_proxymanager_location_v1_backoffice_proto = out.File
	file_proxymanager_location_v1_backoffice_proto_goTypes = nil
	file_proxymanager_location_v1_backoffice_proto_depIdxs = nil
}
