// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: proxymanager/location/v1/customer.proto

package locationv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1"
	v12 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CustomerLocationServiceFetchLocationRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	IdLocation       string                 `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	IdLocations      []string               `protobuf:"bytes,2,rep,name=id_locations,json=idLocations,proto3" json:"id_locations,omitempty"`
	IdParentLocation string                 `protobuf:"bytes,3,opt,name=id_parent_location,json=idParentLocation,proto3" json:"id_parent_location,omitempty"`
	LocationLevel    v1.LocationLevel       `protobuf:"varint,4,opt,name=location_level,json=locationLevel,proto3,enum=algoenum.v1.LocationLevel" json:"location_level,omitempty"`
	NameSearch       string                 `protobuf:"bytes,5,opt,name=name_search,json=nameSearch,proto3" json:"name_search,omitempty"`
	IsoCodeSearch    string                 `protobuf:"bytes,6,opt,name=iso_code_search,json=isoCodeSearch,proto3" json:"iso_code_search,omitempty"`
	Pagination       *v11.PaginationRequest `protobuf:"bytes,7,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CustomerLocationServiceFetchLocationRequest) Reset() {
	*x = CustomerLocationServiceFetchLocationRequest{}
	mi := &file_proxymanager_location_v1_customer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerLocationServiceFetchLocationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerLocationServiceFetchLocationRequest) ProtoMessage() {}

func (x *CustomerLocationServiceFetchLocationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_location_v1_customer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerLocationServiceFetchLocationRequest.ProtoReflect.Descriptor instead.
func (*CustomerLocationServiceFetchLocationRequest) Descriptor() ([]byte, []int) {
	return file_proxymanager_location_v1_customer_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerLocationServiceFetchLocationRequest) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *CustomerLocationServiceFetchLocationRequest) GetIdLocations() []string {
	if x != nil {
		return x.IdLocations
	}
	return nil
}

func (x *CustomerLocationServiceFetchLocationRequest) GetIdParentLocation() string {
	if x != nil {
		return x.IdParentLocation
	}
	return ""
}

func (x *CustomerLocationServiceFetchLocationRequest) GetLocationLevel() v1.LocationLevel {
	if x != nil {
		return x.LocationLevel
	}
	return v1.LocationLevel(0)
}

func (x *CustomerLocationServiceFetchLocationRequest) GetNameSearch() string {
	if x != nil {
		return x.NameSearch
	}
	return ""
}

func (x *CustomerLocationServiceFetchLocationRequest) GetIsoCodeSearch() string {
	if x != nil {
		return x.IsoCodeSearch
	}
	return ""
}

func (x *CustomerLocationServiceFetchLocationRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type CustomerLocationServiceFetchLocationResponse struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Error         *v12.ErrorMessage        `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse  `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Locations     []*CustomerLocationModel `protobuf:"bytes,3,rep,name=locations,proto3" json:"locations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerLocationServiceFetchLocationResponse) Reset() {
	*x = CustomerLocationServiceFetchLocationResponse{}
	mi := &file_proxymanager_location_v1_customer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerLocationServiceFetchLocationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerLocationServiceFetchLocationResponse) ProtoMessage() {}

func (x *CustomerLocationServiceFetchLocationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_location_v1_customer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerLocationServiceFetchLocationResponse.ProtoReflect.Descriptor instead.
func (*CustomerLocationServiceFetchLocationResponse) Descriptor() ([]byte, []int) {
	return file_proxymanager_location_v1_customer_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerLocationServiceFetchLocationResponse) GetError() *v12.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CustomerLocationServiceFetchLocationResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *CustomerLocationServiceFetchLocationResponse) GetLocations() []*CustomerLocationModel {
	if x != nil {
		return x.Locations
	}
	return nil
}

type CustomerLocationModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdLocation    string                 `protobuf:"bytes,1,opt,name=id_location,json=idLocation,proto3" json:"id_location,omitempty"`
	Parent        *CustomerLocationModel `protobuf:"bytes,2,opt,name=parent,proto3" json:"parent,omitempty"`
	LocationLevel v1.LocationLevel       `protobuf:"varint,3,opt,name=location_level,json=locationLevel,proto3,enum=algoenum.v1.LocationLevel" json:"location_level,omitempty"`
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	IsoCode       string                 `protobuf:"bytes,5,opt,name=iso_code,json=isoCode,proto3" json:"iso_code,omitempty"`
	Emoji         string                 `protobuf:"bytes,6,opt,name=emoji,proto3" json:"emoji,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomerLocationModel) Reset() {
	*x = CustomerLocationModel{}
	mi := &file_proxymanager_location_v1_customer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomerLocationModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerLocationModel) ProtoMessage() {}

func (x *CustomerLocationModel) ProtoReflect() protoreflect.Message {
	mi := &file_proxymanager_location_v1_customer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerLocationModel.ProtoReflect.Descriptor instead.
func (*CustomerLocationModel) Descriptor() ([]byte, []int) {
	return file_proxymanager_location_v1_customer_proto_rawDescGZIP(), []int{2}
}

func (x *CustomerLocationModel) GetIdLocation() string {
	if x != nil {
		return x.IdLocation
	}
	return ""
}

func (x *CustomerLocationModel) GetParent() *CustomerLocationModel {
	if x != nil {
		return x.Parent
	}
	return nil
}

func (x *CustomerLocationModel) GetLocationLevel() v1.LocationLevel {
	if x != nil {
		return x.LocationLevel
	}
	return v1.LocationLevel(0)
}

func (x *CustomerLocationModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CustomerLocationModel) GetIsoCode() string {
	if x != nil {
		return x.IsoCode
	}
	return ""
}

func (x *CustomerLocationModel) GetEmoji() string {
	if x != nil {
		return x.Emoji
	}
	return ""
}

var File_proxymanager_location_v1_customer_proto protoreflect.FileDescriptor

const file_proxymanager_location_v1_customer_proto_rawDesc = "" +
	"\n" +
	"'proxymanager/location/v1/customer.proto\x12\x18proxymanager.location.v1\x1a\x14utils/v1/utils.proto\x1a\x18errmsg/v1/errormsg.proto\x1a algoenum/v1/location_level.proto\"\xe8\x02\n" +
	"+CustomerLocationServiceFetchLocationRequest\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x12!\n" +
	"\fid_locations\x18\x02 \x03(\tR\vidLocations\x12,\n" +
	"\x12id_parent_location\x18\x03 \x01(\tR\x10idParentLocation\x12A\n" +
	"\x0elocation_level\x18\x04 \x01(\x0e2\x1a.algoenum.v1.LocationLevelR\rlocationLevel\x12\x1f\n" +
	"\vname_search\x18\x05 \x01(\tR\n" +
	"nameSearch\x12&\n" +
	"\x0fiso_code_search\x18\x06 \x01(\tR\risoCodeSearch\x12;\n" +
	"\n" +
	"pagination\x18\a \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xea\x01\n" +
	",CustomerLocationServiceFetchLocationResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12M\n" +
	"\tlocations\x18\x03 \x03(\v2/.proxymanager.location.v1.CustomerLocationModelR\tlocations\"\x89\x02\n" +
	"\x15CustomerLocationModel\x12\x1f\n" +
	"\vid_location\x18\x01 \x01(\tR\n" +
	"idLocation\x12G\n" +
	"\x06parent\x18\x02 \x01(\v2/.proxymanager.location.v1.CustomerLocationModelR\x06parent\x12A\n" +
	"\x0elocation_level\x18\x03 \x01(\x0e2\x1a.algoenum.v1.LocationLevelR\rlocationLevel\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x19\n" +
	"\biso_code\x18\x05 \x01(\tR\aisoCode\x12\x14\n" +
	"\x05emoji\x18\x06 \x01(\tR\x05emoji2\xba\x01\n" +
	"\x17CustomerLocationService\x12\x9e\x01\n" +
	"\rFetchLocation\x12E.proxymanager.location.v1.CustomerLocationServiceFetchLocationRequest\x1aF.proxymanager.location.v1.CustomerLocationServiceFetchLocationResponseBTZRgit.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/location/v1;locationv1b\x06proto3"

var (
	file_proxymanager_location_v1_customer_proto_rawDescOnce sync.Once
	file_proxymanager_location_v1_customer_proto_rawDescData []byte
)

func file_proxymanager_location_v1_customer_proto_rawDescGZIP() []byte {
	file_proxymanager_location_v1_customer_proto_rawDescOnce.Do(func() {
		file_proxymanager_location_v1_customer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proxymanager_location_v1_customer_proto_rawDesc), len(file_proxymanager_location_v1_customer_proto_rawDesc)))
	})
	return file_proxymanager_location_v1_customer_proto_rawDescData
}

var file_proxymanager_location_v1_customer_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_proxymanager_location_v1_customer_proto_goTypes = []any{
	(*CustomerLocationServiceFetchLocationRequest)(nil),  // 0: proxymanager.location.v1.CustomerLocationServiceFetchLocationRequest
	(*CustomerLocationServiceFetchLocationResponse)(nil), // 1: proxymanager.location.v1.CustomerLocationServiceFetchLocationResponse
	(*CustomerLocationModel)(nil),                        // 2: proxymanager.location.v1.CustomerLocationModel
	(v1.LocationLevel)(0),                                // 3: algoenum.v1.LocationLevel
	(*v11.PaginationRequest)(nil),                        // 4: utils.v1.PaginationRequest
	(*v12.ErrorMessage)(nil),                             // 5: errmsg.v1.ErrorMessage
	(*v11.PaginationResponse)(nil),                       // 6: utils.v1.PaginationResponse
}
var file_proxymanager_location_v1_customer_proto_depIdxs = []int32{
	3, // 0: proxymanager.location.v1.CustomerLocationServiceFetchLocationRequest.location_level:type_name -> algoenum.v1.LocationLevel
	4, // 1: proxymanager.location.v1.CustomerLocationServiceFetchLocationRequest.pagination:type_name -> utils.v1.PaginationRequest
	5, // 2: proxymanager.location.v1.CustomerLocationServiceFetchLocationResponse.error:type_name -> errmsg.v1.ErrorMessage
	6, // 3: proxymanager.location.v1.CustomerLocationServiceFetchLocationResponse.pagination:type_name -> utils.v1.PaginationResponse
	2, // 4: proxymanager.location.v1.CustomerLocationServiceFetchLocationResponse.locations:type_name -> proxymanager.location.v1.CustomerLocationModel
	2, // 5: proxymanager.location.v1.CustomerLocationModel.parent:type_name -> proxymanager.location.v1.CustomerLocationModel
	3, // 6: proxymanager.location.v1.CustomerLocationModel.location_level:type_name -> algoenum.v1.LocationLevel
	0, // 7: proxymanager.location.v1.CustomerLocationService.FetchLocation:input_type -> proxymanager.location.v1.CustomerLocationServiceFetchLocationRequest
	1, // 8: proxymanager.location.v1.CustomerLocationService.FetchLocation:output_type -> proxymanager.location.v1.CustomerLocationServiceFetchLocationResponse
	8, // [8:9] is the sub-list for method output_type
	7, // [7:8] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_proxymanager_location_v1_customer_proto_init() }
func file_proxymanager_location_v1_customer_proto_init() {
	if File_proxymanager_location_v1_customer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proxymanager_location_v1_customer_proto_rawDesc), len(file_proxymanager_location_v1_customer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proxymanager_location_v1_customer_proto_goTypes,
		DependencyIndexes: file_proxymanager_location_v1_customer_proto_depIdxs,
		MessageInfos:      file_proxymanager_location_v1_customer_proto_msgTypes,
	}.Build()
	File_proxymanager_location_v1_customer_proto = out.File
	file_proxymanager_location_v1_customer_proto_goTypes = nil
	file_proxymanager_location_v1_customer_proto_depIdxs = nil
}
