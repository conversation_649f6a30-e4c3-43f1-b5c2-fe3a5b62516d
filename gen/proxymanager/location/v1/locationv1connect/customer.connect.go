// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/location/v1/customer.proto

package locationv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/location/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// CustomerLocationServiceName is the fully-qualified name of the CustomerLocationService service.
	CustomerLocationServiceName = "proxymanager.location.v1.CustomerLocationService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// CustomerLocationServiceFetchLocationProcedure is the fully-qualified name of the
	// CustomerLocationService's FetchLocation RPC.
	CustomerLocationServiceFetchLocationProcedure = "/proxymanager.location.v1.CustomerLocationService/FetchLocation"
)

// CustomerLocationServiceClient is a client for the
// proxymanager.location.v1.CustomerLocationService service.
type CustomerLocationServiceClient interface {
	FetchLocation(context.Context, *connect.Request[v1.CustomerLocationServiceFetchLocationRequest]) (*connect.Response[v1.CustomerLocationServiceFetchLocationResponse], error)
}

// NewCustomerLocationServiceClient constructs a client for the
// proxymanager.location.v1.CustomerLocationService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewCustomerLocationServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) CustomerLocationServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	customerLocationServiceMethods := v1.File_proxymanager_location_v1_customer_proto.Services().ByName("CustomerLocationService").Methods()
	return &customerLocationServiceClient{
		fetchLocation: connect.NewClient[v1.CustomerLocationServiceFetchLocationRequest, v1.CustomerLocationServiceFetchLocationResponse](
			httpClient,
			baseURL+CustomerLocationServiceFetchLocationProcedure,
			connect.WithSchema(customerLocationServiceMethods.ByName("FetchLocation")),
			connect.WithClientOptions(opts...),
		),
	}
}

// customerLocationServiceClient implements CustomerLocationServiceClient.
type customerLocationServiceClient struct {
	fetchLocation *connect.Client[v1.CustomerLocationServiceFetchLocationRequest, v1.CustomerLocationServiceFetchLocationResponse]
}

// FetchLocation calls proxymanager.location.v1.CustomerLocationService.FetchLocation.
func (c *customerLocationServiceClient) FetchLocation(ctx context.Context, req *connect.Request[v1.CustomerLocationServiceFetchLocationRequest]) (*connect.Response[v1.CustomerLocationServiceFetchLocationResponse], error) {
	return c.fetchLocation.CallUnary(ctx, req)
}

// CustomerLocationServiceHandler is an implementation of the
// proxymanager.location.v1.CustomerLocationService service.
type CustomerLocationServiceHandler interface {
	FetchLocation(context.Context, *connect.Request[v1.CustomerLocationServiceFetchLocationRequest]) (*connect.Response[v1.CustomerLocationServiceFetchLocationResponse], error)
}

// NewCustomerLocationServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewCustomerLocationServiceHandler(svc CustomerLocationServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	customerLocationServiceMethods := v1.File_proxymanager_location_v1_customer_proto.Services().ByName("CustomerLocationService").Methods()
	customerLocationServiceFetchLocationHandler := connect.NewUnaryHandler(
		CustomerLocationServiceFetchLocationProcedure,
		svc.FetchLocation,
		connect.WithSchema(customerLocationServiceMethods.ByName("FetchLocation")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.location.v1.CustomerLocationService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case CustomerLocationServiceFetchLocationProcedure:
			customerLocationServiceFetchLocationHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedCustomerLocationServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedCustomerLocationServiceHandler struct{}

func (UnimplementedCustomerLocationServiceHandler) FetchLocation(context.Context, *connect.Request[v1.CustomerLocationServiceFetchLocationRequest]) (*connect.Response[v1.CustomerLocationServiceFetchLocationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.location.v1.CustomerLocationService.FetchLocation is not implemented"))
}
