// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/location/v1/merchant.proto

package locationv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/location/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// MerchantLocationServiceName is the fully-qualified name of the MerchantLocationService service.
	MerchantLocationServiceName = "proxymanager.location.v1.MerchantLocationService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// MerchantLocationServiceFetchLocationProcedure is the fully-qualified name of the
	// MerchantLocationService's FetchLocation RPC.
	MerchantLocationServiceFetchLocationProcedure = "/proxymanager.location.v1.MerchantLocationService/FetchLocation"
)

// MerchantLocationServiceClient is a client for the
// proxymanager.location.v1.MerchantLocationService service.
type MerchantLocationServiceClient interface {
	FetchLocation(context.Context, *connect.Request[v1.MerchantLocationServiceFetchLocationRequest]) (*connect.Response[v1.MerchantLocationServiceFetchLocationResponse], error)
}

// NewMerchantLocationServiceClient constructs a client for the
// proxymanager.location.v1.MerchantLocationService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewMerchantLocationServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) MerchantLocationServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	merchantLocationServiceMethods := v1.File_proxymanager_location_v1_merchant_proto.Services().ByName("MerchantLocationService").Methods()
	return &merchantLocationServiceClient{
		fetchLocation: connect.NewClient[v1.MerchantLocationServiceFetchLocationRequest, v1.MerchantLocationServiceFetchLocationResponse](
			httpClient,
			baseURL+MerchantLocationServiceFetchLocationProcedure,
			connect.WithSchema(merchantLocationServiceMethods.ByName("FetchLocation")),
			connect.WithClientOptions(opts...),
		),
	}
}

// merchantLocationServiceClient implements MerchantLocationServiceClient.
type merchantLocationServiceClient struct {
	fetchLocation *connect.Client[v1.MerchantLocationServiceFetchLocationRequest, v1.MerchantLocationServiceFetchLocationResponse]
}

// FetchLocation calls proxymanager.location.v1.MerchantLocationService.FetchLocation.
func (c *merchantLocationServiceClient) FetchLocation(ctx context.Context, req *connect.Request[v1.MerchantLocationServiceFetchLocationRequest]) (*connect.Response[v1.MerchantLocationServiceFetchLocationResponse], error) {
	return c.fetchLocation.CallUnary(ctx, req)
}

// MerchantLocationServiceHandler is an implementation of the
// proxymanager.location.v1.MerchantLocationService service.
type MerchantLocationServiceHandler interface {
	FetchLocation(context.Context, *connect.Request[v1.MerchantLocationServiceFetchLocationRequest]) (*connect.Response[v1.MerchantLocationServiceFetchLocationResponse], error)
}

// NewMerchantLocationServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewMerchantLocationServiceHandler(svc MerchantLocationServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	merchantLocationServiceMethods := v1.File_proxymanager_location_v1_merchant_proto.Services().ByName("MerchantLocationService").Methods()
	merchantLocationServiceFetchLocationHandler := connect.NewUnaryHandler(
		MerchantLocationServiceFetchLocationProcedure,
		svc.FetchLocation,
		connect.WithSchema(merchantLocationServiceMethods.ByName("FetchLocation")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.location.v1.MerchantLocationService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case MerchantLocationServiceFetchLocationProcedure:
			merchantLocationServiceFetchLocationHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedMerchantLocationServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedMerchantLocationServiceHandler struct{}

func (UnimplementedMerchantLocationServiceHandler) FetchLocation(context.Context, *connect.Request[v1.MerchantLocationServiceFetchLocationRequest]) (*connect.Response[v1.MerchantLocationServiceFetchLocationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.location.v1.MerchantLocationService.FetchLocation is not implemented"))
}
