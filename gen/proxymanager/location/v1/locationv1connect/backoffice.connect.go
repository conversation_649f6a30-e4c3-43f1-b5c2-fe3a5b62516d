// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: proxymanager/location/v1/backoffice.proto

package locationv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/location/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackofficeLocationServiceName is the fully-qualified name of the BackofficeLocationService
	// service.
	BackofficeLocationServiceName = "proxymanager.location.v1.BackofficeLocationService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackofficeLocationServiceReloadCacheProcedure is the fully-qualified name of the
	// BackofficeLocationService's ReloadCache RPC.
	BackofficeLocationServiceReloadCacheProcedure = "/proxymanager.location.v1.BackofficeLocationService/ReloadCache"
	// BackofficeLocationServiceFetchLocationProcedure is the fully-qualified name of the
	// BackofficeLocationService's FetchLocation RPC.
	BackofficeLocationServiceFetchLocationProcedure = "/proxymanager.location.v1.BackofficeLocationService/FetchLocation"
	// BackofficeLocationServiceCreateLocationProcedure is the fully-qualified name of the
	// BackofficeLocationService's CreateLocation RPC.
	BackofficeLocationServiceCreateLocationProcedure = "/proxymanager.location.v1.BackofficeLocationService/CreateLocation"
	// BackofficeLocationServiceUpdateLocationProcedure is the fully-qualified name of the
	// BackofficeLocationService's UpdateLocation RPC.
	BackofficeLocationServiceUpdateLocationProcedure = "/proxymanager.location.v1.BackofficeLocationService/UpdateLocation"
)

// BackofficeLocationServiceClient is a client for the
// proxymanager.location.v1.BackofficeLocationService service.
type BackofficeLocationServiceClient interface {
	ReloadCache(context.Context, *connect.Request[v1.BackofficeLocationServiceReloadCacheRequest]) (*connect.Response[v1.BackofficeLocationServiceReloadCacheResponse], error)
	FetchLocation(context.Context, *connect.Request[v1.BackofficeLocationServiceFetchLocationRequest]) (*connect.Response[v1.BackofficeLocationServiceFetchLocationResponse], error)
	CreateLocation(context.Context, *connect.Request[v1.BackofficeLocationServiceCreateLocationRequest]) (*connect.Response[v1.BackofficeLocationServiceCreateLocationResponse], error)
	UpdateLocation(context.Context, *connect.Request[v1.BackofficeLocationServiceUpdateLocationRequest]) (*connect.Response[v1.BackofficeLocationServiceUpdateLocationResponse], error)
}

// NewBackofficeLocationServiceClient constructs a client for the
// proxymanager.location.v1.BackofficeLocationService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackofficeLocationServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackofficeLocationServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backofficeLocationServiceMethods := v1.File_proxymanager_location_v1_backoffice_proto.Services().ByName("BackofficeLocationService").Methods()
	return &backofficeLocationServiceClient{
		reloadCache: connect.NewClient[v1.BackofficeLocationServiceReloadCacheRequest, v1.BackofficeLocationServiceReloadCacheResponse](
			httpClient,
			baseURL+BackofficeLocationServiceReloadCacheProcedure,
			connect.WithSchema(backofficeLocationServiceMethods.ByName("ReloadCache")),
			connect.WithClientOptions(opts...),
		),
		fetchLocation: connect.NewClient[v1.BackofficeLocationServiceFetchLocationRequest, v1.BackofficeLocationServiceFetchLocationResponse](
			httpClient,
			baseURL+BackofficeLocationServiceFetchLocationProcedure,
			connect.WithSchema(backofficeLocationServiceMethods.ByName("FetchLocation")),
			connect.WithClientOptions(opts...),
		),
		createLocation: connect.NewClient[v1.BackofficeLocationServiceCreateLocationRequest, v1.BackofficeLocationServiceCreateLocationResponse](
			httpClient,
			baseURL+BackofficeLocationServiceCreateLocationProcedure,
			connect.WithSchema(backofficeLocationServiceMethods.ByName("CreateLocation")),
			connect.WithClientOptions(opts...),
		),
		updateLocation: connect.NewClient[v1.BackofficeLocationServiceUpdateLocationRequest, v1.BackofficeLocationServiceUpdateLocationResponse](
			httpClient,
			baseURL+BackofficeLocationServiceUpdateLocationProcedure,
			connect.WithSchema(backofficeLocationServiceMethods.ByName("UpdateLocation")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backofficeLocationServiceClient implements BackofficeLocationServiceClient.
type backofficeLocationServiceClient struct {
	reloadCache    *connect.Client[v1.BackofficeLocationServiceReloadCacheRequest, v1.BackofficeLocationServiceReloadCacheResponse]
	fetchLocation  *connect.Client[v1.BackofficeLocationServiceFetchLocationRequest, v1.BackofficeLocationServiceFetchLocationResponse]
	createLocation *connect.Client[v1.BackofficeLocationServiceCreateLocationRequest, v1.BackofficeLocationServiceCreateLocationResponse]
	updateLocation *connect.Client[v1.BackofficeLocationServiceUpdateLocationRequest, v1.BackofficeLocationServiceUpdateLocationResponse]
}

// ReloadCache calls proxymanager.location.v1.BackofficeLocationService.ReloadCache.
func (c *backofficeLocationServiceClient) ReloadCache(ctx context.Context, req *connect.Request[v1.BackofficeLocationServiceReloadCacheRequest]) (*connect.Response[v1.BackofficeLocationServiceReloadCacheResponse], error) {
	return c.reloadCache.CallUnary(ctx, req)
}

// FetchLocation calls proxymanager.location.v1.BackofficeLocationService.FetchLocation.
func (c *backofficeLocationServiceClient) FetchLocation(ctx context.Context, req *connect.Request[v1.BackofficeLocationServiceFetchLocationRequest]) (*connect.Response[v1.BackofficeLocationServiceFetchLocationResponse], error) {
	return c.fetchLocation.CallUnary(ctx, req)
}

// CreateLocation calls proxymanager.location.v1.BackofficeLocationService.CreateLocation.
func (c *backofficeLocationServiceClient) CreateLocation(ctx context.Context, req *connect.Request[v1.BackofficeLocationServiceCreateLocationRequest]) (*connect.Response[v1.BackofficeLocationServiceCreateLocationResponse], error) {
	return c.createLocation.CallUnary(ctx, req)
}

// UpdateLocation calls proxymanager.location.v1.BackofficeLocationService.UpdateLocation.
func (c *backofficeLocationServiceClient) UpdateLocation(ctx context.Context, req *connect.Request[v1.BackofficeLocationServiceUpdateLocationRequest]) (*connect.Response[v1.BackofficeLocationServiceUpdateLocationResponse], error) {
	return c.updateLocation.CallUnary(ctx, req)
}

// BackofficeLocationServiceHandler is an implementation of the
// proxymanager.location.v1.BackofficeLocationService service.
type BackofficeLocationServiceHandler interface {
	ReloadCache(context.Context, *connect.Request[v1.BackofficeLocationServiceReloadCacheRequest]) (*connect.Response[v1.BackofficeLocationServiceReloadCacheResponse], error)
	FetchLocation(context.Context, *connect.Request[v1.BackofficeLocationServiceFetchLocationRequest]) (*connect.Response[v1.BackofficeLocationServiceFetchLocationResponse], error)
	CreateLocation(context.Context, *connect.Request[v1.BackofficeLocationServiceCreateLocationRequest]) (*connect.Response[v1.BackofficeLocationServiceCreateLocationResponse], error)
	UpdateLocation(context.Context, *connect.Request[v1.BackofficeLocationServiceUpdateLocationRequest]) (*connect.Response[v1.BackofficeLocationServiceUpdateLocationResponse], error)
}

// NewBackofficeLocationServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackofficeLocationServiceHandler(svc BackofficeLocationServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backofficeLocationServiceMethods := v1.File_proxymanager_location_v1_backoffice_proto.Services().ByName("BackofficeLocationService").Methods()
	backofficeLocationServiceReloadCacheHandler := connect.NewUnaryHandler(
		BackofficeLocationServiceReloadCacheProcedure,
		svc.ReloadCache,
		connect.WithSchema(backofficeLocationServiceMethods.ByName("ReloadCache")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeLocationServiceFetchLocationHandler := connect.NewUnaryHandler(
		BackofficeLocationServiceFetchLocationProcedure,
		svc.FetchLocation,
		connect.WithSchema(backofficeLocationServiceMethods.ByName("FetchLocation")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeLocationServiceCreateLocationHandler := connect.NewUnaryHandler(
		BackofficeLocationServiceCreateLocationProcedure,
		svc.CreateLocation,
		connect.WithSchema(backofficeLocationServiceMethods.ByName("CreateLocation")),
		connect.WithHandlerOptions(opts...),
	)
	backofficeLocationServiceUpdateLocationHandler := connect.NewUnaryHandler(
		BackofficeLocationServiceUpdateLocationProcedure,
		svc.UpdateLocation,
		connect.WithSchema(backofficeLocationServiceMethods.ByName("UpdateLocation")),
		connect.WithHandlerOptions(opts...),
	)
	return "/proxymanager.location.v1.BackofficeLocationService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackofficeLocationServiceReloadCacheProcedure:
			backofficeLocationServiceReloadCacheHandler.ServeHTTP(w, r)
		case BackofficeLocationServiceFetchLocationProcedure:
			backofficeLocationServiceFetchLocationHandler.ServeHTTP(w, r)
		case BackofficeLocationServiceCreateLocationProcedure:
			backofficeLocationServiceCreateLocationHandler.ServeHTTP(w, r)
		case BackofficeLocationServiceUpdateLocationProcedure:
			backofficeLocationServiceUpdateLocationHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackofficeLocationServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackofficeLocationServiceHandler struct{}

func (UnimplementedBackofficeLocationServiceHandler) ReloadCache(context.Context, *connect.Request[v1.BackofficeLocationServiceReloadCacheRequest]) (*connect.Response[v1.BackofficeLocationServiceReloadCacheResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.location.v1.BackofficeLocationService.ReloadCache is not implemented"))
}

func (UnimplementedBackofficeLocationServiceHandler) FetchLocation(context.Context, *connect.Request[v1.BackofficeLocationServiceFetchLocationRequest]) (*connect.Response[v1.BackofficeLocationServiceFetchLocationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.location.v1.BackofficeLocationService.FetchLocation is not implemented"))
}

func (UnimplementedBackofficeLocationServiceHandler) CreateLocation(context.Context, *connect.Request[v1.BackofficeLocationServiceCreateLocationRequest]) (*connect.Response[v1.BackofficeLocationServiceCreateLocationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.location.v1.BackofficeLocationService.CreateLocation is not implemented"))
}

func (UnimplementedBackofficeLocationServiceHandler) UpdateLocation(context.Context, *connect.Request[v1.BackofficeLocationServiceUpdateLocationRequest]) (*connect.Response[v1.BackofficeLocationServiceUpdateLocationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("proxymanager.location.v1.BackofficeLocationService.UpdateLocation is not implemented"))
}
