// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: browsermanager/browser/v1/customer.proto

package browserv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/browsermanager/browser/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BrowserCustomerServiceName is the fully-qualified name of the BrowserCustomerService service.
	BrowserCustomerServiceName = "browsermanager.browser.v1.BrowserCustomerService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BrowserCustomerServiceFetchBrowserProcedure is the fully-qualified name of the
	// BrowserCustomerService's FetchBrowser RPC.
	BrowserCustomerServiceFetchBrowserProcedure = "/browsermanager.browser.v1.BrowserCustomerService/FetchBrowser"
)

// BrowserCustomerServiceClient is a client for the browsermanager.browser.v1.BrowserCustomerService
// service.
type BrowserCustomerServiceClient interface {
	FetchBrowser(context.Context, *connect.Request[v1.BrowserCustomerServiceFetchBrowserRequest]) (*connect.Response[v1.BrowserCustomerServiceFetchBrowserResponse], error)
}

// NewBrowserCustomerServiceClient constructs a client for the
// browsermanager.browser.v1.BrowserCustomerService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBrowserCustomerServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BrowserCustomerServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	browserCustomerServiceMethods := v1.File_browsermanager_browser_v1_customer_proto.Services().ByName("BrowserCustomerService").Methods()
	return &browserCustomerServiceClient{
		fetchBrowser: connect.NewClient[v1.BrowserCustomerServiceFetchBrowserRequest, v1.BrowserCustomerServiceFetchBrowserResponse](
			httpClient,
			baseURL+BrowserCustomerServiceFetchBrowserProcedure,
			connect.WithSchema(browserCustomerServiceMethods.ByName("FetchBrowser")),
			connect.WithClientOptions(opts...),
		),
	}
}

// browserCustomerServiceClient implements BrowserCustomerServiceClient.
type browserCustomerServiceClient struct {
	fetchBrowser *connect.Client[v1.BrowserCustomerServiceFetchBrowserRequest, v1.BrowserCustomerServiceFetchBrowserResponse]
}

// FetchBrowser calls browsermanager.browser.v1.BrowserCustomerService.FetchBrowser.
func (c *browserCustomerServiceClient) FetchBrowser(ctx context.Context, req *connect.Request[v1.BrowserCustomerServiceFetchBrowserRequest]) (*connect.Response[v1.BrowserCustomerServiceFetchBrowserResponse], error) {
	return c.fetchBrowser.CallUnary(ctx, req)
}

// BrowserCustomerServiceHandler is an implementation of the
// browsermanager.browser.v1.BrowserCustomerService service.
type BrowserCustomerServiceHandler interface {
	FetchBrowser(context.Context, *connect.Request[v1.BrowserCustomerServiceFetchBrowserRequest]) (*connect.Response[v1.BrowserCustomerServiceFetchBrowserResponse], error)
}

// NewBrowserCustomerServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBrowserCustomerServiceHandler(svc BrowserCustomerServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	browserCustomerServiceMethods := v1.File_browsermanager_browser_v1_customer_proto.Services().ByName("BrowserCustomerService").Methods()
	browserCustomerServiceFetchBrowserHandler := connect.NewUnaryHandler(
		BrowserCustomerServiceFetchBrowserProcedure,
		svc.FetchBrowser,
		connect.WithSchema(browserCustomerServiceMethods.ByName("FetchBrowser")),
		connect.WithHandlerOptions(opts...),
	)
	return "/browsermanager.browser.v1.BrowserCustomerService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BrowserCustomerServiceFetchBrowserProcedure:
			browserCustomerServiceFetchBrowserHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBrowserCustomerServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBrowserCustomerServiceHandler struct{}

func (UnimplementedBrowserCustomerServiceHandler) FetchBrowser(context.Context, *connect.Request[v1.BrowserCustomerServiceFetchBrowserRequest]) (*connect.Response[v1.BrowserCustomerServiceFetchBrowserResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("browsermanager.browser.v1.BrowserCustomerService.FetchBrowser is not implemented"))
}
