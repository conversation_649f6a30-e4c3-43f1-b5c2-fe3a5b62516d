// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: browsermanager/browser/v1/customer.proto

package browserv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BrowserCustomerServiceFetchBrowserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BrowserCustomerServiceFetchBrowserRequest) Reset() {
	*x = BrowserCustomerServiceFetchBrowserRequest{}
	mi := &file_browsermanager_browser_v1_customer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BrowserCustomerServiceFetchBrowserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrowserCustomerServiceFetchBrowserRequest) ProtoMessage() {}

func (x *BrowserCustomerServiceFetchBrowserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_browser_v1_customer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrowserCustomerServiceFetchBrowserRequest.ProtoReflect.Descriptor instead.
func (*BrowserCustomerServiceFetchBrowserRequest) Descriptor() ([]byte, []int) {
	return file_browsermanager_browser_v1_customer_proto_rawDescGZIP(), []int{0}
}

type BrowserCustomerServiceFetchBrowserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Cdp           string                 `protobuf:"bytes,2,opt,name=cdp,proto3" json:"cdp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BrowserCustomerServiceFetchBrowserResponse) Reset() {
	*x = BrowserCustomerServiceFetchBrowserResponse{}
	mi := &file_browsermanager_browser_v1_customer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BrowserCustomerServiceFetchBrowserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BrowserCustomerServiceFetchBrowserResponse) ProtoMessage() {}

func (x *BrowserCustomerServiceFetchBrowserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_browser_v1_customer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BrowserCustomerServiceFetchBrowserResponse.ProtoReflect.Descriptor instead.
func (*BrowserCustomerServiceFetchBrowserResponse) Descriptor() ([]byte, []int) {
	return file_browsermanager_browser_v1_customer_proto_rawDescGZIP(), []int{1}
}

func (x *BrowserCustomerServiceFetchBrowserResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BrowserCustomerServiceFetchBrowserResponse) GetCdp() string {
	if x != nil {
		return x.Cdp
	}
	return ""
}

var File_browsermanager_browser_v1_customer_proto protoreflect.FileDescriptor

const file_browsermanager_browser_v1_customer_proto_rawDesc = "" +
	"\n" +
	"(browsermanager/browser/v1/customer.proto\x12\x19browsermanager.browser.v1\x1a\x18errmsg/v1/errormsg.proto\"+\n" +
	")BrowserCustomerServiceFetchBrowserRequest\"m\n" +
	"*BrowserCustomerServiceFetchBrowserResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12\x10\n" +
	"\x03cdp\x18\x02 \x01(\tR\x03cdp2\xb6\x01\n" +
	"\x16BrowserCustomerService\x12\x9b\x01\n" +
	"\fFetchBrowser\x12D.browsermanager.browser.v1.BrowserCustomerServiceFetchBrowserRequest\x1aE.browsermanager.browser.v1.BrowserCustomerServiceFetchBrowserResponseBTZRgit.tmproxy-infra.com/algo/algoproxy-proto/gen/browsermanager/browser/v1;browserv1b\x06proto3"

var (
	file_browsermanager_browser_v1_customer_proto_rawDescOnce sync.Once
	file_browsermanager_browser_v1_customer_proto_rawDescData []byte
)

func file_browsermanager_browser_v1_customer_proto_rawDescGZIP() []byte {
	file_browsermanager_browser_v1_customer_proto_rawDescOnce.Do(func() {
		file_browsermanager_browser_v1_customer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_browsermanager_browser_v1_customer_proto_rawDesc), len(file_browsermanager_browser_v1_customer_proto_rawDesc)))
	})
	return file_browsermanager_browser_v1_customer_proto_rawDescData
}

var file_browsermanager_browser_v1_customer_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_browsermanager_browser_v1_customer_proto_goTypes = []any{
	(*BrowserCustomerServiceFetchBrowserRequest)(nil),  // 0: browsermanager.browser.v1.BrowserCustomerServiceFetchBrowserRequest
	(*BrowserCustomerServiceFetchBrowserResponse)(nil), // 1: browsermanager.browser.v1.BrowserCustomerServiceFetchBrowserResponse
	(*v1.ErrorMessage)(nil),                            // 2: errmsg.v1.ErrorMessage
}
var file_browsermanager_browser_v1_customer_proto_depIdxs = []int32{
	2, // 0: browsermanager.browser.v1.BrowserCustomerServiceFetchBrowserResponse.error:type_name -> errmsg.v1.ErrorMessage
	0, // 1: browsermanager.browser.v1.BrowserCustomerService.FetchBrowser:input_type -> browsermanager.browser.v1.BrowserCustomerServiceFetchBrowserRequest
	1, // 2: browsermanager.browser.v1.BrowserCustomerService.FetchBrowser:output_type -> browsermanager.browser.v1.BrowserCustomerServiceFetchBrowserResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_browsermanager_browser_v1_customer_proto_init() }
func file_browsermanager_browser_v1_customer_proto_init() {
	if File_browsermanager_browser_v1_customer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_browsermanager_browser_v1_customer_proto_rawDesc), len(file_browsermanager_browser_v1_customer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_browsermanager_browser_v1_customer_proto_goTypes,
		DependencyIndexes: file_browsermanager_browser_v1_customer_proto_depIdxs,
		MessageInfos:      file_browsermanager_browser_v1_customer_proto_msgTypes,
	}.Build()
	File_browsermanager_browser_v1_customer_proto = out.File
	file_browsermanager_browser_v1_customer_proto_goTypes = nil
	file_browsermanager_browser_v1_customer_proto_depIdxs = nil
}
