// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: browsermanager/backconnect/v1/backoffice.proto

package backconnectv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/browsermanager/backconnect/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackConnectBackofficeServiceName is the fully-qualified name of the BackConnectBackofficeService
	// service.
	BackConnectBackofficeServiceName = "browsermanager.backconnect.v1.BackConnectBackofficeService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackConnectBackofficeServiceFetchWorkerProcedure is the fully-qualified name of the
	// BackConnectBackofficeService's FetchWorker RPC.
	BackConnectBackofficeServiceFetchWorkerProcedure = "/browsermanager.backconnect.v1.BackConnectBackofficeService/FetchWorker"
	// BackConnectBackofficeServiceAddProxyProcedure is the fully-qualified name of the
	// BackConnectBackofficeService's AddProxy RPC.
	BackConnectBackofficeServiceAddProxyProcedure = "/browsermanager.backconnect.v1.BackConnectBackofficeService/AddProxy"
	// BackConnectBackofficeServiceRemoveProxyProcedure is the fully-qualified name of the
	// BackConnectBackofficeService's RemoveProxy RPC.
	BackConnectBackofficeServiceRemoveProxyProcedure = "/browsermanager.backconnect.v1.BackConnectBackofficeService/RemoveProxy"
)

// BackConnectBackofficeServiceClient is a client for the
// browsermanager.backconnect.v1.BackConnectBackofficeService service.
type BackConnectBackofficeServiceClient interface {
	FetchWorker(context.Context, *connect.Request[v1.BackConnectBackofficeServiceFetchWorkerRequest]) (*connect.Response[v1.BackConnectBackofficeServiceFetchWorkerResponse], error)
	AddProxy(context.Context, *connect.Request[v1.BackConnectBackofficeServiceAddProxyRequest]) (*connect.Response[v1.BackConnectBackofficeServiceAddProxyResponse], error)
	RemoveProxy(context.Context, *connect.Request[v1.BackConnectBackofficeServiceRemoveProxyRequest]) (*connect.Response[v1.BackConnectBackofficeServiceRemoveProxyResponse], error)
}

// NewBackConnectBackofficeServiceClient constructs a client for the
// browsermanager.backconnect.v1.BackConnectBackofficeService service. By default, it uses the
// Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and sends
// uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackConnectBackofficeServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackConnectBackofficeServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backConnectBackofficeServiceMethods := v1.File_browsermanager_backconnect_v1_backoffice_proto.Services().ByName("BackConnectBackofficeService").Methods()
	return &backConnectBackofficeServiceClient{
		fetchWorker: connect.NewClient[v1.BackConnectBackofficeServiceFetchWorkerRequest, v1.BackConnectBackofficeServiceFetchWorkerResponse](
			httpClient,
			baseURL+BackConnectBackofficeServiceFetchWorkerProcedure,
			connect.WithSchema(backConnectBackofficeServiceMethods.ByName("FetchWorker")),
			connect.WithClientOptions(opts...),
		),
		addProxy: connect.NewClient[v1.BackConnectBackofficeServiceAddProxyRequest, v1.BackConnectBackofficeServiceAddProxyResponse](
			httpClient,
			baseURL+BackConnectBackofficeServiceAddProxyProcedure,
			connect.WithSchema(backConnectBackofficeServiceMethods.ByName("AddProxy")),
			connect.WithClientOptions(opts...),
		),
		removeProxy: connect.NewClient[v1.BackConnectBackofficeServiceRemoveProxyRequest, v1.BackConnectBackofficeServiceRemoveProxyResponse](
			httpClient,
			baseURL+BackConnectBackofficeServiceRemoveProxyProcedure,
			connect.WithSchema(backConnectBackofficeServiceMethods.ByName("RemoveProxy")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backConnectBackofficeServiceClient implements BackConnectBackofficeServiceClient.
type backConnectBackofficeServiceClient struct {
	fetchWorker *connect.Client[v1.BackConnectBackofficeServiceFetchWorkerRequest, v1.BackConnectBackofficeServiceFetchWorkerResponse]
	addProxy    *connect.Client[v1.BackConnectBackofficeServiceAddProxyRequest, v1.BackConnectBackofficeServiceAddProxyResponse]
	removeProxy *connect.Client[v1.BackConnectBackofficeServiceRemoveProxyRequest, v1.BackConnectBackofficeServiceRemoveProxyResponse]
}

// FetchWorker calls browsermanager.backconnect.v1.BackConnectBackofficeService.FetchWorker.
func (c *backConnectBackofficeServiceClient) FetchWorker(ctx context.Context, req *connect.Request[v1.BackConnectBackofficeServiceFetchWorkerRequest]) (*connect.Response[v1.BackConnectBackofficeServiceFetchWorkerResponse], error) {
	return c.fetchWorker.CallUnary(ctx, req)
}

// AddProxy calls browsermanager.backconnect.v1.BackConnectBackofficeService.AddProxy.
func (c *backConnectBackofficeServiceClient) AddProxy(ctx context.Context, req *connect.Request[v1.BackConnectBackofficeServiceAddProxyRequest]) (*connect.Response[v1.BackConnectBackofficeServiceAddProxyResponse], error) {
	return c.addProxy.CallUnary(ctx, req)
}

// RemoveProxy calls browsermanager.backconnect.v1.BackConnectBackofficeService.RemoveProxy.
func (c *backConnectBackofficeServiceClient) RemoveProxy(ctx context.Context, req *connect.Request[v1.BackConnectBackofficeServiceRemoveProxyRequest]) (*connect.Response[v1.BackConnectBackofficeServiceRemoveProxyResponse], error) {
	return c.removeProxy.CallUnary(ctx, req)
}

// BackConnectBackofficeServiceHandler is an implementation of the
// browsermanager.backconnect.v1.BackConnectBackofficeService service.
type BackConnectBackofficeServiceHandler interface {
	FetchWorker(context.Context, *connect.Request[v1.BackConnectBackofficeServiceFetchWorkerRequest]) (*connect.Response[v1.BackConnectBackofficeServiceFetchWorkerResponse], error)
	AddProxy(context.Context, *connect.Request[v1.BackConnectBackofficeServiceAddProxyRequest]) (*connect.Response[v1.BackConnectBackofficeServiceAddProxyResponse], error)
	RemoveProxy(context.Context, *connect.Request[v1.BackConnectBackofficeServiceRemoveProxyRequest]) (*connect.Response[v1.BackConnectBackofficeServiceRemoveProxyResponse], error)
}

// NewBackConnectBackofficeServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackConnectBackofficeServiceHandler(svc BackConnectBackofficeServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backConnectBackofficeServiceMethods := v1.File_browsermanager_backconnect_v1_backoffice_proto.Services().ByName("BackConnectBackofficeService").Methods()
	backConnectBackofficeServiceFetchWorkerHandler := connect.NewUnaryHandler(
		BackConnectBackofficeServiceFetchWorkerProcedure,
		svc.FetchWorker,
		connect.WithSchema(backConnectBackofficeServiceMethods.ByName("FetchWorker")),
		connect.WithHandlerOptions(opts...),
	)
	backConnectBackofficeServiceAddProxyHandler := connect.NewUnaryHandler(
		BackConnectBackofficeServiceAddProxyProcedure,
		svc.AddProxy,
		connect.WithSchema(backConnectBackofficeServiceMethods.ByName("AddProxy")),
		connect.WithHandlerOptions(opts...),
	)
	backConnectBackofficeServiceRemoveProxyHandler := connect.NewUnaryHandler(
		BackConnectBackofficeServiceRemoveProxyProcedure,
		svc.RemoveProxy,
		connect.WithSchema(backConnectBackofficeServiceMethods.ByName("RemoveProxy")),
		connect.WithHandlerOptions(opts...),
	)
	return "/browsermanager.backconnect.v1.BackConnectBackofficeService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackConnectBackofficeServiceFetchWorkerProcedure:
			backConnectBackofficeServiceFetchWorkerHandler.ServeHTTP(w, r)
		case BackConnectBackofficeServiceAddProxyProcedure:
			backConnectBackofficeServiceAddProxyHandler.ServeHTTP(w, r)
		case BackConnectBackofficeServiceRemoveProxyProcedure:
			backConnectBackofficeServiceRemoveProxyHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackConnectBackofficeServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackConnectBackofficeServiceHandler struct{}

func (UnimplementedBackConnectBackofficeServiceHandler) FetchWorker(context.Context, *connect.Request[v1.BackConnectBackofficeServiceFetchWorkerRequest]) (*connect.Response[v1.BackConnectBackofficeServiceFetchWorkerResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("browsermanager.backconnect.v1.BackConnectBackofficeService.FetchWorker is not implemented"))
}

func (UnimplementedBackConnectBackofficeServiceHandler) AddProxy(context.Context, *connect.Request[v1.BackConnectBackofficeServiceAddProxyRequest]) (*connect.Response[v1.BackConnectBackofficeServiceAddProxyResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("browsermanager.backconnect.v1.BackConnectBackofficeService.AddProxy is not implemented"))
}

func (UnimplementedBackConnectBackofficeServiceHandler) RemoveProxy(context.Context, *connect.Request[v1.BackConnectBackofficeServiceRemoveProxyRequest]) (*connect.Response[v1.BackConnectBackofficeServiceRemoveProxyResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("browsermanager.backconnect.v1.BackConnectBackofficeService.RemoveProxy is not implemented"))
}
