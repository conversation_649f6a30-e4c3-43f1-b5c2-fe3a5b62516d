// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: browsermanager/backconnect/v1/controlplane.proto

package backconnectv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/browsermanager/backconnect/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackConnectControlPlaneServiceName is the fully-qualified name of the
	// BackConnectControlPlaneService service.
	BackConnectControlPlaneServiceName = "browsermanager.backconnect.v1.BackConnectControlPlaneService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackConnectControlPlaneServiceRegisterProcedure is the fully-qualified name of the
	// BackConnectControlPlaneService's Register RPC.
	BackConnectControlPlaneServiceRegisterProcedure = "/browsermanager.backconnect.v1.BackConnectControlPlaneService/Register"
	// BackConnectControlPlaneServiceFetchWorkerProcedure is the fully-qualified name of the
	// BackConnectControlPlaneService's FetchWorker RPC.
	BackConnectControlPlaneServiceFetchWorkerProcedure = "/browsermanager.backconnect.v1.BackConnectControlPlaneService/FetchWorker"
	// BackConnectControlPlaneServiceAddProxyProcedure is the fully-qualified name of the
	// BackConnectControlPlaneService's AddProxy RPC.
	BackConnectControlPlaneServiceAddProxyProcedure = "/browsermanager.backconnect.v1.BackConnectControlPlaneService/AddProxy"
	// BackConnectControlPlaneServiceRemoveProxyProcedure is the fully-qualified name of the
	// BackConnectControlPlaneService's RemoveProxy RPC.
	BackConnectControlPlaneServiceRemoveProxyProcedure = "/browsermanager.backconnect.v1.BackConnectControlPlaneService/RemoveProxy"
	// BackConnectControlPlaneServiceFetchBrowserProcedure is the fully-qualified name of the
	// BackConnectControlPlaneService's FetchBrowser RPC.
	BackConnectControlPlaneServiceFetchBrowserProcedure = "/browsermanager.backconnect.v1.BackConnectControlPlaneService/FetchBrowser"
)

// BackConnectControlPlaneServiceClient is a client for the
// browsermanager.backconnect.v1.BackConnectControlPlaneService service.
type BackConnectControlPlaneServiceClient interface {
	Register(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceRegisterRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceRegisterResponse], error)
	FetchWorker(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceFetchWorkerRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceFetchWorkerResponse], error)
	AddProxy(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceAddProxyRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceAddProxyResponse], error)
	RemoveProxy(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceRemoveProxyRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceRemoveProxyResponse], error)
	FetchBrowser(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceFetchBrowserRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceFetchBrowserResponse], error)
}

// NewBackConnectControlPlaneServiceClient constructs a client for the
// browsermanager.backconnect.v1.BackConnectControlPlaneService service. By default, it uses the
// Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and sends
// uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackConnectControlPlaneServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackConnectControlPlaneServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backConnectControlPlaneServiceMethods := v1.File_browsermanager_backconnect_v1_controlplane_proto.Services().ByName("BackConnectControlPlaneService").Methods()
	return &backConnectControlPlaneServiceClient{
		register: connect.NewClient[v1.BackConnectControlPlaneServiceRegisterRequest, v1.BackConnectControlPlaneServiceRegisterResponse](
			httpClient,
			baseURL+BackConnectControlPlaneServiceRegisterProcedure,
			connect.WithSchema(backConnectControlPlaneServiceMethods.ByName("Register")),
			connect.WithClientOptions(opts...),
		),
		fetchWorker: connect.NewClient[v1.BackConnectControlPlaneServiceFetchWorkerRequest, v1.BackConnectControlPlaneServiceFetchWorkerResponse](
			httpClient,
			baseURL+BackConnectControlPlaneServiceFetchWorkerProcedure,
			connect.WithSchema(backConnectControlPlaneServiceMethods.ByName("FetchWorker")),
			connect.WithClientOptions(opts...),
		),
		addProxy: connect.NewClient[v1.BackConnectControlPlaneServiceAddProxyRequest, v1.BackConnectControlPlaneServiceAddProxyResponse](
			httpClient,
			baseURL+BackConnectControlPlaneServiceAddProxyProcedure,
			connect.WithSchema(backConnectControlPlaneServiceMethods.ByName("AddProxy")),
			connect.WithClientOptions(opts...),
		),
		removeProxy: connect.NewClient[v1.BackConnectControlPlaneServiceRemoveProxyRequest, v1.BackConnectControlPlaneServiceRemoveProxyResponse](
			httpClient,
			baseURL+BackConnectControlPlaneServiceRemoveProxyProcedure,
			connect.WithSchema(backConnectControlPlaneServiceMethods.ByName("RemoveProxy")),
			connect.WithClientOptions(opts...),
		),
		fetchBrowser: connect.NewClient[v1.BackConnectControlPlaneServiceFetchBrowserRequest, v1.BackConnectControlPlaneServiceFetchBrowserResponse](
			httpClient,
			baseURL+BackConnectControlPlaneServiceFetchBrowserProcedure,
			connect.WithSchema(backConnectControlPlaneServiceMethods.ByName("FetchBrowser")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backConnectControlPlaneServiceClient implements BackConnectControlPlaneServiceClient.
type backConnectControlPlaneServiceClient struct {
	register     *connect.Client[v1.BackConnectControlPlaneServiceRegisterRequest, v1.BackConnectControlPlaneServiceRegisterResponse]
	fetchWorker  *connect.Client[v1.BackConnectControlPlaneServiceFetchWorkerRequest, v1.BackConnectControlPlaneServiceFetchWorkerResponse]
	addProxy     *connect.Client[v1.BackConnectControlPlaneServiceAddProxyRequest, v1.BackConnectControlPlaneServiceAddProxyResponse]
	removeProxy  *connect.Client[v1.BackConnectControlPlaneServiceRemoveProxyRequest, v1.BackConnectControlPlaneServiceRemoveProxyResponse]
	fetchBrowser *connect.Client[v1.BackConnectControlPlaneServiceFetchBrowserRequest, v1.BackConnectControlPlaneServiceFetchBrowserResponse]
}

// Register calls browsermanager.backconnect.v1.BackConnectControlPlaneService.Register.
func (c *backConnectControlPlaneServiceClient) Register(ctx context.Context, req *connect.Request[v1.BackConnectControlPlaneServiceRegisterRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceRegisterResponse], error) {
	return c.register.CallUnary(ctx, req)
}

// FetchWorker calls browsermanager.backconnect.v1.BackConnectControlPlaneService.FetchWorker.
func (c *backConnectControlPlaneServiceClient) FetchWorker(ctx context.Context, req *connect.Request[v1.BackConnectControlPlaneServiceFetchWorkerRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceFetchWorkerResponse], error) {
	return c.fetchWorker.CallUnary(ctx, req)
}

// AddProxy calls browsermanager.backconnect.v1.BackConnectControlPlaneService.AddProxy.
func (c *backConnectControlPlaneServiceClient) AddProxy(ctx context.Context, req *connect.Request[v1.BackConnectControlPlaneServiceAddProxyRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceAddProxyResponse], error) {
	return c.addProxy.CallUnary(ctx, req)
}

// RemoveProxy calls browsermanager.backconnect.v1.BackConnectControlPlaneService.RemoveProxy.
func (c *backConnectControlPlaneServiceClient) RemoveProxy(ctx context.Context, req *connect.Request[v1.BackConnectControlPlaneServiceRemoveProxyRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceRemoveProxyResponse], error) {
	return c.removeProxy.CallUnary(ctx, req)
}

// FetchBrowser calls browsermanager.backconnect.v1.BackConnectControlPlaneService.FetchBrowser.
func (c *backConnectControlPlaneServiceClient) FetchBrowser(ctx context.Context, req *connect.Request[v1.BackConnectControlPlaneServiceFetchBrowserRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceFetchBrowserResponse], error) {
	return c.fetchBrowser.CallUnary(ctx, req)
}

// BackConnectControlPlaneServiceHandler is an implementation of the
// browsermanager.backconnect.v1.BackConnectControlPlaneService service.
type BackConnectControlPlaneServiceHandler interface {
	Register(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceRegisterRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceRegisterResponse], error)
	FetchWorker(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceFetchWorkerRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceFetchWorkerResponse], error)
	AddProxy(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceAddProxyRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceAddProxyResponse], error)
	RemoveProxy(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceRemoveProxyRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceRemoveProxyResponse], error)
	FetchBrowser(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceFetchBrowserRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceFetchBrowserResponse], error)
}

// NewBackConnectControlPlaneServiceHandler builds an HTTP handler from the service implementation.
// It returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackConnectControlPlaneServiceHandler(svc BackConnectControlPlaneServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backConnectControlPlaneServiceMethods := v1.File_browsermanager_backconnect_v1_controlplane_proto.Services().ByName("BackConnectControlPlaneService").Methods()
	backConnectControlPlaneServiceRegisterHandler := connect.NewUnaryHandler(
		BackConnectControlPlaneServiceRegisterProcedure,
		svc.Register,
		connect.WithSchema(backConnectControlPlaneServiceMethods.ByName("Register")),
		connect.WithHandlerOptions(opts...),
	)
	backConnectControlPlaneServiceFetchWorkerHandler := connect.NewUnaryHandler(
		BackConnectControlPlaneServiceFetchWorkerProcedure,
		svc.FetchWorker,
		connect.WithSchema(backConnectControlPlaneServiceMethods.ByName("FetchWorker")),
		connect.WithHandlerOptions(opts...),
	)
	backConnectControlPlaneServiceAddProxyHandler := connect.NewUnaryHandler(
		BackConnectControlPlaneServiceAddProxyProcedure,
		svc.AddProxy,
		connect.WithSchema(backConnectControlPlaneServiceMethods.ByName("AddProxy")),
		connect.WithHandlerOptions(opts...),
	)
	backConnectControlPlaneServiceRemoveProxyHandler := connect.NewUnaryHandler(
		BackConnectControlPlaneServiceRemoveProxyProcedure,
		svc.RemoveProxy,
		connect.WithSchema(backConnectControlPlaneServiceMethods.ByName("RemoveProxy")),
		connect.WithHandlerOptions(opts...),
	)
	backConnectControlPlaneServiceFetchBrowserHandler := connect.NewUnaryHandler(
		BackConnectControlPlaneServiceFetchBrowserProcedure,
		svc.FetchBrowser,
		connect.WithSchema(backConnectControlPlaneServiceMethods.ByName("FetchBrowser")),
		connect.WithHandlerOptions(opts...),
	)
	return "/browsermanager.backconnect.v1.BackConnectControlPlaneService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackConnectControlPlaneServiceRegisterProcedure:
			backConnectControlPlaneServiceRegisterHandler.ServeHTTP(w, r)
		case BackConnectControlPlaneServiceFetchWorkerProcedure:
			backConnectControlPlaneServiceFetchWorkerHandler.ServeHTTP(w, r)
		case BackConnectControlPlaneServiceAddProxyProcedure:
			backConnectControlPlaneServiceAddProxyHandler.ServeHTTP(w, r)
		case BackConnectControlPlaneServiceRemoveProxyProcedure:
			backConnectControlPlaneServiceRemoveProxyHandler.ServeHTTP(w, r)
		case BackConnectControlPlaneServiceFetchBrowserProcedure:
			backConnectControlPlaneServiceFetchBrowserHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackConnectControlPlaneServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackConnectControlPlaneServiceHandler struct{}

func (UnimplementedBackConnectControlPlaneServiceHandler) Register(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceRegisterRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceRegisterResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("browsermanager.backconnect.v1.BackConnectControlPlaneService.Register is not implemented"))
}

func (UnimplementedBackConnectControlPlaneServiceHandler) FetchWorker(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceFetchWorkerRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceFetchWorkerResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("browsermanager.backconnect.v1.BackConnectControlPlaneService.FetchWorker is not implemented"))
}

func (UnimplementedBackConnectControlPlaneServiceHandler) AddProxy(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceAddProxyRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceAddProxyResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("browsermanager.backconnect.v1.BackConnectControlPlaneService.AddProxy is not implemented"))
}

func (UnimplementedBackConnectControlPlaneServiceHandler) RemoveProxy(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceRemoveProxyRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceRemoveProxyResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("browsermanager.backconnect.v1.BackConnectControlPlaneService.RemoveProxy is not implemented"))
}

func (UnimplementedBackConnectControlPlaneServiceHandler) FetchBrowser(context.Context, *connect.Request[v1.BackConnectControlPlaneServiceFetchBrowserRequest]) (*connect.Response[v1.BackConnectControlPlaneServiceFetchBrowserResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("browsermanager.backconnect.v1.BackConnectControlPlaneService.FetchBrowser is not implemented"))
}
