// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: browsermanager/backconnect/v1/worker.proto

package backconnectv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/browsermanager/backconnect/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// BackConnectWorkerServiceName is the fully-qualified name of the BackConnectWorkerService service.
	BackConnectWorkerServiceName = "browsermanager.backconnect.v1.BackConnectWorkerService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// BackConnectWorkerServiceAddProxyProcedure is the fully-qualified name of the
	// BackConnectWorkerService's AddProxy RPC.
	BackConnectWorkerServiceAddProxyProcedure = "/browsermanager.backconnect.v1.BackConnectWorkerService/AddProxy"
	// BackConnectWorkerServiceRemoveProxyProcedure is the fully-qualified name of the
	// BackConnectWorkerService's RemoveProxy RPC.
	BackConnectWorkerServiceRemoveProxyProcedure = "/browsermanager.backconnect.v1.BackConnectWorkerService/RemoveProxy"
	// BackConnectWorkerServiceHealthCheckProcedure is the fully-qualified name of the
	// BackConnectWorkerService's HealthCheck RPC.
	BackConnectWorkerServiceHealthCheckProcedure = "/browsermanager.backconnect.v1.BackConnectWorkerService/HealthCheck"
)

// BackConnectWorkerServiceClient is a client for the
// browsermanager.backconnect.v1.BackConnectWorkerService service.
type BackConnectWorkerServiceClient interface {
	AddProxy(context.Context, *connect.Request[v1.BackConnectWorkerServiceAddProxyRequest]) (*connect.Response[v1.BackConnectWorkerServiceAddProxyResponse], error)
	RemoveProxy(context.Context, *connect.Request[v1.BackConnectWorkerServiceRemoveProxyRequest]) (*connect.Response[v1.BackConnectWorkerServiceRemoveProxyResponse], error)
	HealthCheck(context.Context, *connect.Request[v1.BackConnectWorkerServiceHealthCheckRequest]) (*connect.Response[v1.BackConnectWorkerServiceHealthCheckResponse], error)
}

// NewBackConnectWorkerServiceClient constructs a client for the
// browsermanager.backconnect.v1.BackConnectWorkerService service. By default, it uses the Connect
// protocol with the binary Protobuf Codec, asks for gzipped responses, and sends uncompressed
// requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewBackConnectWorkerServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) BackConnectWorkerServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	backConnectWorkerServiceMethods := v1.File_browsermanager_backconnect_v1_worker_proto.Services().ByName("BackConnectWorkerService").Methods()
	return &backConnectWorkerServiceClient{
		addProxy: connect.NewClient[v1.BackConnectWorkerServiceAddProxyRequest, v1.BackConnectWorkerServiceAddProxyResponse](
			httpClient,
			baseURL+BackConnectWorkerServiceAddProxyProcedure,
			connect.WithSchema(backConnectWorkerServiceMethods.ByName("AddProxy")),
			connect.WithClientOptions(opts...),
		),
		removeProxy: connect.NewClient[v1.BackConnectWorkerServiceRemoveProxyRequest, v1.BackConnectWorkerServiceRemoveProxyResponse](
			httpClient,
			baseURL+BackConnectWorkerServiceRemoveProxyProcedure,
			connect.WithSchema(backConnectWorkerServiceMethods.ByName("RemoveProxy")),
			connect.WithClientOptions(opts...),
		),
		healthCheck: connect.NewClient[v1.BackConnectWorkerServiceHealthCheckRequest, v1.BackConnectWorkerServiceHealthCheckResponse](
			httpClient,
			baseURL+BackConnectWorkerServiceHealthCheckProcedure,
			connect.WithSchema(backConnectWorkerServiceMethods.ByName("HealthCheck")),
			connect.WithClientOptions(opts...),
		),
	}
}

// backConnectWorkerServiceClient implements BackConnectWorkerServiceClient.
type backConnectWorkerServiceClient struct {
	addProxy    *connect.Client[v1.BackConnectWorkerServiceAddProxyRequest, v1.BackConnectWorkerServiceAddProxyResponse]
	removeProxy *connect.Client[v1.BackConnectWorkerServiceRemoveProxyRequest, v1.BackConnectWorkerServiceRemoveProxyResponse]
	healthCheck *connect.Client[v1.BackConnectWorkerServiceHealthCheckRequest, v1.BackConnectWorkerServiceHealthCheckResponse]
}

// AddProxy calls browsermanager.backconnect.v1.BackConnectWorkerService.AddProxy.
func (c *backConnectWorkerServiceClient) AddProxy(ctx context.Context, req *connect.Request[v1.BackConnectWorkerServiceAddProxyRequest]) (*connect.Response[v1.BackConnectWorkerServiceAddProxyResponse], error) {
	return c.addProxy.CallUnary(ctx, req)
}

// RemoveProxy calls browsermanager.backconnect.v1.BackConnectWorkerService.RemoveProxy.
func (c *backConnectWorkerServiceClient) RemoveProxy(ctx context.Context, req *connect.Request[v1.BackConnectWorkerServiceRemoveProxyRequest]) (*connect.Response[v1.BackConnectWorkerServiceRemoveProxyResponse], error) {
	return c.removeProxy.CallUnary(ctx, req)
}

// HealthCheck calls browsermanager.backconnect.v1.BackConnectWorkerService.HealthCheck.
func (c *backConnectWorkerServiceClient) HealthCheck(ctx context.Context, req *connect.Request[v1.BackConnectWorkerServiceHealthCheckRequest]) (*connect.Response[v1.BackConnectWorkerServiceHealthCheckResponse], error) {
	return c.healthCheck.CallUnary(ctx, req)
}

// BackConnectWorkerServiceHandler is an implementation of the
// browsermanager.backconnect.v1.BackConnectWorkerService service.
type BackConnectWorkerServiceHandler interface {
	AddProxy(context.Context, *connect.Request[v1.BackConnectWorkerServiceAddProxyRequest]) (*connect.Response[v1.BackConnectWorkerServiceAddProxyResponse], error)
	RemoveProxy(context.Context, *connect.Request[v1.BackConnectWorkerServiceRemoveProxyRequest]) (*connect.Response[v1.BackConnectWorkerServiceRemoveProxyResponse], error)
	HealthCheck(context.Context, *connect.Request[v1.BackConnectWorkerServiceHealthCheckRequest]) (*connect.Response[v1.BackConnectWorkerServiceHealthCheckResponse], error)
}

// NewBackConnectWorkerServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewBackConnectWorkerServiceHandler(svc BackConnectWorkerServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	backConnectWorkerServiceMethods := v1.File_browsermanager_backconnect_v1_worker_proto.Services().ByName("BackConnectWorkerService").Methods()
	backConnectWorkerServiceAddProxyHandler := connect.NewUnaryHandler(
		BackConnectWorkerServiceAddProxyProcedure,
		svc.AddProxy,
		connect.WithSchema(backConnectWorkerServiceMethods.ByName("AddProxy")),
		connect.WithHandlerOptions(opts...),
	)
	backConnectWorkerServiceRemoveProxyHandler := connect.NewUnaryHandler(
		BackConnectWorkerServiceRemoveProxyProcedure,
		svc.RemoveProxy,
		connect.WithSchema(backConnectWorkerServiceMethods.ByName("RemoveProxy")),
		connect.WithHandlerOptions(opts...),
	)
	backConnectWorkerServiceHealthCheckHandler := connect.NewUnaryHandler(
		BackConnectWorkerServiceHealthCheckProcedure,
		svc.HealthCheck,
		connect.WithSchema(backConnectWorkerServiceMethods.ByName("HealthCheck")),
		connect.WithHandlerOptions(opts...),
	)
	return "/browsermanager.backconnect.v1.BackConnectWorkerService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case BackConnectWorkerServiceAddProxyProcedure:
			backConnectWorkerServiceAddProxyHandler.ServeHTTP(w, r)
		case BackConnectWorkerServiceRemoveProxyProcedure:
			backConnectWorkerServiceRemoveProxyHandler.ServeHTTP(w, r)
		case BackConnectWorkerServiceHealthCheckProcedure:
			backConnectWorkerServiceHealthCheckHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedBackConnectWorkerServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedBackConnectWorkerServiceHandler struct{}

func (UnimplementedBackConnectWorkerServiceHandler) AddProxy(context.Context, *connect.Request[v1.BackConnectWorkerServiceAddProxyRequest]) (*connect.Response[v1.BackConnectWorkerServiceAddProxyResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("browsermanager.backconnect.v1.BackConnectWorkerService.AddProxy is not implemented"))
}

func (UnimplementedBackConnectWorkerServiceHandler) RemoveProxy(context.Context, *connect.Request[v1.BackConnectWorkerServiceRemoveProxyRequest]) (*connect.Response[v1.BackConnectWorkerServiceRemoveProxyResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("browsermanager.backconnect.v1.BackConnectWorkerService.RemoveProxy is not implemented"))
}

func (UnimplementedBackConnectWorkerServiceHandler) HealthCheck(context.Context, *connect.Request[v1.BackConnectWorkerServiceHealthCheckRequest]) (*connect.Response[v1.BackConnectWorkerServiceHealthCheckResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("browsermanager.backconnect.v1.BackConnectWorkerService.HealthCheck is not implemented"))
}
