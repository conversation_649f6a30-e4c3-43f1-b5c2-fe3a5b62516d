// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: browsermanager/backconnect/v1/backoffice.proto

package backconnectv1

import (
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackConnectBackofficeServiceFetchWorkerRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v1.PaginationRequest  `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectBackofficeServiceFetchWorkerRequest) Reset() {
	*x = BackConnectBackofficeServiceFetchWorkerRequest{}
	mi := &file_browsermanager_backconnect_v1_backoffice_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectBackofficeServiceFetchWorkerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectBackofficeServiceFetchWorkerRequest) ProtoMessage() {}

func (x *BackConnectBackofficeServiceFetchWorkerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_backoffice_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectBackofficeServiceFetchWorkerRequest.ProtoReflect.Descriptor instead.
func (*BackConnectBackofficeServiceFetchWorkerRequest) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{0}
}

func (x *BackConnectBackofficeServiceFetchWorkerRequest) GetPagination() *v1.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackConnectBackofficeServiceFetchWorkerResponse struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	Error         *v11.ErrorMessage              `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v1.PaginationResponse         `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Items         []*BackConnectBackofficeWorker `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectBackofficeServiceFetchWorkerResponse) Reset() {
	*x = BackConnectBackofficeServiceFetchWorkerResponse{}
	mi := &file_browsermanager_backconnect_v1_backoffice_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectBackofficeServiceFetchWorkerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectBackofficeServiceFetchWorkerResponse) ProtoMessage() {}

func (x *BackConnectBackofficeServiceFetchWorkerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_backoffice_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectBackofficeServiceFetchWorkerResponse.ProtoReflect.Descriptor instead.
func (*BackConnectBackofficeServiceFetchWorkerResponse) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{1}
}

func (x *BackConnectBackofficeServiceFetchWorkerResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackConnectBackofficeServiceFetchWorkerResponse) GetPagination() *v1.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackConnectBackofficeServiceFetchWorkerResponse) GetItems() []*BackConnectBackofficeWorker {
	if x != nil {
		return x.Items
	}
	return nil
}

type BackConnectBackofficeServiceAddProxyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdWorker      string                 `protobuf:"bytes,1,opt,name=id_worker,json=idWorker,proto3" json:"id_worker,omitempty"`
	Server        string                 `protobuf:"bytes,2,opt,name=server,proto3" json:"server,omitempty"`
	Username      string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectBackofficeServiceAddProxyRequest) Reset() {
	*x = BackConnectBackofficeServiceAddProxyRequest{}
	mi := &file_browsermanager_backconnect_v1_backoffice_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectBackofficeServiceAddProxyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectBackofficeServiceAddProxyRequest) ProtoMessage() {}

func (x *BackConnectBackofficeServiceAddProxyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_backoffice_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectBackofficeServiceAddProxyRequest.ProtoReflect.Descriptor instead.
func (*BackConnectBackofficeServiceAddProxyRequest) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{2}
}

func (x *BackConnectBackofficeServiceAddProxyRequest) GetIdWorker() string {
	if x != nil {
		return x.IdWorker
	}
	return ""
}

func (x *BackConnectBackofficeServiceAddProxyRequest) GetServer() string {
	if x != nil {
		return x.Server
	}
	return ""
}

func (x *BackConnectBackofficeServiceAddProxyRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *BackConnectBackofficeServiceAddProxyRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type BackConnectBackofficeServiceAddProxyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectBackofficeServiceAddProxyResponse) Reset() {
	*x = BackConnectBackofficeServiceAddProxyResponse{}
	mi := &file_browsermanager_backconnect_v1_backoffice_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectBackofficeServiceAddProxyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectBackofficeServiceAddProxyResponse) ProtoMessage() {}

func (x *BackConnectBackofficeServiceAddProxyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_backoffice_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectBackofficeServiceAddProxyResponse.ProtoReflect.Descriptor instead.
func (*BackConnectBackofficeServiceAddProxyResponse) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{3}
}

func (x *BackConnectBackofficeServiceAddProxyResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackConnectBackofficeServiceRemoveProxyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdWorker      string                 `protobuf:"bytes,1,opt,name=id_worker,json=idWorker,proto3" json:"id_worker,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectBackofficeServiceRemoveProxyRequest) Reset() {
	*x = BackConnectBackofficeServiceRemoveProxyRequest{}
	mi := &file_browsermanager_backconnect_v1_backoffice_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectBackofficeServiceRemoveProxyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectBackofficeServiceRemoveProxyRequest) ProtoMessage() {}

func (x *BackConnectBackofficeServiceRemoveProxyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_backoffice_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectBackofficeServiceRemoveProxyRequest.ProtoReflect.Descriptor instead.
func (*BackConnectBackofficeServiceRemoveProxyRequest) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{4}
}

func (x *BackConnectBackofficeServiceRemoveProxyRequest) GetIdWorker() string {
	if x != nil {
		return x.IdWorker
	}
	return ""
}

type BackConnectBackofficeServiceRemoveProxyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v11.ErrorMessage      `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectBackofficeServiceRemoveProxyResponse) Reset() {
	*x = BackConnectBackofficeServiceRemoveProxyResponse{}
	mi := &file_browsermanager_backconnect_v1_backoffice_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectBackofficeServiceRemoveProxyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectBackofficeServiceRemoveProxyResponse) ProtoMessage() {}

func (x *BackConnectBackofficeServiceRemoveProxyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_backoffice_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectBackofficeServiceRemoveProxyResponse.ProtoReflect.Descriptor instead.
func (*BackConnectBackofficeServiceRemoveProxyResponse) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{5}
}

func (x *BackConnectBackofficeServiceRemoveProxyResponse) GetError() *v11.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackConnectBackofficeWorker struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	IdWorker         string                 `protobuf:"bytes,1,opt,name=id_worker,json=idWorker,proto3" json:"id_worker,omitempty"`
	NameWorker       string                 `protobuf:"bytes,2,opt,name=name_worker,json=nameWorker,proto3" json:"name_worker,omitempty"`
	UrlWorker        string                 `protobuf:"bytes,3,opt,name=url_worker,json=urlWorker,proto3" json:"url_worker,omitempty"`
	IsHealthy        bool                   `protobuf:"varint,4,opt,name=is_healthy,json=isHealthy,proto3" json:"is_healthy,omitempty"`
	RegisterLastTime bool                   `protobuf:"varint,5,opt,name=register_last_time,json=registerLastTime,proto3" json:"register_last_time,omitempty"`
	UsedBy           string                 `protobuf:"bytes,6,opt,name=used_by,json=usedBy,proto3" json:"used_by,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BackConnectBackofficeWorker) Reset() {
	*x = BackConnectBackofficeWorker{}
	mi := &file_browsermanager_backconnect_v1_backoffice_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectBackofficeWorker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectBackofficeWorker) ProtoMessage() {}

func (x *BackConnectBackofficeWorker) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_backoffice_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectBackofficeWorker.ProtoReflect.Descriptor instead.
func (*BackConnectBackofficeWorker) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_backoffice_proto_rawDescGZIP(), []int{6}
}

func (x *BackConnectBackofficeWorker) GetIdWorker() string {
	if x != nil {
		return x.IdWorker
	}
	return ""
}

func (x *BackConnectBackofficeWorker) GetNameWorker() string {
	if x != nil {
		return x.NameWorker
	}
	return ""
}

func (x *BackConnectBackofficeWorker) GetUrlWorker() string {
	if x != nil {
		return x.UrlWorker
	}
	return ""
}

func (x *BackConnectBackofficeWorker) GetIsHealthy() bool {
	if x != nil {
		return x.IsHealthy
	}
	return false
}

func (x *BackConnectBackofficeWorker) GetRegisterLastTime() bool {
	if x != nil {
		return x.RegisterLastTime
	}
	return false
}

func (x *BackConnectBackofficeWorker) GetUsedBy() string {
	if x != nil {
		return x.UsedBy
	}
	return ""
}

var File_browsermanager_backconnect_v1_backoffice_proto protoreflect.FileDescriptor

const file_browsermanager_backconnect_v1_backoffice_proto_rawDesc = "" +
	"\n" +
	".browsermanager/backconnect/v1/backoffice.proto\x12\x1dbrowsermanager.backconnect.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\"m\n" +
	".BackConnectBackofficeServiceFetchWorkerRequest\x12;\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xf0\x01\n" +
	"/BackConnectBackofficeServiceFetchWorkerResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12P\n" +
	"\x05items\x18\x03 \x03(\v2:.browsermanager.backconnect.v1.BackConnectBackofficeWorkerR\x05items\"\x9a\x01\n" +
	"+BackConnectBackofficeServiceAddProxyRequest\x12\x1b\n" +
	"\tid_worker\x18\x01 \x01(\tR\bidWorker\x12\x16\n" +
	"\x06server\x18\x02 \x01(\tR\x06server\x12\x1a\n" +
	"\busername\x18\x03 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x04 \x01(\tR\bpassword\"]\n" +
	",BackConnectBackofficeServiceAddProxyResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"M\n" +
	".BackConnectBackofficeServiceRemoveProxyRequest\x12\x1b\n" +
	"\tid_worker\x18\x01 \x01(\tR\bidWorker\"`\n" +
	"/BackConnectBackofficeServiceRemoveProxyResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"\xe0\x01\n" +
	"\x1bBackConnectBackofficeWorker\x12\x1b\n" +
	"\tid_worker\x18\x01 \x01(\tR\bidWorker\x12\x1f\n" +
	"\vname_worker\x18\x02 \x01(\tR\n" +
	"nameWorker\x12\x1d\n" +
	"\n" +
	"url_worker\x18\x03 \x01(\tR\turlWorker\x12\x1d\n" +
	"\n" +
	"is_healthy\x18\x04 \x01(\bR\tisHealthy\x12,\n" +
	"\x12register_last_time\x18\x05 \x01(\bR\x10registerLastTime\x12\x17\n" +
	"\aused_by\x18\x06 \x01(\tR\x06usedBy2\xa2\x04\n" +
	"\x1cBackConnectBackofficeService\x12\xac\x01\n" +
	"\vFetchWorker\x12M.browsermanager.backconnect.v1.BackConnectBackofficeServiceFetchWorkerRequest\x1aN.browsermanager.backconnect.v1.BackConnectBackofficeServiceFetchWorkerResponse\x12\xa3\x01\n" +
	"\bAddProxy\x12J.browsermanager.backconnect.v1.BackConnectBackofficeServiceAddProxyRequest\x1aK.browsermanager.backconnect.v1.BackConnectBackofficeServiceAddProxyResponse\x12\xac\x01\n" +
	"\vRemoveProxy\x12M.browsermanager.backconnect.v1.BackConnectBackofficeServiceRemoveProxyRequest\x1aN.browsermanager.backconnect.v1.BackConnectBackofficeServiceRemoveProxyResponseB\\ZZgit.tmproxy-infra.com/algo/algoproxy-proto/gen/browsermanager/backconnect/v1;backconnectv1b\x06proto3"

var (
	file_browsermanager_backconnect_v1_backoffice_proto_rawDescOnce sync.Once
	file_browsermanager_backconnect_v1_backoffice_proto_rawDescData []byte
)

func file_browsermanager_backconnect_v1_backoffice_proto_rawDescGZIP() []byte {
	file_browsermanager_backconnect_v1_backoffice_proto_rawDescOnce.Do(func() {
		file_browsermanager_backconnect_v1_backoffice_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_browsermanager_backconnect_v1_backoffice_proto_rawDesc), len(file_browsermanager_backconnect_v1_backoffice_proto_rawDesc)))
	})
	return file_browsermanager_backconnect_v1_backoffice_proto_rawDescData
}

var file_browsermanager_backconnect_v1_backoffice_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_browsermanager_backconnect_v1_backoffice_proto_goTypes = []any{
	(*BackConnectBackofficeServiceFetchWorkerRequest)(nil),  // 0: browsermanager.backconnect.v1.BackConnectBackofficeServiceFetchWorkerRequest
	(*BackConnectBackofficeServiceFetchWorkerResponse)(nil), // 1: browsermanager.backconnect.v1.BackConnectBackofficeServiceFetchWorkerResponse
	(*BackConnectBackofficeServiceAddProxyRequest)(nil),     // 2: browsermanager.backconnect.v1.BackConnectBackofficeServiceAddProxyRequest
	(*BackConnectBackofficeServiceAddProxyResponse)(nil),    // 3: browsermanager.backconnect.v1.BackConnectBackofficeServiceAddProxyResponse
	(*BackConnectBackofficeServiceRemoveProxyRequest)(nil),  // 4: browsermanager.backconnect.v1.BackConnectBackofficeServiceRemoveProxyRequest
	(*BackConnectBackofficeServiceRemoveProxyResponse)(nil), // 5: browsermanager.backconnect.v1.BackConnectBackofficeServiceRemoveProxyResponse
	(*BackConnectBackofficeWorker)(nil),                     // 6: browsermanager.backconnect.v1.BackConnectBackofficeWorker
	(*v1.PaginationRequest)(nil),                            // 7: utils.v1.PaginationRequest
	(*v11.ErrorMessage)(nil),                                // 8: errmsg.v1.ErrorMessage
	(*v1.PaginationResponse)(nil),                           // 9: utils.v1.PaginationResponse
}
var file_browsermanager_backconnect_v1_backoffice_proto_depIdxs = []int32{
	7, // 0: browsermanager.backconnect.v1.BackConnectBackofficeServiceFetchWorkerRequest.pagination:type_name -> utils.v1.PaginationRequest
	8, // 1: browsermanager.backconnect.v1.BackConnectBackofficeServiceFetchWorkerResponse.error:type_name -> errmsg.v1.ErrorMessage
	9, // 2: browsermanager.backconnect.v1.BackConnectBackofficeServiceFetchWorkerResponse.pagination:type_name -> utils.v1.PaginationResponse
	6, // 3: browsermanager.backconnect.v1.BackConnectBackofficeServiceFetchWorkerResponse.items:type_name -> browsermanager.backconnect.v1.BackConnectBackofficeWorker
	8, // 4: browsermanager.backconnect.v1.BackConnectBackofficeServiceAddProxyResponse.error:type_name -> errmsg.v1.ErrorMessage
	8, // 5: browsermanager.backconnect.v1.BackConnectBackofficeServiceRemoveProxyResponse.error:type_name -> errmsg.v1.ErrorMessage
	0, // 6: browsermanager.backconnect.v1.BackConnectBackofficeService.FetchWorker:input_type -> browsermanager.backconnect.v1.BackConnectBackofficeServiceFetchWorkerRequest
	2, // 7: browsermanager.backconnect.v1.BackConnectBackofficeService.AddProxy:input_type -> browsermanager.backconnect.v1.BackConnectBackofficeServiceAddProxyRequest
	4, // 8: browsermanager.backconnect.v1.BackConnectBackofficeService.RemoveProxy:input_type -> browsermanager.backconnect.v1.BackConnectBackofficeServiceRemoveProxyRequest
	1, // 9: browsermanager.backconnect.v1.BackConnectBackofficeService.FetchWorker:output_type -> browsermanager.backconnect.v1.BackConnectBackofficeServiceFetchWorkerResponse
	3, // 10: browsermanager.backconnect.v1.BackConnectBackofficeService.AddProxy:output_type -> browsermanager.backconnect.v1.BackConnectBackofficeServiceAddProxyResponse
	5, // 11: browsermanager.backconnect.v1.BackConnectBackofficeService.RemoveProxy:output_type -> browsermanager.backconnect.v1.BackConnectBackofficeServiceRemoveProxyResponse
	9, // [9:12] is the sub-list for method output_type
	6, // [6:9] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_browsermanager_backconnect_v1_backoffice_proto_init() }
func file_browsermanager_backconnect_v1_backoffice_proto_init() {
	if File_browsermanager_backconnect_v1_backoffice_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_browsermanager_backconnect_v1_backoffice_proto_rawDesc), len(file_browsermanager_backconnect_v1_backoffice_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_browsermanager_backconnect_v1_backoffice_proto_goTypes,
		DependencyIndexes: file_browsermanager_backconnect_v1_backoffice_proto_depIdxs,
		MessageInfos:      file_browsermanager_backconnect_v1_backoffice_proto_msgTypes,
	}.Build()
	File_browsermanager_backconnect_v1_backoffice_proto = out.File
	file_browsermanager_backconnect_v1_backoffice_proto_goTypes = nil
	file_browsermanager_backconnect_v1_backoffice_proto_depIdxs = nil
}
