// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: browsermanager/backconnect/v1/controlplane.proto

package backconnectv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	v11 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackConnectControlPlaneServiceRegisterRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NameWorker    string                 `protobuf:"bytes,1,opt,name=name_worker,json=nameWorker,proto3" json:"name_worker,omitempty"`
	UrlWorker     string                 `protobuf:"bytes,2,opt,name=url_worker,json=urlWorker,proto3" json:"url_worker,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectControlPlaneServiceRegisterRequest) Reset() {
	*x = BackConnectControlPlaneServiceRegisterRequest{}
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectControlPlaneServiceRegisterRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectControlPlaneServiceRegisterRequest) ProtoMessage() {}

func (x *BackConnectControlPlaneServiceRegisterRequest) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectControlPlaneServiceRegisterRequest.ProtoReflect.Descriptor instead.
func (*BackConnectControlPlaneServiceRegisterRequest) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_controlplane_proto_rawDescGZIP(), []int{0}
}

func (x *BackConnectControlPlaneServiceRegisterRequest) GetNameWorker() string {
	if x != nil {
		return x.NameWorker
	}
	return ""
}

func (x *BackConnectControlPlaneServiceRegisterRequest) GetUrlWorker() string {
	if x != nil {
		return x.UrlWorker
	}
	return ""
}

type BackConnectControlPlaneServiceRegisterResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectControlPlaneServiceRegisterResponse) Reset() {
	*x = BackConnectControlPlaneServiceRegisterResponse{}
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectControlPlaneServiceRegisterResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectControlPlaneServiceRegisterResponse) ProtoMessage() {}

func (x *BackConnectControlPlaneServiceRegisterResponse) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectControlPlaneServiceRegisterResponse.ProtoReflect.Descriptor instead.
func (*BackConnectControlPlaneServiceRegisterResponse) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_controlplane_proto_rawDescGZIP(), []int{1}
}

func (x *BackConnectControlPlaneServiceRegisterResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackConnectControlPlaneServiceFetchWorkerRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pagination    *v11.PaginationRequest `protobuf:"bytes,1,opt,name=pagination,proto3" json:"pagination,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectControlPlaneServiceFetchWorkerRequest) Reset() {
	*x = BackConnectControlPlaneServiceFetchWorkerRequest{}
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectControlPlaneServiceFetchWorkerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectControlPlaneServiceFetchWorkerRequest) ProtoMessage() {}

func (x *BackConnectControlPlaneServiceFetchWorkerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectControlPlaneServiceFetchWorkerRequest.ProtoReflect.Descriptor instead.
func (*BackConnectControlPlaneServiceFetchWorkerRequest) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_controlplane_proto_rawDescGZIP(), []int{2}
}

func (x *BackConnectControlPlaneServiceFetchWorkerRequest) GetPagination() *v11.PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

type BackConnectControlPlaneServiceFetchWorkerResponse struct {
	state         protoimpl.MessageState           `protogen:"open.v1"`
	Error         *v1.ErrorMessage                 `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Pagination    *v11.PaginationResponse          `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"`
	Items         []*BackConnectControlPlaneWorker `protobuf:"bytes,3,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectControlPlaneServiceFetchWorkerResponse) Reset() {
	*x = BackConnectControlPlaneServiceFetchWorkerResponse{}
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectControlPlaneServiceFetchWorkerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectControlPlaneServiceFetchWorkerResponse) ProtoMessage() {}

func (x *BackConnectControlPlaneServiceFetchWorkerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectControlPlaneServiceFetchWorkerResponse.ProtoReflect.Descriptor instead.
func (*BackConnectControlPlaneServiceFetchWorkerResponse) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_controlplane_proto_rawDescGZIP(), []int{3}
}

func (x *BackConnectControlPlaneServiceFetchWorkerResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackConnectControlPlaneServiceFetchWorkerResponse) GetPagination() *v11.PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *BackConnectControlPlaneServiceFetchWorkerResponse) GetItems() []*BackConnectControlPlaneWorker {
	if x != nil {
		return x.Items
	}
	return nil
}

type BackConnectControlPlaneServiceAddProxyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdWorker      string                 `protobuf:"bytes,1,opt,name=id_worker,json=idWorker,proto3" json:"id_worker,omitempty"`
	Server        string                 `protobuf:"bytes,2,opt,name=server,proto3" json:"server,omitempty"`
	Username      string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectControlPlaneServiceAddProxyRequest) Reset() {
	*x = BackConnectControlPlaneServiceAddProxyRequest{}
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectControlPlaneServiceAddProxyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectControlPlaneServiceAddProxyRequest) ProtoMessage() {}

func (x *BackConnectControlPlaneServiceAddProxyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectControlPlaneServiceAddProxyRequest.ProtoReflect.Descriptor instead.
func (*BackConnectControlPlaneServiceAddProxyRequest) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_controlplane_proto_rawDescGZIP(), []int{4}
}

func (x *BackConnectControlPlaneServiceAddProxyRequest) GetIdWorker() string {
	if x != nil {
		return x.IdWorker
	}
	return ""
}

func (x *BackConnectControlPlaneServiceAddProxyRequest) GetServer() string {
	if x != nil {
		return x.Server
	}
	return ""
}

func (x *BackConnectControlPlaneServiceAddProxyRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *BackConnectControlPlaneServiceAddProxyRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type BackConnectControlPlaneServiceAddProxyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectControlPlaneServiceAddProxyResponse) Reset() {
	*x = BackConnectControlPlaneServiceAddProxyResponse{}
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectControlPlaneServiceAddProxyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectControlPlaneServiceAddProxyResponse) ProtoMessage() {}

func (x *BackConnectControlPlaneServiceAddProxyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectControlPlaneServiceAddProxyResponse.ProtoReflect.Descriptor instead.
func (*BackConnectControlPlaneServiceAddProxyResponse) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_controlplane_proto_rawDescGZIP(), []int{5}
}

func (x *BackConnectControlPlaneServiceAddProxyResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackConnectControlPlaneServiceRemoveProxyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IdWorker      string                 `protobuf:"bytes,1,opt,name=id_worker,json=idWorker,proto3" json:"id_worker,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectControlPlaneServiceRemoveProxyRequest) Reset() {
	*x = BackConnectControlPlaneServiceRemoveProxyRequest{}
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectControlPlaneServiceRemoveProxyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectControlPlaneServiceRemoveProxyRequest) ProtoMessage() {}

func (x *BackConnectControlPlaneServiceRemoveProxyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectControlPlaneServiceRemoveProxyRequest.ProtoReflect.Descriptor instead.
func (*BackConnectControlPlaneServiceRemoveProxyRequest) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_controlplane_proto_rawDescGZIP(), []int{6}
}

func (x *BackConnectControlPlaneServiceRemoveProxyRequest) GetIdWorker() string {
	if x != nil {
		return x.IdWorker
	}
	return ""
}

type BackConnectControlPlaneServiceRemoveProxyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectControlPlaneServiceRemoveProxyResponse) Reset() {
	*x = BackConnectControlPlaneServiceRemoveProxyResponse{}
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectControlPlaneServiceRemoveProxyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectControlPlaneServiceRemoveProxyResponse) ProtoMessage() {}

func (x *BackConnectControlPlaneServiceRemoveProxyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectControlPlaneServiceRemoveProxyResponse.ProtoReflect.Descriptor instead.
func (*BackConnectControlPlaneServiceRemoveProxyResponse) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_controlplane_proto_rawDescGZIP(), []int{7}
}

func (x *BackConnectControlPlaneServiceRemoveProxyResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackConnectControlPlaneServiceFetchBrowserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectControlPlaneServiceFetchBrowserRequest) Reset() {
	*x = BackConnectControlPlaneServiceFetchBrowserRequest{}
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectControlPlaneServiceFetchBrowserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectControlPlaneServiceFetchBrowserRequest) ProtoMessage() {}

func (x *BackConnectControlPlaneServiceFetchBrowserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectControlPlaneServiceFetchBrowserRequest.ProtoReflect.Descriptor instead.
func (*BackConnectControlPlaneServiceFetchBrowserRequest) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_controlplane_proto_rawDescGZIP(), []int{8}
}

type BackConnectControlPlaneServiceFetchBrowserResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	Cdp           string                 `protobuf:"bytes,2,opt,name=cdp,proto3" json:"cdp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectControlPlaneServiceFetchBrowserResponse) Reset() {
	*x = BackConnectControlPlaneServiceFetchBrowserResponse{}
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectControlPlaneServiceFetchBrowserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectControlPlaneServiceFetchBrowserResponse) ProtoMessage() {}

func (x *BackConnectControlPlaneServiceFetchBrowserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectControlPlaneServiceFetchBrowserResponse.ProtoReflect.Descriptor instead.
func (*BackConnectControlPlaneServiceFetchBrowserResponse) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_controlplane_proto_rawDescGZIP(), []int{9}
}

func (x *BackConnectControlPlaneServiceFetchBrowserResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *BackConnectControlPlaneServiceFetchBrowserResponse) GetCdp() string {
	if x != nil {
		return x.Cdp
	}
	return ""
}

type BackConnectControlPlaneWorker struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	IdWorker         string                 `protobuf:"bytes,1,opt,name=id_worker,json=idWorker,proto3" json:"id_worker,omitempty"`
	NameWorker       string                 `protobuf:"bytes,2,opt,name=name_worker,json=nameWorker,proto3" json:"name_worker,omitempty"`
	UrlWorker        string                 `protobuf:"bytes,3,opt,name=url_worker,json=urlWorker,proto3" json:"url_worker,omitempty"`
	IsHealthy        bool                   `protobuf:"varint,4,opt,name=is_healthy,json=isHealthy,proto3" json:"is_healthy,omitempty"`
	RegisterLastTime bool                   `protobuf:"varint,5,opt,name=register_last_time,json=registerLastTime,proto3" json:"register_last_time,omitempty"`
	UsedBy           string                 `protobuf:"bytes,6,opt,name=used_by,json=usedBy,proto3" json:"used_by,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *BackConnectControlPlaneWorker) Reset() {
	*x = BackConnectControlPlaneWorker{}
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectControlPlaneWorker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectControlPlaneWorker) ProtoMessage() {}

func (x *BackConnectControlPlaneWorker) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_controlplane_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectControlPlaneWorker.ProtoReflect.Descriptor instead.
func (*BackConnectControlPlaneWorker) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_controlplane_proto_rawDescGZIP(), []int{10}
}

func (x *BackConnectControlPlaneWorker) GetIdWorker() string {
	if x != nil {
		return x.IdWorker
	}
	return ""
}

func (x *BackConnectControlPlaneWorker) GetNameWorker() string {
	if x != nil {
		return x.NameWorker
	}
	return ""
}

func (x *BackConnectControlPlaneWorker) GetUrlWorker() string {
	if x != nil {
		return x.UrlWorker
	}
	return ""
}

func (x *BackConnectControlPlaneWorker) GetIsHealthy() bool {
	if x != nil {
		return x.IsHealthy
	}
	return false
}

func (x *BackConnectControlPlaneWorker) GetRegisterLastTime() bool {
	if x != nil {
		return x.RegisterLastTime
	}
	return false
}

func (x *BackConnectControlPlaneWorker) GetUsedBy() string {
	if x != nil {
		return x.UsedBy
	}
	return ""
}

var File_browsermanager_backconnect_v1_controlplane_proto protoreflect.FileDescriptor

const file_browsermanager_backconnect_v1_controlplane_proto_rawDesc = "" +
	"\n" +
	"0browsermanager/backconnect/v1/controlplane.proto\x12\x1dbrowsermanager.backconnect.v1\x1a\x18errmsg/v1/errormsg.proto\x1a\x14utils/v1/utils.proto\"o\n" +
	"-BackConnectControlPlaneServiceRegisterRequest\x12\x1f\n" +
	"\vname_worker\x18\x01 \x01(\tR\n" +
	"nameWorker\x12\x1d\n" +
	"\n" +
	"url_worker\x18\x02 \x01(\tR\turlWorker\"_\n" +
	".BackConnectControlPlaneServiceRegisterResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"o\n" +
	"0BackConnectControlPlaneServiceFetchWorkerRequest\x12;\n" +
	"\n" +
	"pagination\x18\x01 \x01(\v2\x1b.utils.v1.PaginationRequestR\n" +
	"pagination\"\xf4\x01\n" +
	"1BackConnectControlPlaneServiceFetchWorkerResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12<\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2\x1c.utils.v1.PaginationResponseR\n" +
	"pagination\x12R\n" +
	"\x05items\x18\x03 \x03(\v2<.browsermanager.backconnect.v1.BackConnectControlPlaneWorkerR\x05items\"\x9c\x01\n" +
	"-BackConnectControlPlaneServiceAddProxyRequest\x12\x1b\n" +
	"\tid_worker\x18\x01 \x01(\tR\bidWorker\x12\x16\n" +
	"\x06server\x18\x02 \x01(\tR\x06server\x12\x1a\n" +
	"\busername\x18\x03 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x04 \x01(\tR\bpassword\"_\n" +
	".BackConnectControlPlaneServiceAddProxyResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"O\n" +
	"0BackConnectControlPlaneServiceRemoveProxyRequest\x12\x1b\n" +
	"\tid_worker\x18\x01 \x01(\tR\bidWorker\"b\n" +
	"1BackConnectControlPlaneServiceRemoveProxyResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\"3\n" +
	"1BackConnectControlPlaneServiceFetchBrowserRequest\"u\n" +
	"2BackConnectControlPlaneServiceFetchBrowserResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\x12\x10\n" +
	"\x03cdp\x18\x02 \x01(\tR\x03cdp\"\xe2\x01\n" +
	"\x1dBackConnectControlPlaneWorker\x12\x1b\n" +
	"\tid_worker\x18\x01 \x01(\tR\bidWorker\x12\x1f\n" +
	"\vname_worker\x18\x02 \x01(\tR\n" +
	"nameWorker\x12\x1d\n" +
	"\n" +
	"url_worker\x18\x03 \x01(\tR\turlWorker\x12\x1d\n" +
	"\n" +
	"is_healthy\x18\x04 \x01(\bR\tisHealthy\x12,\n" +
	"\x12register_last_time\x18\x05 \x01(\bR\x10registerLastTime\x12\x17\n" +
	"\aused_by\x18\x06 \x01(\tR\x06usedBy2\x90\a\n" +
	"\x1eBackConnectControlPlaneService\x12\xa7\x01\n" +
	"\bRegister\x12L.browsermanager.backconnect.v1.BackConnectControlPlaneServiceRegisterRequest\x1aM.browsermanager.backconnect.v1.BackConnectControlPlaneServiceRegisterResponse\x12\xb0\x01\n" +
	"\vFetchWorker\x12O.browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchWorkerRequest\x1aP.browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchWorkerResponse\x12\xa7\x01\n" +
	"\bAddProxy\x12L.browsermanager.backconnect.v1.BackConnectControlPlaneServiceAddProxyRequest\x1aM.browsermanager.backconnect.v1.BackConnectControlPlaneServiceAddProxyResponse\x12\xb0\x01\n" +
	"\vRemoveProxy\x12O.browsermanager.backconnect.v1.BackConnectControlPlaneServiceRemoveProxyRequest\x1aP.browsermanager.backconnect.v1.BackConnectControlPlaneServiceRemoveProxyResponse\x12\xb3\x01\n" +
	"\fFetchBrowser\x12P.browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchBrowserRequest\x1aQ.browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchBrowserResponseB\\ZZgit.tmproxy-infra.com/algo/algoproxy-proto/gen/browsermanager/backconnect/v1;backconnectv1b\x06proto3"

var (
	file_browsermanager_backconnect_v1_controlplane_proto_rawDescOnce sync.Once
	file_browsermanager_backconnect_v1_controlplane_proto_rawDescData []byte
)

func file_browsermanager_backconnect_v1_controlplane_proto_rawDescGZIP() []byte {
	file_browsermanager_backconnect_v1_controlplane_proto_rawDescOnce.Do(func() {
		file_browsermanager_backconnect_v1_controlplane_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_browsermanager_backconnect_v1_controlplane_proto_rawDesc), len(file_browsermanager_backconnect_v1_controlplane_proto_rawDesc)))
	})
	return file_browsermanager_backconnect_v1_controlplane_proto_rawDescData
}

var file_browsermanager_backconnect_v1_controlplane_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_browsermanager_backconnect_v1_controlplane_proto_goTypes = []any{
	(*BackConnectControlPlaneServiceRegisterRequest)(nil),      // 0: browsermanager.backconnect.v1.BackConnectControlPlaneServiceRegisterRequest
	(*BackConnectControlPlaneServiceRegisterResponse)(nil),     // 1: browsermanager.backconnect.v1.BackConnectControlPlaneServiceRegisterResponse
	(*BackConnectControlPlaneServiceFetchWorkerRequest)(nil),   // 2: browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchWorkerRequest
	(*BackConnectControlPlaneServiceFetchWorkerResponse)(nil),  // 3: browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchWorkerResponse
	(*BackConnectControlPlaneServiceAddProxyRequest)(nil),      // 4: browsermanager.backconnect.v1.BackConnectControlPlaneServiceAddProxyRequest
	(*BackConnectControlPlaneServiceAddProxyResponse)(nil),     // 5: browsermanager.backconnect.v1.BackConnectControlPlaneServiceAddProxyResponse
	(*BackConnectControlPlaneServiceRemoveProxyRequest)(nil),   // 6: browsermanager.backconnect.v1.BackConnectControlPlaneServiceRemoveProxyRequest
	(*BackConnectControlPlaneServiceRemoveProxyResponse)(nil),  // 7: browsermanager.backconnect.v1.BackConnectControlPlaneServiceRemoveProxyResponse
	(*BackConnectControlPlaneServiceFetchBrowserRequest)(nil),  // 8: browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchBrowserRequest
	(*BackConnectControlPlaneServiceFetchBrowserResponse)(nil), // 9: browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchBrowserResponse
	(*BackConnectControlPlaneWorker)(nil),                      // 10: browsermanager.backconnect.v1.BackConnectControlPlaneWorker
	(*v1.ErrorMessage)(nil),                                    // 11: errmsg.v1.ErrorMessage
	(*v11.PaginationRequest)(nil),                              // 12: utils.v1.PaginationRequest
	(*v11.PaginationResponse)(nil),                             // 13: utils.v1.PaginationResponse
}
var file_browsermanager_backconnect_v1_controlplane_proto_depIdxs = []int32{
	11, // 0: browsermanager.backconnect.v1.BackConnectControlPlaneServiceRegisterResponse.error:type_name -> errmsg.v1.ErrorMessage
	12, // 1: browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchWorkerRequest.pagination:type_name -> utils.v1.PaginationRequest
	11, // 2: browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchWorkerResponse.error:type_name -> errmsg.v1.ErrorMessage
	13, // 3: browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchWorkerResponse.pagination:type_name -> utils.v1.PaginationResponse
	10, // 4: browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchWorkerResponse.items:type_name -> browsermanager.backconnect.v1.BackConnectControlPlaneWorker
	11, // 5: browsermanager.backconnect.v1.BackConnectControlPlaneServiceAddProxyResponse.error:type_name -> errmsg.v1.ErrorMessage
	11, // 6: browsermanager.backconnect.v1.BackConnectControlPlaneServiceRemoveProxyResponse.error:type_name -> errmsg.v1.ErrorMessage
	11, // 7: browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchBrowserResponse.error:type_name -> errmsg.v1.ErrorMessage
	0,  // 8: browsermanager.backconnect.v1.BackConnectControlPlaneService.Register:input_type -> browsermanager.backconnect.v1.BackConnectControlPlaneServiceRegisterRequest
	2,  // 9: browsermanager.backconnect.v1.BackConnectControlPlaneService.FetchWorker:input_type -> browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchWorkerRequest
	4,  // 10: browsermanager.backconnect.v1.BackConnectControlPlaneService.AddProxy:input_type -> browsermanager.backconnect.v1.BackConnectControlPlaneServiceAddProxyRequest
	6,  // 11: browsermanager.backconnect.v1.BackConnectControlPlaneService.RemoveProxy:input_type -> browsermanager.backconnect.v1.BackConnectControlPlaneServiceRemoveProxyRequest
	8,  // 12: browsermanager.backconnect.v1.BackConnectControlPlaneService.FetchBrowser:input_type -> browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchBrowserRequest
	1,  // 13: browsermanager.backconnect.v1.BackConnectControlPlaneService.Register:output_type -> browsermanager.backconnect.v1.BackConnectControlPlaneServiceRegisterResponse
	3,  // 14: browsermanager.backconnect.v1.BackConnectControlPlaneService.FetchWorker:output_type -> browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchWorkerResponse
	5,  // 15: browsermanager.backconnect.v1.BackConnectControlPlaneService.AddProxy:output_type -> browsermanager.backconnect.v1.BackConnectControlPlaneServiceAddProxyResponse
	7,  // 16: browsermanager.backconnect.v1.BackConnectControlPlaneService.RemoveProxy:output_type -> browsermanager.backconnect.v1.BackConnectControlPlaneServiceRemoveProxyResponse
	9,  // 17: browsermanager.backconnect.v1.BackConnectControlPlaneService.FetchBrowser:output_type -> browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchBrowserResponse
	13, // [13:18] is the sub-list for method output_type
	8,  // [8:13] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_browsermanager_backconnect_v1_controlplane_proto_init() }
func file_browsermanager_backconnect_v1_controlplane_proto_init() {
	if File_browsermanager_backconnect_v1_controlplane_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_browsermanager_backconnect_v1_controlplane_proto_rawDesc), len(file_browsermanager_backconnect_v1_controlplane_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_browsermanager_backconnect_v1_controlplane_proto_goTypes,
		DependencyIndexes: file_browsermanager_backconnect_v1_controlplane_proto_depIdxs,
		MessageInfos:      file_browsermanager_backconnect_v1_controlplane_proto_msgTypes,
	}.Build()
	File_browsermanager_backconnect_v1_controlplane_proto = out.File
	file_browsermanager_backconnect_v1_controlplane_proto_goTypes = nil
	file_browsermanager_backconnect_v1_controlplane_proto_depIdxs = nil
}
