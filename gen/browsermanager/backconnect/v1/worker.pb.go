// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: browsermanager/backconnect/v1/worker.proto

package backconnectv1

import (
	v1 "git.tmproxy-infra.com/algo/algoproxy-proto/gen/errmsg/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BackConnectWorkerServiceAddProxyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Server        string                 `protobuf:"bytes,1,opt,name=server,proto3" json:"server,omitempty"`
	Username      string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectWorkerServiceAddProxyRequest) Reset() {
	*x = BackConnectWorkerServiceAddProxyRequest{}
	mi := &file_browsermanager_backconnect_v1_worker_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectWorkerServiceAddProxyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectWorkerServiceAddProxyRequest) ProtoMessage() {}

func (x *BackConnectWorkerServiceAddProxyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_worker_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectWorkerServiceAddProxyRequest.ProtoReflect.Descriptor instead.
func (*BackConnectWorkerServiceAddProxyRequest) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_worker_proto_rawDescGZIP(), []int{0}
}

func (x *BackConnectWorkerServiceAddProxyRequest) GetServer() string {
	if x != nil {
		return x.Server
	}
	return ""
}

func (x *BackConnectWorkerServiceAddProxyRequest) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *BackConnectWorkerServiceAddProxyRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type BackConnectWorkerServiceAddProxyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectWorkerServiceAddProxyResponse) Reset() {
	*x = BackConnectWorkerServiceAddProxyResponse{}
	mi := &file_browsermanager_backconnect_v1_worker_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectWorkerServiceAddProxyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectWorkerServiceAddProxyResponse) ProtoMessage() {}

func (x *BackConnectWorkerServiceAddProxyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_worker_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectWorkerServiceAddProxyResponse.ProtoReflect.Descriptor instead.
func (*BackConnectWorkerServiceAddProxyResponse) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_worker_proto_rawDescGZIP(), []int{1}
}

func (x *BackConnectWorkerServiceAddProxyResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackConnectWorkerServiceRemoveProxyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectWorkerServiceRemoveProxyRequest) Reset() {
	*x = BackConnectWorkerServiceRemoveProxyRequest{}
	mi := &file_browsermanager_backconnect_v1_worker_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectWorkerServiceRemoveProxyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectWorkerServiceRemoveProxyRequest) ProtoMessage() {}

func (x *BackConnectWorkerServiceRemoveProxyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_worker_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectWorkerServiceRemoveProxyRequest.ProtoReflect.Descriptor instead.
func (*BackConnectWorkerServiceRemoveProxyRequest) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_worker_proto_rawDescGZIP(), []int{2}
}

type BackConnectWorkerServiceRemoveProxyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectWorkerServiceRemoveProxyResponse) Reset() {
	*x = BackConnectWorkerServiceRemoveProxyResponse{}
	mi := &file_browsermanager_backconnect_v1_worker_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectWorkerServiceRemoveProxyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectWorkerServiceRemoveProxyResponse) ProtoMessage() {}

func (x *BackConnectWorkerServiceRemoveProxyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_worker_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectWorkerServiceRemoveProxyResponse.ProtoReflect.Descriptor instead.
func (*BackConnectWorkerServiceRemoveProxyResponse) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_worker_proto_rawDescGZIP(), []int{3}
}

func (x *BackConnectWorkerServiceRemoveProxyResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

type BackConnectWorkerServiceHealthCheckRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectWorkerServiceHealthCheckRequest) Reset() {
	*x = BackConnectWorkerServiceHealthCheckRequest{}
	mi := &file_browsermanager_backconnect_v1_worker_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectWorkerServiceHealthCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectWorkerServiceHealthCheckRequest) ProtoMessage() {}

func (x *BackConnectWorkerServiceHealthCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_worker_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectWorkerServiceHealthCheckRequest.ProtoReflect.Descriptor instead.
func (*BackConnectWorkerServiceHealthCheckRequest) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_worker_proto_rawDescGZIP(), []int{4}
}

type BackConnectWorkerServiceHealthCheckResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Error         *v1.ErrorMessage       `protobuf:"bytes,1,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BackConnectWorkerServiceHealthCheckResponse) Reset() {
	*x = BackConnectWorkerServiceHealthCheckResponse{}
	mi := &file_browsermanager_backconnect_v1_worker_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackConnectWorkerServiceHealthCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackConnectWorkerServiceHealthCheckResponse) ProtoMessage() {}

func (x *BackConnectWorkerServiceHealthCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_browsermanager_backconnect_v1_worker_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackConnectWorkerServiceHealthCheckResponse.ProtoReflect.Descriptor instead.
func (*BackConnectWorkerServiceHealthCheckResponse) Descriptor() ([]byte, []int) {
	return file_browsermanager_backconnect_v1_worker_proto_rawDescGZIP(), []int{5}
}

func (x *BackConnectWorkerServiceHealthCheckResponse) GetError() *v1.ErrorMessage {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_browsermanager_backconnect_v1_worker_proto protoreflect.FileDescriptor

const file_browsermanager_backconnect_v1_worker_proto_rawDesc = "" +
	"\n" +
	"*browsermanager/backconnect/v1/worker.proto\x12\x1dbrowsermanager.backconnect.v1\x1a\x18errmsg/v1/errormsg.proto\"y\n" +
	"'BackConnectWorkerServiceAddProxyRequest\x12\x16\n" +
	"\x06server\x18\x01 \x01(\tR\x06server\x12\x1a\n" +
	"\busername\x18\x02 \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\x03 \x01(\tR\bpassword\"Y\n" +
	"(BackConnectWorkerServiceAddProxyResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\",\n" +
	"*BackConnectWorkerServiceRemoveProxyRequest\"\\\n" +
	"+BackConnectWorkerServiceRemoveProxyResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error\",\n" +
	"*BackConnectWorkerServiceHealthCheckRequest\"\\\n" +
	"+BackConnectWorkerServiceHealthCheckResponse\x12-\n" +
	"\x05error\x18\x01 \x01(\v2\x17.errmsg.v1.ErrorMessageR\x05error2\x86\x04\n" +
	"\x18BackConnectWorkerService\x12\x9b\x01\n" +
	"\bAddProxy\x12F.browsermanager.backconnect.v1.BackConnectWorkerServiceAddProxyRequest\x1aG.browsermanager.backconnect.v1.BackConnectWorkerServiceAddProxyResponse\x12\xa4\x01\n" +
	"\vRemoveProxy\x12I.browsermanager.backconnect.v1.BackConnectWorkerServiceRemoveProxyRequest\x1aJ.browsermanager.backconnect.v1.BackConnectWorkerServiceRemoveProxyResponse\x12\xa4\x01\n" +
	"\vHealthCheck\x12I.browsermanager.backconnect.v1.BackConnectWorkerServiceHealthCheckRequest\x1aJ.browsermanager.backconnect.v1.BackConnectWorkerServiceHealthCheckResponseB\\ZZgit.tmproxy-infra.com/algo/algoproxy-proto/gen/browsermanager/backconnect/v1;backconnectv1b\x06proto3"

var (
	file_browsermanager_backconnect_v1_worker_proto_rawDescOnce sync.Once
	file_browsermanager_backconnect_v1_worker_proto_rawDescData []byte
)

func file_browsermanager_backconnect_v1_worker_proto_rawDescGZIP() []byte {
	file_browsermanager_backconnect_v1_worker_proto_rawDescOnce.Do(func() {
		file_browsermanager_backconnect_v1_worker_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_browsermanager_backconnect_v1_worker_proto_rawDesc), len(file_browsermanager_backconnect_v1_worker_proto_rawDesc)))
	})
	return file_browsermanager_backconnect_v1_worker_proto_rawDescData
}

var file_browsermanager_backconnect_v1_worker_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_browsermanager_backconnect_v1_worker_proto_goTypes = []any{
	(*BackConnectWorkerServiceAddProxyRequest)(nil),     // 0: browsermanager.backconnect.v1.BackConnectWorkerServiceAddProxyRequest
	(*BackConnectWorkerServiceAddProxyResponse)(nil),    // 1: browsermanager.backconnect.v1.BackConnectWorkerServiceAddProxyResponse
	(*BackConnectWorkerServiceRemoveProxyRequest)(nil),  // 2: browsermanager.backconnect.v1.BackConnectWorkerServiceRemoveProxyRequest
	(*BackConnectWorkerServiceRemoveProxyResponse)(nil), // 3: browsermanager.backconnect.v1.BackConnectWorkerServiceRemoveProxyResponse
	(*BackConnectWorkerServiceHealthCheckRequest)(nil),  // 4: browsermanager.backconnect.v1.BackConnectWorkerServiceHealthCheckRequest
	(*BackConnectWorkerServiceHealthCheckResponse)(nil), // 5: browsermanager.backconnect.v1.BackConnectWorkerServiceHealthCheckResponse
	(*v1.ErrorMessage)(nil),                             // 6: errmsg.v1.ErrorMessage
}
var file_browsermanager_backconnect_v1_worker_proto_depIdxs = []int32{
	6, // 0: browsermanager.backconnect.v1.BackConnectWorkerServiceAddProxyResponse.error:type_name -> errmsg.v1.ErrorMessage
	6, // 1: browsermanager.backconnect.v1.BackConnectWorkerServiceRemoveProxyResponse.error:type_name -> errmsg.v1.ErrorMessage
	6, // 2: browsermanager.backconnect.v1.BackConnectWorkerServiceHealthCheckResponse.error:type_name -> errmsg.v1.ErrorMessage
	0, // 3: browsermanager.backconnect.v1.BackConnectWorkerService.AddProxy:input_type -> browsermanager.backconnect.v1.BackConnectWorkerServiceAddProxyRequest
	2, // 4: browsermanager.backconnect.v1.BackConnectWorkerService.RemoveProxy:input_type -> browsermanager.backconnect.v1.BackConnectWorkerServiceRemoveProxyRequest
	4, // 5: browsermanager.backconnect.v1.BackConnectWorkerService.HealthCheck:input_type -> browsermanager.backconnect.v1.BackConnectWorkerServiceHealthCheckRequest
	1, // 6: browsermanager.backconnect.v1.BackConnectWorkerService.AddProxy:output_type -> browsermanager.backconnect.v1.BackConnectWorkerServiceAddProxyResponse
	3, // 7: browsermanager.backconnect.v1.BackConnectWorkerService.RemoveProxy:output_type -> browsermanager.backconnect.v1.BackConnectWorkerServiceRemoveProxyResponse
	5, // 8: browsermanager.backconnect.v1.BackConnectWorkerService.HealthCheck:output_type -> browsermanager.backconnect.v1.BackConnectWorkerServiceHealthCheckResponse
	6, // [6:9] is the sub-list for method output_type
	3, // [3:6] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_browsermanager_backconnect_v1_worker_proto_init() }
func file_browsermanager_backconnect_v1_worker_proto_init() {
	if File_browsermanager_backconnect_v1_worker_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_browsermanager_backconnect_v1_worker_proto_rawDesc), len(file_browsermanager_backconnect_v1_worker_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_browsermanager_backconnect_v1_worker_proto_goTypes,
		DependencyIndexes: file_browsermanager_backconnect_v1_worker_proto_depIdxs,
		MessageInfos:      file_browsermanager_backconnect_v1_worker_proto_msgTypes,
	}.Build()
	File_browsermanager_backconnect_v1_worker_proto = out.File
	file_browsermanager_backconnect_v1_worker_proto_goTypes = nil
	file_browsermanager_backconnect_v1_worker_proto_depIdxs = nil
}
