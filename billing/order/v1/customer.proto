syntax = "proto3";
package billing.order.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/order/v1;orderv1";

//import "utils/v1/utils.proto";
import "errmsg/v1/errormsg.proto";

service CustomerOrderService {
  rpc CalculateOrderSubscription(CustomerOrderServiceCalculateOrderSubscriptionRequest) returns(CustomerOrderServiceCalculateOrderSubscriptionResponse);
  rpc OrderSubscription(CustomerOrderServiceOrderSubscriptionRequest) returns(CustomerOrderServiceOrderSubscriptionResponse);
  rpc ExtendSubscription(CustomerOrderServiceExtendSubscriptionRequest) returns(CustomerOrderServiceExtendSubscriptionResponse);
}

message CustomerOrderServiceCalculateOrderSubscriptionRequest {
  string id_plan = 1;
  string id_plan_price = 2;
  string coupon_code = 3;
}
message CustomerOrderServiceCalculateOrderSubscriptionResponse {
  double order_amount  = 1;
  double debit_amount = 2;
  double discount_amount = 3;
  errmsg.v1.ErrorMessage error = 4;
}

message CustomerOrderServiceOrderSubscriptionRequest {
  string id_plan = 1;
  string id_plan_price = 2;
  string coupon_code = 3;
}
message CustomerOrderServiceOrderSubscriptionResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message CustomerOrderServiceExtendSubscriptionRequest {
  string id_subscription = 1;
  string id_plan_price = 2;
  string coupon_code = 3;
}

message CustomerOrderServiceExtendSubscriptionResponse {
  errmsg.v1.ErrorMessage error = 1;
}
