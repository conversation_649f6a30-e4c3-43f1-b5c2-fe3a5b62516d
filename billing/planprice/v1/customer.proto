syntax = "proto3";
package billing.planprice.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/planprice/v1;planpricev1";


import "errmsg/v1/errormsg.proto";
import "algoenum/v1/currency.proto";
// billing.plan.v1.CustomerPlanPriceService

service CustomerPlanPriceService {
  rpc FetchPlanPrice(CustomerPlanPriceServiceFetchPlanPriceRequest) returns(CustomerPlanPriceServiceFetchPlanPriceResponse);
}

message CustomerPlanPriceServiceFetchPlanPriceRequest {
  string id_plan = 1;
}

message CustomerPlanPriceServiceFetchPlanPriceResponse {
  errmsg.v1.ErrorMessage error = 1;
  repeated CustomerPlanPriceServicePlanPriceEntity plan_prices = 2;
}



message CustomerPlanPriceServicePlanPriceEntity {
  string id_plan_price = 1;
  int64 billing_cycle_in_sec = 2;
  double data_transfer_in_gbyte = 3;
  double price = 5;
  algoenum.v1.Currency currency = 6;
}
