syntax = "proto3";
package billing.planprice.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/planprice/v1;planpricev1";


import "algoenum/v1/currency.proto";
import "errmsg/v1/errormsg.proto";

// billing.plan.v1.MerchantPlanPriceService

service MerchantPlanPriceService {
  rpc FetchPlanPrice(MerchantPlanPriceServiceFetchPlanPriceRequest) returns(MerchantPlanPriceServiceFetchPlanPriceResponse);
  rpc UpdatePlanPrice(MerchantPlanPriceServiceUpdatePlanPriceRequest) returns (MerchantPlanPriceServiceUpdatePlanPriceResponse);
}

message MerchantPlanPriceServiceFetchPlanPriceRequest {
  string id_plan = 1;
}

message MerchantPlanPriceServiceFetchPlanPriceResponse {
  errmsg.v1.ErrorMessage error = 1;
  repeated MerchantPlanPriceServicePlanPriceEntity plan_prices = 2;
}


message MerchantPlanPriceServiceUpdatePlanPriceRequest {
  string id_plan_price = 1;
  double purchase_price = 2;
}

message MerchantPlanPriceServiceUpdatePlanPriceResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message MerchantPlanPriceServicePlanPriceEntity {
  string id_plan_price = 1;
  int64 billing_cycle_in_sec = 2;
  double data_transfer_in_gbyte = 3;
  double cost_price = 4;
  double purchase_price = 5;
  algoenum.v1.Currency currency = 6;
}
