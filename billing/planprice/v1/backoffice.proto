syntax = "proto3";
package billing.planprice.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/planprice/v1;planpricev1";


import "algoenum/v1/currency.proto";
import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";

// billing.plan.v1.BackofficePlanPriceService

service BackofficePlanPriceService {
  rpc FetchPlanPrice(BackofficePlanPriceServiceFetchPlanPriceRequest) returns(BackofficePlanPriceServiceFetchPlanPriceResponse);
  rpc CreatePlanPrice(BackofficePlanPriceServiceCreatePlanPriceRequest) returns (BackofficePlanPriceServiceCreatePlanPriceResponse);
  rpc UpdatePlanPrice(BackofficePlanPriceServiceUpdatePlanPriceRequest) returns (BackofficePlanPriceServiceUpdatePlanPriceResponse);
}

message BackofficePlanPriceServiceFetchPlanPriceRequest {
  string id_plan = 1;
}

message BackofficePlanPriceServiceFetchPlanPriceResponse {
  errmsg.v1.ErrorMessage error = 1;
  repeated BackofficePlanPriceServicePlanPriceEntity plan_prices = 2;
}

message BackofficePlanPriceServicePlanPriceEntity {
  string id_plan_price = 1;
  int64 billing_cycle_in_sec = 2;
  double data_transfer_in_gbyte = 3;
  double cost_price = 4;
  double purchase_price = 5;
  algoenum.v1.Currency currency = 6;
  bool is_active = 7;
}

message BackofficePlanPriceServiceUpdatePlanPriceRequest {
  string id_plan_price = 1;
  int64 billing_cycle_in_sec = 2;
  double cost_price = 3;
  double purchase_price = 4;
  utils.v1.State state = 5;
}

message BackofficePlanPriceServiceUpdatePlanPriceResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficePlanPriceServiceCreatePlanPriceRequest {
  string id_plan = 1;
  int64 billing_cycle_in_sec = 2;
  double data_transfer_in_gbyte = 3;
  double cost_price = 4;
  double purchase_price = 5;
}

message BackofficePlanPriceServiceCreatePlanPriceResponse {
  errmsg.v1.ErrorMessage error = 1;
}

