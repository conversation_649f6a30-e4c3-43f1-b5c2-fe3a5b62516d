syntax = "proto3";
package billing.transaction.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/transaction/v1;transactionv1";

import "errmsg/v1/errormsg.proto";
service InternalTransactionService {
  rpc CreateUserBalance(InternalTransactionServiceCreateUserBalanceRequest) returns (InternalTransactionServiceCreateUserBalanceResponse);
}

message  InternalTransactionServiceCreateUserBalanceRequest {
  string id_user = 1;
  string id_app = 2;
}

message InternalTransactionServiceCreateUserBalanceResponse {
  errmsg.v1.ErrorMessage error = 1;
}