syntax = "proto3";
package billing.transaction.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/transaction/v1;transactionv1";

import "utils/v1/utils.proto";
import "errmsg/v1/errormsg.proto";
import "algoenum/v1/currency.proto";
import "algoenum/v1/transaction_type.proto";
import "algoenum/v1/transaction_status.proto";
import "algoenum/v1/balance_type.proto";
import "algoenum/v1/order_type.proto";
import "algoenum/v1/payment_gateway_type.proto";

service MerchantTransactionService {
  rpc FetchUserBalance(MerchantTransactionServiceFetchUserBalanceRequest) returns(MerchantTransactionServiceFetchUserBalanceResponse);
  rpc FetchTransaction(MerchantTransactionServiceFetchTransactionRequest) returns (MerchantTransactionServiceFetchTransactionResponse);
}


message MerchantTransactionServiceFetchTransactionRequest {
  string id_user = 1;
  string id_payment_gateway = 2;
  algoenum.v1.TransactionType transaction_type = 3;
  algoenum.v1.TransactionStatus transaction_status = 4;
  int64 from_unix = 5;
  int64 to_unix = 6;
  utils.v1.PaginationRequest pagination = 7;
}

message MerchantTransactionServiceFetchTransactionUserBalanceTransaction {
  string id_user_balance = 1;
  algoenum.v1.BalanceType balance_type = 2;
  double amount = 3;
  double balance_before = 4;
  double balance_after = 5;
}
message MerchantTransactionServiceFetchTransactionResponse {
  repeated MerchantTransactionServiceFetchTransaction transactions = 1;
  utils.v1.PaginationResponse pagination = 2;
  errmsg.v1.ErrorMessage error = 3;
}

message MerchantTransactionServiceFetchTransaction {
  string id_transaction = 1;
  MerchantTransactionServiceFetchTransactionMerchant merchant = 2;
  MerchantTransactionServiceFetchTransactionUser user = 3;
  algoenum.v1.TransactionType transaction_type = 4;
  algoenum.v1.TransactionStatus transaction_status = 5;
  repeated MerchantTransactionServiceFetchTransactionUserBalanceTransaction user_balance_transaction = 6;
  MerchantTransactionServiceFetchTransactionDebitTransaction debit_transaction = 7;
  MerchantTransactionServiceFetchTransactionCreditTransaction credit_transaction = 8;

  double total_amount = 9;
  algoenum.v1.Currency currency = 10;
  int64 created_at = 11;
}

message MerchantTransactionServiceFetchTransactionMerchant {
  string id_merchant = 1;
  string name = 2;
  bool  is_active = 3;
}
message MerchantTransactionServiceFetchTransactionCreditTransaction {
  MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway payment_gateway = 1;
  string description = 2;
  string hash_transaction = 3;
}
message MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway {
  string id_payment_gateway = 1;
  algoenum.v1.PaymentGatewayType payment_gateway_type = 2;
  string account_holder_name = 3;
}

message MerchantTransactionServiceFetchTransactionDebitTransaction {
  MerchantTransactionServiceFetchTransactionDebitTransactionPlan plan = 1;
  MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscription subscription = 2;
  algoenum.v1.OrderType order_type = 3;
  double total_cost = 4;
  double total_purchase = 5;
  double total_discount = 6;
}
message MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscription {
  string id_subscription = 1;
}

message  MerchantTransactionServiceFetchTransactionDebitTransactionPlan {
  string id_plan = 1;
  string name = 2;
  MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice plan_price = 3;
}

message MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice {
  string id_plan_price  =1;
  double cost_price = 2;
  double purchase_price = 3;
  double data_transfer_in_gb = 4;
  int64 billing_cycle_in_sec = 5;
}

message MerchantTransactionServiceFetchTransactionUser {
  string id_user = 1;
  string email = 2;
  string first_name = 3;
  string last_name = 4;
  bool  is_active = 5;
}


message MerchantTransactionServiceFetchUserBalanceRequest{
  string id_user = 1;
  algoenum.v1.BalanceType balance_type = 2;
  utils.v1.PaginationRequest pagination = 3;
}
message MerchantTransactionServiceFetchUserBalanceResponse{
  repeated MerchantTransactionServiceUserBalanceUsers users = 1;
  errmsg.v1.ErrorMessage error = 2;
  utils.v1.PaginationResponse pagination = 3;
}


message MerchantTransactionServiceUserBalanceUsers {
  string id_user = 1;
  double total_balance = 2;
  repeated MerchantTransactionServiceUserBalanceOfUser user_balances = 4;
}

message MerchantTransactionServiceUserBalanceOfUser {
  string id_user_balance = 1;
  algoenum.v1.BalanceType balance_type = 2;
  int64 balance_charge_priority = 3;
  double current_balance = 4;
  algoenum.v1.Currency currency = 5;
}




