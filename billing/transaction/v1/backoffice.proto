syntax = "proto3";
package billing.transaction.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/transaction/v1;transactionv1";

import "utils/v1/utils.proto";
import "errmsg/v1/errormsg.proto";
import "algoenum/v1/currency.proto";
import "algoenum/v1/payment_gateway_type.proto";
import "algoenum/v1/transaction_type.proto";
import "algoenum/v1/transaction_status.proto";
import "algoenum/v1/balance_type.proto";
import "algoenum/v1/order_type.proto";


service BackofficeTransactionService {
  rpc FetchUserBalance(BackofficeTransactionServiceFetchUserBalanceRequest) returns(BackofficeTransactionServiceFetchUserBalanceResponse);
  rpc FetchTransaction(BackofficeTransactionServiceFetchTransactionRequest) returns (BackofficeTransactionServiceFetchTransactionResponse);

  // for testing, only add to promotion balance
  rpc AddCreditUser(BackofficeTransactionServiceAddCreditUserRequest) returns (BackofficeTransactionServiceAddCreditUserResponse);
}
message BackofficeTransactionServiceAddCreditUserRequest {
  string id_user = 1;
  double amount = 2;
  algoenum.v1.Currency currency = 3;
}

message BackofficeTransactionServiceAddCreditUserResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeTransactionServiceFetchTransactionRequest {
  string id_merchant = 1;
  string id_user = 2;
  string id_payment_gateway_type = 3;
  string id_payment_gateway = 4;
  algoenum.v1.TransactionType transaction_type = 5;
  algoenum.v1.TransactionStatus transaction_status = 6;

  int64 from_unix = 10;
  int64 to_unix = 11;
  utils.v1.PaginationRequest pagination = 12;
}
message BackofficeTransactionServiceFetchTransactionUserBalanceTransaction {
  string id_user_balance = 1;
  algoenum.v1.BalanceType balance_type = 2;
  double amount = 3;
  double balance_before = 4;
  double balance_after = 5;
}
message BackofficeTransactionServiceFetchTransactionResponse {
  repeated BackofficeTransactionServiceFetchTransaction transactions = 1;
  utils.v1.PaginationResponse pagination = 2;
  errmsg.v1.ErrorMessage error = 3;
}

message BackofficeTransactionServiceFetchTransaction {
  string id_transaction = 1;
  BackofficeTransactionServiceFetchTransactionMerchant merchant = 2;
  BackofficeTransactionServiceFetchTransactionUser user = 3;
  algoenum.v1.TransactionType transaction_type = 4;
  algoenum.v1.TransactionStatus transaction_status = 5;
  repeated BackofficeTransactionServiceFetchTransactionUserBalanceTransaction user_balance_transaction = 6;
  BackofficeTransactionServiceFetchTransactionDebitTransaction debit_transaction = 7;
  BackofficeTransactionServiceFetchTransactionCreditTransaction credit_transaction = 8;

  double total_amount = 9;
  algoenum.v1.Currency currency = 10;
  int64 created_at = 11;
}

message BackofficeTransactionServiceFetchTransactionMerchant {
  string id_merchant = 1;
  string name = 2;
  bool  is_active = 3;
}
message BackofficeTransactionServiceFetchTransactionCreditTransaction {
  BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway payment_gateway = 1;
  string description = 2;
  string hash_transaction = 3;
}
message BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway {
  string id_payment_gateway = 1;
  algoenum.v1.PaymentGatewayType payment_gateway_type = 2;
  string account_holder_name = 3;
}

message BackofficeTransactionServiceFetchTransactionDebitTransaction {
  BackofficeTransactionServiceFetchTransactionDebitTransactionPlan plan = 1;
  BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscription subscription = 2;
  algoenum.v1.OrderType order_type = 3;
  double total_cost = 4;
  double total_purchase = 5;
  double total_discount = 6;
}
message BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscription {
  string id_subscription = 1;
}

message  BackofficeTransactionServiceFetchTransactionDebitTransactionPlan {
  string id_plan = 1;
  string name = 2;
  BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice plan_price = 3;
}

message BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice {
  string id_plan_price  =1;
  double cost_price = 2;
  double purchase_price = 3;
  double data_transfer_in_gb = 4;
  int64 billing_cycle_in_sec = 5;
}

message BackofficeTransactionServiceFetchTransactionUser {
  string id_user = 1;
  string email = 2;
  string first_name = 3;
  string last_name = 4;
  bool  is_active = 5;
}

message BackofficeTransactionServiceFetchUserBalanceRequest{
  string id_merchant = 1;
  string id_user = 2;
  algoenum.v1.BalanceType balance_type = 3;
  utils.v1.PaginationRequest pagination = 4;
}


message BackofficeTransactionServiceFetchUserBalanceResponse{
  repeated BackofficeTransactionServiceUserBalance users = 1;
  errmsg.v1.ErrorMessage error = 2;
  utils.v1.PaginationResponse pagination = 3;
}

message BackofficeTransactionServiceUserBalance {
  string id_user = 1;
  double total_balance = 2;
  repeated BackofficeTransactionServiceBalance balances = 3 ;
}

message BackofficeTransactionServiceBalance {
  string id_user_balance = 1;
  algoenum.v1.BalanceType balance_type = 2;
  int64 balance_charge_priority = 3;
  double current_balance = 4;
  algoenum.v1.Currency currency = 5;
  bool is_active = 6;
}
