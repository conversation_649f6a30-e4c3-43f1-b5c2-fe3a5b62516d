syntax = "proto3";
package billing.transaction.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/transaction/v1;transactionv1";

import "utils/v1/utils.proto";
import "errmsg/v1/errormsg.proto";
import "algoenum/v1/currency.proto";
import "algoenum/v1/payment_gateway_type.proto";
import "algoenum/v1/balance_type.proto";
import "algoenum/v1/transaction_status.proto";
import "algoenum/v1/transaction_type.proto";
import "algoenum/v1/order_type.proto";

service CustomerTransactionService {
  rpc FetchBalance(CustomerTransactionServiceFetchBalanceRequest) returns (CustomerTransactionServiceFetchBalanceResponse);
  rpc FetchTransaction(CustomerTransactionServiceFetchTransactionRequest) returns (CustomerTransactionServiceFetchTransactionResponse);
}

message CustomerTransactionServiceFetchBalanceRequest{}

message CustomerTransactionServiceFetchBalanceResponse{
  algoenum.v1.Currency currency = 1;
  repeated CustomerTransactionServiceBalance balances = 2;
  errmsg.v1.ErrorMessage error = 3;
}

message CustomerTransactionServiceBalance {
  algoenum.v1.BalanceType balance_type = 1;
  double current_balance  = 2;
}
message CustomerTransactionServiceFetchTransactionRequest {
  string id_payment_gateway = 2;
  algoenum.v1.TransactionType transaction_type = 3;
  algoenum.v1.TransactionStatus transaction_status = 4;
  int64 from_unix = 5;
  int64 to_unix = 6;
  utils.v1.PaginationRequest pagination = 7;
}


message CustomerTransactionServiceFetchTransactionUserBalanceTransaction {
string id_user_balance = 1;
    algoenum.v1.BalanceType balance_type = 2;
    double amount = 3;
    double balance_before = 4;
    double balance_after = 5;
    }
message CustomerTransactionServiceFetchTransactionResponse {
  repeated CustomerTransactionServiceFetchTransaction transactions = 1;
  utils.v1.PaginationResponse pagination = 2;
  errmsg.v1.ErrorMessage error = 3;
}

message CustomerTransactionServiceFetchTransaction {
  string id_transaction = 1;
  algoenum.v1.TransactionType transaction_type = 2;
  algoenum.v1.TransactionStatus transaction_status = 3;
  repeated CustomerTransactionServiceFetchTransactionUserBalanceTransaction user_balance_transaction = 4;
  CustomerTransactionServiceFetchTransactionDebitTransaction debit_transaction = 5;
  CustomerTransactionServiceFetchTransactionCreditTransaction credit_transaction = 6;

  double total_amount = 7;
  algoenum.v1.Currency currency = 8;
  int64 created_at = 9;
}


message CustomerTransactionServiceFetchTransactionCreditTransaction {
  CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway payment_gateway = 1;
  string description = 2;
  string hash_transaction = 3;
}
message CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway {
  string id_payment_gateway = 1;
  algoenum.v1.PaymentGatewayType payment_gateway_type = 2;
  string account_holder_name = 3;
}

message CustomerTransactionServiceFetchTransactionDebitTransaction {
  CustomerTransactionServiceFetchTransactionDebitTransactionPlan plan = 1;
  CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscription subscription = 2;
  algoenum.v1.OrderType order_type = 3;
  double total_purchase = 4;
  double total_discount = 5;
}
message CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscription {
  string id_subscription = 1;
}

message  CustomerTransactionServiceFetchTransactionDebitTransactionPlan {
  string id_plan = 1;
  string name = 2;
  CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice plan_price = 3;
}

message CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice {
  string id_plan_price  =1;
  double purchase_price = 2;
  double data_transfer_in_gb = 3;
  int64 billing_cycle_in_sec = 4;
}

