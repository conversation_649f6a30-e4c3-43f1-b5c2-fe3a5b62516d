syntax = "proto3";
package billing.payment.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/payment/v1;paymentv1";

import "errmsg/v1/errormsg.proto";
import "algoenum/v1/payment_gateway_type.proto";
import "algoenum/v1/vn_bank.proto";
import "utils/v1/utils.proto";
import "algoenum/v1/currency.proto";
// billing.payment.v1.CustomerPaymentService
service CustomerPaymentService {
  rpc FetchPaymentGateway(CustomerPaymentServiceFetchPaymentGatewayRequest) returns (CustomerPaymentServiceFetchPaymentGatewayResponse);

//  rpc FetchSePayPaymentGatewayDetail(CustomerPaymentServiceFetchSePayPaymentGatewayDetailRequest) returns (CustomerPaymentServiceFetchSePayPaymentGatewayDetailResponse);


  rpc FetchAppotaAmountTopUp(CustomerPaymentServiceFetchAppotaAmountTopUpRequest) returns (CustomerPaymentServiceFetchAppotaAmountTopUpResponse);
  rpc CreateAppotaPayment(CustomerPaymentServiceCreateAppotaPaymentRequest) returns(CustomerPaymentServiceCreateAppotaPaymentResponse);

  rpc FetchDodoAmountTopUp(CustomerPaymentServiceFetchDodoAmountTopUpRequest) returns (CustomerPaymentServiceFetchDodoAmountTopUpResponse);
  rpc CreateDodoPayment(CustomerPaymentServiceCreateDodoPaymentRequest) returns (CustomerPaymentServiceCreateDodoPaymentResponse);

  rpc FetchSePayAmountTopUp(CustomerPaymentServiceFetchSePayAmountTopUpRequest) returns (CustomerPaymentServiceFetchSePayAmountTopUpResponse);
  rpc CreateSePayPayment(CustomerPaymentServiceCreateSePayPaymentRequest) returns (CustomerPaymentServiceCreateSePayPaymentResponse);
}

//message CustomerPaymentServiceFetchSePayPaymentGatewayDetailRequest {
//  string id_payment_gateway = 1;
//}
//
//message CustomerPaymentServiceFetchSePayPaymentGatewayDetailResponse {
//  errmsg.v1.ErrorMessage error = 1;
//  CustomerPaymentServiceSePayPaymentGatewayDetail sepay_payment_gateway_detail = 2;
//}
//
//message CustomerPaymentServiceSePayPaymentGatewayDetail {
//  algoenum.v1.VNBankType vn_bank = 1;
//  string account_holder_name = 2;
//  string account_number = 3;
//  string prefix = 4;
//  string suffix = 5;
//}


message CustomerPaymentServiceFetchSePayAmountTopUpRequest {
  utils.v1.PaginationRequest pagination = 1;
}

message CustomerPaymentServiceFetchSePayAmountTopUpResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated CustomerPaymentServiceSepayAmountTopUp sepay_amount_top_ups = 3;
}


message CustomerPaymentServiceSepayAmountTopUp {
  string id_payment_gateway_sepay_amount_top_up = 1;
  double amount = 2;
  double promotion = 3;
  algoenum.v1.Currency currency = 4;
}

message CustomerPaymentServiceCreateSePayPaymentRequest {
  string id_payment_gateway_sepay_amount_top_up = 1;
}

message CustomerPaymentServiceCreateSePayPaymentResponse {
  errmsg.v1.ErrorMessage error = 1;
  string image_qr = 2;
  algoenum.v1.VNBankType bank_type = 3;
  string account_holder_name = 4;
  string account_number = 5;
  string payment_desc = 6;
}


message CustomerPaymentServiceCreateAppotaPaymentRequest {
  string id_payment_gateway_appota_amount_top_up = 1;
  string first_name = 2;
  string last_name = 3;
  string bank_code = 4;
}

message CustomerPaymentServiceCreateAppotaPaymentResponse {
  errmsg.v1.ErrorMessage error = 1;
  string payment_link = 2;
}

message CustomerPaymentServiceCreateDodoPaymentRequest {
  string id_payment_gateway_dodo_amount_top_up = 1;
  string first_name = 2;
  string last_name = 3;
  string user_city = 4;
  string user_country = 5;
  string user_state = 6;
  string user_phone = 7;
  string user_street = 8;
  string user_zipcode = 9;
}
message CustomerPaymentServiceCreateDodoPaymentResponse {
  errmsg.v1.ErrorMessage error = 1;
  string payment_link = 2;
}

message CustomerPaymentServiceFetchDodoAmountTopUpRequest {
  utils.v1.PaginationRequest pagination = 1;
}

message CustomerPaymentServiceFetchDodoAmountTopUpResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated CustomerPaymentServiceDodoAmountTopUp dodo_amount_top_ups = 3;
}


message CustomerPaymentServiceDodoAmountTopUp {
  string id_payment_gateway_dodo_amount_top_up = 1;
  double amount = 2;
  double promotion = 3;
  algoenum.v1.Currency currency = 4;
}


message CustomerPaymentServiceFetchAppotaAmountTopUpRequest {
  utils.v1.PaginationRequest pagination = 1;
}
message CustomerPaymentServiceFetchAppotaAmountTopUpResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated CustomerPaymentServiceAppotaAmountTopUp appota_amount_top_ups = 3;
}
message CustomerPaymentServiceAppotaAmountTopUp {
  string id_payment_gateway_appota_amount_top_up = 1;
  double amount = 2;
  double promotion = 3;
  algoenum.v1.Currency currency = 4;
}


message CustomerPaymentServiceFetchPaymentGatewayRequest {}
message CustomerPaymentServiceFetchPaymentGatewayResponse {
  repeated CustomerPaymentServicePaymentGateway payment_gateways = 1;
  errmsg.v1.ErrorMessage error = 2;
  utils.v1.PaginationResponse pagination = 3;
}

message CustomerPaymentServicePaymentGateway {
  string id_payment_gateway = 1;
  CustomerPaymentServicePaymentGatewayPaymentGatewayType payment_gateway_type = 3;
}

message CustomerPaymentServicePaymentGatewayPaymentGatewayType {
  algoenum.v1.PaymentGatewayType payment_gateway_type = 2;
}