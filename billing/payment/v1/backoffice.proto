syntax = "proto3";
package billing.payment.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/payment/v1;paymentv1";

import "utils/v1/utils.proto";

import "errmsg/v1/errormsg.proto";
import "algoenum/v1/currency.proto";
import "algoenum/v1/payment_gateway_type.proto";
import "algoenum/v1/vn_bank.proto";

// billing.payment.v1.BackofficePaymentService
service BackofficePaymentService {
    rpc FetchPaymentGatewayType(BackofficePaymentServiceFetchPaymentGatewayTypeRequest) returns (BackofficePaymentServiceFetchPaymentGatewayTypeResponse);
    rpc UpdatePaymentGatewayType(BackofficePaymentServiceUpdatePaymentGatewayTypeRequest) returns (BackofficePaymentServiceUpdatePaymentGatewayTypeResponse);

    rpc FetchPaymentGateway(BackofficePaymentServiceFetchPaymentGatewayRequest) returns (BackofficePaymentServiceFetchPaymentGatewayResponse);
    rpc UpdatePaymentGatewayState(BackofficePaymentServiceUpdatePaymentGatewayStateRequest) returns (BackofficePaymentServiceUpdatePaymentGatewayStateResponse);

    rpc FetchAppotaPaymentGatewayDetail(BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest) returns (BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse);
    rpc FetchDodoPaymentGatewayDetail(BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest) returns (BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse);
    rpc FetchSePayPaymentGatewayDetail(BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest) returns (BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse);

    rpc CreateAppotaPaymentGateway(BackofficePaymentServiceCreateAppotaPaymentGatewayRequest) returns (BackofficePaymentServiceCreateAppotaPaymentGatewayResponse);
    rpc CreateDodoPaymentGateway(BackofficePaymentServiceCreateDodoPaymentGatewayRequest) returns (BackofficePaymentServiceCreateDodoPaymentGatewayResponse);
    rpc CreateSePayPaymentGateway(BackofficePaymentServiceCreateSePayPaymentGatewayRequest) returns (BackofficePaymentServiceCreateSePayPaymentGatewayResponse);

    rpc CreateSePayAmountTopUp(BackofficePaymentServiceCreateSePayAmountTopUpRequest) returns(BackofficePaymentServiceCreateSePayAmountTopUpResponse);
    rpc CreateDodoAmountTopUp(BackofficePaymentServiceCreateDodoAmountTopUpRequest) returns(BackofficePaymentServiceCreateDodoAmountTopUpResponse);
    rpc CreateAppotaAmountTopUp(BackofficePaymentServiceCreateAppotaAmountTopUpRequest) returns (BackofficePaymentServiceCreateAppotaAmountTopUpResponse);

   rpc FetchAppotaAmountTopUp(BackofficePaymentServiceFetchAppotaAmountTopUpRequest) returns (BackofficePaymentServiceFetchAppotaAmountTopUpResponse);
   rpc FetchDodoAmountTopUp(BackofficePaymentServiceFetchDodoAmountTopUpRequest) returns (BackofficePaymentServiceFetchDodoAmountTopUpResponse);
   rpc FetchSePayAmountTopUp(BackofficePaymentServiceFetchSePayAmountTopUpRequest) returns (BackofficePaymentServiceFetchSePayAmountTopUpResponse);

}

message BackofficePaymentServiceFetchSePayAmountTopUpRequest {
  string id_merchant = 1;
  utils.v1.State state = 2;
  utils.v1.PaginationRequest pagination = 3;
}

message BackofficePaymentServiceFetchSePayAmountTopUpResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated BackofficePaymentServiceSePayAmountTopUp sepay_amount_top_ups = 3;
}

message BackofficePaymentServiceSePayAmountTopUp {
  string id_payment_gateway_sepay_amount_top_up = 1;
  double amount = 2;
  double promotion = 3;
  algoenum.v1.Currency currency = 4 ;
}

message BackofficePaymentServiceFetchDodoAmountTopUpRequest {
  string id_merchant = 1;
  utils.v1.State state = 2;
  utils.v1.PaginationRequest pagination = 3;
}
message BackofficePaymentServiceFetchDodoAmountTopUpResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated BackofficePaymentServiceDodoAmountTopUp dodo_amount_top_ups = 3;
}
message BackofficePaymentServiceDodoAmountTopUp {
  string id_payment_gateway_dodo_amount_top_up = 1;
  double amount = 2;
  double promotion = 3;
  string product_id_dodo = 4;
  algoenum.v1.Currency currency = 5;
}


message BackofficePaymentServiceFetchAppotaAmountTopUpRequest {
  string id_merchant  = 1;
  utils.v1.State state = 2;
  utils.v1.PaginationRequest pagination = 3;
}
message BackofficePaymentServiceFetchAppotaAmountTopUpResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated BackofficePaymentServiceAppotaAmountTopUp appota_amount_top_ups = 3;
}
message BackofficePaymentServiceAppotaAmountTopUp {
  string id_payment_gateway_appota_amount_top_up = 1;
  double amount = 2;
  double promotion = 3;
  algoenum.v1.Currency currency = 4;
}



message BackofficePaymentServiceCreateSePayPaymentGatewayRequest {
  string id_merchant = 1;
  algoenum.v1.VNBankType vn_bank = 2;
  string account_holder_name = 3;
  string account_number = 4;
  string prefix = 5;
  string suffix = 6;
  string api_key = 7;
  bool is_enable_for_customer = 8;
}

message BackofficePaymentServiceCreateSePayPaymentGatewayResponse {
  errmsg.v1.ErrorMessage error = 1;
}
message BackofficePaymentServiceCreateDodoPaymentGatewayRequest {
  string id_merchant = 1;
  string signing_key = 2;
  string api_key = 3;
  string base_url = 4;
  bool is_enable_for_customer = 5;
}

message BackofficePaymentServiceCreateDodoPaymentGatewayResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficePaymentServiceCreateAppotaPaymentGatewayRequest {
  string id_merchant = 1;
  string partner_code = 2;
  string api_key = 3;
  string secret_key = 4;
  bool is_enable_for_customer = 5;
}
message BackofficePaymentServiceCreateAppotaPaymentGatewayResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message  BackofficePaymentServiceCreateSePayAmountTopUpRequest {
  string id_merchant = 1;
  double amount = 2;
  double promotion = 3;
}
message  BackofficePaymentServiceCreateSePayAmountTopUpResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message BackofficePaymentServiceCreateAppotaAmountTopUpRequest {
  string id_merchant = 1;
  double amount =  2;
  double promotion = 3;
}

message BackofficePaymentServiceCreateAppotaAmountTopUpResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message  BackofficePaymentServiceCreateDodoAmountTopUpRequest {
  string id_merchant = 1;
  string product_id_dodo  = 2;
  double amount = 3;
  double promotion = 4;
}
message  BackofficePaymentServiceCreateDodoAmountTopUpResponse {
  errmsg.v1.ErrorMessage error = 1;
}



message BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest {
  string id_payment_gateway = 1;
}

message BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse {
  errmsg.v1.ErrorMessage error = 1;
  BackofficePaymentServiceSePayPaymentGatewayDetail sepay_payment_gateway_detail = 2;
}

message BackofficePaymentServiceSePayPaymentGatewayDetail {
  string bank_name = 1;
  string account_holder_name = 2;
  string account_number = 3;
  string prefix = 4;
  string suffix = 5;
  string api_key = 6;
}

message BackofficePaymentServiceFetchPaymentGatewayTypeRequest {
  string id_payment_gateway_type = 1;
  algoenum.v1.PaymentGatewayType payment_gateway_type = 2;
  algoenum.v1.Currency currency  = 3;
  utils.v1.State  state = 4;
  utils.v1.PaginationRequest pagination = 5;
}
message BackofficePaymentServiceFetchPaymentGatewayTypeResponse {
  repeated BackofficePaymentGatewayType payment_gateway_types = 1;
  errmsg.v1.ErrorMessage error = 2;
  utils.v1.PaginationResponse pagination = 3;
}


message BackofficePaymentGatewayType {
  string id_payment_gateway_type = 1;
  algoenum.v1.PaymentGatewayType payment_gateway_type  = 2;
  algoenum.v1.Currency currency = 3;
  bool  is_active = 4;
}




message BackofficePaymentServiceUpdatePaymentGatewayStateRequest {
  string id_payment_gateway = 1;
  utils.v1.State state = 2;
}
message BackofficePaymentServiceUpdatePaymentGatewayStateResponse {
  errmsg.v1.ErrorMessage error = 1;
}




message  BackofficePaymentServiceUpdatePaymentGatewayTypeRequest {
  string id_payment_gateway_type = 1;
  utils.v1.State state = 2;
}
message  BackofficePaymentServiceUpdatePaymentGatewayTypeResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficePaymentServicePaymentGatewayMerchant {
  string id_merchant = 1;
  string name = 2;
  algoenum.v1.Currency currency = 3;
  bool is_active = 4;
}

message BackofficePaymentGateway {
  string id_payment_gateway = 1;
  BackofficePaymentServicePaymentGatewayMerchant merchant = 2;
  BackofficePaymentGatewayType payment_gateway_type = 3;
  string account_holder_name = 4;
  bool is_active = 5;
  bool is_enable_for_customer = 6;
  bool state = 7;
}

message BackofficePaymentServiceFetchPaymentGatewayRequest {
  string id_merchant = 1;
  string id_payment_gateway_type = 2;
  string account_holder_name_search = 3;
  utils.v1.State state = 4;
  utils.v1.PaginationRequest pagination = 5;
}

message BackofficePaymentServiceFetchPaymentGatewayResponse {
  repeated BackofficePaymentGateway payment_gateways = 1;
  errmsg.v1.ErrorMessage error = 2;
  utils.v1.PaginationResponse pagination = 3;
}

message BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest {
  string id_payment_gateway = 1;
}

message BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse {
  errmsg.v1.ErrorMessage error = 1;
  BackofficePaymentServiceAppotaPaymentGatewayDetail appota_payment_gateway_detail = 2;
}

message BackofficePaymentServiceAppotaPaymentGatewayDetail {
  string partner_code = 1;
  string api_key = 2;
  string secret_key = 3;
}

message BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest {
  string id_payment_gateway = 1;
}

message BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse {
  errmsg.v1.ErrorMessage error = 1;
  BackofficePaymentServiceDodoPaymentGatewayDetail dodo_payment_gateway_detail = 2;
}

message BackofficePaymentServiceDodoPaymentGatewayDetail {
  string signing_key = 1;
  string api_key = 2;
  string base_url = 3;
}
