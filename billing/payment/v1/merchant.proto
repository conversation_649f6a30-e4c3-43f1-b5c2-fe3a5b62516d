syntax = "proto3";
package billing.payment.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/payment/v1;paymentv1";

import "algoenum/v1/payment_gateway_type.proto";
import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";
import "algoenum/v1/currency.proto";
import "algoenum/v1/vn_bank.proto";

// billing.payment.v1.MerchantPaymentService
service MerchantPaymentService {
//  rpc FetchPaymentGatewayType(MerchantPaymentServiceFetchPaymentGatewayTypeRequest) returns (MerchantPaymentServiceFetchPaymentGatewayTypeResponse);

  rpc FetchPaymentGateway(MerchantPaymentServiceFetchPaymentGatewayRequest) returns (MerchantPaymentServiceFetchPaymentGatewayResponse);
//  rpc FetchAppotaPaymentGatewayDetail(MerchantPaymentServiceFetchAppotaPaymentGatewayDetailRequest) returns (MerchantPaymentServiceFetchAppotaPaymentGatewayDetailResponse);
//  rpc FetchDodoPaymentGatewayDetail(MerchantPaymentServiceFetchDodoPaymentGatewayDetailRequest) returns (MerchantPaymentServiceFetchDodoPaymentGatewayDetailResponse);
//  rpc FetchSePayPaymentGatewayDetail(MerchantPaymentServiceFetchSePayPaymentGatewayDetailRequest) returns (MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponse);

  // amount top_up
  rpc FetchAppotaAmountTopUp(MerchantPaymentServiceFetchAppotaAmountTopUpRequest) returns (MerchantPaymentServiceFetchAppotaAmountTopUpResponse);
  rpc FetchDodoAmountTopUp(MerchantPaymentServiceFetchDodoAmountTopUpRequest) returns (MerchantPaymentServiceFetchDodoAmountTopUpResponse);
  rpc FetchSePayAmountTopUp(MerchantPaymentServiceFetchSePayAmountTopUpRequest) returns (MerchantPaymentServiceFetchSePayAmountTopUpResponse);

  rpc CreateDodoPayment(MerchantPaymentServiceCreateDodoPaymentRequest) returns (MerchantPaymentServiceCreateDodoPaymentResponse);
  rpc CreateSePayPayment(MerchantPaymentServiceCreateSePayPaymentRequest) returns (MerchantPaymentServiceCreateSePayPaymentResponse);
}

message MerchantPaymentServiceCreateDodoPaymentRequest {
  string id_payment_gateway_dodo_amount_top_up = 1;
  string first_name = 2;
  string last_name = 3;
  string user_city = 4;
  string user_country = 5;
  string user_state = 6;
  string user_phone = 7;
  string user_street = 8;
  string user_zipcode = 9;
}
message MerchantPaymentServiceCreateDodoPaymentResponse {
  errmsg.v1.ErrorMessage error = 1;
  string payment_link = 2;
}
message MerchantPaymentServiceCreateSePayPaymentRequest {
  string id_payment_gateway_sepay_amount_top_up = 1;
}

message MerchantPaymentServiceCreateSePayPaymentResponse {
  errmsg.v1.ErrorMessage error = 1;
  string image_qr = 2;
  algoenum.v1.VNBankType bank_type = 3;
  string account_holder_name = 4;
  string account_number = 5;
  string payment_desc = 6;
}


message MerchantPaymentServiceFetchSePayAmountTopUpRequest {
  utils.v1.State state = 1;
  utils.v1.PaginationRequest pagination = 2;
}

message MerchantPaymentServiceFetchSePayAmountTopUpResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated MerchantPaymentServiceSePayAmountTopUp sepay_amount_top_ups = 3;
}

message MerchantPaymentServiceSePayAmountTopUp {
  string id_payment_gateway_sepay_amount_top_up = 1;
  double amount = 2;
  double promotion = 3;
  algoenum.v1.Currency currency = 4 ;
}




message MerchantPaymentServiceFetchSePayPaymentGatewayDetailRequest {
  string id_payment_gateway = 1;
}

message MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponse {
  errmsg.v1.ErrorMessage error = 1;
  MerchantPaymentServiceSePayPaymentGatewayDetail sepay_payment_gateway_detail = 2;
}

message MerchantPaymentServiceSePayPaymentGatewayDetail {
  algoenum.v1.VNBankType vn_bank = 1;
  string account_holder_name = 2;
  string account_number = 3;
  string prefix = 4;
  string suffix = 5;
}






//message MerchantPaymentServiceFetchDodoPaymentGatewayDetailRequest {
//  string id_payment_gateway = 1;
//}
//
//message MerchantPaymentServiceFetchDodoPaymentGatewayDetailResponse {
//  errmsg.v1.ErrorMessage error = 1;
//  MerchantPaymentServiceDodoPaymentGatewayDetail dodo_payment_gateway_detail = 2;
//}
//
//message MerchantPaymentServiceDodoPaymentGatewayDetail {
//  string signing_key = 1;
//  string api_key = 2;
//  string base_url = 3;
//}


//message MerchantPaymentServiceFetchAppotaPaymentGatewayDetailRequest {
//  string id_payment_gateway = 1;
//}
//
//message MerchantPaymentServiceFetchAppotaPaymentGatewayDetailResponse {
//  errmsg.v1.ErrorMessage error = 1;
//  MerchantPaymentServiceAppotaPaymentGatewayDetail appota_payment_gateway_detail = 2;
//}
//
//message MerchantPaymentServiceAppotaPaymentGatewayDetail {
//  string partner_code = 1;
//  string api_key = 2;
//  string secret_key = 3;
//}

message MerchantPaymentServiceFetchDodoAmountTopUpRequest {
  utils.v1.State state = 1;
  utils.v1.PaginationRequest pagination = 2;
}
message MerchantPaymentServiceFetchDodoAmountTopUpResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated MerchantPaymentServiceDodoAmountTopUp dodo_amount_top_ups = 3;
}
message MerchantPaymentServiceDodoAmountTopUp {
  string id_payment_gateway_dodo_amount_top_up = 1;
  double amount = 2;
  double promotion = 3;
  string product_id_dodo = 4;
  algoenum.v1.Currency currency = 5;
}


message MerchantPaymentServiceFetchAppotaAmountTopUpRequest {
  utils.v1.State state = 1;
  utils.v1.PaginationRequest pagination = 2;
}
message MerchantPaymentServiceFetchAppotaAmountTopUpResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated MerchantPaymentServiceAppotaAmountTopUp appota_amount_top_ups = 3;
}
message MerchantPaymentServiceAppotaAmountTopUp {
  string id_payment_gateway_appota_amount_top_up = 1;
  double amount = 2;
  double promotion = 3;
  algoenum.v1.Currency currency = 4;
}








message MerchantPaymentServiceUpdatePaymentGatewayStateRequest {
  string id_payment_gateway = 1;
  utils.v1.State state = 2;
}

message MerchantPaymentServiceUpdatePaymentGatewayStateResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message MerchantPaymentServiceFetchPaymentGatewayTypeRequest {
  string id_payment_gateway_type = 1;
  algoenum.v1.PaymentGatewayType payment_gateway_type = 2;
  algoenum.v1.Currency currency  = 3;
  utils.v1.State  state = 4;
  utils.v1.PaginationRequest pagination = 5;
}
message MerchantPaymentServiceFetchPaymentGatewayTypeResponse {
  repeated MerchantPaymentServicePaymentGatewayType payment_gateway_types = 1;
  errmsg.v1.ErrorMessage error = 2;
  utils.v1.PaginationResponse pagination = 3;
}


message MerchantPaymentServicePaymentGatewayType {
  string id_payment_gateway_type = 1;
  algoenum.v1.PaymentGatewayType payment_gateway_type = 2;
  string name = 3;
}

message MerchantPaymentServiceFetchPaymentGatewayRequest {
  string id_payment_gateway = 1;
  string account_holder_name_search = 2;
  utils.v1.PaginationRequest pagination = 3;
}

message MerchantPaymentServiceFetchPaymentGatewayResponse {
  repeated MerchantPaymentServicePaymentGateway payment_gateways = 1;
  errmsg.v1.ErrorMessage error = 2;
  utils.v1.PaginationResponse pagination = 3;
}

message MerchantPaymentServicePaymentGateway {
  string id_payment_gateway = 1;
  string account_holder_name = 2;
  MerchantPaymentServicePaymentGatewayPaymentGatewayType payment_gateway_type = 3;
  bool  state = 4;
}


message MerchantPaymentServicePaymentGatewayPaymentGatewayType {
  string id_payment_gateway_type = 1;
  algoenum.v1.PaymentGatewayType payment_gateway_type = 2;
  bool is_active = 3;
}
