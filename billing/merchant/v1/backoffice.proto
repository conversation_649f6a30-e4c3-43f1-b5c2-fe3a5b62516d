syntax = "proto3";
package billing.merchant.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/billing/merchant/v1;merchantv1";

import "errmsg/v1/errormsg.proto";

import "algoenum/v1/currency.proto";
import "algoenum/v1/app_country.proto";
import "utils/v1/utils.proto";

 service BackofficeMerchantService {
  rpc CreateMerchant(BackofficeMerchantServiceCreateMerchantRequest) returns (BackofficeMerchantServiceCreateMerchantResponse);
  rpc FetchMerchant(BackofficeMerchantServiceFetchMerchantRequest) returns (BackofficeMerchantServiceFetchMerchantResponse);
  rpc UpdateMerchant(BackofficeMerchantServiceUpdateMerchantRequest) returns(BackofficeMerchantServiceUpdateMerchantResponse);
 }

 message BackofficeMerchantServiceCreateMerchantRequest {
   string name = 1;
   string domain = 2;
   algoenum.v1.Currency currency = 3;
   algoenum.v1.AppCountry app_country = 4;
 }

 message BackofficeMerchantServiceCreateMerchantResponse {
  errmsg.v1.ErrorMessage error = 1;
 }

 message BackofficeMerchantServiceFetchMerchantRequest {
  string name_search = 1;
  utils.v1.State state = 2;
  utils.v1.PaginationRequest pagination = 3;
 }

 message BackofficeMerchantServiceFetchMerchantResponse {
   errmsg.v1.ErrorMessage error = 1;
   utils.v1.PaginationResponse pagination = 2;
   repeated MerchantBackofficeMerchantEntity merchants = 3;
 }
message BackofficeMerchantServiceUpdateMerchantRequest{
   string id_merchant = 1;
   algoenum.v1.Currency currency = 2;
  utils.v1.State state = 3;
}
message BackofficeMerchantServiceUpdateMerchantResponse{
   errmsg.v1.ErrorMessage error = 1;
}

 message MerchantBackofficeMerchantEntity {
   string id_merchant  = 1;
   string name = 2;
   string domain = 3;
   algoenum.v1.Currency currency = 4;
   bool is_active = 5;
   bool state = 6;
 }
