syntax = "proto3";
package algoenum.v1;

option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1;algoenumv1";

enum ResNodeStatus {
  RES_NODE_STATUS_UNSPECIFIED = 0;

  RES_NODE_STATUS_OFFLINE = 1;
  RES_NODE_STATUS_ONLINE = 2;
}

enum ResNodePowerState  {
  RES_NODE_POWER_STATE_UNSPECIFIED = 0;

  RES_NODE_POWER_STATE_OFF = 1;
  RES_NODE_POWER_STATE_ON = 2;
  RES_NODE_POWER_STATE_POWER_ID_INCORRECT = 3;
}

enum ResDeviceStatus {
  RES_DEVICE_STATUS_UNSPECIFIED = 0;

  RES_DEVICE_STATUS_OFFLINE = 1;
  RES_DEVICE_STATUS_ONLINE = 2;
  RES_DEVICE_STATUS_ON_DIAL = 3;
}