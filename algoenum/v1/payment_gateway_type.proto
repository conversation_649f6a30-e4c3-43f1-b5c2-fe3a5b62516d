syntax = "proto3";
package algoenum.v1;

option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/algoenum/v1;algoenumv1";

enum PaymentGatewayType {
  PAYMENT_GATEWAY_TYPE_UNSPECIFIED = 0;

//  PAYMENT_GATEWAY_TYPE_VCB_VND = 1;
//  PAYMENT_GATEWAY_TYPE_ACB_VND = 2;
  PAYMENT_GATEWAY_TYPE_SEPAY_VND = 3;
  PAYMENT_GATEWAY_TYPE_APPOTA_PAY_VND = 4;

  PAYMENT_GATEWAY_TYPE_ADMIN_VND = 5;
  PAYMENT_GATEWAY_TYPE_ADMIN_USD = 6;
  PAYMENT_GATEWAY_TYPE_DODO_PAY_USD = 7;
}