syntax = "proto3";
package utils.v1;

option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/utils/v1;utilsv1";


message State {
  bool is_active = 1;
  bool  is_deactivate = 2;
}
message Bool {
  bool is_true = 1;
  bool  is_false = 2;
}
message PaginationRequest {
  int64 page_size = 1;
  int64 page_number = 2;
}

message  PaginationResponse {
  int64 current_page = 1;
  int64 page_size = 2;
  int64 total = 3;
  int64 total_pages = 4;
}