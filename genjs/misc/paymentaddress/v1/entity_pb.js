// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file misc/paymentaddress/v1/entity.proto (package misc.paymentaddress.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";

/**
 * Describes the file misc/paymentaddress/v1/entity.proto.
 */
export const file_misc_paymentaddress_v1_entity = /*@__PURE__*/
  fileDesc("CiNtaXNjL3BheW1lbnRhZGRyZXNzL3YxL2VudGl0eS5wcm90bxIWbWlzYy5wYXltZW50YWRkcmVzcy52MSInCgVTdGF0ZRIQCghpZF9zdGF0ZRgBIAEoAxIMCgRuYW1lGAIgASgJIiUKBENpdHkSDwoHaWRfY2l0eRgBIAEoAxIMCgRuYW1lGAIgASgJImoKB0NvdW50cnkSEgoKaWRfY291bnRyeRgBIAEoAxIMCgRuYW1lGAIgASgJEgwKBGlzbzIYAyABKAkSDAoEaXNvMxgEIAEoCRISCgpwaG9uZV9jb2RlGAUgASgJEg0KBWVtb2ppGAYgASgJQlhaVmdpdC50bXByb3h5LWluZnJhLmNvbS9hbGdvL2FsZ29wcm94eS1wcm90by9nZW4vbWlzYy9wYXltZW50YWRkcmVzcy92MTtwYXltZW50YWRkcmVzc3YxYgZwcm90bzM");

/**
 * Describes the message misc.paymentaddress.v1.State.
 * Use `create(StateSchema)` to create a new message.
 */
export const StateSchema = /*@__PURE__*/
  messageDesc(file_misc_paymentaddress_v1_entity, 0);

/**
 * Describes the message misc.paymentaddress.v1.City.
 * Use `create(CitySchema)` to create a new message.
 */
export const CitySchema = /*@__PURE__*/
  messageDesc(file_misc_paymentaddress_v1_entity, 1);

/**
 * Describes the message misc.paymentaddress.v1.Country.
 * Use `create(CountrySchema)` to create a new message.
 */
export const CountrySchema = /*@__PURE__*/
  messageDesc(file_misc_paymentaddress_v1_entity, 2);

