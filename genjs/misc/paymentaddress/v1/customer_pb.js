// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file misc/paymentaddress/v1/customer.proto (package misc.paymentaddress.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_misc_paymentaddress_v1_entity } from "./entity_pb";

/**
 * Describes the file misc/paymentaddress/v1/customer.proto.
 */
export const file_misc_paymentaddress_v1_customer = /*@__PURE__*/
  fileDesc("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", [file_utils_v1_utils, file_errmsg_v1_errormsg, file_misc_paymentaddress_v1_entity]);

/**
 * Describes the message misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCountriesRequest.
 * Use `create(CustomerPaymentAddressServiceFetchCountriesRequestSchema)` to create a new message.
 */
export const CustomerPaymentAddressServiceFetchCountriesRequestSchema = /*@__PURE__*/
  messageDesc(file_misc_paymentaddress_v1_customer, 0);

/**
 * Describes the message misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCountriesResponse.
 * Use `create(CustomerPaymentAddressServiceFetchCountriesResponseSchema)` to create a new message.
 */
export const CustomerPaymentAddressServiceFetchCountriesResponseSchema = /*@__PURE__*/
  messageDesc(file_misc_paymentaddress_v1_customer, 1);

/**
 * Describes the message misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchStatesRequest.
 * Use `create(CustomerPaymentAddressServiceFetchStatesRequestSchema)` to create a new message.
 */
export const CustomerPaymentAddressServiceFetchStatesRequestSchema = /*@__PURE__*/
  messageDesc(file_misc_paymentaddress_v1_customer, 2);

/**
 * Describes the message misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchStatesResponse.
 * Use `create(CustomerPaymentAddressServiceFetchStatesResponseSchema)` to create a new message.
 */
export const CustomerPaymentAddressServiceFetchStatesResponseSchema = /*@__PURE__*/
  messageDesc(file_misc_paymentaddress_v1_customer, 3);

/**
 * Describes the message misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCitiesRequest.
 * Use `create(CustomerPaymentAddressServiceFetchCitiesRequestSchema)` to create a new message.
 */
export const CustomerPaymentAddressServiceFetchCitiesRequestSchema = /*@__PURE__*/
  messageDesc(file_misc_paymentaddress_v1_customer, 4);

/**
 * Describes the message misc.paymentaddress.v1.CustomerPaymentAddressServiceFetchCitiesResponse.
 * Use `create(CustomerPaymentAddressServiceFetchCitiesResponseSchema)` to create a new message.
 */
export const CustomerPaymentAddressServiceFetchCitiesResponseSchema = /*@__PURE__*/
  messageDesc(file_misc_paymentaddress_v1_customer, 5);

/**
 * @generated from service misc.paymentaddress.v1.CustomerPaymentAddressService
 */
export const CustomerPaymentAddressService = /*@__PURE__*/
  serviceDesc(file_misc_paymentaddress_v1_customer, 0);

