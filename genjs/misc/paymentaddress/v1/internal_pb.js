// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file misc/paymentaddress/v1/internal.proto (package misc.paymentaddress.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_misc_paymentaddress_v1_entity } from "./entity_pb";

/**
 * Describes the file misc/paymentaddress/v1/internal.proto.
 */
export const file_misc_paymentaddress_v1_internal = /*@__PURE__*/
  fileDesc("CiVtaXNjL3BheW1lbnRhZGRyZXNzL3YxL2ludGVybmFsLnByb3RvEhZtaXNjLnBheW1lbnRhZGRyZXNzLnYxInAKN0ludGVybmFsUGF5bWVudEFkZHJlc3NTZXJ2aWNlVmFsaWRQYXltZW50QWRkcmVzc1JlcXVlc3QSEgoKaWRfY291bnRyeRgBIAEoAxIQCghpZF9zdGF0ZRgCIAEoAxIPCgdpZF9jaXR5GAMgASgDIu4BCjhJbnRlcm5hbFBheW1lbnRBZGRyZXNzU2VydmljZVZhbGlkUGF5bWVudEFkZHJlc3NSZXNwb25zZRImCgVlcnJvchgBIAEoCzIXLmVycm1zZy52MS5FcnJvck1lc3NhZ2USMAoHY291bnRyeRgCIAEoCzIfLm1pc2MucGF5bWVudGFkZHJlc3MudjEuQ291bnRyeRIsCgVzdGF0ZRgDIAEoCzIdLm1pc2MucGF5bWVudGFkZHJlc3MudjEuU3RhdGUSKgoEY2l0eRgEIAEoCzIcLm1pc2MucGF5bWVudGFkZHJlc3MudjEuQ2l0eTLaAQodSW50ZXJuYWxQYXltZW50QWRkcmVzc1NlcnZpY2USuAEKE1ZhbGlkUGF5bWVudEFkZHJlc3MSTy5taXNjLnBheW1lbnRhZGRyZXNzLnYxLkludGVybmFsUGF5bWVudEFkZHJlc3NTZXJ2aWNlVmFsaWRQYXltZW50QWRkcmVzc1JlcXVlc3QaUC5taXNjLnBheW1lbnRhZGRyZXNzLnYxLkludGVybmFsUGF5bWVudEFkZHJlc3NTZXJ2aWNlVmFsaWRQYXltZW50QWRkcmVzc1Jlc3BvbnNlQlhaVmdpdC50bXByb3h5LWluZnJhLmNvbS9hbGdvL2FsZ29wcm94eS1wcm90by9nZW4vbWlzYy9wYXltZW50YWRkcmVzcy92MTtwYXltZW50YWRkcmVzc3YxYgZwcm90bzM", [file_errmsg_v1_errormsg, file_misc_paymentaddress_v1_entity]);

/**
 * Describes the message misc.paymentaddress.v1.InternalPaymentAddressServiceValidPaymentAddressRequest.
 * Use `create(InternalPaymentAddressServiceValidPaymentAddressRequestSchema)` to create a new message.
 */
export const InternalPaymentAddressServiceValidPaymentAddressRequestSchema = /*@__PURE__*/
  messageDesc(file_misc_paymentaddress_v1_internal, 0);

/**
 * Describes the message misc.paymentaddress.v1.InternalPaymentAddressServiceValidPaymentAddressResponse.
 * Use `create(InternalPaymentAddressServiceValidPaymentAddressResponseSchema)` to create a new message.
 */
export const InternalPaymentAddressServiceValidPaymentAddressResponseSchema = /*@__PURE__*/
  messageDesc(file_misc_paymentaddress_v1_internal, 1);

/**
 * @generated from service misc.paymentaddress.v1.InternalPaymentAddressService
 */
export const InternalPaymentAddressService = /*@__PURE__*/
  serviceDesc(file_misc_paymentaddress_v1_internal, 0);

