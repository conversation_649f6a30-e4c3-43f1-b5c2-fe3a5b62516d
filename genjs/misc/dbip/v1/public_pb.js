// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file misc/dbip/v1/public.proto (package misc.dbip.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_misc_paymentaddress_v1_entity } from "../../paymentaddress/v1/entity_pb";

/**
 * Describes the file misc/dbip/v1/public.proto.
 */
export const file_misc_dbip_v1_public = /*@__PURE__*/
  fileDesc("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", [file_utils_v1_utils, file_errmsg_v1_errormsg, file_misc_paymentaddress_v1_entity]);

/**
 * Describes the message misc.dbip.v1.PublicDBIPServiceIPInfoRequest.
 * Use `create(PublicDBIPServiceIPInfoRequestSchema)` to create a new message.
 */
export const PublicDBIPServiceIPInfoRequestSchema = /*@__PURE__*/
  messageDesc(file_misc_dbip_v1_public, 0);

/**
 * Describes the message misc.dbip.v1.PublicDBIPServiceIPInfoResponse.
 * Use `create(PublicDBIPServiceIPInfoResponseSchema)` to create a new message.
 */
export const PublicDBIPServiceIPInfoResponseSchema = /*@__PURE__*/
  messageDesc(file_misc_dbip_v1_public, 1);

/**
 * Describes the message misc.dbip.v1.IPInfo.
 * Use `create(IPInfoSchema)` to create a new message.
 */
export const IPInfoSchema = /*@__PURE__*/
  messageDesc(file_misc_dbip_v1_public, 2);

/**
 * Describes the message misc.dbip.v1.NamesRecord.
 * Use `create(NamesRecordSchema)` to create a new message.
 */
export const NamesRecordSchema = /*@__PURE__*/
  messageDesc(file_misc_dbip_v1_public, 3);

/**
 * Describes the message misc.dbip.v1.GeoLocation.
 * Use `create(GeoLocationSchema)` to create a new message.
 */
export const GeoLocationSchema = /*@__PURE__*/
  messageDesc(file_misc_dbip_v1_public, 4);

/**
 * Describes the message misc.dbip.v1.ConnectionInfo.
 * Use `create(ConnectionInfoSchema)` to create a new message.
 */
export const ConnectionInfoSchema = /*@__PURE__*/
  messageDesc(file_misc_dbip_v1_public, 5);

/**
 * Describes the message misc.dbip.v1.PublicDBIPServiceFetchCountriesRequest.
 * Use `create(PublicDBIPServiceFetchCountriesRequestSchema)` to create a new message.
 */
export const PublicDBIPServiceFetchCountriesRequestSchema = /*@__PURE__*/
  messageDesc(file_misc_dbip_v1_public, 6);

/**
 * Describes the message misc.dbip.v1.PublicDBIPServiceFetchCountriesResponse.
 * Use `create(PublicDBIPServiceFetchCountriesResponseSchema)` to create a new message.
 */
export const PublicDBIPServiceFetchCountriesResponseSchema = /*@__PURE__*/
  messageDesc(file_misc_dbip_v1_public, 7);

/**
 * Describes the message misc.dbip.v1.PublicDBIPServiceFetchStatesRequest.
 * Use `create(PublicDBIPServiceFetchStatesRequestSchema)` to create a new message.
 */
export const PublicDBIPServiceFetchStatesRequestSchema = /*@__PURE__*/
  messageDesc(file_misc_dbip_v1_public, 8);

/**
 * Describes the message misc.dbip.v1.PublicDBIPServiceFetchStatesResponse.
 * Use `create(PublicDBIPServiceFetchStatesResponseSchema)` to create a new message.
 */
export const PublicDBIPServiceFetchStatesResponseSchema = /*@__PURE__*/
  messageDesc(file_misc_dbip_v1_public, 9);

/**
 * Describes the message misc.dbip.v1.PublicDBIPServiceFetchCitiesRequest.
 * Use `create(PublicDBIPServiceFetchCitiesRequestSchema)` to create a new message.
 */
export const PublicDBIPServiceFetchCitiesRequestSchema = /*@__PURE__*/
  messageDesc(file_misc_dbip_v1_public, 10);

/**
 * Describes the message misc.dbip.v1.PublicDBIPServiceFetchCitiesResponse.
 * Use `create(PublicDBIPServiceFetchCitiesResponseSchema)` to create a new message.
 */
export const PublicDBIPServiceFetchCitiesResponseSchema = /*@__PURE__*/
  messageDesc(file_misc_dbip_v1_public, 11);

/**
 * @generated from service misc.dbip.v1.PublicDBIPService
 */
export const PublicDBIPService = /*@__PURE__*/
  serviceDesc(file_misc_dbip_v1_public, 0);

