// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/dns/v1/controlplane.proto (package proxymanager.dns.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";

/**
 * Describes the file proxymanager/dns/v1/controlplane.proto.
 */
export const file_proxymanager_dns_v1_controlplane = /*@__PURE__*/
  fileDesc("CiZwcm94eW1hbmFnZXIvZG5zL3YxL2NvbnRyb2xwbGFuZS5wcm90bxITcHJveHltYW5hZ2VyLmRucy52MSJKChdHZXRCbG9ja2VkRG9tYWluUmVxdWVzdBIvCgpwYWdpbmF0aW9uGAEgASgLMhsudXRpbHMudjEuUGFnaW5hdGlvblJlcXVlc3QikAEKGEdldEJsb2NrZWREb21haW5SZXNwb25zZRImCgVlcnJvchgBIAEoCzIXLmVycm1zZy52MS5FcnJvck1lc3NhZ2USLwoKcGFnaW5hdGlvbhgCIAEoCzIbLnV0aWxzLnYxLlBhZ2luYXRpb25SZXF1ZXN0EhsKE2xpc3RfYmxvY2tlZF9kb21haW4YAyADKAkyhAEKEUROU01hbmFnZXJTZXJ2aWNlEm8KEEdldEJsb2NrZWREb21haW4SLC5wcm94eW1hbmFnZXIuZG5zLnYxLkdldEJsb2NrZWREb21haW5SZXF1ZXN0Gi0ucHJveHltYW5hZ2VyLmRucy52MS5HZXRCbG9ja2VkRG9tYWluUmVzcG9uc2VCSlpIZ2l0LnRtcHJveHktaW5mcmEuY29tL2FsZ28vYWxnb3Byb3h5LXByb3RvL2dlbi9wcm94eW1hbmFnZXIvZG5zL3YxO2Ruc3YxYgZwcm90bzM", [file_errmsg_v1_errormsg, file_utils_v1_utils]);

/**
 * Describes the message proxymanager.dns.v1.GetBlockedDomainRequest.
 * Use `create(GetBlockedDomainRequestSchema)` to create a new message.
 */
export const GetBlockedDomainRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_dns_v1_controlplane, 0);

/**
 * Describes the message proxymanager.dns.v1.GetBlockedDomainResponse.
 * Use `create(GetBlockedDomainResponseSchema)` to create a new message.
 */
export const GetBlockedDomainResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_dns_v1_controlplane, 1);

/**
 * @generated from service proxymanager.dns.v1.DNSManagerService
 */
export const DNSManagerService = /*@__PURE__*/
  serviceDesc(file_proxymanager_dns_v1_controlplane, 0);

