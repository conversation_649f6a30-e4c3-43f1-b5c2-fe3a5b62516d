// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/dns/v1/backoffice.proto (package proxymanager.dns.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_algoenum_v1_ip_type } from "../../../algoenum/v1/ip_type_pb";

/**
 * Describes the file proxymanager/dns/v1/backoffice.proto.
 */
export const file_proxymanager_dns_v1_backoffice = /*@__PURE__*/
  fileDesc("CiRwcm94eW1hbmFnZXIvZG5zL3YxL2JhY2tvZmZpY2UucHJvdG8SE3Byb3h5bWFuYWdlci5kbnMudjEi1AEKI0JhY2tvZmZpY2VETlNTZXJ2aWNlRmV0Y2hETlNSZXF1ZXN0Eg4KBmlkX2RucxgBIAEoCRITCgtuYW1lX3NlYXJjaBgCIAEoCRIRCglpcF9zZWFyY2gYAyABKAkSJAoHaXBfdHlwZRgEIAEoDjITLmFsZ29lbnVtLnYxLklQVHlwZRIeCgVzdGF0ZRgFIAEoCzIPLnV0aWxzLnYxLlN0YXRlEi8KCnBhZ2luYXRpb24YBiABKAsyGy51dGlscy52MS5QYWdpbmF0aW9uUmVxdWVzdCK2AQokQmFja29mZmljZUROU1NlcnZpY2VGZXRjaEROU1Jlc3BvbnNlEiYKBWVycm9yGAEgASgLMhcuZXJybXNnLnYxLkVycm9yTWVzc2FnZRIwCgpwYWdpbmF0aW9uGAIgASgLMhwudXRpbHMudjEuUGFnaW5hdGlvblJlc3BvbnNlEjQKCGxpc3RfZG5zGAMgAygLMiIucHJveHltYW5hZ2VyLmRucy52MS5CYWNrb2ZmaWNlRE5TIoIBCg1CYWNrb2ZmaWNlRE5TEg4KBmlkX2RucxgBIAEoCRIkCgdpcF90eXBlGAIgASgOMhMuYWxnb2VudW0udjEuSVBUeXBlEgwKBG5hbWUYAyABKAkSDAoEZG5zMRgEIAEoCRIMCgRkbnMyGAUgASgJEhEKCWlzX2FjdGl2ZRgGIAEoCCJQCiRCYWNrb2ZmaWNlRE5TU2VydmljZUNyZWF0ZUROU1JlcXVlc3QSDAoEbmFtZRgBIAEoCRIMCgRkbnMxGAIgASgJEgwKBGRuczIYAyABKAkiTwolQmFja29mZmljZUROU1NlcnZpY2VDcmVhdGVETlNSZXNwb25zZRImCgVlcnJvchgBIAEoCzIXLmVycm1zZy52MS5FcnJvck1lc3NhZ2UigAEKJEJhY2tvZmZpY2VETlNTZXJ2aWNlVXBkYXRlRE5TUmVxdWVzdBIOCgZpZF9kbnMYASABKAkSDAoEbmFtZRgCIAEoCRIMCgRkbnMxGAMgASgJEgwKBGRuczIYBCABKAkSHgoFc3RhdGUYBSABKAsyDy51dGlscy52MS5TdGF0ZSJPCiVCYWNrb2ZmaWNlRE5TU2VydmljZVVwZGF0ZUROU1Jlc3BvbnNlEiYKBWVycm9yGAEgASgLMhcuZXJybXNnLnYxLkVycm9yTWVzc2FnZTKhAwoUQmFja29mZmljZUROU1NlcnZpY2USfwoIRmV0Y2hETlMSOC5wcm94eW1hbmFnZXIuZG5zLnYxLkJhY2tvZmZpY2VETlNTZXJ2aWNlRmV0Y2hETlNSZXF1ZXN0GjkucHJveHltYW5hZ2VyLmRucy52MS5CYWNrb2ZmaWNlRE5TU2VydmljZUZldGNoRE5TUmVzcG9uc2USggEKCUNyZWF0ZUROUxI5LnByb3h5bWFuYWdlci5kbnMudjEuQmFja29mZmljZUROU1NlcnZpY2VDcmVhdGVETlNSZXF1ZXN0GjoucHJveHltYW5hZ2VyLmRucy52MS5CYWNrb2ZmaWNlRE5TU2VydmljZUNyZWF0ZUROU1Jlc3BvbnNlEoIBCglVcGRhdGVETlMSOS5wcm94eW1hbmFnZXIuZG5zLnYxLkJhY2tvZmZpY2VETlNTZXJ2aWNlVXBkYXRlRE5TUmVxdWVzdBo6LnByb3h5bWFuYWdlci5kbnMudjEuQmFja29mZmljZUROU1NlcnZpY2VVcGRhdGVETlNSZXNwb25zZUJKWkhnaXQudG1wcm94eS1pbmZyYS5jb20vYWxnby9hbGdvcHJveHktcHJvdG8vZ2VuL3Byb3h5bWFuYWdlci9kbnMvdjE7ZG5zdjFiBnByb3RvMw", [file_errmsg_v1_errormsg, file_utils_v1_utils, file_algoenum_v1_ip_type]);

/**
 * Describes the message proxymanager.dns.v1.BackofficeDNSServiceFetchDNSRequest.
 * Use `create(BackofficeDNSServiceFetchDNSRequestSchema)` to create a new message.
 */
export const BackofficeDNSServiceFetchDNSRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_dns_v1_backoffice, 0);

/**
 * Describes the message proxymanager.dns.v1.BackofficeDNSServiceFetchDNSResponse.
 * Use `create(BackofficeDNSServiceFetchDNSResponseSchema)` to create a new message.
 */
export const BackofficeDNSServiceFetchDNSResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_dns_v1_backoffice, 1);

/**
 * Describes the message proxymanager.dns.v1.BackofficeDNS.
 * Use `create(BackofficeDNSSchema)` to create a new message.
 */
export const BackofficeDNSSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_dns_v1_backoffice, 2);

/**
 * Describes the message proxymanager.dns.v1.BackofficeDNSServiceCreateDNSRequest.
 * Use `create(BackofficeDNSServiceCreateDNSRequestSchema)` to create a new message.
 */
export const BackofficeDNSServiceCreateDNSRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_dns_v1_backoffice, 3);

/**
 * Describes the message proxymanager.dns.v1.BackofficeDNSServiceCreateDNSResponse.
 * Use `create(BackofficeDNSServiceCreateDNSResponseSchema)` to create a new message.
 */
export const BackofficeDNSServiceCreateDNSResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_dns_v1_backoffice, 4);

/**
 * Describes the message proxymanager.dns.v1.BackofficeDNSServiceUpdateDNSRequest.
 * Use `create(BackofficeDNSServiceUpdateDNSRequestSchema)` to create a new message.
 */
export const BackofficeDNSServiceUpdateDNSRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_dns_v1_backoffice, 5);

/**
 * Describes the message proxymanager.dns.v1.BackofficeDNSServiceUpdateDNSResponse.
 * Use `create(BackofficeDNSServiceUpdateDNSResponseSchema)` to create a new message.
 */
export const BackofficeDNSServiceUpdateDNSResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_dns_v1_backoffice, 6);

/**
 * @generated from service proxymanager.dns.v1.BackofficeDNSService
 */
export const BackofficeDNSService = /*@__PURE__*/
  serviceDesc(file_proxymanager_dns_v1_backoffice, 0);

