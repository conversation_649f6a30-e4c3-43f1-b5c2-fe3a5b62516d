// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/dns/v1/customer.proto (package proxymanager.dns.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_algoenum_v1_ip_type } from "../../../algoenum/v1/ip_type_pb";

/**
 * Describes the file proxymanager/dns/v1/customer.proto.
 */
export const file_proxymanager_dns_v1_customer = /*@__PURE__*/
  fileDesc("CiJwcm94eW1hbmFnZXIvZG5zL3YxL2N1c3RvbWVyLnByb3RvEhNwcm94eW1hbmFnZXIuZG5zLnYxItIBCiFDdXN0b21lckROU1NlcnZpY2VGZXRjaEROU1JlcXVlc3QSDgoGaWRfZG5zGAEgASgJEhMKC25hbWVfc2VhcmNoGAIgASgJEhEKCWlwX3NlYXJjaBgDIAEoCRIkCgdpcF90eXBlGAQgASgOMhMuYWxnb2VudW0udjEuSVBUeXBlEh4KBXN0YXRlGAUgASgLMg8udXRpbHMudjEuU3RhdGUSLwoKcGFnaW5hdGlvbhgGIAEoCzIbLnV0aWxzLnYxLlBhZ2luYXRpb25SZXF1ZXN0IrIBCiJDdXN0b21lckROU1NlcnZpY2VGZXRjaEROU1Jlc3BvbnNlEiYKBWVycm9yGAEgASgLMhcuZXJybXNnLnYxLkVycm9yTWVzc2FnZRIwCgpwYWdpbmF0aW9uGAIgASgLMhwudXRpbHMudjEuUGFnaW5hdGlvblJlc3BvbnNlEjIKCGxpc3RfZG5zGAMgAygLMiAucHJveHltYW5hZ2VyLmRucy52MS5DdXN0b21lckROUyJRCgtDdXN0b21lckROUxIOCgZpZF9kbnMYASABKAkSJAoHaXBfdHlwZRgCIAEoDjITLmFsZ29lbnVtLnYxLklQVHlwZRIMCgRuYW1lGAMgASgJMpEBChJDdXN0b21lckROU1NlcnZpY2USewoIRmV0Y2hETlMSNi5wcm94eW1hbmFnZXIuZG5zLnYxLkN1c3RvbWVyRE5TU2VydmljZUZldGNoRE5TUmVxdWVzdBo3LnByb3h5bWFuYWdlci5kbnMudjEuQ3VzdG9tZXJETlNTZXJ2aWNlRmV0Y2hETlNSZXNwb25zZUJKWkhnaXQudG1wcm94eS1pbmZyYS5jb20vYWxnby9hbGdvcHJveHktcHJvdG8vZ2VuL3Byb3h5bWFuYWdlci9kbnMvdjE7ZG5zdjFiBnByb3RvMw", [file_errmsg_v1_errormsg, file_utils_v1_utils, file_algoenum_v1_ip_type]);

/**
 * Describes the message proxymanager.dns.v1.CustomerDNSServiceFetchDNSRequest.
 * Use `create(CustomerDNSServiceFetchDNSRequestSchema)` to create a new message.
 */
export const CustomerDNSServiceFetchDNSRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_dns_v1_customer, 0);

/**
 * Describes the message proxymanager.dns.v1.CustomerDNSServiceFetchDNSResponse.
 * Use `create(CustomerDNSServiceFetchDNSResponseSchema)` to create a new message.
 */
export const CustomerDNSServiceFetchDNSResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_dns_v1_customer, 1);

/**
 * Describes the message proxymanager.dns.v1.CustomerDNS.
 * Use `create(CustomerDNSSchema)` to create a new message.
 */
export const CustomerDNSSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_dns_v1_customer, 2);

/**
 * @generated from service proxymanager.dns.v1.CustomerDNSService
 */
export const CustomerDNSService = /*@__PURE__*/
  serviceDesc(file_proxymanager_dns_v1_customer, 0);

