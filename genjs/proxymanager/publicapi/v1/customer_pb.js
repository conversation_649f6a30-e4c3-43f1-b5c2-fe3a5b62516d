// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/publicapi/v1/customer.proto (package proxymanager.publicapi.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_algoenum_v1_location_level } from "../../../algoenum/v1/location_level_pb";

/**
 * Describes the file proxymanager/publicapi/v1/customer.proto.
 */
export const file_proxymanager_publicapi_v1_customer = /*@__PURE__*/
  fileDesc("Cihwcm94eW1hbmFnZXIvcHVibGljYXBpL3YxL2N1c3RvbWVyLnByb3RvEhlwcm94eW1hbmFnZXIucHVibGljYXBpLnYxIkEKKkN1c3RvbWVyUHVibGljQVBJU2VydmljZUNoYW5nZVByb3h5UmVxdWVzdBITCgtwcm94eV90b2tlbhgBIAEoCSKMAQorQ3VzdG9tZXJQdWJsaWNBUElTZXJ2aWNlQ2hhbmdlUHJveHlSZXNwb25zZRImCgVlcnJvchgBIAEoCzIXLmVycm1zZy52MS5FcnJvck1lc3NhZ2USNQoFcHJveHkYAiABKAsyJi5wcm94eW1hbmFnZXIucHVibGljYXBpLnYxLlByb3h5RW50aXR5IroCCgtQcm94eUVudGl0eRINCgVwcm94eRgBIAEoCRIQCghpcF9wcm94eRgCIAEoCRISCgpwb3J0X3Byb3h5GAMgASgDEhEKCWlwX2FsbG93cxgEIAMoCRIWCg51c2VybmFtZV9wcm94eRgFIAEoCRIWCg5wYXNzd29yZF9wcm94eRgGIAEoCRIRCglpcF9wdWJsaWMYByABKAkSUAoHY291bnRyeRgJIAEoCzI/LnByb3h5bWFuYWdlci5wdWJsaWNhcGkudjEuQ3VzdG9tZXJQdWJsaWNBUElQcm94eUVudGl0eUxvY2F0aW9uEk4KBXN0YXRlGAogASgLMj8ucHJveHltYW5hZ2VyLnB1YmxpY2FwaS52MS5DdXN0b21lclB1YmxpY0FQSVByb3h5RW50aXR5TG9jYXRpb24ibgokQ3VzdG9tZXJQdWJsaWNBUElQcm94eUVudGl0eUxvY2F0aW9uEgwKBG5hbWUYASABKAkSKQoFbGV2ZWwYAiABKA4yGi5hbGdvZW51bS52MS5Mb2NhdGlvbkxldmVsEg0KBWVtb2ppGAMgASgJMrkBChhDdXN0b21lclB1YmxpY0FQSVNlcnZpY2USnAEKC0NoYW5nZVByb3h5EkUucHJveHltYW5hZ2VyLnB1YmxpY2FwaS52MS5DdXN0b21lclB1YmxpY0FQSVNlcnZpY2VDaGFuZ2VQcm94eVJlcXVlc3QaRi5wcm94eW1hbmFnZXIucHVibGljYXBpLnYxLkN1c3RvbWVyUHVibGljQVBJU2VydmljZUNoYW5nZVByb3h5UmVzcG9uc2VCVlpUZ2l0LnRtcHJveHktaW5mcmEuY29tL2FsZ28vYWxnb3Byb3h5LXByb3RvL2dlbi9wcm94eW1hbmFnZXIvcHVibGljYXBpL3YxO3B1YmxpY2FwaXYxYgZwcm90bzM", [file_errmsg_v1_errormsg, file_algoenum_v1_location_level]);

/**
 * Describes the message proxymanager.publicapi.v1.CustomerPublicAPIServiceChangeProxyRequest.
 * Use `create(CustomerPublicAPIServiceChangeProxyRequestSchema)` to create a new message.
 */
export const CustomerPublicAPIServiceChangeProxyRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_publicapi_v1_customer, 0);

/**
 * Describes the message proxymanager.publicapi.v1.CustomerPublicAPIServiceChangeProxyResponse.
 * Use `create(CustomerPublicAPIServiceChangeProxyResponseSchema)` to create a new message.
 */
export const CustomerPublicAPIServiceChangeProxyResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_publicapi_v1_customer, 1);

/**
 * Describes the message proxymanager.publicapi.v1.ProxyEntity.
 * Use `create(ProxyEntitySchema)` to create a new message.
 */
export const ProxyEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_publicapi_v1_customer, 2);

/**
 * Describes the message proxymanager.publicapi.v1.CustomerPublicAPIProxyEntityLocation.
 * Use `create(CustomerPublicAPIProxyEntityLocationSchema)` to create a new message.
 */
export const CustomerPublicAPIProxyEntityLocationSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_publicapi_v1_customer, 3);

/**
 * @generated from service proxymanager.publicapi.v1.CustomerPublicAPIService
 */
export const CustomerPublicAPIService = /*@__PURE__*/
  serviceDesc(file_proxymanager_publicapi_v1_customer, 0);

