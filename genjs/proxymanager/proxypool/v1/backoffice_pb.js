// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/proxypool/v1/backoffice.proto (package proxymanager.proxypool.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_algoenum_v1_service } from "../../../algoenum/v1/service_pb";
import { file_algoenum_v1_location_level } from "../../../algoenum/v1/location_level_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";

/**
 * Describes the file proxymanager/proxypool/v1/backoffice.proto.
 */
export const file_proxymanager_proxypool_v1_backoffice = /*@__PURE__*/
  fileDesc("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", [file_errmsg_v1_errormsg, file_algoenum_v1_service, file_algoenum_v1_location_level, file_utils_v1_utils]);

/**
 * Describes the message proxymanager.proxypool.v1.BackofficeProxyPoolServiceHealthCheckRequest.
 * Use `create(BackofficeProxyPoolServiceHealthCheckRequestSchema)` to create a new message.
 */
export const BackofficeProxyPoolServiceHealthCheckRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxypool_v1_backoffice, 0);

/**
 * Describes the message proxymanager.proxypool.v1.BackofficeProxyPoolServiceHealthCheckResponse.
 * Use `create(BackofficeProxyPoolServiceHealthCheckResponseSchema)` to create a new message.
 */
export const BackofficeProxyPoolServiceHealthCheckResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxypool_v1_backoffice, 1);

/**
 * Describes the message proxymanager.proxypool.v1.BackofficeProxyPoolServiceRemoveProxyPoolRequest.
 * Use `create(BackofficeProxyPoolServiceRemoveProxyPoolRequestSchema)` to create a new message.
 */
export const BackofficeProxyPoolServiceRemoveProxyPoolRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxypool_v1_backoffice, 2);

/**
 * Describes the message proxymanager.proxypool.v1.BackofficeProxyPoolServiceRemoveProxyPoolResponse.
 * Use `create(BackofficeProxyPoolServiceRemoveProxyPoolResponseSchema)` to create a new message.
 */
export const BackofficeProxyPoolServiceRemoveProxyPoolResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxypool_v1_backoffice, 3);

/**
 * Describes the message proxymanager.proxypool.v1.BackofficeProxyPoolServiceFetchProxyPoolRequest.
 * Use `create(BackofficeProxyPoolServiceFetchProxyPoolRequestSchema)` to create a new message.
 */
export const BackofficeProxyPoolServiceFetchProxyPoolRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxypool_v1_backoffice, 4);

/**
 * Describes the message proxymanager.proxypool.v1.BackofficeProxyPoolServiceFetchProxyPoolResponse.
 * Use `create(BackofficeProxyPoolServiceFetchProxyPoolResponseSchema)` to create a new message.
 */
export const BackofficeProxyPoolServiceFetchProxyPoolResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxypool_v1_backoffice, 5);

/**
 * Describes the message proxymanager.proxypool.v1.BackofficeProxyPoolServiceProxyPoolEntity.
 * Use `create(BackofficeProxyPoolServiceProxyPoolEntitySchema)` to create a new message.
 */
export const BackofficeProxyPoolServiceProxyPoolEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxypool_v1_backoffice, 6);

/**
 * Describes the message proxymanager.proxypool.v1.BackofficeProxyPoolServiceProxyPoolEntityLocation.
 * Use `create(BackofficeProxyPoolServiceProxyPoolEntityLocationSchema)` to create a new message.
 */
export const BackofficeProxyPoolServiceProxyPoolEntityLocationSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxypool_v1_backoffice, 7);

/**
 * @generated from service proxymanager.proxypool.v1.BackofficeProxyPoolService
 */
export const BackofficeProxyPoolService = /*@__PURE__*/
  serviceDesc(file_proxymanager_proxypool_v1_backoffice, 0);

