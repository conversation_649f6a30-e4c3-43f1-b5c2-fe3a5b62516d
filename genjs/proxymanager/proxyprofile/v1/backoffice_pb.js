// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/proxyprofile/v1/backoffice.proto (package proxymanager.proxyprofile.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_algoenum_v1_location_level } from "../../../algoenum/v1/location_level_pb";

/**
 * Describes the file proxymanager/proxyprofile/v1/backoffice.proto.
 */
export const file_proxymanager_proxyprofile_v1_backoffice = /*@__PURE__*/
  fileDesc("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", [file_errmsg_v1_errormsg, file_utils_v1_utils, file_algoenum_v1_location_level]);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceResetProxyProfileRequest.
 * Use `create(BackofficeProxyProfileServiceResetProxyProfileRequestSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceResetProxyProfileRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 0);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceResetProxyProfileResponse.
 * Use `create(BackofficeProxyProfileServiceResetProxyProfileResponseSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceResetProxyProfileResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 1);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceFetchProxyProfileRequest.
 * Use `create(BackofficeProxyProfileServiceFetchProxyProfileRequestSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceFetchProxyProfileRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 2);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceFetchProxyProfileResponse.
 * Use `create(BackofficeProxyProfileServiceFetchProxyProfileResponseSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceFetchProxyProfileResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 3);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntity.
 * Use `create(BackofficeProxyProfileServiceProxyProfileEntitySchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceProxyProfileEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 4);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntityDNS.
 * Use `create(BackofficeProxyProfileServiceProxyProfileEntityDNSSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceProxyProfileEntityDNSSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 5);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntityTelco.
 * Use `create(BackofficeProxyProfileServiceProxyProfileEntityTelcoSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceProxyProfileEntityTelcoSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 6);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceProxyProfileEntityLocation.
 * Use `create(BackofficeProxyProfileServiceProxyProfileEntityLocationSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceProxyProfileEntityLocationSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 7);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileLocationRequest.
 * Use `create(BackofficeProxyProfileServiceConfigProxyProfileLocationRequestSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceConfigProxyProfileLocationRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 8);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileLocationResponse.
 * Use `create(BackofficeProxyProfileServiceConfigProxyProfileLocationResponseSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceConfigProxyProfileLocationResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 9);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest.
 * Use `create(BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequestSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 10);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse.
 * Use `create(BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponseSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 11);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest.
 * Use `create(BackofficeProxyProfileServiceConfigProxyProfileTelcoRequestSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceConfigProxyProfileTelcoRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 12);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse.
 * Use `create(BackofficeProxyProfileServiceConfigProxyProfileTelcoResponseSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceConfigProxyProfileTelcoResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 13);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceSetDefaultProxyProfileRequest.
 * Use `create(BackofficeProxyProfileServiceSetDefaultProxyProfileRequestSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceSetDefaultProxyProfileRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 14);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceSetDefaultProxyProfileResponse.
 * Use `create(BackofficeProxyProfileServiceSetDefaultProxyProfileResponseSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceSetDefaultProxyProfileResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 15);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceCreateProxyProfileRequest.
 * Use `create(BackofficeProxyProfileServiceCreateProxyProfileRequestSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceCreateProxyProfileRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 16);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceCreateProxyProfileResponse.
 * Use `create(BackofficeProxyProfileServiceCreateProxyProfileResponseSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceCreateProxyProfileResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 17);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceUpdateProxyProfileRequest.
 * Use `create(BackofficeProxyProfileServiceUpdateProxyProfileRequestSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceUpdateProxyProfileRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 18);

/**
 * Describes the message proxymanager.proxyprofile.v1.BackofficeProxyProfileServiceUpdateProxyProfileResponse.
 * Use `create(BackofficeProxyProfileServiceUpdateProxyProfileResponseSchema)` to create a new message.
 */
export const BackofficeProxyProfileServiceUpdateProxyProfileResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_backoffice, 19);

/**
 * @generated from service proxymanager.proxyprofile.v1.BackofficeProxyProfileService
 */
export const BackofficeProxyProfileService = /*@__PURE__*/
  serviceDesc(file_proxymanager_proxyprofile_v1_backoffice, 0);

