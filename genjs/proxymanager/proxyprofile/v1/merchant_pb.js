// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/proxyprofile/v1/merchant.proto (package proxymanager.proxyprofile.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_algoenum_v1_ip_type } from "../../../algoenum/v1/ip_type_pb";
import { file_algoenum_v1_proxy_type } from "../../../algoenum/v1/proxy_type_pb";
import { file_algoenum_v1_change_type } from "../../../algoenum/v1/change_type_pb";
import { file_algoenum_v1_data_transfer_type } from "../../../algoenum/v1/data_transfer_type_pb";
import { file_algoenum_v1_location_level } from "../../../algoenum/v1/location_level_pb";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";

/**
 * Describes the file proxymanager/proxyprofile/v1/merchant.proto.
 */
export const file_proxymanager_proxyprofile_v1_merchant = /*@__PURE__*/
  fileDesc("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", [file_algoenum_v1_ip_type, file_algoenum_v1_proxy_type, file_algoenum_v1_change_type, file_algoenum_v1_data_transfer_type, file_algoenum_v1_location_level, file_errmsg_v1_errormsg, file_utils_v1_utils]);

/**
 * Describes the message proxymanager.proxyprofile.v1.MerchantProxyProfileServiceFetchProxyProfileRequest.
 * Use `create(MerchantProxyProfileServiceFetchProxyProfileRequestSchema)` to create a new message.
 */
export const MerchantProxyProfileServiceFetchProxyProfileRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_merchant, 0);

/**
 * Describes the message proxymanager.proxyprofile.v1.MerchantProxyProfileServiceFetchProxyProfileResponse.
 * Use `create(MerchantProxyProfileServiceFetchProxyProfileResponseSchema)` to create a new message.
 */
export const MerchantProxyProfileServiceFetchProxyProfileResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_merchant, 1);

/**
 * Describes the message proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntity.
 * Use `create(MerchantProxyProfileServiceProxyProfileEntitySchema)` to create a new message.
 */
export const MerchantProxyProfileServiceProxyProfileEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_merchant, 2);

/**
 * Describes the message proxymanager.proxyprofile.v1.MerchantProxyProfileUser.
 * Use `create(MerchantProxyProfileUserSchema)` to create a new message.
 */
export const MerchantProxyProfileUserSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_merchant, 3);

/**
 * Describes the message proxymanager.proxyprofile.v1.MerchantProxyProfilePlan.
 * Use `create(MerchantProxyProfilePlanSchema)` to create a new message.
 */
export const MerchantProxyProfilePlanSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_merchant, 4);

/**
 * Describes the message proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntityDNS.
 * Use `create(MerchantProxyProfileServiceProxyProfileEntityDNSSchema)` to create a new message.
 */
export const MerchantProxyProfileServiceProxyProfileEntityDNSSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_merchant, 5);

/**
 * Describes the message proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntityTelco.
 * Use `create(MerchantProxyProfileServiceProxyProfileEntityTelcoSchema)` to create a new message.
 */
export const MerchantProxyProfileServiceProxyProfileEntityTelcoSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_merchant, 6);

/**
 * Describes the message proxymanager.proxyprofile.v1.MerchantProxyProfileServiceProxyProfileEntityLocation.
 * Use `create(MerchantProxyProfileServiceProxyProfileEntityLocationSchema)` to create a new message.
 */
export const MerchantProxyProfileServiceProxyProfileEntityLocationSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_merchant, 7);

/**
 * @generated from service proxymanager.proxyprofile.v1.MerchantProxyProfileService
 */
export const MerchantProxyProfileService = /*@__PURE__*/
  serviceDesc(file_proxymanager_proxyprofile_v1_merchant, 0);

