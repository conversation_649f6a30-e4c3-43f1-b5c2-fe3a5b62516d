// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/proxyprofile/v1/customer.proto (package proxymanager.proxyprofile.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_algoenum_v1_location_level } from "../../../algoenum/v1/location_level_pb";

/**
 * Describes the file proxymanager/proxyprofile/v1/customer.proto.
 */
export const file_proxymanager_proxyprofile_v1_customer = /*@__PURE__*/
  fileDesc("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", [file_utils_v1_utils, file_errmsg_v1_errormsg, file_algoenum_v1_location_level]);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceResetProxyProfileRequest.
 * Use `create(CustomerProxyProfileServiceResetProxyProfileRequestSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceResetProxyProfileRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 0);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceResetProxyProfileResponse.
 * Use `create(CustomerProxyProfileServiceResetProxyProfileResponseSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceResetProxyProfileResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 1);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceApplyProxyProfileRequest.
 * Use `create(CustomerProxyProfileServiceApplyProxyProfileRequestSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceApplyProxyProfileRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 2);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceApplyProxyProfileResponse.
 * Use `create(CustomerProxyProfileServiceApplyProxyProfileResponseSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceApplyProxyProfileResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 3);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileTelcoRequest.
 * Use `create(CustomerProxyProfileServiceConfigProxyProfileTelcoRequestSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceConfigProxyProfileTelcoRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 4);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileTelcoResponse.
 * Use `create(CustomerProxyProfileServiceConfigProxyProfileTelcoResponseSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceConfigProxyProfileTelcoResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 5);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileLocationRequest.
 * Use `create(CustomerProxyProfileServiceConfigProxyProfileLocationRequestSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceConfigProxyProfileLocationRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 6);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileLocationResponse.
 * Use `create(CustomerProxyProfileServiceConfigProxyProfileLocationResponseSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceConfigProxyProfileLocationResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 7);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest.
 * Use `create(CustomerProxyProfileServiceConfigProxyProfileIPAllowRequestSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceConfigProxyProfileIPAllowRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 8);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse.
 * Use `create(CustomerProxyProfileServiceConfigProxyProfileIPAllowResponseSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceConfigProxyProfileIPAllowResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 9);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceFetchProxyProfileRequest.
 * Use `create(CustomerProxyProfileServiceFetchProxyProfileRequestSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceFetchProxyProfileRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 10);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceFetchProxyProfileResponse.
 * Use `create(CustomerProxyProfileServiceFetchProxyProfileResponseSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceFetchProxyProfileResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 11);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntity.
 * Use `create(CustomerProxyProfileServiceProxyProfileEntitySchema)` to create a new message.
 */
export const CustomerProxyProfileServiceProxyProfileEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 12);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntityDNS.
 * Use `create(CustomerProxyProfileServiceProxyProfileEntityDNSSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceProxyProfileEntityDNSSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 13);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntityTelco.
 * Use `create(CustomerProxyProfileServiceProxyProfileEntityTelcoSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceProxyProfileEntityTelcoSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 14);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceProxyProfileEntityLocation.
 * Use `create(CustomerProxyProfileServiceProxyProfileEntityLocationSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceProxyProfileEntityLocationSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 15);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceCreateProxyProfileRequest.
 * Use `create(CustomerProxyProfileServiceCreateProxyProfileRequestSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceCreateProxyProfileRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 16);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceCreateProxyProfileResponse.
 * Use `create(CustomerProxyProfileServiceCreateProxyProfileResponseSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceCreateProxyProfileResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 17);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceUpdateProxyProfileRequest.
 * Use `create(CustomerProxyProfileServiceUpdateProxyProfileRequestSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceUpdateProxyProfileRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 18);

/**
 * Describes the message proxymanager.proxyprofile.v1.CustomerProxyProfileServiceUpdateProxyProfileResponse.
 * Use `create(CustomerProxyProfileServiceUpdateProxyProfileResponseSchema)` to create a new message.
 */
export const CustomerProxyProfileServiceUpdateProxyProfileResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_proxyprofile_v1_customer, 19);

/**
 * @generated from service proxymanager.proxyprofile.v1.CustomerProxyProfileService
 */
export const CustomerProxyProfileService = /*@__PURE__*/
  serviceDesc(file_proxymanager_proxyprofile_v1_customer, 0);

