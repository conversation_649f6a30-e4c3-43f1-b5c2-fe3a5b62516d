// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/tracking/v2/tracking.proto (package proxymanager.tracking.v2, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";

/**
 * Describes the file proxymanager/tracking/v2/tracking.proto.
 */
export const file_proxymanager_tracking_v2_tracking = /*@__PURE__*/
  fileDesc("Cidwcm94eW1hbmFnZXIvdHJhY2tpbmcvdjIvdHJhY2tpbmcucHJvdG8SGHByb3h5bWFuYWdlci50cmFja2luZy52MiJTCg9UcmFja2luZ1JlcXVlc3QSQAoNbGlzdF90cmFja2luZxgBIAMoCzIpLnByb3h5bWFuYWdlci50cmFja2luZy52Mi5UcmFja2luZ1NlZ21lbnQiigEKD1RyYWNraW5nU2VnbWVudBISCgppZF9zZXNzaW9uGAEgASgJEg8KB2lwX3VzZXIYAiABKAkSDgoGZG9tYWluGAMgASgJEhQKDGlwX29mX2RvbWFpbhgEIAEoCRIUCgxieXRlc191cGxvYWQYBSABKAMSFgoOYnl0ZXNfZG93bmxvYWQYBiABKAMiIwoQVHJhY2tpbmdSZXNwb25zZRIPCgdzdWNjZXNzGAEgASgIIjoKF0NoZWNrRG9tYWluQmxvY2tSZXF1ZXN0Eg8KB2lwX3VzZXIYASABKAkSDgoGZG9tYWluGAIgASgJIiwKGENoZWNrRG9tYWluQmxvY2tSZXNwb25zZRIQCghpc19hbGxvdxgBIAEoCDLvAQoPVHJhY2tpbmdTZXJ2aWNlEmEKCFRyYWNraW5nEikucHJveHltYW5hZ2VyLnRyYWNraW5nLnYyLlRyYWNraW5nUmVxdWVzdBoqLnByb3h5bWFuYWdlci50cmFja2luZy52Mi5UcmFja2luZ1Jlc3BvbnNlEnkKEENoZWNrRG9tYWluQmxvY2sSMS5wcm94eW1hbmFnZXIudHJhY2tpbmcudjIuQ2hlY2tEb21haW5CbG9ja1JlcXVlc3QaMi5wcm94eW1hbmFnZXIudHJhY2tpbmcudjIuQ2hlY2tEb21haW5CbG9ja1Jlc3BvbnNlQlRaUmdpdC50bXByb3h5LWluZnJhLmNvbS9hbGdvL2FsZ29wcm94eS1wcm90by9nZW4vcHJveHltYW5hZ2VyL3RyYWNraW5nL3YyO3RyYWNraW5ndjJiBnByb3RvMw");

/**
 * Describes the message proxymanager.tracking.v2.TrackingRequest.
 * Use `create(TrackingRequestSchema)` to create a new message.
 */
export const TrackingRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_tracking_v2_tracking, 0);

/**
 * Describes the message proxymanager.tracking.v2.TrackingSegment.
 * Use `create(TrackingSegmentSchema)` to create a new message.
 */
export const TrackingSegmentSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_tracking_v2_tracking, 1);

/**
 * Describes the message proxymanager.tracking.v2.TrackingResponse.
 * Use `create(TrackingResponseSchema)` to create a new message.
 */
export const TrackingResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_tracking_v2_tracking, 2);

/**
 * Describes the message proxymanager.tracking.v2.CheckDomainBlockRequest.
 * Use `create(CheckDomainBlockRequestSchema)` to create a new message.
 */
export const CheckDomainBlockRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_tracking_v2_tracking, 3);

/**
 * Describes the message proxymanager.tracking.v2.CheckDomainBlockResponse.
 * Use `create(CheckDomainBlockResponseSchema)` to create a new message.
 */
export const CheckDomainBlockResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_tracking_v2_tracking, 4);

/**
 * @generated from service proxymanager.tracking.v2.TrackingService
 */
export const TrackingService = /*@__PURE__*/
  serviceDesc(file_proxymanager_tracking_v2_tracking, 0);

