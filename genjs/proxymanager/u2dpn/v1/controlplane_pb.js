// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/u2dpn/v1/controlplane.proto (package proxymanager.u2dpn.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";

/**
 * Describes the file proxymanager/u2dpn/v1/controlplane.proto.
 */
export const file_proxymanager_u2dpn_v1_controlplane = /*@__PURE__*/
  fileDesc("Cihwcm94eW1hbmFnZXIvdTJkcG4vdjEvY29udHJvbHBsYW5lLnByb3RvEhVwcm94eW1hbmFnZXIudTJkcG4udjEiZAo3Q29udHJvbFBsYW5lVTJkcG5TZXJ2aWNlSW5zdGFudFByb3h5Tm90aWZpY2F0aW9uUmVxdWVzdBIYChBpZF91MmRwbl9zZXNzaW9uGAEgASgJEg8KB2lwX2FkZHIYAiABKAkiYgo4Q29udHJvbFBsYW5lVTJkcG5TZXJ2aWNlSW5zdGFudFByb3h5Tm90aWZpY2F0aW9uUmVzcG9uc2USJgoFZXJyb3IYASABKAsyFy5lcnJtc2cudjEuRXJyb3JNZXNzYWdlMtgBChhDb250cm9sUGxhbmVVMmRwblNlcnZpY2USuwEKGEluc3RhbnRQcm94eU5vdGlmaWNhdGlvbhJOLnByb3h5bWFuYWdlci51MmRwbi52MS5Db250cm9sUGxhbmVVMmRwblNlcnZpY2VJbnN0YW50UHJveHlOb3RpZmljYXRpb25SZXF1ZXN0Gk8ucHJveHltYW5hZ2VyLnUyZHBuLnYxLkNvbnRyb2xQbGFuZVUyZHBuU2VydmljZUluc3RhbnRQcm94eU5vdGlmaWNhdGlvblJlc3BvbnNlQk5aTGdpdC50bXByb3h5LWluZnJhLmNvbS9hbGdvL2FsZ29wcm94eS1wcm90by9nZW4vcHJveHltYW5hZ2VyL3UyZHBuL3YxO3UyZHBudjFiBnByb3RvMw", [file_errmsg_v1_errormsg]);

/**
 * Describes the message proxymanager.u2dpn.v1.ControlPlaneU2dpnServiceInstantProxyNotificationRequest.
 * Use `create(ControlPlaneU2dpnServiceInstantProxyNotificationRequestSchema)` to create a new message.
 */
export const ControlPlaneU2dpnServiceInstantProxyNotificationRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_u2dpn_v1_controlplane, 0);

/**
 * Describes the message proxymanager.u2dpn.v1.ControlPlaneU2dpnServiceInstantProxyNotificationResponse.
 * Use `create(ControlPlaneU2dpnServiceInstantProxyNotificationResponseSchema)` to create a new message.
 */
export const ControlPlaneU2dpnServiceInstantProxyNotificationResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_u2dpn_v1_controlplane, 1);

/**
 * @generated from service proxymanager.u2dpn.v1.ControlPlaneU2dpnService
 */
export const ControlPlaneU2dpnService = /*@__PURE__*/
  serviceDesc(file_proxymanager_u2dpn_v1_controlplane, 0);

