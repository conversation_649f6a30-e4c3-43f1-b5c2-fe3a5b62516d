// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/residential/v1/backoffice.proto (package proxymanager.residential.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_proxymanager_telco_v1_backoffice } from "../../telco/v1/backoffice_pb";
import { file_algoenum_v1_res_node } from "../../../algoenum/v1/res_node_pb";

/**
 * Describes the file proxymanager/residential/v1/backoffice.proto.
 */
export const file_proxymanager_residential_v1_backoffice = /*@__PURE__*/
  fileDesc("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", [file_utils_v1_utils, file_errmsg_v1_errormsg, file_proxymanager_telco_v1_backoffice, file_algoenum_v1_res_node]);

/**
 * Describes the message proxymanager.residential.v1.BackofficeResidentialServiceRestartPortRequest.
 * Use `create(BackofficeResidentialServiceRestartPortRequestSchema)` to create a new message.
 */
export const BackofficeResidentialServiceRestartPortRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 0);

/**
 * Describes the message proxymanager.residential.v1.BackofficeResidentialServiceRestartPortResponse.
 * Use `create(BackofficeResidentialServiceRestartPortResponseSchema)` to create a new message.
 */
export const BackofficeResidentialServiceRestartPortResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 1);

/**
 * Describes the message proxymanager.residential.v1.CreateResAccountRequest.
 * Use `create(CreateResAccountRequestSchema)` to create a new message.
 */
export const CreateResAccountRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 2);

/**
 * Describes the message proxymanager.residential.v1.CreateResAccountResponse.
 * Use `create(CreateResAccountResponseSchema)` to create a new message.
 */
export const CreateResAccountResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 3);

/**
 * Describes the message proxymanager.residential.v1.FetchResAccountRequest.
 * Use `create(FetchResAccountRequestSchema)` to create a new message.
 */
export const FetchResAccountRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 4);

/**
 * Describes the message proxymanager.residential.v1.FetchResAccountBinding.
 * Use `create(FetchResAccountBindingSchema)` to create a new message.
 */
export const FetchResAccountBindingSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 5);

/**
 * Describes the message proxymanager.residential.v1.FetchResAccountResponse.
 * Use `create(FetchResAccountResponseSchema)` to create a new message.
 */
export const FetchResAccountResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 6);

/**
 * Describes the message proxymanager.residential.v1.UpdateResAccountRequest.
 * Use `create(UpdateResAccountRequestSchema)` to create a new message.
 */
export const UpdateResAccountRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 7);

/**
 * Describes the message proxymanager.residential.v1.UpdateResAccountResponse.
 * Use `create(UpdateResAccountResponseSchema)` to create a new message.
 */
export const UpdateResAccountResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 8);

/**
 * Describes the message proxymanager.residential.v1.CreateResNodeRequest.
 * Use `create(CreateResNodeRequestSchema)` to create a new message.
 */
export const CreateResNodeRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 9);

/**
 * Describes the message proxymanager.residential.v1.CreateResNodeResponse.
 * Use `create(CreateResNodeResponseSchema)` to create a new message.
 */
export const CreateResNodeResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 10);

/**
 * Describes the message proxymanager.residential.v1.UpdateResNodeRequest.
 * Use `create(UpdateResNodeRequestSchema)` to create a new message.
 */
export const UpdateResNodeRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 11);

/**
 * Describes the message proxymanager.residential.v1.UpdateResNodeResponse.
 * Use `create(UpdateResNodeResponseSchema)` to create a new message.
 */
export const UpdateResNodeResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 12);

/**
 * Describes the message proxymanager.residential.v1.FetchResNodeRequest.
 * Use `create(FetchResNodeRequestSchema)` to create a new message.
 */
export const FetchResNodeRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 13);

/**
 * Describes the message proxymanager.residential.v1.FetchResNodeResponse.
 * Use `create(FetchResNodeResponseSchema)` to create a new message.
 */
export const FetchResNodeResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 14);

/**
 * Describes the message proxymanager.residential.v1.FetchResPortRequest.
 * Use `create(FetchResPortRequestSchema)` to create a new message.
 */
export const FetchResPortRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 15);

/**
 * Describes the message proxymanager.residential.v1.FetchResPortResponse.
 * Use `create(FetchResPortResponseSchema)` to create a new message.
 */
export const FetchResPortResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 16);

/**
 * Describes the message proxymanager.residential.v1.UpdateResPortRequest.
 * Use `create(UpdateResPortRequestSchema)` to create a new message.
 */
export const UpdateResPortRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 17);

/**
 * Describes the message proxymanager.residential.v1.UpdateResPortResponse.
 * Use `create(UpdateResPortResponseSchema)` to create a new message.
 */
export const UpdateResPortResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 18);

/**
 * Describes the message proxymanager.residential.v1.FetchResDeviceRequest.
 * Use `create(FetchResDeviceRequestSchema)` to create a new message.
 */
export const FetchResDeviceRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 19);

/**
 * Describes the message proxymanager.residential.v1.FetchResDeviceResponse.
 * Use `create(FetchResDeviceResponseSchema)` to create a new message.
 */
export const FetchResDeviceResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 20);

/**
 * Describes the message proxymanager.residential.v1.Account.
 * Use `create(AccountSchema)` to create a new message.
 */
export const AccountSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 21);

/**
 * Describes the message proxymanager.residential.v1.AccountNode.
 * Use `create(AccountNodeSchema)` to create a new message.
 */
export const AccountNodeSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 22);

/**
 * Describes the message proxymanager.residential.v1.AccountPort.
 * Use `create(AccountPortSchema)` to create a new message.
 */
export const AccountPortSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 23);

/**
 * Describes the message proxymanager.residential.v1.Node.
 * Use `create(NodeSchema)` to create a new message.
 */
export const NodeSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 24);

/**
 * Describes the message proxymanager.residential.v1.BackofficePort.
 * Use `create(BackofficePortSchema)` to create a new message.
 */
export const BackofficePortSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 25);

/**
 * Describes the message proxymanager.residential.v1.BackofficePortAccount.
 * Use `create(BackofficePortAccountSchema)` to create a new message.
 */
export const BackofficePortAccountSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 26);

/**
 * Describes the message proxymanager.residential.v1.BackofficePortAccountTelco.
 * Use `create(BackofficePortAccountTelcoSchema)` to create a new message.
 */
export const BackofficePortAccountTelcoSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 27);

/**
 * Describes the message proxymanager.residential.v1.BackofficeDevice.
 * Use `create(BackofficeDeviceSchema)` to create a new message.
 */
export const BackofficeDeviceSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 28);

/**
 * Describes the message proxymanager.residential.v1.BackofficeDeviceNode.
 * Use `create(BackofficeDeviceNodeSchema)` to create a new message.
 */
export const BackofficeDeviceNodeSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 29);

/**
 * Describes the message proxymanager.residential.v1.BackofficeDeviceNodeLocation.
 * Use `create(BackofficeDeviceNodeLocationSchema)` to create a new message.
 */
export const BackofficeDeviceNodeLocationSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 30);

/**
 * Describes the message proxymanager.residential.v1.BackofficeDevicePort.
 * Use `create(BackofficeDevicePortSchema)` to create a new message.
 */
export const BackofficeDevicePortSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 31);

/**
 * Describes the message proxymanager.residential.v1.BackofficeDevicePortAccount.
 * Use `create(BackofficeDevicePortAccountSchema)` to create a new message.
 */
export const BackofficeDevicePortAccountSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 32);

/**
 * Describes the message proxymanager.residential.v1.BackofficeDevicePortAccountTelco.
 * Use `create(BackofficeDevicePortAccountTelcoSchema)` to create a new message.
 */
export const BackofficeDevicePortAccountTelcoSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 33);

/**
 * Describes the message proxymanager.residential.v1.BackofficeDeviceProxy.
 * Use `create(BackofficeDeviceProxySchema)` to create a new message.
 */
export const BackofficeDeviceProxySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_backoffice, 34);

/**
 * @generated from service proxymanager.residential.v1.BackofficeResidentialService
 */
export const BackofficeResidentialService = /*@__PURE__*/
  serviceDesc(file_proxymanager_residential_v1_backoffice, 0);

