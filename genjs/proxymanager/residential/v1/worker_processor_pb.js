// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/residential/v1/worker_processor.proto (package proxymanager.residential.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";

/**
 * Describes the file proxymanager/residential/v1/worker_processor.proto.
 */
export const file_proxymanager_residential_v1_worker_processor = /*@__PURE__*/
  fileDesc("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", [file_errmsg_v1_errormsg]);

/**
 * Describes the message proxymanager.residential.v1.WorkerProcessorResidentialServiceHealthCheckRequest.
 * Use `create(WorkerProcessorResidentialServiceHealthCheckRequestSchema)` to create a new message.
 */
export const WorkerProcessorResidentialServiceHealthCheckRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_worker_processor, 0);

/**
 * Describes the message proxymanager.residential.v1.WorkerProcessorResidentialServiceHealthCheckResponse.
 * Use `create(WorkerProcessorResidentialServiceHealthCheckResponseSchema)` to create a new message.
 */
export const WorkerProcessorResidentialServiceHealthCheckResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_worker_processor, 1);

/**
 * Describes the message proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartNodeRequest.
 * Use `create(WorkerProcessorResidentialServiceRestartNodeRequestSchema)` to create a new message.
 */
export const WorkerProcessorResidentialServiceRestartNodeRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_worker_processor, 2);

/**
 * Describes the message proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartNodeResponse.
 * Use `create(WorkerProcessorResidentialServiceRestartNodeResponseSchema)` to create a new message.
 */
export const WorkerProcessorResidentialServiceRestartNodeResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_worker_processor, 3);

/**
 * Describes the message proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartPortRequest.
 * Use `create(WorkerProcessorResidentialServiceRestartPortRequestSchema)` to create a new message.
 */
export const WorkerProcessorResidentialServiceRestartPortRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_worker_processor, 4);

/**
 * Describes the message proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartPortResponse.
 * Use `create(WorkerProcessorResidentialServiceRestartPortResponseSchema)` to create a new message.
 */
export const WorkerProcessorResidentialServiceRestartPortResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_worker_processor, 5);

/**
 * Describes the message proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartDeviceRequest.
 * Use `create(WorkerProcessorResidentialServiceRestartDeviceRequestSchema)` to create a new message.
 */
export const WorkerProcessorResidentialServiceRestartDeviceRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_worker_processor, 6);

/**
 * Describes the message proxymanager.residential.v1.WorkerProcessorResidentialServiceRestartDeviceResponse.
 * Use `create(WorkerProcessorResidentialServiceRestartDeviceResponseSchema)` to create a new message.
 */
export const WorkerProcessorResidentialServiceRestartDeviceResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_worker_processor, 7);

/**
 * @generated from service proxymanager.residential.v1.WorkerProcessorResidentialService
 */
export const WorkerProcessorResidentialService = /*@__PURE__*/
  serviceDesc(file_proxymanager_residential_v1_worker_processor, 0);

