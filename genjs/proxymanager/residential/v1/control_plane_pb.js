// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/residential/v1/control_plane.proto (package proxymanager.residential.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_algoenum_v1_res_node } from "../../../algoenum/v1/res_node_pb";

/**
 * Describes the file proxymanager/residential/v1/control_plane.proto.
 */
export const file_proxymanager_residential_v1_control_plane = /*@__PURE__*/
  fileDesc("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", [file_errmsg_v1_errormsg, file_utils_v1_utils, file_algoenum_v1_res_node]);

/**
 * Describes the message proxymanager.residential.v1.ControlPlaneResidentialServiceFetchPortConfigRequest.
 * Use `create(ControlPlaneResidentialServiceFetchPortConfigRequestSchema)` to create a new message.
 */
export const ControlPlaneResidentialServiceFetchPortConfigRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_control_plane, 0);

/**
 * Describes the message proxymanager.residential.v1.ControlPlaneResidentialServiceFetchPortConfigResponse.
 * Use `create(ControlPlaneResidentialServiceFetchPortConfigResponseSchema)` to create a new message.
 */
export const ControlPlaneResidentialServiceFetchPortConfigResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_control_plane, 1);

/**
 * Describes the message proxymanager.residential.v1.ControlPlaneResidentialServiceManagerPortConfig.
 * Use `create(ControlPlaneResidentialServiceManagerPortConfigSchema)` to create a new message.
 */
export const ControlPlaneResidentialServiceManagerPortConfigSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_control_plane, 2);

/**
 * Describes the message proxymanager.residential.v1.ManagerDevice.
 * Use `create(ManagerDeviceSchema)` to create a new message.
 */
export const ManagerDeviceSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_control_plane, 3);

/**
 * Describes the message proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateNodeStatusRequest.
 * Use `create(ControlPlaneResidentialServiceUpdateNodeStatusRequestSchema)` to create a new message.
 */
export const ControlPlaneResidentialServiceUpdateNodeStatusRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_control_plane, 4);

/**
 * Describes the message proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateNodeStatusResponse.
 * Use `create(ControlPlaneResidentialServiceUpdateNodeStatusResponseSchema)` to create a new message.
 */
export const ControlPlaneResidentialServiceUpdateNodeStatusResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_control_plane, 5);

/**
 * Describes the message proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateDeviceStatusRequest.
 * Use `create(ControlPlaneResidentialServiceUpdateDeviceStatusRequestSchema)` to create a new message.
 */
export const ControlPlaneResidentialServiceUpdateDeviceStatusRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_control_plane, 6);

/**
 * Describes the message proxymanager.residential.v1.ControlPlaneResidentialServiceUpdateDeviceStatusResponse.
 * Use `create(ControlPlaneResidentialServiceUpdateDeviceStatusResponseSchema)` to create a new message.
 */
export const ControlPlaneResidentialServiceUpdateDeviceStatusResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_control_plane, 7);

/**
 * @generated from service proxymanager.residential.v1.ControlPlaneResidentialService
 */
export const ControlPlaneResidentialService = /*@__PURE__*/
  serviceDesc(file_proxymanager_residential_v1_control_plane, 0);

