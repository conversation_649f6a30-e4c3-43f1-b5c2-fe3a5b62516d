// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/residential/v1/worker.proto (package proxymanager.residential.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_hop_v1_hop } from "../../../hop/v1/hop_pb";

/**
 * Describes the file proxymanager/residential/v1/worker.proto.
 */
export const file_proxymanager_residential_v1_worker = /*@__PURE__*/
  fileDesc("Cihwcm94eW1hbmFnZXIvcmVzaWRlbnRpYWwvdjEvd29ya2VyLnByb3RvEhtwcm94eW1hbmFnZXIucmVzaWRlbnRpYWwudjEiLAoqV29ya2VyUmVzaWRlbnRpYWxTZXJ2aWNlSGVhbHRoQ2hlY2tSZXF1ZXN0IlUKK1dvcmtlclJlc2lkZW50aWFsU2VydmljZUhlYWx0aENoZWNrUmVzcG9uc2USJgoFZXJyb3IYASABKAsyFy5lcnJtc2cudjEuRXJyb3JNZXNzYWdlIoIBCipXb3JrZXJSZXNpZGVudGlhbFNlcnZpY2VDb25maWdSb3V0ZVJlcXVlc3QSFQoNaWRfcmVzX2RldmljZRgBIAEoCRITCgtkZXZpY2VfbmFtZRgCIAEoCRIOCgZpc19hZGQYAyABKAgSGAoDaG9wGAQgASgLMgsuaG9wLnYxLkhvcCJVCitXb3JrZXJSZXNpZGVudGlhbFNlcnZpY2VDb25maWdSb3V0ZVJlc3BvbnNlEiYKBWVycm9yGAIgASgLMhcuZXJybXNnLnYxLkVycm9yTWVzc2FnZTLgAgoYV29ya2VyUmVzaWRlbnRpYWxTZXJ2aWNlEqABCgtIZWFsdGhDaGVjaxJHLnByb3h5bWFuYWdlci5yZXNpZGVudGlhbC52MS5Xb3JrZXJSZXNpZGVudGlhbFNlcnZpY2VIZWFsdGhDaGVja1JlcXVlc3QaSC5wcm94eW1hbmFnZXIucmVzaWRlbnRpYWwudjEuV29ya2VyUmVzaWRlbnRpYWxTZXJ2aWNlSGVhbHRoQ2hlY2tSZXNwb25zZRKgAQoLQ29uZmlnUm91dGUSRy5wcm94eW1hbmFnZXIucmVzaWRlbnRpYWwudjEuV29ya2VyUmVzaWRlbnRpYWxTZXJ2aWNlQ29uZmlnUm91dGVSZXF1ZXN0GkgucHJveHltYW5hZ2VyLnJlc2lkZW50aWFsLnYxLldvcmtlclJlc2lkZW50aWFsU2VydmljZUNvbmZpZ1JvdXRlUmVzcG9uc2VCWlpYZ2l0LnRtcHJveHktaW5mcmEuY29tL2FsZ28vYWxnb3Byb3h5LXByb3RvL2dlbi9wcm94eW1hbmFnZXIvcmVzaWRlbnRpYWwvdjE7cmVzaWRlbnRpYWx2MWIGcHJvdG8z", [file_errmsg_v1_errormsg, file_hop_v1_hop]);

/**
 * Describes the message proxymanager.residential.v1.WorkerResidentialServiceHealthCheckRequest.
 * Use `create(WorkerResidentialServiceHealthCheckRequestSchema)` to create a new message.
 */
export const WorkerResidentialServiceHealthCheckRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_worker, 0);

/**
 * Describes the message proxymanager.residential.v1.WorkerResidentialServiceHealthCheckResponse.
 * Use `create(WorkerResidentialServiceHealthCheckResponseSchema)` to create a new message.
 */
export const WorkerResidentialServiceHealthCheckResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_worker, 1);

/**
 * Describes the message proxymanager.residential.v1.WorkerResidentialServiceConfigRouteRequest.
 * Use `create(WorkerResidentialServiceConfigRouteRequestSchema)` to create a new message.
 */
export const WorkerResidentialServiceConfigRouteRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_worker, 2);

/**
 * Describes the message proxymanager.residential.v1.WorkerResidentialServiceConfigRouteResponse.
 * Use `create(WorkerResidentialServiceConfigRouteResponseSchema)` to create a new message.
 */
export const WorkerResidentialServiceConfigRouteResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_residential_v1_worker, 3);

/**
 * @generated from service proxymanager.residential.v1.WorkerResidentialService
 */
export const WorkerResidentialService = /*@__PURE__*/
  serviceDesc(file_proxymanager_residential_v1_worker, 0);

