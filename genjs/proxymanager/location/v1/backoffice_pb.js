// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/location/v1/backoffice.proto (package proxymanager.location.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_algoenum_v1_location_level } from "../../../algoenum/v1/location_level_pb";

/**
 * Describes the file proxymanager/location/v1/backoffice.proto.
 */
export const file_proxymanager_location_v1_backoffice = /*@__PURE__*/
  fileDesc("Cilwcm94eW1hbmFnZXIvbG9jYXRpb24vdjEvYmFja29mZmljZS5wcm90bxIYcHJveHltYW5hZ2VyLmxvY2F0aW9uLnYxIi0KK0JhY2tvZmZpY2VMb2NhdGlvblNlcnZpY2VSZWxvYWRDYWNoZVJlcXVlc3QiVgosQmFja29mZmljZUxvY2F0aW9uU2VydmljZVJlbG9hZENhY2hlUmVzcG9uc2USJgoFZXJyb3IYASABKAsyFy5lcnJtc2cudjEuRXJyb3JNZXNzYWdlIokCCi1CYWNrb2ZmaWNlTG9jYXRpb25TZXJ2aWNlRmV0Y2hMb2NhdGlvblJlcXVlc3QSEwoLaWRfbG9jYXRpb24YASABKAkSFAoMaWRfbG9jYXRpb25zGAIgAygJEhoKEmlkX3BhcmVudF9sb2NhdGlvbhgDIAEoCRIyCg5sb2NhdGlvbl9sZXZlbBgEIAEoDjIaLmFsZ29lbnVtLnYxLkxvY2F0aW9uTGV2ZWwSEwoLbmFtZV9zZWFyY2gYBSABKAkSFwoPaXNvX2NvZGVfc2VhcmNoGAYgASgJEi8KCnBhZ2luYXRpb24YByABKAsyGy51dGlscy52MS5QYWdpbmF0aW9uUmVxdWVzdCLQAQouQmFja29mZmljZUxvY2F0aW9uU2VydmljZUZldGNoTG9jYXRpb25SZXNwb25zZRImCgVlcnJvchgBIAEoCzIXLmVycm1zZy52MS5FcnJvck1lc3NhZ2USMAoKcGFnaW5hdGlvbhgCIAEoCzIcLnV0aWxzLnYxLlBhZ2luYXRpb25SZXNwb25zZRJECglsb2NhdGlvbnMYAyADKAsyMS5wcm94eW1hbmFnZXIubG9jYXRpb24udjEuQmFja29mZmljZUxvY2F0aW9uTW9kZWwirwEKLkJhY2tvZmZpY2VMb2NhdGlvblNlcnZpY2VDcmVhdGVMb2NhdGlvblJlcXVlc3QSGgoSaWRfcGFyZW50X2xvY2F0aW9uGAEgASgJEjIKDmxvY2F0aW9uX2xldmVsGAIgASgOMhouYWxnb2VudW0udjEuTG9jYXRpb25MZXZlbBIMCgRuYW1lGAMgASgJEhAKCGlzb19jb2RlGAQgASgJEg0KBWVtb2ppGAUgASgJIlkKL0JhY2tvZmZpY2VMb2NhdGlvblNlcnZpY2VDcmVhdGVMb2NhdGlvblJlc3BvbnNlEiYKBWVycm9yGAEgASgLMhcuZXJybXNnLnYxLkVycm9yTWVzc2FnZSKIAQouQmFja29mZmljZUxvY2F0aW9uU2VydmljZVVwZGF0ZUxvY2F0aW9uUmVxdWVzdBITCgtpZF9sb2NhdGlvbhgBIAEoCRIyCg5sb2NhdGlvbl9sZXZlbBgCIAEoDjIaLmFsZ29lbnVtLnYxLkxvY2F0aW9uTGV2ZWwSDQoFZW1vamkYAyABKAkiWQovQmFja29mZmljZUxvY2F0aW9uU2VydmljZVVwZGF0ZUxvY2F0aW9uUmVzcG9uc2USJgoFZXJyb3IYASABKAsyFy5lcnJtc2cudjEuRXJyb3JNZXNzYWdlItQBChdCYWNrb2ZmaWNlTG9jYXRpb25Nb2RlbBITCgtpZF9sb2NhdGlvbhgBIAEoCRJBCgZwYXJlbnQYAiABKAsyMS5wcm94eW1hbmFnZXIubG9jYXRpb24udjEuQmFja29mZmljZUxvY2F0aW9uTW9kZWwSMgoObG9jYXRpb25fbGV2ZWwYAyABKA4yGi5hbGdvZW51bS52MS5Mb2NhdGlvbkxldmVsEgwKBG5hbWUYBCABKAkSEAoIaXNvX2NvZGUYBSABKAkSDQoFZW1vamkYBiABKAkyrwUKGUJhY2tvZmZpY2VMb2NhdGlvblNlcnZpY2USnAEKC1JlbG9hZENhY2hlEkUucHJveHltYW5hZ2VyLmxvY2F0aW9uLnYxLkJhY2tvZmZpY2VMb2NhdGlvblNlcnZpY2VSZWxvYWRDYWNoZVJlcXVlc3QaRi5wcm94eW1hbmFnZXIubG9jYXRpb24udjEuQmFja29mZmljZUxvY2F0aW9uU2VydmljZVJlbG9hZENhY2hlUmVzcG9uc2USogEKDUZldGNoTG9jYXRpb24SRy5wcm94eW1hbmFnZXIubG9jYXRpb24udjEuQmFja29mZmljZUxvY2F0aW9uU2VydmljZUZldGNoTG9jYXRpb25SZXF1ZXN0GkgucHJveHltYW5hZ2VyLmxvY2F0aW9uLnYxLkJhY2tvZmZpY2VMb2NhdGlvblNlcnZpY2VGZXRjaExvY2F0aW9uUmVzcG9uc2USpQEKDkNyZWF0ZUxvY2F0aW9uEkgucHJveHltYW5hZ2VyLmxvY2F0aW9uLnYxLkJhY2tvZmZpY2VMb2NhdGlvblNlcnZpY2VDcmVhdGVMb2NhdGlvblJlcXVlc3QaSS5wcm94eW1hbmFnZXIubG9jYXRpb24udjEuQmFja29mZmljZUxvY2F0aW9uU2VydmljZUNyZWF0ZUxvY2F0aW9uUmVzcG9uc2USpQEKDlVwZGF0ZUxvY2F0aW9uEkgucHJveHltYW5hZ2VyLmxvY2F0aW9uLnYxLkJhY2tvZmZpY2VMb2NhdGlvblNlcnZpY2VVcGRhdGVMb2NhdGlvblJlcXVlc3QaSS5wcm94eW1hbmFnZXIubG9jYXRpb24udjEuQmFja29mZmljZUxvY2F0aW9uU2VydmljZVVwZGF0ZUxvY2F0aW9uUmVzcG9uc2VCVFpSZ2l0LnRtcHJveHktaW5mcmEuY29tL2FsZ28vYWxnb3Byb3h5LXByb3RvL2dlbi9wcm94eW1hbmFnZXIvbG9jYXRpb24vdjE7bG9jYXRpb252MWIGcHJvdG8z", [file_utils_v1_utils, file_errmsg_v1_errormsg, file_algoenum_v1_location_level]);

/**
 * Describes the message proxymanager.location.v1.BackofficeLocationServiceReloadCacheRequest.
 * Use `create(BackofficeLocationServiceReloadCacheRequestSchema)` to create a new message.
 */
export const BackofficeLocationServiceReloadCacheRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_location_v1_backoffice, 0);

/**
 * Describes the message proxymanager.location.v1.BackofficeLocationServiceReloadCacheResponse.
 * Use `create(BackofficeLocationServiceReloadCacheResponseSchema)` to create a new message.
 */
export const BackofficeLocationServiceReloadCacheResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_location_v1_backoffice, 1);

/**
 * Describes the message proxymanager.location.v1.BackofficeLocationServiceFetchLocationRequest.
 * Use `create(BackofficeLocationServiceFetchLocationRequestSchema)` to create a new message.
 */
export const BackofficeLocationServiceFetchLocationRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_location_v1_backoffice, 2);

/**
 * Describes the message proxymanager.location.v1.BackofficeLocationServiceFetchLocationResponse.
 * Use `create(BackofficeLocationServiceFetchLocationResponseSchema)` to create a new message.
 */
export const BackofficeLocationServiceFetchLocationResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_location_v1_backoffice, 3);

/**
 * Describes the message proxymanager.location.v1.BackofficeLocationServiceCreateLocationRequest.
 * Use `create(BackofficeLocationServiceCreateLocationRequestSchema)` to create a new message.
 */
export const BackofficeLocationServiceCreateLocationRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_location_v1_backoffice, 4);

/**
 * Describes the message proxymanager.location.v1.BackofficeLocationServiceCreateLocationResponse.
 * Use `create(BackofficeLocationServiceCreateLocationResponseSchema)` to create a new message.
 */
export const BackofficeLocationServiceCreateLocationResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_location_v1_backoffice, 5);

/**
 * Describes the message proxymanager.location.v1.BackofficeLocationServiceUpdateLocationRequest.
 * Use `create(BackofficeLocationServiceUpdateLocationRequestSchema)` to create a new message.
 */
export const BackofficeLocationServiceUpdateLocationRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_location_v1_backoffice, 6);

/**
 * Describes the message proxymanager.location.v1.BackofficeLocationServiceUpdateLocationResponse.
 * Use `create(BackofficeLocationServiceUpdateLocationResponseSchema)` to create a new message.
 */
export const BackofficeLocationServiceUpdateLocationResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_location_v1_backoffice, 7);

/**
 * Describes the message proxymanager.location.v1.BackofficeLocationModel.
 * Use `create(BackofficeLocationModelSchema)` to create a new message.
 */
export const BackofficeLocationModelSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_location_v1_backoffice, 8);

/**
 * @generated from service proxymanager.location.v1.BackofficeLocationService
 */
export const BackofficeLocationService = /*@__PURE__*/
  serviceDesc(file_proxymanager_location_v1_backoffice, 0);

