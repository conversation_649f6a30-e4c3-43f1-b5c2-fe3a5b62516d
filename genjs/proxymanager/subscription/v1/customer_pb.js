// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/subscription/v1/customer.proto (package proxymanager.subscription.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_algoenum_v1_location_level } from "../../../algoenum/v1/location_level_pb";

/**
 * Describes the file proxymanager/subscription/v1/customer.proto.
 */
export const file_proxymanager_subscription_v1_customer = /*@__PURE__*/
  fileDesc("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", [file_errmsg_v1_errormsg, file_utils_v1_utils, file_algoenum_v1_location_level]);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionServiceUpdateSubscriptionRequest.
 * Use `create(CustomerSubscriptionServiceUpdateSubscriptionRequestSchema)` to create a new message.
 */
export const CustomerSubscriptionServiceUpdateSubscriptionRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 0);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionServiceUpdateSubscriptionResponse.
 * Use `create(CustomerSubscriptionServiceUpdateSubscriptionResponseSchema)` to create a new message.
 */
export const CustomerSubscriptionServiceUpdateSubscriptionResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 1);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionServiceRevokeProxyTokenRequest.
 * Use `create(CustomerSubscriptionServiceRevokeProxyTokenRequestSchema)` to create a new message.
 */
export const CustomerSubscriptionServiceRevokeProxyTokenRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 2);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionServiceRevokeProxyTokenResponse.
 * Use `create(CustomerSubscriptionServiceRevokeProxyTokenResponseSchema)` to create a new message.
 */
export const CustomerSubscriptionServiceRevokeProxyTokenResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 3);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest.
 * Use `create(CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequestSchema)` to create a new message.
 */
export const CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 4);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse.
 * Use `create(CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponseSchema)` to create a new message.
 */
export const CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 5);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionServiceFetchUserPlanRequest.
 * Use `create(CustomerSubscriptionServiceFetchUserPlanRequestSchema)` to create a new message.
 */
export const CustomerSubscriptionServiceFetchUserPlanRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 6);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionServiceFetchUserPlanResponse.
 * Use `create(CustomerSubscriptionServiceFetchUserPlanResponseSchema)` to create a new message.
 */
export const CustomerSubscriptionServiceFetchUserPlanResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 7);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionServiceFetchUserPlanPlan.
 * Use `create(CustomerSubscriptionServiceFetchUserPlanPlanSchema)` to create a new message.
 */
export const CustomerSubscriptionServiceFetchUserPlanPlanSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 8);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionServiceDeleteSubscriptionRequest.
 * Use `create(CustomerSubscriptionServiceDeleteSubscriptionRequestSchema)` to create a new message.
 */
export const CustomerSubscriptionServiceDeleteSubscriptionRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 9);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionServiceDeleteSubscriptionResponse.
 * Use `create(CustomerSubscriptionServiceDeleteSubscriptionResponseSchema)` to create a new message.
 */
export const CustomerSubscriptionServiceDeleteSubscriptionResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 10);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionServiceFetchProxyTokenRequest.
 * Use `create(CustomerSubscriptionServiceFetchProxyTokenRequestSchema)` to create a new message.
 */
export const CustomerSubscriptionServiceFetchProxyTokenRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 11);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionServiceFetchProxyTokenResponse.
 * Use `create(CustomerSubscriptionServiceFetchProxyTokenResponseSchema)` to create a new message.
 */
export const CustomerSubscriptionServiceFetchProxyTokenResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 12);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionProxyTokenEntity.
 * Use `create(CustomerSubscriptionProxyTokenEntitySchema)` to create a new message.
 */
export const CustomerSubscriptionProxyTokenEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 13);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionProxyTokenEntityBackConnect.
 * Use `create(CustomerSubscriptionProxyTokenEntityBackConnectSchema)` to create a new message.
 */
export const CustomerSubscriptionProxyTokenEntityBackConnectSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 14);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionProxyTokenEntityProxy.
 * Use `create(CustomerSubscriptionProxyTokenEntityProxySchema)` to create a new message.
 */
export const CustomerSubscriptionProxyTokenEntityProxySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 15);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionLocation.
 * Use `create(CustomerSubscriptionLocationSchema)` to create a new message.
 */
export const CustomerSubscriptionLocationSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 16);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionServiceFetchSubscriptionRequest.
 * Use `create(CustomerSubscriptionServiceFetchSubscriptionRequestSchema)` to create a new message.
 */
export const CustomerSubscriptionServiceFetchSubscriptionRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 17);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionServiceFetchSubscriptionResponse.
 * Use `create(CustomerSubscriptionServiceFetchSubscriptionResponseSchema)` to create a new message.
 */
export const CustomerSubscriptionServiceFetchSubscriptionResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 18);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionEntity.
 * Use `create(CustomerSubscriptionEntitySchema)` to create a new message.
 */
export const CustomerSubscriptionEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 19);

/**
 * Describes the message proxymanager.subscription.v1.CustomerSubscriptionPlanEntity.
 * Use `create(CustomerSubscriptionPlanEntitySchema)` to create a new message.
 */
export const CustomerSubscriptionPlanEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_customer, 20);

/**
 * @generated from service proxymanager.subscription.v1.CustomerSubscriptionService
 */
export const CustomerSubscriptionService = /*@__PURE__*/
  serviceDesc(file_proxymanager_subscription_v1_customer, 0);

