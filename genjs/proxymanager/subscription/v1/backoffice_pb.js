// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/subscription/v1/backoffice.proto (package proxymanager.subscription.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";

/**
 * Describes the file proxymanager/subscription/v1/backoffice.proto.
 */
export const file_proxymanager_subscription_v1_backoffice = /*@__PURE__*/
  fileDesc("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", [file_utils_v1_utils, file_errmsg_v1_errormsg]);

/**
 * Describes the message proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchProxyTokenRequest.
 * Use `create(BackofficeSubscriptionServiceFetchProxyTokenRequestSchema)` to create a new message.
 */
export const BackofficeSubscriptionServiceFetchProxyTokenRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_backoffice, 0);

/**
 * Describes the message proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchProxyTokenResponse.
 * Use `create(BackofficeSubscriptionServiceFetchProxyTokenResponseSchema)` to create a new message.
 */
export const BackofficeSubscriptionServiceFetchProxyTokenResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_backoffice, 1);

/**
 * Describes the message proxymanager.subscription.v1.BackofficeSubscriptionProxyTokenEntity.
 * Use `create(BackofficeSubscriptionProxyTokenEntitySchema)` to create a new message.
 */
export const BackofficeSubscriptionProxyTokenEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_backoffice, 2);

/**
 * Describes the message proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchSubscriptionRequest.
 * Use `create(BackofficeSubscriptionServiceFetchSubscriptionRequestSchema)` to create a new message.
 */
export const BackofficeSubscriptionServiceFetchSubscriptionRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_backoffice, 3);

/**
 * Describes the message proxymanager.subscription.v1.BackofficeSubscriptionServiceFetchSubscriptionResponse.
 * Use `create(BackofficeSubscriptionServiceFetchSubscriptionResponseSchema)` to create a new message.
 */
export const BackofficeSubscriptionServiceFetchSubscriptionResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_backoffice, 4);

/**
 * Describes the message proxymanager.subscription.v1.BackofficeSubscriptionEntity.
 * Use `create(BackofficeSubscriptionEntitySchema)` to create a new message.
 */
export const BackofficeSubscriptionEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_backoffice, 5);

/**
 * Describes the message proxymanager.subscription.v1.BackofficeSubscriptionPlanEntity.
 * Use `create(BackofficeSubscriptionPlanEntitySchema)` to create a new message.
 */
export const BackofficeSubscriptionPlanEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_backoffice, 6);

/**
 * @generated from service proxymanager.subscription.v1.BackofficeSubscriptionService
 */
export const BackofficeSubscriptionService = /*@__PURE__*/
  serviceDesc(file_proxymanager_subscription_v1_backoffice, 0);

