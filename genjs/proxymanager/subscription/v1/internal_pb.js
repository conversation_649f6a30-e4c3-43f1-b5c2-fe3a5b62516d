// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/subscription/v1/internal.proto (package proxymanager.subscription.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";

/**
 * Describes the file proxymanager/subscription/v1/internal.proto.
 */
export const file_proxymanager_subscription_v1_internal = /*@__PURE__*/
  fileDesc("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", [file_errmsg_v1_errormsg]);

/**
 * Describes the message proxymanager.subscription.v1.InternalSubscriptionServiceCreateSubscriptionRequest.
 * Use `create(InternalSubscriptionServiceCreateSubscriptionRequestSchema)` to create a new message.
 */
export const InternalSubscriptionServiceCreateSubscriptionRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_internal, 0);

/**
 * Describes the message proxymanager.subscription.v1.InternalSubscriptionServiceCreateSubscriptionResponse.
 * Use `create(InternalSubscriptionServiceCreateSubscriptionResponseSchema)` to create a new message.
 */
export const InternalSubscriptionServiceCreateSubscriptionResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_internal, 1);

/**
 * Describes the message proxymanager.subscription.v1.InternalSubscriptionServiceExtendSubscriptionRequest.
 * Use `create(InternalSubscriptionServiceExtendSubscriptionRequestSchema)` to create a new message.
 */
export const InternalSubscriptionServiceExtendSubscriptionRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_internal, 2);

/**
 * Describes the message proxymanager.subscription.v1.InternalSubscriptionServiceExtendSubscriptionResponse.
 * Use `create(InternalSubscriptionServiceExtendSubscriptionResponseSchema)` to create a new message.
 */
export const InternalSubscriptionServiceExtendSubscriptionResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_internal, 3);

/**
 * Describes the message proxymanager.subscription.v1.InternalSubscriptionServiceFetchSubscriptionRequest.
 * Use `create(InternalSubscriptionServiceFetchSubscriptionRequestSchema)` to create a new message.
 */
export const InternalSubscriptionServiceFetchSubscriptionRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_internal, 4);

/**
 * Describes the message proxymanager.subscription.v1.InternalSubscriptionServiceFetchSubscriptionResponse.
 * Use `create(InternalSubscriptionServiceFetchSubscriptionResponseSchema)` to create a new message.
 */
export const InternalSubscriptionServiceFetchSubscriptionResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_internal, 5);

/**
 * Describes the message proxymanager.subscription.v1.InternalSubscriptionModel.
 * Use `create(InternalSubscriptionModelSchema)` to create a new message.
 */
export const InternalSubscriptionModelSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_internal, 6);

/**
 * @generated from service proxymanager.subscription.v1.InternalSubscriptionService
 */
export const InternalSubscriptionService = /*@__PURE__*/
  serviceDesc(file_proxymanager_subscription_v1_internal, 0);

