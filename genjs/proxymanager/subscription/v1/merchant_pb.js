// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/subscription/v1/merchant.proto (package proxymanager.subscription.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";

/**
 * Describes the file proxymanager/subscription/v1/merchant.proto.
 */
export const file_proxymanager_subscription_v1_merchant = /*@__PURE__*/
  fileDesc("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", [file_utils_v1_utils, file_errmsg_v1_errormsg]);

/**
 * Describes the message proxymanager.subscription.v1.MerchantSubscriptionServiceFetchProxyTokenRequest.
 * Use `create(MerchantSubscriptionServiceFetchProxyTokenRequestSchema)` to create a new message.
 */
export const MerchantSubscriptionServiceFetchProxyTokenRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_merchant, 0);

/**
 * Describes the message proxymanager.subscription.v1.MerchantSubscriptionServiceFetchProxyTokenResponse.
 * Use `create(MerchantSubscriptionServiceFetchProxyTokenResponseSchema)` to create a new message.
 */
export const MerchantSubscriptionServiceFetchProxyTokenResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_merchant, 1);

/**
 * Describes the message proxymanager.subscription.v1.MerchantSubscriptionProxyTokenEntity.
 * Use `create(MerchantSubscriptionProxyTokenEntitySchema)` to create a new message.
 */
export const MerchantSubscriptionProxyTokenEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_merchant, 2);

/**
 * Describes the message proxymanager.subscription.v1.MerchantSubscriptionServiceFetchSubscriptionRequest.
 * Use `create(MerchantSubscriptionServiceFetchSubscriptionRequestSchema)` to create a new message.
 */
export const MerchantSubscriptionServiceFetchSubscriptionRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_merchant, 3);

/**
 * Describes the message proxymanager.subscription.v1.MerchantSubscriptionServiceFetchSubscriptionResponse.
 * Use `create(MerchantSubscriptionServiceFetchSubscriptionResponseSchema)` to create a new message.
 */
export const MerchantSubscriptionServiceFetchSubscriptionResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_merchant, 4);

/**
 * Describes the message proxymanager.subscription.v1.MerchantSubscriptionEntity.
 * Use `create(MerchantSubscriptionEntitySchema)` to create a new message.
 */
export const MerchantSubscriptionEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_merchant, 5);

/**
 * Describes the message proxymanager.subscription.v1.MerchantSubscriptionPlanEntity.
 * Use `create(MerchantSubscriptionPlanEntitySchema)` to create a new message.
 */
export const MerchantSubscriptionPlanEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_subscription_v1_merchant, 6);

/**
 * @generated from service proxymanager.subscription.v1.MerchantSubscriptionService
 */
export const MerchantSubscriptionService = /*@__PURE__*/
  serviceDesc(file_proxymanager_subscription_v1_merchant, 0);

