// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/plan/v1/internal.proto (package proxymanager.plan.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_algoenum_v1_data_transfer_type } from "../../../algoenum/v1/data_transfer_type_pb";

/**
 * Describes the file proxymanager/plan/v1/internal.proto.
 */
export const file_proxymanager_plan_v1_internal = /*@__PURE__*/
  fileDesc("CiNwcm94eW1hbmFnZXIvcGxhbi92MS9pbnRlcm5hbC5wcm90bxIUcHJveHltYW5hZ2VyLnBsYW4udjEiSwojSW50ZXJuYWxQbGFuU2VydmljZUZldGNoUGxhblJlcXVlc3QSEwoLaWRfbWVyY2hhbnQYASABKAkSDwoHaWRfcGxhbhgCIAMoCSKRAQokSW50ZXJuYWxQbGFuU2VydmljZUZldGNoUGxhblJlc3BvbnNlEkEKBHBsYW4YASADKAsyMy5wcm94eW1hbmFnZXIucGxhbi52MS5JbnRlcm5hbFBsYW5TZXJ2aWNlUGxhbkVudGl0eRImCgVlcnJvchgCIAEoCzIXLmVycm1zZy52MS5FcnJvck1lc3NhZ2UihwIKHUludGVybmFsUGxhblNlcnZpY2VQbGFuRW50aXR5Eg8KB2lkX3BsYW4YASABKAkSEwoLaWRfbWVyY2hhbnQYAiABKAkSDAoEbmFtZRgDIAEoCRI5ChJkYXRhX3RyYW5zZmVyX3R5cGUYBCABKA4yHS5hbGdvZW51bS52MS5EYXRhVHJhbnNmZXJUeXBlEiEKGXRpbWVfdG9fbGl2ZV9wcm94eV9pbl9zZWMYBSABKAMSJwofdGltZV90b19jaGFuZ2VfcGVyX3Byb3h5X2luX3NlYxgGIAEoAxIYChBjb25jdXJyZW50X3Byb3h5GAcgASgDEhEKCWlzX2FjdGl2ZRgIIAEoCDKaAQoTSW50ZXJuYWxQbGFuU2VydmljZRKCAQoJRmV0Y2hQbGFuEjkucHJveHltYW5hZ2VyLnBsYW4udjEuSW50ZXJuYWxQbGFuU2VydmljZUZldGNoUGxhblJlcXVlc3QaOi5wcm94eW1hbmFnZXIucGxhbi52MS5JbnRlcm5hbFBsYW5TZXJ2aWNlRmV0Y2hQbGFuUmVzcG9uc2VCTFpKZ2l0LnRtcHJveHktaW5mcmEuY29tL2FsZ28vYWxnb3Byb3h5LXByb3RvL2dlbi9wcm94eW1hbmFnZXIvcGxhbi92MTtwbGFudjFiBnByb3RvMw", [file_errmsg_v1_errormsg, file_algoenum_v1_data_transfer_type]);

/**
 * Describes the message proxymanager.plan.v1.InternalPlanServiceFetchPlanRequest.
 * Use `create(InternalPlanServiceFetchPlanRequestSchema)` to create a new message.
 */
export const InternalPlanServiceFetchPlanRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_internal, 0);

/**
 * Describes the message proxymanager.plan.v1.InternalPlanServiceFetchPlanResponse.
 * Use `create(InternalPlanServiceFetchPlanResponseSchema)` to create a new message.
 */
export const InternalPlanServiceFetchPlanResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_internal, 1);

/**
 * Describes the message proxymanager.plan.v1.InternalPlanServicePlanEntity.
 * Use `create(InternalPlanServicePlanEntitySchema)` to create a new message.
 */
export const InternalPlanServicePlanEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_internal, 2);

/**
 * @generated from service proxymanager.plan.v1.InternalPlanService
 */
export const InternalPlanService = /*@__PURE__*/
  serviceDesc(file_proxymanager_plan_v1_internal, 0);

