// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/plan/v1/merchant.proto (package proxymanager.plan.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_algoenum_v1_ip_type } from "../../../algoenum/v1/ip_type_pb";
import { file_algoenum_v1_proxy_type } from "../../../algoenum/v1/proxy_type_pb";
import { file_algoenum_v1_change_type } from "../../../algoenum/v1/change_type_pb";
import { file_algoenum_v1_data_transfer_type } from "../../../algoenum/v1/data_transfer_type_pb";
import { file_algoenum_v1_location_level } from "../../../algoenum/v1/location_level_pb";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";

/**
 * Describes the file proxymanager/plan/v1/merchant.proto.
 */
export const file_proxymanager_plan_v1_merchant = /*@__PURE__*/
  fileDesc("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", [file_algoenum_v1_ip_type, file_algoenum_v1_proxy_type, file_algoenum_v1_change_type, file_algoenum_v1_data_transfer_type, file_algoenum_v1_location_level, file_errmsg_v1_errormsg, file_utils_v1_utils]);

/**
 * Describes the message proxymanager.plan.v1.MerchantPlanServiceFetchPlanRequest.
 * Use `create(MerchantPlanServiceFetchPlanRequestSchema)` to create a new message.
 */
export const MerchantPlanServiceFetchPlanRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_merchant, 0);

/**
 * Describes the message proxymanager.plan.v1.MerchantPlanServiceFetchPlanResponse.
 * Use `create(MerchantPlanServiceFetchPlanResponseSchema)` to create a new message.
 */
export const MerchantPlanServiceFetchPlanResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_merchant, 1);

/**
 * Describes the message proxymanager.plan.v1.MerchantPlanServicePlanEntity.
 * Use `create(MerchantPlanServicePlanEntitySchema)` to create a new message.
 */
export const MerchantPlanServicePlanEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_merchant, 2);

/**
 * Describes the message proxymanager.plan.v1.MerchantPlanServicePlanLocation.
 * Use `create(MerchantPlanServicePlanLocationSchema)` to create a new message.
 */
export const MerchantPlanServicePlanLocationSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_merchant, 3);

/**
 * @generated from service proxymanager.plan.v1.MerchantPlanService
 */
export const MerchantPlanService = /*@__PURE__*/
  serviceDesc(file_proxymanager_plan_v1_merchant, 0);

