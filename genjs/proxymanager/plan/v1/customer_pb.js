// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/plan/v1/customer.proto (package proxymanager.plan.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_algoenum_v1_ip_type } from "../../../algoenum/v1/ip_type_pb";
import { file_algoenum_v1_proxy_type } from "../../../algoenum/v1/proxy_type_pb";
import { file_algoenum_v1_change_type } from "../../../algoenum/v1/change_type_pb";
import { file_algoenum_v1_data_transfer_type } from "../../../algoenum/v1/data_transfer_type_pb";
import { file_algoenum_v1_location_level } from "../../../algoenum/v1/location_level_pb";

/**
 * Describes the file proxymanager/plan/v1/customer.proto.
 */
export const file_proxymanager_plan_v1_customer = /*@__PURE__*/
  fileDesc("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", [file_errmsg_v1_errormsg, file_utils_v1_utils, file_algoenum_v1_ip_type, file_algoenum_v1_proxy_type, file_algoenum_v1_change_type, file_algoenum_v1_data_transfer_type, file_algoenum_v1_location_level]);

/**
 * Describes the message proxymanager.plan.v1.CustomerPlanServiceFetchAvailableLocationRequest.
 * Use `create(CustomerPlanServiceFetchAvailableLocationRequestSchema)` to create a new message.
 */
export const CustomerPlanServiceFetchAvailableLocationRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_customer, 0);

/**
 * Describes the message proxymanager.plan.v1.CustomerPlanServiceFetchAvailableLocationResponse.
 * Use `create(CustomerPlanServiceFetchAvailableLocationResponseSchema)` to create a new message.
 */
export const CustomerPlanServiceFetchAvailableLocationResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_customer, 1);

/**
 * Describes the message proxymanager.plan.v1.CustomerPlanServiceFetchPlanRequest.
 * Use `create(CustomerPlanServiceFetchPlanRequestSchema)` to create a new message.
 */
export const CustomerPlanServiceFetchPlanRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_customer, 2);

/**
 * Describes the message proxymanager.plan.v1.CustomerPlanServiceFetchPlanResponse.
 * Use `create(CustomerPlanServiceFetchPlanResponseSchema)` to create a new message.
 */
export const CustomerPlanServiceFetchPlanResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_customer, 3);

/**
 * Describes the message proxymanager.plan.v1.CustomerPlanServicePlanEntity.
 * Use `create(CustomerPlanServicePlanEntitySchema)` to create a new message.
 */
export const CustomerPlanServicePlanEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_customer, 4);

/**
 * Describes the message proxymanager.plan.v1.CustomerPlanServicePlanLocation.
 * Use `create(CustomerPlanServicePlanLocationSchema)` to create a new message.
 */
export const CustomerPlanServicePlanLocationSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_customer, 5);

/**
 * @generated from service proxymanager.plan.v1.CustomerPlanService
 */
export const CustomerPlanService = /*@__PURE__*/
  serviceDesc(file_proxymanager_plan_v1_customer, 0);

