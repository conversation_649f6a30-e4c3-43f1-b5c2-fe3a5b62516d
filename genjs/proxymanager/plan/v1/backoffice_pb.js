// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/plan/v1/backoffice.proto (package proxymanager.plan.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_algoenum_v1_ip_type } from "../../../algoenum/v1/ip_type_pb";
import { file_algoenum_v1_proxy_type } from "../../../algoenum/v1/proxy_type_pb";
import { file_algoenum_v1_change_type } from "../../../algoenum/v1/change_type_pb";
import { file_algoenum_v1_data_transfer_type } from "../../../algoenum/v1/data_transfer_type_pb";
import { file_algoenum_v1_location_level } from "../../../algoenum/v1/location_level_pb";

/**
 * Describes the file proxymanager/plan/v1/backoffice.proto.
 */
export const file_proxymanager_plan_v1_backoffice = /*@__PURE__*/
  fileDesc("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", [file_errmsg_v1_errormsg, file_utils_v1_utils, file_algoenum_v1_ip_type, file_algoenum_v1_proxy_type, file_algoenum_v1_change_type, file_algoenum_v1_data_transfer_type, file_algoenum_v1_location_level]);

/**
 * Describes the message proxymanager.plan.v1.BackofficePlanServiceConfigPlanBackConnectRequest.
 * Use `create(BackofficePlanServiceConfigPlanBackConnectRequestSchema)` to create a new message.
 */
export const BackofficePlanServiceConfigPlanBackConnectRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_backoffice, 0);

/**
 * Describes the message proxymanager.plan.v1.BackofficePlanServiceConfigPlanBackConnectResponse.
 * Use `create(BackofficePlanServiceConfigPlanBackConnectResponseSchema)` to create a new message.
 */
export const BackofficePlanServiceConfigPlanBackConnectResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_backoffice, 1);

/**
 * Describes the message proxymanager.plan.v1.BackofficePlanServiceConfigPlanLocationRequest.
 * Use `create(BackofficePlanServiceConfigPlanLocationRequestSchema)` to create a new message.
 */
export const BackofficePlanServiceConfigPlanLocationRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_backoffice, 2);

/**
 * Describes the message proxymanager.plan.v1.BackofficePlanServiceConfigPlanLocationResponse.
 * Use `create(BackofficePlanServiceConfigPlanLocationResponseSchema)` to create a new message.
 */
export const BackofficePlanServiceConfigPlanLocationResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_backoffice, 3);

/**
 * Describes the message proxymanager.plan.v1.BackofficePlanServiceCreatePlanRequest.
 * Use `create(BackofficePlanServiceCreatePlanRequestSchema)` to create a new message.
 */
export const BackofficePlanServiceCreatePlanRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_backoffice, 4);

/**
 * Describes the message proxymanager.plan.v1.BackofficePlanServiceCreatePlanResponse.
 * Use `create(BackofficePlanServiceCreatePlanResponseSchema)` to create a new message.
 */
export const BackofficePlanServiceCreatePlanResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_backoffice, 5);

/**
 * Describes the message proxymanager.plan.v1.BackofficePlanServiceFetchPlanRequest.
 * Use `create(BackofficePlanServiceFetchPlanRequestSchema)` to create a new message.
 */
export const BackofficePlanServiceFetchPlanRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_backoffice, 6);

/**
 * Describes the message proxymanager.plan.v1.BackofficePlanServiceFetchPlanResponse.
 * Use `create(BackofficePlanServiceFetchPlanResponseSchema)` to create a new message.
 */
export const BackofficePlanServiceFetchPlanResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_backoffice, 7);

/**
 * Describes the message proxymanager.plan.v1.BackofficePlanServiceUpdatePlanRequest.
 * Use `create(BackofficePlanServiceUpdatePlanRequestSchema)` to create a new message.
 */
export const BackofficePlanServiceUpdatePlanRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_backoffice, 8);

/**
 * Describes the message proxymanager.plan.v1.BackofficePlanServiceUpdatePlanResponse.
 * Use `create(BackofficePlanServiceUpdatePlanResponseSchema)` to create a new message.
 */
export const BackofficePlanServiceUpdatePlanResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_backoffice, 9);

/**
 * Describes the message proxymanager.plan.v1.BackofficePlanServicePlanEntity.
 * Use `create(BackofficePlanServicePlanEntitySchema)` to create a new message.
 */
export const BackofficePlanServicePlanEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_backoffice, 10);

/**
 * Describes the message proxymanager.plan.v1.BackofficePlanServicePlanBackConnect.
 * Use `create(BackofficePlanServicePlanBackConnectSchema)` to create a new message.
 */
export const BackofficePlanServicePlanBackConnectSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_backoffice, 11);

/**
 * Describes the message proxymanager.plan.v1.BackofficePlanServicePlanMerchantEntity.
 * Use `create(BackofficePlanServicePlanMerchantEntitySchema)` to create a new message.
 */
export const BackofficePlanServicePlanMerchantEntitySchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_backoffice, 12);

/**
 * Describes the message proxymanager.plan.v1.BackofficePlanServicePlanLocation.
 * Use `create(BackofficePlanServicePlanLocationSchema)` to create a new message.
 */
export const BackofficePlanServicePlanLocationSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_plan_v1_backoffice, 13);

/**
 * @generated from service proxymanager.plan.v1.BackofficePlanService
 */
export const BackofficePlanService = /*@__PURE__*/
  serviceDesc(file_proxymanager_plan_v1_backoffice, 0);

