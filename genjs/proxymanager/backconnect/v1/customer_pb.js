// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/backconnect/v1/customer.proto (package proxymanager.backconnect.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_algoenum_v1_location_level } from "../../../algoenum/v1/location_level_pb";

/**
 * Describes the file proxymanager/backconnect/v1/customer.proto.
 */
export const file_proxymanager_backconnect_v1_customer = /*@__PURE__*/
  fileDesc("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", [file_errmsg_v1_errormsg, file_utils_v1_utils, file_algoenum_v1_location_level]);

/**
 * Describes the message proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest.
 * Use `create(CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequestSchema)` to create a new message.
 */
export const CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_customer, 0);

/**
 * Describes the message proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse.
 * Use `create(CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponseSchema)` to create a new message.
 */
export const CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_customer, 1);

/**
 * Describes the message proxymanager.backconnect.v1.CustomerBackConnectLocation.
 * Use `create(CustomerBackConnectLocationSchema)` to create a new message.
 */
export const CustomerBackConnectLocationSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_customer, 2);

/**
 * Describes the message proxymanager.backconnect.v1.CustomerBackConnectServiceCreateBackConnectManagerRequest.
 * Use `create(CustomerBackConnectServiceCreateBackConnectManagerRequestSchema)` to create a new message.
 */
export const CustomerBackConnectServiceCreateBackConnectManagerRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_customer, 3);

/**
 * Describes the message proxymanager.backconnect.v1.CustomerBackConnectServiceCreateBackConnectManagerResponse.
 * Use `create(CustomerBackConnectServiceCreateBackConnectManagerResponseSchema)` to create a new message.
 */
export const CustomerBackConnectServiceCreateBackConnectManagerResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_customer, 4);

/**
 * Describes the message proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableBackConnectRequest.
 * Use `create(CustomerBackConnectServiceFetchAvailableBackConnectRequestSchema)` to create a new message.
 */
export const CustomerBackConnectServiceFetchAvailableBackConnectRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_customer, 5);

/**
 * Describes the message proxymanager.backconnect.v1.CustomerBackConnectServiceFetchAvailableBackConnectResponse.
 * Use `create(CustomerBackConnectServiceFetchAvailableBackConnectResponseSchema)` to create a new message.
 */
export const CustomerBackConnectServiceFetchAvailableBackConnectResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_customer, 6);

/**
 * Describes the message proxymanager.backconnect.v1.CustomerBackConnectServiceBackConnectManager.
 * Use `create(CustomerBackConnectServiceBackConnectManagerSchema)` to create a new message.
 */
export const CustomerBackConnectServiceBackConnectManagerSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_customer, 7);

/**
 * Describes the message proxymanager.backconnect.v1.CustomerBackConnectServiceBackConnectManagerLocation.
 * Use `create(CustomerBackConnectServiceBackConnectManagerLocationSchema)` to create a new message.
 */
export const CustomerBackConnectServiceBackConnectManagerLocationSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_customer, 8);

/**
 * Describes the message proxymanager.backconnect.v1.CustomerBackConnectServiceBackConnectManagerTelco.
 * Use `create(CustomerBackConnectServiceBackConnectManagerTelcoSchema)` to create a new message.
 */
export const CustomerBackConnectServiceBackConnectManagerTelcoSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_customer, 9);

/**
 * @generated from service proxymanager.backconnect.v1.CustomerBackConnectService
 */
export const CustomerBackConnectService = /*@__PURE__*/
  serviceDesc(file_proxymanager_backconnect_v1_customer, 0);

