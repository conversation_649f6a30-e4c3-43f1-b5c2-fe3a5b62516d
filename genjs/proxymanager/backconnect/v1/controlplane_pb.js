// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/backconnect/v1/controlplane.proto (package proxymanager.backconnect.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_algoenum_v1_proxy_protocol } from "../../../algoenum/v1/proxy_protocol_pb";
import { file_algoenum_v1_back_connect_port_status } from "../../../algoenum/v1/back_connect_port_status_pb";
import { file_algoenum_v1_back_connect_manager_status } from "../../../algoenum/v1/back_connect_manager_status_pb";
import { file_algoenum_v1_packet_type } from "../../../algoenum/v1/packet_type_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";

/**
 * Describes the file proxymanager/backconnect/v1/controlplane.proto.
 */
export const file_proxymanager_backconnect_v1_controlplane = /*@__PURE__*/
  fileDesc("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", [file_errmsg_v1_errormsg, file_algoenum_v1_proxy_protocol, file_algoenum_v1_back_connect_port_status, file_algoenum_v1_back_connect_manager_status, file_algoenum_v1_packet_type, file_utils_v1_utils]);

/**
 * Describes the message proxymanager.backconnect.v1.BackConnectControlPlaneServiceTrackingRequest.
 * Use `create(BackConnectControlPlaneServiceTrackingRequestSchema)` to create a new message.
 */
export const BackConnectControlPlaneServiceTrackingRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_controlplane, 0);

/**
 * Describes the message proxymanager.backconnect.v1.BackConnectControlPlaneServiceTrackingResponse.
 * Use `create(BackConnectControlPlaneServiceTrackingResponseSchema)` to create a new message.
 */
export const BackConnectControlPlaneServiceTrackingResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_controlplane, 1);

/**
 * Describes the message proxymanager.backconnect.v1.BackConnectControlPlaneServiceRegisterManagerRequest.
 * Use `create(BackConnectControlPlaneServiceRegisterManagerRequestSchema)` to create a new message.
 */
export const BackConnectControlPlaneServiceRegisterManagerRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_controlplane, 2);

/**
 * Describes the message proxymanager.backconnect.v1.BackConnectControlPlaneServiceRegisterManagerResponse.
 * Use `create(BackConnectControlPlaneServiceRegisterManagerResponseSchema)` to create a new message.
 */
export const BackConnectControlPlaneServiceRegisterManagerResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_controlplane, 3);

/**
 * Describes the message proxymanager.backconnect.v1.BackConnectControlPlaneServiceFetchPortRequest.
 * Use `create(BackConnectControlPlaneServiceFetchPortRequestSchema)` to create a new message.
 */
export const BackConnectControlPlaneServiceFetchPortRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_controlplane, 4);

/**
 * Describes the message proxymanager.backconnect.v1.BackConnectControlPlaneServiceFetchPortResponse.
 * Use `create(BackConnectControlPlaneServiceFetchPortResponseSchema)` to create a new message.
 */
export const BackConnectControlPlaneServiceFetchPortResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_controlplane, 5);

/**
 * Describes the message proxymanager.backconnect.v1.ControlPlaneBackConnectPort.
 * Use `create(ControlPlaneBackConnectPortSchema)` to create a new message.
 */
export const ControlPlaneBackConnectPortSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_controlplane, 6);

/**
 * @generated from service proxymanager.backconnect.v1.BackConnectControlPlaneService
 */
export const BackConnectControlPlaneService = /*@__PURE__*/
  serviceDesc(file_proxymanager_backconnect_v1_controlplane, 0);

