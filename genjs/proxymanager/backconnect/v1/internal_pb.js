// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/backconnect/v1/internal.proto (package proxymanager.backconnect.v1, syntax proto3)
/* eslint-disable */

import { fileDesc } from "@bufbuild/protobuf/codegenv1";

/**
 * Describes the file proxymanager/backconnect/v1/internal.proto.
 */
export const file_proxymanager_backconnect_v1_internal = /*@__PURE__*/
  fileDesc("Cipwcm94eW1hbmFnZXIvYmFja2Nvbm5lY3QvdjEvaW50ZXJuYWwucHJvdG8SG3Byb3h5bWFuYWdlci5iYWNrY29ubmVjdC52MUJaWlhnaXQudG1wcm94eS1pbmZyYS5jb20vYWxnby9hbGdvcHJveHktcHJvdG8vZ2VuL3Byb3h5bWFuYWdlci9iYWNrY29ubmVjdC92MTtiYWNrY29ubmVjdHYxYgZwcm90bzM");

