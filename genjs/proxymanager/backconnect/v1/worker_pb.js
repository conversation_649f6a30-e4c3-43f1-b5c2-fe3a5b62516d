// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/backconnect/v1/worker.proto (package proxymanager.backconnect.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_hop_v1_hop } from "../../../hop/v1/hop_pb";

/**
 * Describes the file proxymanager/backconnect/v1/worker.proto.
 */
export const file_proxymanager_backconnect_v1_worker = /*@__PURE__*/
  fileDesc("Cihwcm94eW1hbmFnZXIvYmFja2Nvbm5lY3QvdjEvd29ya2VyLnByb3RvEhtwcm94eW1hbmFnZXIuYmFja2Nvbm5lY3QudjEiRAooQmFja0Nvbm5lY3RXb3JrZXJTZXJ2aWNlQ29uZmlnSG9wUmVxdWVzdBIYCgNob3AYASABKAsyCy5ob3AudjEuSG9wIlMKKUJhY2tDb25uZWN0V29ya2VyU2VydmljZUNvbmZpZ0hvcFJlc3BvbnNlEiYKBWVycm9yGAEgASgLMhcuZXJybXNnLnYxLkVycm9yTWVzc2FnZSIsCipCYWNrQ29ubmVjdFdvcmtlclNlcnZpY2VIZWFsdGhDaGVja1JlcXVlc3QiVQorQmFja0Nvbm5lY3RXb3JrZXJTZXJ2aWNlSGVhbHRoQ2hlY2tSZXNwb25zZRImCgVlcnJvchgBIAEoCzIXLmVycm1zZy52MS5FcnJvck1lc3NhZ2Uy2gIKGEJhY2tDb25uZWN0V29ya2VyU2VydmljZRKgAQoLSGVhbHRoQ2hlY2sSRy5wcm94eW1hbmFnZXIuYmFja2Nvbm5lY3QudjEuQmFja0Nvbm5lY3RXb3JrZXJTZXJ2aWNlSGVhbHRoQ2hlY2tSZXF1ZXN0GkgucHJveHltYW5hZ2VyLmJhY2tjb25uZWN0LnYxLkJhY2tDb25uZWN0V29ya2VyU2VydmljZUhlYWx0aENoZWNrUmVzcG9uc2USmgEKCUNvbmZpZ0hvcBJFLnByb3h5bWFuYWdlci5iYWNrY29ubmVjdC52MS5CYWNrQ29ubmVjdFdvcmtlclNlcnZpY2VDb25maWdIb3BSZXF1ZXN0GkYucHJveHltYW5hZ2VyLmJhY2tjb25uZWN0LnYxLkJhY2tDb25uZWN0V29ya2VyU2VydmljZUNvbmZpZ0hvcFJlc3BvbnNlQlpaWGdpdC50bXByb3h5LWluZnJhLmNvbS9hbGdvL2FsZ29wcm94eS1wcm90by9nZW4vcHJveHltYW5hZ2VyL2JhY2tjb25uZWN0L3YxO2JhY2tjb25uZWN0djFiBnByb3RvMw", [file_errmsg_v1_errormsg, file_hop_v1_hop]);

/**
 * Describes the message proxymanager.backconnect.v1.BackConnectWorkerServiceConfigHopRequest.
 * Use `create(BackConnectWorkerServiceConfigHopRequestSchema)` to create a new message.
 */
export const BackConnectWorkerServiceConfigHopRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_worker, 0);

/**
 * Describes the message proxymanager.backconnect.v1.BackConnectWorkerServiceConfigHopResponse.
 * Use `create(BackConnectWorkerServiceConfigHopResponseSchema)` to create a new message.
 */
export const BackConnectWorkerServiceConfigHopResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_worker, 1);

/**
 * Describes the message proxymanager.backconnect.v1.BackConnectWorkerServiceHealthCheckRequest.
 * Use `create(BackConnectWorkerServiceHealthCheckRequestSchema)` to create a new message.
 */
export const BackConnectWorkerServiceHealthCheckRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_worker, 2);

/**
 * Describes the message proxymanager.backconnect.v1.BackConnectWorkerServiceHealthCheckResponse.
 * Use `create(BackConnectWorkerServiceHealthCheckResponseSchema)` to create a new message.
 */
export const BackConnectWorkerServiceHealthCheckResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_worker, 3);

/**
 * BackConnectWorkerService receive command from controlplane
 *
 * @generated from service proxymanager.backconnect.v1.BackConnectWorkerService
 */
export const BackConnectWorkerService = /*@__PURE__*/
  serviceDesc(file_proxymanager_backconnect_v1_worker, 0);

