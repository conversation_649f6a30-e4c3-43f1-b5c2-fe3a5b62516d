// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file proxymanager/backconnect/v1/backoffice.proto (package proxymanager.backconnect.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_algoenum_v1_back_connect_manager_status } from "../../../algoenum/v1/back_connect_manager_status_pb";
import { file_algoenum_v1_back_connect_port_status } from "../../../algoenum/v1/back_connect_port_status_pb";
import { file_algoenum_v1_location_level } from "../../../algoenum/v1/location_level_pb";
import { file_algoenum_v1_proxy_protocol } from "../../../algoenum/v1/proxy_protocol_pb";

/**
 * Describes the file proxymanager/backconnect/v1/backoffice.proto.
 */
export const file_proxymanager_backconnect_v1_backoffice = /*@__PURE__*/
  fileDesc("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", [file_errmsg_v1_errormsg, file_utils_v1_utils, file_algoenum_v1_back_connect_manager_status, file_algoenum_v1_back_connect_port_status, file_algoenum_v1_location_level, file_algoenum_v1_proxy_protocol]);

/**
 * Describes the message proxymanager.backconnect.v1.BackofficeBackConnectServiceCreateBackConnectManagerRequest.
 * Use `create(BackofficeBackConnectServiceCreateBackConnectManagerRequestSchema)` to create a new message.
 */
export const BackofficeBackConnectServiceCreateBackConnectManagerRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_backoffice, 0);

/**
 * Describes the message proxymanager.backconnect.v1.BackofficeBackConnectServiceCreateBackConnectManagerResponse.
 * Use `create(BackofficeBackConnectServiceCreateBackConnectManagerResponseSchema)` to create a new message.
 */
export const BackofficeBackConnectServiceCreateBackConnectManagerResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_backoffice, 1);

/**
 * Describes the message proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectManagerRequest.
 * Use `create(BackofficeBackConnectServiceFetchBackConnectManagerRequestSchema)` to create a new message.
 */
export const BackofficeBackConnectServiceFetchBackConnectManagerRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_backoffice, 2);

/**
 * Describes the message proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectManagerResponse.
 * Use `create(BackofficeBackConnectServiceFetchBackConnectManagerResponseSchema)` to create a new message.
 */
export const BackofficeBackConnectServiceFetchBackConnectManagerResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_backoffice, 3);

/**
 * Describes the message proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectManager.
 * Use `create(BackofficeBackConnectServiceBackConnectManagerSchema)` to create a new message.
 */
export const BackofficeBackConnectServiceBackConnectManagerSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_backoffice, 4);

/**
 * Describes the message proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectManagerLocation.
 * Use `create(BackofficeBackConnectServiceBackConnectManagerLocationSchema)` to create a new message.
 */
export const BackofficeBackConnectServiceBackConnectManagerLocationSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_backoffice, 5);

/**
 * Describes the message proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectManagerTelco.
 * Use `create(BackofficeBackConnectServiceBackConnectManagerTelcoSchema)` to create a new message.
 */
export const BackofficeBackConnectServiceBackConnectManagerTelcoSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_backoffice, 6);

/**
 * Describes the message proxymanager.backconnect.v1.BackofficeBackConnectServiceUpdateBackConnectManagerRequest.
 * Use `create(BackofficeBackConnectServiceUpdateBackConnectManagerRequestSchema)` to create a new message.
 */
export const BackofficeBackConnectServiceUpdateBackConnectManagerRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_backoffice, 7);

/**
 * Describes the message proxymanager.backconnect.v1.BackofficeBackConnectServiceUpdateBackConnectManagerResponse.
 * Use `create(BackofficeBackConnectServiceUpdateBackConnectManagerResponseSchema)` to create a new message.
 */
export const BackofficeBackConnectServiceUpdateBackConnectManagerResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_backoffice, 8);

/**
 * Describes the message proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectPortRequest.
 * Use `create(BackofficeBackConnectServiceFetchBackConnectPortRequestSchema)` to create a new message.
 */
export const BackofficeBackConnectServiceFetchBackConnectPortRequestSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_backoffice, 9);

/**
 * Describes the message proxymanager.backconnect.v1.BackofficeBackConnectServiceFetchBackConnectPortResponse.
 * Use `create(BackofficeBackConnectServiceFetchBackConnectPortResponseSchema)` to create a new message.
 */
export const BackofficeBackConnectServiceFetchBackConnectPortResponseSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_backoffice, 10);

/**
 * Describes the message proxymanager.backconnect.v1.BackofficeBackConnectServiceBackConnectPort.
 * Use `create(BackofficeBackConnectServiceBackConnectPortSchema)` to create a new message.
 */
export const BackofficeBackConnectServiceBackConnectPortSchema = /*@__PURE__*/
  messageDesc(file_proxymanager_backconnect_v1_backoffice, 11);

/**
 * @generated from service proxymanager.backconnect.v1.BackofficeBackConnectService
 */
export const BackofficeBackConnectService = /*@__PURE__*/
  serviceDesc(file_proxymanager_backconnect_v1_backoffice, 0);

