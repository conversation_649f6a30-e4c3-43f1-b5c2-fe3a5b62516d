// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file auth/v1/merchant.proto (package auth.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_auth_v1_backoffice_auth } from "./backoffice_auth_pb";
import { file_errmsg_v1_errormsg } from "../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../utils/v1/utils_pb";

/**
 * Describes the file auth/v1/merchant.proto.
 */
export const file_auth_v1_merchant = /*@__PURE__*/
  fileDesc("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", [file_auth_v1_backoffice_auth, file_errmsg_v1_errormsg, file_utils_v1_utils]);

/**
 * Describes the message auth.v1.MerchantAuthServiceChangePasswordRequest.
 * Use `create(MerchantAuthServiceChangePasswordRequestSchema)` to create a new message.
 */
export const MerchantAuthServiceChangePasswordRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 0);

/**
 * Describes the message auth.v1.MerchantAuthServiceChangePasswordResponse.
 * Use `create(MerchantAuthServiceChangePasswordResponseSchema)` to create a new message.
 */
export const MerchantAuthServiceChangePasswordResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 1);

/**
 * Describes the message auth.v1.MerchantAuthServiceFetchRoleRequest.
 * Use `create(MerchantAuthServiceFetchRoleRequestSchema)` to create a new message.
 */
export const MerchantAuthServiceFetchRoleRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 2);

/**
 * Describes the message auth.v1.MerchantAuthServiceFetchRoleResponse.
 * Use `create(MerchantAuthServiceFetchRoleResponseSchema)` to create a new message.
 */
export const MerchantAuthServiceFetchRoleResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 3);

/**
 * Describes the message auth.v1.MerchantRoleModel.
 * Use `create(MerchantRoleModelSchema)` to create a new message.
 */
export const MerchantRoleModelSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 4);

/**
 * Describes the message auth.v1.MerchantAuthServiceUpdateUserRequest.
 * Use `create(MerchantAuthServiceUpdateUserRequestSchema)` to create a new message.
 */
export const MerchantAuthServiceUpdateUserRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 5);

/**
 * Describes the message auth.v1.MerchantAuthServiceUpdateUserResponse.
 * Use `create(MerchantAuthServiceUpdateUserResponseSchema)` to create a new message.
 */
export const MerchantAuthServiceUpdateUserResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 6);

/**
 * Describes the message auth.v1.MerchantAuthServiceFetchUserRequest.
 * Use `create(MerchantAuthServiceFetchUserRequestSchema)` to create a new message.
 */
export const MerchantAuthServiceFetchUserRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 7);

/**
 * Describes the message auth.v1.MerchantAuthServiceFetchUserResponse.
 * Use `create(MerchantAuthServiceFetchUserResponseSchema)` to create a new message.
 */
export const MerchantAuthServiceFetchUserResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 8);

/**
 * Describes the message auth.v1.MerchantServiceUserModel.
 * Use `create(MerchantServiceUserModelSchema)` to create a new message.
 */
export const MerchantServiceUserModelSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 9);

/**
 * Describes the message auth.v1.MerchantUserRoleModel.
 * Use `create(MerchantUserRoleModelSchema)` to create a new message.
 */
export const MerchantUserRoleModelSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 10);

/**
 * Describes the message auth.v1.MerchantAuthServiceSignUpRequest.
 * Use `create(MerchantAuthServiceSignUpRequestSchema)` to create a new message.
 */
export const MerchantAuthServiceSignUpRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 11);

/**
 * Describes the message auth.v1.MerchantAuthServiceSignUpResponse.
 * Use `create(MerchantAuthServiceSignUpResponseSchema)` to create a new message.
 */
export const MerchantAuthServiceSignUpResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 12);

/**
 * Describes the message auth.v1.MerchantAuthServiceLoginRequest.
 * Use `create(MerchantAuthServiceLoginRequestSchema)` to create a new message.
 */
export const MerchantAuthServiceLoginRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 13);

/**
 * Describes the message auth.v1.MerchantAuthServiceLoginResponse.
 * Use `create(MerchantAuthServiceLoginResponseSchema)` to create a new message.
 */
export const MerchantAuthServiceLoginResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 14);

/**
 * Describes the message auth.v1.MerchantAuthServiceMeRequest.
 * Use `create(MerchantAuthServiceMeRequestSchema)` to create a new message.
 */
export const MerchantAuthServiceMeRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 15);

/**
 * Describes the message auth.v1.MerchantAuthServiceMeResponse.
 * Use `create(MerchantAuthServiceMeResponseSchema)` to create a new message.
 */
export const MerchantAuthServiceMeResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 16);

/**
 * Describes the message auth.v1.MerchantMeUserDetails.
 * Use `create(MerchantMeUserDetailsSchema)` to create a new message.
 */
export const MerchantMeUserDetailsSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 17);

/**
 * Describes the message auth.v1.MerchantAuthServiceDetail.
 * Use `create(MerchantAuthServiceDetailSchema)` to create a new message.
 */
export const MerchantAuthServiceDetailSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 18);

/**
 * Describes the message auth.v1.MerchantAuthServiceRefreshTokenRequest.
 * Use `create(MerchantAuthServiceRefreshTokenRequestSchema)` to create a new message.
 */
export const MerchantAuthServiceRefreshTokenRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 19);

/**
 * Describes the message auth.v1.MerchantAuthServiceRefreshTokenResponse.
 * Use `create(MerchantAuthServiceRefreshTokenResponseSchema)` to create a new message.
 */
export const MerchantAuthServiceRefreshTokenResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 20);

/**
 * Describes the message auth.v1.MerchantAuthServiceForgotPasswordRequest.
 * Use `create(MerchantAuthServiceForgotPasswordRequestSchema)` to create a new message.
 */
export const MerchantAuthServiceForgotPasswordRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 21);

/**
 * Describes the message auth.v1.MerchantAuthServiceForgotPasswordResponse.
 * Use `create(MerchantAuthServiceForgotPasswordResponseSchema)` to create a new message.
 */
export const MerchantAuthServiceForgotPasswordResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_merchant, 22);

/**
 * @generated from service auth.v1.MerchantAuthService
 */
export const MerchantAuthService = /*@__PURE__*/
  serviceDesc(file_auth_v1_merchant, 0);

