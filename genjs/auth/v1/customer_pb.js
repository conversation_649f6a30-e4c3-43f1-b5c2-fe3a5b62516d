// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file auth/v1/customer.proto (package auth.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../errmsg/v1/errormsg_pb";

/**
 * Describes the file auth/v1/customer.proto.
 */
export const file_auth_v1_customer = /*@__PURE__*/
  fileDesc("ChZhdXRoL3YxL2N1c3RvbWVyLnByb3RvEgdhdXRoLnYxIlYKKEN1c3RvbWVyQXV0aFNlcnZpY2VDaGFuZ2VQYXNzd29yZFJlcXVlc3QSFAoMb2xkX3Bhc3N3b3JkGAEgASgJEhQKDG5ld19wYXNzd29yZBgCIAEoCSJTCilDdXN0b21lckF1dGhTZXJ2aWNlQ2hhbmdlUGFzc3dvcmRSZXNwb25zZRImCgVlcnJvchgBIAEoCzIXLmVycm1zZy52MS5FcnJvck1lc3NhZ2Ui2QEKIEN1c3RvbWVyQXV0aFNlcnZpY2VTaWduVXBSZXF1ZXN0Eg0KBWVtYWlsGAEgASgJEhAKCHBhc3N3b3JkGAIgASgJEhIKCmZpcnN0X25hbWUYAyABKAkSEQoJbGFzdF9uYW1lGAQgASgJEhQKDHBob25lX251bWJlchgFIAEoCRIQCghyZWZfY29kZRgGIAEoCRIOCgZzdHJlZXQYByABKAkSEAoIaWRfc3RhdGUYCCABKAMSDwoHaWRfY2l0eRgJIAEoAxISCgppZF9jb3VudHJ5GAogASgDIl4KIUN1c3RvbWVyQXV0aFNlcnZpY2VTaWduVXBSZXNwb25zZRIRCgl0b2tlbl90dGwYASABKAMSJgoFZXJyb3IYAiABKAsyFy5lcnJtc2cudjEuRXJyb3JNZXNzYWdlIjcKJkN1c3RvbWVyQXV0aFNlcnZpY2VWZXJpZnlTaWduVXBSZXF1ZXN0Eg0KBXRva2VuGAEgASgJIlEKJ0N1c3RvbWVyQXV0aFNlcnZpY2VWZXJpZnlTaWduVXBSZXNwb25zZRImCgVlcnJvchgBIAEoCzIXLmVycm1zZy52MS5FcnJvck1lc3NhZ2UiYgofQ3VzdG9tZXJBdXRoU2VydmljZUxvZ2luUmVxdWVzdBINCgVlbWFpbBgBIAEoCRIQCghwYXNzd29yZBgCIAEoCRILCgNvdHAYAyABKAkSEQoJZGV2aWNlX2lkGAQgASgJInAKIEN1c3RvbWVyQXV0aFNlcnZpY2VMb2dpblJlc3BvbnNlEg0KBXRva2VuGAEgASgJEhUKDXJlZnJlc2hfdG9rZW4YAiABKAkSJgoFZXJyb3IYAyABKAsyFy5lcnJtc2cudjEuRXJyb3JNZXNzYWdlIh4KHEN1c3RvbWVyQXV0aFNlcnZpY2VNZVJlcXVlc3QiiwEKHUN1c3RvbWVyQXV0aFNlcnZpY2VNZVJlc3BvbnNlEjMKC3VzZXJfZGV0YWlsGAEgASgLMh4uYXV0aC52MS5DdXN0b21lck1lVXNlckRldGFpbHMSDQoFcGF0aHMYAiADKAkSJgoFZXJyb3IYAyABKAsyFy5lcnJtc2cudjEuRXJyb3JNZXNzYWdlIooBChVDdXN0b21lck1lVXNlckRldGFpbHMSEgoKZmlyc3RfbmFtZRgBIAEoCRIRCglsYXN0X25hbWUYAiABKAkSDQoFZW1haWwYAyABKAkSFAoMcGhvbmVfbnVtYmVyGAQgASgJEhAKCHJlZl9jb2RlGAUgASgJEhMKC3VzZXJfcmVmX2lkGAYgASgJIlIKJkN1c3RvbWVyQXV0aFNlcnZpY2VSZWZyZXNoVG9rZW5SZXF1ZXN0EhUKDXJlZnJlc2hfdG9rZW4YASABKAkSEQoJZGV2aWNlX2lkGAIgASgJIncKJ0N1c3RvbWVyQXV0aFNlcnZpY2VSZWZyZXNoVG9rZW5SZXNwb25zZRImCgVlcnJvchgBIAEoCzIXLmVycm1zZy52MS5FcnJvck1lc3NhZ2USDQoFdG9rZW4YAiABKAkSFQoNcmVmcmVzaF90b2tlbhgDIAEoCSJsCihDdXN0b21lckF1dGhTZXJ2aWNlRm9yZ290UGFzc3dvcmRSZXF1ZXN0Eg0KBWVtYWlsGAEgASgJEg4KBmRvbWFpbhgCIAEoCRILCgNvdHAYAyABKAkSFAoMbmV3X3Bhc3N3b3JkGAQgASgJIlMKKUN1c3RvbWVyQXV0aFNlcnZpY2VGb3Jnb3RQYXNzd29yZFJlc3BvbnNlEiYKBWVycm9yGAEgASgLMhcuZXJybXNnLnYxLkVycm9yTWVzc2FnZSJOCiRDdXN0b21lckF1dGhTZXJ2aWNlTG9naW5PQXV0aFJlcXVlc3QSEwoLb2F1dGhfdG9rZW4YASABKAkSEQoJZGV2aWNlX2lkGAIgASgJInUKJUN1c3RvbWVyQXV0aFNlcnZpY2VMb2dpbk9BdXRoUmVzcG9uc2USJgoFZXJyb3IYASABKAsyFy5lcnJtc2cudjEuRXJyb3JNZXNzYWdlEg0KBXRva2VuGAIgASgJEhUKDXJlZnJlc2hfdG9rZW4YAyABKAkiKQonQ3VzdG9tZXJBdXRoU2VydmljZUZldGNoT0F1dGhBcHBSZXF1ZXN0ImUKKEN1c3RvbWVyQXV0aFNlcnZpY2VGZXRjaE9BdXRoQXBwUmVzcG9uc2USJgoFZXJyb3IYASABKAsyFy5lcnJtc2cudjEuRXJyb3JNZXNzYWdlEhEKCWNsaWVudF9pZBgCIAEoCTLkBwoTQ3VzdG9tZXJBdXRoU2VydmljZRJfCgZTaWduVXASKS5hdXRoLnYxLkN1c3RvbWVyQXV0aFNlcnZpY2VTaWduVXBSZXF1ZXN0GiouYXV0aC52MS5DdXN0b21lckF1dGhTZXJ2aWNlU2lnblVwUmVzcG9uc2UScQoMVmVyaWZ5U2lnblVwEi8uYXV0aC52MS5DdXN0b21lckF1dGhTZXJ2aWNlVmVyaWZ5U2lnblVwUmVxdWVzdBowLmF1dGgudjEuQ3VzdG9tZXJBdXRoU2VydmljZVZlcmlmeVNpZ25VcFJlc3BvbnNlElwKBUxvZ2luEiguYXV0aC52MS5DdXN0b21lckF1dGhTZXJ2aWNlTG9naW5SZXF1ZXN0GikuYXV0aC52MS5DdXN0b21lckF1dGhTZXJ2aWNlTG9naW5SZXNwb25zZRJTCgJNZRIlLmF1dGgudjEuQ3VzdG9tZXJBdXRoU2VydmljZU1lUmVxdWVzdBomLmF1dGgudjEuQ3VzdG9tZXJBdXRoU2VydmljZU1lUmVzcG9uc2USdwoOQ2hhbmdlUGFzc3dvcmQSMS5hdXRoLnYxLkN1c3RvbWVyQXV0aFNlcnZpY2VDaGFuZ2VQYXNzd29yZFJlcXVlc3QaMi5hdXRoLnYxLkN1c3RvbWVyQXV0aFNlcnZpY2VDaGFuZ2VQYXNzd29yZFJlc3BvbnNlEnEKDFJlZnJlc2hUb2tlbhIvLmF1dGgudjEuQ3VzdG9tZXJBdXRoU2VydmljZVJlZnJlc2hUb2tlblJlcXVlc3QaMC5hdXRoLnYxLkN1c3RvbWVyQXV0aFNlcnZpY2VSZWZyZXNoVG9rZW5SZXNwb25zZRJ3Cg5Gb3Jnb3RQYXNzd29yZBIxLmF1dGgudjEuQ3VzdG9tZXJBdXRoU2VydmljZUZvcmdvdFBhc3N3b3JkUmVxdWVzdBoyLmF1dGgudjEuQ3VzdG9tZXJBdXRoU2VydmljZUZvcmdvdFBhc3N3b3JkUmVzcG9uc2USawoKTG9naW5PQXV0aBItLmF1dGgudjEuQ3VzdG9tZXJBdXRoU2VydmljZUxvZ2luT0F1dGhSZXF1ZXN0Gi4uYXV0aC52MS5DdXN0b21lckF1dGhTZXJ2aWNlTG9naW5PQXV0aFJlc3BvbnNlEnQKDUZldGNoT0F1dGhBcHASMC5hdXRoLnYxLkN1c3RvbWVyQXV0aFNlcnZpY2VGZXRjaE9BdXRoQXBwUmVxdWVzdBoxLmF1dGgudjEuQ3VzdG9tZXJBdXRoU2VydmljZUZldGNoT0F1dGhBcHBSZXNwb25zZUI/Wj1naXQudG1wcm94eS1pbmZyYS5jb20vYWxnby9hbGdvcHJveHktcHJvdG8vZ2VuL2F1dGgvdjE7YXV0aHYxYgZwcm90bzM", [file_errmsg_v1_errormsg]);

/**
 * Describes the message auth.v1.CustomerAuthServiceChangePasswordRequest.
 * Use `create(CustomerAuthServiceChangePasswordRequestSchema)` to create a new message.
 */
export const CustomerAuthServiceChangePasswordRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 0);

/**
 * Describes the message auth.v1.CustomerAuthServiceChangePasswordResponse.
 * Use `create(CustomerAuthServiceChangePasswordResponseSchema)` to create a new message.
 */
export const CustomerAuthServiceChangePasswordResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 1);

/**
 * Describes the message auth.v1.CustomerAuthServiceSignUpRequest.
 * Use `create(CustomerAuthServiceSignUpRequestSchema)` to create a new message.
 */
export const CustomerAuthServiceSignUpRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 2);

/**
 * Describes the message auth.v1.CustomerAuthServiceSignUpResponse.
 * Use `create(CustomerAuthServiceSignUpResponseSchema)` to create a new message.
 */
export const CustomerAuthServiceSignUpResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 3);

/**
 * Describes the message auth.v1.CustomerAuthServiceVerifySignUpRequest.
 * Use `create(CustomerAuthServiceVerifySignUpRequestSchema)` to create a new message.
 */
export const CustomerAuthServiceVerifySignUpRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 4);

/**
 * Describes the message auth.v1.CustomerAuthServiceVerifySignUpResponse.
 * Use `create(CustomerAuthServiceVerifySignUpResponseSchema)` to create a new message.
 */
export const CustomerAuthServiceVerifySignUpResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 5);

/**
 * Describes the message auth.v1.CustomerAuthServiceLoginRequest.
 * Use `create(CustomerAuthServiceLoginRequestSchema)` to create a new message.
 */
export const CustomerAuthServiceLoginRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 6);

/**
 * Describes the message auth.v1.CustomerAuthServiceLoginResponse.
 * Use `create(CustomerAuthServiceLoginResponseSchema)` to create a new message.
 */
export const CustomerAuthServiceLoginResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 7);

/**
 * Describes the message auth.v1.CustomerAuthServiceMeRequest.
 * Use `create(CustomerAuthServiceMeRequestSchema)` to create a new message.
 */
export const CustomerAuthServiceMeRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 8);

/**
 * Describes the message auth.v1.CustomerAuthServiceMeResponse.
 * Use `create(CustomerAuthServiceMeResponseSchema)` to create a new message.
 */
export const CustomerAuthServiceMeResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 9);

/**
 * Describes the message auth.v1.CustomerMeUserDetails.
 * Use `create(CustomerMeUserDetailsSchema)` to create a new message.
 */
export const CustomerMeUserDetailsSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 10);

/**
 * Describes the message auth.v1.CustomerAuthServiceRefreshTokenRequest.
 * Use `create(CustomerAuthServiceRefreshTokenRequestSchema)` to create a new message.
 */
export const CustomerAuthServiceRefreshTokenRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 11);

/**
 * Describes the message auth.v1.CustomerAuthServiceRefreshTokenResponse.
 * Use `create(CustomerAuthServiceRefreshTokenResponseSchema)` to create a new message.
 */
export const CustomerAuthServiceRefreshTokenResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 12);

/**
 * Describes the message auth.v1.CustomerAuthServiceForgotPasswordRequest.
 * Use `create(CustomerAuthServiceForgotPasswordRequestSchema)` to create a new message.
 */
export const CustomerAuthServiceForgotPasswordRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 13);

/**
 * Describes the message auth.v1.CustomerAuthServiceForgotPasswordResponse.
 * Use `create(CustomerAuthServiceForgotPasswordResponseSchema)` to create a new message.
 */
export const CustomerAuthServiceForgotPasswordResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 14);

/**
 * Describes the message auth.v1.CustomerAuthServiceLoginOAuthRequest.
 * Use `create(CustomerAuthServiceLoginOAuthRequestSchema)` to create a new message.
 */
export const CustomerAuthServiceLoginOAuthRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 15);

/**
 * Describes the message auth.v1.CustomerAuthServiceLoginOAuthResponse.
 * Use `create(CustomerAuthServiceLoginOAuthResponseSchema)` to create a new message.
 */
export const CustomerAuthServiceLoginOAuthResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 16);

/**
 * Describes the message auth.v1.CustomerAuthServiceFetchOAuthAppRequest.
 * Use `create(CustomerAuthServiceFetchOAuthAppRequestSchema)` to create a new message.
 */
export const CustomerAuthServiceFetchOAuthAppRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 17);

/**
 * Describes the message auth.v1.CustomerAuthServiceFetchOAuthAppResponse.
 * Use `create(CustomerAuthServiceFetchOAuthAppResponseSchema)` to create a new message.
 */
export const CustomerAuthServiceFetchOAuthAppResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_customer, 18);

/**
 * @generated from service auth.v1.CustomerAuthService
 */
export const CustomerAuthService = /*@__PURE__*/
  serviceDesc(file_auth_v1_customer, 0);

