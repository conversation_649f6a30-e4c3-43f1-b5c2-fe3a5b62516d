// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file auth/v1/internal.proto (package auth.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../utils/v1/utils_pb";
import { file_algoenum_v1_app_type } from "../../algoenum/v1/app_type_pb";
import { file_algoenum_v1_app_country } from "../../algoenum/v1/app_country_pb";

/**
 * Describes the file auth/v1/internal.proto.
 */
export const file_auth_v1_internal = /*@__PURE__*/
  fileDesc("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", [file_errmsg_v1_errormsg, file_utils_v1_utils, file_algoenum_v1_app_type, file_algoenum_v1_app_country]);

/**
 * Describes the message auth.v1.InternalAuthServiceFetchUserRequest.
 * Use `create(InternalAuthServiceFetchUserRequestSchema)` to create a new message.
 */
export const InternalAuthServiceFetchUserRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 0);

/**
 * Describes the message auth.v1.InternalAuthServiceFetchUserResponse.
 * Use `create(InternalAuthServiceFetchUserResponseSchema)` to create a new message.
 */
export const InternalAuthServiceFetchUserResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 1);

/**
 * Describes the message auth.v1.InternalAuthServiceFetchUserUserModel.
 * Use `create(InternalAuthServiceFetchUserUserModelSchema)` to create a new message.
 */
export const InternalAuthServiceFetchUserUserModelSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 2);

/**
 * Describes the message auth.v1.InternalAuthServiceCustomerLoginRequest.
 * Use `create(InternalAuthServiceCustomerLoginRequestSchema)` to create a new message.
 */
export const InternalAuthServiceCustomerLoginRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 3);

/**
 * Describes the message auth.v1.InternalAuthServiceCustomerLoginResponse.
 * Use `create(InternalAuthServiceCustomerLoginResponseSchema)` to create a new message.
 */
export const InternalAuthServiceCustomerLoginResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 4);

/**
 * Describes the message auth.v1.InternalAuthServiceFetchUserInfoRequest.
 * Use `create(InternalAuthServiceFetchUserInfoRequestSchema)` to create a new message.
 */
export const InternalAuthServiceFetchUserInfoRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 5);

/**
 * Describes the message auth.v1.InternalAuthServiceFetchUserInfoResponse.
 * Use `create(InternalAuthServiceFetchUserInfoResponseSchema)` to create a new message.
 */
export const InternalAuthServiceFetchUserInfoResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 6);

/**
 * Describes the message auth.v1.InternalUserInfoModel.
 * Use `create(InternalUserInfoModelSchema)` to create a new message.
 */
export const InternalUserInfoModelSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 7);

/**
 * Describes the message auth.v1.InternalAuthServiceCreateAppMerchantRequest.
 * Use `create(InternalAuthServiceCreateAppMerchantRequestSchema)` to create a new message.
 */
export const InternalAuthServiceCreateAppMerchantRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 8);

/**
 * Describes the message auth.v1.InternalAuthServiceCreateAppMerchantResponse.
 * Use `create(InternalAuthServiceCreateAppMerchantResponseSchema)` to create a new message.
 */
export const InternalAuthServiceCreateAppMerchantResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 9);

/**
 * Describes the message auth.v1.AuthInternalServiceCreateUserRequest.
 * Use `create(AuthInternalServiceCreateUserRequestSchema)` to create a new message.
 */
export const AuthInternalServiceCreateUserRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 10);

/**
 * Describes the message auth.v1.AuthInternalServiceCreateUserResponse.
 * Use `create(AuthInternalServiceCreateUserResponseSchema)` to create a new message.
 */
export const AuthInternalServiceCreateUserResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 11);

/**
 * Describes the message auth.v1.InternalAuthServiceFetchAppRequest.
 * Use `create(InternalAuthServiceFetchAppRequestSchema)` to create a new message.
 */
export const InternalAuthServiceFetchAppRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 12);

/**
 * Describes the message auth.v1.InternalAuthServiceFetchAppResponse.
 * Use `create(InternalAuthServiceFetchAppResponseSchema)` to create a new message.
 */
export const InternalAuthServiceFetchAppResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 13);

/**
 * Describes the message auth.v1.InternalAuthServiceMerchantLoginRequest.
 * Use `create(InternalAuthServiceMerchantLoginRequestSchema)` to create a new message.
 */
export const InternalAuthServiceMerchantLoginRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 14);

/**
 * Describes the message auth.v1.InternalAuthServiceMerchantLoginResponse.
 * Use `create(InternalAuthServiceMerchantLoginResponseSchema)` to create a new message.
 */
export const InternalAuthServiceMerchantLoginResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 15);

/**
 * Describes the message auth.v1.InternalAuthServiceLoginRequest.
 * Use `create(InternalAuthServiceLoginRequestSchema)` to create a new message.
 */
export const InternalAuthServiceLoginRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 16);

/**
 * Describes the message auth.v1.InternalAuthServiceLoginResponse.
 * Use `create(InternalAuthServiceLoginResponseSchema)` to create a new message.
 */
export const InternalAuthServiceLoginResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 17);

/**
 * Describes the message auth.v1.InternalAuthServiceLogoutRequest.
 * Use `create(InternalAuthServiceLogoutRequestSchema)` to create a new message.
 */
export const InternalAuthServiceLogoutRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 18);

/**
 * Describes the message auth.v1.InternalAuthServiceLogoutResponse.
 * Use `create(InternalAuthServiceLogoutResponseSchema)` to create a new message.
 */
export const InternalAuthServiceLogoutResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 19);

/**
 * Describes the message auth.v1.InternalAuthServiceRegisterRequest.
 * Use `create(InternalAuthServiceRegisterRequestSchema)` to create a new message.
 */
export const InternalAuthServiceRegisterRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 20);

/**
 * Describes the message auth.v1.InternalAuthServiceRegisterResponse.
 * Use `create(InternalAuthServiceRegisterResponseSchema)` to create a new message.
 */
export const InternalAuthServiceRegisterResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 21);

/**
 * Describes the message auth.v1.InternalAuthServiceChangePasswordRequest.
 * Use `create(InternalAuthServiceChangePasswordRequestSchema)` to create a new message.
 */
export const InternalAuthServiceChangePasswordRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 22);

/**
 * Describes the message auth.v1.InternalAuthServiceChangePasswordResponse.
 * Use `create(InternalAuthServiceChangePasswordResponseSchema)` to create a new message.
 */
export const InternalAuthServiceChangePasswordResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 23);

/**
 * Describes the message auth.v1.InternalAuthServiceVerifyRequest.
 * Use `create(InternalAuthServiceVerifyRequestSchema)` to create a new message.
 */
export const InternalAuthServiceVerifyRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 24);

/**
 * Describes the message auth.v1.InternalAuthServiceVerifyResponse.
 * Use `create(InternalAuthServiceVerifyResponseSchema)` to create a new message.
 */
export const InternalAuthServiceVerifyResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 25);

/**
 * Describes the message auth.v1.InternalUserModel.
 * Use `create(InternalUserModelSchema)` to create a new message.
 */
export const InternalUserModelSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 26);

/**
 * Describes the message auth.v1.InternalAppModel.
 * Use `create(InternalAppModelSchema)` to create a new message.
 */
export const InternalAppModelSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 27);

/**
 * Describes the message auth.v1.InternalAuthServiceSendMailHtmlRequest.
 * Use `create(InternalAuthServiceSendMailHtmlRequestSchema)` to create a new message.
 */
export const InternalAuthServiceSendMailHtmlRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 28);

/**
 * Describes the message auth.v1.InternalAuthServiceSendMailHtmlResponse.
 * Use `create(InternalAuthServiceSendMailHtmlResponseSchema)` to create a new message.
 */
export const InternalAuthServiceSendMailHtmlResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 29);

/**
 * Describes the message auth.v1.InternalAuthServiceRefreshTokenRequest.
 * Use `create(InternalAuthServiceRefreshTokenRequestSchema)` to create a new message.
 */
export const InternalAuthServiceRefreshTokenRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 30);

/**
 * Describes the message auth.v1.InternalAuthServiceRefreshTokenResponse.
 * Use `create(InternalAuthServiceRefreshTokenResponseSchema)` to create a new message.
 */
export const InternalAuthServiceRefreshTokenResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 31);

/**
 * Describes the message auth.v1.InternalAuthServiceForgotPasswordRequest.
 * Use `create(InternalAuthServiceForgotPasswordRequestSchema)` to create a new message.
 */
export const InternalAuthServiceForgotPasswordRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 32);

/**
 * Describes the message auth.v1.InternalAuthServiceForgotPasswordResponse.
 * Use `create(InternalAuthServiceForgotPasswordResponseSchema)` to create a new message.
 */
export const InternalAuthServiceForgotPasswordResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_internal, 33);

/**
 * @generated from service auth.v1.InternalAuthService
 */
export const InternalAuthService = /*@__PURE__*/
  serviceDesc(file_auth_v1_internal, 0);

