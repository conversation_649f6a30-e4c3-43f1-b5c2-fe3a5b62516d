// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file auth/v1/backoffice_auth.proto (package auth.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../utils/v1/utils_pb";
import { file_algoenum_v1_app_type } from "../../algoenum/v1/app_type_pb";
import { file_algoenum_v1_template_email } from "../../algoenum/v1/template_email_pb";
import { file_algoenum_v1_app_country } from "../../algoenum/v1/app_country_pb";

/**
 * Describes the file auth/v1/backoffice_auth.proto.
 */
export const file_auth_v1_backoffice_auth = /*@__PURE__*/
  fileDesc("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", [file_errmsg_v1_errormsg, file_utils_v1_utils, file_algoenum_v1_app_type, file_algoenum_v1_template_email, file_algoenum_v1_app_country]);

/**
 * Describes the message auth.v1.BackofficeAuthServiceChangePasswordRequest.
 * Use `create(BackofficeAuthServiceChangePasswordRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceChangePasswordRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 0);

/**
 * Describes the message auth.v1.BackofficeAuthServiceChangePasswordResponse.
 * Use `create(BackofficeAuthServiceChangePasswordResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceChangePasswordResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 1);

/**
 * Describes the message auth.v1.BackofficeAuthServiceMeRequest.
 * Use `create(BackofficeAuthServiceMeRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceMeRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 2);

/**
 * Describes the message auth.v1.BackofficeAuthServiceMeResponse.
 * Use `create(BackofficeAuthServiceMeResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceMeResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 3);

/**
 * Describes the message auth.v1.BackofficeUserDetail.
 * Use `create(BackofficeUserDetailSchema)` to create a new message.
 */
export const BackofficeUserDetailSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 4);

/**
 * Describes the message auth.v1.BackofficeAuthServiceReloadEnforcerRequest.
 * Use `create(BackofficeAuthServiceReloadEnforcerRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceReloadEnforcerRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 5);

/**
 * Describes the message auth.v1.BackofficeAuthServiceReloadEnforcerResponse.
 * Use `create(BackofficeAuthServiceReloadEnforcerResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceReloadEnforcerResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 6);

/**
 * Describes the message auth.v1.BackofficeAuthServiceLoginRequest.
 * Use `create(BackofficeAuthServiceLoginRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceLoginRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 7);

/**
 * Describes the message auth.v1.BackofficeAuthServiceLoginResponse.
 * Use `create(BackofficeAuthServiceLoginResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceLoginResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 8);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreateAppRequest.
 * Use `create(BackofficeAuthServiceCreateAppRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreateAppRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 9);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreateAppResponse.
 * Use `create(BackofficeAuthServiceCreateAppResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreateAppResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 10);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchAppRequest.
 * Use `create(BackofficeAuthServiceFetchAppRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchAppRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 11);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchAppResponse.
 * Use `create(BackofficeAuthServiceFetchAppResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchAppResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 12);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateAppRequest.
 * Use `create(BackofficeAuthServiceUpdateAppRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateAppRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 13);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateAppResponse.
 * Use `create(BackofficeAuthServiceUpdateAppResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateAppResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 14);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchUserRequest.
 * Use `create(BackofficeAuthServiceFetchUserRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchUserRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 15);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchUserResponse.
 * Use `create(BackofficeAuthServiceFetchUserResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchUserResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 16);

/**
 * Describes the message auth.v1.BackofficeUserModel.
 * Use `create(BackofficeUserModelSchema)` to create a new message.
 */
export const BackofficeUserModelSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 17);

/**
 * Describes the message auth.v1.BackofficeUserRoleModel.
 * Use `create(BackofficeUserRoleModelSchema)` to create a new message.
 */
export const BackofficeUserRoleModelSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 18);

/**
 * Describes the message auth.v1.AuthUserApp.
 * Use `create(AuthUserAppSchema)` to create a new message.
 */
export const AuthUserAppSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 19);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreateUserRequest.
 * Use `create(BackofficeAuthServiceCreateUserRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreateUserRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 20);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreateUserResponse.
 * Use `create(BackofficeAuthServiceCreateUserResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreateUserResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 21);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateUserRequest.
 * Use `create(BackofficeAuthServiceUpdateUserRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateUserRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 22);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateUserResponse.
 * Use `create(BackofficeAuthServiceUpdateUserResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateUserResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 23);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchRoleRequest.
 * Use `create(BackofficeAuthServiceFetchRoleRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchRoleRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 24);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchRoleResponse.
 * Use `create(BackofficeAuthServiceFetchRoleResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchRoleResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 25);

/**
 * Describes the message auth.v1.RoleModel.
 * Use `create(RoleModelSchema)` to create a new message.
 */
export const RoleModelSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 26);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreateRoleRequest.
 * Use `create(BackofficeAuthServiceCreateRoleRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreateRoleRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 27);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreateRoleResponse.
 * Use `create(BackofficeAuthServiceCreateRoleResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreateRoleResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 28);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateRoleRequest.
 * Use `create(BackofficeAuthServiceUpdateRoleRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateRoleRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 29);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateRoleResponse.
 * Use `create(BackofficeAuthServiceUpdateRoleResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateRoleResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 30);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchServiceRequest.
 * Use `create(BackofficeAuthServiceFetchServiceRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchServiceRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 31);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchServiceResponse.
 * Use `create(BackofficeAuthServiceFetchServiceResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchServiceResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 32);

/**
 * Describes the message auth.v1.ServiceModel.
 * Use `create(ServiceModelSchema)` to create a new message.
 */
export const ServiceModelSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 33);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreateServiceRequest.
 * Use `create(BackofficeAuthServiceCreateServiceRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreateServiceRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 34);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreateServiceResponse.
 * Use `create(BackofficeAuthServiceCreateServiceResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreateServiceResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 35);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateServiceRequest.
 * Use `create(BackofficeAuthServiceUpdateServiceRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateServiceRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 36);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateServiceResponse.
 * Use `create(BackofficeAuthServiceUpdateServiceResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateServiceResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 37);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchPathRequest.
 * Use `create(BackofficeAuthServiceFetchPathRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchPathRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 38);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchPathResponse.
 * Use `create(BackofficeAuthServiceFetchPathResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchPathResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 39);

/**
 * Describes the message auth.v1.PathModel.
 * Use `create(PathModelSchema)` to create a new message.
 */
export const PathModelSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 40);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreatePathRequest.
 * Use `create(BackofficeAuthServiceCreatePathRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreatePathRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 41);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreatePathResponse.
 * Use `create(BackofficeAuthServiceCreatePathResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreatePathResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 42);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdatePathRequest.
 * Use `create(BackofficeAuthServiceUpdatePathRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdatePathRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 43);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdatePathResponse.
 * Use `create(BackofficeAuthServiceUpdatePathResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdatePathResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 44);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchPolicyRequest.
 * Use `create(BackofficeAuthServiceFetchPolicyRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchPolicyRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 45);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchPolicyResponse.
 * Use `create(BackofficeAuthServiceFetchPolicyResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchPolicyResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 46);

/**
 * Describes the message auth.v1.PolicyModel.
 * Use `create(PolicyModelSchema)` to create a new message.
 */
export const PolicyModelSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 47);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreatePolicyRequest.
 * Use `create(BackofficeAuthServiceCreatePolicyRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreatePolicyRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 48);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreatePolicyResponse.
 * Use `create(BackofficeAuthServiceCreatePolicyResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreatePolicyResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 49);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdatePolicyRequest.
 * Use `create(BackofficeAuthServiceUpdatePolicyRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdatePolicyRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 50);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdatePolicyResponse.
 * Use `create(BackofficeAuthServiceUpdatePolicyResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdatePolicyResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 51);

/**
 * Describes the message auth.v1.BackofficeAppModel.
 * Use `create(BackofficeAppModelSchema)` to create a new message.
 */
export const BackofficeAppModelSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 52);

/**
 * Describes the message auth.v1.BackofficeAuthServiceInitTotpRequest.
 * Use `create(BackofficeAuthServiceInitTotpRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceInitTotpRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 53);

/**
 * Describes the message auth.v1.BackofficeAuthServiceInitTotpResponse.
 * Use `create(BackofficeAuthServiceInitTotpResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceInitTotpResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 54);

/**
 * Describes the message auth.v1.BackofficeAuthServiceVerifyTotpRequest.
 * Use `create(BackofficeAuthServiceVerifyTotpRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceVerifyTotpRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 55);

/**
 * Describes the message auth.v1.BackofficeAuthServiceVerifyTotpResponse.
 * Use `create(BackofficeAuthServiceVerifyTotpResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceVerifyTotpResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 56);

/**
 * Describes the message auth.v1.BackofficeAuthServiceRemoveTotpRequest.
 * Use `create(BackofficeAuthServiceRemoveTotpRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceRemoveTotpRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 57);

/**
 * Describes the message auth.v1.BackofficeAuthServiceRemoveTotpResponse.
 * Use `create(BackofficeAuthServiceRemoveTotpResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceRemoveTotpResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 58);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchConfigMailRequest.
 * Use `create(BackofficeAuthServiceFetchConfigMailRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchConfigMailRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 59);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchConfigMailResponse.
 * Use `create(BackofficeAuthServiceFetchConfigMailResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchConfigMailResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 60);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreateConfigMailRequest.
 * Use `create(BackofficeAuthServiceCreateConfigMailRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreateConfigMailRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 61);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreateConfigMailResponse.
 * Use `create(BackofficeAuthServiceCreateConfigMailResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreateConfigMailResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 62);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateConfigMailRequest.
 * Use `create(BackofficeAuthServiceUpdateConfigMailRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateConfigMailRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 63);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateConfigMailResponse.
 * Use `create(BackofficeAuthServiceUpdateConfigMailResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateConfigMailResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 64);

/**
 * Describes the message auth.v1.ConfigMailModel.
 * Use `create(ConfigMailModelSchema)` to create a new message.
 */
export const ConfigMailModelSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 65);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchConfigTemplateEmailRequest.
 * Use `create(BackofficeAuthServiceFetchConfigTemplateEmailRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchConfigTemplateEmailRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 66);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchConfigTemplateEmailResponse.
 * Use `create(BackofficeAuthServiceFetchConfigTemplateEmailResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchConfigTemplateEmailResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 67);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreateConfigTemplateEmailRequest.
 * Use `create(BackofficeAuthServiceCreateConfigTemplateEmailRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreateConfigTemplateEmailRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 68);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreateConfigTemplateEmailResponse.
 * Use `create(BackofficeAuthServiceCreateConfigTemplateEmailResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreateConfigTemplateEmailResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 69);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateConfigTemplateEmailRequest.
 * Use `create(BackofficeAuthServiceUpdateConfigTemplateEmailRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateConfigTemplateEmailRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 70);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateConfigTemplateEmailResponse.
 * Use `create(BackofficeAuthServiceUpdateConfigTemplateEmailResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateConfigTemplateEmailResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 71);

/**
 * Describes the message auth.v1.ConfigTemplateEmailModel.
 * Use `create(ConfigTemplateEmailModelSchema)` to create a new message.
 */
export const ConfigTemplateEmailModelSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 72);

/**
 * Describes the message auth.v1.BackofficeAuthServiceRefreshTokenRequest.
 * Use `create(BackofficeAuthServiceRefreshTokenRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceRefreshTokenRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 73);

/**
 * Describes the message auth.v1.BackofficeAuthServiceRefreshTokenResponse.
 * Use `create(BackofficeAuthServiceRefreshTokenResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceRefreshTokenResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 74);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateRefCodeRequest.
 * Use `create(BackofficeAuthServiceUpdateRefCodeRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateRefCodeRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 75);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateRefCodeResponse.
 * Use `create(BackofficeAuthServiceUpdateRefCodeResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateRefCodeResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 76);

/**
 * Describes the message auth.v1.BackofficeAuthServiceForgotPasswordRequest.
 * Use `create(BackofficeAuthServiceForgotPasswordRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceForgotPasswordRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 77);

/**
 * Describes the message auth.v1.BackofficeAuthServiceForgotPasswordResponse.
 * Use `create(BackofficeAuthServiceForgotPasswordResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceForgotPasswordResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 78);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchOAuthConfigRequest.
 * Use `create(BackofficeAuthServiceFetchOAuthConfigRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchOAuthConfigRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 79);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchOAuthConfigResponse.
 * Use `create(BackofficeAuthServiceFetchOAuthConfigResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchOAuthConfigResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 80);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreateOAuthConfigRequest.
 * Use `create(BackofficeAuthServiceCreateOAuthConfigRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreateOAuthConfigRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 81);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreateOAuthConfigResponse.
 * Use `create(BackofficeAuthServiceCreateOAuthConfigResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreateOAuthConfigResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 82);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateOAuthConfigRequest.
 * Use `create(BackofficeAuthServiceUpdateOAuthConfigRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateOAuthConfigRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 83);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateOAuthConfigResponse.
 * Use `create(BackofficeAuthServiceUpdateOAuthConfigResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateOAuthConfigResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 84);

/**
 * Describes the message auth.v1.OAuthConfigModel.
 * Use `create(OAuthConfigModelSchema)` to create a new message.
 */
export const OAuthConfigModelSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 85);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchCompanyRequest.
 * Use `create(BackofficeAuthServiceFetchCompanyRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchCompanyRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 86);

/**
 * Describes the message auth.v1.BackofficeAuthServiceFetchCompanyResponse.
 * Use `create(BackofficeAuthServiceFetchCompanyResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceFetchCompanyResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 87);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreateCompanyRequest.
 * Use `create(BackofficeAuthServiceCreateCompanyRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreateCompanyRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 88);

/**
 * Describes the message auth.v1.BackofficeAuthServiceCreateCompanyResponse.
 * Use `create(BackofficeAuthServiceCreateCompanyResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceCreateCompanyResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 89);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateCompanyRequest.
 * Use `create(BackofficeAuthServiceUpdateCompanyRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateCompanyRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 90);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateCompanyResponse.
 * Use `create(BackofficeAuthServiceUpdateCompanyResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateCompanyResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 91);

/**
 * Describes the message auth.v1.CompanyModel.
 * Use `create(CompanyModelSchema)` to create a new message.
 */
export const CompanyModelSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 92);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateProfileRequest.
 * Use `create(BackofficeAuthServiceUpdateProfileRequestSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateProfileRequestSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 93);

/**
 * Describes the message auth.v1.BackofficeAuthServiceUpdateProfileResponse.
 * Use `create(BackofficeAuthServiceUpdateProfileResponseSchema)` to create a new message.
 */
export const BackofficeAuthServiceUpdateProfileResponseSchema = /*@__PURE__*/
  messageDesc(file_auth_v1_backoffice_auth, 94);

/**
 * @generated from service auth.v1.BackofficeAuthService
 */
export const BackofficeAuthService = /*@__PURE__*/
  serviceDesc(file_auth_v1_backoffice_auth, 0);

