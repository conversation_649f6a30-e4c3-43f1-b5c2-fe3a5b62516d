// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file browsermanager/backconnect/v1/worker.proto (package browsermanager.backconnect.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";

/**
 * Describes the file browsermanager/backconnect/v1/worker.proto.
 */
export const file_browsermanager_backconnect_v1_worker = /*@__PURE__*/
  fileDesc("Cipicm93c2VybWFuYWdlci9iYWNrY29ubmVjdC92MS93b3JrZXIucHJvdG8SHWJyb3dzZXJtYW5hZ2VyLmJhY2tjb25uZWN0LnYxIl0KJ0JhY2tDb25uZWN0V29ya2VyU2VydmljZUFkZFByb3h5UmVxdWVzdBIOCgZzZXJ2ZXIYASABKAkSEAoIdXNlcm5hbWUYAiABKAkSEAoIcGFzc3dvcmQYAyABKAkiUgooQmFja0Nvbm5lY3RXb3JrZXJTZXJ2aWNlQWRkUHJveHlSZXNwb25zZRImCgVlcnJvchgBIAEoCzIXLmVycm1zZy52MS5FcnJvck1lc3NhZ2UiLAoqQmFja0Nvbm5lY3RXb3JrZXJTZXJ2aWNlUmVtb3ZlUHJveHlSZXF1ZXN0IlUKK0JhY2tDb25uZWN0V29ya2VyU2VydmljZVJlbW92ZVByb3h5UmVzcG9uc2USJgoFZXJyb3IYASABKAsyFy5lcnJtc2cudjEuRXJyb3JNZXNzYWdlIiwKKkJhY2tDb25uZWN0V29ya2VyU2VydmljZUhlYWx0aENoZWNrUmVxdWVzdCJVCitCYWNrQ29ubmVjdFdvcmtlclNlcnZpY2VIZWFsdGhDaGVja1Jlc3BvbnNlEiYKBWVycm9yGAEgASgLMhcuZXJybXNnLnYxLkVycm9yTWVzc2FnZTKGBAoYQmFja0Nvbm5lY3RXb3JrZXJTZXJ2aWNlEpsBCghBZGRQcm94eRJGLmJyb3dzZXJtYW5hZ2VyLmJhY2tjb25uZWN0LnYxLkJhY2tDb25uZWN0V29ya2VyU2VydmljZUFkZFByb3h5UmVxdWVzdBpHLmJyb3dzZXJtYW5hZ2VyLmJhY2tjb25uZWN0LnYxLkJhY2tDb25uZWN0V29ya2VyU2VydmljZUFkZFByb3h5UmVzcG9uc2USpAEKC1JlbW92ZVByb3h5EkkuYnJvd3Nlcm1hbmFnZXIuYmFja2Nvbm5lY3QudjEuQmFja0Nvbm5lY3RXb3JrZXJTZXJ2aWNlUmVtb3ZlUHJveHlSZXF1ZXN0GkouYnJvd3Nlcm1hbmFnZXIuYmFja2Nvbm5lY3QudjEuQmFja0Nvbm5lY3RXb3JrZXJTZXJ2aWNlUmVtb3ZlUHJveHlSZXNwb25zZRKkAQoLSGVhbHRoQ2hlY2sSSS5icm93c2VybWFuYWdlci5iYWNrY29ubmVjdC52MS5CYWNrQ29ubmVjdFdvcmtlclNlcnZpY2VIZWFsdGhDaGVja1JlcXVlc3QaSi5icm93c2VybWFuYWdlci5iYWNrY29ubmVjdC52MS5CYWNrQ29ubmVjdFdvcmtlclNlcnZpY2VIZWFsdGhDaGVja1Jlc3BvbnNlQlxaWmdpdC50bXByb3h5LWluZnJhLmNvbS9hbGdvL2FsZ29wcm94eS1wcm90by9nZW4vYnJvd3Nlcm1hbmFnZXIvYmFja2Nvbm5lY3QvdjE7YmFja2Nvbm5lY3R2MWIGcHJvdG8z", [file_errmsg_v1_errormsg]);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectWorkerServiceAddProxyRequest.
 * Use `create(BackConnectWorkerServiceAddProxyRequestSchema)` to create a new message.
 */
export const BackConnectWorkerServiceAddProxyRequestSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_worker, 0);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectWorkerServiceAddProxyResponse.
 * Use `create(BackConnectWorkerServiceAddProxyResponseSchema)` to create a new message.
 */
export const BackConnectWorkerServiceAddProxyResponseSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_worker, 1);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectWorkerServiceRemoveProxyRequest.
 * Use `create(BackConnectWorkerServiceRemoveProxyRequestSchema)` to create a new message.
 */
export const BackConnectWorkerServiceRemoveProxyRequestSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_worker, 2);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectWorkerServiceRemoveProxyResponse.
 * Use `create(BackConnectWorkerServiceRemoveProxyResponseSchema)` to create a new message.
 */
export const BackConnectWorkerServiceRemoveProxyResponseSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_worker, 3);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectWorkerServiceHealthCheckRequest.
 * Use `create(BackConnectWorkerServiceHealthCheckRequestSchema)` to create a new message.
 */
export const BackConnectWorkerServiceHealthCheckRequestSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_worker, 4);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectWorkerServiceHealthCheckResponse.
 * Use `create(BackConnectWorkerServiceHealthCheckResponseSchema)` to create a new message.
 */
export const BackConnectWorkerServiceHealthCheckResponseSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_worker, 5);

/**
 * @generated from service browsermanager.backconnect.v1.BackConnectWorkerService
 */
export const BackConnectWorkerService = /*@__PURE__*/
  serviceDesc(file_browsermanager_backconnect_v1_worker, 0);

