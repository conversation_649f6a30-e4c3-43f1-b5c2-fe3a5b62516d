// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file browsermanager/backconnect/v1/backoffice.proto (package browsermanager.backconnect.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";

/**
 * Describes the file browsermanager/backconnect/v1/backoffice.proto.
 */
export const file_browsermanager_backconnect_v1_backoffice = /*@__PURE__*/
  fileDesc("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", [file_errmsg_v1_errormsg, file_utils_v1_utils]);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectBackofficeServiceFetchWorkerRequest.
 * Use `create(BackConnectBackofficeServiceFetchWorkerRequestSchema)` to create a new message.
 */
export const BackConnectBackofficeServiceFetchWorkerRequestSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_backoffice, 0);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectBackofficeServiceFetchWorkerResponse.
 * Use `create(BackConnectBackofficeServiceFetchWorkerResponseSchema)` to create a new message.
 */
export const BackConnectBackofficeServiceFetchWorkerResponseSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_backoffice, 1);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectBackofficeServiceAddProxyRequest.
 * Use `create(BackConnectBackofficeServiceAddProxyRequestSchema)` to create a new message.
 */
export const BackConnectBackofficeServiceAddProxyRequestSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_backoffice, 2);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectBackofficeServiceAddProxyResponse.
 * Use `create(BackConnectBackofficeServiceAddProxyResponseSchema)` to create a new message.
 */
export const BackConnectBackofficeServiceAddProxyResponseSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_backoffice, 3);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectBackofficeServiceRemoveProxyRequest.
 * Use `create(BackConnectBackofficeServiceRemoveProxyRequestSchema)` to create a new message.
 */
export const BackConnectBackofficeServiceRemoveProxyRequestSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_backoffice, 4);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectBackofficeServiceRemoveProxyResponse.
 * Use `create(BackConnectBackofficeServiceRemoveProxyResponseSchema)` to create a new message.
 */
export const BackConnectBackofficeServiceRemoveProxyResponseSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_backoffice, 5);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectBackofficeWorker.
 * Use `create(BackConnectBackofficeWorkerSchema)` to create a new message.
 */
export const BackConnectBackofficeWorkerSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_backoffice, 6);

/**
 * @generated from service browsermanager.backconnect.v1.BackConnectBackofficeService
 */
export const BackConnectBackofficeService = /*@__PURE__*/
  serviceDesc(file_browsermanager_backconnect_v1_backoffice, 0);

