// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file browsermanager/backconnect/v1/controlplane.proto (package browsermanager.backconnect.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";

/**
 * Describes the file browsermanager/backconnect/v1/controlplane.proto.
 */
export const file_browsermanager_backconnect_v1_controlplane = /*@__PURE__*/
  fileDesc("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", [file_errmsg_v1_errormsg, file_utils_v1_utils]);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectControlPlaneServiceRegisterRequest.
 * Use `create(BackConnectControlPlaneServiceRegisterRequestSchema)` to create a new message.
 */
export const BackConnectControlPlaneServiceRegisterRequestSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_controlplane, 0);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectControlPlaneServiceRegisterResponse.
 * Use `create(BackConnectControlPlaneServiceRegisterResponseSchema)` to create a new message.
 */
export const BackConnectControlPlaneServiceRegisterResponseSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_controlplane, 1);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchWorkerRequest.
 * Use `create(BackConnectControlPlaneServiceFetchWorkerRequestSchema)` to create a new message.
 */
export const BackConnectControlPlaneServiceFetchWorkerRequestSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_controlplane, 2);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchWorkerResponse.
 * Use `create(BackConnectControlPlaneServiceFetchWorkerResponseSchema)` to create a new message.
 */
export const BackConnectControlPlaneServiceFetchWorkerResponseSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_controlplane, 3);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectControlPlaneServiceAddProxyRequest.
 * Use `create(BackConnectControlPlaneServiceAddProxyRequestSchema)` to create a new message.
 */
export const BackConnectControlPlaneServiceAddProxyRequestSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_controlplane, 4);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectControlPlaneServiceAddProxyResponse.
 * Use `create(BackConnectControlPlaneServiceAddProxyResponseSchema)` to create a new message.
 */
export const BackConnectControlPlaneServiceAddProxyResponseSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_controlplane, 5);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectControlPlaneServiceRemoveProxyRequest.
 * Use `create(BackConnectControlPlaneServiceRemoveProxyRequestSchema)` to create a new message.
 */
export const BackConnectControlPlaneServiceRemoveProxyRequestSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_controlplane, 6);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectControlPlaneServiceRemoveProxyResponse.
 * Use `create(BackConnectControlPlaneServiceRemoveProxyResponseSchema)` to create a new message.
 */
export const BackConnectControlPlaneServiceRemoveProxyResponseSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_controlplane, 7);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchBrowserRequest.
 * Use `create(BackConnectControlPlaneServiceFetchBrowserRequestSchema)` to create a new message.
 */
export const BackConnectControlPlaneServiceFetchBrowserRequestSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_controlplane, 8);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectControlPlaneServiceFetchBrowserResponse.
 * Use `create(BackConnectControlPlaneServiceFetchBrowserResponseSchema)` to create a new message.
 */
export const BackConnectControlPlaneServiceFetchBrowserResponseSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_controlplane, 9);

/**
 * Describes the message browsermanager.backconnect.v1.BackConnectControlPlaneWorker.
 * Use `create(BackConnectControlPlaneWorkerSchema)` to create a new message.
 */
export const BackConnectControlPlaneWorkerSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_backconnect_v1_controlplane, 10);

/**
 * @generated from service browsermanager.backconnect.v1.BackConnectControlPlaneService
 */
export const BackConnectControlPlaneService = /*@__PURE__*/
  serviceDesc(file_browsermanager_backconnect_v1_controlplane, 0);

