// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file browsermanager/browser/v1/customer.proto (package browsermanager.browser.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";

/**
 * Describes the file browsermanager/browser/v1/customer.proto.
 */
export const file_browsermanager_browser_v1_customer = /*@__PURE__*/
  fileDesc("Cihicm93c2VybWFuYWdlci9icm93c2VyL3YxL2N1c3RvbWVyLnByb3RvEhlicm93c2VybWFuYWdlci5icm93c2VyLnYxIisKKUJyb3dzZXJDdXN0b21lclNlcnZpY2VGZXRjaEJyb3dzZXJSZXF1ZXN0ImEKKkJyb3dzZXJDdXN0b21lclNlcnZpY2VGZXRjaEJyb3dzZXJSZXNwb25zZRImCgVlcnJvchgBIAEoCzIXLmVycm1zZy52MS5FcnJvck1lc3NhZ2USCwoDY2RwGAIgASgJMrYBChZCcm93c2VyQ3VzdG9tZXJTZXJ2aWNlEpsBCgxGZXRjaEJyb3dzZXISRC5icm93c2VybWFuYWdlci5icm93c2VyLnYxLkJyb3dzZXJDdXN0b21lclNlcnZpY2VGZXRjaEJyb3dzZXJSZXF1ZXN0GkUuYnJvd3Nlcm1hbmFnZXIuYnJvd3Nlci52MS5Ccm93c2VyQ3VzdG9tZXJTZXJ2aWNlRmV0Y2hCcm93c2VyUmVzcG9uc2VCVFpSZ2l0LnRtcHJveHktaW5mcmEuY29tL2FsZ28vYWxnb3Byb3h5LXByb3RvL2dlbi9icm93c2VybWFuYWdlci9icm93c2VyL3YxO2Jyb3dzZXJ2MWIGcHJvdG8z", [file_errmsg_v1_errormsg]);

/**
 * Describes the message browsermanager.browser.v1.BrowserCustomerServiceFetchBrowserRequest.
 * Use `create(BrowserCustomerServiceFetchBrowserRequestSchema)` to create a new message.
 */
export const BrowserCustomerServiceFetchBrowserRequestSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_browser_v1_customer, 0);

/**
 * Describes the message browsermanager.browser.v1.BrowserCustomerServiceFetchBrowserResponse.
 * Use `create(BrowserCustomerServiceFetchBrowserResponseSchema)` to create a new message.
 */
export const BrowserCustomerServiceFetchBrowserResponseSchema = /*@__PURE__*/
  messageDesc(file_browsermanager_browser_v1_customer, 1);

/**
 * @generated from service browsermanager.browser.v1.BrowserCustomerService
 */
export const BrowserCustomerService = /*@__PURE__*/
  serviceDesc(file_browsermanager_browser_v1_customer, 0);

