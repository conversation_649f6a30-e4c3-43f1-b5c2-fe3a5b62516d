// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file algoenum/v1/config_type.proto (package algoenum.v1, syntax proto3)
/* eslint-disable */

import { enumDesc, fileDesc, tsEnum } from "@bufbuild/protobuf/codegenv1";

/**
 * Describes the file algoenum/v1/config_type.proto.
 */
export const file_algoenum_v1_config_type = /*@__PURE__*/
  fileDesc("Ch1hbGdvZW51bS92MS9jb25maWdfdHlwZS5wcm90bxILYWxnb2VudW0udjEqZgoKQ29uZmlnVHlwZRIbChdDT05GSUdfVFlQRV9VTlNQRUNJRklFRBAAEiEKHUNPTkZJR19UWVBFX1RFTVBMQVRFX01BSUxfT1RQEAESGAoUQ09ORklHX1RZUEVfTUFJTF9BV1MQAkJHWkVnaXQudG1wcm94eS1pbmZyYS5jb20vYWxnby9hbGdvcHJveHktcHJvdG8vZ2VuL2FsZ29lbnVtL3YxO2FsZ29lbnVtdjFiBnByb3RvMw");

/**
 * Describes the enum algoenum.v1.ConfigType.
 */
export const ConfigTypeSchema = /*@__PURE__*/
  enumDesc(file_algoenum_v1_config_type, 0);

/**
 * @generated from enum algoenum.v1.ConfigType
 */
export const ConfigType = /*@__PURE__*/
  tsEnum(ConfigTypeSchema);

