// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file algoenum/v1/packet_type.proto (package algoenum.v1, syntax proto3)
/* eslint-disable */

import { enumDesc, fileDesc, tsEnum } from "@bufbuild/protobuf/codegenv1";

/**
 * Describes the file algoenum/v1/packet_type.proto.
 */
export const file_algoenum_v1_packet_type = /*@__PURE__*/
  fileDesc("Ch1hbGdvZW51bS92MS9wYWNrZXRfdHlwZS5wcm90bxILYWxnb2VudW0udjEqUwoKUGFja2V0VHlwZRIbChdQQUNLRVRfVFlQRV9VTlNQRUNJRklFRBAAEhMKD1BBQ0tFVF9UWVBFX1RDUBABEhMKD1BBQ0tFVF9UWVBFX1VEUBACQkdaRWdpdC50bXByb3h5LWluZnJhLmNvbS9hbGdvL2FsZ29wcm94eS1wcm90by9nZW4vYWxnb2VudW0vdjE7YWxnb2VudW12MWIGcHJvdG8z");

/**
 * Describes the enum algoenum.v1.PacketType.
 */
export const PacketTypeSchema = /*@__PURE__*/
  enumDesc(file_algoenum_v1_packet_type, 0);

/**
 * @generated from enum algoenum.v1.PacketType
 */
export const PacketType = /*@__PURE__*/
  tsEnum(PacketTypeSchema);

