// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file algoenum/v1/back_connect_manager_status.proto (package algoenum.v1, syntax proto3)
/* eslint-disable */

import { enumDesc, fileDesc, tsEnum } from "@bufbuild/protobuf/codegenv1";

/**
 * Describes the file algoenum/v1/back_connect_manager_status.proto.
 */
export const file_algoenum_v1_back_connect_manager_status = /*@__PURE__*/
  fileDesc("Ci1hbGdvZW51bS92MS9iYWNrX2Nvbm5lY3RfbWFuYWdlcl9zdGF0dXMucHJvdG8SC2FsZ29lbnVtLnYxKpgBChhCYWNrQ29ubmVjdE1hbmFnZXJTdGF0dXMSKwonQkFDS19DT05ORUNUX01BTkFHRVJfU1RBVFVTX1VOU1BFQ0lGSUVEEAASJgoiQkFDS19DT05ORUNUX01BTkFHRVJfU1RBVFVTX09OTElORRABEicKI0JBQ0tfQ09OTkVDVF9NQU5BR0VSX1NUQVRVU19PRkZMSU5FEAJCR1pFZ2l0LnRtcHJveHktaW5mcmEuY29tL2FsZ28vYWxnb3Byb3h5LXByb3RvL2dlbi9hbGdvZW51bS92MTthbGdvZW51bXYxYgZwcm90bzM");

/**
 * Describes the enum algoenum.v1.BackConnectManagerStatus.
 */
export const BackConnectManagerStatusSchema = /*@__PURE__*/
  enumDesc(file_algoenum_v1_back_connect_manager_status, 0);

/**
 * @generated from enum algoenum.v1.BackConnectManagerStatus
 */
export const BackConnectManagerStatus = /*@__PURE__*/
  tsEnum(BackConnectManagerStatusSchema);

