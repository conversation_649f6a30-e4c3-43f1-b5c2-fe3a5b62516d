// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file algoenum/v1/service.proto (package algoenum.v1, syntax proto3)
/* eslint-disable */

import { enumDesc, fileDesc, tsEnum } from "@bufbuild/protobuf/codegenv1";

/**
 * Describes the file algoenum/v1/service.proto.
 */
export const file_algoenum_v1_service = /*@__PURE__*/
  fileDesc("ChlhbGdvZW51bS92MS9zZXJ2aWNlLnByb3RvEgthbGdvZW51bS52MSpgCgtTZXJ2aWNlVHlwZRIcChhTRVJWSUNFX1RZUEVfVU5TUEVDSUZJRUQQABIbChdTRVJWSUNFX1RZUEVfQUxHT19QUk9YWRABEhYKElNFUlZJQ0VfVFlQRV9VMkRQThACQkdaRWdpdC50bXByb3h5LWluZnJhLmNvbS9hbGdvL2FsZ29wcm94eS1wcm90by9nZW4vYWxnb2VudW0vdjE7YWxnb2VudW12MWIGcHJvdG8z");

/**
 * Describes the enum algoenum.v1.ServiceType.
 */
export const ServiceTypeSchema = /*@__PURE__*/
  enumDesc(file_algoenum_v1_service, 0);

/**
 * @generated from enum algoenum.v1.ServiceType
 */
export const ServiceType = /*@__PURE__*/
  tsEnum(ServiceTypeSchema);

