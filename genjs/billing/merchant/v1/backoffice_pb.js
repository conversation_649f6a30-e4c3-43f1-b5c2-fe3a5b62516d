// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file billing/merchant/v1/backoffice.proto (package billing.merchant.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_algoenum_v1_currency } from "../../../algoenum/v1/currency_pb";
import { file_algoenum_v1_app_country } from "../../../algoenum/v1/app_country_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";

/**
 * Describes the file billing/merchant/v1/backoffice.proto.
 */
export const file_billing_merchant_v1_backoffice = /*@__PURE__*/
  fileDesc("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", [file_errmsg_v1_errormsg, file_algoenum_v1_currency, file_algoenum_v1_app_country, file_utils_v1_utils]);

/**
 * Describes the message billing.merchant.v1.BackofficeMerchantServiceCreateMerchantRequest.
 * Use `create(BackofficeMerchantServiceCreateMerchantRequestSchema)` to create a new message.
 */
export const BackofficeMerchantServiceCreateMerchantRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_merchant_v1_backoffice, 0);

/**
 * Describes the message billing.merchant.v1.BackofficeMerchantServiceCreateMerchantResponse.
 * Use `create(BackofficeMerchantServiceCreateMerchantResponseSchema)` to create a new message.
 */
export const BackofficeMerchantServiceCreateMerchantResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_merchant_v1_backoffice, 1);

/**
 * Describes the message billing.merchant.v1.BackofficeMerchantServiceFetchMerchantRequest.
 * Use `create(BackofficeMerchantServiceFetchMerchantRequestSchema)` to create a new message.
 */
export const BackofficeMerchantServiceFetchMerchantRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_merchant_v1_backoffice, 2);

/**
 * Describes the message billing.merchant.v1.BackofficeMerchantServiceFetchMerchantResponse.
 * Use `create(BackofficeMerchantServiceFetchMerchantResponseSchema)` to create a new message.
 */
export const BackofficeMerchantServiceFetchMerchantResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_merchant_v1_backoffice, 3);

/**
 * Describes the message billing.merchant.v1.BackofficeMerchantServiceUpdateMerchantRequest.
 * Use `create(BackofficeMerchantServiceUpdateMerchantRequestSchema)` to create a new message.
 */
export const BackofficeMerchantServiceUpdateMerchantRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_merchant_v1_backoffice, 4);

/**
 * Describes the message billing.merchant.v1.BackofficeMerchantServiceUpdateMerchantResponse.
 * Use `create(BackofficeMerchantServiceUpdateMerchantResponseSchema)` to create a new message.
 */
export const BackofficeMerchantServiceUpdateMerchantResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_merchant_v1_backoffice, 5);

/**
 * Describes the message billing.merchant.v1.MerchantBackofficeMerchantEntity.
 * Use `create(MerchantBackofficeMerchantEntitySchema)` to create a new message.
 */
export const MerchantBackofficeMerchantEntitySchema = /*@__PURE__*/
  messageDesc(file_billing_merchant_v1_backoffice, 6);

/**
 * @generated from service billing.merchant.v1.BackofficeMerchantService
 */
export const BackofficeMerchantService = /*@__PURE__*/
  serviceDesc(file_billing_merchant_v1_backoffice, 0);

