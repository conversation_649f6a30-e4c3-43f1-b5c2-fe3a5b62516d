// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file billing/planprice/v1/customer.proto (package billing.planprice.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_algoenum_v1_currency } from "../../../algoenum/v1/currency_pb";

/**
 * Describes the file billing/planprice/v1/customer.proto.
 */
export const file_billing_planprice_v1_customer = /*@__PURE__*/
  fileDesc("CiNiaWxsaW5nL3BsYW5wcmljZS92MS9jdXN0b21lci5wcm90bxIUYmlsbGluZy5wbGFucHJpY2UudjEiQAotQ3VzdG9tZXJQbGFuUHJpY2VTZXJ2aWNlRmV0Y2hQbGFuUHJpY2VSZXF1ZXN0Eg8KB2lkX3BsYW4YASABKAkirAEKLkN1c3RvbWVyUGxhblByaWNlU2VydmljZUZldGNoUGxhblByaWNlUmVzcG9uc2USJgoFZXJyb3IYASABKAsyFy5lcnJtc2cudjEuRXJyb3JNZXNzYWdlElIKC3BsYW5fcHJpY2VzGAIgAygLMj0uYmlsbGluZy5wbGFucHJpY2UudjEuQ3VzdG9tZXJQbGFuUHJpY2VTZXJ2aWNlUGxhblByaWNlRW50aXR5IrYBCidDdXN0b21lclBsYW5QcmljZVNlcnZpY2VQbGFuUHJpY2VFbnRpdHkSFQoNaWRfcGxhbl9wcmljZRgBIAEoCRIcChRiaWxsaW5nX2N5Y2xlX2luX3NlYxgCIAEoAxIeChZkYXRhX3RyYW5zZmVyX2luX2dieXRlGAMgASgBEg0KBXByaWNlGAUgASgBEicKCGN1cnJlbmN5GAYgASgOMhUuYWxnb2VudW0udjEuQ3VycmVuY3kyuAEKGEN1c3RvbWVyUGxhblByaWNlU2VydmljZRKbAQoORmV0Y2hQbGFuUHJpY2USQy5iaWxsaW5nLnBsYW5wcmljZS52MS5DdXN0b21lclBsYW5QcmljZVNlcnZpY2VGZXRjaFBsYW5QcmljZVJlcXVlc3QaRC5iaWxsaW5nLnBsYW5wcmljZS52MS5DdXN0b21lclBsYW5QcmljZVNlcnZpY2VGZXRjaFBsYW5QcmljZVJlc3BvbnNlQlFaT2dpdC50bXByb3h5LWluZnJhLmNvbS9hbGdvL2FsZ29wcm94eS1wcm90by9nZW4vYmlsbGluZy9wbGFucHJpY2UvdjE7cGxhbnByaWNldjFiBnByb3RvMw", [file_errmsg_v1_errormsg, file_algoenum_v1_currency]);

/**
 * Describes the message billing.planprice.v1.CustomerPlanPriceServiceFetchPlanPriceRequest.
 * Use `create(CustomerPlanPriceServiceFetchPlanPriceRequestSchema)` to create a new message.
 */
export const CustomerPlanPriceServiceFetchPlanPriceRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_planprice_v1_customer, 0);

/**
 * Describes the message billing.planprice.v1.CustomerPlanPriceServiceFetchPlanPriceResponse.
 * Use `create(CustomerPlanPriceServiceFetchPlanPriceResponseSchema)` to create a new message.
 */
export const CustomerPlanPriceServiceFetchPlanPriceResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_planprice_v1_customer, 1);

/**
 * Describes the message billing.planprice.v1.CustomerPlanPriceServicePlanPriceEntity.
 * Use `create(CustomerPlanPriceServicePlanPriceEntitySchema)` to create a new message.
 */
export const CustomerPlanPriceServicePlanPriceEntitySchema = /*@__PURE__*/
  messageDesc(file_billing_planprice_v1_customer, 2);

/**
 * @generated from service billing.planprice.v1.CustomerPlanPriceService
 */
export const CustomerPlanPriceService = /*@__PURE__*/
  serviceDesc(file_billing_planprice_v1_customer, 0);

