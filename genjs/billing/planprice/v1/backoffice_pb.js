// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file billing/planprice/v1/backoffice.proto (package billing.planprice.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_algoenum_v1_currency } from "../../../algoenum/v1/currency_pb";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";

/**
 * Describes the file billing/planprice/v1/backoffice.proto.
 */
export const file_billing_planprice_v1_backoffice = /*@__PURE__*/
  fileDesc("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", [file_algoenum_v1_currency, file_errmsg_v1_errormsg, file_utils_v1_utils]);

/**
 * Describes the message billing.planprice.v1.BackofficePlanPriceServiceFetchPlanPriceRequest.
 * Use `create(BackofficePlanPriceServiceFetchPlanPriceRequestSchema)` to create a new message.
 */
export const BackofficePlanPriceServiceFetchPlanPriceRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_planprice_v1_backoffice, 0);

/**
 * Describes the message billing.planprice.v1.BackofficePlanPriceServiceFetchPlanPriceResponse.
 * Use `create(BackofficePlanPriceServiceFetchPlanPriceResponseSchema)` to create a new message.
 */
export const BackofficePlanPriceServiceFetchPlanPriceResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_planprice_v1_backoffice, 1);

/**
 * Describes the message billing.planprice.v1.BackofficePlanPriceServicePlanPriceEntity.
 * Use `create(BackofficePlanPriceServicePlanPriceEntitySchema)` to create a new message.
 */
export const BackofficePlanPriceServicePlanPriceEntitySchema = /*@__PURE__*/
  messageDesc(file_billing_planprice_v1_backoffice, 2);

/**
 * Describes the message billing.planprice.v1.BackofficePlanPriceServiceUpdatePlanPriceRequest.
 * Use `create(BackofficePlanPriceServiceUpdatePlanPriceRequestSchema)` to create a new message.
 */
export const BackofficePlanPriceServiceUpdatePlanPriceRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_planprice_v1_backoffice, 3);

/**
 * Describes the message billing.planprice.v1.BackofficePlanPriceServiceUpdatePlanPriceResponse.
 * Use `create(BackofficePlanPriceServiceUpdatePlanPriceResponseSchema)` to create a new message.
 */
export const BackofficePlanPriceServiceUpdatePlanPriceResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_planprice_v1_backoffice, 4);

/**
 * Describes the message billing.planprice.v1.BackofficePlanPriceServiceCreatePlanPriceRequest.
 * Use `create(BackofficePlanPriceServiceCreatePlanPriceRequestSchema)` to create a new message.
 */
export const BackofficePlanPriceServiceCreatePlanPriceRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_planprice_v1_backoffice, 5);

/**
 * Describes the message billing.planprice.v1.BackofficePlanPriceServiceCreatePlanPriceResponse.
 * Use `create(BackofficePlanPriceServiceCreatePlanPriceResponseSchema)` to create a new message.
 */
export const BackofficePlanPriceServiceCreatePlanPriceResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_planprice_v1_backoffice, 6);

/**
 * @generated from service billing.planprice.v1.BackofficePlanPriceService
 */
export const BackofficePlanPriceService = /*@__PURE__*/
  serviceDesc(file_billing_planprice_v1_backoffice, 0);

