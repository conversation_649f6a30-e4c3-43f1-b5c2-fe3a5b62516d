// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file billing/order/v1/customer.proto (package billing.order.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";

/**
 * Describes the file billing/order/v1/customer.proto.
 */
export const file_billing_order_v1_customer = /*@__PURE__*/
  fileDesc("Ch9iaWxsaW5nL29yZGVyL3YxL2N1c3RvbWVyLnByb3RvEhBiaWxsaW5nLm9yZGVyLnYxInQKNUN1c3RvbWVyT3JkZXJTZXJ2aWNlQ2FsY3VsYXRlT3JkZXJTdWJzY3JpcHRpb25SZXF1ZXN0Eg8KB2lkX3BsYW4YASABKAkSFQoNaWRfcGxhbl9wcmljZRgCIAEoCRITCgtjb3Vwb25fY29kZRgDIAEoCSKlAQo2Q3VzdG9tZXJPcmRlclNlcnZpY2VDYWxjdWxhdGVPcmRlclN1YnNjcmlwdGlvblJlc3BvbnNlEhQKDG9yZGVyX2Ftb3VudBgBIAEoARIUCgxkZWJpdF9hbW91bnQYAiABKAESFwoPZGlzY291bnRfYW1vdW50GAMgASgBEiYKBWVycm9yGAQgASgLMhcuZXJybXNnLnYxLkVycm9yTWVzc2FnZSJrCixDdXN0b21lck9yZGVyU2VydmljZU9yZGVyU3Vic2NyaXB0aW9uUmVxdWVzdBIPCgdpZF9wbGFuGAEgASgJEhUKDWlkX3BsYW5fcHJpY2UYAiABKAkSEwoLY291cG9uX2NvZGUYAyABKAkiVwotQ3VzdG9tZXJPcmRlclNlcnZpY2VPcmRlclN1YnNjcmlwdGlvblJlc3BvbnNlEiYKBWVycm9yGAEgASgLMhcuZXJybXNnLnYxLkVycm9yTWVzc2FnZSJ0Ci1DdXN0b21lck9yZGVyU2VydmljZUV4dGVuZFN1YnNjcmlwdGlvblJlcXVlc3QSFwoPaWRfc3Vic2NyaXB0aW9uGAEgASgJEhUKDWlkX3BsYW5fcHJpY2UYAiABKAkSEwoLY291cG9uX2NvZGUYAyABKAkiWAouQ3VzdG9tZXJPcmRlclNlcnZpY2VFeHRlbmRTdWJzY3JpcHRpb25SZXNwb25zZRImCgVlcnJvchgBIAEoCzIXLmVycm1zZy52MS5FcnJvck1lc3NhZ2Uy+QMKFEN1c3RvbWVyT3JkZXJTZXJ2aWNlEq8BChpDYWxjdWxhdGVPcmRlclN1YnNjcmlwdGlvbhJHLmJpbGxpbmcub3JkZXIudjEuQ3VzdG9tZXJPcmRlclNlcnZpY2VDYWxjdWxhdGVPcmRlclN1YnNjcmlwdGlvblJlcXVlc3QaSC5iaWxsaW5nLm9yZGVyLnYxLkN1c3RvbWVyT3JkZXJTZXJ2aWNlQ2FsY3VsYXRlT3JkZXJTdWJzY3JpcHRpb25SZXNwb25zZRKUAQoRT3JkZXJTdWJzY3JpcHRpb24SPi5iaWxsaW5nLm9yZGVyLnYxLkN1c3RvbWVyT3JkZXJTZXJ2aWNlT3JkZXJTdWJzY3JpcHRpb25SZXF1ZXN0Gj8uYmlsbGluZy5vcmRlci52MS5DdXN0b21lck9yZGVyU2VydmljZU9yZGVyU3Vic2NyaXB0aW9uUmVzcG9uc2USlwEKEkV4dGVuZFN1YnNjcmlwdGlvbhI/LmJpbGxpbmcub3JkZXIudjEuQ3VzdG9tZXJPcmRlclNlcnZpY2VFeHRlbmRTdWJzY3JpcHRpb25SZXF1ZXN0GkAuYmlsbGluZy5vcmRlci52MS5DdXN0b21lck9yZGVyU2VydmljZUV4dGVuZFN1YnNjcmlwdGlvblJlc3BvbnNlQklaR2dpdC50bXByb3h5LWluZnJhLmNvbS9hbGdvL2FsZ29wcm94eS1wcm90by9nZW4vYmlsbGluZy9vcmRlci92MTtvcmRlcnYxYgZwcm90bzM", [file_errmsg_v1_errormsg]);

/**
 * Describes the message billing.order.v1.CustomerOrderServiceCalculateOrderSubscriptionRequest.
 * Use `create(CustomerOrderServiceCalculateOrderSubscriptionRequestSchema)` to create a new message.
 */
export const CustomerOrderServiceCalculateOrderSubscriptionRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_order_v1_customer, 0);

/**
 * Describes the message billing.order.v1.CustomerOrderServiceCalculateOrderSubscriptionResponse.
 * Use `create(CustomerOrderServiceCalculateOrderSubscriptionResponseSchema)` to create a new message.
 */
export const CustomerOrderServiceCalculateOrderSubscriptionResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_order_v1_customer, 1);

/**
 * Describes the message billing.order.v1.CustomerOrderServiceOrderSubscriptionRequest.
 * Use `create(CustomerOrderServiceOrderSubscriptionRequestSchema)` to create a new message.
 */
export const CustomerOrderServiceOrderSubscriptionRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_order_v1_customer, 2);

/**
 * Describes the message billing.order.v1.CustomerOrderServiceOrderSubscriptionResponse.
 * Use `create(CustomerOrderServiceOrderSubscriptionResponseSchema)` to create a new message.
 */
export const CustomerOrderServiceOrderSubscriptionResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_order_v1_customer, 3);

/**
 * Describes the message billing.order.v1.CustomerOrderServiceExtendSubscriptionRequest.
 * Use `create(CustomerOrderServiceExtendSubscriptionRequestSchema)` to create a new message.
 */
export const CustomerOrderServiceExtendSubscriptionRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_order_v1_customer, 4);

/**
 * Describes the message billing.order.v1.CustomerOrderServiceExtendSubscriptionResponse.
 * Use `create(CustomerOrderServiceExtendSubscriptionResponseSchema)` to create a new message.
 */
export const CustomerOrderServiceExtendSubscriptionResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_order_v1_customer, 5);

/**
 * @generated from service billing.order.v1.CustomerOrderService
 */
export const CustomerOrderService = /*@__PURE__*/
  serviceDesc(file_billing_order_v1_customer, 0);

