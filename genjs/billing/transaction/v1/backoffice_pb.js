// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file billing/transaction/v1/backoffice.proto (package billing.transaction.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_algoenum_v1_currency } from "../../../algoenum/v1/currency_pb";
import { file_algoenum_v1_payment_gateway_type } from "../../../algoenum/v1/payment_gateway_type_pb";
import { file_algoenum_v1_transaction_type } from "../../../algoenum/v1/transaction_type_pb";
import { file_algoenum_v1_transaction_status } from "../../../algoenum/v1/transaction_status_pb";
import { file_algoenum_v1_balance_type } from "../../../algoenum/v1/balance_type_pb";
import { file_algoenum_v1_order_type } from "../../../algoenum/v1/order_type_pb";

/**
 * Describes the file billing/transaction/v1/backoffice.proto.
 */
export const file_billing_transaction_v1_backoffice = /*@__PURE__*/
  fileDesc("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", [file_utils_v1_utils, file_errmsg_v1_errormsg, file_algoenum_v1_currency, file_algoenum_v1_payment_gateway_type, file_algoenum_v1_transaction_type, file_algoenum_v1_transaction_status, file_algoenum_v1_balance_type, file_algoenum_v1_order_type]);

/**
 * Describes the message billing.transaction.v1.BackofficeTransactionServiceAddCreditUserRequest.
 * Use `create(BackofficeTransactionServiceAddCreditUserRequestSchema)` to create a new message.
 */
export const BackofficeTransactionServiceAddCreditUserRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_backoffice, 0);

/**
 * Describes the message billing.transaction.v1.BackofficeTransactionServiceAddCreditUserResponse.
 * Use `create(BackofficeTransactionServiceAddCreditUserResponseSchema)` to create a new message.
 */
export const BackofficeTransactionServiceAddCreditUserResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_backoffice, 1);

/**
 * Describes the message billing.transaction.v1.BackofficeTransactionServiceFetchTransactionRequest.
 * Use `create(BackofficeTransactionServiceFetchTransactionRequestSchema)` to create a new message.
 */
export const BackofficeTransactionServiceFetchTransactionRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_backoffice, 2);

/**
 * Describes the message billing.transaction.v1.BackofficeTransactionServiceFetchTransactionUserBalanceTransaction.
 * Use `create(BackofficeTransactionServiceFetchTransactionUserBalanceTransactionSchema)` to create a new message.
 */
export const BackofficeTransactionServiceFetchTransactionUserBalanceTransactionSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_backoffice, 3);

/**
 * Describes the message billing.transaction.v1.BackofficeTransactionServiceFetchTransactionResponse.
 * Use `create(BackofficeTransactionServiceFetchTransactionResponseSchema)` to create a new message.
 */
export const BackofficeTransactionServiceFetchTransactionResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_backoffice, 4);

/**
 * Describes the message billing.transaction.v1.BackofficeTransactionServiceFetchTransaction.
 * Use `create(BackofficeTransactionServiceFetchTransactionSchema)` to create a new message.
 */
export const BackofficeTransactionServiceFetchTransactionSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_backoffice, 5);

/**
 * Describes the message billing.transaction.v1.BackofficeTransactionServiceFetchTransactionMerchant.
 * Use `create(BackofficeTransactionServiceFetchTransactionMerchantSchema)` to create a new message.
 */
export const BackofficeTransactionServiceFetchTransactionMerchantSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_backoffice, 6);

/**
 * Describes the message billing.transaction.v1.BackofficeTransactionServiceFetchTransactionCreditTransaction.
 * Use `create(BackofficeTransactionServiceFetchTransactionCreditTransactionSchema)` to create a new message.
 */
export const BackofficeTransactionServiceFetchTransactionCreditTransactionSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_backoffice, 7);

/**
 * Describes the message billing.transaction.v1.BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGateway.
 * Use `create(BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGatewaySchema)` to create a new message.
 */
export const BackofficeTransactionServiceFetchTransactionCreditTransactionPaymentGatewaySchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_backoffice, 8);

/**
 * Describes the message billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransaction.
 * Use `create(BackofficeTransactionServiceFetchTransactionDebitTransactionSchema)` to create a new message.
 */
export const BackofficeTransactionServiceFetchTransactionDebitTransactionSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_backoffice, 9);

/**
 * Describes the message billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscription.
 * Use `create(BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscriptionSchema)` to create a new message.
 */
export const BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSubscriptionSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_backoffice, 10);

/**
 * Describes the message billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransactionPlan.
 * Use `create(BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSchema)` to create a new message.
 */
export const BackofficeTransactionServiceFetchTransactionDebitTransactionPlanSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_backoffice, 11);

/**
 * Describes the message billing.transaction.v1.BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPrice.
 * Use `create(BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPriceSchema)` to create a new message.
 */
export const BackofficeTransactionServiceFetchTransactionDebitTransactionPlanPriceSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_backoffice, 12);

/**
 * Describes the message billing.transaction.v1.BackofficeTransactionServiceFetchTransactionUser.
 * Use `create(BackofficeTransactionServiceFetchTransactionUserSchema)` to create a new message.
 */
export const BackofficeTransactionServiceFetchTransactionUserSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_backoffice, 13);

/**
 * Describes the message billing.transaction.v1.BackofficeTransactionServiceFetchUserBalanceRequest.
 * Use `create(BackofficeTransactionServiceFetchUserBalanceRequestSchema)` to create a new message.
 */
export const BackofficeTransactionServiceFetchUserBalanceRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_backoffice, 14);

/**
 * Describes the message billing.transaction.v1.BackofficeTransactionServiceFetchUserBalanceResponse.
 * Use `create(BackofficeTransactionServiceFetchUserBalanceResponseSchema)` to create a new message.
 */
export const BackofficeTransactionServiceFetchUserBalanceResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_backoffice, 15);

/**
 * Describes the message billing.transaction.v1.BackofficeTransactionServiceUserBalance.
 * Use `create(BackofficeTransactionServiceUserBalanceSchema)` to create a new message.
 */
export const BackofficeTransactionServiceUserBalanceSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_backoffice, 16);

/**
 * Describes the message billing.transaction.v1.BackofficeTransactionServiceBalance.
 * Use `create(BackofficeTransactionServiceBalanceSchema)` to create a new message.
 */
export const BackofficeTransactionServiceBalanceSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_backoffice, 17);

/**
 * @generated from service billing.transaction.v1.BackofficeTransactionService
 */
export const BackofficeTransactionService = /*@__PURE__*/
  serviceDesc(file_billing_transaction_v1_backoffice, 0);

