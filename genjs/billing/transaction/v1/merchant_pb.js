// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file billing/transaction/v1/merchant.proto (package billing.transaction.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_algoenum_v1_currency } from "../../../algoenum/v1/currency_pb";
import { file_algoenum_v1_transaction_type } from "../../../algoenum/v1/transaction_type_pb";
import { file_algoenum_v1_transaction_status } from "../../../algoenum/v1/transaction_status_pb";
import { file_algoenum_v1_balance_type } from "../../../algoenum/v1/balance_type_pb";
import { file_algoenum_v1_order_type } from "../../../algoenum/v1/order_type_pb";
import { file_algoenum_v1_payment_gateway_type } from "../../../algoenum/v1/payment_gateway_type_pb";

/**
 * Describes the file billing/transaction/v1/merchant.proto.
 */
export const file_billing_transaction_v1_merchant = /*@__PURE__*/
  fileDesc("CiViaWxsaW5nL3RyYW5zYWN0aW9uL3YxL21lcmNoYW50LnByb3RvEhZiaWxsaW5nLnRyYW5zYWN0aW9uLnYxIqkCCjFNZXJjaGFudFRyYW5zYWN0aW9uU2VydmljZUZldGNoVHJhbnNhY3Rpb25SZXF1ZXN0Eg8KB2lkX3VzZXIYASABKAkSGgoSaWRfcGF5bWVudF9nYXRld2F5GAIgASgJEjYKEHRyYW5zYWN0aW9uX3R5cGUYAyABKA4yHC5hbGdvZW51bS52MS5UcmFuc2FjdGlvblR5cGUSOgoSdHJhbnNhY3Rpb25fc3RhdHVzGAQgASgOMh4uYWxnb2VudW0udjEuVHJhbnNhY3Rpb25TdGF0dXMSEQoJZnJvbV91bml4GAUgASgDEg8KB3RvX3VuaXgYBiABKAMSLwoKcGFnaW5hdGlvbhgHIAEoCzIbLnV0aWxzLnYxLlBhZ2luYXRpb25SZXF1ZXN0IsoBCkBNZXJjaGFudFRyYW5zYWN0aW9uU2VydmljZUZldGNoVHJhbnNhY3Rpb25Vc2VyQmFsYW5jZVRyYW5zYWN0aW9uEhcKD2lkX3VzZXJfYmFsYW5jZRgBIAEoCRIuCgxiYWxhbmNlX3R5cGUYAiABKA4yGC5hbGdvZW51bS52MS5CYWxhbmNlVHlwZRIOCgZhbW91bnQYAyABKAESFgoOYmFsYW5jZV9iZWZvcmUYBCABKAESFQoNYmFsYW5jZV9hZnRlchgFIAEoASLoAQoyTWVyY2hhbnRUcmFuc2FjdGlvblNlcnZpY2VGZXRjaFRyYW5zYWN0aW9uUmVzcG9uc2USWAoMdHJhbnNhY3Rpb25zGAEgAygLMkIuYmlsbGluZy50cmFuc2FjdGlvbi52MS5NZXJjaGFudFRyYW5zYWN0aW9uU2VydmljZUZldGNoVHJhbnNhY3Rpb24SMAoKcGFnaW5hdGlvbhgCIAEoCzIcLnV0aWxzLnYxLlBhZ2luYXRpb25SZXNwb25zZRImCgVlcnJvchgDIAEoCzIXLmVycm1zZy52MS5FcnJvck1lc3NhZ2UimwYKKk1lcmNoYW50VHJhbnNhY3Rpb25TZXJ2aWNlRmV0Y2hUcmFuc2FjdGlvbhIWCg5pZF90cmFuc2FjdGlvbhgBIAEoCRJcCghtZXJjaGFudBgCIAEoCzJKLmJpbGxpbmcudHJhbnNhY3Rpb24udjEuTWVyY2hhbnRUcmFuc2FjdGlvblNlcnZpY2VGZXRjaFRyYW5zYWN0aW9uTWVyY2hhbnQSVAoEdXNlchgDIAEoCzJGLmJpbGxpbmcudHJhbnNhY3Rpb24udjEuTWVyY2hhbnRUcmFuc2FjdGlvblNlcnZpY2VGZXRjaFRyYW5zYWN0aW9uVXNlchI2ChB0cmFuc2FjdGlvbl90eXBlGAQgASgOMhwuYWxnb2VudW0udjEuVHJhbnNhY3Rpb25UeXBlEjoKEnRyYW5zYWN0aW9uX3N0YXR1cxgFIAEoDjIeLmFsZ29lbnVtLnYxLlRyYW5zYWN0aW9uU3RhdHVzEnoKGHVzZXJfYmFsYW5jZV90cmFuc2FjdGlvbhgGIAMoCzJYLmJpbGxpbmcudHJhbnNhY3Rpb24udjEuTWVyY2hhbnRUcmFuc2FjdGlvblNlcnZpY2VGZXRjaFRyYW5zYWN0aW9uVXNlckJhbGFuY2VUcmFuc2FjdGlvbhJtChFkZWJpdF90cmFuc2FjdGlvbhgHIAEoCzJSLmJpbGxpbmcudHJhbnNhY3Rpb24udjEuTWVyY2hhbnRUcmFuc2FjdGlvblNlcnZpY2VGZXRjaFRyYW5zYWN0aW9uRGViaXRUcmFuc2FjdGlvbhJvChJjcmVkaXRfdHJhbnNhY3Rpb24YCCABKAsyUy5iaWxsaW5nLnRyYW5zYWN0aW9uLnYxLk1lcmNoYW50VHJhbnNhY3Rpb25TZXJ2aWNlRmV0Y2hUcmFuc2FjdGlvbkNyZWRpdFRyYW5zYWN0aW9uEhQKDHRvdGFsX2Ftb3VudBgJIAEoARInCghjdXJyZW5jeRgKIAEoDjIVLmFsZ29lbnVtLnYxLkN1cnJlbmN5EhIKCmNyZWF0ZWRfYXQYCyABKAMiagoyTWVyY2hhbnRUcmFuc2FjdGlvblNlcnZpY2VGZXRjaFRyYW5zYWN0aW9uTWVyY2hhbnQSEwoLaWRfbWVyY2hhbnQYASABKAkSDAoEbmFtZRgCIAEoCRIRCglpc19hY3RpdmUYAyABKAgi6AEKO01lcmNoYW50VHJhbnNhY3Rpb25TZXJ2aWNlRmV0Y2hUcmFuc2FjdGlvbkNyZWRpdFRyYW5zYWN0aW9uEnoKD3BheW1lbnRfZ2F0ZXdheRgBIAEoCzJhLmJpbGxpbmcudHJhbnNhY3Rpb24udjEuTWVyY2hhbnRUcmFuc2FjdGlvblNlcnZpY2VGZXRjaFRyYW5zYWN0aW9uQ3JlZGl0VHJhbnNhY3Rpb25QYXltZW50R2F0ZXdheRITCgtkZXNjcmlwdGlvbhgCIAEoCRIYChBoYXNoX3RyYW5zYWN0aW9uGAMgASgJIsMBCklNZXJjaGFudFRyYW5zYWN0aW9uU2VydmljZUZldGNoVHJhbnNhY3Rpb25DcmVkaXRUcmFuc2FjdGlvblBheW1lbnRHYXRld2F5EhoKEmlkX3BheW1lbnRfZ2F0ZXdheRgBIAEoCRI9ChRwYXltZW50X2dhdGV3YXlfdHlwZRgCIAEoDjIfLmFsZ29lbnVtLnYxLlBheW1lbnRHYXRld2F5VHlwZRIbChNhY2NvdW50X2hvbGRlcl9uYW1lGAMgASgJIowDCjpNZXJjaGFudFRyYW5zYWN0aW9uU2VydmljZUZldGNoVHJhbnNhY3Rpb25EZWJpdFRyYW5zYWN0aW9uEmQKBHBsYW4YASABKAsyVi5iaWxsaW5nLnRyYW5zYWN0aW9uLnYxLk1lcmNoYW50VHJhbnNhY3Rpb25TZXJ2aWNlRmV0Y2hUcmFuc2FjdGlvbkRlYml0VHJhbnNhY3Rpb25QbGFuEngKDHN1YnNjcmlwdGlvbhgCIAEoCzJiLmJpbGxpbmcudHJhbnNhY3Rpb24udjEuTWVyY2hhbnRUcmFuc2FjdGlvblNlcnZpY2VGZXRjaFRyYW5zYWN0aW9uRGViaXRUcmFuc2FjdGlvblBsYW5TdWJzY3JpcHRpb24SKgoKb3JkZXJfdHlwZRgDIAEoDjIWLmFsZ29lbnVtLnYxLk9yZGVyVHlwZRISCgp0b3RhbF9jb3N0GAQgASgBEhYKDnRvdGFsX3B1cmNoYXNlGAUgASgBEhYKDnRvdGFsX2Rpc2NvdW50GAYgASgBImUKSk1lcmNoYW50VHJhbnNhY3Rpb25TZXJ2aWNlRmV0Y2hUcmFuc2FjdGlvbkRlYml0VHJhbnNhY3Rpb25QbGFuU3Vic2NyaXB0aW9uEhcKD2lkX3N1YnNjcmlwdGlvbhgBIAEoCSLQAQo+TWVyY2hhbnRUcmFuc2FjdGlvblNlcnZpY2VGZXRjaFRyYW5zYWN0aW9uRGViaXRUcmFuc2FjdGlvblBsYW4SDwoHaWRfcGxhbhgBIAEoCRIMCgRuYW1lGAIgASgJEm8KCnBsYW5fcHJpY2UYAyABKAsyWy5iaWxsaW5nLnRyYW5zYWN0aW9uLnYxLk1lcmNoYW50VHJhbnNhY3Rpb25TZXJ2aWNlRmV0Y2hUcmFuc2FjdGlvbkRlYml0VHJhbnNhY3Rpb25QbGFuUHJpY2UiwwEKQ01lcmNoYW50VHJhbnNhY3Rpb25TZXJ2aWNlRmV0Y2hUcmFuc2FjdGlvbkRlYml0VHJhbnNhY3Rpb25QbGFuUHJpY2USFQoNaWRfcGxhbl9wcmljZRgBIAEoCRISCgpjb3N0X3ByaWNlGAIgASgBEhYKDnB1cmNoYXNlX3ByaWNlGAMgASgBEhsKE2RhdGFfdHJhbnNmZXJfaW5fZ2IYBCABKAESHAoUYmlsbGluZ19jeWNsZV9pbl9zZWMYBSABKAMiigEKLk1lcmNoYW50VHJhbnNhY3Rpb25TZXJ2aWNlRmV0Y2hUcmFuc2FjdGlvblVzZXISDwoHaWRfdXNlchgBIAEoCRINCgVlbWFpbBgCIAEoCRISCgpmaXJzdF9uYW1lGAMgASgJEhEKCWxhc3RfbmFtZRgEIAEoCRIRCglpc19hY3RpdmUYBSABKAgipQEKMU1lcmNoYW50VHJhbnNhY3Rpb25TZXJ2aWNlRmV0Y2hVc2VyQmFsYW5jZVJlcXVlc3QSDwoHaWRfdXNlchgBIAEoCRIuCgxiYWxhbmNlX3R5cGUYAiABKA4yGC5hbGdvZW51bS52MS5CYWxhbmNlVHlwZRIvCgpwYWdpbmF0aW9uGAMgASgLMhsudXRpbHMudjEuUGFnaW5hdGlvblJlcXVlc3Qi4QEKMk1lcmNoYW50VHJhbnNhY3Rpb25TZXJ2aWNlRmV0Y2hVc2VyQmFsYW5jZVJlc3BvbnNlElEKBXVzZXJzGAEgAygLMkIuYmlsbGluZy50cmFuc2FjdGlvbi52MS5NZXJjaGFudFRyYW5zYWN0aW9uU2VydmljZVVzZXJCYWxhbmNlVXNlcnMSJgoFZXJyb3IYAiABKAsyFy5lcnJtc2cudjEuRXJyb3JNZXNzYWdlEjAKCnBhZ2luYXRpb24YAyABKAsyHC51dGlscy52MS5QYWdpbmF0aW9uUmVzcG9uc2UisAEKKk1lcmNoYW50VHJhbnNhY3Rpb25TZXJ2aWNlVXNlckJhbGFuY2VVc2VycxIPCgdpZF91c2VyGAEgASgJEhUKDXRvdGFsX2JhbGFuY2UYAiABKAESWgoNdXNlcl9iYWxhbmNlcxgEIAMoCzJDLmJpbGxpbmcudHJhbnNhY3Rpb24udjEuTWVyY2hhbnRUcmFuc2FjdGlvblNlcnZpY2VVc2VyQmFsYW5jZU9mVXNlciLZAQorTWVyY2hhbnRUcmFuc2FjdGlvblNlcnZpY2VVc2VyQmFsYW5jZU9mVXNlchIXCg9pZF91c2VyX2JhbGFuY2UYASABKAkSLgoMYmFsYW5jZV90eXBlGAIgASgOMhguYWxnb2VudW0udjEuQmFsYW5jZVR5cGUSHwoXYmFsYW5jZV9jaGFyZ2VfcHJpb3JpdHkYAyABKAMSFwoPY3VycmVudF9iYWxhbmNlGAQgASgBEicKCGN1cnJlbmN5GAUgASgOMhUuYWxnb2VudW0udjEuQ3VycmVuY3ky9AIKGk1lcmNoYW50VHJhbnNhY3Rpb25TZXJ2aWNlEqkBChBGZXRjaFVzZXJCYWxhbmNlEkkuYmlsbGluZy50cmFuc2FjdGlvbi52MS5NZXJjaGFudFRyYW5zYWN0aW9uU2VydmljZUZldGNoVXNlckJhbGFuY2VSZXF1ZXN0GkouYmlsbGluZy50cmFuc2FjdGlvbi52MS5NZXJjaGFudFRyYW5zYWN0aW9uU2VydmljZUZldGNoVXNlckJhbGFuY2VSZXNwb25zZRKpAQoQRmV0Y2hUcmFuc2FjdGlvbhJJLmJpbGxpbmcudHJhbnNhY3Rpb24udjEuTWVyY2hhbnRUcmFuc2FjdGlvblNlcnZpY2VGZXRjaFRyYW5zYWN0aW9uUmVxdWVzdBpKLmJpbGxpbmcudHJhbnNhY3Rpb24udjEuTWVyY2hhbnRUcmFuc2FjdGlvblNlcnZpY2VGZXRjaFRyYW5zYWN0aW9uUmVzcG9uc2VCVVpTZ2l0LnRtcHJveHktaW5mcmEuY29tL2FsZ28vYWxnb3Byb3h5LXByb3RvL2dlbi9iaWxsaW5nL3RyYW5zYWN0aW9uL3YxO3RyYW5zYWN0aW9udjFiBnByb3RvMw", [file_utils_v1_utils, file_errmsg_v1_errormsg, file_algoenum_v1_currency, file_algoenum_v1_transaction_type, file_algoenum_v1_transaction_status, file_algoenum_v1_balance_type, file_algoenum_v1_order_type, file_algoenum_v1_payment_gateway_type]);

/**
 * Describes the message billing.transaction.v1.MerchantTransactionServiceFetchTransactionRequest.
 * Use `create(MerchantTransactionServiceFetchTransactionRequestSchema)` to create a new message.
 */
export const MerchantTransactionServiceFetchTransactionRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_merchant, 0);

/**
 * Describes the message billing.transaction.v1.MerchantTransactionServiceFetchTransactionUserBalanceTransaction.
 * Use `create(MerchantTransactionServiceFetchTransactionUserBalanceTransactionSchema)` to create a new message.
 */
export const MerchantTransactionServiceFetchTransactionUserBalanceTransactionSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_merchant, 1);

/**
 * Describes the message billing.transaction.v1.MerchantTransactionServiceFetchTransactionResponse.
 * Use `create(MerchantTransactionServiceFetchTransactionResponseSchema)` to create a new message.
 */
export const MerchantTransactionServiceFetchTransactionResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_merchant, 2);

/**
 * Describes the message billing.transaction.v1.MerchantTransactionServiceFetchTransaction.
 * Use `create(MerchantTransactionServiceFetchTransactionSchema)` to create a new message.
 */
export const MerchantTransactionServiceFetchTransactionSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_merchant, 3);

/**
 * Describes the message billing.transaction.v1.MerchantTransactionServiceFetchTransactionMerchant.
 * Use `create(MerchantTransactionServiceFetchTransactionMerchantSchema)` to create a new message.
 */
export const MerchantTransactionServiceFetchTransactionMerchantSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_merchant, 4);

/**
 * Describes the message billing.transaction.v1.MerchantTransactionServiceFetchTransactionCreditTransaction.
 * Use `create(MerchantTransactionServiceFetchTransactionCreditTransactionSchema)` to create a new message.
 */
export const MerchantTransactionServiceFetchTransactionCreditTransactionSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_merchant, 5);

/**
 * Describes the message billing.transaction.v1.MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGateway.
 * Use `create(MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGatewaySchema)` to create a new message.
 */
export const MerchantTransactionServiceFetchTransactionCreditTransactionPaymentGatewaySchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_merchant, 6);

/**
 * Describes the message billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransaction.
 * Use `create(MerchantTransactionServiceFetchTransactionDebitTransactionSchema)` to create a new message.
 */
export const MerchantTransactionServiceFetchTransactionDebitTransactionSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_merchant, 7);

/**
 * Describes the message billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscription.
 * Use `create(MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscriptionSchema)` to create a new message.
 */
export const MerchantTransactionServiceFetchTransactionDebitTransactionPlanSubscriptionSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_merchant, 8);

/**
 * Describes the message billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransactionPlan.
 * Use `create(MerchantTransactionServiceFetchTransactionDebitTransactionPlanSchema)` to create a new message.
 */
export const MerchantTransactionServiceFetchTransactionDebitTransactionPlanSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_merchant, 9);

/**
 * Describes the message billing.transaction.v1.MerchantTransactionServiceFetchTransactionDebitTransactionPlanPrice.
 * Use `create(MerchantTransactionServiceFetchTransactionDebitTransactionPlanPriceSchema)` to create a new message.
 */
export const MerchantTransactionServiceFetchTransactionDebitTransactionPlanPriceSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_merchant, 10);

/**
 * Describes the message billing.transaction.v1.MerchantTransactionServiceFetchTransactionUser.
 * Use `create(MerchantTransactionServiceFetchTransactionUserSchema)` to create a new message.
 */
export const MerchantTransactionServiceFetchTransactionUserSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_merchant, 11);

/**
 * Describes the message billing.transaction.v1.MerchantTransactionServiceFetchUserBalanceRequest.
 * Use `create(MerchantTransactionServiceFetchUserBalanceRequestSchema)` to create a new message.
 */
export const MerchantTransactionServiceFetchUserBalanceRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_merchant, 12);

/**
 * Describes the message billing.transaction.v1.MerchantTransactionServiceFetchUserBalanceResponse.
 * Use `create(MerchantTransactionServiceFetchUserBalanceResponseSchema)` to create a new message.
 */
export const MerchantTransactionServiceFetchUserBalanceResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_merchant, 13);

/**
 * Describes the message billing.transaction.v1.MerchantTransactionServiceUserBalanceUsers.
 * Use `create(MerchantTransactionServiceUserBalanceUsersSchema)` to create a new message.
 */
export const MerchantTransactionServiceUserBalanceUsersSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_merchant, 14);

/**
 * Describes the message billing.transaction.v1.MerchantTransactionServiceUserBalanceOfUser.
 * Use `create(MerchantTransactionServiceUserBalanceOfUserSchema)` to create a new message.
 */
export const MerchantTransactionServiceUserBalanceOfUserSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_merchant, 15);

/**
 * @generated from service billing.transaction.v1.MerchantTransactionService
 */
export const MerchantTransactionService = /*@__PURE__*/
  serviceDesc(file_billing_transaction_v1_merchant, 0);

