// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file billing/transaction/v1/customer.proto (package billing.transaction.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_algoenum_v1_currency } from "../../../algoenum/v1/currency_pb";
import { file_algoenum_v1_payment_gateway_type } from "../../../algoenum/v1/payment_gateway_type_pb";
import { file_algoenum_v1_balance_type } from "../../../algoenum/v1/balance_type_pb";
import { file_algoenum_v1_transaction_status } from "../../../algoenum/v1/transaction_status_pb";
import { file_algoenum_v1_transaction_type } from "../../../algoenum/v1/transaction_type_pb";
import { file_algoenum_v1_order_type } from "../../../algoenum/v1/order_type_pb";

/**
 * Describes the file billing/transaction/v1/customer.proto.
 */
export const file_billing_transaction_v1_customer = /*@__PURE__*/
  fileDesc("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", [file_utils_v1_utils, file_errmsg_v1_errormsg, file_algoenum_v1_currency, file_algoenum_v1_payment_gateway_type, file_algoenum_v1_balance_type, file_algoenum_v1_transaction_status, file_algoenum_v1_transaction_type, file_algoenum_v1_order_type]);

/**
 * Describes the message billing.transaction.v1.CustomerTransactionServiceFetchBalanceRequest.
 * Use `create(CustomerTransactionServiceFetchBalanceRequestSchema)` to create a new message.
 */
export const CustomerTransactionServiceFetchBalanceRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_customer, 0);

/**
 * Describes the message billing.transaction.v1.CustomerTransactionServiceFetchBalanceResponse.
 * Use `create(CustomerTransactionServiceFetchBalanceResponseSchema)` to create a new message.
 */
export const CustomerTransactionServiceFetchBalanceResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_customer, 1);

/**
 * Describes the message billing.transaction.v1.CustomerTransactionServiceBalance.
 * Use `create(CustomerTransactionServiceBalanceSchema)` to create a new message.
 */
export const CustomerTransactionServiceBalanceSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_customer, 2);

/**
 * Describes the message billing.transaction.v1.CustomerTransactionServiceFetchTransactionRequest.
 * Use `create(CustomerTransactionServiceFetchTransactionRequestSchema)` to create a new message.
 */
export const CustomerTransactionServiceFetchTransactionRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_customer, 3);

/**
 * Describes the message billing.transaction.v1.CustomerTransactionServiceFetchTransactionUserBalanceTransaction.
 * Use `create(CustomerTransactionServiceFetchTransactionUserBalanceTransactionSchema)` to create a new message.
 */
export const CustomerTransactionServiceFetchTransactionUserBalanceTransactionSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_customer, 4);

/**
 * Describes the message billing.transaction.v1.CustomerTransactionServiceFetchTransactionResponse.
 * Use `create(CustomerTransactionServiceFetchTransactionResponseSchema)` to create a new message.
 */
export const CustomerTransactionServiceFetchTransactionResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_customer, 5);

/**
 * Describes the message billing.transaction.v1.CustomerTransactionServiceFetchTransaction.
 * Use `create(CustomerTransactionServiceFetchTransactionSchema)` to create a new message.
 */
export const CustomerTransactionServiceFetchTransactionSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_customer, 6);

/**
 * Describes the message billing.transaction.v1.CustomerTransactionServiceFetchTransactionCreditTransaction.
 * Use `create(CustomerTransactionServiceFetchTransactionCreditTransactionSchema)` to create a new message.
 */
export const CustomerTransactionServiceFetchTransactionCreditTransactionSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_customer, 7);

/**
 * Describes the message billing.transaction.v1.CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGateway.
 * Use `create(CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGatewaySchema)` to create a new message.
 */
export const CustomerTransactionServiceFetchTransactionCreditTransactionPaymentGatewaySchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_customer, 8);

/**
 * Describes the message billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransaction.
 * Use `create(CustomerTransactionServiceFetchTransactionDebitTransactionSchema)` to create a new message.
 */
export const CustomerTransactionServiceFetchTransactionDebitTransactionSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_customer, 9);

/**
 * Describes the message billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscription.
 * Use `create(CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscriptionSchema)` to create a new message.
 */
export const CustomerTransactionServiceFetchTransactionDebitTransactionPlanSubscriptionSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_customer, 10);

/**
 * Describes the message billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransactionPlan.
 * Use `create(CustomerTransactionServiceFetchTransactionDebitTransactionPlanSchema)` to create a new message.
 */
export const CustomerTransactionServiceFetchTransactionDebitTransactionPlanSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_customer, 11);

/**
 * Describes the message billing.transaction.v1.CustomerTransactionServiceFetchTransactionDebitTransactionPlanPrice.
 * Use `create(CustomerTransactionServiceFetchTransactionDebitTransactionPlanPriceSchema)` to create a new message.
 */
export const CustomerTransactionServiceFetchTransactionDebitTransactionPlanPriceSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_customer, 12);

/**
 * @generated from service billing.transaction.v1.CustomerTransactionService
 */
export const CustomerTransactionService = /*@__PURE__*/
  serviceDesc(file_billing_transaction_v1_customer, 0);

