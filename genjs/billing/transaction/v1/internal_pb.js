// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file billing/transaction/v1/internal.proto (package billing.transaction.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";

/**
 * Describes the file billing/transaction/v1/internal.proto.
 */
export const file_billing_transaction_v1_internal = /*@__PURE__*/
  fileDesc("CiViaWxsaW5nL3RyYW5zYWN0aW9uL3YxL2ludGVybmFsLnByb3RvEhZiaWxsaW5nLnRyYW5zYWN0aW9uLnYxIlUKMkludGVybmFsVHJhbnNhY3Rpb25TZXJ2aWNlQ3JlYXRlVXNlckJhbGFuY2VSZXF1ZXN0Eg8KB2lkX3VzZXIYASABKAkSDgoGaWRfYXBwGAIgASgJIl0KM0ludGVybmFsVHJhbnNhY3Rpb25TZXJ2aWNlQ3JlYXRlVXNlckJhbGFuY2VSZXNwb25zZRImCgVlcnJvchgBIAEoCzIXLmVycm1zZy52MS5FcnJvck1lc3NhZ2UyywEKGkludGVybmFsVHJhbnNhY3Rpb25TZXJ2aWNlEqwBChFDcmVhdGVVc2VyQmFsYW5jZRJKLmJpbGxpbmcudHJhbnNhY3Rpb24udjEuSW50ZXJuYWxUcmFuc2FjdGlvblNlcnZpY2VDcmVhdGVVc2VyQmFsYW5jZVJlcXVlc3QaSy5iaWxsaW5nLnRyYW5zYWN0aW9uLnYxLkludGVybmFsVHJhbnNhY3Rpb25TZXJ2aWNlQ3JlYXRlVXNlckJhbGFuY2VSZXNwb25zZUJVWlNnaXQudG1wcm94eS1pbmZyYS5jb20vYWxnby9hbGdvcHJveHktcHJvdG8vZ2VuL2JpbGxpbmcvdHJhbnNhY3Rpb24vdjE7dHJhbnNhY3Rpb252MWIGcHJvdG8z", [file_errmsg_v1_errormsg]);

/**
 * Describes the message billing.transaction.v1.InternalTransactionServiceCreateUserBalanceRequest.
 * Use `create(InternalTransactionServiceCreateUserBalanceRequestSchema)` to create a new message.
 */
export const InternalTransactionServiceCreateUserBalanceRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_internal, 0);

/**
 * Describes the message billing.transaction.v1.InternalTransactionServiceCreateUserBalanceResponse.
 * Use `create(InternalTransactionServiceCreateUserBalanceResponseSchema)` to create a new message.
 */
export const InternalTransactionServiceCreateUserBalanceResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_transaction_v1_internal, 1);

/**
 * @generated from service billing.transaction.v1.InternalTransactionService
 */
export const InternalTransactionService = /*@__PURE__*/
  serviceDesc(file_billing_transaction_v1_internal, 0);

