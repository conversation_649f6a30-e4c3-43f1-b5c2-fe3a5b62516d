// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file billing/payment/v1/merchant.proto (package billing.payment.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_algoenum_v1_payment_gateway_type } from "../../../algoenum/v1/payment_gateway_type_pb";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_algoenum_v1_currency } from "../../../algoenum/v1/currency_pb";
import { file_algoenum_v1_vn_bank } from "../../../algoenum/v1/vn_bank_pb";

/**
 * Describes the file billing/payment/v1/merchant.proto.
 */
export const file_billing_payment_v1_merchant = /*@__PURE__*/
  fileDesc("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", [file_algoenum_v1_payment_gateway_type, file_errmsg_v1_errormsg, file_utils_v1_utils, file_algoenum_v1_currency, file_algoenum_v1_vn_bank]);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceCreateDodoPaymentRequest.
 * Use `create(MerchantPaymentServiceCreateDodoPaymentRequestSchema)` to create a new message.
 */
export const MerchantPaymentServiceCreateDodoPaymentRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 0);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceCreateDodoPaymentResponse.
 * Use `create(MerchantPaymentServiceCreateDodoPaymentResponseSchema)` to create a new message.
 */
export const MerchantPaymentServiceCreateDodoPaymentResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 1);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceCreateSePayPaymentRequest.
 * Use `create(MerchantPaymentServiceCreateSePayPaymentRequestSchema)` to create a new message.
 */
export const MerchantPaymentServiceCreateSePayPaymentRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 2);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceCreateSePayPaymentResponse.
 * Use `create(MerchantPaymentServiceCreateSePayPaymentResponseSchema)` to create a new message.
 */
export const MerchantPaymentServiceCreateSePayPaymentResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 3);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceFetchSePayAmountTopUpRequest.
 * Use `create(MerchantPaymentServiceFetchSePayAmountTopUpRequestSchema)` to create a new message.
 */
export const MerchantPaymentServiceFetchSePayAmountTopUpRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 4);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceFetchSePayAmountTopUpResponse.
 * Use `create(MerchantPaymentServiceFetchSePayAmountTopUpResponseSchema)` to create a new message.
 */
export const MerchantPaymentServiceFetchSePayAmountTopUpResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 5);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceSePayAmountTopUp.
 * Use `create(MerchantPaymentServiceSePayAmountTopUpSchema)` to create a new message.
 */
export const MerchantPaymentServiceSePayAmountTopUpSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 6);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceFetchSePayPaymentGatewayDetailRequest.
 * Use `create(MerchantPaymentServiceFetchSePayPaymentGatewayDetailRequestSchema)` to create a new message.
 */
export const MerchantPaymentServiceFetchSePayPaymentGatewayDetailRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 7);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponse.
 * Use `create(MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponseSchema)` to create a new message.
 */
export const MerchantPaymentServiceFetchSePayPaymentGatewayDetailResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 8);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceSePayPaymentGatewayDetail.
 * Use `create(MerchantPaymentServiceSePayPaymentGatewayDetailSchema)` to create a new message.
 */
export const MerchantPaymentServiceSePayPaymentGatewayDetailSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 9);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceFetchDodoAmountTopUpRequest.
 * Use `create(MerchantPaymentServiceFetchDodoAmountTopUpRequestSchema)` to create a new message.
 */
export const MerchantPaymentServiceFetchDodoAmountTopUpRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 10);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceFetchDodoAmountTopUpResponse.
 * Use `create(MerchantPaymentServiceFetchDodoAmountTopUpResponseSchema)` to create a new message.
 */
export const MerchantPaymentServiceFetchDodoAmountTopUpResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 11);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceDodoAmountTopUp.
 * Use `create(MerchantPaymentServiceDodoAmountTopUpSchema)` to create a new message.
 */
export const MerchantPaymentServiceDodoAmountTopUpSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 12);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceFetchAppotaAmountTopUpRequest.
 * Use `create(MerchantPaymentServiceFetchAppotaAmountTopUpRequestSchema)` to create a new message.
 */
export const MerchantPaymentServiceFetchAppotaAmountTopUpRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 13);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceFetchAppotaAmountTopUpResponse.
 * Use `create(MerchantPaymentServiceFetchAppotaAmountTopUpResponseSchema)` to create a new message.
 */
export const MerchantPaymentServiceFetchAppotaAmountTopUpResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 14);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceAppotaAmountTopUp.
 * Use `create(MerchantPaymentServiceAppotaAmountTopUpSchema)` to create a new message.
 */
export const MerchantPaymentServiceAppotaAmountTopUpSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 15);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceUpdatePaymentGatewayStateRequest.
 * Use `create(MerchantPaymentServiceUpdatePaymentGatewayStateRequestSchema)` to create a new message.
 */
export const MerchantPaymentServiceUpdatePaymentGatewayStateRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 16);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceUpdatePaymentGatewayStateResponse.
 * Use `create(MerchantPaymentServiceUpdatePaymentGatewayStateResponseSchema)` to create a new message.
 */
export const MerchantPaymentServiceUpdatePaymentGatewayStateResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 17);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayTypeRequest.
 * Use `create(MerchantPaymentServiceFetchPaymentGatewayTypeRequestSchema)` to create a new message.
 */
export const MerchantPaymentServiceFetchPaymentGatewayTypeRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 18);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayTypeResponse.
 * Use `create(MerchantPaymentServiceFetchPaymentGatewayTypeResponseSchema)` to create a new message.
 */
export const MerchantPaymentServiceFetchPaymentGatewayTypeResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 19);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServicePaymentGatewayType.
 * Use `create(MerchantPaymentServicePaymentGatewayTypeSchema)` to create a new message.
 */
export const MerchantPaymentServicePaymentGatewayTypeSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 20);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayRequest.
 * Use `create(MerchantPaymentServiceFetchPaymentGatewayRequestSchema)` to create a new message.
 */
export const MerchantPaymentServiceFetchPaymentGatewayRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 21);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServiceFetchPaymentGatewayResponse.
 * Use `create(MerchantPaymentServiceFetchPaymentGatewayResponseSchema)` to create a new message.
 */
export const MerchantPaymentServiceFetchPaymentGatewayResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 22);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServicePaymentGateway.
 * Use `create(MerchantPaymentServicePaymentGatewaySchema)` to create a new message.
 */
export const MerchantPaymentServicePaymentGatewaySchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 23);

/**
 * Describes the message billing.payment.v1.MerchantPaymentServicePaymentGatewayPaymentGatewayType.
 * Use `create(MerchantPaymentServicePaymentGatewayPaymentGatewayTypeSchema)` to create a new message.
 */
export const MerchantPaymentServicePaymentGatewayPaymentGatewayTypeSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_merchant, 24);

/**
 * billing.payment.v1.MerchantPaymentService
 *
 *  rpc FetchPaymentGatewayType(MerchantPaymentServiceFetchPaymentGatewayTypeRequest) returns (MerchantPaymentServiceFetchPaymentGatewayTypeResponse);
 *
 * @generated from service billing.payment.v1.MerchantPaymentService
 */
export const MerchantPaymentService = /*@__PURE__*/
  serviceDesc(file_billing_payment_v1_merchant, 0);

