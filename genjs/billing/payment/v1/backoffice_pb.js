// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file billing/payment/v1/backoffice.proto (package billing.payment.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_algoenum_v1_currency } from "../../../algoenum/v1/currency_pb";
import { file_algoenum_v1_payment_gateway_type } from "../../../algoenum/v1/payment_gateway_type_pb";
import { file_algoenum_v1_vn_bank } from "../../../algoenum/v1/vn_bank_pb";

/**
 * Describes the file billing/payment/v1/backoffice.proto.
 */
export const file_billing_payment_v1_backoffice = /*@__PURE__*/
  fileDesc("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", [file_utils_v1_utils, file_errmsg_v1_errormsg, file_algoenum_v1_currency, file_algoenum_v1_payment_gateway_type, file_algoenum_v1_vn_bank]);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceFetchSePayAmountTopUpRequest.
 * Use `create(BackofficePaymentServiceFetchSePayAmountTopUpRequestSchema)` to create a new message.
 */
export const BackofficePaymentServiceFetchSePayAmountTopUpRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 0);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceFetchSePayAmountTopUpResponse.
 * Use `create(BackofficePaymentServiceFetchSePayAmountTopUpResponseSchema)` to create a new message.
 */
export const BackofficePaymentServiceFetchSePayAmountTopUpResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 1);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceSePayAmountTopUp.
 * Use `create(BackofficePaymentServiceSePayAmountTopUpSchema)` to create a new message.
 */
export const BackofficePaymentServiceSePayAmountTopUpSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 2);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceFetchDodoAmountTopUpRequest.
 * Use `create(BackofficePaymentServiceFetchDodoAmountTopUpRequestSchema)` to create a new message.
 */
export const BackofficePaymentServiceFetchDodoAmountTopUpRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 3);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceFetchDodoAmountTopUpResponse.
 * Use `create(BackofficePaymentServiceFetchDodoAmountTopUpResponseSchema)` to create a new message.
 */
export const BackofficePaymentServiceFetchDodoAmountTopUpResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 4);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceDodoAmountTopUp.
 * Use `create(BackofficePaymentServiceDodoAmountTopUpSchema)` to create a new message.
 */
export const BackofficePaymentServiceDodoAmountTopUpSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 5);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceFetchAppotaAmountTopUpRequest.
 * Use `create(BackofficePaymentServiceFetchAppotaAmountTopUpRequestSchema)` to create a new message.
 */
export const BackofficePaymentServiceFetchAppotaAmountTopUpRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 6);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceFetchAppotaAmountTopUpResponse.
 * Use `create(BackofficePaymentServiceFetchAppotaAmountTopUpResponseSchema)` to create a new message.
 */
export const BackofficePaymentServiceFetchAppotaAmountTopUpResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 7);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceAppotaAmountTopUp.
 * Use `create(BackofficePaymentServiceAppotaAmountTopUpSchema)` to create a new message.
 */
export const BackofficePaymentServiceAppotaAmountTopUpSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 8);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceCreateSePayPaymentGatewayRequest.
 * Use `create(BackofficePaymentServiceCreateSePayPaymentGatewayRequestSchema)` to create a new message.
 */
export const BackofficePaymentServiceCreateSePayPaymentGatewayRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 9);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceCreateSePayPaymentGatewayResponse.
 * Use `create(BackofficePaymentServiceCreateSePayPaymentGatewayResponseSchema)` to create a new message.
 */
export const BackofficePaymentServiceCreateSePayPaymentGatewayResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 10);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceCreateDodoPaymentGatewayRequest.
 * Use `create(BackofficePaymentServiceCreateDodoPaymentGatewayRequestSchema)` to create a new message.
 */
export const BackofficePaymentServiceCreateDodoPaymentGatewayRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 11);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceCreateDodoPaymentGatewayResponse.
 * Use `create(BackofficePaymentServiceCreateDodoPaymentGatewayResponseSchema)` to create a new message.
 */
export const BackofficePaymentServiceCreateDodoPaymentGatewayResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 12);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceCreateAppotaPaymentGatewayRequest.
 * Use `create(BackofficePaymentServiceCreateAppotaPaymentGatewayRequestSchema)` to create a new message.
 */
export const BackofficePaymentServiceCreateAppotaPaymentGatewayRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 13);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceCreateAppotaPaymentGatewayResponse.
 * Use `create(BackofficePaymentServiceCreateAppotaPaymentGatewayResponseSchema)` to create a new message.
 */
export const BackofficePaymentServiceCreateAppotaPaymentGatewayResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 14);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceCreateSePayAmountTopUpRequest.
 * Use `create(BackofficePaymentServiceCreateSePayAmountTopUpRequestSchema)` to create a new message.
 */
export const BackofficePaymentServiceCreateSePayAmountTopUpRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 15);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceCreateSePayAmountTopUpResponse.
 * Use `create(BackofficePaymentServiceCreateSePayAmountTopUpResponseSchema)` to create a new message.
 */
export const BackofficePaymentServiceCreateSePayAmountTopUpResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 16);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceCreateAppotaAmountTopUpRequest.
 * Use `create(BackofficePaymentServiceCreateAppotaAmountTopUpRequestSchema)` to create a new message.
 */
export const BackofficePaymentServiceCreateAppotaAmountTopUpRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 17);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceCreateAppotaAmountTopUpResponse.
 * Use `create(BackofficePaymentServiceCreateAppotaAmountTopUpResponseSchema)` to create a new message.
 */
export const BackofficePaymentServiceCreateAppotaAmountTopUpResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 18);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceCreateDodoAmountTopUpRequest.
 * Use `create(BackofficePaymentServiceCreateDodoAmountTopUpRequestSchema)` to create a new message.
 */
export const BackofficePaymentServiceCreateDodoAmountTopUpRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 19);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceCreateDodoAmountTopUpResponse.
 * Use `create(BackofficePaymentServiceCreateDodoAmountTopUpResponseSchema)` to create a new message.
 */
export const BackofficePaymentServiceCreateDodoAmountTopUpResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 20);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequest.
 * Use `create(BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequestSchema)` to create a new message.
 */
export const BackofficePaymentServiceFetchSePayPaymentGatewayDetailRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 21);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponse.
 * Use `create(BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponseSchema)` to create a new message.
 */
export const BackofficePaymentServiceFetchSePayPaymentGatewayDetailResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 22);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceSePayPaymentGatewayDetail.
 * Use `create(BackofficePaymentServiceSePayPaymentGatewayDetailSchema)` to create a new message.
 */
export const BackofficePaymentServiceSePayPaymentGatewayDetailSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 23);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayTypeRequest.
 * Use `create(BackofficePaymentServiceFetchPaymentGatewayTypeRequestSchema)` to create a new message.
 */
export const BackofficePaymentServiceFetchPaymentGatewayTypeRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 24);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayTypeResponse.
 * Use `create(BackofficePaymentServiceFetchPaymentGatewayTypeResponseSchema)` to create a new message.
 */
export const BackofficePaymentServiceFetchPaymentGatewayTypeResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 25);

/**
 * Describes the message billing.payment.v1.BackofficePaymentGatewayType.
 * Use `create(BackofficePaymentGatewayTypeSchema)` to create a new message.
 */
export const BackofficePaymentGatewayTypeSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 26);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayStateRequest.
 * Use `create(BackofficePaymentServiceUpdatePaymentGatewayStateRequestSchema)` to create a new message.
 */
export const BackofficePaymentServiceUpdatePaymentGatewayStateRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 27);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayStateResponse.
 * Use `create(BackofficePaymentServiceUpdatePaymentGatewayStateResponseSchema)` to create a new message.
 */
export const BackofficePaymentServiceUpdatePaymentGatewayStateResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 28);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayTypeRequest.
 * Use `create(BackofficePaymentServiceUpdatePaymentGatewayTypeRequestSchema)` to create a new message.
 */
export const BackofficePaymentServiceUpdatePaymentGatewayTypeRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 29);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceUpdatePaymentGatewayTypeResponse.
 * Use `create(BackofficePaymentServiceUpdatePaymentGatewayTypeResponseSchema)` to create a new message.
 */
export const BackofficePaymentServiceUpdatePaymentGatewayTypeResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 30);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServicePaymentGatewayMerchant.
 * Use `create(BackofficePaymentServicePaymentGatewayMerchantSchema)` to create a new message.
 */
export const BackofficePaymentServicePaymentGatewayMerchantSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 31);

/**
 * Describes the message billing.payment.v1.BackofficePaymentGateway.
 * Use `create(BackofficePaymentGatewaySchema)` to create a new message.
 */
export const BackofficePaymentGatewaySchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 32);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayRequest.
 * Use `create(BackofficePaymentServiceFetchPaymentGatewayRequestSchema)` to create a new message.
 */
export const BackofficePaymentServiceFetchPaymentGatewayRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 33);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceFetchPaymentGatewayResponse.
 * Use `create(BackofficePaymentServiceFetchPaymentGatewayResponseSchema)` to create a new message.
 */
export const BackofficePaymentServiceFetchPaymentGatewayResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 34);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequest.
 * Use `create(BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequestSchema)` to create a new message.
 */
export const BackofficePaymentServiceFetchAppotaPaymentGatewayDetailRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 35);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponse.
 * Use `create(BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponseSchema)` to create a new message.
 */
export const BackofficePaymentServiceFetchAppotaPaymentGatewayDetailResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 36);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceAppotaPaymentGatewayDetail.
 * Use `create(BackofficePaymentServiceAppotaPaymentGatewayDetailSchema)` to create a new message.
 */
export const BackofficePaymentServiceAppotaPaymentGatewayDetailSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 37);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequest.
 * Use `create(BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequestSchema)` to create a new message.
 */
export const BackofficePaymentServiceFetchDodoPaymentGatewayDetailRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 38);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponse.
 * Use `create(BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponseSchema)` to create a new message.
 */
export const BackofficePaymentServiceFetchDodoPaymentGatewayDetailResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 39);

/**
 * Describes the message billing.payment.v1.BackofficePaymentServiceDodoPaymentGatewayDetail.
 * Use `create(BackofficePaymentServiceDodoPaymentGatewayDetailSchema)` to create a new message.
 */
export const BackofficePaymentServiceDodoPaymentGatewayDetailSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_backoffice, 40);

/**
 * billing.payment.v1.BackofficePaymentService
 *
 * @generated from service billing.payment.v1.BackofficePaymentService
 */
export const BackofficePaymentService = /*@__PURE__*/
  serviceDesc(file_billing_payment_v1_backoffice, 0);

