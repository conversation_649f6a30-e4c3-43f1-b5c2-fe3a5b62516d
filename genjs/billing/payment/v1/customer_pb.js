// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file billing/payment/v1/customer.proto (package billing.payment.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import { file_errmsg_v1_errormsg } from "../../../errmsg/v1/errormsg_pb";
import { file_algoenum_v1_payment_gateway_type } from "../../../algoenum/v1/payment_gateway_type_pb";
import { file_algoenum_v1_vn_bank } from "../../../algoenum/v1/vn_bank_pb";
import { file_utils_v1_utils } from "../../../utils/v1/utils_pb";
import { file_algoenum_v1_currency } from "../../../algoenum/v1/currency_pb";

/**
 * Describes the file billing/payment/v1/customer.proto.
 */
export const file_billing_payment_v1_customer = /*@__PURE__*/
  fileDesc("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", [file_errmsg_v1_errormsg, file_algoenum_v1_payment_gateway_type, file_algoenum_v1_vn_bank, file_utils_v1_utils, file_algoenum_v1_currency]);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServiceFetchSePayAmountTopUpRequest.
 * Use `create(CustomerPaymentServiceFetchSePayAmountTopUpRequestSchema)` to create a new message.
 */
export const CustomerPaymentServiceFetchSePayAmountTopUpRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 0);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServiceFetchSePayAmountTopUpResponse.
 * Use `create(CustomerPaymentServiceFetchSePayAmountTopUpResponseSchema)` to create a new message.
 */
export const CustomerPaymentServiceFetchSePayAmountTopUpResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 1);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServiceSepayAmountTopUp.
 * Use `create(CustomerPaymentServiceSepayAmountTopUpSchema)` to create a new message.
 */
export const CustomerPaymentServiceSepayAmountTopUpSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 2);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServiceCreateSePayPaymentRequest.
 * Use `create(CustomerPaymentServiceCreateSePayPaymentRequestSchema)` to create a new message.
 */
export const CustomerPaymentServiceCreateSePayPaymentRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 3);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServiceCreateSePayPaymentResponse.
 * Use `create(CustomerPaymentServiceCreateSePayPaymentResponseSchema)` to create a new message.
 */
export const CustomerPaymentServiceCreateSePayPaymentResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 4);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServiceCreateAppotaPaymentRequest.
 * Use `create(CustomerPaymentServiceCreateAppotaPaymentRequestSchema)` to create a new message.
 */
export const CustomerPaymentServiceCreateAppotaPaymentRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 5);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServiceCreateAppotaPaymentResponse.
 * Use `create(CustomerPaymentServiceCreateAppotaPaymentResponseSchema)` to create a new message.
 */
export const CustomerPaymentServiceCreateAppotaPaymentResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 6);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServiceCreateDodoPaymentRequest.
 * Use `create(CustomerPaymentServiceCreateDodoPaymentRequestSchema)` to create a new message.
 */
export const CustomerPaymentServiceCreateDodoPaymentRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 7);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServiceCreateDodoPaymentResponse.
 * Use `create(CustomerPaymentServiceCreateDodoPaymentResponseSchema)` to create a new message.
 */
export const CustomerPaymentServiceCreateDodoPaymentResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 8);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServiceFetchDodoAmountTopUpRequest.
 * Use `create(CustomerPaymentServiceFetchDodoAmountTopUpRequestSchema)` to create a new message.
 */
export const CustomerPaymentServiceFetchDodoAmountTopUpRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 9);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServiceFetchDodoAmountTopUpResponse.
 * Use `create(CustomerPaymentServiceFetchDodoAmountTopUpResponseSchema)` to create a new message.
 */
export const CustomerPaymentServiceFetchDodoAmountTopUpResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 10);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServiceDodoAmountTopUp.
 * Use `create(CustomerPaymentServiceDodoAmountTopUpSchema)` to create a new message.
 */
export const CustomerPaymentServiceDodoAmountTopUpSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 11);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServiceFetchAppotaAmountTopUpRequest.
 * Use `create(CustomerPaymentServiceFetchAppotaAmountTopUpRequestSchema)` to create a new message.
 */
export const CustomerPaymentServiceFetchAppotaAmountTopUpRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 12);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServiceFetchAppotaAmountTopUpResponse.
 * Use `create(CustomerPaymentServiceFetchAppotaAmountTopUpResponseSchema)` to create a new message.
 */
export const CustomerPaymentServiceFetchAppotaAmountTopUpResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 13);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServiceAppotaAmountTopUp.
 * Use `create(CustomerPaymentServiceAppotaAmountTopUpSchema)` to create a new message.
 */
export const CustomerPaymentServiceAppotaAmountTopUpSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 14);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServiceFetchPaymentGatewayRequest.
 * Use `create(CustomerPaymentServiceFetchPaymentGatewayRequestSchema)` to create a new message.
 */
export const CustomerPaymentServiceFetchPaymentGatewayRequestSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 15);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServiceFetchPaymentGatewayResponse.
 * Use `create(CustomerPaymentServiceFetchPaymentGatewayResponseSchema)` to create a new message.
 */
export const CustomerPaymentServiceFetchPaymentGatewayResponseSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 16);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServicePaymentGateway.
 * Use `create(CustomerPaymentServicePaymentGatewaySchema)` to create a new message.
 */
export const CustomerPaymentServicePaymentGatewaySchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 17);

/**
 * Describes the message billing.payment.v1.CustomerPaymentServicePaymentGatewayPaymentGatewayType.
 * Use `create(CustomerPaymentServicePaymentGatewayPaymentGatewayTypeSchema)` to create a new message.
 */
export const CustomerPaymentServicePaymentGatewayPaymentGatewayTypeSchema = /*@__PURE__*/
  messageDesc(file_billing_payment_v1_customer, 18);

/**
 * billing.payment.v1.CustomerPaymentService
 *
 * @generated from service billing.payment.v1.CustomerPaymentService
 */
export const CustomerPaymentService = /*@__PURE__*/
  serviceDesc(file_billing_payment_v1_customer, 0);

