// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file hop/v1/hop.proto (package hop.v1, syntax proto3)
/* eslint-disable */

import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";

/**
 * Describes the file hop/v1/hop.proto.
 */
export const file_hop_v1_hop = /*@__PURE__*/
  fileDesc("ChBob3AvdjEvaG9wLnByb3RvEgZob3AudjEizAIKA0hvcBISCgppZF9zZXNzaW9uGAEgASgJEhIKCmxvY2FsX3BvcnQYAiABKAQSEAoIdXNlcm5hbWUYAyABKAkSEAoIcGFzc3dvcmQYBCABKAkSEAoIaXBfYWxsb3cYBSADKAkSFgoObmV4dF9ob3BfczVfaXAYBiABKAkSGAoQbmV4dF9ob3BfczVfcG9ydBgHIAEoBBIcChRuZXh0X2hvcF9zNV91c2VybmFtZRgIIAEoCRIcChRuZXh0X2hvcF9zNV9wYXNzd29yZBgJIAEoCRIRCglwdWJsaWNfaXAYCiABKAkSDAoEZG5zMRgLIAEoCRIMCgRkbnMyGAwgASgJEhAKCGlwX3Byb3h5GA0gASgJEhEKCWJhbmR3aWR0aBgOIAEoBBISCgpleHBpcmVkX2F0GA8gASgEEhEKCWlzX2FjdGl2ZRgQIAEoCEI9WjtnaXQudG1wcm94eS1pbmZyYS5jb20vYWxnby9hbGdvcHJveHktcHJvdG8vZ2VuL2hvcC92MTtob3B2MWIGcHJvdG8z");

/**
 * Describes the message hop.v1.Hop.
 * Use `create(HopSchema)` to create a new message.
 */
export const HopSchema = /*@__PURE__*/
  messageDesc(file_hop_v1_hop, 0);

