// @generated by protoc-gen-es v2.2.0 with parameter "target=js"
// @generated from file errmsg/v1/errormsg.proto (package errmsg.v1, syntax proto3)
/* eslint-disable */

import { enumDesc, fileDesc, messageDesc, tsEnum } from "@bufbuild/protobuf/codegenv1";
import { file_google_protobuf_any } from "@bufbuild/protobuf/wkt";

/**
 * Describes the file errmsg/v1/errormsg.proto.
 */
export const file_errmsg_v1_errormsg = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_any]);

/**
 * Describes the message errmsg.v1.ErrorMessage.
 * Use `create(ErrorMessageSchema)` to create a new message.
 */
export const ErrorMessageSchema = /*@__PURE__*/
  messageDesc(file_errmsg_v1_errormsg, 0);

/**
 * Describes the enum errmsg.v1.ErrorCode.
 */
export const ErrorCodeSchema = /*@__PURE__*/
  enumDesc(file_errmsg_v1_errormsg, 0);

/**
 * @generated from enum errmsg.v1.ErrorCode
 */
export const ErrorCode = /*@__PURE__*/
  tsEnum(ErrorCodeSchema);

