syntax = "proto3";
package browsermanager.backconnect.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/browsermanager/backconnect/v1;backconnectv1";

import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";

service BackConnectControlPlaneService {
  rpc Register(BackConnectControlPlaneServiceRegisterRequest) returns(BackConnectControlPlaneServiceRegisterResponse);
  rpc <PERSON>tch<PERSON>orker(BackConnectControlPlaneServiceFetchWorkerRequest) returns(BackConnectControlPlaneServiceFetchWorkerResponse);
  rpc AddProxy(BackConnectControlPlaneServiceAddProxyRequest) returns(BackConnectControlPlaneServiceAddProxyResponse);
  rpc RemoveProxy(BackConnectControlPlaneServiceRemoveProxyRequest) returns(BackConnectControlPlaneServiceRemoveProxyResponse);
  rpc FetchBrowser (BackConnectControlPlaneServiceFetchBrowserRequest) returns(BackConnectControlPlaneServiceFetchBrowserResponse);
}

message BackConnectControlPlaneServiceRegisterRequest {
  string name_worker = 1;
  string url_worker = 2;
}

message BackConnectControlPlaneServiceRegisterResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackConnectControlPlaneServiceFetchWorkerRequest {
  utils.v1.PaginationRequest pagination = 1 ;
}

message BackConnectControlPlaneServiceFetchWorkerResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated BackConnectControlPlaneWorker items = 3;
}

message BackConnectControlPlaneServiceAddProxyRequest {
  string id_worker = 1;
  string server = 2;
  string username = 3;
  string password = 4;
}

message BackConnectControlPlaneServiceAddProxyResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackConnectControlPlaneServiceRemoveProxyRequest {
  string id_worker = 1;
}

message BackConnectControlPlaneServiceRemoveProxyResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackConnectControlPlaneServiceFetchBrowserRequest {
}

message BackConnectControlPlaneServiceFetchBrowserResponse {
  errmsg.v1.ErrorMessage error = 1;
  string cdp = 2;
}

message BackConnectControlPlaneWorker {
  string id_worker = 1;
  string name_worker = 2;
  string url_worker = 3;
  bool is_healthy = 4;
  bool register_last_time = 5;
  string used_by = 6;
}
