syntax = "proto3";
package browsermanager.backconnect.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/browsermanager/backconnect/v1;backconnectv1";

import "errmsg/v1/errormsg.proto";
service BackConnectWorkerService {
  rpc AddProxy(BackConnectWorkerServiceAddProxyRequest) returns(BackConnectWorkerServiceAddProxyResponse);
  rpc RemoveProxy(BackConnectWorkerServiceRemoveProxyRequest) returns(BackConnectWorkerServiceRemoveProxyResponse);
  rpc HealthCheck(BackConnectWorkerServiceHealthCheckRequest) returns(BackConnectWorkerServiceHealthCheckResponse);
}

message BackConnectWorkerServiceAddProxyRequest {
  string server = 1;
  string username = 2;
  string password = 3;

}

message BackConnectWorkerServiceAddProxyResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackConnectWorkerServiceRemoveProxyRequest {
}

message BackConnectWorkerServiceRemoveProxyResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackConnectWorkerServiceHealthCheckRequest {
}

message BackConnectWorkerServiceHealthCheckResponse {
  errmsg.v1.ErrorMessage error = 1;
}