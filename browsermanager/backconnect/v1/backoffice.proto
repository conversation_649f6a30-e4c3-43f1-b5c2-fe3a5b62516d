syntax = "proto3";
package browsermanager.backconnect.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/browsermanager/backconnect/v1;backconnectv1";

import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";

service BackConnectBackofficeService {
  rpc FetchWorker(BackConnectBackofficeServiceFetchWorkerRequest) returns(BackConnectBackofficeServiceFetchWorkerResponse);
  rpc AddProxy(BackConnectBackofficeServiceAddProxyRequest) returns(BackConnectBackofficeServiceAddProxyResponse);
  rpc RemoveProxy(BackConnectBackofficeServiceRemoveProxyRequest) returns(BackConnectBackofficeServiceRemoveProxyResponse);
}

message BackConnectBackofficeServiceFetchWorkerRequest {
  utils.v1.PaginationRequest pagination = 1 ;
}

message BackConnectBackofficeServiceFetchWorkerResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated BackConnectBackofficeWorker items = 3;
}
    
message BackConnectBackofficeServiceAddProxyRequest {
  string id_worker = 1;
  string server = 2;
  string username = 3;
  string password = 4;
}

message BackConnectBackofficeServiceAddProxyResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackConnectBackofficeServiceRemoveProxyRequest {
  string id_worker = 1;
}

message BackConnectBackofficeServiceRemoveProxyResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message BackConnectBackofficeWorker {
  string id_worker = 1;
  string name_worker = 2;
  string url_worker = 3;
  bool is_healthy = 4;
  bool register_last_time = 5;
  string used_by = 6;
}
