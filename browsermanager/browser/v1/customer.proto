syntax = "proto3";
package browsermanager.browser.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/browsermanager/browser/v1;browserv1";

import "errmsg/v1/errormsg.proto";

service BrowserCustomerService {
  rpc FetchBrowser(BrowserCustomerServiceFetchBrowserRequest) returns(BrowserCustomerServiceFetchBrowserResponse);
}

message BrowserCustomerServiceFetchBrowserRequest {
}

message BrowserCustomerServiceFetchBrowserResponse {
  errmsg.v1.ErrorMessage error = 1;
  string cdp = 2;
}
