syntax = "proto3";
package hop.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/hop/v1;hopv1";


message Hop {
  string id_session = 1;
  uint64 local_port = 2;
  string username =3;
  string password =4;
  repeated string ip_allow = 5;

  string next_hop_s5_ip = 6;
  uint64 next_hop_s5_port = 7;
  string next_hop_s5_username= 8;
  string next_hop_s5_password = 9;
  string public_ip = 10;
  string dns1 = 11;
  string dns2 = 12;
  string ip_proxy = 13;
  uint64 bandwidth = 14;
  uint64 expired_at = 15;
  bool is_active = 16;
}

