syntax = "proto3";
package proxymanager.residential.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/residential/v1;residentialv1";

import "errmsg/v1/errormsg.proto";
import "hop/v1/hop.proto";

service WorkerResidentialService {
  rpc HealthCheck(WorkerResidentialServiceHealthCheckRequest) returns(WorkerResidentialServiceHealthCheckResponse);
  rpc ConfigRoute(WorkerResidentialServiceConfigRouteRequest) returns (WorkerResidentialServiceConfigRouteResponse);

}

message WorkerResidentialServiceHealthCheckRequest {}

message WorkerResidentialServiceHealthCheckResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message WorkerResidentialServiceConfigRouteRequest{
  string id_res_device = 1;
  string device_name = 2;
  bool  is_add = 3;
  hop.v1.Hop hop = 4;
}

message  WorkerResidentialServiceConfigRouteResponse {
  errmsg.v1.ErrorMessage error = 2;
}
