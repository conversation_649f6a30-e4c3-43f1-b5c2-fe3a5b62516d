syntax = "proto3";
package proxymanager.residential.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/residential/v1;residentialv1";

import "errmsg/v1/errormsg.proto";

service WorkerProcessorResidentialService {
  rpc HealthCheck(WorkerProcessorResidentialServiceHealthCheckRequest) returns(WorkerProcessorResidentialServiceHealthCheckResponse);
  rpc RestartNode(WorkerProcessorResidentialServiceRestartNodeRequest) returns (WorkerProcessorResidentialServiceRestartNodeResponse);
  rpc RestartPort(WorkerProcessorResidentialServiceRestartPortRequest) returns(WorkerProcessorResidentialServiceRestartPortResponse);
  rpc RestartDevice(WorkerProcessorResidentialServiceRestartDeviceRequest) returns (WorkerProcessorResidentialServiceRestartDeviceResponse);
}

message WorkerProcessorResidentialServiceHealthCheckRequest {}

message WorkerProcessorResidentialServiceHealthCheckResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message WorkerProcessorResidentialServiceRestartNodeRequest {}

message WorkerProcessorResidentialServiceRestartNodeResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message WorkerProcessorResidentialServiceRestartPortRequest {
  string id_res_port = 1;
  int64 network_port = 2;
}
message WorkerProcessorResidentialServiceRestartPortResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message WorkerProcessorResidentialServiceRestartDeviceRequest {
  string id_res_device = 1;
}
message WorkerProcessorResidentialServiceRestartDeviceResponse{
  errmsg.v1.ErrorMessage error = 1;
}
