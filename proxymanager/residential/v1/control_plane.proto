syntax = "proto3";
package proxymanager.residential.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/residential/v1;residentialv1";

import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";
import "algoenum/v1/res_node.proto";

service ControlPlaneResidentialService {
  rpc FetchPortConfig(ControlPlaneResidentialServiceFetchPortConfigRequest) returns (ControlPlaneResidentialServiceFetchPortConfigResponse);
  rpc UpdateNodeStatus(ControlPlaneResidentialServiceUpdateNodeStatusRequest) returns (ControlPlaneResidentialServiceUpdateNodeStatusResponse);
  rpc UpdateDeviceStatus(ControlPlaneResidentialServiceUpdateDeviceStatusRequest) returns (ControlPlaneResidentialServiceUpdateDeviceStatusResponse);
}

message ControlPlaneResidentialServiceFetchPortConfigRequest {
  string id_res_node = 1;
  int64 network_port = 2;
  utils.v1.PaginationRequest pagination = 3;
  utils.v1.State state = 4;
}
message ControlPlaneResidentialServiceFetchPortConfigResponse {
  repeated ControlPlaneResidentialServiceManagerPortConfig configs = 1;
  errmsg.v1.ErrorMessage error = 2;
  utils.v1.PaginationResponse pagination = 3;
}
message ControlPlaneResidentialServiceManagerPortConfig {
  string id_res_port = 1;
  uint64 network_port =2;
  string user_pppoe = 3;
  string pass_pppoe = 4;
  repeated ManagerDevice list_device = 5;
  bool is_active = 6;
}


message ManagerDevice {
  string id_res_device = 1;
  string device_name = 2;
}


message ControlPlaneResidentialServiceUpdateNodeStatusRequest{
  string id_res_node = 1;
  string ipv4 = 2;
  string ipv6 = 3;
  string ip_control = 4;
  uint64 port_control = 5;
  int64 speed_in_gb = 6;
  algoenum.v1.ResNodeStatus status  = 7;
}
message ControlPlaneResidentialServiceUpdateNodeStatusResponse{
  errmsg.v1.ErrorMessage error = 1;
}


message ControlPlaneResidentialServiceUpdateDeviceStatusRequest{
  string id_res_device = 1;
  string ipv4_prefix = 2;
  string ipv6_prefix = 3;

  string ip_proxy = 4;
  int64 s5_port = 5;
  string ip_control = 6;
  int64 port_control = 7;
  algoenum.v1.ResDeviceStatus device_status= 8;

}
message ControlPlaneResidentialServiceUpdateDeviceStatusResponse{
  errmsg.v1.ErrorMessage error = 1;
}

