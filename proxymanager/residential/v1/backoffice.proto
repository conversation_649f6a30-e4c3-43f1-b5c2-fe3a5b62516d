syntax = "proto3";
package proxymanager.residential.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/residential/v1;residentialv1";


import "utils/v1/utils.proto";
import "errmsg/v1/errormsg.proto";
import "proxymanager/telco/v1/backoffice.proto";
import "algoenum/v1/res_node.proto";
//import "proxymanager/lo"

service BackofficeResidentialService {
    rpc FetchResAccount(FetchResAccountRequest) returns  (FetchResAccountResponse);
    rpc CreateResAccount(CreateResAccountRequest) returns  (CreateResAccountResponse);
    rpc UpdateResAccount(UpdateResAccountRequest) returns  (UpdateResAccountResponse);

    rpc FetchResNode(FetchResNodeRequest) returns (FetchResNodeResponse);
    rpc CreateResNode(CreateResNodeRequest) returns (CreateResNodeResponse);
    rpc UpdateResNode(UpdateResNodeRequest) returns (UpdateResNodeResponse);

    rpc FetchResPort(FetchResPortRequest) returns (FetchResPortResponse);
    rpc UpdateResPort(UpdateResPortRequest) returns (UpdateResPortResponse);
    rpc FetchResDevice(FetchResDeviceRequest) returns (FetchResDeviceResponse);

    rpc RestartPort(BackofficeResidentialServiceRestartPortRequest) returns(BackofficeResidentialServiceRestartPortResponse);
}
message BackofficeResidentialServiceRestartPortRequest{
  string id_res_port = 1;
}


message BackofficeResidentialServiceRestartPortResponse{
  errmsg.v1.ErrorMessage error = 1;
}

message CreateResAccountRequest {
  string id_telco = 1;
  string id_location = 2;
  string username = 3;
  string password = 4;
}
message CreateResAccountResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message FetchResAccountRequest {
  string id_res_account = 1;
  string id_location = 2;
  string username_search = 3;
  FetchResAccountBinding binding = 4;
  utils.v1.State state = 5;
  utils.v1.PaginationRequest pagination = 6;
}
message FetchResAccountBinding {
  bool enable = 1;
  bool has_binding =2;
}

message FetchResAccountResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated  Account items = 3;
}

message UpdateResAccountRequest{
  string id_res_account = 1;
  string id_telco = 2;
  string id_location = 3;
  string username = 4;
  string password = 5;
  utils.v1.State state = 6;
}
message UpdateResAccountResponse{
  errmsg.v1.ErrorMessage error = 1;
}


message CreateResNodeRequest {
  string id_location = 1;
  string power_device_id = 2;
  string name  = 3;
}
message CreateResNodeResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message UpdateResNodeRequest {
  string id_res_node = 1;
  string id_location = 2;
  string name = 3;
  string power_device_id = 4;
  bool  is_change_secret_key = 5;
  utils.v1.State state = 6;
}

message UpdateResNodeResponse {
  errmsg.v1.ErrorMessage error = 1;
}
message FetchResNodeRequest {
  string id_res_node = 1;
  string power_device_id = 2;
  string name_search = 3;
  algoenum.v1.ResNodeStatus node_status = 4;
  algoenum.v1.ResNodePowerState power_state = 5;

  utils.v1.State state = 6;
  utils.v1.PaginationRequest pagination = 7;
}

message FetchResNodeResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated Node items = 3;
}

message FetchResPortRequest {
  string id_res_node = 1;
  string id_res_port = 2;
  uint64 network_port= 3;
  utils.v1.State state = 4;
  utils.v1.PaginationRequest pagination = 5;
}
message FetchResPortResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated BackofficePort items = 3;
}

message UpdateResPortRequest {
  string id_res_port = 1;
  string id_res_account = 2;
  uint64 total_device_active = 3;

  utils.v1.State state = 6;
}
message UpdateResPortResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message FetchResDeviceRequest {
  string id_res_port = 1;
  string name_search = 2;
  string ip_search = 3;
  utils.v1.State state = 4;
  utils.v1.PaginationRequest pagination = 5;
}

message FetchResDeviceResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated  BackofficeDevice items = 3;
}


message Account {
  string id_res_account = 1;
  proxymanager.telco.v1.BackofficeTelcoModel telco = 2;
//  controlplane.location.v1.BackofficeLocationModel location = 3;
  AccountNode node = 4;
  AccountPort port = 5;
  string username = 6;
  string password = 7;
  bool  is_active = 8;
  bool state = 9;
}

message AccountNode {
  string id_res_node = 1;
  string name = 2;
  bool  is_active = 3;
}
message AccountPort {
  string id_res_port = 1;
  uint64 network_port = 2;
  bool  is_active = 3;
}

message Node {
  string id_res_node = 1;
//  controlplane.location.v1.BackofficeLocationModel location = 2;
  string aes_secret_b64 = 3;
  string iv_secret_b64 = 4;
  string id_power_device = 5;
  string name = 6;
  string ipv4 = 7;
  string ipv6 = 8;
  int64 control_port = 9;
  algoenum.v1.ResNodeStatus status = 10;
  algoenum.v1.ResNodePowerState power_state = 11;
  uint64 speed_in_gb = 12;
  bool is_active = 13;
  bool state  = 14;
}

message BackofficePort {
  string id_res_port = 1;
  uint64 network_port = 2;
  uint64 total_device_active = 3;
  BackofficePortAccount account  = 4;
  bool is_active = 5;
  bool state = 6;
}

message BackofficePortAccount {
  string id_res_account = 1;
  BackofficePortAccountTelco telco = 2;
  string username = 3;
  bool is_active = 4;
}
message BackofficePortAccountTelco {
  string id_telco = 1;
  string name = 2;
  bool  is_active = 3;
}

message BackofficeDevice {
  string id_res_device = 1;
  BackofficeDeviceNode node = 2;
  BackofficeDevicePort port = 3;
  string name = 4;
  string ip_proxy = 5;
  uint64 port_proxy = 6;
  algoenum.v1.ResDeviceStatus device_status = 7;
  BackofficeDeviceProxy proxy = 8;
  bool  is_active = 9;
  bool  state = 10;
}

message BackofficeDeviceNode {
  string id_res_node = 1;
  string name = 2;
  BackofficeDeviceNodeLocation location =3;
  bool is_active = 4;
}
message BackofficeDeviceNodeLocation {
  string id_location = 1;
  string  name = 2;
  bool is_active = 3;
}

message BackofficeDevicePort {
  string id_res_port = 1;
  uint64 network_port = 2;
  BackofficeDevicePortAccount account  = 3;
  bool is_active = 4;
}
message BackofficeDevicePortAccount {
  string id_res_account =1;
  BackofficeDevicePortAccountTelco telco = 2;
  string username = 4;
  string password = 5;
  bool is_active = 6;
}

message BackofficeDevicePortAccountTelco {
  string id_telco = 1;
  string name = 2;
  string asn = 3;
  bool is_active = 4;
}

message BackofficeDeviceProxy {
  string ipv4 = 1;
  string ipv6 =2;
}