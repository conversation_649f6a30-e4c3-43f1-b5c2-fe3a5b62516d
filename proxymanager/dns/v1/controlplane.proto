syntax = "proto3";
package proxymanager.dns.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/dns/v1;dnsv1";

import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";

service DNSManagerService {
  rpc GetBlockedDomain(GetBlockedDomainRequest) returns (GetBlockedDomainResponse);
}

message GetBlockedDomainRequest {
  utils.v1.PaginationRequest pagination = 1;
}

message GetBlockedDomainResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationRequest pagination = 2;
  repeated string list_blocked_domain = 3;
}




