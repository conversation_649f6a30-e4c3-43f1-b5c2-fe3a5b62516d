syntax = "proto3";
package proxymanager.dns.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/dns/v1;dnsv1";

import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";
import "algoenum/v1/ip_type.proto";


service BackofficeDNSService {
  rpc FetchDNS(BackofficeDNSServiceFetchDNSRequest) returns (BackofficeDNSServiceFetchDNSResponse);
  rpc CreateDNS(BackofficeDNSServiceCreateDNSRequest) returns (BackofficeDNSServiceCreateDNSResponse);
  rpc UpdateDNS(BackofficeDNSServiceUpdateDNSRequest) returns (BackofficeDNSServiceUpdateDNSResponse);
}

message BackofficeDNSServiceFetchDNSRequest {
  string id_dns = 1;
  string name_search = 2;
  string ip_search = 3;
  algoenum.v1.IPType ip_type = 4;
  utils.v1.State state =  5;
  utils.v1.PaginationRequest pagination = 6;
}

message BackofficeDNSServiceFetchDNSResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated BackofficeDNS list_dns = 3;
}

message BackofficeDNS {
  string id_dns = 1;
  algoenum.v1.IPType ip_type = 2;
  string name = 3;
  string dns1 = 4;
  string dns2 = 5;
  bool is_active = 6;
}



message BackofficeDNSServiceCreateDNSRequest {
  string name = 1;
  string dns1 = 2;
  string dns2 =  3;
}

message BackofficeDNSServiceCreateDNSResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message BackofficeDNSServiceUpdateDNSRequest{
  string id_dns = 1;
  string name = 2;
  string dns1 = 3;
  string dns2 = 4;
  utils.v1.State  state = 5;
}
message BackofficeDNSServiceUpdateDNSResponse{
  errmsg.v1.ErrorMessage error = 1;
}