syntax = "proto3";
package proxymanager.u2dpn.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/u2dpn/v1;u2dpnv1";

import "errmsg/v1/errormsg.proto";

service ControlPlaneU2dpnService {
 rpc InstantProxyNotification(ControlPlaneU2dpnServiceInstantProxyNotificationRequest) returns(ControlPlaneU2dpnServiceInstantProxyNotificationResponse);
}

message ControlPlaneU2dpnServiceInstantProxyNotificationRequest {
  string id_u2dpn_session = 1;
  string ip_addr = 2;
}

message ControlPlaneU2dpnServiceInstantProxyNotificationResponse {
  errmsg.v1.ErrorMessage error = 1;
}