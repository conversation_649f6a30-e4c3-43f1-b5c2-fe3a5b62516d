syntax = "proto3";
package proxymanager.telco.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/telco/v1;telcov1";

import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";


service BackofficeTelcoService {
  rpc ReloadCache(BackofficeTelcoServiceReloadCacheRequest) returns(BackofficeTelcoServiceReloadCacheResponse);

  rpc FetchTelco(BackofficeTelcoServiceFetchTelcoRequest) returns (BackofficeTelcoServiceFetchTelcoResponse);
  rpc CreateTelco(BackofficeTelcoServiceCreateTelcoRequest) returns (BackofficeTelcoServiceCreateTelcoResponse);
  rpc UpdateTelco(BackofficeTelcoServiceUpdateTelcoRequest) returns (BackofficeTelcoServiceUpdateTelcoResponse);
}

message BackofficeTelcoServiceReloadCacheRequest{}
message BackofficeTelcoServiceReloadCacheResponse{
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeTelcoServiceFetchTelcoRequest{
  string id_telco = 1;
  string name_search = 2;
  utils.v1.PaginationRequest pagination = 3;
}


message BackofficeTelcoServiceFetchTelcoResponse{
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated BackofficeTelcoModel items = 3;
}

message BackofficeTelcoServiceCreateTelcoRequest {
  string name = 1;
}

message BackofficeTelcoServiceCreateTelcoResponse {
  errmsg.v1.ErrorMessage error = 1;
}
message BackofficeTelcoServiceUpdateTelcoRequest {
  string id_telco = 1;
  string name = 2;
  string id_default_dns = 3;
}
message BackofficeTelcoServiceUpdateTelcoResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeTelcoModel {
  string id_telco = 1;
  string name = 2;
  string id_default_dns = 3;
}