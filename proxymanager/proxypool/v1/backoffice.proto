syntax = "proto3";
package proxymanager.proxypool.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/proxypool/v1;proxypoolv1";

import "errmsg/v1/errormsg.proto";
import "algoenum/v1/service.proto";
import "algoenum/v1/location_level.proto";

import "utils/v1/utils.proto";

service BackofficeProxyPoolService {
  rpc FetchProxyPool(BackofficeProxyPoolServiceFetchProxyPoolRequest) returns (BackofficeProxyPoolServiceFetchProxyPoolResponse);
  rpc RemoveProxyPool(BackofficeProxyPoolServiceRemoveProxyPoolRequest) returns (BackofficeProxyPoolServiceRemoveProxyPoolResponse);
  rpc HealthCheck(BackofficeProxyPoolServiceHealthCheckRequest) returns (BackofficeProxyPoolServiceHealthCheckResponse);
}

message BackofficeProxyPoolServiceHealthCheckRequest {
  string id_proxy_pool = 1;
}

message BackofficeProxyPoolServiceHealthCheckResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeProxyPoolServiceRemoveProxyPoolRequest {
  string id_proxy_pool = 1;
}

message BackofficeProxyPoolServiceRemoveProxyPoolResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message BackofficeProxyPoolServiceFetchProxyPoolRequest {
  algoenum.v1.ServiceType service = 1;
  string ip_proxy_search = 2;
  string username_search = 3;
  string id_location  = 4;
  utils.v1.PaginationRequest pagination = 5;
}

message BackofficeProxyPoolServiceFetchProxyPoolResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated BackofficeProxyPoolServiceProxyPoolEntity proxy_pools = 3;
}

message BackofficeProxyPoolServiceProxyPoolEntity {
  string id_proxy_pool = 1;
  string username = 2;
  algoenum.v1.ServiceType service = 3;
  BackofficeProxyPoolServiceProxyPoolEntityLocation region = 4;
  BackofficeProxyPoolServiceProxyPoolEntityLocation country = 5;
  BackofficeProxyPoolServiceProxyPoolEntityLocation state = 6;
  string public_ip = 7;
  int64 created_at = 8;
}

message BackofficeProxyPoolServiceProxyPoolEntityLocation {
  string id_location = 1;
  string name = 2;
  algoenum.v1.LocationLevel location_level = 3;
}

