syntax = "proto3";
package proxymanager.publicapi.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/publicapi/v1;publicapiv1";

import "errmsg/v1/errormsg.proto";
import "algoenum/v1/location_level.proto";
service CustomerPublicAPIService {
  rpc ChangeProxy(CustomerPublicAPIServiceChangeProxyRequest) returns (CustomerPublicAPIServiceChangeProxyResponse);
}


message CustomerPublicAPIServiceChangeProxyRequest {
  string proxy_token = 1;
}

message CustomerPublicAPIServiceChangeProxyResponse {
  errmsg.v1.ErrorMessage error = 1;
  ProxyEntity proxy = 2;
}

message ProxyEntity {
  string proxy  = 1;
  string ip_proxy = 2;
  int64 port_proxy = 3;
  repeated string ip_allows = 4;
  string username_proxy = 5;
  string password_proxy = 6;
  string ip_public = 7;
  CustomerPublicAPIProxyEntityLocation country = 9;
  CustomerPublicAPIProxyEntityLocation state = 10;
}


message CustomerPublicAPIProxyEntityLocation {
  string name = 1;
  algoenum.v1.LocationLevel level = 2;
  string emoji = 3;
}