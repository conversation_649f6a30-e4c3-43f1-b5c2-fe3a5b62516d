syntax = "proto3";
package proxymanager.backconnect.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/backconnect/v1;backconnectv1";

import "errmsg/v1/errormsg.proto";
import "hop/v1/hop.proto";

// BackConnectWorkerService receive command from controlplane
service BackConnectWorkerService {
  rpc HealthCheck(BackConnectWorkerServiceHealthCheckRequest) returns (BackConnectWorkerServiceHealthCheckResponse);
  rpc ConfigHop(BackConnectWorkerServiceConfigHopRequest) returns (BackConnectWorkerServiceConfigHopResponse);
}

message BackConnectWorkerServiceConfigHopRequest {
  hop.v1.Hop hop = 1;
}

message BackConnectWorkerServiceConfigHopResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackConnectWorkerServiceHealthCheckRequest {}

message BackConnectWorkerServiceHealthCheckResponse {
  errmsg.v1.ErrorMessage error = 1;
}