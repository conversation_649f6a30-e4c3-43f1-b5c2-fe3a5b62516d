syntax = "proto3";
package proxymanager.backconnect.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/backconnect/v1;backconnectv1";

import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";
import "algoenum/v1/back_connect_manager_status.proto";
import "algoenum/v1/back_connect_port_status.proto";
import "algoenum/v1/location_level.proto";
import "algoenum/v1/proxy_protocol.proto";

service BackofficeBackConnectService {
  rpc FetchBackConnectManager(BackofficeBackConnectServiceFetchBackConnectManagerRequest) returns(BackofficeBackConnectServiceFetchBackConnectManagerResponse);
  rpc CreateBackConnectManager(BackofficeBackConnectServiceCreateBackConnectManagerRequest) returns(BackofficeBackConnectServiceCreateBackConnectManagerResponse);
  rpc UpdateBackConnectManager(BackofficeBackConnectServiceUpdateBackConnectManagerRequest) returns(BackofficeBackConnectServiceUpdateBackConnectManagerResponse);

  rpc FetchBackConnectPort(BackofficeBackConnectServiceFetchBackConnectPortRequest) returns(BackofficeBackConnectServiceFetchBackConnectPortResponse);
}
message BackofficeBackConnectServiceCreateBackConnectManagerRequest{
  string id_merchant = 1;
  string name = 2;
  int64 start_port = 3;
  int64 end_port = 4;
}
message BackofficeBackConnectServiceCreateBackConnectManagerResponse{
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeBackConnectServiceFetchBackConnectManagerRequest {
  string id_merchant = 3;
  string id_location = 1;
  string id_telco = 2;
  string public_ip_search = 4;
  utils.v1.State state = 5;
  utils.v1.PaginationRequest pagination = 6;
}

message  BackofficeBackConnectServiceFetchBackConnectManagerResponse {
  repeated BackofficeBackConnectServiceBackConnectManager back_connect_managers = 1;
  utils.v1.PaginationResponse pagination = 2;
  errmsg.v1.ErrorMessage error = 3;
}

message BackofficeBackConnectServiceBackConnectManager {
  string id_back_connect_manager = 1;
  string name = 2;
  string id_merchant = 3;
  BackofficeBackConnectServiceBackConnectManagerLocation location = 4;
  BackofficeBackConnectServiceBackConnectManagerTelco telco = 5;
  string control_ip = 6;
  int64 control_port = 7;
  string public_ip = 8;
  string domain = 9;
  algoenum.v1.BackConnectManagerStatus status = 10;
  bool is_active  = 11;
  bool start_port = 12;
  bool end_port = 13;
  bool state = 14;
  string aes_key = 15;
  string iv_random = 16;
}

message BackofficeBackConnectServiceBackConnectManagerLocation {
  string id_location = 1;
  algoenum.v1.LocationLevel location_level = 2;
  string name = 3;
  string emoji = 4;
}


message BackofficeBackConnectServiceBackConnectManagerTelco {
  string id_telco = 1;
  string name = 3;
}

message BackofficeBackConnectServiceUpdateBackConnectManagerRequest {
  string id_back_connect_manager = 1;
  string id_location =2;
  string id_telco = 3;
  string name = 4;
  bool is_change_aes_key = 5;
  utils.v1.State state = 6;
}
message BackofficeBackConnectServiceUpdateBackConnectManagerResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeBackConnectServiceFetchBackConnectPortRequest {
  string id_back_connect_manager = 1;
  string id_proxy_token = 2;
  algoenum.v1.BackConnectPortStatus status = 3;
  int64 port = 4;
  utils.v1.PaginationRequest pagination = 5;
}

message BackofficeBackConnectServiceFetchBackConnectPortResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated  BackofficeBackConnectServiceBackConnectPort back_connect_ports = 3;
}

message BackofficeBackConnectServiceBackConnectPort {
  string id_back_connect_port = 1;
  int64 port = 2;
  algoenum.v1.ProxyProtocol proxy_protocol = 3;
  algoenum.v1.BackConnectPortStatus status = 4;
}
