syntax = "proto3";
package proxymanager.backconnect.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/backconnect/v1;backconnectv1";

import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";
import "algoenum/v1/location_level.proto";

service CustomerBackConnectService {
  rpc FetchAvailableLocationOfBackConnect(CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest) returns (CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse);
//  rpc FetchAvailableBackConnect(CustomerBackConnectServiceFetchAvailableBackConnectRequest) returns(CustomerBackConnectServiceFetchAvailableBackConnectResponse);
}
message CustomerBackConnectServiceFetchAvailableLocationOfBackConnectRequest {
  string id_plan = 1;
  utils.v1.PaginationRequest pagination = 2;
}

message CustomerBackConnectServiceFetchAvailableLocationOfBackConnectResponse {
  errmsg.v1.ErrorMessage error = 1;
  repeated CustomerBackConnectLocation locations = 2;
}
message CustomerBackConnectLocation {
  string id_location = 1;
  string name = 2;
  algoenum.v1.LocationLevel level = 3;
  string emoji = 4;
}


message CustomerBackConnectServiceCreateBackConnectManagerRequest{
  string id_location = 1;
//  string id_telco = 2;
  string name = 3;
  int64 start_port = 4;
  int64 end_port = 5;
}
message CustomerBackConnectServiceCreateBackConnectManagerResponse{
  errmsg.v1.ErrorMessage error = 1;
}
message CustomerBackConnectServiceFetchAvailableBackConnectRequest {
  string id_plan = 1;
  string id_location = 2;
  string public_ip_search = 3;
  utils.v1.PaginationRequest pagination = 4;
}

message  CustomerBackConnectServiceFetchAvailableBackConnectResponse {
  repeated CustomerBackConnectServiceBackConnectManager back_connect_managers = 1;
  utils.v1.PaginationResponse pagination = 2;
  errmsg.v1.ErrorMessage error = 3;
}

message CustomerBackConnectServiceBackConnectManager {
  string id_back_connect_manager = 1;
  string name = 2;
  CustomerBackConnectServiceBackConnectManagerLocation location = 3;
  CustomerBackConnectServiceBackConnectManagerTelco telco = 4;
  string public_ip = 5;
}

message CustomerBackConnectServiceBackConnectManagerLocation {
  string id_location = 1;
  algoenum.v1.LocationLevel location_level = 2;
  string name = 3;
  bool is_active = 4;
}
message CustomerBackConnectServiceBackConnectManagerTelco {
  string id_telco = 1;
  string name = 2;
}

