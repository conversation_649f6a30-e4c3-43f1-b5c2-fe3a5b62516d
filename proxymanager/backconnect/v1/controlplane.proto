syntax = "proto3";
package proxymanager.backconnect.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/backconnect/v1;backconnectv1";

import "errmsg/v1/errormsg.proto";
import "algoenum/v1/proxy_protocol.proto";
import "algoenum/v1/back_connect_port_status.proto";
import "algoenum/v1/back_connect_manager_status.proto";
import "algoenum/v1/packet_type.proto";
import "utils/v1/utils.proto";

service BackConnectControlPlaneService {
    rpc RegisterManager(BackConnectControlPlaneServiceRegisterManagerRequest) returns (BackConnectControlPlaneServiceRegisterManagerResponse);
    rpc FetchPort(BackConnectControlPlaneServiceFetchPortRequest) returns (BackConnectControlPlaneServiceFetchPortResponse);
    rpc Tracking(BackConnectControlPlaneServiceTrackingRequest) returns (BackConnectControlPlaneServiceTrackingResponse);
}

message BackConnectControlPlaneServiceTrackingRequest {
  string id_session = 1;
  algoenum.v1.PacketType packet_type = 2;
  string ip_user = 3;
  string domain = 4;
  string ip_of_domain = 5;
  int64 bytes_upload = 6;
  int64 bytes_download = 7;
  int64 created_at = 8;
}


message BackConnectControlPlaneServiceTrackingResponse {
  bool success = 1;
}

message BackConnectControlPlaneServiceRegisterManagerRequest {
  string id_backconnect_manager = 1;
  string public_ip = 2;
  string control_ip = 3;
  int64 control_port = 4;
  algoenum.v1.BackConnectManagerStatus status = 5;
}

message BackConnectControlPlaneServiceRegisterManagerResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackConnectControlPlaneServiceFetchPortRequest {
  string id_back_connect_manager = 1;
  utils.v1.PaginationRequest pagination = 2;
}
message BackConnectControlPlaneServiceFetchPortResponse {
  repeated ControlPlaneBackConnectPort ports = 1;
  utils.v1.PaginationResponse pagination = 2;
  errmsg.v1.ErrorMessage error = 3;
}

message ControlPlaneBackConnectPort {
  string id_back_connect_port = 1;
  int64 port = 2;
  algoenum.v1.ProxyProtocol proxy_protocol = 3;
  algoenum.v1.BackConnectPortStatus status = 4;
}