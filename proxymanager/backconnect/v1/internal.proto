syntax = "proto3";
package proxymanager.backconnect.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/backconnect/v1;backconnectv1";


//service BackConnectInternalService {
//}
//
//message BackConnectInternalServiceFetchBackConnectPortRequest {
//  repeated string list_id_back_connect_port = 1;
//}
//
//message BackConnectInternalServiceFetchBackConnectPortResponse {
//
//}
//
//message InternalPortResponse