syntax = "proto3";
package proxymanager.tracking.v2;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/tracking/v2;trackingv2";

service TrackingService {
  rpc Tracking(TrackingRequest) returns (TrackingResponse);
  rpc CheckDomainBlock(CheckDomainBlockRequest) returns (CheckDomainBlockResponse);
}

message TrackingRequest {
  repeated TrackingSegment list_tracking = 1;
}

message TrackingSegment {
  string id_session = 1;
  string ip_user =2 ;
  string domain = 3;
  string ip_of_domain = 4;
  int64 bytes_upload = 5;
  int64 bytes_download = 6;
}

message TrackingResponse {
  bool success = 1;
}

message CheckDomainBlockRequest {
  string ip_user = 1;
  string domain = 2;
}

message CheckDomainBlockResponse {
  bool is_allow = 1;
}

//message TrackingDNSRequest {
//  string id_session = 1;
//  string ip_user  =2 ;
//  string domain = 3 ;
//  string ip = 4;
//}
//
//message TrackingDNSResponse {
//  bool  success = 1;
//}
//
//message TrackingDataUsageRequest {
//  string id_session = 1;
//  uint64 up_byte = 2;
//  uint64 down_byte = 3;
//}
//
//message TrackingDataUsageResponse {
//  bool success = 1;
//}