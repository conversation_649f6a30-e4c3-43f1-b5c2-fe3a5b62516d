syntax = "proto3";
package proxymanager.location.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/location/v1;locationv1";

import "utils/v1/utils.proto";
import "errmsg/v1/errormsg.proto";
import "algoenum/v1/location_level.proto";

service MerchantLocationService {
  rpc FetchLocation(MerchantLocationServiceFetchLocationRequest) returns(MerchantLocationServiceFetchLocationResponse);

}

message MerchantLocationServiceFetchLocationRequest {
  string id_location = 1;
  repeated string id_locations= 2;
  string id_parent_location = 3;
  algoenum.v1.LocationLevel location_level = 4;
  string name_search = 5;
  string iso_code_search = 6;
  utils.v1.PaginationRequest pagination = 7 ;
}

message MerchantLocationServiceFetchLocationResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated MerchantLocationModel locations = 3;
}


message MerchantLocationModel {
  string id_location = 1;
  MerchantLocationModel parent = 2;
  algoenum.v1.LocationLevel location_level = 3;
  string name = 4;
  string iso_code = 5;
  string emoji = 6;
}
