syntax = "proto3";
package proxymanager.location.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/location/v1;locationv1";

import "utils/v1/utils.proto";
import "errmsg/v1/errormsg.proto";
import "algoenum/v1/location_level.proto";

service BackofficeLocationService {
  rpc ReloadCache(BackofficeLocationServiceReloadCacheRequest) returns (BackofficeLocationServiceReloadCacheResponse);
  
  rpc FetchLocation(BackofficeLocationServiceFetchLocationRequest) returns(BackofficeLocationServiceFetchLocationResponse);
  rpc CreateLocation(BackofficeLocationServiceCreateLocationRequest) returns(BackofficeLocationServiceCreateLocationResponse);
  rpc UpdateLocation(BackofficeLocationServiceUpdateLocationRequest) returns(BackofficeLocationServiceUpdateLocationResponse);
}
message BackofficeLocationServiceReloadCacheRequest {}
message BackofficeLocationServiceReloadCacheResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeLocationServiceFetchLocationRequest {
  string id_location = 1;
  repeated string id_locations= 2;
  string id_parent_location = 3;
  algoenum.v1.LocationLevel location_level = 4;
  string name_search = 5;
  string iso_code_search = 6;

  utils.v1.PaginationRequest pagination = 7 ;
}

message BackofficeLocationServiceFetchLocationResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated BackofficeLocationModel locations = 3;
}


message BackofficeLocationServiceCreateLocationRequest{
  string id_parent_location = 1;
  algoenum.v1.LocationLevel location_level = 2;
  string name = 3;
  string iso_code = 4;
  string emoji = 5;
}

message BackofficeLocationServiceCreateLocationResponse{
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeLocationServiceUpdateLocationRequest{
  string id_location = 1;
  algoenum.v1.LocationLevel location_level = 2;
  string emoji = 3;
}

message BackofficeLocationServiceUpdateLocationResponse{
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeLocationModel {
  string id_location = 1;
  BackofficeLocationModel parent = 2;
  algoenum.v1.LocationLevel location_level = 3;
  string name = 4;
  string iso_code = 5;
  string emoji = 6;
}
