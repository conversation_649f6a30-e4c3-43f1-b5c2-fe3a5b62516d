syntax = "proto3";
package proxymanager.plan.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/plan/v1;planv1";

import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";
import "algoenum/v1/ip_type.proto";
import "algoenum/v1/proxy_type.proto";
import "algoenum/v1/change_type.proto";
import "algoenum/v1/data_transfer_type.proto";
import "algoenum/v1/location_level.proto";

service  BackofficePlanService {
  rpc FetchPlan(BackofficePlanServiceFetchPlanRequest) returns (BackofficePlanServiceFetchPlanResponse);
  rpc CreatePlan(BackofficePlanServiceCreatePlanRequest) returns (BackofficePlanServiceCreatePlanResponse);
  rpc UpdatePlan(BackofficePlanServiceUpdatePlanRequest) returns (BackofficePlanServiceUpdatePlanResponse);

  rpc ConfigPlanLocation(BackofficePlanServiceConfigPlanLocationRequest) returns(BackofficePlanServiceConfigPlanLocationResponse);
  rpc ConfigPlanBackConnect(BackofficePlanServiceConfigPlanBackConnectRequest) returns(BackofficePlanServiceConfigPlanBackConnectResponse);
}

message BackofficePlanServiceConfigPlanBackConnectRequest {
  string id_plan = 1;
  string id_back_connect_manager = 2;
  bool is_enable  = 3;
}

message BackofficePlanServiceConfigPlanBackConnectResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message BackofficePlanServiceConfigPlanLocationRequest {
  string id_plan = 1;
  string id_location = 2;
  bool is_enable  = 3;
}
message BackofficePlanServiceConfigPlanLocationResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message BackofficePlanServiceCreatePlanRequest {
  string id_merchant = 1;
  string name = 2;
  algoenum.v1.IPType ip_type = 3;
  algoenum.v1.ProxyType proxy_type = 4;
  algoenum.v1.ChangeType change_type = 5;
  algoenum.v1.DataTransferType data_transfer_type = 6;
  int64 bandwidth_per_proxy_in_mbit = 7;
  int64 time_to_live_per_proxy_in_sec = 8;
  int64 time_to_change_per_proxy_in_sec = 9;
  int64 concurrent_proxy = 10;
  string description = 11;
  int64 index = 12;
}




message BackofficePlanServiceCreatePlanResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message BackofficePlanServiceFetchPlanRequest {
  string id_merchant = 1;
  string id_plan = 2;
  string id_location = 3;
  string name_search = 4;
  algoenum.v1.IPType ip_type = 5;
  algoenum.v1.ProxyType proxy_type = 6;
  algoenum.v1.ChangeType change_type = 7;
  algoenum.v1.DataTransferType data_transfer_type = 8;

  utils.v1.State state = 9;
  utils.v1.PaginationRequest pagination = 10;
}

message BackofficePlanServiceFetchPlanResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated BackofficePlanServicePlanEntity plans = 3;
}


message BackofficePlanServiceUpdatePlanRequest{
  string id_plan = 1;
  string name = 2;
  algoenum.v1.IPType ip_type = 3;
  algoenum.v1.ProxyType proxy_type = 4;
  algoenum.v1.ChangeType change_type = 5;
  algoenum.v1.DataTransferType data_transfer_type = 6;
  int64 bandwidth_per_proxy_in_mbit = 7;
  int64 time_to_live_per_proxy_in_sec = 8;
  int64 time_to_change_per_proxy_in_sec = 9;
  int64 concurrent_proxy =10;
  string description = 11;
  int64 index = 12;
  utils.v1.State state = 13;
}

message BackofficePlanServiceUpdatePlanResponse{
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficePlanServicePlanEntity {
  string id_plan = 1;
  string name = 2;
  BackofficePlanServicePlanMerchantEntity merchant = 3;
  repeated BackofficePlanServicePlanLocation locations = 4;
  algoenum.v1.IPType ip_type = 5;
  algoenum.v1.ProxyType proxy_type = 6;
  algoenum.v1.ChangeType change_type = 7;
  algoenum.v1.DataTransferType data_transfer_type = 8;
  int64 data_transfer_in_gbyte = 9;
  int64 bandwidth_per_proxy_in_mbit = 10;
  int64 time_to_live_per_proxy_in_sec = 11;
  int64 time_to_change_per_proxy_in_sec = 12;
  int64 concurrent_proxy = 13;
  string description = 14;
  int64 index = 15;
  bool  is_active = 16;
  repeated BackofficePlanServicePlanBackConnect back_connects = 17;
}

message  BackofficePlanServicePlanBackConnect {
  string id_back_connect_manager = 1;
  string name = 2;
}


message BackofficePlanServicePlanMerchantEntity {
  string id_merchant = 1;
  string name = 2;
  bool is_active = 3;
}


message  BackofficePlanServicePlanLocation {
  string id_location = 1;
  string name = 2;
  algoenum.v1.LocationLevel location_level = 3;
  bool is_active = 4;
}
