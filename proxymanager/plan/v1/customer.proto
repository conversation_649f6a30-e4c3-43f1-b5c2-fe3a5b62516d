syntax = "proto3";
package proxymanager.plan.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/plan/v1;planv1";

import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";
import "algoenum/v1/ip_type.proto";
import "algoenum/v1/proxy_type.proto";
import "algoenum/v1/change_type.proto";
import "algoenum/v1/data_transfer_type.proto";
import "algoenum/v1/location_level.proto";

service CustomerPlanService {
  rpc FetchAvailableLocation(CustomerPlanServiceFetchAvailableLocationRequest) returns (CustomerPlanServiceFetchAvailableLocationResponse);
  rpc FetchPlan(CustomerPlanServiceFetchPlanRequest) returns (CustomerPlanServiceFetchPlanResponse);
}



message CustomerPlanServiceFetchAvailableLocationRequest {
  utils.v1.PaginationRequest pagination = 8;
}
message CustomerPlanServiceFetchAvailableLocationResponse {
  repeated CustomerPlanServicePlanLocation locations = 1;
  errmsg.v1.ErrorMessage error = 2;
  utils.v1.PaginationResponse pagination = 3;
}

message CustomerPlanServiceFetchPlanRequest {
  string id_plan = 1;
  string id_location = 2;
  string name_search = 3;
  algoenum.v1.IPType ip_type = 4;
  algoenum.v1.ProxyType proxy_type = 5;
  algoenum.v1.ChangeType change_type = 6;
  algoenum.v1.DataTransferType data_transfer_type = 7;
  utils.v1.PaginationRequest pagination = 8;
}

message CustomerPlanServiceFetchPlanResponse {
  repeated CustomerPlanServicePlanEntity plans = 1;
  errmsg.v1.ErrorMessage error = 2;
  utils.v1.PaginationResponse pagination = 3;
}

message CustomerPlanServicePlanEntity {
  string id_plan = 1;
  string name = 2;
  repeated CustomerPlanServicePlanLocation locations = 4;
  algoenum.v1.IPType ip_type = 5;
  algoenum.v1.ProxyType proxy_type = 6;
  algoenum.v1.ChangeType change_type = 7;
  algoenum.v1.DataTransferType data_transfer_type = 8;
  int64 data_transfer_in_gbyte = 9;
  int64 bandwidth_per_proxy_in_mbit = 10;
  int64 time_to_live_per_proxy_in_sec = 11;
  int64 time_to_change_per_proxy_in_sec = 12;
  int64 concurrent_proxy = 13;
  string description = 14;
}

message CustomerPlanServicePlanLocation {
  string id_location = 1;
  string name = 2;
  algoenum.v1.LocationLevel location_level = 3;
  string emoji = 4;
}