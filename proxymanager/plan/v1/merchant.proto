syntax = "proto3";
package proxymanager.plan.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/plan/v1;planv1";

import "algoenum/v1/ip_type.proto";
import "algoenum/v1/proxy_type.proto";
import "algoenum/v1/change_type.proto";
import "algoenum/v1/data_transfer_type.proto";
import "algoenum/v1/location_level.proto";
import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";

service  MerchantPlanService {
  rpc FetchPlan(MerchantPlanServiceFetchPlanRequest) returns (MerchantPlanServiceFetchPlanResponse);
}

message MerchantPlanServiceFetchPlanRequest {
  string id_plan = 1;
  string id_location = 2;
  string name_search = 3;
  algoenum.v1.IPType ip_type = 4;
  algoenum.v1.ProxyType proxy_type = 5;
  algoenum.v1.ChangeType change_type = 6;
  algoenum.v1.DataTransferType data_transfer_type = 7;
  utils.v1.PaginationRequest pagination = 8;
}

message MerchantPlanServiceFetchPlanResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated MerchantPlanServicePlanEntity plans = 3;
}

message MerchantPlanServicePlanEntity {
  string id_plan = 1;
  string name = 2;
  repeated MerchantPlanServicePlanLocation locations = 3;
  algoenum.v1.IPType ip_type = 4;
  algoenum.v1.ProxyType proxy_type = 5;
  algoenum.v1.ChangeType change_type = 6;
  algoenum.v1.DataTransferType data_transfer_type = 7;
  int64 data_transfer_in_gbyte = 8;
  int64 bandwidth_per_proxy_in_mbit = 9;
  int64 time_to_live_per_proxy_in_sec = 10;
  int64 time_to_change_per_proxy_in_sec = 11;
  int64 concurrent_proxy = 12;
  string description = 13;
}

message  MerchantPlanServicePlanLocation {
  string id_location = 1;
  string name = 2;
  algoenum.v1.LocationLevel location_level = 3;
  bool is_active = 4;
}

