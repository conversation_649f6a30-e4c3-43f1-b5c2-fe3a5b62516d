
syntax = "proto3";
package proxymanager.plan.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/plan/v1;planv1";

import "errmsg/v1/errormsg.proto";
import "algoenum/v1/data_transfer_type.proto";

service  InternalPlanService {
  rpc FetchPlan(InternalPlanServiceFetchPlanRequest) returns(InternalPlanServiceFetchPlanResponse);
}

message InternalPlanServiceFetchPlanRequest {
  string id_merchant = 1;
  repeated string id_plan = 2;
}
message InternalPlanServiceFetchPlanResponse {
    repeated InternalPlanServicePlanEntity plan = 1;
    errmsg.v1.ErrorMessage error = 2;
}

message InternalPlanServicePlanEntity {
  string id_plan = 1;
  string id_merchant = 2;
  string name = 3;
  algoenum.v1.DataTransferType data_transfer_type = 4;
  int64 time_to_live_proxy_in_sec = 5;
  int64 time_to_change_per_proxy_in_sec = 6;
  int64 concurrent_proxy = 7;
  bool  is_active = 8;
}