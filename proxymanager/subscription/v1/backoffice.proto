syntax = "proto3";
package proxymanager.subscription.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/subscription/v1;subscriptionv1";

import "utils/v1/utils.proto";
import "errmsg/v1/errormsg.proto";


service BackofficeSubscriptionService {
  rpc FetchSubscription(BackofficeSubscriptionServiceFetchSubscriptionRequest) returns (BackofficeSubscriptionServiceFetchSubscriptionResponse);

  rpc FetchProxyToken(BackofficeSubscriptionServiceFetchProxyTokenRequest) returns (BackofficeSubscriptionServiceFetchProxyTokenResponse);
}

message BackofficeSubscriptionServiceFetchProxyTokenRequest {
  string id_merchant = 1;
  string id_user = 2;
  string id_plan  = 3;
  string id_subscription = 4;
  utils.v1.PaginationRequest pagination = 5;
}

message BackofficeSubscriptionServiceFetchProxyTokenResponse {
  repeated BackofficeSubscriptionProxyTokenEntity proxy_tokens = 1;
  errmsg.v1.ErrorMessage  error = 2;
  utils.v1.PaginationResponse pagination = 3;

}

message BackofficeSubscriptionProxyTokenEntity {
  string id_proxy_token = 1;
  string id_proxy_profile = 2;
  string proxy_token = 3;
}

//message BackofficeSubscriptionServiceFetchSubscriptionStaticsRequest {
//  repeated string id_subscriptions = 1;
//  utils.v1.PaginationRequest pagination = 2;
//}
//message BackofficeSubscriptionServiceFetchSubscriptionStaticsResponse {}
//
//message BackofficeSubscriptionServiceSubscriptionStatics {
//
//}

message BackofficeSubscriptionServiceFetchSubscriptionRequest {
  string id_merchant = 1;
  string id_user = 2;
  string id_plan  = 3;
  string id_subscription = 4;
  bool is_expired = 5;
  utils.v1.State state = 6;
  utils.v1.PaginationRequest pagination = 7;
}

message BackofficeSubscriptionServiceFetchSubscriptionResponse {
  repeated BackofficeSubscriptionEntity subscriptions = 1;
  errmsg.v1.ErrorMessage  error = 2;
  utils.v1.PaginationResponse pagination = 3;
}

message BackofficeSubscriptionEntity {
  string id_subscription = 1;
  int64 expired_at = 4 ;
  double data_transfer_remaining = 5;
  bool  is_active = 6;
  BackofficeSubscriptionPlanEntity plan = 7;
}

message  BackofficeSubscriptionPlanEntity {
  string id_plan = 1;
  string name = 2;
  bool is_active = 4;
}

