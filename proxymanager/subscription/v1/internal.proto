syntax = "proto3";
package proxymanager.subscription.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/subscription/v1;subscriptionv1";

import "errmsg/v1/errormsg.proto";


service InternalSubscriptionService {
  rpc FetchSubscription(InternalSubscriptionServiceFetchSubscriptionRequest) returns (InternalSubscriptionServiceFetchSubscriptionResponse);
  rpc CreateSubscription(InternalSubscriptionServiceCreateSubscriptionRequest) returns(InternalSubscriptionServiceCreateSubscriptionResponse);
  rpc ExtendSubscription(InternalSubscriptionServiceExtendSubscriptionRequest) returns(InternalSubscriptionServiceExtendSubscriptionResponse);
}

message InternalSubscriptionServiceCreateSubscriptionRequest {
  string id_plan = 1;
  string id_subscription = 2;
  string id_user = 3;
  int64 duration = 4;
  double data_transfer_in_gbyte = 5;
  double user_lat = 6;
  double user_long = 7;
}
message InternalSubscriptionServiceCreateSubscriptionResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message InternalSubscriptionServiceExtendSubscriptionRequest {
  string id_subscription = 1;
  int64 duration = 2;
  double data_transfer_in_gbyte = 3;
}

message InternalSubscriptionServiceExtendSubscriptionResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message InternalSubscriptionServiceFetchSubscriptionRequest {
  repeated string list_id_subscription = 1;
}
message InternalSubscriptionServiceFetchSubscriptionResponse {
  repeated InternalSubscriptionModel subscriptions  = 1;
  errmsg.v1.ErrorMessage error = 2;
}

message InternalSubscriptionModel {
  string id_subscription = 1;
  string id_merchant = 2;
  string id_user = 3;
  string name = 4;
}
