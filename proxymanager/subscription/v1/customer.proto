syntax = "proto3";
package proxymanager.subscription.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/subscription/v1;subscriptionv1";


import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";
import "algoenum/v1/location_level.proto";
service CustomerSubscriptionService {
  rpc FetchUserPlan(CustomerSubscriptionServiceFetchUserPlanRequest) returns (CustomerSubscriptionServiceFetchUserPlanResponse);
  rpc FetchSubscription(CustomerSubscriptionServiceFetchSubscriptionRequest) returns(CustomerSubscriptionServiceFetchSubscriptionResponse);
  rpc UpdateSubscription(CustomerSubscriptionServiceUpdateSubscriptionRequest) returns(CustomerSubscriptionServiceUpdateSubscriptionResponse);

  rpc FetchProxyToken(CustomerSubscriptionServiceFetchProxyTokenRequest) returns (CustomerSubscriptionServiceFetchProxyTokenResponse);

  rpc UpdateBackConnectProxyToken(CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest) returns(CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse);
  rpc RevokeProxyToken(CustomerSubscriptionServiceRevokeProxyTokenRequest) returns(CustomerSubscriptionServiceRevokeProxyTokenResponse);

  rpc DeleteSubscription(CustomerSubscriptionServiceDeleteSubscriptionRequest) returns(CustomerSubscriptionServiceDeleteSubscriptionResponse);
}
message CustomerSubscriptionServiceUpdateSubscriptionRequest{
  string id_subscription = 1;
  string name = 2;
}
message CustomerSubscriptionServiceUpdateSubscriptionResponse {
  errmsg.v1.ErrorMessage  error = 1;
}
message CustomerSubscriptionServiceRevokeProxyTokenRequest{
  string id_proxy_token = 1;
}

message CustomerSubscriptionServiceRevokeProxyTokenResponse{
  errmsg.v1.ErrorMessage  error = 1;
}

message CustomerSubscriptionServiceUpdateBackConnectProxyTokenRequest{
  string id_proxy_token = 1;
  string id_location_of_back_connect = 2;
}

message CustomerSubscriptionServiceUpdateBackConnectProxyTokenResponse{
  errmsg.v1.ErrorMessage  error = 1;
}


message CustomerSubscriptionServiceFetchUserPlanRequest {}
message CustomerSubscriptionServiceFetchUserPlanResponse {
  errmsg.v1.ErrorMessage  error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated CustomerSubscriptionServiceFetchUserPlanPlan plans = 3;
}
message CustomerSubscriptionServiceFetchUserPlanPlan {
  string id_plan  = 1;
  string name  = 2;
}

message CustomerSubscriptionServiceDeleteSubscriptionRequest {
  string id_subscription = 1;
}
message CustomerSubscriptionServiceDeleteSubscriptionResponse {
  errmsg.v1.ErrorMessage  error = 1;
}




message CustomerSubscriptionServiceFetchProxyTokenRequest {
  string id_subscription = 4;
  utils.v1.PaginationRequest pagination = 5;
}

message CustomerSubscriptionServiceFetchProxyTokenResponse {
  repeated CustomerSubscriptionProxyTokenEntity proxy_tokens = 1;
  errmsg.v1.ErrorMessage  error = 2;
  utils.v1.PaginationResponse pagination = 3;
}

message CustomerSubscriptionProxyTokenEntity {
  string id_proxy_token = 1;
  string id_proxy_profile = 2;
  string proxy_token = 3;
  CustomerSubscriptionProxyTokenEntityBackConnect back_connect = 4;
  CustomerSubscriptionProxyTokenEntityProxy proxy = 5;
}


message CustomerSubscriptionProxyTokenEntityBackConnect {
  string id_back_connect_manager = 1;
  string name = 2;
  CustomerSubscriptionLocation country = 3;
  string ip_proxy = 4;
  int64  port_proxy = 5;
}
message CustomerSubscriptionProxyTokenEntityProxy {
  CustomerSubscriptionLocation country = 1;
  CustomerSubscriptionLocation state = 2;
  string public_ip = 3;
  repeated string ip_allows = 4;
  string username_proxy = 5;
  string password_proxy = 6;
}

message CustomerSubscriptionLocation {
  string name = 1;
  algoenum.v1.LocationLevel level = 2;
  string emoji = 3;
}

message CustomerSubscriptionServiceFetchSubscriptionRequest{
  string id_plan = 1;
  string id_subscription = 2;
  bool is_expired = 3;
  string name_search  = 4;
  utils.v1.PaginationRequest pagination = 5;
}

message CustomerSubscriptionServiceFetchSubscriptionResponse {
  repeated CustomerSubscriptionEntity subscriptions = 1;
  errmsg.v1.ErrorMessage  error = 2;
  utils.v1.PaginationResponse pagination = 3;
}

message CustomerSubscriptionEntity {
  string id_subscription = 1;
  string name = 2;
  double data_transfer_remaining = 3;
  int64 expired_at = 4;
  CustomerSubscriptionPlanEntity plan = 5;
}

message  CustomerSubscriptionPlanEntity {
  string id_plan = 1;
  string name = 2;
  int64 concurrent_proxy = 3;
}


//message CustomerSubscriptionServicePlanLocation {
//  string id_location = 1;
//  algoenum.v1.LocationLevel location_level = 2;
//  string name = 3;
//}
