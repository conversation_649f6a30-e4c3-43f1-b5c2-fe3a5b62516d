syntax = "proto3";
package proxymanager.subscription.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/subscription/v1;subscriptionv1";

import "utils/v1/utils.proto";
import "errmsg/v1/errormsg.proto";

service MerchantSubscriptionService {
  rpc FetchSubscription(MerchantSubscriptionServiceFetchSubscriptionRequest) returns (MerchantSubscriptionServiceFetchSubscriptionResponse);

  rpc FetchProxyToken(MerchantSubscriptionServiceFetchProxyTokenRequest) returns (MerchantSubscriptionServiceFetchProxyTokenResponse);
}
message MerchantSubscriptionServiceFetchProxyTokenRequest {
  string id_merchant = 1;
  string id_user = 2;
  string id_plan  = 3;
  string id_subscription = 4;
  utils.v1.PaginationRequest pagination = 5;
}

message MerchantSubscriptionServiceFetchProxyTokenResponse {
  repeated MerchantSubscriptionProxyTokenEntity proxy_tokens = 1;
  errmsg.v1.ErrorMessage  error = 2;
  utils.v1.PaginationResponse pagination = 3;
}

message MerchantSubscriptionProxyTokenEntity {
  string id_proxy_token = 1;
  string id_proxy_profile = 2;
  string proxy_token = 3;
}

message MerchantSubscriptionServiceFetchSubscriptionRequest {
  string id_user = 2;
  string id_plan  = 3;
  string proxy_token = 4;
  bool is_expired = 5;
  utils.v1.State state = 6;
  utils.v1.PaginationRequest pagination = 7;
}

message MerchantSubscriptionServiceFetchSubscriptionResponse {
  repeated MerchantSubscriptionEntity subscriptions = 1;
  errmsg.v1.ErrorMessage  error = 2;
  utils.v1.PaginationResponse pagination = 3;
}

message MerchantSubscriptionEntity {
  string id_subscription = 1;
  double data_transfer_remaining = 4;
  int64 expired_at = 5 ;
  bool  is_active = 6;
  MerchantSubscriptionPlanEntity plan = 7;
}

message  MerchantSubscriptionPlanEntity {
  string id_plan = 1;
  string name = 2;
  bool is_active = 4;
}
