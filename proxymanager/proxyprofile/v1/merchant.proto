syntax = "proto3";
package proxymanager.proxyprofile.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/proxyprofile/v1;proxyprofilev1";

import "algoenum/v1/ip_type.proto";
import "algoenum/v1/proxy_type.proto";
import "algoenum/v1/change_type.proto";
import "algoenum/v1/data_transfer_type.proto";
import "algoenum/v1/location_level.proto";
import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";


service MerchantProxyProfileService {
  rpc FetchProxyProfile(MerchantProxyProfileServiceFetchProxyProfileRequest) returns (MerchantProxyProfileServiceFetchProxyProfileResponse);
}



message MerchantProxyProfileServiceFetchProxyProfileRequest {
  string id_user = 1;
  string id_plan = 2;
  string id_proxy_profile = 3;
  utils.v1.State state = 4;
  utils.v1.PaginationRequest pagination = 5;
}

message MerchantProxyProfileServiceFetchProxyProfileResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated MerchantProxyProfileServiceProxyProfileEntity proxy_profiles = 3;
}


message MerchantProxyProfileServiceProxyProfileEntity {
  string id_proxy_profile = 1;
  string name = 2;
  MerchantProxyProfileServiceProxyProfileEntityDNS dns  = 3;
  int64 auto_switch_ip_after_in_sec = 4;
  bool auto_switch_ip_when_disconnect = 5;
  bool is_static_username_password = 6;
  repeated string ip_allows = 7;
  repeated MerchantProxyProfileServiceProxyProfileEntityTelco telcos = 8;
  repeated MerchantProxyProfileServiceProxyProfileEntityLocation locations = 9;
  bool is_active = 10;
  bool is_default_profile = 11;
  int64 bandwidth_per_proxy_mbit = 12;
  string username_proxy = 13;
  string password_proxy = 14;
}

message MerchantProxyProfileUser {
  string id_user = 1;
  string email = 2;
}
message MerchantProxyProfilePlan {
  string id_plan = 1;
  string name = 2;
  algoenum.v1.IPType ip_type = 4;
  algoenum.v1.ProxyType proxy_type = 5;
  algoenum.v1.ChangeType change_type = 6;
  algoenum.v1.DataTransferType data_transfer_type = 7;
}
message MerchantProxyProfileServiceProxyProfileEntityDNS {
  string id_dns = 1;
  string name = 2;
  string dns_1 = 3;
  string dns_2 = 4;
}
message MerchantProxyProfileServiceProxyProfileEntityTelco {
  string id_telco = 1;
  string name = 2;
}
message MerchantProxyProfileServiceProxyProfileEntityLocation {
  string id_location = 1;
  algoenum.v1.LocationLevel level = 2;
  string name = 3;
}
