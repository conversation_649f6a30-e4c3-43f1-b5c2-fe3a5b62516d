syntax = "proto3";
package proxymanager.proxyprofile.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/proxyprofile/v1;proxyprofilev1";

import "errmsg/v1/errormsg.proto";
import "utils/v1/utils.proto";
import "algoenum/v1/location_level.proto";
service BackofficeProxyProfileService {
  rpc FetchProxyProfile(BackofficeProxyProfileServiceFetchProxyProfileRequest) returns(BackofficeProxyProfileServiceFetchProxyProfileResponse);

  rpc ResetProxyProfile(BackofficeProxyProfileServiceResetProxyProfileRequest) returns (BackofficeProxyProfileServiceResetProxyProfileResponse);

  rpc SetDefaultProxyProfile(BackofficeProxyProfileServiceSetDefaultProxyProfileRequest) returns(BackofficeProxyProfileServiceSetDefaultProxyProfileResponse);
  rpc CreateProxyProfile(BackofficeProxyProfileServiceCreateProxyProfileRequest) returns (BackofficeProxyProfileServiceCreateProxyProfileResponse);
  rpc UpdateProxyProfile(BackofficeProxyProfileServiceUpdateProxyProfileRequest) returns (BackofficeProxyProfileServiceUpdateProxyProfileResponse);
  rpc ConfigProxyProfileLocation(BackofficeProxyProfileServiceConfigProxyProfileLocationRequest) returns (BackofficeProxyProfileServiceConfigProxyProfileLocationResponse);
  rpc ConfigProxyProfileIPAllow(BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest) returns (BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse);
  rpc ConfigProxyProfileTelco(BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest) returns (BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse);
}

message BackofficeProxyProfileServiceResetProxyProfileRequest {
  string id_proxy_profile = 1;
}

message BackofficeProxyProfileServiceResetProxyProfileResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeProxyProfileServiceFetchProxyProfileRequest {
  string id_merchant = 1;
  string id_user = 2;
  string id_plan = 3;
  string id_proxy_profile = 4;
  utils.v1.State state = 5;
  utils.v1.PaginationRequest pagination = 6;
}

message BackofficeProxyProfileServiceFetchProxyProfileResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated BackofficeProxyProfileServiceProxyProfileEntity proxy_profiles = 3;
}


message BackofficeProxyProfileServiceProxyProfileEntity {
  string id_proxy_profile = 1;
  string name = 2;
  BackofficeProxyProfileServiceProxyProfileEntityDNS dns  = 3;
  int64 auto_switch_ip_after_in_sec = 4;
  bool auto_switch_ip_when_disconnect = 5;
  bool is_static_username_password = 6;
  string username_proxy = 7;
  string password_proxy = 8;
  repeated string ip_allows = 9;
  repeated BackofficeProxyProfileServiceProxyProfileEntityTelco telcos = 10;
  repeated BackofficeProxyProfileServiceProxyProfileEntityLocation locations = 11;
  bool is_active = 12;
  bool is_default_profile = 13;
  bool can_update = 14;
  int64 bandwidth_per_proxy_mbit = 15;
  bool is_public = 16;
}


message BackofficeProxyProfileServiceProxyProfileEntityDNS {
  string id_dns = 1;
  string name = 2;
  string dns_1 = 3;
  string dns_2 = 4;
}
message BackofficeProxyProfileServiceProxyProfileEntityTelco {
  string id_telco = 1;
  string name = 2;
}
message BackofficeProxyProfileServiceProxyProfileEntityLocation {
  string id_location = 1;
  algoenum.v1.LocationLevel level = 2;
  string name = 3;
}


message BackofficeProxyProfileServiceConfigProxyProfileLocationRequest {
  string id_proxy_profile = 1;
  string id_location = 2;
  bool is_enable = 3;
}

message BackofficeProxyProfileServiceConfigProxyProfileLocationResponse {
  errmsg.v1.ErrorMessage error = 1;
}
message BackofficeProxyProfileServiceConfigProxyProfileIPAllowRequest {
  string id_proxy_profile = 1;
  string ip_allow = 2;
  bool is_enable = 3;
}

message BackofficeProxyProfileServiceConfigProxyProfileIPAllowResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeProxyProfileServiceConfigProxyProfileTelcoRequest {
  string id_proxy_profile = 1;
  string id_telco = 2;
  bool is_enable = 3;
}
message BackofficeProxyProfileServiceConfigProxyProfileTelcoResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeProxyProfileServiceSetDefaultProxyProfileRequest {
  string id_plan = 1;
  string id_proxy_profile = 2;
}

message BackofficeProxyProfileServiceSetDefaultProxyProfileResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message BackofficeProxyProfileServiceCreateProxyProfileRequest {
  string id_plan = 1;
  string id_dns = 2;
  string name = 3;
  int64 auto_switch_ip_after_in_sec  = 4;
  bool auto_switch_ip_when_disconnect = 5;
  bool is_static_username_password = 6;
  int64 bandwidth_per_proxy_mbit = 7;
}

message BackofficeProxyProfileServiceCreateProxyProfileResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message BackofficeProxyProfileServiceUpdateProxyProfileRequest {
  string id_proxy_profile = 1;
  string id_dns = 2;
  string name = 3;
  int64 auto_switch_ip_after_in_sec  = 4;
  bool auto_switch_ip_when_disconnect = 5;
  bool is_static_username_password = 6;
  bool  revoke_username_password = 7;
  int64 bandwidth_per_proxy_mbit = 8;
  utils.v1.State state = 9;
}

message BackofficeProxyProfileServiceUpdateProxyProfileResponse {
  errmsg.v1.ErrorMessage error = 1;
}







