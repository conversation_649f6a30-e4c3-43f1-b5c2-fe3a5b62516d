syntax = "proto3";
package proxymanager.proxyprofile.v1;
option go_package = "git.tmproxy-infra.com/algo/algoproxy-proto/gen/proxymanager/proxyprofile/v1;proxyprofilev1";

import "utils/v1/utils.proto";
import "errmsg/v1/errormsg.proto";
import "algoenum/v1/location_level.proto";

service CustomerProxyProfileService {
  rpc ApplyProxyProfile(CustomerProxyProfileServiceApplyProxyProfileRequest) returns (CustomerProxyProfileServiceApplyProxyProfileResponse);
  rpc ResetProxyProfile(CustomerProxyProfileServiceResetProxyProfileRequest) returns (CustomerProxyProfileServiceResetProxyProfileResponse);

  rpc FetchProxyProfile(CustomerProxyProfileServiceFetchProxyProfileRequest) returns (CustomerProxyProfileServiceFetchProxyProfileResponse);
  rpc CreateProxyProfile(CustomerProxyProfileServiceCreateProxyProfileRequest) returns (CustomerProxyProfileServiceCreateProxyProfileResponse);
  rpc UpdateProxyProfile(CustomerProxyProfileServiceUpdateProxyProfileRequest) returns (CustomerProxyProfileServiceUpdateProxyProfileResponse);

  rpc ConfigProxyProfileLocation(CustomerProxyProfileServiceConfigProxyProfileLocationRequest) returns (CustomerProxyProfileServiceConfigProxyProfileLocationResponse);
  rpc ConfigProxyProfileIPAllow(CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest) returns (CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse);
  rpc ConfigProxyProfileTelco(CustomerProxyProfileServiceConfigProxyProfileTelcoRequest) returns (CustomerProxyProfileServiceConfigProxyProfileTelcoResponse);
}
message CustomerProxyProfileServiceResetProxyProfileRequest {
  string id_proxy_profile = 1;
}

message CustomerProxyProfileServiceResetProxyProfileResponse {
  errmsg.v1.ErrorMessage error = 1;
}

message CustomerProxyProfileServiceApplyProxyProfileRequest {
  string id_proxy_profile = 1;
  repeated string id_proxy_token = 2;
}

message CustomerProxyProfileServiceApplyProxyProfileResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message CustomerProxyProfileServiceConfigProxyProfileTelcoRequest {
  string id_proxy_profile = 1;
  string id_telco = 2;
  bool is_enable = 3;
}

message CustomerProxyProfileServiceConfigProxyProfileTelcoResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message CustomerProxyProfileServiceConfigProxyProfileLocationRequest {
  string id_proxy_profile = 1;
  string id_location = 2;
  bool is_enable = 3;
}

message CustomerProxyProfileServiceConfigProxyProfileLocationResponse {
  errmsg.v1.ErrorMessage error = 1;
}
message CustomerProxyProfileServiceConfigProxyProfileIPAllowRequest {
  string id_proxy_profile = 1;
  string ip_allow = 2;
  bool is_enable = 3;
}

message CustomerProxyProfileServiceConfigProxyProfileIPAllowResponse {
  errmsg.v1.ErrorMessage error = 1;
}


message CustomerProxyProfileServiceFetchProxyProfileRequest {
  string id_plan = 1;
  string id_proxy_profile = 2;
  utils.v1.PaginationRequest pagination = 3;
  bool is_public = 4;
}

message CustomerProxyProfileServiceFetchProxyProfileResponse {
  errmsg.v1.ErrorMessage error = 1;
  utils.v1.PaginationResponse pagination = 2;
  repeated CustomerProxyProfileServiceProxyProfileEntity proxy_profiles = 3;
}

message CustomerProxyProfileServiceProxyProfileEntity {
  string id_proxy_profile = 1;
  string name = 2;
  CustomerProxyProfileServiceProxyProfileEntityDNS dns  = 3;
  int64 auto_switch_ip_after_in_sec = 4;
  bool auto_switch_ip_when_disconnect = 5;
  bool is_static_username_password = 6;
  repeated string ip_allows = 7;
  repeated CustomerProxyProfileServiceProxyProfileEntityTelco telcos = 8;
  repeated CustomerProxyProfileServiceProxyProfileEntityLocation locations = 9;
  int64 bandwidth_per_proxy_mbit = 10;
  string username_proxy = 13;
  string password_proxy = 14;
}


message CustomerProxyProfileServiceProxyProfileEntityDNS {
  string id_dns = 1;
  string name = 2;
  string dns_1 = 3;
  string dns_2 = 4;
}
message CustomerProxyProfileServiceProxyProfileEntityTelco {
  string id_telco = 1;
  string name = 2;
}
message CustomerProxyProfileServiceProxyProfileEntityLocation {
  string id_location = 1;
  algoenum.v1.LocationLevel level = 2;
  string name = 3;
}

message CustomerProxyProfileServiceCreateProxyProfileRequest {
  string id_plan = 1;
  string id_dns = 2;
  string name = 3;
  int64 auto_switch_ip_after_in_sec  = 4;
  bool auto_switch_ip_when_disconnect = 5;
  bool is_static_username_password = 6;
}

message CustomerProxyProfileServiceCreateProxyProfileResponse {
  errmsg.v1.ErrorMessage error = 1;
  string id_proxy_profile = 2;
}

message CustomerProxyProfileServiceUpdateProxyProfileRequest {
  string id_proxy_profile = 1;
  string id_dns = 2;
  string name = 3;
  int64 auto_switch_ip_after_in_sec  = 4;
  bool auto_switch_ip_when_disconnect = 5;
  bool is_static_username_password = 6;
  bool revoke_username_password = 7;
  int64 bandwidth_per_proxy_mbit = 8;
  utils.v1.State state = 9;
}

message CustomerProxyProfileServiceUpdateProxyProfileResponse {
  errmsg.v1.ErrorMessage error = 1;
}



